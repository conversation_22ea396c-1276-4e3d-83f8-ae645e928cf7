"""
Command-line tool to display the position manager dashboard.
"""

import asyncio
import argparse
import logging
import yaml
import time
from datetime import datetime

from executors.htx_executor import HTXExecutor
from feeds.htx_futures import HTXFuturesClient
from position_manager import PositionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def display_dashboard(config_file: str):
    """
    Display the position manager dashboard.

    Args:
        config_file: Path to configuration file
    """
    # Load configuration
    with open(config_file, "r") as f:
        config = yaml.safe_load(f)

    # Try to get executor instance
    executor = HTXExecutor.get_instance()

    if not executor:
        # Create HTX client
        htx_client = HTXFuturesClient(
            api_key=config.get("exchange", {}).get("api_key", ""),
            api_secret=config.get("exchange", {}).get("api_secret", ""),
            testnet=config.get("exchange", {}).get("testnet", True)
        )

        # Create executor
        executor = HTXExecutor(
            client=htx_client,
            config=config,
            simulation_mode=True
        )

        # Create position manager
        position_manager = PositionManager(
            executor=executor,
            config=config
        )

        # Set position manager on executor
        executor.position_manager = position_manager
    else:
        # Get position manager from executor
        position_manager = executor.position_manager

    # Display dashboard
    dashboard = position_manager.get_position_dashboard()
    print("\n" + dashboard + "\n")

    # If running in continuous mode, update every second
    if args.continuous:
        print("Press Ctrl+C to exit...")
        try:
            while True:
                # Clear screen
                print("\033c", end="")

                # Update dashboard
                dashboard = position_manager.get_position_dashboard()
                print("\n" + dashboard + "\n")

                # Wait for next update
                await asyncio.sleep(1.0)
        except KeyboardInterrupt:
            print("\nExiting...")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Display position manager dashboard")
    parser.add_argument("--config", "-c", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--continuous", "-C", action="store_true", help="Continuously update dashboard")
    args = parser.parse_args()

    asyncio.run(display_dashboard(args.config))
