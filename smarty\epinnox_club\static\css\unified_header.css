/* Money Circle Unified Header Component */

/* Header Container */
.unified-header {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Branding Section */
.header-branding {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-branding .logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    color: white;
}

.header-branding .brand-text h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.header-branding .brand-text p {
    margin: 0.25rem 0 0 0;
    color: #94a3b8;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Navigation */
.header-nav {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.header-nav .nav-link {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95rem;
    position: relative;
}

.header-nav .nav-link:hover {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
    transform: translateY(-1px);
}

.header-nav .nav-link.active {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.2);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.header-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #8b5cf6;
    border-radius: 50%;
}

/* User Info Section */
.header-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-welcome {
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.user-role {
    color: #94a3b8;
    font-size: 0.8rem;
    margin: 0;
}

.logout-btn {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.logout-btn:hover {
    color: #ef4444;
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

/* Breadcrumb Navigation */
.header-breadcrumb {
    background: rgba(30, 41, 59, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding: 0.75rem 0;
}

.breadcrumb-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item:hover {
    color: #8b5cf6;
}

.breadcrumb-item.active {
    color: #e2e8f0;
    font-weight: 500;
}

.breadcrumb-separator {
    color: #64748b;
    margin: 0 0.25rem;
}

/* Status Indicators */
.status-indicators {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator.online {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-indicator.trading {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-indicator.alert {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .header-nav {
        order: -1;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .header-branding {
        order: 1;
    }
    
    .header-user-info {
        order: 2;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .header-branding .brand-text h1 {
        font-size: 1.5rem;
    }
    
    .header-nav {
        gap: 0.5rem;
    }
    
    .header-nav .nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .user-details {
        display: none;
    }
    
    .breadcrumb-content {
        padding: 0 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-nav {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .header-nav .nav-link {
        width: 100%;
        text-align: center;
    }
    
    .status-indicators {
        flex-wrap: wrap;
        justify-content: center;
    }
}
