/* Money Circle Unified Header Component */

/* Header Container */
.unified-header {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Branding Section */
.header-branding {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-branding .logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    color: white;
}

.header-branding .brand-text h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.header-branding .brand-text p {
    margin: 0.25rem 0 0 0;
    color: #94a3b8;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Navigation */
.header-nav {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.header-nav .nav-link {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95rem;
    position: relative;
}

.header-nav .nav-link:hover {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
    transform: translateY(-1px);
}

.header-nav .nav-link.active {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.2);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.header-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #8b5cf6;
    border-radius: 50%;
}

/* User Info Section */
.header-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-welcome {
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.user-role {
    color: #94a3b8;
    font-size: 0.8rem;
    margin: 0;
}

.logout-btn {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.logout-btn:hover {
    color: #ef4444;
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

/* Breadcrumb Navigation */
.header-breadcrumb {
    background: rgba(30, 41, 59, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding: 0.75rem 0;
}

.breadcrumb-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item:hover {
    color: #8b5cf6;
}

.breadcrumb-item.active {
    color: #e2e8f0;
    font-weight: 500;
}

.breadcrumb-separator {
    color: #64748b;
    margin: 0 0.25rem;
}

/* Status Indicators */
.status-indicators {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Enhanced Header Styles for Role-Based Navigation */

/* Notification Badge */
.notification-badge {
    position: relative;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.notification-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #f44336;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.notification-count.hidden {
    display: none;
}

/* User Profile Dropdown */
.user-profile-dropdown {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.user-profile-dropdown:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dropdown-arrow {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.user-profile-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* User Dropdown Menu */
.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 10px 0;
    min-width: 220px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.user-dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 15px 20px;
}

.dropdown-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.dropdown-item.logout:hover {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.dropdown-icon {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 8px;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
    display: none;
    background: rgba(0, 0, 0, 0.95);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.mobile-nav.active {
    display: block;
}

.mobile-nav .nav-link {
    display: block;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.mobile-nav .nav-link:last-child {
    border-bottom: none;
}

/* Role-based navigation styles */
.nav-link.admin-only {
    border-left: 3px solid #f44336;
    padding-left: 17px;
}

.nav-link.member-plus {
    border-left: 3px solid #2196F3;
    padding-left: 17px;
}

.nav-link.viewer-plus {
    border-left: 3px solid #4CAF50;
    padding-left: 17px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .user-details {
        display: none;
    }

    .notification-badge {
        padding: 6px;
    }
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator.online {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-indicator.trading {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-indicator.alert {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
    }

    .header-nav {
        order: -1;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .header-branding {
        order: 1;
    }

    .header-user-info {
        order: 2;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }

    .header-branding .brand-text h1 {
        font-size: 1.5rem;
    }

    .header-nav {
        gap: 0.5rem;
    }

    .header-nav .nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .user-details {
        display: none;
    }

    .breadcrumb-content {
        padding: 0 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-nav {
        flex-direction: column;
        gap: 0.25rem;
    }

    .header-nav .nav-link {
        width: 100%;
        text-align: center;
    }

    .status-indicators {
        flex-wrap: wrap;
        justify-content: center;
    }
}
