#!/usr/bin/env python
"""
Launch the smart-trader system on testnet.
"""

import os
import sys
import yaml
import argparse
import logging
import asyncio
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("testnet-launcher")

# Import orchestrator
from orchestrator import run_orchestrator


def setup_directories():
    """Set up required directories."""
    dirs = ["data", "logs", "models", "llm/prompts"]
    for d in dirs:
        os.makedirs(d, exist_ok=True)
        logger.info(f"Created directory: {d}")


def load_config(config_path):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        sys.exit(1)


def setup_file_logging(config):
    """Set up file logging based on configuration."""
    try:
        log_config = config.get("logging", {})
        log_file = log_config.get("file", "logs/testnet.log")
        log_level = getattr(logging, log_config.get("level", "INFO"))

        # Create a simple FileHandler instead of RotatingFileHandler to avoid the bug
        file_handler = logging.FileHandler(log_file)

        # Set formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Set level
        file_handler.setLevel(log_level)

        # Add to root logger
        logging.getLogger().addHandler(file_handler)

        logger.info(f"File logging configured: {log_file}")
    except Exception as e:
        logger.error(f"Error setting up file logging: {e}")


def check_api_keys(config):
    """Check if API keys are configured."""
    api_key = config.get("exchange", {}).get("api_key", "")
    api_secret = config.get("exchange", {}).get("api_secret", "")

    if not api_key or not api_secret:
        logger.warning("API key or secret not configured. Please add them to config.yaml")
        return False

    return True


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run the smart-trader system on testnet")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--strategy", default="smart_model_integrated_strategy", help="Trading strategy to use")
    args = parser.parse_args()

    # Set up directories
    setup_directories()

    # Load configuration
    config = load_config(args.config)

    # Override strategy from command line
    if args.strategy:
        config.setdefault('strategy', {})['type'] = args.strategy
        logger.info(f"Using strategy from command line: {args.strategy}")

    # Set up file logging
    setup_file_logging(config)

    # Check API keys
    if not check_api_keys(config):
        logger.warning("Continuing without API keys (some features may not work)")

    # Log startup
    logger.info("=" * 50)
    logger.info(f"Starting smart-trader system on testnet at {datetime.now().isoformat()}")
    logger.info(f"Trading symbols: {config.get('trading', {}).get('symbols', [])}")
    logger.info(f"Trading enabled: {config.get('trading', {}).get('enabled', False)}")
    logger.info(f"Simulation mode: {config.get('trading', {}).get('simulation_mode', True)}")
    logger.info("=" * 50)

    # Run the orchestrator
    try:
        await run_orchestrator(config)
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Error running orchestrator: {e}", exc_info=True)
    finally:
        logger.info("Smart-trader system shutdown complete")


if __name__ == "__main__":
    # Import logging handlers here to avoid circular imports
    import logging.handlers
    import platform

    # Use SelectorEventLoop on Windows to fix aiodns issue
    if platform.system() == 'Windows':
        import asyncio
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    # Run the main function
    asyncio.run(main())
