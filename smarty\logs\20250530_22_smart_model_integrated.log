2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_22_smart_model_integrated.log
2025-05-30 22:47:43,050 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_22_smart_model_integrated_events.json
2025-05-30 22:47:43,051 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 22:47:43,051 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 22:47:43
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 22:47:43,052 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 22:47:43,053 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 22:47:43,054 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 22:47:43,069 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 22:47:43,070 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 22:47:43,070 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-30 22:52:53,120 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 22:52:53,120 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_22_smart_model_integrated.log
2025-05-30 22:52:53,120 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_22_smart_model_integrated_events.json
2025-05-30 22:52:53,121 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 22:52:53,121 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 22:52:53
2025-05-30 22:52:53,121 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 22:52:53,121 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 22:52:53,122 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 22:52:53,123 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 22:52:53,124 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 22:52:53,124 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 22:52:53,124 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 22:52:53,124 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 22:52:53,127 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 22:52:53,128 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 22:52:53,128 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 22:52:53,129 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 22:52:53,129 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-30 22:52:56,144 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-30 22:52:56,154 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-30 22:52:56,171 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-30 22:52:56,172 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-30 22:52:58,016 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-30 22:52:58,017 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-30 22:52:58,018 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-30 22:53:00,007 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-30 22:53:00,008 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-30 22:53:00,009 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-30 22:53:00,009 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-30 22:53:00,010 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-30 22:53:00,010 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-30 22:53:00,010 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-30 22:53:00,011 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-30 22:53:00,011 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-30 22:53:00,011 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-30 22:53:00,011 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-30 22:53:00,020 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-30 22:53:00,024 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-30 22:53:20,248 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 22:53:20,249 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_22_smart_model_integrated.log
2025-05-30 22:53:20,249 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_22_smart_model_integrated_events.json
2025-05-30 22:53:20,249 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 22:53:20,249 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 22:53:20
2025-05-30 22:53:20,250 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 22:53:20,250 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 22:53:20,250 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 22:53:20,252 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 22:53:20,252 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 22:53:20,253 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 22:53:20,253 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 22:53:20,253 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 22:53:20,256 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 22:53:20,257 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 22:53:20,257 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 22:53:20,257 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 22:53:20,258 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-30 22:53:21,355 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-30 22:53:21,359 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-30 22:53:21,367 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-30 22:53:21,367 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-30 22:53:22,786 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-30 22:53:22,788 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-30 22:53:22,789 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-30 22:53:25,159 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-30 22:53:25,159 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-30 22:53:25,160 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-30 22:53:25,161 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-30 22:53:25,161 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-30 22:53:25,163 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-30 22:53:25,204 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-30 22:53:25,204 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-30 22:53:25,205 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-30 22:53:25,205 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-30 22:53:25,206 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-30 22:53:25,208 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-30 22:53:25,211 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-30 22:54:45,794 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 22:54:45,795 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_22_smart_model_integrated.log
2025-05-30 22:54:45,795 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_22_smart_model_integrated_events.json
2025-05-30 22:54:45,795 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 22:54:45,796 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 22:54:45
2025-05-30 22:54:45,796 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 22:54:45,796 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 22:54:45,797 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 22:54:45,799 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 22:54:45,799 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 22:54:45,799 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 22:54:45,800 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 22:54:45,800 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 22:54:45,803 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 22:54:45,804 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 22:54:45,804 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 22:54:45,805 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 22:54:45,806 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-30 22:54:46,944 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-30 22:54:46,947 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-30 22:54:46,954 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-30 22:54:46,954 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-30 22:54:48,457 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-30 22:54:48,459 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-30 22:54:48,460 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-30 22:54:49,974 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-30 22:54:49,975 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-30 22:54:49,976 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-30 22:54:49,976 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-30 22:54:49,977 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-30 22:54:49,977 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-30 22:54:50,009 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-30 22:54:50,010 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-30 22:54:50,010 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-30 22:54:50,011 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-30 22:54:50,011 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-30 22:54:50,015 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-30 22:54:50,019 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
