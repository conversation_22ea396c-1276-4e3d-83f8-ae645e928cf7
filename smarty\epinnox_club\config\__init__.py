"""
Money Circle Phase 3 Configuration Package
"""

__version__ = "3.0.0"

# Import the original config function
import sys
import os
from pathlib import Path

# Add the parent directory to the path to import the original config
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

# Import the original config function from the parent directory
import importlib.util
config_path = parent_dir / 'config.py'
spec = importlib.util.spec_from_file_location("config", config_path)
config_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_module)
get_config = config_module.get_config
