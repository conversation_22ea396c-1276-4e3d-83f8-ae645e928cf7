#!/usr/bin/env python3
"""
HTX Data Producer for Smart-Trader Dashboard

This script connects to HTX WebSocket and feeds real market data
into the main SQLite bus for the dashboard to consume.
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from feeds.htx_futures import HTXFuturesClient
from feeds.binance_fallback_client import BinanceFallbackClient
from pipeline.databus import SQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HTXDataProducer:
    """Produces real market data for the Smart-Trader dashboard with Binance fallback."""

    def __init__(self):
        self.htx_client = None
        self.binance_client = None
        self.bus = None
        self.running = False
        self.symbols = ["BTC-USDT", "ETH-USDT"]  # Main trading pairs
        self.using_fallback = False

    async def start(self):
        """Start the data producer with HTX primary and Binance fallback."""
        logger.info("🚀 Starting Market Data Producer for Smart-Trader Dashboard...")

        try:
            # Initialize main SQLite bus (same as dashboard uses)
            bus_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "bus.db")
            self.bus = SQLiteBus(path=bus_path, poll_interval=0.5)

            # Try HTX first
            if await self._try_htx_connection():
                logger.info("✅ Using HTX as primary data source")
                self.using_fallback = False
            else:
                logger.warning("⚠️ HTX connection failed, switching to Binance fallback")
                if await self._try_binance_fallback():
                    logger.info("✅ Using Binance as fallback data source")
                    self.using_fallback = True
                else:
                    raise Exception("Both HTX and Binance connections failed")

            self.running = True
            logger.info(f"🎯 Market Data Producer started for {len(self.symbols)} symbols")
            logger.info("📊 Real market data is now flowing to data/bus.db")
            logger.info("🌐 Dashboard should now show live market data!")

            # Keep running and processing messages
            await self.run_forever()

        except Exception as e:
            logger.error(f"❌ Failed to start Market Data Producer: {e}")
            await self.stop()

    async def _try_htx_connection(self) -> bool:
        """Try to connect to HTX WebSocket."""
        try:
            logger.info("🔄 Attempting HTX connection...")

            # Initialize HTX client (no API keys needed for market data)
            self.htx_client = HTXFuturesClient(testnet=False)
            self.htx_client.simulation_mode = True

            # Set publisher for HTX client
            self.htx_client.set_publisher(self.bus.publish)

            # Connect to HTX
            await self.htx_client.connect()
            logger.info("✅ Connected to HTX WebSocket")

            # Subscribe to market data for each symbol
            success_count = 0
            for symbol in self.symbols:
                # Subscribe to 1-second klines
                kline_channel = self.htx_client.CHANNEL_KLINE.format(symbol=symbol, interval="1s")
                if await self.htx_client.subscribe(kline_channel):
                    logger.info(f"✅ HTX: Subscribed to klines: {symbol}")
                    success_count += 1

                # Subscribe to trades
                trade_channel = self.htx_client.CHANNEL_TRADE.format(symbol=symbol)
                if await self.htx_client.subscribe(trade_channel):
                    logger.info(f"✅ HTX: Subscribed to trades: {symbol}")
                    success_count += 1

                # Subscribe to orderbook depth
                depth_channel = self.htx_client.CHANNEL_DEPTH.format(symbol=symbol)
                if await self.htx_client.subscribe(depth_channel):
                    logger.info(f"✅ HTX: Subscribed to depth: {symbol}")
                    success_count += 1

            return success_count > 0

        except Exception as e:
            logger.error(f"❌ HTX connection failed: {e}")
            return False

    async def _try_binance_fallback(self) -> bool:
        """Try to connect to Binance as fallback."""
        try:
            logger.info("🔄 Attempting Binance fallback connection...")

            # Initialize Binance fallback client
            self.binance_client = BinanceFallbackClient()
            self.binance_client.set_publisher(self.bus.publish)

            # Connect to Binance
            if await self.binance_client.connect():
                logger.info("✅ Connected to Binance WebSocket (fallback)")
                return True
            else:
                logger.error("❌ Binance fallback connection failed")
                return False

        except Exception as e:
            logger.error(f"❌ Binance fallback failed: {e}")
            return False

    async def run_forever(self):
        """Keep the producer running and log periodic status."""
        message_count = 0
        last_log_time = datetime.now()

        while self.running:
            try:
                if not self.using_fallback and self.htx_client:
                    # Get next market message from HTX (non-blocking)
                    message = await self.htx_client.get_next_market_message(timeout=1.0)

                    if message:
                        message_count += 1

                        # Log status every 100 messages
                        if message_count % 100 == 0:
                            current_time = datetime.now()
                            elapsed = (current_time - last_log_time).total_seconds()
                            rate = 100 / elapsed if elapsed > 0 else 0
                            source = "HTX" if not self.using_fallback else "Binance"
                            logger.info(f"[DATA] Processed {message_count} {source} messages ({rate:.1f} msg/sec)")
                            last_log_time = current_time

                elif self.using_fallback and self.binance_client:
                    # For Binance, just keep the connection alive and count messages
                    # (Binance client handles message processing internally)
                    await asyncio.sleep(1)
                    message_count += 1

                    # Log status every 60 seconds for Binance
                    if message_count % 60 == 0:
                        current_time = datetime.now()
                        logger.info(f"📈 Binance fallback active - {message_count} seconds uptime")
                        last_log_time = current_time

                await asyncio.sleep(0.01)  # Small delay to prevent busy waiting

            except asyncio.TimeoutError:
                # No message received, continue
                pass
            except Exception as e:
                logger.error(f"❌ Error processing messages: {e}")
                await asyncio.sleep(1)

    async def stop(self):
        """Stop the market data producer."""
        logger.info("🛑 Stopping Market Data Producer...")
        self.running = False

        # Close HTX client if active
        if self.htx_client:
            try:
                await self.htx_client.close()
                logger.info("✅ HTX client closed")
            except Exception as e:
                logger.error(f"Error closing HTX client: {e}")

        # Close Binance client if active
        if self.binance_client:
            try:
                await self.binance_client.close()
                logger.info("✅ Binance fallback client closed")
            except Exception as e:
                logger.error(f"Error closing Binance client: {e}")

        # Close SQLite bus
        if self.bus:
            try:
                self.bus.close()
                logger.info("✅ SQLite bus closed")
            except Exception as e:
                logger.error(f"Error closing bus: {e}")

        logger.info("🏁 Market Data Producer stopped")

# Global producer instance
producer = None

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    if producer:
        asyncio.create_task(producer.stop())

async def main():
    """Main function."""
    global producer

    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    producer = HTXDataProducer()

    try:
        await producer.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    finally:
        if producer:
            await producer.stop()

if __name__ == "__main__":
    # Fix for Windows: Use SelectorEventLoop instead of ProactorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
