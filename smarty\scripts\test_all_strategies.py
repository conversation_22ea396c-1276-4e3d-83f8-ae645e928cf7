#!/usr/bin/env python3
"""
Strategy Testing Script - Run All Strategies in Background
Tests each trading strategy individually with live data feeds
"""

import asyncio
import subprocess
import time
import signal
import sys
from pathlib import Path
from typing import Dict, List, Optional
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init(autoreset=True)

class StrategyTester:
    """Test all trading strategies in background mode."""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
        # Available strategies to test
        self.strategies = {
            'smart_model_integrated': {
                'command': 'python orchestrator.py --debug',
                'description': 'Smart Model Integrated Strategy',
                'test_duration': 60,  # seconds
                'expected_outputs': ['Model loaded', 'Strategy started', 'Signal generated']
            },
            'smart_strategy_only': {
                'command': 'python run_smart_strategy_live.py',
                'description': 'Smart Strategy Only',
                'test_duration': 45,
                'expected_outputs': ['Strategy initialized', 'Live data connected']
            },
            'order_flow': {
                'command': 'python live_dataframe_strategy_runner.py',
                'description': 'Order Flow Strategy',
                'test_duration': 30,
                'expected_outputs': ['DataFrame strategy', 'Order flow analysis']
            }
        }
        
        # Core dependencies
        self.dependencies = {
            'data_producer': {
                'command': 'python feeds/htx_data_producer.py',
                'description': 'HTX Data Producer',
                'startup_delay': 3
            },
            'dashboard': {
                'command': 'python live_dashboard.py',
                'description': 'Live Dashboard',
                'startup_delay': 2
            }
        }
    
    def print_banner(self):
        """Print testing banner."""
        banner = f"""
{Fore.CYAN + Style.BRIGHT}
╔══════════════════════════════════════════════════════════════╗
║                🧪 STRATEGY TESTING SUITE                    ║
║              Background Strategy Validation                  ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
{Fore.GREEN}🎯 Testing Strategies:{Style.RESET_ALL}
  • Smart Model Integrated (60s test)
  • Smart Strategy Only (45s test)  
  • Order Flow Strategy (30s test)
  
{Fore.YELLOW}📊 Dependencies:{Style.RESET_ALL}
  • HTX Data Producer (Live Feed)
  • Dashboard (localhost:8082)
{Fore.WHITE}{'='*60}{Style.RESET_ALL}
"""
        print(banner)
    
    def start_dependency(self, dep_name: str) -> bool:
        """Start a dependency component."""
        dep = self.dependencies[dep_name]
        
        try:
            print(f"{Fore.YELLOW}🚀 Starting {dep['description']}...{Style.RESET_ALL}")
            
            process = subprocess.Popen(
                dep['command'].split(),
                cwd=Path.cwd(),
                stdout=subprocess.DEVNULL,  # Run silently in background
                stderr=subprocess.DEVNULL
            )
            
            time.sleep(dep.get('startup_delay', 2))
            
            if process.poll() is None:
                self.processes[dep_name] = process
                print(f"{Fore.GREEN}✅ {dep['description']} started (PID: {process.pid}){Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}❌ {dep['description']} failed to start{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start {dep['description']}: {e}{Style.RESET_ALL}")
            return False
    
    def test_strategy(self, strategy_name: str) -> Dict:
        """Test a single strategy."""
        strategy = self.strategies[strategy_name]
        
        print(f"\n{Fore.MAGENTA + Style.BRIGHT}🧪 TESTING: {strategy['description']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Command: {strategy['command']}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Duration: {strategy['test_duration']}s{Style.RESET_ALL}")
        
        result = {
            'name': strategy_name,
            'description': strategy['description'],
            'success': False,
            'pid': None,
            'outputs_found': [],
            'errors': [],
            'duration': 0
        }
        
        try:
            # Start strategy
            start_time = time.time()
            process = subprocess.Popen(
                strategy['command'].split(),
                cwd=Path.cwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            if process.poll() is None:
                result['pid'] = process.pid
                print(f"{Fore.GREEN}✅ Strategy started (PID: {process.pid}){Style.RESET_ALL}")
                
                # Monitor for expected duration
                print(f"{Fore.YELLOW}⏱️ Monitoring for {strategy['test_duration']} seconds...{Style.RESET_ALL}")
                
                end_time = start_time + strategy['test_duration']
                output_lines = []
                
                while time.time() < end_time and process.poll() is None:
                    # Read output
                    try:
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            # Check for expected outputs
                            for expected in strategy['expected_outputs']:
                                if expected.lower() in line.lower():
                                    if expected not in result['outputs_found']:
                                        result['outputs_found'].append(expected)
                                        print(f"{Fore.GREEN}  ✓ Found: {expected}{Style.RESET_ALL}")
                    except:
                        pass
                    
                    time.sleep(0.1)
                
                result['duration'] = time.time() - start_time
                
                # Terminate strategy
                if process.poll() is None:
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                
                # Evaluate success
                if len(result['outputs_found']) >= len(strategy['expected_outputs']) // 2:
                    result['success'] = True
                    print(f"{Fore.GREEN}✅ Strategy test PASSED{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}⚠️ Strategy test PARTIAL (found {len(result['outputs_found'])}/{len(strategy['expected_outputs'])} expected outputs){Style.RESET_ALL}")
                
            else:
                result['errors'].append("Failed to start")
                print(f"{Fore.RED}❌ Strategy failed to start{Style.RESET_ALL}")
                
        except Exception as e:
            result['errors'].append(str(e))
            print(f"{Fore.RED}❌ Strategy test error: {e}{Style.RESET_ALL}")
        
        return result
    
    def print_results(self, results: List[Dict]):
        """Print comprehensive test results."""
        print(f"\n{Fore.CYAN + Style.BRIGHT}📊 STRATEGY TEST RESULTS{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'='*60}{Style.RESET_ALL}")
        
        passed = sum(1 for r in results if r['success'])
        total = len(results)
        
        for result in results:
            status = f"{Fore.GREEN}✅ PASSED" if result['success'] else f"{Fore.RED}❌ FAILED"
            print(f"\n{Fore.YELLOW}{result['description']}:{Style.RESET_ALL}")
            print(f"  Status: {status}{Style.RESET_ALL}")
            print(f"  Duration: {result['duration']:.1f}s")
            print(f"  PID: {result['pid'] or 'N/A'}")
            print(f"  Outputs Found: {len(result['outputs_found'])}")
            
            if result['outputs_found']:
                for output in result['outputs_found']:
                    print(f"    ✓ {output}")
            
            if result['errors']:
                for error in result['errors']:
                    print(f"    ❌ {error}")
        
        print(f"\n{Fore.CYAN + Style.BRIGHT}SUMMARY:{Style.RESET_ALL}")
        print(f"  Total Strategies: {total}")
        print(f"  Passed: {Fore.GREEN}{passed}{Style.RESET_ALL}")
        print(f"  Failed: {Fore.RED}{total - passed}{Style.RESET_ALL}")
        print(f"  Success Rate: {Fore.YELLOW}{(passed/total)*100:.1f}%{Style.RESET_ALL}")
    
    async def run_tests(self):
        """Run all strategy tests."""
        self.print_banner()
        
        # Start dependencies
        print(f"{Fore.CYAN}🔧 Starting Dependencies...{Style.RESET_ALL}")
        for dep_name in ['data_producer', 'dashboard']:
            if not self.start_dependency(dep_name):
                print(f"{Fore.RED}❌ Failed to start {dep_name}. Aborting tests.{Style.RESET_ALL}")
                return
        
        print(f"{Fore.GREEN}✅ All dependencies started{Style.RESET_ALL}")
        
        # Wait for dependencies to stabilize
        print(f"{Fore.YELLOW}⏱️ Waiting for dependencies to stabilize...{Style.RESET_ALL}")
        await asyncio.sleep(5)
        
        # Run strategy tests
        results = []
        for strategy_name in self.strategies:
            result = self.test_strategy(strategy_name)
            results.append(result)
            
            # Brief pause between tests
            if strategy_name != list(self.strategies.keys())[-1]:
                print(f"{Fore.YELLOW}⏸️ Pausing 10s before next test...{Style.RESET_ALL}")
                await asyncio.sleep(10)
        
        # Print results
        self.print_results(results)
        
        # Cleanup
        await self.cleanup()
    
    async def cleanup(self):
        """Clean up all processes."""
        print(f"\n{Fore.YELLOW}🧹 Cleaning up processes...{Style.RESET_ALL}")
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    print(f"{Fore.YELLOW}🛑 Stopping {name}...{Style.RESET_ALL}")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    print(f"{Fore.GREEN}✅ {name} stopped{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}❌ Error stopping {name}: {e}{Style.RESET_ALL}")
        
        print(f"{Fore.GREEN}✅ Cleanup complete{Style.RESET_ALL}")

async def main():
    """Main entry point."""
    tester = StrategyTester()
    
    try:
        await tester.run_tests()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Test interrupted by user{Style.RESET_ALL}")
        await tester.cleanup()
    except Exception as e:
        print(f"{Fore.RED}❌ Test error: {e}{Style.RESET_ALL}")
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
