#!/usr/bin/env python3
"""
Live DataFrame Strategy Runner

Runs the DataFrame-based smart strategy on live data with real signal generation.
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dataframe_smart_strategy import DataFrameSmartStrategy
from pipeline.databus import SQLiteBus
import yaml

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LiveDataFrameStrategyRunner:
    """Run DataFrame-based smart strategy on live data."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the live strategy runner."""
        self.config = self._load_config(config_path)
        self.bus = None
        self.strategy = None
        self.running = False
        self.symbols = self.config.get("trading", {}).get("symbols", ["BTC-USDT"])
        self.signal_interval = 30  # Generate signals every 30 seconds

        # Trading settings
        self.trading_enabled = self.config.get("trading", {}).get("enabled", False)
        self.simulation_mode = self.config.get("trading", {}).get("simulation_mode", True)

        # Signal tracking
        self.signal_count = 0
        self.last_signal_time = None

        logger.info(f"🎯 Live DataFrame Strategy Runner initialized")
        logger.info(f"📊 Symbols: {self.symbols}")
        logger.info(f"🔧 Trading enabled: {self.trading_enabled}")
        logger.info(f"⚡ Simulation mode: {self.simulation_mode}")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {config_path}")
            return config
        except Exception as e:
            logger.error(f"❌ Failed to load config: {e}")
            return {}

    async def start(self):
        """Start the live strategy runner."""
        logger.info("🚀 Starting Live DataFrame Strategy Runner...")

        try:
            # Connect to SQLite bus
            bus_path = self.config.get("message_bus", {}).get("path", "data/bus.db")
            self.bus = SQLiteBus(path=bus_path, poll_interval=0.5)
            logger.info(f"✅ Connected to SQLite bus at {bus_path}")

            # Initialize strategy
            strategy_config = self.config.get("strategy", {})
            self.strategy = DataFrameSmartStrategy(strategy_config)
            logger.info("✅ DataFrame Smart Strategy initialized")

            # Set up signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            self.running = True

            # Start strategy loop
            await self._strategy_loop()

        except Exception as e:
            logger.error(f"❌ Failed to start live strategy: {e}")
            raise

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.running = False

    async def _strategy_loop(self):
        """Main strategy execution loop."""
        logger.info("📊 Starting strategy loop...")
        logger.info(f"🔄 Signal generation interval: {self.signal_interval} seconds")

        while self.running:
            try:
                # Generate signals for all symbols
                for symbol in self.symbols:
                    signal = await self._generate_signal(symbol)

                    if signal:
                        await self._process_signal(signal)

                # Log periodic status
                if self.signal_count > 0 and self.signal_count % 5 == 0:
                    logger.info(f"📈 Generated {self.signal_count} signals so far")

                # Wait before next iteration
                await asyncio.sleep(self.signal_interval)

            except Exception as e:
                logger.error(f"❌ Error in strategy loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _generate_signal(self, symbol: str):
        """Generate a trading signal for the given symbol."""
        try:
            # Use the DataFrame smart strategy
            signal = self.strategy.generate_signal(symbol)

            if signal:
                self.signal_count += 1
                self.last_signal_time = datetime.now()

                confidence = signal.metadata.get("confidence", signal.score)
                logger.info(f"🎯 Signal #{self.signal_count}: {signal.action} {symbol}")
                logger.info(f"   Score: {signal.score:.3f}, Confidence: {confidence:.3f}")
                logger.info(f"   Rationale: {signal.rationale}")

                return signal
            else:
                logger.debug(f"No signal generated for {symbol}")
                return None

        except Exception as e:
            logger.error(f"❌ Error generating signal for {symbol}: {e}")
            return None

    async def _process_signal(self, signal):
        """Process a trading signal."""
        try:
            # Create signal data for bus
            confidence = signal.metadata.get("confidence", signal.score)
            signal_data = {
                "symbol": signal.symbol,
                "action": signal.action.value if hasattr(signal.action, 'value') else str(signal.action),
                "score": signal.score,
                "confidence": confidence,
                "rationale": signal.rationale,
                "timestamp": datetime.now().isoformat(),
                "source": "dataframe_smart_strategy",
                "signal_id": self.signal_count,
                "price": signal.metadata.get("price", 0)
            }

            # Publish signal to bus for dashboard
            self.bus.publish("signals.trading", datetime.now().timestamp(), signal_data)
            logger.info(f"📡 Signal published to bus")

            # ALSO publish analysis data for AI Market Analysis dashboard component
            await self._publish_analysis_data(signal)

            # Handle trading execution
            if self.trading_enabled:
                if self.simulation_mode:
                    logger.info(f"📊 SIMULATION: Would execute {signal.action} {signal.symbol}")
                    logger.info(f"   💰 Simulation mode - no real trades executed")
                else:
                    logger.warning(f"🚨 REAL TRADING SIGNAL: {signal.action} {signal.symbol}")
                    logger.warning(f"⚠️  Real trading execution would happen here!")
                    # Note: Real trading execution would be implemented here
            else:
                logger.info(f"📊 Trading disabled - signal logged only")

        except Exception as e:
            logger.error(f"❌ Error processing signal: {e}")

    async def _publish_analysis_data(self, signal):
        """Publish analysis data for AI Market Analysis dashboard component."""
        try:
            # Get the latest market analysis from the strategy
            analysis = self.strategy.analyze_market_data(signal.symbol)

            if analysis:
                # Create analysis data that matches what the dashboard expects
                analysis_data = {
                    "symbol": signal.symbol,
                    "rsi": analysis.get("rsi", 50.0),
                    "trend": analysis.get("sma_trend", "neutral"),
                    "volatility": "Medium",  # Could be calculated from price data
                    "signal_strength": "Strong" if abs(signal.score) > 0.7 else "Medium" if abs(signal.score) > 0.4 else "Weak",
                    "price": analysis.get("current_price", 0),
                    "volume": analysis.get("volume", 0),
                    "timestamp": datetime.now().isoformat(),
                    "source": "order_flow_analysis"
                }

                # Publish to analysis stream that dashboard looks for
                self.bus.publish(f"analysis.{signal.symbol}.orderflow", datetime.now().timestamp(), analysis_data)
                logger.info(f"📊 Analysis data published for AI dashboard")

        except Exception as e:
            logger.error(f"❌ Error publishing analysis data: {e}")

    async def stop(self):
        """Stop the live strategy runner."""
        logger.info("🛑 Stopping live DataFrame strategy runner...")
        self.running = False

        if self.strategy:
            self.strategy.close()
            logger.info("✅ DataFrame strategy closed")

        if self.bus:
            self.bus.close()
            logger.info("✅ SQLite bus connection closed")

        # Print final statistics
        logger.info("📊 Final Statistics:")
        logger.info(f"   Total signals generated: {self.signal_count}")
        if self.last_signal_time:
            logger.info(f"   Last signal time: {self.last_signal_time}")


async def main():
    """Main entry point."""
    logger.info("🎯 Live DataFrame Strategy Runner")
    logger.info("=" * 60)
    logger.info("🔥 Running smart strategy on LIVE market data!")
    logger.info("📊 Using DataFrame-based analysis with real-time data")
    logger.info("=" * 60)

    # Create and start strategy runner
    runner = LiveDataFrameStrategyRunner()

    try:
        await runner.start()
    except KeyboardInterrupt:
        logger.info("📡 Keyboard interrupt received")
    finally:
        await runner.stop()
        logger.info("🏁 Live DataFrame strategy runner stopped")


if __name__ == "__main__":
    asyncio.run(main())
