#!/usr/bin/env python3
"""
Check the actual database schema to understand table structure.
"""

import sqlite3

def check_schema():
    """Check database schema."""
    try:
        conn = sqlite3.connect('data/money_circle.db')
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("DATABASE TABLES:")
        print("=" * 50)
        for table in tables:
            table_name = table[0]
            print(f"\n📋 Table: {table_name}")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, pk = col
                print(f"   {col_name} ({col_type})")
        
        # Check specific tables we need
        important_tables = ['users', 'user_trades', 'user_positions', 'member_profiles']
        
        print("\n" + "=" * 50)
        print("IMPORTANT TABLES DETAILS:")
        print("=" * 50)
        
        for table_name in important_tables:
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                print(f"\n📋 {table_name.upper()}:")
                if columns:
                    for col in columns:
                        col_id, col_name, col_type, not_null, default_val, pk = col
                        pk_marker = " (PK)" if pk else ""
                        null_marker = " NOT NULL" if not_null else ""
                        default_marker = f" DEFAULT {default_val}" if default_val else ""
                        print(f"   {col_name}: {col_type}{pk_marker}{null_marker}{default_marker}")
                else:
                    print("   ❌ Table does not exist")
                    
            except Exception as e:
                print(f"   ❌ Error checking {table_name}: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")

if __name__ == "__main__":
    check_schema()
