"""
Tests for the VWAP-Deviation model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.vwap_deviation import VWAPDeviationModel, DeviationSignal


class TestVWAPDeviationModel(unittest.TestCase):
    """Test the VWAP-Deviation model."""

    def setUp(self):
        """Set up test environment."""
        self.model = VWAPDeviationModel(
            lookback_periods=100,
            significant_deviation=0.5,  # Lower threshold for tests
            vwap_types=["1min", "5min", "15min"]
        )

    def test_timeframe_conversion(self):
        """Test timeframe string to minutes conversion."""
        self.assertEqual(self.model._get_minutes_from_timeframe("1min"), 1)
        self.assertEqual(self.model._get_minutes_from_timeframe("5min"), 5)
        self.assertEqual(self.model._get_minutes_from_timeframe("1h"), 60)
        self.assertEqual(self.model._get_minutes_from_timeframe("1d"), 1440)
        self.assertEqual(self.model._get_minutes_from_timeframe("invalid"), 1)  # Default

    def test_vwap_calculation(self):
        """Test VWAP calculation."""
        # Simple test case
        prices = [100.0, 101.0, 102.0, 103.0, 104.0]
        volumes = [10.0, 20.0, 15.0, 5.0, 10.0]

        # Calculate expected VWAP
        expected_vwap = sum(p * v for p, v in zip(prices, volumes)) / sum(volumes)

        # Calculate VWAP using the model
        vwap = self.model._calculate_vwap(prices, volumes)

        # Check result
        self.assertAlmostEqual(vwap, expected_vwap, places=6)

    def test_filter_by_timeframe(self):
        """Test filtering data by timeframe."""
        # Set up test data
        symbol = "BTC-USDT"
        now = datetime.now()

        # Create test data spanning 30 minutes
        self.model._price_cache[symbol] = [100.0 + i for i in range(30)]
        self.model._volume_cache[symbol] = [10.0 for _ in range(30)]
        self.model._timestamp_cache[symbol] = [
            now - timedelta(minutes=30-i) for i in range(30)
        ]

        # Filter for last 5 minutes
        filtered_prices, filtered_volumes = self.model._filter_by_timeframe(
            symbol, now, 5
        )

        # Check results
        self.assertEqual(len(filtered_prices), 5)
        self.assertEqual(len(filtered_volumes), 5)
        self.assertEqual(filtered_prices[0], 125.0)
        self.assertEqual(filtered_prices[-1], 129.0)

    def test_predict_with_synthetic_data(self):
        """Test predict method with synthetic data."""
        async def test_predict_async():
            # Create synthetic price data
            np.random.seed(42)
            base_price = 100.0

            # Generate prices with a trend and some noise
            prices = []
            volumes = []
            timestamps = []

            now = datetime.now()

            # Generate 100 data points, one per minute
            for i in range(100):
                # Price with trend and noise
                price = base_price + (i * 0.1) + np.random.normal(0, 1.0)
                volume = 10.0 + np.random.normal(0, 3.0)
                timestamp = now - timedelta(minutes=100-i)

                prices.append(price)
                volumes.append(max(0.1, volume))  # Ensure positive volume
                timestamps.append(timestamp)

            # Feed data points one by one
            for i in range(len(prices)):
                features = {
                    "symbol": "BTC-USDT",
                    "mid_price": prices[i],
                    "volume": volumes[i],
                    "timestamp": timestamps[i]
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == len(prices) - 1:
                    # Verify prediction structure
                    self.assertIn("vwap", prediction)
                    self.assertIn("z_score", prediction)
                    self.assertIn("primary_z_score", prediction)
                    self.assertIn("signal", prediction)
                    self.assertIn("bands", prediction)
                    self.assertIn("action", prediction)
                    self.assertIn("confidence", prediction)

                    # Verify VWAP values for each timeframe
                    for timeframe in self.model.vwap_types:
                        if timeframe in prediction["vwap"]:
                            self.assertGreater(prediction["vwap"][timeframe], 0)

                    # Verify signal is one of the valid signals
                    self.assertIn(prediction["signal"], [s.value for s in DeviationSignal])

                    # Verify action is one of the valid actions
                    self.assertIn(prediction["action"], ["BUY", "SELL", "HOLD"])

                    # Verify confidence is between 0 and 1
                    self.assertGreaterEqual(prediction["confidence"], 0.0)
                    self.assertLessEqual(prediction["confidence"], 1.0)

        # Run async test
        asyncio.run(test_predict_async())

    def test_signal_generation(self):
        """Test signal generation logic directly."""
        # Test ABOVE_VWAP signal
        if 1.5 > 0.5 or 150 > 100 * 1.2:  # This should be true
            signal = DeviationSignal.ABOVE_VWAP
        else:
            signal = DeviationSignal.AT_VWAP
        self.assertEqual(signal, DeviationSignal.ABOVE_VWAP)

        # Test BELOW_VWAP signal
        if -1.5 < -0.5 or 50 < 100 * 0.8:  # This should be true
            signal = DeviationSignal.BELOW_VWAP
        else:
            signal = DeviationSignal.AT_VWAP
        self.assertEqual(signal, DeviationSignal.BELOW_VWAP)

    def test_action_from_signal(self):
        """Test action determination from signal."""
        # Test ABOVE_VWAP -> SELL
        signal = DeviationSignal.ABOVE_VWAP
        if signal == DeviationSignal.ABOVE_VWAP:
            action = "SELL"
        elif signal == DeviationSignal.BELOW_VWAP:
            action = "BUY"
        else:
            action = "HOLD"
        self.assertEqual(action, "SELL")

        # Test BELOW_VWAP -> BUY
        signal = DeviationSignal.BELOW_VWAP
        if signal == DeviationSignal.ABOVE_VWAP:
            action = "SELL"
        elif signal == DeviationSignal.BELOW_VWAP:
            action = "BUY"
        else:
            action = "HOLD"
        self.assertEqual(action, "BUY")

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Create features with invalid price
            features = {
                "symbol": "BTC-USDT",
                "mid_price": 0.0,
                "volume": 10.0,
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["signal"], DeviationSignal.AT_VWAP.value)
            self.assertEqual(prediction["action"], "HOLD")
            self.assertEqual(prediction["confidence"], 0.0)
            self.assertEqual(prediction["primary_z_score"], 0.0)

        # Run async test
        asyncio.run(test_insufficient_data_async())


if __name__ == "__main__":
    unittest.main()
