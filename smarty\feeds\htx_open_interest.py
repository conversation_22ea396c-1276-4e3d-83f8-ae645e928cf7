"""
HTX Open Interest client for the smart-trader system.

This module fetches open interest data from HTX Futures API.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List

import aiohttp

from core.events import OpenInterestEvent
from core.utils import retry_async

logger = logging.getLogger(__name__)


class HTXOpenInterestClient:
    """
    HTX Open Interest client.

    Fetches open interest data from HTX Futures API.
    """

    # API endpoints
    REST_BASE_URL = "https://api.hbdm.com"
    OPEN_INTEREST_ENDPOINT = "/linear-swap-api/v1/swap_open_interest"

    def __init__(
        self,
        session: Optional[aiohttp.ClientSession] = None,
        testnet: bool = False
    ):
        """
        Initialize the HTX Open Interest client.

        Args:
            session: aiohttp session to use for requests
            testnet: Whether to use testnet
        """
        self.session = session
        self.testnet = testnet

        # Cache for open interest data
        self._oi_cache: Dict[str, Dict[str, Any]] = {}

    async def fetch_open_interest(self, symbol: str) -> Optional[OpenInterestEvent]:
        """
        Fetch current open interest for a symbol.

        Args:
            symbol: Trading symbol (e.g., "BTC-USDT")

        Returns:
            OpenInterestEvent or None if failed
        """
        try:
            # Create session if needed
            if self.session is None or self.session.closed:
                self.session = aiohttp.ClientSession()

            # Build URL
            url = f"{self.REST_BASE_URL}{self.OPEN_INTEREST_ENDPOINT}"

            # Prepare parameters
            params = {"contract_code": symbol}

            # Make request
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"Failed to fetch open interest: {response.status}, message='{await response.text()}', url='{url}'")
                    return None

                # Try to parse JSON response
                try:
                    # First try standard JSON parsing
                    try:
                        data = await response.json()
                        # Track successful JSON parse
                        try:
                            from core.feature_store import feature_store
                            asyncio.create_task(feature_store.increment(symbol, "metrics.open_interest.clean_json_responses", 1))
                        except Exception:
                            pass
                    except aiohttp.ContentTypeError:
                        # If content type error, try to parse text as JSON
                        text = await response.text()
                        logger.debug(f"Response text: {text[:200]}...")
                        data = json.loads(text)
                        # Track JSON parse fallback
                        try:
                            from core.feature_store import feature_store
                            asyncio.create_task(feature_store.increment(symbol, "metrics.open_interest.text_fallbacks", 1))
                        except Exception:
                            pass
                        logger.warning(f"Used text fallback for open interest JSON parsing: content_type='{response.content_type}'")
                except Exception as e:
                    logger.error(f"Error parsing open interest JSON response: {response.status}, message='{e}', url='{url}', content_type='{response.content_type}'")
                    # Track JSON parse error
                    try:
                        from core.feature_store import feature_store
                        asyncio.create_task(feature_store.increment(symbol, "metrics.open_interest.parse_errors", 1))
                    except Exception:
                        pass
                    # Use cached data if available
                    if symbol in self._oi_cache:
                        logger.warning(f"Using cached open interest for {symbol} due to parsing error")
                        cache_entry = self._oi_cache[symbol]
                        # Create event from cache
                        return OpenInterestEvent(
                            symbol=symbol,
                            timestamp=cache_entry["timestamp"],
                            open_interest=cache_entry["open_interest"],
                            amount=cache_entry["amount"]
                        )
                    return None

                # Check for errors
                if data.get("status") != "ok":
                    logger.error(f"API error: {data.get('err_msg', 'Unknown error')}")
                    return None

                # Extract open interest data
                oi_data = data.get("data", [])
                if not oi_data:
                    logger.warning(f"No open interest data for {symbol}")
                    return None

                # Get the first (and usually only) entry
                entry = oi_data[0]

                # Extract fields
                open_interest = float(entry.get("volume", 0.0))
                amount = float(entry.get("amount", 0.0))
                timestamp = datetime.now(timezone.utc)

                # Create event
                event = OpenInterestEvent(
                    symbol=symbol,
                    timestamp=timestamp,
                    open_interest=open_interest,
                    amount=amount
                )

                # Update cache
                self._oi_cache[symbol] = {
                    "open_interest": open_interest,
                    "amount": amount,
                    "timestamp": timestamp
                }

                return event

        except Exception as e:
            logger.error(f"Error fetching open interest: {e}")
            return None

    async def close(self) -> None:
        """
        Close the client session.
        """
        if self.session and not self.session.closed:
            await self.session.close()


async def fetch_open_interest(
    symbol: str,
    session: Optional[aiohttp.ClientSession] = None,
    testnet: bool = False
) -> Optional[OpenInterestEvent]:
    """
    Fetch current open interest for a symbol.

    This is a convenience function that creates a client and fetches the open interest.

    Args:
        symbol: Trading symbol (e.g., "BTC-USDT")
        session: aiohttp session to use for requests
        testnet: Whether to use testnet

    Returns:
        OpenInterestEvent or None if failed
    """
    client = HTXOpenInterestClient(session=session, testnet=testnet)

    async def fetch():
        return await client.fetch_open_interest(symbol)

    try:
        return await retry_async(
            fetch,
            retries=3,
            delay=1.0
        )
    finally:
        await client.close()
