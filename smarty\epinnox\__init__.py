"""
Epinnox Trading System - Enhanced Smart Strategy with CCXT Integration

This package contains the smart trading strategy and its dependencies,
enhanced for use with CCXT for multi-exchange support.

Based on the smart-trader system's smart_model_integrated_strategy.
"""

__version__ = "1.0.0"
__author__ = "Epinnox Trading"

# Core components
from .core.events import Signal, Side, OrderType
from .core.feature_store import FeatureStore
from .strategy import EpinnoxSmartStrategy

# Models
from .models.rsi import RSIModel
from .models.vwap_deviation import VWAPDeviationModel
from .models.funding_momentum import FundingMomentumModel
from .models.open_interest_momentum import OpenInterestMomentumModel

__all__ = [
    'Signal',
    'Side', 
    'OrderType',
    'FeatureStore',
    'EpinnoxSmartStrategy',
    'RSIModel',
    'VWAPDeviationModel', 
    'FundingMomentumModel',
    'OpenInterestMomentumModel'
]
