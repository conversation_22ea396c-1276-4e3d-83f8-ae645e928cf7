#!/usr/bin/env python3
"""
Test adding real HTX credentials to Money Circle platform.
"""

import requests
import yaml
import json
import sys
from pathlib import Path

def load_credentials():
    """Load credentials from credentials.yaml"""
    cred_file = Path("credentials.yaml")
    if not cred_file.exists():
        print("❌ credentials.yaml not found")
        return None
    
    with open(cred_file, 'r') as f:
        creds = yaml.safe_load(f)
    
    return creds

def test_real_htx_integration():
    """Test with real HTX credentials"""
    print("🚀 Testing Real HTX Credentials Integration")
    print("=" * 50)
    
    # Load credentials
    creds = load_credentials()
    if not creds:
        return False
    
    # Get HTX credentials
    accounts = creds.get('accounts', [])
    default_account = creds.get('default_account', 'EPX')
    
    # Find the default HTX account
    htx_account = None
    for account in accounts:
        if account.get('name') == default_account and account.get('exchange') == 'huobi':
            htx_account = account
            break
    
    if not htx_account:
        print("❌ No default HTX account found")
        return False
    
    api_key = htx_account.get('api_key')
    secret_key = htx_account.get('secret_key')
    account_name = htx_account.get('name')
    
    print(f"📋 Using account: {account_name} - {htx_account.get('description')}")
    print(f"📊 API Key: {api_key[:8]}...")
    
    # Login to Money Circle
    session = requests.Session()
    
    login_response = session.post(
        'http://localhost:8084/login',
        data={'username': 'epinnox', 'password': 'securepass123'},
        headers={'Content-Type': 'application/x-www-form-urlencoded'},
        allow_redirects=False
    )
    
    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return False
    
    print("✅ Login successful")
    
    # Add real HTX account
    print(f"📊 Adding real HTX account: {account_name}")
    
    account_data = {
        'exchange': 'huobi',
        'api_key': api_key,
        'secret_key': secret_key
    }
    
    response = session.post(
        'http://localhost:8084/api/exchange/add',
        json=account_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"📡 Response status: {response.status_code}")
    print(f"📄 Response: {response.text}")
    
    if response.status_code == 200:
        print("✅ Real HTX account added successfully!")
        
        # Test portfolio access
        print("\n🔍 Testing portfolio access...")
        portfolio_response = session.get('http://localhost:8084/api/portfolio')
        
        if portfolio_response.status_code == 200:
            portfolio_data = portfolio_response.json()
            print("✅ Portfolio data retrieved")
            
            exchanges = portfolio_data.get('exchanges', {})
            print(f"📊 Found {len(exchanges)} exchange connections")
            
            for exchange_name, exchange_data in exchanges.items():
                connected = exchange_data.get('connected', False)
                status = "✅ Connected" if connected else "❌ Disconnected"
                print(f"  🏦 {exchange_name}: {status}")
                
                if connected and 'balance' in exchange_data:
                    balance = exchange_data['balance']
                    total_currencies = len([k for k, v in balance.get('total', {}).items() if v > 0])
                    print(f"    💰 Balances in {total_currencies} currencies")
        
        return True
    else:
        print("❌ Failed to add real HTX account")
        try:
            error_data = response.json()
            print(f"   Error: {error_data.get('error', 'Unknown error')}")
        except:
            pass
        return False

def main():
    success = test_real_htx_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 REAL HTX INTEGRATION: SUCCESS")
        print("✅ Your HTX account is now connected to Money Circle!")
        print("🌐 You can access the dashboard at: http://localhost:8084")
        print("🔐 Login with: epinnox / securepass123")
    else:
        print("❌ REAL HTX INTEGRATION: FAILED")
        print("❌ Please check your HTX credentials")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
