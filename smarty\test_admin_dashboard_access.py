#!/usr/bin/env python3
"""
Test dashboard access using the default admin user to verify the fix works.
This bypasses the session persistence issue by using the known admin credentials.
"""

import requests
import sys

def test_admin_dashboard_access():
    """Test dashboard access using default admin user."""
    print("🔧 Testing Admin Dashboard Access")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as admin user
        print("1️⃣ Logging in as admin user (epinnox)...")
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123'
        }
        
        login_response = session.post(
            'http://localhost:8084/login',
            data=login_data,
            allow_redirects=False
        )
        
        print(f"📊 Login response status: {login_response.status_code}")
        
        if login_response.status_code != 302:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        location = login_response.headers.get('Location', '')
        print(f"🔄 Login redirected to: {location}")
        
        if '/dashboard' not in location:
            print(f"❌ Login didn't redirect to dashboard: {location}")
            return False
        
        print("✅ Login successful, redirected to dashboard")
        
        # Test direct dashboard access
        print("2️⃣ Testing direct dashboard access...")
        dashboard_response = session.get(
            'http://localhost:8084/dashboard',
            allow_redirects=False
        )
        
        print(f"📊 Dashboard response status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("🎉 SUCCESS! Admin can access dashboard directly!")
            print("✅ Dashboard redirect fix is working for users with accepted agreements!")
            return True
        elif dashboard_response.status_code == 302:
            redirect_location = dashboard_response.headers.get('Location', '')
            print(f"🔄 Dashboard redirected to: {redirect_location}")
            
            if '/agreement' in redirect_location:
                print("❌ FAILED! Admin redirected to agreement (this shouldn't happen)")
                return False
            elif '/login' in redirect_location:
                print("❌ FAILED! Admin redirected to login (session issue)")
                return False
            else:
                print(f"❓ Unexpected redirect: {redirect_location}")
                return False
        else:
            print(f"❌ FAILED! Unexpected response: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_welcome_page_role_display():
    """Test that the role display bug is fixed on welcome page."""
    print("\n🔧 Testing Welcome Page Role Display Fix")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as admin
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123'
        }
        
        session.post('http://localhost:8084/login', data=login_data)
        
        # Access welcome page
        print("1️⃣ Accessing welcome page...")
        welcome_response = session.get('http://localhost:8084/welcome')
        
        if welcome_response.status_code != 200:
            print(f"❌ Welcome page not accessible: {welcome_response.status_code}")
            return False
        
        content = welcome_response.text
        
        # Check for role display bug
        print("2️⃣ Checking role display...")
        if 'Role: Admin Member' in content:
            print("❌ FAILED! Role still shows 'Admin Member' (bug not fixed)")
            return False
        elif 'Role: Admin' in content:
            print("✅ SUCCESS! Role correctly shows 'Admin' (bug fixed)")
            return True
        else:
            print("❓ Role display not found in content")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_dashboard_button_link():
    """Test that the dashboard button on welcome page works correctly."""
    print("\n🔧 Testing Dashboard Button Link")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as admin
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123'
        }
        
        session.post('http://localhost:8084/login', data=login_data)
        
        # Access welcome page
        welcome_response = session.get('http://localhost:8084/welcome')
        
        if welcome_response.status_code != 200:
            print(f"❌ Welcome page not accessible: {welcome_response.status_code}")
            return False
        
        content = welcome_response.text
        
        # Check for dashboard button
        print("1️⃣ Checking dashboard button link...")
        if 'href="/dashboard"' in content and 'Continue to Dashboard' in content:
            print("✅ Dashboard button link is correct")
        else:
            print("❌ Dashboard button link not found or incorrect")
            return False
        
        # Test clicking the dashboard button (simulate)
        print("2️⃣ Testing dashboard button click (simulate)...")
        dashboard_response = session.get(
            'http://localhost:8084/dashboard',
            allow_redirects=False
        )
        
        if dashboard_response.status_code == 200:
            print("✅ SUCCESS! Dashboard button would work correctly!")
            return True
        else:
            print(f"❌ FAILED! Dashboard button would not work: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run admin dashboard access tests."""
    print("🚀 MONEY CIRCLE ADMIN DASHBOARD ACCESS - TESTING")
    print("=" * 60)
    
    tests = [
        ("Admin Dashboard Access", test_admin_dashboard_access),
        ("Welcome Page Role Display Fix", test_welcome_page_role_display),
        ("Dashboard Button Link", test_dashboard_button_link),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        if test_func():
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Dashboard redirect fix is working!")
        print("✅ Role display bug is fixed!")
        print("✅ Dashboard button link is working!")
        return 0
    else:
        print("❌ SOME ISSUES STILL EXIST")
        return 1

if __name__ == "__main__":
    sys.exit(main())
