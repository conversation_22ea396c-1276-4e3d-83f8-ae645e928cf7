#!/usr/bin/env python3
"""
Simple Demo Data Seeder for Money Circle
Creates essential demo data for immediate testing.
"""

import sqlite3
import random
import bcrypt
from datetime import datetime, timedelta

class SimpleDemoSeeder:
    """Simple demo data seeder for Money Circle."""
    
    def __init__(self, db_path: str = "data/money_circle.db"):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.execute("PRAGMA foreign_keys = ON")
    
    def seed_essential_data(self):
        """Seed essential demo data."""
        print("🌱 Starting simple demo data seeding...")
        
        # Clear existing data
        self.clear_data()
        
        # Create demo users
        users = self.create_demo_users()
        
        # Create some basic trading data
        self.create_demo_trades(users)
        
        self.conn.commit()
        print("✅ Simple demo data seeding completed!")
        
        # Show login info
        self.show_login_info()
    
    def clear_data(self):
        """Clear existing demo data."""
        print("🧹 Clearing existing data...")
        
        tables = ['user_trades', 'user_positions', 'user_exchanges', 'users']
        for table in tables:
            try:
                self.conn.execute(f"DELETE FROM {table}")
                print(f"   Cleared {table}")
            except sqlite3.OperationalError as e:
                print(f"   Warning: {table} - {e}")
    
    def create_demo_users(self):
        """Create demo user accounts."""
        print("👥 Creating demo users...")
        
        demo_users = [
            {"username": "alex_trader", "email": "<EMAIL>", "role": "admin"},
            {"username": "sarah_crypto", "email": "<EMAIL>", "role": "member"},
            {"username": "mike_scalper", "email": "<EMAIL>", "role": "member"},
            {"username": "emma_hodler", "email": "<EMAIL>", "role": "member"},
            {"username": "david_quant", "email": "<EMAIL>", "role": "member"}
        ]
        
        users = []
        for user_data in demo_users:
            try:
                # Hash password (all demo accounts use 'demo123')
                password_hash = bcrypt.hashpw('demo123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                
                # Create user
                cursor = self.conn.execute("""
                    INSERT INTO users (
                        username, email, hashed_password, role, 
                        date_joined, last_login, is_active, agreement_accepted
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_data['username'],
                    user_data['email'],
                    password_hash,
                    user_data['role'],
                    (datetime.now() - timedelta(days=30)).isoformat(),
                    (datetime.now() - timedelta(days=1)).isoformat(),
                    True,
                    True
                ))
                
                user_id = cursor.lastrowid
                users.append({
                    'user_id': user_id,
                    'username': user_data['username'],
                    'role': user_data['role']
                })
                
                print(f"   Created user: {user_data['username']} (ID: {user_id})")
                
                # Create agreement record
                self.conn.execute("""
                    INSERT INTO membership_agreements (
                        user_id, agreement_text, ip_address, user_agent, agreed_at
                    ) VALUES (?, ?, ?, ?, ?)
                """, (
                    user_id,
                    f"Demo Agreement for {user_data['username']}",
                    "127.0.0.1",
                    "Demo Browser",
                    datetime.now().isoformat()
                ))
                
            except Exception as e:
                print(f"   Error creating user {user_data['username']}: {e}")
        
        return users
    
    def create_demo_trades(self, users):
        """Create demo trading data."""
        print("💹 Creating demo trades...")
        
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        exchanges = ['HTX', 'Binance']
        
        total_trades = 0
        for user in users:
            # Create 10-20 trades per user
            num_trades = random.randint(10, 20)
            
            for _ in range(num_trades):
                try:
                    symbol = random.choice(symbols)
                    exchange = random.choice(exchanges)
                    side = random.choice(['buy', 'sell'])
                    
                    # Generate realistic trade data
                    if 'BTC' in symbol:
                        price = random.uniform(45000, 55000)
                        size = random.uniform(0.001, 0.1)
                    elif 'ETH' in symbol:
                        price = random.uniform(2800, 3200)
                        size = random.uniform(0.01, 1.0)
                    else:  # ADA
                        price = random.uniform(0.4, 0.6)
                        size = random.uniform(100, 1000)
                    
                    fee = price * size * 0.001  # 0.1% fee
                    
                    # Random timestamp within last 30 days
                    trade_time = datetime.now() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23)
                    )
                    
                    self.conn.execute("""
                        INSERT INTO user_trades (
                            user_id, exchange_name, symbol, side, size, price, fee,
                            order_type, strategy_name, timestamp
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user['user_id'],
                        exchange,
                        symbol,
                        side,
                        size,
                        price,
                        fee,
                        random.choice(['market', 'limit']),
                        random.choice(['Manual', 'Grid Bot', 'DCA']),
                        trade_time.isoformat()
                    ))
                    
                    total_trades += 1
                    
                except Exception as e:
                    print(f"   Error creating trade: {e}")
        
        print(f"   Created {total_trades} demo trades")
        
        # Create some demo positions
        self.create_demo_positions(users)
    
    def create_demo_positions(self, users):
        """Create demo positions."""
        print("📈 Creating demo positions...")
        
        total_positions = 0
        for user in users:
            # 50% chance of having 1-2 open positions
            if random.random() < 0.5:
                num_positions = random.randint(1, 2)
                
                for _ in range(num_positions):
                    try:
                        symbol = random.choice(['BTCUSDT', 'ETHUSDT'])
                        exchange = random.choice(['HTX', 'Binance'])
                        side = random.choice(['long', 'short'])
                        
                        if 'BTC' in symbol:
                            entry_price = random.uniform(48000, 52000)
                            current_price = entry_price * random.uniform(0.98, 1.02)
                            size = random.uniform(0.01, 0.05)
                        else:  # ETH
                            entry_price = random.uniform(2900, 3100)
                            current_price = entry_price * random.uniform(0.98, 1.02)
                            size = random.uniform(0.1, 0.5)
                        
                        # Calculate PnL
                        if side == 'long':
                            pnl = (current_price - entry_price) * size
                        else:
                            pnl = (entry_price - current_price) * size
                        
                        self.conn.execute("""
                            INSERT INTO user_positions (
                                user_id, exchange_name, symbol, side, size, 
                                entry_price, current_price, pnl, status, created_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            user['user_id'],
                            exchange,
                            symbol,
                            side,
                            size,
                            entry_price,
                            current_price,
                            pnl,
                            'open',
                            datetime.now().isoformat()
                        ))
                        
                        total_positions += 1
                        
                    except Exception as e:
                        print(f"   Error creating position: {e}")
        
        print(f"   Created {total_positions} demo positions")
    
    def show_login_info(self):
        """Show login information."""
        print("\n" + "="*50)
        print("🎯 DEMO LOGIN INFORMATION")
        print("="*50)
        print("Username: alex_trader (admin)")
        print("Username: sarah_crypto (member)")
        print("Username: mike_scalper (member)")
        print("Password: demo123 (for all accounts)")
        print("\n🚀 Ready to test Money Circle!")
        print("="*50)

def main():
    """Run the simple demo seeder."""
    seeder = SimpleDemoSeeder()
    seeder.seed_essential_data()

if __name__ == "__main__":
    main()
