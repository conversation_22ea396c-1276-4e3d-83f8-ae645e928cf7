"""
Tests for the Funding Momentum model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.funding_momentum import FundingMomentumModel, FundingSignal
from core.feature_store import feature_store


class TestFundingMomentumModel(unittest.TestCase):
    """Test the Funding Momentum model."""

    def setUp(self):
        """Set up test environment."""
        self.model = FundingMomentumModel(
            short_window=5,
            long_window=20,
            signal_threshold=1.0,
            contrarian=True
        )

    def test_delta_calculation(self):
        """Test funding rate delta calculation."""
        # Create test funding rate data
        symbol = "BTC-USDT"
        now = datetime.now()

        # Add funding rates to cache
        self.model._funding_cache[symbol] = []
        for i in range(10):
            # Funding rate increases by 0.001 each step
            rate = 0.001 * i
            time = now - timedelta(minutes=10-i)
            self.model._funding_cache[symbol].append((time, rate))

        # Calculate delta
        delta = self.model._calculate_delta(symbol)

        # Delta should be the difference between latest and 5 minutes ago
        expected_delta = 0.001 * 5  # 5 steps * 0.001
        self.assertAlmostEqual(delta, expected_delta, places=6)

    def test_z_score_calculation(self):
        """Test z-score calculation."""
        # Create test delta data
        symbol = "BTC-USDT"
        now = datetime.now()

        # Add deltas to cache
        self.model._delta_cache[symbol] = []
        for i in range(10):
            # Constant delta of 0.001
            delta = 0.001
            time = now - timedelta(minutes=10-i)
            self.model._delta_cache[symbol].append((time, delta))

        # Add an outlier delta
        self.model._delta_cache[symbol].append((now, 0.005))  # 5x the normal delta

        # Calculate z-score
        z_score = self.model._calculate_z_score(symbol)

        # Z-score should be positive (outlier is higher than mean)
        self.assertGreater(z_score, 0)

    def test_action_from_signal_contrarian(self):
        """Test action determination with contrarian approach."""
        # Test positive momentum
        action, confidence = self.model._get_action_from_signal(
            FundingSignal.POSITIVE_MOMENTUM, 2.0
        )
        self.assertEqual(action, "SELL")  # Contrarian: sell when funding rate increases
        self.assertGreater(confidence, 0)

        # Test negative momentum
        action, confidence = self.model._get_action_from_signal(
            FundingSignal.NEGATIVE_MOMENTUM, -2.0
        )
        self.assertEqual(action, "BUY")  # Contrarian: buy when funding rate decreases
        self.assertGreater(confidence, 0)

        # Test neutral
        action, confidence = self.model._get_action_from_signal(
            FundingSignal.NEUTRAL, 0.0
        )
        self.assertEqual(action, "HOLD")
        self.assertEqual(confidence, 0.0)

    def test_action_from_signal_trend_following(self):
        """Test action determination with trend-following approach."""
        # Create a trend-following model
        trend_model = FundingMomentumModel(contrarian=False)

        # Test positive momentum
        action, confidence = trend_model._get_action_from_signal(
            FundingSignal.POSITIVE_MOMENTUM, 2.0
        )
        self.assertEqual(action, "BUY")  # Trend: buy when funding rate increases
        self.assertGreater(confidence, 0)

        # Test negative momentum
        action, confidence = trend_model._get_action_from_signal(
            FundingSignal.NEGATIVE_MOMENTUM, -2.0
        )
        self.assertEqual(action, "SELL")  # Trend: sell when funding rate decreases
        self.assertGreater(confidence, 0)

    def test_predict_with_synthetic_data(self):
        """Test predict method with synthetic data."""
        async def test_predict_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic funding rate data with a steady increase
            for i in range(30):
                # Funding rate increases by 0.001 each step
                rate = 0.001 * i
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "funding_rate", rate)
                await feature_store.add_time_series(symbol, "funding_rates", rate, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # Verify prediction structure
                    self.assertIn("funding_rate", prediction)
                    self.assertIn("funding_delta", prediction)
                    self.assertIn("funding_delta_z", prediction)
                    self.assertIn("signal", prediction)
                    self.assertIn("action", prediction)
                    self.assertIn("confidence", prediction)

                    # With steadily increasing funding rate, we should get POSITIVE_MOMENTUM
                    self.assertEqual(prediction["signal"], FundingSignal.POSITIVE_MOMENTUM.value)

                    # With contrarian approach, we should get SELL
                    self.assertEqual(prediction["action"], "SELL")

                    # Verify confidence is between 0 and 1
                    self.assertGreaterEqual(prediction["confidence"], 0.0)
                    self.assertLessEqual(prediction["confidence"], 1.0)

        # Run async test
        asyncio.run(test_predict_async())

    def test_predict_with_flat_data(self):
        """Test predict method with flat funding rate data."""
        async def test_flat_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic funding rate data with a constant rate
            for i in range(30):
                # Constant funding rate
                rate = 0.001
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "funding_rate", rate)
                await feature_store.add_time_series(symbol, "funding_rates", rate, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # With flat funding rate, we should get NEUTRAL
                    self.assertEqual(prediction["signal"], FundingSignal.NEUTRAL.value)

                    # With neutral signal, we should get HOLD
                    self.assertEqual(prediction["action"], "HOLD")

                    # Verify delta is close to zero
                    self.assertAlmostEqual(prediction["funding_delta"], 0.0, places=6)

        # Run async test
        asyncio.run(test_flat_data_async())

    def test_predict_with_decreasing_data(self):
        """Test predict method with decreasing funding rate data."""
        async def test_decreasing_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic funding rate data with a steady decrease
            for i in range(30):
                # Funding rate decreases by 0.001 each step
                rate = 0.03 - (0.001 * i)
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "funding_rate", rate)
                await feature_store.add_time_series(symbol, "funding_rates", rate, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # With steadily decreasing funding rate, we should get NEGATIVE_MOMENTUM
                    self.assertEqual(prediction["signal"], FundingSignal.NEGATIVE_MOMENTUM.value)

                    # With contrarian approach, we should get BUY
                    self.assertEqual(prediction["action"], "BUY")

                    # Verify delta is negative
                    self.assertLess(prediction["funding_delta"], 0)

        # Run async test
        asyncio.run(test_decreasing_data_async())

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"

            # Call predict without any data in feature store
            features = {
                "symbol": symbol,
                "timestamp": datetime.now()
            }

            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["signal"], FundingSignal.NEUTRAL.value)
            self.assertEqual(prediction["action"], "HOLD")
            self.assertEqual(prediction["confidence"], 0.0)

        # Run async test
        asyncio.run(test_insufficient_data_async())


if __name__ == "__main__":
    unittest.main()
