"""
HTX Futures API constants and configuration.
"""

# Version information
__version__ = "2.0.0"

# === HTX USDT-M Futures PRODUCTION ENDPOINTS ===
# NOTE: These endpoints are currently geo-blocked in many regions
# Use VPN with Asia region (HK/SG/JP) to access production endpoints
REST_BASE_URL = "https://api-usdt.linear.contract.huobi.pro"
WS_MARKET_URL = "wss://api-usdt.linear.contract.huobi.pro/ws"
WS_PRIVATE_URL = "wss://api-usdt.linear.contract.huobi.pro/notification"

# Testnet endpoints (may also be geo-blocked)
TESTNET_REST_BASE_URL = "https://api-swap-testnet.huobiapi.com"
TESTNET_WS_MARKET_URL = "wss://api-swap-testnet.huobiapi.com/ws"
TESTNET_WS_PRIVATE_URL = "wss://api-swap-testnet.huobiapi.com/notification"

# WebSocket channels
CHANNEL_KLINE = "market.{symbol}.kline.{interval}"
CHANNEL_TRADE = "market.{symbol}.trade.detail"
CHANNEL_DEPTH = "market.{symbol}.depth.step0"
CHANNEL_POSITION = "positions.{symbol}"
CHANNEL_ORDER = "orders.{symbol}"

# API endpoints
ENDPOINTS = {
    "contract_info": "/linear-swap-api/v1/swap_contract_info",
    "price_limits": "/linear-swap-api/v1/swap_price_limit",
    "account_info": "/linear-swap-api/v1/swap_account_info",
    "position_info": "/linear-swap-api/v1/swap_position_info",
    "place_order": "/linear-swap-api/v1/swap_order",
    "cancel_order": "/linear-swap-api/v1/swap_cancel",
    "order_info": "/linear-swap-api/v1/swap_order_info",
    "open_orders": "/linear-swap-api/v1/swap_openorders",
    "set_leverage": "/linear-swap-api/v1/swap_cross_switch_lever_rate",
}

# Default configuration values
DEFAULT_CONFIG = {
    "ping_interval": 20,
    "queue_size": 1000,
    "max_reconnect_delay": 60,
    "reconnect_backoff_factor": 2.0,
    "request_timeout": 30,
    "max_retries": 3,
    "retry_delay": 1.0,
    "debug": False,
    "verbosity": "INFO",
}

# Error messages
ERROR_MESSAGES = {
    "not_connected": "Cannot perform operation: not connected to HTX",
    "not_authenticated": "Cannot perform operation: not authenticated",
    "invalid_symbol": "Invalid symbol format",
    "invalid_order": "Invalid order parameters",
    "queue_full": "Message queue is full, dropping message",
    "connection_failed": "Failed to establish connection to HTX",
    "auth_failed": "Authentication with HTX failed",
    "api_error": "HTX API returned an error",
    "timeout": "Operation timed out",
    "rate_limit": "Rate limit exceeded",
}

# WebSocket message types
WS_MESSAGE_TYPES = {
    "ping": "ping",
    "pong": "pong",
    "subscribe": "sub",
    "unsubscribe": "unsub",
    "auth": "auth",
    "notify": "notify",
}

# Order status mappings
ORDER_STATUS_MAP = {
    1: "SUBMITTED",
    2: "SUBMITTED",
    3: "PARTIALLY_FILLED",
    4: "FILLED",
    5: "PARTIALLY_CANCELED",
    6: "CANCELED",
    7: "CANCELED",
    11: "CANCELING",
}

# Rate limiting
RATE_LIMITS = {
    "rest_requests_per_second": 10,
    "ws_subscriptions_per_second": 5,
    "order_requests_per_second": 5,
}

# Validation patterns
SYMBOL_PATTERN = r"^[A-Z]+-USDT$"
INTERVAL_PATTERN = r"^(1min|5min|15min|30min|60min|4hour|1day|1week|1mon)$"

# Health check configuration
HEALTH_CHECK = {
    "ping_timeout": 10.0,
    "max_missed_pongs": 3,
    "connection_check_interval": 30.0,
}
