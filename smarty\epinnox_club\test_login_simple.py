#!/usr/bin/env python3
"""
Simple login test for Money Circle demo accounts.
"""

import requests
import sys

def test_login():
    """Test login with demo accounts."""
    print("🔐 TESTING MONEY CIRCLE LOGIN")
    print("=" * 40)
    
    # Test accounts
    test_accounts = [
        ('epinnox', 'securepass123'),
        ('trader_alex', 'securepass123'),
        ('crypto_sarah', 'securepass123')
    ]
    
    for username, password in test_accounts:
        try:
            # Create a session to handle cookies
            session = requests.Session()
            
            # First get the login page to establish session
            login_page = session.get('http://localhost:8084/login')
            print(f"Login page status for {username}: {login_page.status_code}")
            
            # Submit login form
            login_data = {
                'username': username,
                'password': password
            }
            
            response = session.post('http://localhost:8084/login', data=login_data, allow_redirects=False)
            
            print(f"Username: {username}")
            print(f"  Response status: {response.status_code}")
            print(f"  Response headers: {dict(response.headers)}")
            
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                if '/dashboard' in location:
                    print(f"  ✅ Login successful - redirected to {location}")
                    
                    # Test accessing dashboard
                    dashboard = session.get('http://localhost:8084/dashboard')
                    print(f"  Dashboard access: {dashboard.status_code}")
                    
                    if dashboard.status_code == 200:
                        print(f"  ✅ Dashboard accessible")
                    else:
                        print(f"  ⚠️ Dashboard not accessible")
                        
                elif 'error=' in location:
                    print(f"  ❌ Login failed - {location}")
                else:
                    print(f"  ⚠️ Unexpected redirect - {location}")
            else:
                print(f"  ❌ Login failed - status {response.status_code}")
            
            print()
            
        except Exception as e:
            print(f"  ❌ Error testing {username}: {e}")
            print()

def main():
    """Main test function."""
    print("🎯 MONEY CIRCLE LOGIN TEST")
    print("=" * 50)
    
    try:
        # Test if server is running
        response = requests.get('http://localhost:8084', timeout=5)
        print(f"✅ Server is running (status: {response.status_code})")
    except:
        print("❌ Server is not running!")
        print("Please start the server with: python start_money_circle.py")
        return 1
    
    print()
    test_login()
    
    print("🌐 Manual Test:")
    print("Visit http://localhost:8084/login in your browser")
    print("Try logging in with:")
    print("  Username: epinnox")
    print("  Password: securepass123")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
