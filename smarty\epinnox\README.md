# 🚀 Epinnox Trading System

**Enhanced Smart Trading Strategy with CCXT Multi-Exchange Support**

Epinnox is an advanced algorithmic trading system built on the foundation of the smart-trader system's `smart_model_integrated_strategy`. It combines multiple AI models and technical indicators to generate sophisticated trading signals across multiple exchanges using CCXT.

## ✨ Features

### 🧠 **Smart Strategy Components**
- **Technical Analysis**: SMA crossovers, RSI, Bollinger Bands
- **VWAP Deviation**: Volume-weighted average price analysis
- **RSI Model**: Advanced RSI with divergence detection
- **Funding Momentum**: Futures funding rate momentum analysis
- **Open Interest**: Market sentiment through OI changes
- **Signal Fusion**: Weighted ensemble of all components

### 🌐 **Multi-Exchange Support**
- **CCXT Integration**: Support for 100+ exchanges
- **Unified Interface**: Single API for all exchanges
- **Real-time Data**: Live market data streaming
- **Exchange-Aware**: Separate feature stores per exchange

### 📊 **Advanced Analytics**
- **Dynamic Thresholds**: Adaptive buy/sell thresholds
- **Confidence Weighting**: Signal agreement analysis
- **Performance Metrics**: Comprehensive backtesting
- **Risk Management**: Built-in position sizing and stops

## 🏗️ Architecture

```
epinnox/
├── core/                    # Core system components
│   ├── events.py           # Data structures and events
│   ├── feature_store.py    # Multi-exchange data storage
│   └── utils.py            # Utility functions
├── models/                  # AI/ML models
│   ├── rsi.py              # RSI model with divergences
│   ├── vwap_deviation.py   # VWAP deviation detector
│   ├── funding_momentum.py # Funding rate momentum
│   └── open_interest_momentum.py # OI momentum
├── strategy.py             # Main smart strategy
├── ccxt_integration.py     # CCXT exchange manager
├── config.yaml            # Configuration file
├── example.py             # Usage examples
└── README.md              # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Install dependencies
pip install ccxt pandas numpy pyyaml

# Navigate to epinnox directory
cd smarty/epinnox
```

### 2. Configuration

Edit `config.yaml` to configure exchanges and strategy parameters:

```yaml
# Exchange Configuration
exchanges:
  default: "binance"
  enabled:
    - "binance"
    - "bybit"
  
  binance:
    sandbox: true
    api_key: "your_api_key"
    api_secret: "your_api_secret"

# Strategy Configuration
strategy:
  weights:
    technical: 0.3
    vwap: 0.2
    rsi_model: 0.15
    funding: 0.1
    open_interest: 0.1
```

### 3. Run Examples

```bash
# Live trading (simulation mode)
python example.py live

# Backtesting
python example.py backtest

# Strategy optimization
python example.py optimize
```

## 📈 Strategy Details

### **Signal Generation Process**

1. **Data Collection**: Real-time market data from multiple exchanges
2. **Feature Calculation**: Technical indicators and model inputs
3. **Model Predictions**: Individual model signals
4. **Signal Fusion**: Weighted ensemble of all signals
5. **Threshold Application**: Dynamic buy/sell thresholds
6. **Confidence Scoring**: Agreement-based confidence weighting

### **Component Weights (Default)**

| Component | Weight | Description |
|-----------|--------|-------------|
| Technical | 0.30 | SMA, RSI, Bollinger Bands |
| VWAP | 0.20 | Volume-weighted price deviation |
| RSI Model | 0.15 | Advanced RSI with divergences |
| Funding | 0.10 | Funding rate momentum (futures) |
| Open Interest | 0.10 | OI momentum analysis (futures) |
| Volatility | 0.10 | Volatility regime detection |
| Ensemble | 0.05 | Meta-ensemble signals |

### **Signal Fusion Formula**

```python
ensemble_score = Σ(signal_i × weight_i) / total_weight
confidence_weighted_score = ensemble_score × confidence_multiplier
```

## 🔧 Advanced Usage

### **Custom Strategy Configuration**

```python
from epinnox import EpinnoxSmartStrategy

# Custom weights
config = {
    "strategy": {
        "weights": {
            "technical": 0.4,
            "vwap": 0.3,
            "rsi_model": 0.2,
            "funding": 0.1
        },
        "base_buy_threshold": 0.25,
        "base_sell_threshold": -0.25
    }
}

strategy = EpinnoxSmartStrategy(config["strategy"])
```

### **Multi-Exchange Signal Generation**

```python
from epinnox import CCXTManager, EpinnoxSmartStrategy
from datetime import datetime

# Initialize components
ccxt_manager = CCXTManager(config)
strategy = EpinnoxSmartStrategy(config["strategy"])

# Generate signals for multiple exchanges
symbols = ["BTC/USDT", "ETH/USDT"]
for exchange_id in ["binance", "bybit"]:
    signals = await strategy.generate_signals(
        timestamp=datetime.now(),
        symbols=symbols,
        exchange=exchange_id
    )
```

### **Model Status Monitoring**

```python
# Get detailed model status
status = strategy.get_model_status()
print(f"Current weights: {status['weights']}")
print(f"RSI settings: {status['models']['rsi']}")
print(f"Thresholds: {status['thresholds']}")
```

## 📊 Backtesting & Optimization

### **Backtesting Configuration**

```yaml
backtesting:
  enabled: true
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_balance: 10000
  commission: 0.001
  slippage: 0.0005
```

### **Optimization Parameters**

```yaml
optimization:
  method: "grid_search"
  target_metric: "sharpe_ratio"
  parameter_ranges:
    weights:
      technical: [0.2, 0.4]
      vwap: [0.1, 0.3]
    thresholds:
      base_buy_threshold: [0.2, 0.4]
```

## 🛡️ Risk Management

### **Built-in Risk Controls**
- **Position Sizing**: Configurable position sizes
- **Stop Losses**: Percentage-based stop losses
- **Take Profits**: Automatic profit taking
- **Max Drawdown**: Portfolio protection
- **Leverage Limits**: Futures leverage control

### **Configuration Example**

```yaml
trading:
  position_size: 0.01  # BTC
  leverage: 1
  stop_loss_pct: 2.0
  take_profit_pct: 4.0
  max_drawdown_pct: 5.0
```

## 🔍 Monitoring & Alerts

### **Performance Metrics**
- Real-time P&L tracking
- Sharpe ratio calculation
- Maximum drawdown monitoring
- Win rate analysis
- Signal accuracy metrics

### **Alert Configuration**

```yaml
monitoring:
  alerts:
    max_drawdown: 5.0
    min_sharpe_ratio: 0.5
    max_consecutive_losses: 5
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is part of the smart-trader system and follows the same licensing terms.

## 🙏 Acknowledgments

- Built on the foundation of the smart-trader system
- Powered by CCXT for exchange connectivity
- Inspired by quantitative trading best practices

---

**⚠️ Disclaimer**: This software is for educational and research purposes. Always test thoroughly before using with real funds. Trading involves risk of loss.
