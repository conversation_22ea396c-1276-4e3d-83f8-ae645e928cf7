<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Money Circle - Authentication{% endblock %}</title>

    <!-- Critical CSS for Authentication Pages -->
    <style>
        /* Critical authentication CSS inlined for instant rendering */
        :root {
            --primary-50: #faf5ff;
            --primary-600: #8b5cf6;
            --primary-700: #7c3aed;
            --primary-800: #6d28d9;
            --primary-900: #581c87;
            --success-500: #22c55e;
            --error-500: #ef4444;
            --warning-500: #f59e0b;
            --bg-primary: #0f1419;
            --bg-secondary: #1a1f2e;
            --bg-card: rgba(255, 255, 255, 0.05);
            --bg-card-hover: rgba(255, 255, 255, 0.08);
            --text-primary: #f1f5f9;
            --text-secondary: #e2e8f0;
            --text-tertiary: #94a3b8;
            --text-muted: #64748b;
            --border-primary: rgba(255, 255, 255, 0.1);
            --border-accent: rgba(139, 92, 246, 0.3);
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --transition-normal: 300ms ease;
            --touch-target-min: 44px;
            --touch-target-comfortable: 48px;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3);
        }

        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-secondary);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Authentication Container */
        .auth-container {
            width: 100%;
            max-width: 420px;
            margin: var(--space-4);
            padding: 0;
        }

        .auth-card {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-lg);
            transition: var(--transition-normal);
        }

        /* Authentication Header */
        .auth-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .auth-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .logo-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            font-size: 1.75rem;
            box-shadow: var(--shadow-glow);
        }

        .auth-header h1 {
            font-size: 2.25rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .auth-header h2 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
            margin: var(--space-4) 0 var(--space-2) 0;
        }

        .auth-header p {
            color: var(--text-tertiary);
            font-size: 1rem;
            margin: 0;
        }

        /* Form Styles */
        .auth-form {
            margin-bottom: var(--space-6);
        }

        .form-group {
            position: relative;
            margin-bottom: var(--space-6);
        }

        .form-group label {
            display: block;
            color: var(--text-secondary);
            margin-bottom: var(--space-2);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: var(--space-4);
            padding-right: var(--space-8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: rgba(255, 255, 255, 0.02);
            color: var(--text-primary);
            font-size: 1rem;
            transition: var(--transition-normal);
            min-height: var(--touch-target-min);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-600);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            background: rgba(255, 255, 255, 0.05);
        }

        .form-group input::placeholder {
            color: var(--text-muted);
        }

        .input-icon {
            position: absolute;
            right: var(--space-4);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 1.25rem;
            pointer-events: none;
            margin-top: 12px; /* Adjust for label */
        }

        /* Button Styles */
        .auth-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-4) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition-normal);
            min-height: var(--touch-target-comfortable);
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        .auth-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Error Messages */
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
            color: var(--error-500);
            font-size: 0.875rem;
            text-align: center;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
            color: var(--success-500);
            font-size: 0.875rem;
            text-align: center;
        }

        /* Links */
        .auth-links {
            text-align: center;
            margin-top: var(--space-6);
        }

        .auth-links p {
            color: var(--text-tertiary);
            font-size: 0.875rem;
            margin: 0;
        }

        .auth-links a {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-normal);
        }

        .auth-links a:hover {
            color: var(--primary-500);
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .auth-container {
                margin: var(--space-2);
            }
            
            .auth-card {
                padding: var(--space-6);
            }
            
            .auth-header h1 {
                font-size: 1.875rem;
            }
            
            .auth-header h2 {
                font-size: 1.25rem;
            }
            
            .logo-icon {
                width: 48px;
                height: 48px;
                font-size: 1.5rem;
            }
        }

        /* Touch Device Optimizations */
        @media (hover: hover) {
            .auth-btn:hover {
                background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
                transform: translateY(-1px);
                box-shadow: var(--shadow-glow);
            }
            
            .auth-card:hover {
                border-color: var(--border-accent);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }
        }

        @media (hover: none) {
            .auth-btn:focus,
            .auth-btn:active {
                background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
                transform: scale(0.98);
            }
        }
    </style>

    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Meta tags for SEO -->
    <meta name="description" content="{% block description %}Money Circle - Professional Investment Club Authentication{% endblock %}">
    <meta name="keywords" content="investment club, trading, authentication, login">
    <meta name="author" content="Epinnox">
    <meta name="robots" content="noindex, nofollow">
</head>
<body class="{% block body_class %}auth-page{% endblock %}">
    <!-- Authentication Content -->
    <div class="auth-container">
        {% block content %}{% endblock %}
    </div>

    <!-- Core JavaScript for Authentication -->
    <script>
        // Authentication page utilities
        window.MoneyCircle = {
            auth: {
                showToast: function(message, type = 'info', duration = 3000) {
                    // Simple toast notification for auth pages
                    const toast = document.createElement('div');
                    toast.className = `toast toast-${type}`;
                    toast.textContent = message;
                    toast.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        padding: 12px 20px;
                        border-radius: 8px;
                        color: white;
                        font-weight: 500;
                        z-index: 1000;
                        transition: all 0.3s ease;
                        background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#8b5cf6'};
                    `;
                    
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(100%)';
                        setTimeout(() => document.body.removeChild(toast), 300);
                    }, duration);
                }
            }
        };

        // Global error handler for auth pages
        window.addEventListener('error', function(e) {
            console.error('Authentication page error:', e.error);
        });

        // Initialize authentication page
        document.addEventListener('DOMContentLoaded', function() {
            // Focus first input field
            const firstInput = document.querySelector('input[type="text"], input[type="email"]');
            if (firstInput) {
                firstInput.focus();
            }
            
            // Add loading states to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.textContent;
                        submitBtn.textContent = 'Processing...';
                        
                        // Re-enable after 10 seconds as fallback
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.textContent = originalText;
                        }, 10000);
                    }
                });
            });
        });
    </script>

    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
