#!/usr/bin/env python3
"""
Comprehensive test for Money Circle Real-Time Market Data Integration.
Tests all new market data features: Multi-exchange feeds, WebSocket streaming, API endpoints, and UI integration.
"""

import requests
import websocket
import json
import sys
import time
import threading
from datetime import datetime

def test_market_data_api_endpoints():
    """Test all market data API endpoints."""
    print("🚀 TESTING REAL-TIME MARKET DATA API ENDPOINTS")
    print("=" * 70)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print(f"❌ Login failed: {login_response.status_code}")
        return False
    
    print("✅ Authentication successful")
    
    # Test market data API endpoints
    api_tests = [
        ('/api/market/tickers', 'All Tickers API'),
        ('/api/market/ticker/BTCUSDT', 'BTC Ticker API'),
        ('/api/market/ticker/ETHUSDT', 'ETH Ticker API'),
        ('/api/market/status', 'Market Status API'),
        ('/api/market/health', 'Market Health API'),
        ('/api/market/realtime/BTCUSDT', 'Real-time BTC Data API'),
        ('/api/market/quality', 'Data Quality API'),
        ('/api/market/history/BTCUSDT', 'Price History API'),
        ('/api/market/stats/BTCUSDT', 'Market Statistics API')
    ]
    
    successful_tests = 0
    
    for endpoint, name in api_tests:
        try:
            print(f"🧪 Testing {name}...")
            response = session.get(f'http://localhost:8084{endpoint}')
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    print(f"✅ {name}: SUCCESS")
                    
                    # Validate data structure
                    if 'ticker' in endpoint and 'data' in data:
                        tick_data = data['data']
                        required_fields = ['price', 'bid', 'ask', 'volume_24h', 'change_24h_percent']
                        
                        if all(field in tick_data for field in required_fields):
                            print(f"   📊 Data structure valid: Price=${tick_data['price']:.2f}, Change={tick_data['change_24h_percent']:.2f}%")
                        else:
                            print(f"   ⚠️ Missing required fields in ticker data")
                    
                    elif 'status' in endpoint and 'status' in data:
                        status = data['status']
                        print(f"   📊 Market Status: {status['overall_health']}, Connections: {status['active_connections']}/{status['total_connections']}")
                    
                    elif 'health' in endpoint and 'health' in data:
                        health = data['health']
                        print(f"   📊 Health Score: {health['score']}/100, Status: {health['status']}")
                    
                    elif 'quality' in endpoint and 'quality' in data:
                        quality = data['quality']
                        print(f"   📊 Quality Score: {quality['overall_score']}/100")
                    
                    successful_tests += 1
                else:
                    print(f"❌ {name}: API returned error - {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {name}: Exception - {e}")
    
    print(f"\n📊 API Test Results: {successful_tests}/{len(api_tests)} endpoints working")
    return successful_tests >= len(api_tests) * 0.8  # 80% success rate

def test_websocket_market_data():
    """Test WebSocket market data streaming."""
    print("\n🌐 TESTING WEBSOCKET MARKET DATA STREAMING")
    print("=" * 70)
    
    messages_received = []
    connection_successful = False
    
    def on_message(ws, message):
        try:
            data = json.loads(message)
            messages_received.append(data)
            
            print(f"📨 Received: {data.get('type', 'unknown')} message")
            
            if data.get('type') == 'welcome':
                print(f"   🎉 Welcome: {data.get('message', '')}")
                
                # Subscribe to symbols
                subscribe_msg = {
                    'type': 'subscribe',
                    'symbols': ['BTC/USDT', 'ETH/USDT']
                }
                ws.send(json.dumps(subscribe_msg))
                print("   📡 Subscription request sent")
            
            elif data.get('type') == 'subscribed':
                print(f"   ✅ Subscribed to: {data.get('symbol', '')}")
            
            elif data.get('type') == 'ticker':
                symbol = data.get('symbol', '')
                ticker_data = data.get('data', {})
                price = ticker_data.get('price', 0)
                change = ticker_data.get('change_24h_percent', 0)
                source = ticker_data.get('source', 'unknown')
                quality = ticker_data.get('quality', 'unknown')
                
                print(f"   📊 {symbol}: ${price:.2f} ({change:+.2f}%) from {source} [{quality}]")
            
            elif data.get('type') == 'heartbeat':
                print("   💓 Heartbeat received")
                
        except Exception as e:
            print(f"   ❌ Error processing message: {e}")
    
    def on_error(ws, error):
        print(f"❌ WebSocket error: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("🔌 WebSocket connection closed")
    
    def on_open(ws):
        nonlocal connection_successful
        connection_successful = True
        print("✅ WebSocket connection established")
    
    try:
        print("🔄 Connecting to WebSocket...")
        ws = websocket.WebSocketApp(
            "ws://localhost:8084/ws/market",
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # Run WebSocket in a separate thread with timeout
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        # Wait for connection and messages
        time.sleep(10)  # Wait 10 seconds for messages
        
        ws.close()
        
        print(f"\n📊 WebSocket Test Results:")
        print(f"   Connection successful: {connection_successful}")
        print(f"   Messages received: {len(messages_received)}")
        
        # Analyze message types
        message_types = {}
        for msg in messages_received:
            msg_type = msg.get('type', 'unknown')
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        print(f"   Message types: {message_types}")
        
        # Check for ticker messages (real-time data)
        ticker_messages = [msg for msg in messages_received if msg.get('type') == 'ticker']
        if ticker_messages:
            print(f"   ✅ Real-time ticker data received: {len(ticker_messages)} updates")
            return True
        else:
            print(f"   ⚠️ No real-time ticker data received")
            return connection_successful
            
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

def test_dashboard_integration():
    """Test dashboard integration with real-time market data."""
    print("\n🎨 TESTING DASHBOARD INTEGRATION")
    print("=" * 70)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    try:
        # Test personal dashboard
        print("🧪 Testing personal dashboard...")
        dashboard_response = session.get('http://localhost:8084/dashboard')
        
        if dashboard_response.status_code == 200:
            content = dashboard_response.text
            
            # Check for enhanced market data features
            enhanced_features = [
                'market-status-bar',
                'source-indicator',
                'data-quality',
                'ticker-card',
                'price-section',
                'bid-ask',
                'data-info',
                'market-health',
                'connectMarketDataWebSocket',
                'updateTickerData',
                'ws/market'
            ]
            
            found_features = 0
            for feature in enhanced_features:
                if feature in content:
                    found_features += 1
                    print(f"   ✅ Found: {feature}")
                else:
                    print(f"   ❌ Missing: {feature}")
            
            print(f"\n📊 Dashboard Integration: {found_features}/{len(enhanced_features)} features found")
            
            if found_features >= len(enhanced_features) * 0.8:
                print("✅ Dashboard integration successful")
                return True
            else:
                print("⚠️ Dashboard integration incomplete")
                return False
        else:
            print(f"❌ Dashboard not accessible: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard integration test failed: {e}")
        return False

def test_data_source_connectivity():
    """Test connectivity to different data sources."""
    print("\n📡 TESTING DATA SOURCE CONNECTIVITY")
    print("=" * 70)
    
    session = requests.Session()
    
    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)
    
    try:
        # Get market status to check data sources
        response = session.get('http://localhost:8084/api/market/status')
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and 'status' in data:
                status = data['status']
                data_sources = status.get('data_sources', {})
                
                print("📊 Data Source Status:")
                for source, connected in data_sources.items():
                    status_icon = "✅" if connected else "❌"
                    print(f"   {status_icon} {source.upper()}: {'Connected' if connected else 'Disconnected'}")
                
                # Check overall health
                overall_health = status.get('overall_health', 'unknown')
                connection_rate = status.get('connection_rate', 0)
                
                print(f"\n📊 Overall Status:")
                print(f"   Health: {overall_health}")
                print(f"   Connection Rate: {connection_rate:.1f}%")
                print(f"   Active Connections: {status.get('active_connections', 0)}/{status.get('total_connections', 0)}")
                
                return connection_rate >= 25  # At least 25% of sources connected
            else:
                print("❌ Invalid market status response")
                return False
        else:
            print(f"❌ Market status API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Data source connectivity test failed: {e}")
        return False

def main():
    """Run comprehensive real-time market data tests."""
    print("🎯 MONEY CIRCLE REAL-TIME MARKET DATA TEST SUITE")
    print("=" * 70)
    
    tests = [
        ("Market Data API Endpoints", test_market_data_api_endpoints),
        ("WebSocket Market Data Streaming", test_websocket_market_data),
        ("Dashboard Integration", test_dashboard_integration),
        ("Data Source Connectivity", test_data_source_connectivity),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 FINAL TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL REAL-TIME MARKET DATA TESTS PASSED!")
        print("✅ Multi-exchange data feeds working")
        print("✅ WebSocket streaming operational")
        print("✅ API endpoints responding correctly")
        print("✅ Dashboard integration complete")
        print("✅ Data source connectivity established")
        print("\n🌟 MONEY CIRCLE NOW HAS PROFESSIONAL-GRADE REAL-TIME MARKET DATA!")
        return 0
    else:
        print("❌ SOME MARKET DATA FEATURES NEED ATTENTION")
        if passed >= total * 0.75:
            print("⚠️ Most features working - minor issues to resolve")
        else:
            print("❌ Major issues detected - requires investigation")
        return 1

if __name__ == "__main__":
    sys.exit(main())
