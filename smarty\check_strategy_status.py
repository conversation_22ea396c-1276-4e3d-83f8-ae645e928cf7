#!/usr/bin/env python3
"""
Strategy Status Checker
Quick check to see if a strategy is ready to run from the dashboard.
"""

import sqlite3
import subprocess
import psutil
import sys
import time
import json
from datetime import datetime

def check_database():
    """Check if database is accessible."""
    try:
        conn = sqlite3.connect("data/bus.db", timeout=2)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM messages LIMIT 1")
        conn.close()
        return True
    except Exception:
        return False

def check_process_running(strategy_name):
    """Check if strategy process is running."""
    strategy_commands = {
        "Smart Model Integrated": "orchestrator.py",
        "Smart Strategy Only": "run_smart_strategy_live.py",
        "RSI Strategy": "htx_data_producer.py",
        "Bollinger Bands": "htx_data_producer.py",
        "Multi-Signal": "htx_data_producer.py",
        "Ensemble Model": "htx_data_producer.py",
        "SMA Crossover": "htx_data_producer.py",
        "VWAP Strategy": "htx_data_producer.py",
        "Scalper Strategy": "htx_data_producer.py",
        "Order Flow": "live_dataframe_strategy_runner.py"
    }
    
    script_name = strategy_commands.get(strategy_name)
    if not script_name:
        return {"running": False, "error": "Unknown strategy"}
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] in ['python.exe', 'python']:
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if script_name in cmdline:
                        return {"running": True, "pid": proc.info['pid']}
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return {"running": False}
    except Exception as e:
        return {"running": False, "error": str(e)}

def check_data_flow():
    """Check if data is flowing recently."""
    try:
        conn = sqlite3.connect("data/bus.db", timeout=2)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check messages in last 60 seconds
        recent_threshold = time.time() - 60
        cursor.execute(
            "SELECT COUNT(*) as count FROM messages WHERE ts > ?",
            (recent_threshold,)
        )
        recent_count = cursor.fetchone()['count']
        
        # Get latest message timestamp
        cursor.execute("SELECT MAX(ts) as latest FROM messages")
        latest_ts = cursor.fetchone()['latest']
        
        conn.close()
        
        data_age = time.time() - latest_ts if latest_ts else 999
        
        return {
            "data_flowing": recent_count > 0,
            "recent_messages": recent_count,
            "data_age_seconds": data_age
        }
    except Exception as e:
        return {"data_flowing": False, "error": str(e)}

def main():
    """Main execution."""
    if len(sys.argv) != 2:
        print("Usage: python check_strategy_status.py <strategy_name>")
        sys.exit(1)
    
    strategy_name = sys.argv[1]
    
    # Run checks
    db_ok = check_database()
    process_status = check_process_running(strategy_name)
    data_flow = check_data_flow()
    
    # Determine overall status
    if not db_ok:
        status = "ERROR"
        message = "Database connection failed"
    elif not data_flow.get("data_flowing", False):
        status = "WARNING"
        message = "No recent data flow"
    elif process_status.get("running", False):
        status = "RUNNING"
        message = f"Strategy running (PID: {process_status['pid']})"
    else:
        status = "READY"
        message = "Strategy ready to start"
    
    # Output for dashboard
    result = {
        "strategy_name": strategy_name,
        "status": status,
        "message": message,
        "database_ok": db_ok,
        "process_running": process_status.get("running", False),
        "data_flowing": data_flow.get("data_flowing", False),
        "timestamp": datetime.now().isoformat()
    }
    
    print(json.dumps(result))
    
    # Exit code: 0 = ready/running, 1 = warning, 2 = error
    if status == "ERROR":
        sys.exit(2)
    elif status == "WARNING":
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
