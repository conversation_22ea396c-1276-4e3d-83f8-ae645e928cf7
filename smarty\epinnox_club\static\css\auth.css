/* Money Circle Authentication Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.auth-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    text-align: center;
}

.auth-card h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: #FFD700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.auth-card h2 {
    font-size: 1.2em;
    margin-bottom: 30px;
    color: #94a3b8;
    font-weight: 400;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #e2e8f0;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
    background: rgba(0, 0, 0, 0.3);
}

.form-group input::placeholder {
    color: #64748b;
}

.auth-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-card p {
    margin-top: 20px;
    color: #94a3b8;
}

.auth-card a {
    color: #FFD700;
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-card a:hover {
    color: #FFA500;
    text-decoration: underline;
}

/* Error messages */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

/* Success messages */
.success-message {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #86efac;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 480px) {
    .auth-container {
        padding: 10px;
    }
    
    .auth-card {
        padding: 30px 20px;
    }
    
    .auth-card h1 {
        font-size: 2em;
    }
}
