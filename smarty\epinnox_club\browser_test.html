<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Circle - Browser Compatibility Test</title>
    
    <!-- Core CSS Files -->
    <link rel="stylesheet" href="/static/css/design_system.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/browser_fallbacks.css">
    
    <style>
        .browser-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--primary-600, #8b5cf6);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: var(--bg-card, rgba(255, 255, 255, 0.05));
            border-radius: 12px;
            border: 1px solid var(--border-primary, rgba(255, 255, 255, 0.1));
        }
        
        .feature-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-indicator {
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-weight: 600;
        }
        
        .feature-supported {
            background: var(--success-600, #16a34a);
            color: white;
        }
        
        .feature-unsupported {
            background: var(--error-600, #dc2626);
            color: white;
        }
        
        .feature-partial {
            background: var(--warning-600, #d97706);
            color: white;
        }
        
        .compatibility-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .browser-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .version-info {
            font-size: 0.9em;
            color: var(--text-tertiary, #94a3b8);
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="browser-info" id="browserInfo">
        <div id="browserName">Detecting...</div>
        <div id="browserVersion"></div>
        <div id="engineInfo"></div>
    </div>
    
    <div class="container">
        <h1 style="color: var(--warning-400, #FFD700); text-align: center; margin-bottom: 30px;">
            Money Circle Browser Compatibility Test
        </h1>
        
        <!-- CSS Feature Detection -->
        <div class="test-section">
            <h2 style="color: var(--primary-400, #c084fc);">CSS Feature Support</h2>
            <p style="color: var(--text-secondary, #e2e8f0);">Testing critical CSS features for Money Circle dashboard.</p>
            
            <div class="feature-test">
                <div class="feature-indicator" id="cssGrid">CSS Grid</div>
                <div class="feature-indicator" id="flexbox">Flexbox</div>
                <div class="feature-indicator" id="customProps">Custom Properties</div>
                <div class="feature-indicator" id="backdropFilter">Backdrop Filter</div>
                <div class="feature-indicator" id="transforms">CSS Transforms</div>
                <div class="feature-indicator" id="mediaQueries">Media Queries</div>
            </div>
        </div>
        
        <!-- Layout Compatibility Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400, #c084fc);">Layout Compatibility</h2>
            <p style="color: var(--text-secondary, #e2e8f0);">Testing dashboard grid layout across browsers.</p>
            
            <div class="dashboard-grid">
                <div class="portfolio-overview">
                    <h3>Portfolio Overview (Grid Test)</h3>
                    <div class="portfolio-cards">
                        <div class="portfolio-card">
                            <h4>Total Value</h4>
                            <div class="value">$1,234.56</div>
                            <div class="change positive">+2.5%</div>
                        </div>
                        <div class="portfolio-card">
                            <h4>Available Balance</h4>
                            <div class="value">$567.89</div>
                            <div class="change negative">-1.2%</div>
                        </div>
                    </div>
                </div>
                
                <div class="exchange-connections">
                    <h3>Exchange Connections</h3>
                    <div class="exchange-card connected">
                        <h4>Binance</h4>
                        <div class="connection-status" style="color: var(--success-500, #22c55e);">Connected</div>
                    </div>
                </div>
                
                <div class="trading-interface">
                    <h3>Trading Interface</h3>
                    <button class="btn btn-primary">Primary Button</button>
                    <button class="btn btn-secondary">Secondary Button</button>
                </div>
            </div>
        </div>
        
        <!-- Interactive Elements Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400, #c084fc);">Interactive Elements</h2>
            <p style="color: var(--text-secondary, #e2e8f0);">Testing hover effects and touch interactions.</p>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                <button class="btn btn-primary">Hover Test Button</button>
                <button class="place-order-btn" style="width: auto; min-width: 150px;">Order Button</button>
                <div class="portfolio-card" style="cursor: pointer; min-width: 200px;">
                    <h4>Hover Card</h4>
                    <p>Should respond to hover/touch</p>
                </div>
            </div>
        </div>
        
        <!-- Browser Support Matrix -->
        <div class="test-section">
            <h2 style="color: var(--primary-400, #c084fc);">Browser Support Matrix</h2>
            <p style="color: var(--text-secondary, #e2e8f0);">Recommended browser versions for optimal experience.</p>
            
            <div class="compatibility-grid">
                <div class="browser-card">
                    <h4 style="color: var(--success-500, #22c55e);">Chrome</h4>
                    <div class="version-info">88+ (Full Support)</div>
                </div>
                <div class="browser-card">
                    <h4 style="color: var(--success-500, #22c55e);">Firefox</h4>
                    <div class="version-info">85+ (Full Support)</div>
                </div>
                <div class="browser-card">
                    <h4 style="color: var(--success-500, #22c55e);">Safari</h4>
                    <div class="version-info">14+ (Full Support)</div>
                </div>
                <div class="browser-card">
                    <h4 style="color: var(--success-500, #22c55e);">Edge</h4>
                    <div class="version-info">88+ (Full Support)</div>
                </div>
                <div class="browser-card">
                    <h4 style="color: var(--warning-500, #f59e0b);">Internet Explorer</h4>
                    <div class="version-info">11 (Limited Support)</div>
                </div>
            </div>
        </div>
        
        <!-- Performance Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400, #c084fc);">Performance Metrics</h2>
            <p style="color: var(--text-secondary, #e2e8f0);">Page loading and rendering performance.</p>
            
            <div id="performanceMetrics" style="margin-top: 15px;">
                <div>Page Load Time: <span id="loadTime">Calculating...</span></div>
                <div>DOM Ready Time: <span id="domTime">Calculating...</span></div>
                <div>Render Time: <span id="renderTime">Calculating...</span></div>
            </div>
        </div>
    </div>
    
    <script>
        // Browser Detection
        function detectBrowser() {
            const ua = navigator.userAgent;
            let browserName = 'Unknown';
            let browserVersion = '';
            let engine = '';
            
            if (ua.indexOf('Chrome') > -1 && ua.indexOf('Edge') === -1) {
                browserName = 'Chrome';
                browserVersion = ua.match(/Chrome\/(\d+)/)[1];
                engine = 'Blink';
            } else if (ua.indexOf('Firefox') > -1) {
                browserName = 'Firefox';
                browserVersion = ua.match(/Firefox\/(\d+)/)[1];
                engine = 'Gecko';
            } else if (ua.indexOf('Safari') > -1 && ua.indexOf('Chrome') === -1) {
                browserName = 'Safari';
                browserVersion = ua.match(/Version\/(\d+)/)[1];
                engine = 'WebKit';
            } else if (ua.indexOf('Edge') > -1) {
                browserName = 'Edge';
                browserVersion = ua.match(/Edge\/(\d+)/)[1];
                engine = 'EdgeHTML/Blink';
            } else if (ua.indexOf('Trident') > -1) {
                browserName = 'Internet Explorer';
                browserVersion = ua.match(/rv:(\d+)/)[1];
                engine = 'Trident';
            }
            
            document.getElementById('browserName').textContent = browserName;
            document.getElementById('browserVersion').textContent = `Version: ${browserVersion}`;
            document.getElementById('engineInfo').textContent = `Engine: ${engine}`;
        }
        
        // CSS Feature Detection
        function testCSSFeatures() {
            const features = {
                cssGrid: CSS.supports('display', 'grid'),
                flexbox: CSS.supports('display', 'flex'),
                customProps: CSS.supports('--css', 'variables'),
                backdropFilter: CSS.supports('backdrop-filter', 'blur(10px)'),
                transforms: CSS.supports('transform', 'translateY(-2px)'),
                mediaQueries: window.matchMedia !== undefined
            };
            
            Object.keys(features).forEach(feature => {
                const element = document.getElementById(feature);
                const supported = features[feature];
                
                element.className = `feature-indicator ${supported ? 'feature-supported' : 'feature-unsupported'}`;
                element.textContent = `${feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: ${supported ? 'Supported' : 'Not Supported'}`;
            });
        }
        
        // Performance Metrics
        function measurePerformance() {
            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                const domTime = timing.domContentLoadedEventEnd - timing.navigationStart;
                const renderTime = timing.domComplete - timing.domLoading;
                
                document.getElementById('loadTime').textContent = `${loadTime}ms`;
                document.getElementById('domTime').textContent = `${domTime}ms`;
                document.getElementById('renderTime').textContent = `${renderTime}ms`;
            } else {
                document.getElementById('loadTime').textContent = 'Not available';
                document.getElementById('domTime').textContent = 'Not available';
                document.getElementById('renderTime').textContent = 'Not available';
            }
        }
        
        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            detectBrowser();
            testCSSFeatures();
            
            // Measure performance after page load
            window.addEventListener('load', function() {
                setTimeout(measurePerformance, 100);
            });
        });
        
        // Log browser capabilities
        console.log('Browser Compatibility Test Results:');
        console.log('User Agent:', navigator.userAgent);
        console.log('Viewport:', window.innerWidth + 'x' + window.innerHeight);
        console.log('Device Pixel Ratio:', window.devicePixelRatio);
        console.log('Touch Support:', 'ontouchstart' in window);
    </script>
</body>
</html>
