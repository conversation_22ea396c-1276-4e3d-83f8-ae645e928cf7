#!/usr/bin/env python3
"""
Test adding HTX account to Money Circle platform.
"""

import requests
import yaml
import json
import sys
from pathlib import Path

def load_credentials():
    """Load credentials from credentials.yaml"""
    cred_file = Path("credentials.yaml")
    if not cred_file.exists():
        print("❌ credentials.yaml not found")
        return None
    
    with open(cred_file, 'r') as f:
        creds = yaml.safe_load(f)
    
    return creds

def login_to_money_circle():
    """Login to Money Circle platform"""
    print("🔐 Logging into Money Circle platform...")
    
    session = requests.Session()
    
    # Login
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }
    
    response = session.post(
        'http://localhost:8084/login',
        data=login_data,
        headers={'Content-Type': 'application/x-www-form-urlencoded'},
        allow_redirects=False
    )
    
    if response.status_code in [200, 302]:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def add_htx_account(session, api_key, secret_key, account_name):
    """Add HTX account to Money Circle"""
    print(f"📊 Adding HTX account: {account_name}")
    
    # Try both exchange names
    for exchange_name in ['huobi', 'HTX']:
        print(f"🔧 Trying exchange name: {exchange_name}")
        
        account_data = {
            'exchange': exchange_name,
            'api_key': api_key,
            'secret_key': secret_key
        }
        
        response = session.post(
            'http://localhost:8084/api/exchange/add',
            json=account_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            print(f"✅ Successfully added HTX account with name: {exchange_name}")
            return True
        else:
            print(f"❌ Failed to add account with name: {exchange_name}")
    
    return False

def test_account_access(session):
    """Test accessing the added account"""
    print("🔍 Testing account access...")
    
    response = session.get('http://localhost:8084/api/portfolio')
    print(f"📡 Portfolio response: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Portfolio data retrieved")
        
        # Check for exchange accounts
        exchanges = data.get('exchanges', [])
        print(f"📊 Found {len(exchanges)} exchange accounts")
        
        for exchange in exchanges:
            print(f"  🏦 {exchange.get('name', 'Unknown')}: {exchange.get('status', 'Unknown')}")
    else:
        print(f"❌ Failed to get portfolio: {response.text}")

def main():
    print("🚀 Money Circle HTX Integration Test")
    print("=" * 50)
    
    # Load credentials
    creds = load_credentials()
    if not creds:
        sys.exit(1)
    
    # Get HTX credentials
    accounts = creds.get('accounts', [])
    default_account = creds.get('default_account', 'EPX')
    
    # Find the default HTX account
    htx_account = None
    for account in accounts:
        if account.get('name') == default_account and account.get('exchange') == 'huobi':
            htx_account = account
            break
    
    if not htx_account:
        print("❌ No default HTX account found")
        sys.exit(1)
    
    api_key = htx_account.get('api_key')
    secret_key = htx_account.get('secret_key')
    account_name = htx_account.get('name')
    
    print(f"📋 Using account: {account_name} - {htx_account.get('description')}")
    print(f"📊 API Key: {api_key[:8]}...")
    
    # Login to Money Circle
    session = login_to_money_circle()
    if not session:
        sys.exit(1)
    
    # Add HTX account
    success = add_htx_account(session, api_key, secret_key, account_name)
    
    if success:
        # Test account access
        test_account_access(session)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 HTX integration test: SUCCESS")
        print("✅ HTX account successfully added to Money Circle")
    else:
        print("❌ HTX integration test: FAILED")
        print("❌ Could not add HTX account to Money Circle")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
