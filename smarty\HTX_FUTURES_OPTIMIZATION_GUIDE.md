# HTX-Futures Trading Optimization Guide for SQLiteBus

This guide provides detailed information on optimizing the SQLiteBus implementation specifically for HTX-Futures trading workloads in the smart-trader system.

## Workload Characteristics

HTX-Futures trading has specific workload characteristics that require tailored optimizations:

1. **High-Frequency Market Data**:
   - Trade data: Small messages (200-500 bytes), very high frequency (10-100 per second)
   - Kline data: Small messages (500-1000 bytes), medium frequency (1-5 per second)
   - Orderbook data: Larger messages (2-10 KB), medium frequency (5-20 per second)

2. **Medium-Frequency Model Outputs**:
   - RSI, VWAP, Volatility Regime, etc.: Small messages (100-500 bytes), low frequency (1-5 per second)
   - OrderFlow Net, Liquidity Imbalance: Medium messages (500-2000 bytes), low frequency (1-5 per second)

3. **Low-Frequency External Data**:
   - Funding rates: Small messages (100-500 bytes), very low frequency (once per minute)
   - Open interest: Small messages (100-500 bytes), very low frequency (once per minute)
   - Social sentiment: Small messages (100-500 bytes), very low frequency (once per minute)

4. **Very Low-Frequency LLM Outputs**:
   - Trading decisions: Larger messages (1-10 KB), very low frequency (once every few seconds)

## Stream-Specific Optimizations

The key to optimizing for HTX-Futures trading is to use stream-specific configurations and priority streams:

### 1. High-Frequency Market Data Streams

For streams like `htx.trade` and `htx.kline`:

```yaml
"htx.trade":
  batch_size: 200       # Larger batches for high-frequency data
  batch_timeout: 0.2    # Short timeout to avoid delays
  compression: false    # No compression for small messages
```

### 2. Orderbook Data Streams

For streams like `htx.orderbook`:

```yaml
"htx.orderbook":
  batch_size: 50        # Moderate batch size
  batch_timeout: 0.5    # Moderate timeout
  compression: true     # Enable compression for larger messages
  compression_level: 6  # Balanced compression level
  compression_threshold: 512  # Compress messages larger than 512 bytes
```

### 3. Model Output Streams

For streams like `model.rsi`, `model.vwap`, etc.:

```yaml
"model.*":
  batch_size: 20        # Smaller batches for lower frequency
  batch_timeout: 1.0    # Longer timeout is acceptable
  compression: false    # No compression for small messages
```

### 4. LLM Output Streams

For streams like `llm.decision`:

```yaml
"llm.*":
  batch_size: 10        # Small batches for low frequency
  batch_timeout: 2.0    # Longer timeout is acceptable
  compression: true     # Enable compression for larger messages
  compression_level: 9  # Higher compression level for infrequent messages
  compression_threshold: 256  # Compress even smaller messages
```

### 5. Priority Streams

For critical streams that need lower latency:

```yaml
# Priority streams are processed with higher priority
priority_streams:
  - "htx.trade"      # Trade data is critical for real-time decisions
  - "htx.orderbook"  # Orderbook data is critical for liquidity analysis
  - "model.volatility_regime"  # Volatility regime affects position sizing
  - "model.liquidity_imbalance"  # Liquidity imbalance affects execution strategy
```

Priority streams receive special treatment:
- Smaller batch sizes (1/4 of the configured batch size)
- Shorter timeouts (1/2 of the configured timeout)
- Processed before non-priority streams
- Logged with additional debug information

## Complete Configuration Example

Here's a complete configuration example for HTX-Futures trading:

```yaml
pipeline:
  bus: "optimized_sqlite"
  optimized_sqlite:
    # Database settings
    path: "data/htx_optimized_bus.db"

    # Performance settings
    poll_interval: 0.05  # 50ms polling for low latency
    batch_size: 100      # Default batch size
    batch_timeout: 0.5   # Default timeout

    # SQLite optimizations
    cache_size: 20000    # Larger cache for better performance
    mmap_size: 100000000 # 100MB memory map for better read performance
    busy_timeout: 5000   # 5 seconds busy timeout
    synchronous: "NORMAL" # Balance between performance and durability

    # Stream-specific settings
    stream_config:
      # Market data streams (high frequency, small messages)
      "htx.trade":
        batch_size: 200
        batch_timeout: 0.2
        compression: false
      "htx.kline":
        batch_size: 100
        batch_timeout: 0.3
        compression: false

      # Orderbook streams (medium frequency, larger messages)
      "htx.orderbook":
        batch_size: 50
        batch_timeout: 0.5
        compression: true
        compression_level: 6
        compression_threshold: 512

      # Model output streams (low frequency, small messages)
      "model.*":
        batch_size: 20
        batch_timeout: 1.0
        compression: false

      # LLM output streams (very low frequency, larger messages)
      "llm.*":
        batch_size: 10
        batch_timeout: 2.0
        compression: true
        compression_level: 9
        compression_threshold: 256

    # Priority streams (processed with higher priority)
    priority_streams:
      - "htx.trade"
      - "htx.orderbook"
      - "model.volatility_regime"
      - "model.liquidity_imbalance"

    # Compression settings (global defaults)
    compression: false
    compression_level: 6
    compression_threshold: 1024

  # Maintenance settings
  cleanup_interval_hours: 6
  message_retention_days: 3
```

## Performance Tuning Recommendations

Based on HTX-Futures trading workloads, here are specific recommendations:

1. **Increase Cache Size**: Set `cache_size` to at least 20,000 pages to accommodate the high volume of market data.

2. **Reduce Poll Interval**: Set `poll_interval` to 0.05 seconds (50ms) for lower latency on market data.

3. **Stream-Specific Batching**: Use larger batch sizes for high-frequency streams and smaller batch sizes for low-frequency streams.

4. **Selective Compression**: Enable compression only for larger messages like orderbook data and LLM outputs.

5. **Shorter Retention Period**: For HTX-Futures trading, a shorter retention period (3 days) is usually sufficient.

6. **More Frequent Cleanup**: Run cleanup more frequently (every 6 hours) to keep the database size manageable.

7. **Prioritize Critical Streams**: Use priority streams for market data and critical model outputs to ensure they are processed with lower latency.

8. **Measure Latency Impact**: Use the `priority_stream_test.py` script to measure the impact of priority streams on latency for your specific workload.

## Monitoring Recommendations

For HTX-Futures trading workloads, monitor these key metrics:

1. **Trade Data Throughput**: Monitor the throughput of the `htx.trade` stream to ensure it can handle peak market activity.

2. **Orderbook Processing Latency**: Monitor the time between orderbook updates and model processing.

3. **Database Size Growth**: Monitor the database size growth rate, especially during high volatility periods.

4. **Message Processing Delays**: Monitor any delays in message processing, especially for trade and orderbook data.

## Scaling Recommendations

As your HTX-Futures trading system grows, consider these scaling strategies:

1. **Symbol-Specific Databases**: For multi-symbol trading, consider using separate databases for each symbol.

2. **Time-Based Sharding**: Implement time-based sharding (e.g., one database per day) for historical data.

3. **Separate High-Frequency Streams**: Consider moving high-frequency streams like `htx.trade` to a separate in-memory bus.

4. **Redis for Real-Time Data**: For ultra-low-latency requirements, consider using Redis for real-time market data.

## Conclusion

By applying these HTX-Futures specific optimizations to your SQLiteBus implementation, you can achieve significantly better performance for your smart-trader system. The stream-specific configurations allow you to fine-tune the behavior for different types of data, ensuring optimal performance across your entire system.
