#!/usr/bin/env python3
"""
Live Data Bridge - Convert SQLite Bus Data to DataFrames

This module bridges the gap between live market data in the SQLite bus
and the smart strategy by providing data in pandas DataFrame format.
"""

import pandas as pd
import numpy as np
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pipeline.databus import SQLiteBus
import json

logger = logging.getLogger(__name__)


class LiveDataBridge:
    """Bridge between SQLite bus and DataFrame-based analysis."""

    def __init__(self, bus_path: str = "data/bus.db"):
        """Initialize the data bridge."""
        self.bus_path = bus_path
        self.bus = None
        self.market_data_cache = {}
        self.trade_data_cache = {}

    def connect(self):
        """Connect to the SQLite bus."""
        try:
            self.bus = SQLiteBus(path=self.bus_path, poll_interval=0.1)
            logger.info(f"✅ Connected to SQLite bus at {self.bus_path}")
        except Exception as e:
            logger.error(f"❌ Failed to connect to SQLite bus: {e}")
            raise

    def get_live_price_data(self, symbol: str = "BTC-USDT", lookback_minutes: int = 60) -> pd.DataFrame:
        """
        Get live price data as a DataFrame.

        Args:
            symbol: Trading symbol
            lookback_minutes: How many minutes of data to include

        Returns:
            DataFrame with OHLCV data
        """
        try:
            if not self.bus:
                self.connect()

            # Get recent market data from bus
            cutoff_time = datetime.now() - timedelta(minutes=lookback_minutes)

            # Query the bus for recent kline/market data
            market_data = []

            # Use direct SQL query to get recent messages
            cursor = self.bus.conn.cursor()
            cursor.execute("""
                SELECT ts, stream, payload FROM messages
                WHERE ts > ? AND (stream LIKE '%kline%' OR stream LIKE '%market%' OR stream LIKE '%trade%')
                ORDER BY ts ASC
            """, (cutoff_time.timestamp(),))

            messages = cursor.fetchall()

            for row in messages:
                timestamp, topic, payload = row
                if 'kline' in topic:
                    try:
                        data = json.loads(payload) if isinstance(payload, str) else payload

                        # Extract symbol from channel name (e.g., "market.BTC-USDT.kline.1s")
                        ch = data.get('ch', '')
                        if symbol in ch:
                            tick = data.get('tick', {})
                            if tick:
                                market_data.append({
                                    'timestamp': pd.to_datetime(timestamp, unit='s'),
                                    'open': float(tick.get('open', 0)),
                                    'high': float(tick.get('high', 0)),
                                    'low': float(tick.get('low', 0)),
                                    'close': float(tick.get('close', 0)),
                                    'volume': float(tick.get('vol', tick.get('amount', 0)))
                                })
                    except Exception as e:
                        continue

            if not market_data:
                # Generate synthetic data based on current price if no historical data
                logger.warning(f"No historical data found, generating synthetic data for {symbol}")
                return self._generate_synthetic_data(symbol, lookback_minutes)

            # Convert to DataFrame
            df = pd.DataFrame(market_data)
            df = df.sort_values('timestamp').reset_index(drop=True)

            # Fill missing OHLC data
            df['open'] = df['open'].fillna(df['close'])
            df['high'] = df[['open', 'high', 'close']].max(axis=1)
            df['low'] = df[['open', 'low', 'close']].min(axis=1)

            logger.info(f"📊 Retrieved {len(df)} price points for {symbol}")
            return df

        except Exception as e:
            logger.error(f"❌ Error getting price data for {symbol}: {e}")
            return self._generate_synthetic_data(symbol, lookback_minutes)

    def get_current_price(self, symbol: str = "BTC-USDT") -> float:
        """Get the current price for a symbol."""
        try:
            if not self.bus:
                self.connect()

            # Get the most recent market data
            cursor = self.bus.conn.cursor()
            cursor.execute("""
                SELECT ts, stream, payload FROM messages
                ORDER BY ts DESC LIMIT 100
            """)
            recent_messages = cursor.fetchall()

            for row in recent_messages:
                _, topic, payload = row
                if 'kline' in topic:
                    try:
                        data = json.loads(payload) if isinstance(payload, str) else payload
                        ch = data.get('ch', '')
                        if symbol in ch:
                            tick = data.get('tick', {})
                            if tick and 'close' in tick:
                                price = float(tick['close'])
                                logger.debug(f"Current {symbol} price: ${price:,.2f}")
                                return price
                    except Exception:
                        continue

            # Fallback: try to get from trade data
            for row in recent_messages:
                _, topic, payload = row
                if 'trade' in topic:
                    try:
                        data = json.loads(payload) if isinstance(payload, str) else payload
                        ch = data.get('ch', '')
                        if symbol in ch:
                            tick = data.get('tick', {})
                            if tick and 'data' in tick:
                                trades = tick['data']
                                if trades and len(trades) > 0:
                                    price = float(trades[0].get('price', 0))
                                    if price > 0:
                                        logger.debug(f"Current {symbol} price (trade): ${price:,.2f}")
                                        return price
                    except Exception:
                        continue

            logger.warning(f"No current price found for {symbol}")
            return 0.0

        except Exception as e:
            logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return 0.0

    def _generate_synthetic_data(self, symbol: str, lookback_minutes: int) -> pd.DataFrame:
        """Generate synthetic OHLCV data for testing."""
        logger.info(f"🔧 Generating synthetic data for {symbol}")

        # Get current price from bus
        current_price = self.get_current_price(symbol)
        if current_price == 0:
            # Use a reasonable default for BTC
            current_price = 109000.0 if 'BTC' in symbol else 3500.0

        # Generate timestamps
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=lookback_minutes)
        timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')

        # Generate realistic price movement
        np.random.seed(42)  # For reproducible results
        returns = np.random.normal(0, 0.001, len(timestamps))  # 0.1% volatility
        prices = [current_price]

        for i in range(1, len(timestamps)):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(new_price)

        # Create OHLCV data
        data = []
        for i, (ts, price) in enumerate(zip(timestamps, prices)):
            # Add some intrabar volatility
            volatility = price * 0.0005  # 0.05% intrabar volatility
            high = price + np.random.uniform(0, volatility)
            low = price - np.random.uniform(0, volatility)
            open_price = prices[i-1] if i > 0 else price

            data.append({
                'timestamp': ts,
                'open': open_price,
                'high': max(open_price, price, high),
                'low': min(open_price, price, low),
                'close': price,
                'volume': np.random.uniform(100, 1000)
            })

        df = pd.DataFrame(data)
        logger.info(f"📊 Generated {len(df)} synthetic price points for {symbol}")
        return df

    def get_technical_indicators(self, symbol: str = "BTC-USDT", lookback_minutes: int = 60) -> Dict:
        """Calculate technical indicators from live data."""
        try:
            df = self.get_live_price_data(symbol, lookback_minutes)

            if df.empty:
                return {}

            # Calculate technical indicators
            indicators = {}

            # Simple Moving Averages
            indicators['sma_10'] = df['close'].rolling(window=10).mean().iloc[-1] if len(df) >= 10 else df['close'].iloc[-1]
            indicators['sma_20'] = df['close'].rolling(window=20).mean().iloc[-1] if len(df) >= 20 else df['close'].iloc[-1]

            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            indicators['rsi'] = (100 - (100 / (1 + rs))).iloc[-1] if len(df) >= 14 else 50.0

            # Current price
            indicators['current_price'] = df['close'].iloc[-1]
            indicators['price_change'] = df['close'].iloc[-1] - df['close'].iloc[0] if len(df) > 1 else 0
            indicators['price_change_pct'] = (indicators['price_change'] / df['close'].iloc[0] * 100) if len(df) > 1 else 0

            # Volume
            indicators['avg_volume'] = df['volume'].mean()
            indicators['current_volume'] = df['volume'].iloc[-1]

            logger.info(f"📈 Calculated indicators for {symbol}: RSI={indicators['rsi']:.1f}, Price=${indicators['current_price']:.2f}")
            return indicators

        except Exception as e:
            logger.error(f"❌ Error calculating indicators for {symbol}: {e}")
            return {}

    def close(self):
        """Close the bus connection."""
        if self.bus:
            self.bus.close()
            logger.info("✅ SQLite bus connection closed")


# Global instance for easy access
live_data_bridge = LiveDataBridge()


def get_live_market_dataframe(symbol: str = "BTC-USDT", lookback_minutes: int = 60) -> pd.DataFrame:
    """Convenience function to get live market data as DataFrame."""
    if not live_data_bridge.bus:
        live_data_bridge.connect()
    return live_data_bridge.get_live_price_data(symbol, lookback_minutes)


def get_current_market_price(symbol: str = "BTC-USDT") -> float:
    """Convenience function to get current market price."""
    if not live_data_bridge.bus:
        live_data_bridge.connect()
    return live_data_bridge.get_current_price(symbol)


def get_live_technical_indicators(symbol: str = "BTC-USDT") -> Dict:
    """Convenience function to get live technical indicators."""
    if not live_data_bridge.bus:
        live_data_bridge.connect()
    return live_data_bridge.get_technical_indicators(symbol)
