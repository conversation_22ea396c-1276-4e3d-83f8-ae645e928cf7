#!/usr/bin/env python3
"""
Comprehensive test for Money Circle Enhanced Club Features.
Tests Strategy Marketplace, Member Directory, and Club Analytics with all new functionality.
"""

import requests
import json
import sys
import time
from datetime import datetime

def test_enhanced_strategy_marketplace():
    """Test the enhanced strategy marketplace features."""
    print("🎯 TESTING ENHANCED STRATEGY MARKETPLACE")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)

    if login_response.status_code not in [200, 302]:
        print(f"❌ Login failed: {login_response.status_code}")
        return False

    print("✅ Authentication successful")

    # Test strategy marketplace page
    print("🧪 Testing strategy marketplace page...")
    marketplace_response = session.get('http://localhost:8084/club/strategies')

    if marketplace_response.status_code == 200:
        content = marketplace_response.text

        # Check for enhanced features
        enhanced_features = [
            'strategy-marketplace',
            'marketplace-stats',
            'search-bar',
            'filter-controls',
            'featured-strategies',
            'strategy-grid',
            'strategy-card',
            'performance-metric',
            'strategy_marketplace.js'
        ]

        found_features = 0
        for feature in enhanced_features:
            if feature in content:
                found_features += 1
                print(f"   ✅ Found: {feature}")
            else:
                print(f"   ❌ Missing: {feature}")

        print(f"\n📊 Strategy Marketplace: {found_features}/{len(enhanced_features)} features found")

        if found_features >= len(enhanced_features) * 0.8:
            print("✅ Strategy Marketplace enhanced successfully")
            return True
        else:
            print("⚠️ Strategy Marketplace enhancement incomplete")
            return False
    else:
        print(f"❌ Strategy Marketplace not accessible: {marketplace_response.status_code}")
        return False

def test_enhanced_member_directory():
    """Test the enhanced member directory features."""
    print("\n👥 TESTING ENHANCED MEMBER DIRECTORY")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)

    # Test member directory page
    print("🧪 Testing member directory page...")
    directory_response = session.get('http://localhost:8084/club/members')

    if directory_response.status_code == 200:
        content = directory_response.text

        # Check for enhanced features
        enhanced_features = [
            'member-directory',
            'member-stats-overview',
            'search-bar',
            'filter-controls',
            'leaderboards-section',
            'member-grid',
            'member-card',
            'experience-badge',
            'achievement-badge',
            'member_directory.js'
        ]

        found_features = 0
        for feature in enhanced_features:
            if feature in content:
                found_features += 1
                print(f"   ✅ Found: {feature}")
            else:
                print(f"   ❌ Missing: {feature}")

        print(f"\n📊 Member Directory: {found_features}/{len(enhanced_features)} features found")

        if found_features >= len(enhanced_features) * 0.8:
            print("✅ Member Directory enhanced successfully")
            return True
        else:
            print("⚠️ Member Directory enhancement incomplete")
            return False
    else:
        print(f"❌ Member Directory not accessible: {directory_response.status_code}")
        return False

def test_enhanced_club_analytics():
    """Test the enhanced club analytics features."""
    print("\n📊 TESTING ENHANCED CLUB ANALYTICS")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)

    # Test club analytics page
    print("🧪 Testing club analytics page...")
    analytics_response = session.get('http://localhost:8084/club/analytics')

    if analytics_response.status_code == 200:
        content = analytics_response.text

        # Check for enhanced features
        enhanced_features = [
            'club-analytics',
            'analytics-controls',
            'time-range-selector',
            'analytics-tabs',
            'metric-card',
            'chart-container',
            'performance-chart',
            'portfolio-allocation',
            'risk-metrics',
            'club_analytics.js',
            'Chart.js'
        ]

        found_features = 0
        for feature in enhanced_features:
            if feature in content:
                found_features += 1
                print(f"   ✅ Found: {feature}")
            else:
                print(f"   ❌ Missing: {feature}")

        print(f"\n📊 Club Analytics: {found_features}/{len(enhanced_features)} features found")

        if found_features >= len(enhanced_features) * 0.8:
            print("✅ Club Analytics enhanced successfully")
            return True
        else:
            print("⚠️ Club Analytics enhancement incomplete")
            return False
    else:
        print(f"❌ Club Analytics not accessible: {analytics_response.status_code}")
        return False

def test_enhanced_api_endpoints():
    """Test the enhanced API endpoints."""
    print("\n🔗 TESTING ENHANCED API ENDPOINTS")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)

    # Test strategy API endpoints
    strategy_endpoints = [
        '/api/strategies/1/details',
        '/api/strategies/1/follow',
        '/api/strategies/1/unfollow'
    ]

    print("🧪 Testing strategy API endpoints...")
    strategy_success = 0
    for endpoint in strategy_endpoints:
        try:
            if 'follow' in endpoint or 'unfollow' in endpoint:
                response = session.post(f'http://localhost:8084{endpoint}')
            else:
                response = session.get(f'http://localhost:8084{endpoint}')

            if response.status_code in [200, 404]:  # 404 is acceptable for non-existent strategies
                print(f"   ✅ {endpoint}: {response.status_code}")
                strategy_success += 1
            else:
                print(f"   ❌ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Error - {e}")

    # Test member API endpoints
    member_endpoints = [
        '/api/members/1/profile',
        '/api/members/1/connect',
        '/api/members/1/disconnect'
    ]

    print("\n🧪 Testing member API endpoints...")
    member_success = 0
    for endpoint in member_endpoints:
        try:
            if 'connect' in endpoint or 'disconnect' in endpoint:
                response = session.post(f'http://localhost:8084{endpoint}')
            else:
                response = session.get(f'http://localhost:8084{endpoint}')

            if response.status_code in [200, 404]:  # 404 is acceptable for non-existent members
                print(f"   ✅ {endpoint}: {response.status_code}")
                member_success += 1
            else:
                print(f"   ❌ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Error - {e}")

    # Test message API endpoint
    print("\n🧪 Testing message API endpoint...")
    message_success = 0
    try:
        message_data = {
            'subject': 'Test Message',
            'content': 'This is a test message from the enhanced features test.'
        }
        response = session.post(
            'http://localhost:8084/api/members/1/message',
            json=message_data,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code in [200, 404]:
            print(f"   ✅ Message API: {response.status_code}")
            message_success = 1
        else:
            print(f"   ❌ Message API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Message API: Error - {e}")

    total_endpoints = len(strategy_endpoints) + len(member_endpoints) + 1
    total_success = strategy_success + member_success + message_success

    print(f"\n📊 API Endpoints: {total_success}/{total_endpoints} working")

    return total_success >= total_endpoints * 0.7  # 70% success rate

def test_responsive_design():
    """Test responsive design features."""
    print("\n📱 TESTING RESPONSIVE DESIGN")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)

    pages = [
        ('/club/strategies', 'Strategy Marketplace'),
        ('/club/members', 'Member Directory'),
        ('/club/analytics', 'Club Analytics')
    ]

    responsive_success = 0

    for url, name in pages:
        try:
            print(f"🧪 Testing {name} responsive design...")
            response = session.get(f'http://localhost:8084{url}')

            if response.status_code == 200:
                content = response.text

                # Check for responsive design features
                responsive_features = [
                    '@media (max-width: 768px)',
                    'grid-template-columns',
                    'flex-wrap',
                    'mobile-responsive',
                    'viewport'
                ]

                found_responsive = 0
                for feature in responsive_features:
                    if feature in content:
                        found_responsive += 1

                if found_responsive >= 3:
                    print(f"   ✅ {name}: Responsive design implemented")
                    responsive_success += 1
                else:
                    print(f"   ⚠️ {name}: Limited responsive features")
            else:
                print(f"   ❌ {name}: Not accessible")

        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")

    print(f"\n📊 Responsive Design: {responsive_success}/{len(pages)} pages responsive")

    return responsive_success >= len(pages) * 0.8

def test_integration_with_existing_systems():
    """Test integration with existing Money Circle systems."""
    print("\n🔗 TESTING INTEGRATION WITH EXISTING SYSTEMS")
    print("=" * 70)

    session = requests.Session()

    # Login first
    login_data = {'username': 'epinnox', 'password': 'securepass123'}
    session.post('http://localhost:8084/login', data=login_data)

    integration_tests = [
        ('Navigation Links', ['/club/strategies', '/club/members', '/club/analytics']),
        ('Authentication', ['session_id', 'epinnox']),
        ('Database Integration', ['strategy_proposals', 'member_profiles', 'trading_performance']),
        ('Market Data Integration', ['real-time', 'WebSocket', 'market data'])
    ]

    integration_success = 0

    for test_name, test_items in integration_tests:
        print(f"🧪 Testing {test_name}...")

        if test_name == 'Navigation Links':
            # Test navigation between enhanced pages
            nav_success = 0
            for url in test_items:
                try:
                    response = session.get(f'http://localhost:8084{url}')
                    if response.status_code == 200:
                        nav_success += 1
                except:
                    pass

            if nav_success == len(test_items):
                print(f"   ✅ {test_name}: All pages accessible")
                integration_success += 1
            else:
                print(f"   ⚠️ {test_name}: {nav_success}/{len(test_items)} pages accessible")

        elif test_name == 'Authentication':
            # Test authentication integration
            try:
                response = session.get('http://localhost:8084/club/strategies')
                if response.status_code == 200 and 'epinnox' in response.text:
                    print(f"   ✅ {test_name}: Working correctly")
                    integration_success += 1
                else:
                    print(f"   ⚠️ {test_name}: Issues detected")
            except:
                print(f"   ❌ {test_name}: Failed")

        elif test_name == 'Database Integration':
            # Test database integration (check if pages load without errors)
            try:
                response = session.get('http://localhost:8084/club/analytics')
                if response.status_code == 200:
                    print(f"   ✅ {test_name}: Database queries working")
                    integration_success += 1
                else:
                    print(f"   ❌ {test_name}: Database issues")
            except:
                print(f"   ❌ {test_name}: Failed")

        elif test_name == 'Market Data Integration':
            # Test market data integration
            try:
                response = session.get('http://localhost:8084/club/analytics')
                if response.status_code == 200 and 'chart' in response.text.lower():
                    print(f"   ✅ {test_name}: Charts and data visualization working")
                    integration_success += 1
                else:
                    print(f"   ⚠️ {test_name}: Limited integration")
            except:
                print(f"   ❌ {test_name}: Failed")

    print(f"\n📊 System Integration: {integration_success}/{len(integration_tests)} tests passed")

    return integration_success >= len(integration_tests) * 0.75

def main():
    """Run comprehensive enhanced club features tests."""
    print("🎯 MONEY CIRCLE ENHANCED CLUB FEATURES TEST SUITE")
    print("=" * 70)

    tests = [
        ("Enhanced Strategy Marketplace", test_enhanced_strategy_marketplace),
        ("Enhanced Member Directory", test_enhanced_member_directory),
        ("Enhanced Club Analytics", test_enhanced_club_analytics),
        ("Enhanced API Endpoints", test_enhanced_api_endpoints),
        ("Responsive Design", test_responsive_design),
        ("Integration with Existing Systems", test_integration_with_existing_systems),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 50)

        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")

    print("\n" + "=" * 70)
    print(f"📊 FINAL TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL ENHANCED CLUB FEATURES TESTS PASSED!")
        print("✅ Strategy Marketplace with advanced filtering and social features")
        print("✅ Member Directory with leaderboards and connection management")
        print("✅ Club Analytics with comprehensive charts and metrics")
        print("✅ Enhanced API endpoints for all new functionality")
        print("✅ Responsive design optimized for all devices")
        print("✅ Seamless integration with existing Money Circle systems")
        print("\n🌟 MONEY CIRCLE NOW HAS PROFESSIONAL-GRADE COLLABORATIVE FEATURES!")
        return 0
    else:
        print("❌ SOME ENHANCED FEATURES NEED ATTENTION")
        if passed >= total * 0.75:
            print("⚠️ Most features working - minor issues to resolve")
        else:
            print("❌ Major issues detected - requires investigation")
        return 1

if __name__ == "__main__":
    sys.exit(main())
