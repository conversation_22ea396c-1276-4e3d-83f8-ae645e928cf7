"""
SignalStar API client for fetching social sentiment data.
"""

import httpx
import logging
import asyncio
import time
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class SignalStarClient:
    """
    Client for the SignalStar API to fetch social sentiment data.

    This client provides methods to fetch the latest sentiment index
    and historical sentiment data for crypto assets.
    """

    def __init__(self, api_key: str, base_url: str = "https://api.signalstar.com/v1"):
        """
        Initialize the SignalStar client.

        Args:
            api_key: API key for authentication
            base_url: Base URL for the SignalStar API
        """
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
        self.client = httpx.AsyncClient(headers=self.headers, timeout=30.0)

        # Cache for sentiment data
        self._sentiment_cache: Dict[str, float] = {}
        self._historical_cache: Dict[str, Dict[str, Any]] = {}
        self._last_fetch: Optional[datetime] = None

        # Message bus publisher function
        self._publish: Optional[Callable[[str, float, dict], None]] = None

    def set_publisher(self, publish_fn: Callable[[str, float, dict], None]) -> None:
        """
        Set the message bus publisher function.

        Args:
            publish_fn: Function to publish messages to the bus
                        Takes (stream_name, timestamp, payload)
        """
        self._publish = publish_fn
        logger.info("Message bus publisher set for SignalStar client")

    async def get_latest_sentiment(self, symbol: str = "BTC") -> float:
        """
        Get the latest sentiment index for a symbol.

        Args:
            symbol: Trading symbol (default: BTC)

        Returns:
            Latest sentiment index value
        """
        try:
            # Check if we need to fetch new data (cache for 1 minute)
            if (self._last_fetch is None or
                datetime.now() - self._last_fetch > timedelta(minutes=1) or
                symbol not in self._sentiment_cache):

                url = f"{self.base_url}/sentiment/latest"
                params = {"symbol": symbol}

                response = await self.client.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                sentiment_value = float(data["index"])
                self._sentiment_cache[symbol] = sentiment_value
                self._last_fetch = datetime.now()

                logger.debug(f"Fetched latest sentiment for {symbol}: {sentiment_value}")

                # Publish to message bus if publisher is set
                if self._publish:
                    self._publish(
                        "signalstar.sentiment",
                        time.time(),
                        {
                            "symbol": symbol,
                            "sentiment": sentiment_value,
                            "timestamp": datetime.now().isoformat()
                        }
                    )

            return self._sentiment_cache[symbol]

        except Exception as e:
            logger.error(f"Error fetching latest sentiment: {e}")
            # Return neutral sentiment if there's an error
            return 50.0

    async def get_historical_sentiment(self, minutes: int, symbol: str = "BTC") -> List[float]:
        """
        Get historical sentiment data for a symbol.

        Args:
            minutes: Number of minutes of historical data to fetch
            symbol: Trading symbol (default: BTC)

        Returns:
            List of historical sentiment index values
        """
        try:
            cache_key = f"{symbol}_{minutes}"

            # Check if we need to fetch new data (cache for 5 minutes)
            if (cache_key not in self._historical_cache or
                datetime.now() - self._historical_cache[cache_key]["timestamp"] > timedelta(minutes=5)):

                url = f"{self.base_url}/sentiment/history"
                params = {
                    "symbol": symbol,
                    "interval": f"{minutes}m"
                }

                response = await self.client.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                sentiment_values = [float(item["index"]) for item in data["data"]]

                self._historical_cache[cache_key] = {
                    "data": sentiment_values,
                    "timestamp": datetime.now()
                }

                logger.debug(f"Fetched historical sentiment for {symbol} ({minutes}m): {len(sentiment_values)} points")

            return self._historical_cache[cache_key]["data"]

        except Exception as e:
            logger.error(f"Error fetching historical sentiment: {e}")
            # Return neutral sentiment if there's an error
            return [50.0] * minutes

    async def close(self):
        """Close the HTTP client session."""
        await self.client.aclose()

# Synchronous wrapper for compatibility
class SyncSignalStarClient:
    """
    Synchronous wrapper for the async SignalStarClient.
    """

    def __init__(self, api_key: str, base_url: str = "https://api.signalstar.com/v1"):
        """Initialize the synchronous SignalStar client."""
        self.async_client = SignalStarClient(api_key, base_url)

    def get_latest_sentiment(self, symbol: str = "BTC") -> float:
        """Get the latest sentiment index (synchronous)."""
        return asyncio.run(self.async_client.get_latest_sentiment(symbol))

    def get_historical_sentiment(self, minutes: int, symbol: str = "BTC") -> List[float]:
        """Get historical sentiment data (synchronous)."""
        return asyncio.run(self.async_client.get_historical_sentiment(minutes, symbol))

    def close(self):
        """Close the HTTP client session."""
        asyncio.run(self.async_client.close())
