"""
RSI-based model for the smart-trader system.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple

from core.utils import calculate_rsi

logger = logging.getLogger(__name__)


class RSIModel:
    """
    RSI-based trading model.

    This model uses Relative Strength Index (RSI) to generate trading signals.
    It can detect overbought and oversold conditions, as well as divergences
    between price and RSI.
    """

    def __init__(
        self,
        period: int = 14,
        overbought_threshold: float = 70.0,
        oversold_threshold: float = 30.0,
        divergence_lookback: int = 10
    ):
        """
        Initialize the RSI model.

        Args:
            period: RSI period
            overbought_threshold: Threshold for overbought condition
            oversold_threshold: Threshold for oversold condition
            divergence_lookback: Number of periods to look back for divergence
        """
        self.period = period
        self.overbought_threshold = overbought_threshold
        self.oversold_threshold = oversold_threshold
        self.divergence_lookback = divergence_lookback

        # Cache for RSI values
        self._rsi_cache: Dict[str, np.ndarray] = {}
        self._price_cache: Dict[str, np.ndarray] = {}

    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'close_prices': Array of close prices
                - 'timestamp': Current timestamp

        Returns:
            Dictionary of prediction results including:
                - 'rsi': Current RSI value
                - 'is_overbought': Whether RSI is in overbought territory
                - 'is_oversold': Whether RSI is in oversold territory
                - 'bullish_divergence': Whether there's a bullish divergence
                - 'bearish_divergence': Whether there's a bearish divergence
                - 'signal_strength': Signal strength between -1 and 1
        """
        symbol = features.get('symbol', '')
        close_prices = features.get('close_prices', [])

        if len(close_prices) < self.period + 1:
            logger.warning(f"Not enough price data for RSI calculation: {len(close_prices)} < {self.period + 1}")
            return {
                'rsi': 50.0,
                'is_overbought': False,
                'is_oversold': False,
                'bullish_divergence': False,
                'bearish_divergence': False,
                'signal_strength': 0.0
            }

        # Convert to numpy array if needed
        if not isinstance(close_prices, np.ndarray):
            close_prices = np.array(close_prices, dtype=np.float64)

        # Calculate RSI
        rsi_values = calculate_rsi(close_prices, self.period)

        # Get current RSI (last value)
        current_rsi = rsi_values[-1]

        # Update caches
        self._rsi_cache[symbol] = rsi_values
        self._price_cache[symbol] = close_prices

        # Check for overbought/oversold conditions
        is_overbought = current_rsi > self.overbought_threshold
        is_oversold = current_rsi < self.oversold_threshold

        # Check for divergences
        bullish_divergence, bearish_divergence = self._check_divergences(symbol)

        # Calculate signal strength
        signal_strength = self._calculate_signal_strength(current_rsi, bullish_divergence, bearish_divergence)

        return {
            'rsi': float(current_rsi),
            'is_overbought': is_overbought,
            'is_oversold': is_oversold,
            'bullish_divergence': bullish_divergence,
            'bearish_divergence': bearish_divergence,
            'signal_strength': signal_strength,
            'prob_overbought': self._calculate_overbought_probability(current_rsi)
        }

    def _check_divergences(self, symbol: str) -> Tuple[bool, bool]:
        """
        Check for bullish and bearish divergences.

        Args:
            symbol: Trading symbol

        Returns:
            Tuple of (bullish_divergence, bearish_divergence)
        """
        if symbol not in self._rsi_cache or symbol not in self._price_cache:
            return False, False

        rsi_values = self._rsi_cache[symbol]
        price_values = self._price_cache[symbol]

        if len(rsi_values) < self.divergence_lookback or len(price_values) < self.divergence_lookback:
            return False, False

        # Get the lookback window
        rsi_window = rsi_values[-self.divergence_lookback:]
        price_window = price_values[-self.divergence_lookback:]

        # Find local minima and maxima
        rsi_min_idx = np.argmin(rsi_window)
        rsi_max_idx = np.argmax(rsi_window)
        price_min_idx = np.argmin(price_window)
        price_max_idx = np.argmax(price_window)

        # Check for bullish divergence (price makes lower low but RSI makes higher low)
        bullish_divergence = (
            price_min_idx > rsi_min_idx and
            price_window[price_min_idx] < price_window[rsi_min_idx] and
            rsi_window[price_min_idx] > rsi_window[rsi_min_idx]
        )

        # Check for bearish divergence (price makes higher high but RSI makes lower high)
        bearish_divergence = (
            price_max_idx > rsi_max_idx and
            price_window[price_max_idx] > price_window[rsi_max_idx] and
            rsi_window[price_max_idx] < rsi_window[rsi_max_idx]
        )

        return bullish_divergence, bearish_divergence

    def _calculate_signal_strength(
        self,
        rsi: float,
        bullish_divergence: bool,
        bearish_divergence: bool
    ) -> float:
        """
        Calculate signal strength between -1 and 1.

        Args:
            rsi: Current RSI value
            bullish_divergence: Whether there's a bullish divergence
            bearish_divergence: Whether there's a bearish divergence

        Returns:
            Signal strength between -1 and 1
        """
        # Base signal from RSI
        if rsi > 50:
            base_signal = (rsi - 50) / (self.overbought_threshold - 50)
        else:
            base_signal = (rsi - 50) / (50 - self.oversold_threshold)

        # Clamp to [-1, 1]
        base_signal = max(-1.0, min(1.0, base_signal))

        # Adjust for divergences
        if bullish_divergence:
            base_signal = max(0.2, base_signal)  # Ensure at least slightly positive
        if bearish_divergence:
            base_signal = min(-0.2, base_signal)  # Ensure at least slightly negative

        return base_signal

    def _calculate_overbought_probability(self, rsi: float) -> float:
        """
        Calculate the probability that the market is overbought.

        Args:
            rsi: Current RSI value

        Returns:
            Probability between 0 and 1
        """
        if rsi >= self.overbought_threshold:
            # Linearly scale from threshold to 100
            return 0.5 + 0.5 * (rsi - self.overbought_threshold) / (100 - self.overbought_threshold)
        elif rsi <= self.oversold_threshold:
            # Low probability when oversold
            return 0.0
        else:
            # Linear scale from oversold to overbought
            return 0.5 * (rsi - self.oversold_threshold) / (self.overbought_threshold - self.oversold_threshold)
