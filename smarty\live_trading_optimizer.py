#!/usr/bin/env python3
"""
💰 Live Trading Configuration Optimizer

Optimizes trading configurations for live operations with real funds.
Focuses on risk management, position sizing, and strategy allocation
for the $100 Epinnox trading account.
"""

import asyncio
import json
import yaml
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TradingAccount:
    """Trading account configuration."""
    balance: float
    max_daily_risk: float
    max_position_risk: float
    available_margin: float
    currency: str = "USDT"

@dataclass
class StrategyAllocation:
    """Strategy allocation configuration."""
    name: str
    allocation_percent: float
    max_position_size: float
    min_confidence: float
    risk_multiplier: float
    enabled: bool = True

class LiveTradingOptimizer:
    """Optimize trading configurations for live operations."""
    
    def __init__(self, account_balance: float = 100.0):
        self.account = TradingAccount(
            balance=account_balance,
            max_daily_risk=account_balance * 0.05,  # 5% daily risk limit
            max_position_risk=account_balance * 0.02,  # 2% per position risk
            available_margin=account_balance * 0.8  # 80% available for trading
        )
        
    async def optimize_for_live_trading(self) -> Dict[str, Any]:
        """Generate optimized configuration for live trading."""
        logger.info("💰 Optimizing configuration for live trading")
        logger.info(f"   Account Balance: ${self.account.balance:.2f}")
        logger.info(f"   Daily Risk Limit: ${self.account.max_daily_risk:.2f}")
        logger.info(f"   Position Risk Limit: ${self.account.max_position_risk:.2f}")
        
        # Calculate optimal strategy allocations
        strategy_allocations = self._calculate_strategy_allocations()
        
        # Generate risk management rules
        risk_management = self._generate_risk_management_rules()
        
        # Create position sizing rules
        position_sizing = self._create_position_sizing_rules()
        
        # Generate trading schedule
        trading_schedule = self._create_trading_schedule()
        
        # Create optimized configuration
        optimized_config = {
            "trading": {
                "mode": "live",
                "account_balance": self.account.balance,
                "max_daily_loss": self.account.max_daily_risk,
                "max_position_size": self.account.max_position_risk,
                "min_position_size": 2.0,  # Minimum $2 position
                "leverage": 1,  # No leverage for safety
                
                "risk_management": risk_management,
                "position_sizing": position_sizing,
                "trading_schedule": trading_schedule
            },
            
            "strategies": {
                allocation.name.lower().replace(" ", "_"): {
                    "enabled": allocation.enabled,
                    "allocation": allocation.allocation_percent,
                    "max_position_size": allocation.max_position_size,
                    "min_confidence": allocation.min_confidence,
                    "risk_multiplier": allocation.risk_multiplier
                }
                for allocation in strategy_allocations
            },
            
            "market_data": {
                "primary_source": "htx",
                "fallback_source": "binance",
                "symbols": ["BTC-USDT"],  # Start with single pair
                "max_data_age": 10,
                "min_update_frequency": 1
            },
            
            "monitoring": {
                "track_performance": True,
                "save_trades": True,
                "log_level": "INFO",
                "alerts": {
                    "large_loss": 2.0,  # Alert if single trade loss > $2
                    "daily_loss_limit": 4.0,  # Alert if daily loss > $4
                    "connection_issues": True
                }
            },
            
            "safety": {
                "max_consecutive_losses": 3,  # Conservative for small account
                "cooling_off_period": 1800,  # 30 minutes cooling off
                "max_total_exposure": 60.0,  # Maximum 60% exposure
                "emergency_stop_loss": 10.0,  # Emergency stop if total loss > $10
                "manual_override": True
            }
        }
        
        # Save optimized configuration
        await self._save_optimized_config(optimized_config)
        
        logger.info("✅ Live trading configuration optimized")
        return optimized_config
    
    def _calculate_strategy_allocations(self) -> List[StrategyAllocation]:
        """Calculate optimal strategy allocations based on performance and risk."""
        logger.info("📊 Calculating optimal strategy allocations")
        
        # Strategy allocations optimized for $100 account
        allocations = [
            StrategyAllocation(
                name="Smart Model Integrated",
                allocation_percent=50.0,  # Primary strategy
                max_position_size=15.0,   # $15 max position
                min_confidence=0.75,      # High confidence required
                risk_multiplier=1.0,      # Standard risk
                enabled=True
            ),
            StrategyAllocation(
                name="Smart Strategy Only",
                allocation_percent=30.0,  # Secondary strategy
                max_position_size=10.0,   # $10 max position
                min_confidence=0.7,       # Good confidence required
                risk_multiplier=0.8,      # Lower risk
                enabled=True
            ),
            StrategyAllocation(
                name="Order Flow",
                allocation_percent=20.0,  # Tertiary strategy
                max_position_size=8.0,    # $8 max position
                min_confidence=0.8,       # Very high confidence required
                risk_multiplier=0.6,      # Lowest risk
                enabled=True
            )
        ]
        
        for allocation in allocations:
            logger.info(f"   {allocation.name}: {allocation.allocation_percent}% "
                       f"(max ${allocation.max_position_size})")
        
        return allocations
    
    def _generate_risk_management_rules(self) -> Dict[str, Any]:
        """Generate comprehensive risk management rules."""
        logger.info("🛡️ Generating risk management rules")
        
        return {
            "max_daily_loss": self.account.max_daily_risk,
            "max_drawdown": 15.0,  # 15% max drawdown
            "position_sizing": "fixed_dollar",  # Fixed dollar amounts
            "stop_loss_percent": 1.0,  # 1% stop loss
            "take_profit_percent": 2.0,  # 2% take profit (2:1 ratio)
            "trailing_stop": True,
            "trailing_distance": 0.5,  # 0.5% trailing distance
            
            # Advanced risk controls
            "correlation_limit": 0.7,  # Don't trade highly correlated pairs
            "volatility_filter": True,  # Filter out high volatility periods
            "news_filter": True,  # Avoid trading during major news
            "weekend_trading": False,  # No weekend trading for crypto
            
            # Account protection
            "daily_trade_limit": 10,  # Maximum 10 trades per day
            "hourly_trade_limit": 3,  # Maximum 3 trades per hour
            "minimum_gap_minutes": 15,  # 15 minutes between trades
        }
    
    def _create_position_sizing_rules(self) -> Dict[str, Any]:
        """Create position sizing rules optimized for small account."""
        logger.info("📏 Creating position sizing rules")
        
        return {
            "method": "fixed_dollar",  # Use fixed dollar amounts
            "base_position_size": 5.0,  # $5 base position
            "max_position_size": self.account.max_position_risk,
            "min_position_size": 2.0,  # $2 minimum
            
            # Dynamic sizing based on confidence
            "confidence_multiplier": {
                "0.9": 1.5,  # 90%+ confidence: 1.5x size
                "0.8": 1.2,  # 80%+ confidence: 1.2x size
                "0.7": 1.0,  # 70%+ confidence: 1.0x size
                "0.6": 0.8,  # 60%+ confidence: 0.8x size
            },
            
            # Risk-based adjustments
            "volatility_adjustment": True,  # Reduce size in high volatility
            "drawdown_adjustment": True,    # Reduce size during drawdown
            "winning_streak_adjustment": False,  # Don't increase size on wins
        }
    
    def _create_trading_schedule(self) -> Dict[str, Any]:
        """Create optimized trading schedule."""
        logger.info("⏰ Creating trading schedule")
        
        return {
            "timezone": "UTC",
            "active_hours": {
                "start": "00:00",  # 24/7 for crypto
                "end": "23:59"
            },
            "high_activity_periods": [
                {"start": "08:00", "end": "12:00", "description": "Asian session"},
                {"start": "13:00", "end": "17:00", "description": "European session"},
                {"start": "20:00", "end": "24:00", "description": "US session"}
            ],
            "avoid_periods": [
                {"start": "02:00", "end": "06:00", "description": "Low liquidity"},
            ],
            "weekend_trading": False,  # Conservative approach
            "holiday_trading": False   # Avoid major holidays
        }
    
    async def _save_optimized_config(self, config: Dict[str, Any]):
        """Save optimized configuration to file."""
        timestamp = int(time.time())
        config_file = f"trading_config_live_optimized_{timestamp}.yaml"
        
        # Add metadata
        config["metadata"] = {
            "generated_at": datetime.now().isoformat(),
            "optimizer_version": "1.0",
            "account_balance": self.account.balance,
            "optimization_target": "live_trading_safety"
        }
        
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        logger.info(f"💾 Optimized configuration saved to {config_file}")
        
        # Also update the main live config
        with open("trading_config_live.yaml", 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        logger.info("✅ Main live configuration updated")
    
    async def generate_trading_plan(self) -> Dict[str, Any]:
        """Generate comprehensive trading plan for live operations."""
        logger.info("📋 Generating comprehensive trading plan")
        
        trading_plan = {
            "account_overview": {
                "initial_balance": self.account.balance,
                "risk_per_trade": self.account.max_position_risk,
                "daily_risk_limit": self.account.max_daily_risk,
                "target_monthly_return": 10.0,  # 10% monthly target
                "maximum_drawdown": 15.0
            },
            
            "strategy_deployment": {
                "phase_1": {
                    "duration": "Week 1-2",
                    "strategies": ["Smart Model Integrated"],
                    "allocation": 50.0,
                    "max_position": 10.0,
                    "goal": "Validate primary strategy performance"
                },
                "phase_2": {
                    "duration": "Week 3-4",
                    "strategies": ["Smart Model Integrated", "Smart Strategy Only"],
                    "allocation": 80.0,
                    "max_position": 15.0,
                    "goal": "Add secondary strategy if Phase 1 successful"
                },
                "phase_3": {
                    "duration": "Month 2+",
                    "strategies": ["All three strategies"],
                    "allocation": 100.0,
                    "max_position": 20.0,
                    "goal": "Full deployment if previous phases successful"
                }
            },
            
            "performance_targets": {
                "weekly": {
                    "min_return": 1.0,   # 1% minimum
                    "target_return": 2.5, # 2.5% target
                    "max_drawdown": 5.0   # 5% max weekly drawdown
                },
                "monthly": {
                    "min_return": 5.0,    # 5% minimum
                    "target_return": 10.0, # 10% target
                    "max_drawdown": 15.0   # 15% max monthly drawdown
                }
            },
            
            "risk_controls": {
                "stop_trading_if": [
                    "Daily loss exceeds $5",
                    "Weekly loss exceeds $10",
                    "3 consecutive losing days",
                    "Drawdown exceeds 15%"
                ],
                "review_strategy_if": [
                    "Win rate below 40%",
                    "Sharpe ratio below 0.5",
                    "Maximum drawdown exceeds 10%"
                ]
            },
            
            "monitoring_schedule": {
                "daily": "Review P&L, drawdown, and trade quality",
                "weekly": "Analyze strategy performance and adjust allocations",
                "monthly": "Comprehensive review and optimization"
            }
        }
        
        # Save trading plan
        plan_file = f"trading_plan_live_{int(time.time())}.json"
        with open(plan_file, 'w') as f:
            json.dump(trading_plan, f, indent=2)
        
        logger.info(f"📋 Trading plan saved to {plan_file}")
        return trading_plan
    
    async def validate_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trading configuration for safety."""
        logger.info("🔍 Validating trading configuration")
        
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }
        
        # Check position sizing
        max_position = config.get("trading", {}).get("max_position_size", 0)
        if max_position > self.account.balance * 0.25:  # More than 25% of account
            validation_results["warnings"].append(
                f"Position size ${max_position} is large for ${self.account.balance} account"
            )
        
        # Check daily risk
        daily_risk = config.get("trading", {}).get("max_daily_loss", 0)
        if daily_risk > self.account.balance * 0.1:  # More than 10% daily risk
            validation_results["errors"].append(
                f"Daily risk ${daily_risk} exceeds safe limit for small account"
            )
            validation_results["valid"] = False
        
        # Check strategy allocations
        total_allocation = sum(
            strategy.get("allocation", 0) 
            for strategy in config.get("strategies", {}).values()
        )
        if total_allocation > 100:
            validation_results["errors"].append(
                f"Total strategy allocation {total_allocation}% exceeds 100%"
            )
            validation_results["valid"] = False
        
        # Add recommendations
        if max_position < 5.0:
            validation_results["recommendations"].append(
                "Consider increasing minimum position size for better profit potential"
            )
        
        logger.info(f"✅ Configuration validation complete: {'VALID' if validation_results['valid'] else 'INVALID'}")
        return validation_results

async def main():
    """Main optimizer runner."""
    logger.info("💰 Live Trading Configuration Optimizer")
    logger.info("=" * 50)
    
    # Initialize optimizer with current account balance
    optimizer = LiveTradingOptimizer(account_balance=100.0)
    
    # Generate optimized configuration
    optimized_config = await optimizer.optimize_for_live_trading()
    
    # Validate configuration
    validation = await optimizer.validate_configuration(optimized_config)
    
    # Generate trading plan
    trading_plan = await optimizer.generate_trading_plan()
    
    # Print summary
    logger.info("\n📊 Optimization Summary")
    logger.info("=" * 30)
    logger.info(f"Account Balance: ${optimizer.account.balance:.2f}")
    logger.info(f"Max Daily Risk: ${optimizer.account.max_daily_risk:.2f}")
    logger.info(f"Max Position Size: ${optimizer.account.max_position_risk:.2f}")
    logger.info(f"Configuration Valid: {'✅ YES' if validation['valid'] else '❌ NO'}")
    
    if validation["warnings"]:
        logger.warning("⚠️ Warnings:")
        for warning in validation["warnings"]:
            logger.warning(f"   - {warning}")
    
    if validation["errors"]:
        logger.error("❌ Errors:")
        for error in validation["errors"]:
            logger.error(f"   - {error}")
    
    logger.info("\n🎉 Live trading optimization complete!")

if __name__ == "__main__":
    asyncio.run(main())
