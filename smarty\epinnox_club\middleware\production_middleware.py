#!/usr/bin/env python3
"""
Money Circle Production Middleware
Security headers, compression, and production optimizations.
"""

import gzip
import time
import logging
from typing import Callable, Dict, Any
from aiohttp import web, hdrs
from aiohttp.web_middlewares import middleware

logger = logging.getLogger(__name__)

@middleware
async def security_headers_middleware(request: web.Request, handler: Callable) -> web.Response:
    """Add security headers to all responses."""
    response = await handler(request)
    
    # Get security headers from config
    config = getattr(request.app, 'config', None)
    if config and hasattr(config, 'SECURITY_HEADERS'):
        security_headers = config.SECURITY_HEADERS
        
        for header, value in security_headers.items():
            response.headers[header] = value
    
    # Add security headers for production
    if not getattr(config, 'DEBUG', True):
        # Remove server information
        if 'Server' in response.headers:
            del response.headers['Server']
        
        # Add additional security headers
        response.headers['X-Powered-By'] = 'Money Circle Investment Platform'
        
        # HTTPS redirect header if not using HTTPS
        if config and getattr(config, 'FORCE_HTTPS', False):
            if request.scheme != 'https' and request.host != 'localhost':
                # Redirect to HTTPS
                https_url = f"https://{request.host}{request.path_qs}"
                return web.Response(status=301, headers={'Location': https_url})
    
    return response

@middleware
async def compression_middleware(request: web.Request, handler: Callable) -> web.Response:
    """Compress responses for better performance."""
    response = await handler(request)
    
    # Get compression settings from config
    config = getattr(request.app, 'config', None)
    if not config or not getattr(config, 'ENABLE_COMPRESSION', False):
        return response
    
    # Check if client accepts gzip
    accept_encoding = request.headers.get('Accept-Encoding', '')
    if 'gzip' not in accept_encoding.lower():
        return response
    
    # Only compress text-based content
    content_type = response.headers.get('Content-Type', '')
    compressible_types = [
        'text/html',
        'text/css',
        'text/javascript',
        'application/javascript',
        'application/json',
        'text/plain',
        'application/xml',
        'text/xml'
    ]
    
    if not any(ct in content_type for ct in compressible_types):
        return response
    
    # Don't compress if already compressed
    if response.headers.get('Content-Encoding'):
        return response
    
    # Don't compress small responses
    if hasattr(response, 'body') and len(response.body) < 1024:
        return response
    
    try:
        # Compress the response body
        if hasattr(response, 'body') and response.body:
            compression_level = getattr(config, 'COMPRESSION_LEVEL', 6)
            compressed_body = gzip.compress(response.body, compresslevel=compression_level)
            
            # Only use compression if it actually reduces size
            if len(compressed_body) < len(response.body):
                response.body = compressed_body
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = str(len(compressed_body))
                response.headers['Vary'] = 'Accept-Encoding'
                
                logger.debug(f"Compressed response: {len(response.body)} -> {len(compressed_body)} bytes")
    
    except Exception as e:
        logger.warning(f"Compression failed: {e}")
    
    return response

@middleware
async def performance_middleware(request: web.Request, handler: Callable) -> web.Response:
    """Add performance optimizations and caching headers."""
    start_time = time.time()
    
    response = await handler(request)
    
    # Add performance headers
    processing_time = time.time() - start_time
    response.headers['X-Response-Time'] = f"{processing_time:.3f}s"
    
    # Get config for cache settings
    config = getattr(request.app, 'config', None)
    
    # Add caching headers for static files
    if request.path.startswith('/static/'):
        if config and hasattr(config, 'STATIC_FILE_CACHE_TIMEOUT'):
            cache_timeout = config.STATIC_FILE_CACHE_TIMEOUT
            response.headers['Cache-Control'] = f'public, max-age={cache_timeout}, immutable'
            response.headers['Expires'] = time.strftime(
                '%a, %d %b %Y %H:%M:%S GMT',
                time.gmtime(time.time() + cache_timeout)
            )
        
        # Add ETag for static files
        if hasattr(response, 'body') and response.body:
            import hashlib
            etag = hashlib.md5(response.body).hexdigest()
            response.headers['ETag'] = f'"{etag}"'
            
            # Check if client has cached version
            if_none_match = request.headers.get('If-None-Match')
            if if_none_match and etag in if_none_match:
                return web.Response(status=304)
    
    # Add caching headers for API responses
    elif request.path.startswith('/api/'):
        if config and hasattr(config, 'API_CACHE_TIMEOUT'):
            cache_timeout = config.API_CACHE_TIMEOUT
            response.headers['Cache-Control'] = f'private, max-age={cache_timeout}'
    
    # Add no-cache headers for sensitive pages
    elif request.path in ['/login', '/register', '/dashboard', '/club']:
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    
    return response

@middleware
async def rate_limiting_middleware(request: web.Request, handler: Callable) -> web.Response:
    """Basic rate limiting for API endpoints."""
    # Skip rate limiting for static files
    if request.path.startswith('/static/'):
        return await handler(request)
    
    # Get client IP
    client_ip = request.remote or '127.0.0.1'
    
    # Get rate limiting config
    config = getattr(request.app, 'config', None)
    if not config:
        return await handler(request)
    
    # Simple in-memory rate limiting (for production, use Redis)
    if not hasattr(request.app, '_rate_limit_store'):
        request.app._rate_limit_store = {}
    
    rate_limit_store = request.app._rate_limit_store
    current_time = time.time()
    
    # Clean old entries (older than 1 minute)
    rate_limit_store = {
        ip: timestamps for ip, timestamps in rate_limit_store.items()
        if any(t > current_time - 60 for t in timestamps)
    }
    request.app._rate_limit_store = rate_limit_store
    
    # Check rate limit
    if client_ip not in rate_limit_store:
        rate_limit_store[client_ip] = []
    
    # Remove timestamps older than 1 minute
    rate_limit_store[client_ip] = [
        t for t in rate_limit_store[client_ip] 
        if t > current_time - 60
    ]
    
    # Check if rate limit exceeded
    rate_limit = getattr(config, 'API_RATE_LIMIT', 100)
    if len(rate_limit_store[client_ip]) >= rate_limit:
        logger.warning(f"Rate limit exceeded for IP: {client_ip}")
        return web.json_response(
            {'error': 'Rate limit exceeded. Please try again later.'},
            status=429,
            headers={'Retry-After': '60'}
        )
    
    # Add current request timestamp
    rate_limit_store[client_ip].append(current_time)
    
    response = await handler(request)
    
    # Add rate limit headers
    remaining = max(0, rate_limit - len(rate_limit_store[client_ip]))
    response.headers['X-RateLimit-Limit'] = str(rate_limit)
    response.headers['X-RateLimit-Remaining'] = str(remaining)
    response.headers['X-RateLimit-Reset'] = str(int(current_time + 60))
    
    return response

@middleware
async def health_check_middleware(request: web.Request, handler: Callable) -> web.Response:
    """Handle health check requests."""
    if request.path == '/health':
        # Basic health check
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'version': '1.0.0',
            'service': 'Money Circle Investment Platform'
        }
        
        # Check database connectivity
        try:
            app = request.app
            if hasattr(app, 'money_circle_app') and hasattr(app.money_circle_app, 'db_manager'):
                db_manager = app.money_circle_app.db_manager
                # Simple database check
                cursor = db_manager.conn.execute("SELECT 1")
                cursor.fetchone()
                health_status['database'] = 'connected'
            else:
                health_status['database'] = 'not_configured'
        except Exception as e:
            health_status['database'] = f'error: {str(e)}'
            health_status['status'] = 'degraded'
        
        # Check configuration
        config = getattr(request.app, 'config', None)
        if config:
            health_status['environment'] = 'production' if not getattr(config, 'DEBUG', True) else 'development'
            health_status['compression'] = getattr(config, 'ENABLE_COMPRESSION', False)
            health_status['security_headers'] = bool(getattr(config, 'SECURITY_HEADERS', None))
        
        status_code = 200 if health_status['status'] == 'healthy' else 503
        return web.json_response(health_status, status=status_code)
    
    return await handler(request)

def setup_production_middleware(app: web.Application, config: Any) -> None:
    """Setup all production middleware."""
    # Store config in app for middleware access
    app.config = config
    
    # Add middleware in order (last added runs first)
    app.middlewares.append(health_check_middleware)
    
    if getattr(config, 'API_RATE_LIMIT', None):
        app.middlewares.append(rate_limiting_middleware)
        logger.info("[OK] Rate limiting middleware enabled")
    
    app.middlewares.append(performance_middleware)
    logger.info("[OK] Performance middleware enabled")
    
    if getattr(config, 'ENABLE_COMPRESSION', False):
        app.middlewares.append(compression_middleware)
        logger.info("[OK] Compression middleware enabled")
    
    if getattr(config, 'SECURITY_HEADERS', None):
        app.middlewares.append(security_headers_middleware)
        logger.info("[OK] Security headers middleware enabled")
    
    logger.info("[OK] Production middleware setup complete")
