{"strategy_name": "Order Flow", "test_start_time": "2025-05-29T03:49:38.965323", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 5918, "orderflow_messages": 0, "dataframe_messages": 0, "market_messages": 2965, "signal_messages": 0, "data_flowing": true}, "components": {"dataframe_runner": true, "order_flow_analysis": false, "signal_generation": false, "market_data_feed": true}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-29T03:50:19.890111"}