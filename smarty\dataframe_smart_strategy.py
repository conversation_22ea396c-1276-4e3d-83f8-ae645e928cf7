#!/usr/bin/env python3
"""
DataFrame-Based Smart Strategy

A simplified version of the smart strategy that works directly with
pandas DataFrames from live market data.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from live_data_bridge import LiveDataBridge
from core.events import Signal, Side

logger = logging.getLogger(__name__)


class DataFrameSmartStrategy:
    """Smart trading strategy using DataFrame-based analysis."""

    def __init__(self, config: Dict = None):
        """Initialize the DataFrame-based smart strategy."""
        self.config = config or {}
        self.data_bridge = LiveDataBridge()
        self.data_bridge.connect()

        # Strategy parameters
        self.rsi_oversold = self.config.get('rsi_oversold', 30)
        self.rsi_overbought = self.config.get('rsi_overbought', 70)
        self.sma_fast = self.config.get('sma_fast', 10)
        self.sma_slow = self.config.get('sma_slow', 20)
        self.min_confidence = self.config.get('min_confidence', 0.6)

        logger.info("📊 DataFrame Smart Strategy initialized")

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band

    def calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD indicator."""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    def analyze_market_data(self, symbol: str = "BTC-USDT") -> Dict:
        """Analyze current market conditions."""
        try:
            # Get live market data
            df = self.data_bridge.get_live_price_data(symbol, lookback_minutes=120)

            if df.empty or len(df) < 20:
                logger.warning(f"Insufficient data for analysis: {len(df)} points")
                return {}

            # Calculate technical indicators
            df['rsi'] = self.calculate_rsi(df['close'])
            df['sma_fast'] = df['close'].rolling(window=self.sma_fast).mean()
            df['sma_slow'] = df['close'].rolling(window=self.sma_slow).mean()

            # Bollinger Bands
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = self.calculate_bollinger_bands(df['close'])

            # MACD
            df['macd'], df['macd_signal'], df['macd_histogram'] = self.calculate_macd(df['close'])

            # Get current values
            current = df.iloc[-1]
            previous = df.iloc[-2] if len(df) > 1 else current

            analysis = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'current_price': current['close'],
                'price_change': current['close'] - previous['close'],
                'price_change_pct': ((current['close'] - previous['close']) / previous['close']) * 100,

                # Technical indicators
                'rsi': current['rsi'],
                'rsi_previous': previous['rsi'],
                'sma_fast': current['sma_fast'],
                'sma_slow': current['sma_slow'],
                'bb_position': (current['close'] - current['bb_lower']) / (current['bb_upper'] - current['bb_lower']),
                'macd': current['macd'],
                'macd_signal': current['macd_signal'],
                'macd_histogram': current['macd_histogram'],

                # Volume analysis
                'volume': current['volume'],
                'avg_volume': df['volume'].tail(20).mean(),
                'volume_ratio': current['volume'] / df['volume'].tail(20).mean() if df['volume'].tail(20).mean() > 0 else 1,

                # Trend analysis
                'sma_trend': 'bullish' if current['sma_fast'] > current['sma_slow'] else 'bearish',
                'price_vs_sma': 'above' if current['close'] > current['sma_slow'] else 'below',
                'macd_trend': 'bullish' if current['macd'] > current['macd_signal'] else 'bearish'
            }

            logger.info(f"📊 Market analysis complete for {symbol}")
            logger.info(f"   Price: ${analysis['current_price']:.2f} ({analysis['price_change_pct']:+.2f}%)")
            logger.info(f"   RSI: {analysis['rsi']:.1f}, Trend: {analysis['sma_trend']}")

            return analysis

        except Exception as e:
            logger.error(f"❌ Error analyzing market data for {symbol}: {e}")
            return {}

    def generate_signal(self, symbol: str = "BTC-USDT") -> Optional[Signal]:
        """Generate a trading signal based on current market analysis."""
        try:
            analysis = self.analyze_market_data(symbol)

            if not analysis:
                logger.warning("No market analysis available")
                return None

            # Initialize signal components
            signals = []
            reasons = []

            # RSI signals
            if analysis['rsi'] < self.rsi_oversold:
                signals.append(1.0)  # Strong buy
                reasons.append(f"RSI oversold ({analysis['rsi']:.1f})")
            elif analysis['rsi'] > self.rsi_overbought:
                signals.append(-1.0)  # Strong sell
                reasons.append(f"RSI overbought ({analysis['rsi']:.1f})")
            elif analysis['rsi'] < 40:
                signals.append(0.5)  # Weak buy
                reasons.append(f"RSI low ({analysis['rsi']:.1f})")
            elif analysis['rsi'] > 60:
                signals.append(-0.5)  # Weak sell
                reasons.append(f"RSI high ({analysis['rsi']:.1f})")

            # Moving Average signals
            if analysis['sma_trend'] == 'bullish' and analysis['price_vs_sma'] == 'above':
                signals.append(0.7)
                reasons.append("Bullish MA trend")
            elif analysis['sma_trend'] == 'bearish' and analysis['price_vs_sma'] == 'below':
                signals.append(-0.7)
                reasons.append("Bearish MA trend")

            # MACD signals
            if analysis['macd_trend'] == 'bullish' and analysis['macd_histogram'] > 0:
                signals.append(0.6)
                reasons.append("MACD bullish")
            elif analysis['macd_trend'] == 'bearish' and analysis['macd_histogram'] < 0:
                signals.append(-0.6)
                reasons.append("MACD bearish")

            # Bollinger Bands signals
            if analysis['bb_position'] < 0.2:  # Near lower band
                signals.append(0.5)
                reasons.append("Near BB lower band")
            elif analysis['bb_position'] > 0.8:  # Near upper band
                signals.append(-0.5)
                reasons.append("Near BB upper band")

            # Volume confirmation
            if analysis['volume_ratio'] > 1.5:  # High volume
                if signals and signals[-1] > 0:
                    signals.append(0.3)
                    reasons.append("High volume confirmation")
                elif signals and signals[-1] < 0:
                    signals.append(-0.3)
                    reasons.append("High volume confirmation")

            # Calculate final signal
            if not signals:
                logger.debug("No signals generated")
                return None

            final_score = np.mean(signals)
            confidence = min(abs(final_score), 1.0)

            # Determine action
            if final_score > 0.3 and confidence >= self.min_confidence:
                action = Side.BUY
            elif final_score < -0.3 and confidence >= self.min_confidence:
                action = Side.SELL
            else:
                action = Side.HOLD

            # Debug logging for signal generation
            logger.info(f"🔍 Signal Debug for {symbol}:")
            logger.info(f"   Final Score: {final_score:.3f}")
            logger.info(f"   Confidence: {confidence:.3f}")
            logger.info(f"   Min Confidence: {self.min_confidence}")
            logger.info(f"   Action: {action}")
            logger.info(f"   Reasons: {reasons}")

            # Create signal
            if action != Side.HOLD:
                signal = Signal(
                    symbol=symbol,
                    action=action,
                    score=final_score,
                    timestamp=analysis['timestamp'],
                    rationale=f"DataFrame analysis: {', '.join(reasons[:3])}",  # Limit reasons
                    source="dataframe_smart_strategy",
                    metadata={"confidence": confidence, "price": analysis['current_price']}
                )

                logger.info(f"🎯 Signal generated: {action} {symbol}")
                logger.info(f"   Score: {final_score:.3f}, Confidence: {confidence:.3f}")
                logger.info(f"   Rationale: {signal.rationale}")

                return signal
            else:
                logger.debug(f"Signal below threshold: score={final_score:.3f}, confidence={confidence:.3f}")
                return None

        except Exception as e:
            logger.error(f"❌ Error generating signal for {symbol}: {e}")
            return None

    def close(self):
        """Close the strategy and data connections."""
        self.data_bridge.close()
        logger.info("📊 DataFrame Smart Strategy closed")


# Test function
async def test_dataframe_strategy():
    """Test the DataFrame-based strategy."""
    logger.info("🧪 Testing DataFrame Smart Strategy...")

    strategy = DataFrameSmartStrategy()

    try:
        # Test market analysis
        analysis = strategy.analyze_market_data("BTC-USDT")
        if analysis:
            logger.info("✅ Market analysis successful")

        # Test signal generation
        signal = strategy.generate_signal("BTC-USDT")
        if signal:
            logger.info("✅ Signal generation successful")
        else:
            logger.info("ℹ️  No signal generated (normal)")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
    finally:
        strategy.close()


if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_dataframe_strategy())
