# 🧹 Smart-Trader System Cleanup Plan

## **Files to Remove**

### **1. Duplicate/Backup Files**
- `llm_consumer.py.bak`
- `llm_consumer.py.bak.20250522183932`
- `llm_consumer.py.new`
- `orchestrator_with_bus.py` (superseded by main orchestrator)

### **2. Old Test/Debug Files** ✅ COMPLETED
- ❌ `test_imports.py` (removed)
- ❌ `test_specific_imports.py` (removed)
- ❌ `test_orchestrator.py` (removed)
- ❌ `debug_python.py` (removed)
- ❌ `simple_testnet.py` (removed)
- ❌ `minimal_testnet.py` (removed)
- ❌ `check_database.py` (removed)
- ❌ `quick_test.py` (removed)
- ❌ `test_smart_strategy.py` (removed)
- ❌ `smart_strategy_tester.py` (removed)

### **3. Migration/Analysis Scripts (Keep for Reference)**
- Move to `archive/` folder:
  - `migrate_to_htx_optimized.py`
  - `migrate_to_optimized_bus.py`
  - `analyze_phi_e2e_test.py`

### **4. Old Configuration Files** ✅ COMPLETED
- ❌ `config_testnet.yaml` (consolidated into config.yaml)
- ❌ `config_legacy.yaml` (consolidated into config.yaml)
- ❌ `config_simple_testnet.yaml` (consolidated into config.yaml)
- `config_phi.yaml` (superseded by config.yaml)
- `config_phi_e2e_test.yaml`
- `config_phi3_real_data.yaml`
- `config_real_data.yaml`

### **5. Archive Files**
- `config.zip`
- `smarty_v1.01.zip`
- `smarty_v1.02.zip`
- `smarty_v1.03.zip`

### **6. Temporary/Log Files**
- `simulation.log`

### **7. Consolidate LLM Files**
- Keep: `llm_consumer.py` (main)
- Remove: `llm_consumer_fix.py`, `llm_consumer_normalize.py`
- Keep: `phi_llm_consumer.py` (specialized)

### **8. Consolidate Run Scripts**
- Keep: `run_testnet.py`, `run_backtest.py`
- Remove: `run_real_data.py`, `run_test.py`, `run_with_phi_model.py`, `run_phi_e2e_test.py`

## **Files to Keep (Core System)**

### **Main Application Files**
- `orchestrator.py` - Main system orchestrator
- `live_trader.py` - Live trading system
- `demo_live_trading.py` - Demo system
- `test_live_integration.py` - Integration tests

### **Configuration**
- `config_testnet.yaml` - Main configuration
- `credentials.yaml` - API credentials

### **Core Modules**
- `core/` - Core system components
- `models/` - ML models
- `backtester/` - Backtesting system
- `monitoring/` - Monitoring system
- `clients/` - API clients
- `executors/` - Trade execution
- `feeds/` - Data feeds
- `llm/` - LLM integration

### **Utilities**
- `position_manager.py` - Position management
- `position_utils.py` - Position utilities
- `position_dashboard.py` - Position monitoring
- `health_check.py` - System health monitoring
- `monitor.py` - System monitoring
- `simple_test.py` - Basic system test

### **Data & Scripts**
- `generate_sample_data.py` - Data generation
- `download_sample_data.py` - Data download
- `optimize_strategy.py` - Strategy optimization

### **Documentation**
- `README.md` - Main documentation
- `SYSTEM_ACHIEVEMENTS.md` - Achievement summary
- `HTX_FUTURES_OPTIMIZATION_GUIDE.md`
- `MIGRATION_GUIDE.md`
- `SQLITE_OPTIMIZATION_GUIDE.md`
- `TESTNET_DEPLOYMENT.md`

### **Requirements**
- `requirements.txt` - Python dependencies

## **Directory Structure After Cleanup**

```
smarty/
├── core/                    # Core system components
├── models/                  # ML models
├── backtester/             # Backtesting framework
├── monitoring/             # Real-time monitoring
├── clients/                # API clients
├── executors/              # Trade execution
├── feeds/                  # Data feeds
├── llm/                    # LLM integration
├── data/                   # Data storage
├── logs/                   # Log files
├── results/                # Backtest results
├── demo_reports/           # Demo reports
├── test_results/           # Test results
├── archive/                # Archived files
├── orchestrator.py         # Main orchestrator
├── live_trader.py          # Live trading system
├── demo_live_trading.py    # Demo system
├── test_live_integration.py # Integration tests
├── position_manager.py     # Position management
├── llm_consumer.py         # LLM consumer
├── phi_llm_consumer.py     # Phi LLM consumer
├── run_testnet.py          # Testnet runner
├── run_backtest.py         # Backtesting runner
├── health_check.py         # Health monitoring
├── monitor.py              # System monitoring
├── simple_test.py          # Basic tests
├── config.yaml             # SINGLE MASTER CONFIGURATION
├── credentials.yaml        # API credentials
├── requirements.txt        # Dependencies
├── README.md               # Documentation
└── SYSTEM_ACHIEVEMENTS.md  # Achievement summary
```

## **Cleanup Actions**

1. **Create archive directory**
2. **Move files to archive**
3. **Remove unnecessary files**
4. **Clean up __pycache__ directories**
5. **Update imports if needed**
6. **Test system after cleanup**
