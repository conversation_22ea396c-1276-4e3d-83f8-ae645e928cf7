#!/usr/bin/env python3
"""
Run Smart Strategy on Live Data

This script runs the smart strategy using live market data from the SQLite bus,
without requiring HTX API connections (uses existing Binance fallback data).
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from backtester.strategies import smart_model_integrated_strategy
    logger.info("✅ Smart strategy imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import smart strategy: {e}")
    logger.error("Make sure backtester/strategies.py exists and is accessible")
    raise
from pipeline.databus import SQLiteBus
from core.events import Signal
import yaml


class LiveSmartStrategy:
    """Run smart strategy on live data without HTX API dependencies."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the live smart strategy runner."""
        self.config = self._load_config(config_path)
        self.bus = None
        self.running = False
        self.symbols = self.config.get("trading", {}).get("symbols", ["BTC-USDT"])
        self.signal_interval = 30  # Generate signals every 30 seconds

        # Trading settings
        self.trading_enabled = self.config.get("trading", {}).get("enabled", False)
        self.simulation_mode = self.config.get("trading", {}).get("simulation_mode", True)

        logger.info(f"🎯 Smart Strategy initialized for symbols: {self.symbols}")
        logger.info(f"📊 Trading enabled: {self.trading_enabled}")
        logger.info(f"🔧 Simulation mode: {self.simulation_mode}")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {config_path}")
            return config
        except Exception as e:
            logger.error(f"❌ Failed to load config: {e}")
            return {}

    async def start(self):
        """Start the live smart strategy."""
        logger.info("🚀 Starting Live Smart Strategy...")

        try:
            # Connect to SQLite bus
            bus_path = self.config.get("message_bus", {}).get("path", "data/bus.db")
            self.bus = SQLiteBus(path=bus_path, poll_interval=0.5)
            logger.info(f"✅ Connected to SQLite bus at {bus_path}")

            # Set up signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            self.running = True

            # Start strategy loop
            await self._strategy_loop()

        except Exception as e:
            logger.error(f"❌ Failed to start live strategy: {e}")
            raise

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.running = False

    async def _strategy_loop(self):
        """Main strategy execution loop."""
        logger.info("📊 Starting strategy loop...")

        signal_count = 0

        while self.running:
            try:
                # Generate signals for all symbols
                for symbol in self.symbols:
                    signal = await self._generate_signal(symbol)

                    if signal:
                        signal_count += 1
                        await self._process_signal(signal)

                # Log periodic status
                if signal_count > 0 and signal_count % 10 == 0:
                    logger.info(f"📈 Generated {signal_count} signals so far")

                # Wait before next iteration
                await asyncio.sleep(self.signal_interval)

            except Exception as e:
                logger.error(f"❌ Error in strategy loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _generate_signal(self, symbol: str) -> Signal:
        """Generate a trading signal for the given symbol."""
        try:
            # Use the smart model integrated strategy
            signals = await smart_model_integrated_strategy(
                timestamp=datetime.now(),
                symbols=[symbol],
                signal_source="live_smart_strategy"
            )

            if signals:
                signal = signals[0]
                logger.info(f"🎯 Generated signal: {signal.action} {symbol} (score: {signal.score:.3f})")
                logger.info(f"📝 Rationale: {signal.rationale}")
                return signal
            else:
                logger.debug(f"No signal generated for {symbol}")
                return None

        except Exception as e:
            logger.error(f"❌ Error generating signal for {symbol}: {e}")
            return None

    async def _process_signal(self, signal: Signal):
        """Process a trading signal."""
        try:
            # Validate signal
            if not signal or not hasattr(signal, 'symbol') or not hasattr(signal, 'action'):
                logger.error("❌ Invalid signal object received")
                return

            # Convert action to string if it's an enum
            action_str = signal.action.value if hasattr(signal.action, 'value') else str(signal.action)

            # Publish signal to bus for dashboard
            signal_data = {
                "symbol": signal.symbol,
                "action": action_str,
                "score": getattr(signal, 'score', 0.0),
                "confidence": getattr(signal, 'confidence', 0.0),
                "rationale": getattr(signal, 'rationale', 'No rationale provided'),
                "timestamp": datetime.now().isoformat(),
                "source": "live_smart_strategy",
                "price": getattr(signal, 'price', 0.0)
            }

            # Publish to signals topic
            self.bus.publish("signals.trading", datetime.now().timestamp(), signal_data)
            logger.info(f"📡 Published signal to bus: {action_str} {signal.symbol}")

            if self.trading_enabled and not self.simulation_mode:
                logger.warning(f"🚨 REAL TRADING SIGNAL: {action_str} {signal.symbol}")
                logger.warning("⚠️  Real trading execution would happen here!")
                # Note: Real trading execution would be implemented here
            else:
                logger.info(f"📊 Simulation signal: {action_str} {signal.symbol}")

        except Exception as e:
            logger.error(f"❌ Error processing signal: {e}")
            logger.error(f"Signal object: {signal}")

    async def stop(self):
        """Stop the live strategy."""
        logger.info("🛑 Stopping live smart strategy...")
        self.running = False

        if self.bus:
            self.bus.close()
            logger.info("✅ SQLite bus connection closed")


async def main():
    """Main entry point."""
    logger.info("🎯 Live Smart Strategy Runner")
    logger.info("=" * 50)

    # Create and start strategy
    strategy = LiveSmartStrategy()

    try:
        await strategy.start()
    except KeyboardInterrupt:
        logger.info("📡 Keyboard interrupt received")
    finally:
        await strategy.stop()
        logger.info("🏁 Live smart strategy stopped")


if __name__ == "__main__":
    asyncio.run(main())
