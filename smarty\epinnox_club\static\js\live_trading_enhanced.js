/**
 * Enhanced Live Trading Interface for Money Circle
 * Professional HTX futures trading with advanced order management and risk controls
 */

class EnhancedLiveTradingInterface {
    constructor() {
        this.currentSymbol = 'DOGE/USDT';
        this.currentExchange = 'HTX';
        this.currentLeverage = 10;
        this.currentOrderType = 'market';
        this.currentSide = 'buy';

        // Real-time data
        this.marketData = {};
        this.positions = [];
        this.openOrders = [];
        this.orderBook = { bids: [], asks: [] };
        this.recentTrades = [];

        // WebSocket connection for real-time updates
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        // UI state
        this.isConnected = false;
        this.isTrading = false;

        this.init();
    }

    init() {
        console.log('🚀 Initializing Enhanced Live Trading Interface');

        // Initialize UI components
        this.initializeUI();
        this.setupEventListeners();
        this.connectWebSocket();

        // Start real-time data updates
        this.startDataUpdates();

        console.log('✅ Enhanced Live Trading Interface initialized');
    }

    initializeUI() {
        // Initialize trading tabs
        this.initializeTradingTabs();

        // Initialize leverage selector
        this.initializeLeverageSelector();

        // Initialize order type selector
        this.initializeOrderTypeSelector();

        // Update market info
        this.updateMarketInfo();

        // Load initial data
        this.loadInitialData();
    }

    initializeTradingTabs() {
        const buyTab = document.getElementById('buy-tab');
        const sellTab = document.getElementById('sell-tab');

        if (buyTab && sellTab) {
            buyTab.addEventListener('click', () => this.selectTradingSide('buy'));
            sellTab.addEventListener('click', () => this.selectTradingSide('sell'));

            // Set initial state
            this.selectTradingSide('buy');
        }
    }

    initializeLeverageSelector() {
        const leverageButtons = document.querySelectorAll('.leverage-btn');
        leverageButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const leverage = parseInt(btn.textContent.replace('x', ''));
                this.selectLeverage(leverage);
            });
        });

        // Set initial leverage
        this.selectLeverage(10);
    }

    initializeOrderTypeSelector() {
        const orderTypeSelect = document.getElementById('order-type');
        if (orderTypeSelect) {
            orderTypeSelect.addEventListener('change', (e) => {
                this.currentOrderType = e.target.value;
                this.updateOrderForm();
            });
        }
    }

    setupEventListeners() {
        // Order placement buttons
        const placeOrderBtn = document.getElementById('place-order-btn');
        if (placeOrderBtn) {
            placeOrderBtn.addEventListener('click', () => this.placeOrder());
        }

        // Position management buttons
        const closeAllBtn = document.getElementById('close-all-positions');
        if (closeAllBtn) {
            closeAllBtn.addEventListener('click', () => this.closeAllPositions());
        }

        const cancelAllBtn = document.getElementById('cancel-all-orders');
        if (cancelAllBtn) {
            cancelAllBtn.addEventListener('click', () => this.cancelAllOrders());
        }

        // Form inputs
        const amountInput = document.getElementById('order-amount');
        const priceInput = document.getElementById('order-price');

        if (amountInput) {
            amountInput.addEventListener('input', () => this.updateOrderSummary());
        }

        if (priceInput) {
            priceInput.addEventListener('input', () => this.updateOrderSummary());
        }
    }

    selectTradingSide(side) {
        this.currentSide = side;

        // Update UI
        const buyTab = document.getElementById('buy-tab');
        const sellTab = document.getElementById('sell-tab');
        const orderButton = document.getElementById('place-order-btn');

        if (buyTab && sellTab && orderButton) {
            buyTab.classList.toggle('active', side === 'buy');
            sellTab.classList.toggle('active', side === 'sell');

            orderButton.className = `order-button ${side}`;
            orderButton.textContent = side === 'buy' ? 'BUY / LONG' : 'SELL / SHORT';
        }

        this.updateOrderSummary();
    }

    selectLeverage(leverage) {
        this.currentLeverage = leverage;

        // Update UI
        const leverageButtons = document.querySelectorAll('.leverage-btn');
        leverageButtons.forEach(btn => {
            const btnLeverage = parseInt(btn.textContent.replace('x', ''));
            btn.classList.toggle('active', btnLeverage === leverage);
        });

        this.updateOrderSummary();
    }

    updateOrderForm() {
        const priceGroup = document.getElementById('price-group');
        const stopPriceGroup = document.getElementById('stop-price-group');

        if (priceGroup) {
            priceGroup.style.display = this.currentOrderType === 'limit' ? 'block' : 'none';
        }

        if (stopPriceGroup) {
            stopPriceGroup.style.display = this.currentOrderType === 'stop' ? 'block' : 'none';
        }

        this.updateOrderSummary();
    }

    updateOrderSummary() {
        const amountInput = document.getElementById('order-amount');
        const priceInput = document.getElementById('order-price');

        if (!amountInput) return;

        const amount = parseFloat(amountInput.value) || 0;
        const price = this.currentOrderType === 'market'
            ? this.getMarketPrice()
            : parseFloat(priceInput?.value) || 0;

        const notionalValue = amount * price;
        const margin = notionalValue / this.currentLeverage;

        // Update summary display
        this.updateSummaryField('summary-amount', `${amount.toFixed(0)} DOGE`);
        this.updateSummaryField('summary-price', `$${price.toFixed(6)}`);
        this.updateSummaryField('summary-notional', `$${notionalValue.toFixed(2)}`);
        this.updateSummaryField('summary-margin', `$${margin.toFixed(2)}`);
        this.updateSummaryField('summary-leverage', `${this.currentLeverage}x`);
    }

    updateSummaryField(fieldId, value) {
        const field = document.getElementById(fieldId);
        if (field) {
            field.textContent = value;
        }
    }

    getMarketPrice() {
        if (this.marketData.price) {
            return this.currentSide === 'buy' ? this.marketData.ask : this.marketData.bid;
        }
        return 0;
    }

    async placeOrder() {
        if (this.isTrading) return;

        try {
            this.isTrading = true;
            this.updateOrderButton('Placing Order...', true);

            const orderData = this.getOrderData();

            // Validate order data
            if (!this.validateOrderData(orderData)) {
                this.showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Show confirmation dialog for live trading
            if (!await this.confirmOrder(orderData)) {
                return;
            }

            // Place order based on type
            let response;
            switch (this.currentOrderType) {
                case 'market':
                    response = await this.placeMarketOrder(orderData);
                    break;
                case 'limit':
                    response = await this.placeLimitOrder(orderData);
                    break;
                case 'stop':
                    response = await this.placeStopOrder(orderData);
                    break;
                default:
                    throw new Error('Invalid order type');
            }

            if (response.success) {
                this.showNotification(`Order placed successfully: ${response.message}`, 'success');
                this.clearOrderForm();
                this.refreshData();
            } else {
                this.showNotification(`Order failed: ${response.error}`, 'error');
            }

        } catch (error) {
            console.error('Order placement error:', error);
            this.showNotification(`Order failed: ${error.message}`, 'error');
        } finally {
            this.isTrading = false;
            this.updateOrderButton();
        }
    }

    getOrderData() {
        const amountInput = document.getElementById('order-amount');
        const priceInput = document.getElementById('order-price');
        const stopPriceInput = document.getElementById('stop-price');
        const reduceOnlyCheckbox = document.getElementById('reduce-only');

        return {
            exchange: this.currentExchange,
            symbol: this.currentSymbol,
            side: this.currentSide,
            amount: parseFloat(amountInput?.value) || 0,
            price: parseFloat(priceInput?.value) || 0,
            stop_price: parseFloat(stopPriceInput?.value) || 0,
            leverage: this.currentLeverage,
            reduce_only: reduceOnlyCheckbox?.checked || false,
            type: this.currentOrderType
        };
    }

    validateOrderData(data) {
        if (data.amount <= 0) {
            this.showNotification('Amount must be greater than 0', 'error');
            return false;
        }

        if (this.currentOrderType === 'limit' && data.price <= 0) {
            this.showNotification('Price must be greater than 0 for limit orders', 'error');
            return false;
        }

        if (this.currentOrderType === 'stop' && data.stop_price <= 0) {
            this.showNotification('Stop price must be greater than 0 for stop orders', 'error');
            return false;
        }

        return true;
    }

    async confirmOrder(orderData) {
        const orderType = this.currentOrderType.toUpperCase();
        const side = this.currentSide.toUpperCase();
        const amount = orderData.amount;
        const symbol = this.currentSymbol;
        const leverage = this.currentLeverage;

        let priceText = '';
        if (this.currentOrderType === 'limit') {
            priceText = ` at $${orderData.price.toFixed(6)}`;
        } else if (this.currentOrderType === 'stop') {
            priceText = ` with stop at $${orderData.stop_price.toFixed(6)}`;
        }

        const message = `Confirm ${orderType} order:\n\n` +
                       `${side} ${amount} ${symbol}${priceText}\n` +
                       `Leverage: ${leverage}x\n` +
                       `Exchange: ${this.currentExchange}\n\n` +
                       `⚠️ This is a LIVE trading order with real money!`;

        return confirm(message);
    }

    async placeMarketOrder(orderData) {
        const response = await fetch('/api/trading/place-market-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });

        return await response.json();
    }

    async placeLimitOrder(orderData) {
        const response = await fetch('/api/trading/place-limit-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });

        return await response.json();
    }

    async placeStopOrder(orderData) {
        const response = await fetch('/api/trading/place-stop-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData)
        });

        return await response.json();
    }

    async closeAllPositions() {
        if (!confirm('⚠️ Close ALL positions? This action cannot be undone!')) {
            return;
        }

        try {
            const response = await fetch('/api/trading/close-position', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    exchange: this.currentExchange
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('All positions closed successfully', 'success');
                this.refreshData();
            } else {
                this.showNotification(`Failed to close positions: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Close positions error:', error);
            this.showNotification(`Error closing positions: ${error.message}`, 'error');
        }
    }

    async cancelAllOrders() {
        if (!confirm('Cancel ALL open orders?')) {
            return;
        }

        try {
            const response = await fetch('/api/trading/cancel-all-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    exchange: this.currentExchange
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('All orders cancelled successfully', 'success');
                this.refreshData();
            } else {
                this.showNotification(`Failed to cancel orders: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Cancel orders error:', error);
            this.showNotification(`Error cancelling orders: ${error.message}`, 'error');
        }
    }

    clearOrderForm() {
        const amountInput = document.getElementById('order-amount');
        const priceInput = document.getElementById('order-price');
        const stopPriceInput = document.getElementById('stop-price');

        if (amountInput) amountInput.value = '';
        if (priceInput) priceInput.value = '';
        if (stopPriceInput) stopPriceInput.value = '';

        this.updateOrderSummary();
    }

    updateOrderButton(text = null, disabled = false) {
        const button = document.getElementById('place-order-btn');
        if (button) {
            if (text) {
                button.textContent = text;
            } else {
                button.textContent = this.currentSide === 'buy' ? 'BUY / LONG' : 'SELL / SHORT';
            }
            button.disabled = disabled;
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #00ff88, #00d4aa)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #ff4757, #ff3742)';
                break;
            case 'warning':
                notification.style.background = 'linear-gradient(135deg, #ffa502, #ff6348)';
                break;
            default:
                notification.style.background = 'linear-gradient(135deg, #3742fa, #2f3542)';
        }

        // Add to page
        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    connectWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/trading`;

            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('✅ WebSocket connected for live trading');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.updateConnectionStatus(true);
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('WebSocket message parse error:', error);
                }
            };

            this.ws.onclose = () => {
                console.log('❌ WebSocket disconnected');
                this.isConnected = false;
                this.updateConnectionStatus(false);
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.isConnected = false;
                this.updateConnectionStatus(false);
            };

        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.scheduleReconnect();
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'market_data':
                this.updateMarketData(data.data);
                break;
            case 'position_update':
                this.updatePositions(data.data);
                break;
            case 'order_update':
                this.updateOrders(data.data);
                break;
            case 'orderbook_update':
                this.updateOrderBook(data.data);
                break;
            case 'trade_update':
                this.updateRecentTrades(data.data);
                break;
            default:
                console.log('Unknown WebSocket message type:', data.type);
        }
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

            console.log(`Reconnecting WebSocket in ${delay}ms (attempt ${this.reconnectAttempts})`);

            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            console.error('Max WebSocket reconnection attempts reached');
            this.showNotification('Connection lost. Please refresh the page.', 'error');
        }
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.textContent = connected ? '🟢 Connected' : '🔴 Disconnected';
            statusElement.className = connected ? 'status-connected' : 'status-disconnected';
        }
    }

    updateMarketInfo() {
        const symbolElement = document.getElementById('market-symbol');
        const priceElement = document.getElementById('market-price');
        const changeElement = document.getElementById('market-change');

        if (symbolElement) symbolElement.textContent = this.currentSymbol;

        if (this.marketData.price) {
            if (priceElement) priceElement.textContent = `$${this.marketData.price.toFixed(6)}`;

            if (changeElement && this.marketData.change_24h_percent !== undefined) {
                const change = this.marketData.change_24h_percent;
                changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                changeElement.className = `ticker-change ${change >= 0 ? 'positive' : 'negative'}`;
            }
        }
    }

    async loadInitialData() {
        try {
            // Load positions, orders, and market data
            await Promise.all([
                this.refreshPositions(),
                this.refreshOrders(),
                this.refreshOrderBook(),
                this.refreshRecentTrades()
            ]);

        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    async refreshData() {
        await this.loadInitialData();
    }

    startDataUpdates() {
        // Update market data every 1 second
        setInterval(() => {
            if (this.isConnected) {
                this.updateMarketInfo();
                this.updateOrderSummary();
            }
        }, 1000);

        // Refresh positions and orders every 5 seconds
        setInterval(() => {
            if (this.isConnected) {
                this.refreshPositions();
                this.refreshOrders();
            }
        }, 5000);

        // Refresh order book every 2 seconds
        setInterval(() => {
            if (this.isConnected) {
                this.refreshOrderBook();
            }
        }, 2000);
    }

    async refreshPositions() {
        try {
            const response = await fetch(`/api/trading/positions/${this.currentExchange}`);
            const data = await response.json();

            if (data.success) {
                this.positions = data.positions || [];
                this.updatePositionsDisplay();
            }
        } catch (error) {
            console.error('Error refreshing positions:', error);
        }
    }

    async refreshOrders() {
        try {
            const response = await fetch(`/api/trading/orders/${this.currentExchange}`);
            const data = await response.json();

            if (data.success) {
                this.openOrders = data.orders || [];
                this.updateOrdersDisplay();
            }
        } catch (error) {
            console.error('Error refreshing orders:', error);
        }
    }

    async refreshOrderBook() {
        try {
            const response = await fetch(`/api/trading/orderbook/${this.currentExchange}/${this.currentSymbol.replace('/', '')}`);
            const data = await response.json();

            if (data.success) {
                this.orderBook = data.orderbook || { bids: [], asks: [] };
                this.updateOrderBookDisplay();
            }
        } catch (error) {
            console.error('Error refreshing order book:', error);
        }
    }

    async refreshRecentTrades() {
        try {
            const response = await fetch(`/api/trading/recent-trades/${this.currentExchange}/${this.currentSymbol.replace('/', '')}`);
            const data = await response.json();

            if (data.success) {
                this.recentTrades = data.trades || [];
                this.updateRecentTradesDisplay();
            }
        } catch (error) {
            console.error('Error refreshing recent trades:', error);
        }
    }

    updateMarketData(data) {
        this.marketData = { ...this.marketData, ...data };
        this.updateMarketInfo();
    }

    updatePositions(data) {
        this.positions = data;
        this.updatePositionsDisplay();
    }

    updateOrders(data) {
        this.openOrders = data;
        this.updateOrdersDisplay();
    }

    updateOrderBook(data) {
        this.orderBook = data;
        this.updateOrderBookDisplay();
    }

    updateRecentTrades(data) {
        this.recentTrades = data;
        this.updateRecentTradesDisplay();
    }

    updatePositionsDisplay() {
        const container = document.getElementById('positions-list');
        if (!container) return;

        if (this.positions.length === 0) {
            container.innerHTML = '<div class="no-data">No open positions</div>';
            return;
        }

        container.innerHTML = this.positions.map(position => `
            <div class="position-item">
                <div class="position-header">
                    <span class="position-symbol">${position.symbol}</span>
                    <span class="position-side ${position.side}">${position.side.toUpperCase()}</span>
                </div>
                <div class="position-details">
                    <div class="position-detail">
                        <span class="detail-label">Size:</span>
                        <span class="detail-value">${position.size}</span>
                    </div>
                    <div class="position-detail">
                        <span class="detail-label">Entry:</span>
                        <span class="detail-value">$${position.entry_price?.toFixed(6) || '0.000000'}</span>
                    </div>
                    <div class="position-detail">
                        <span class="detail-label">Mark:</span>
                        <span class="detail-value">$${position.mark_price?.toFixed(6) || '0.000000'}</span>
                    </div>
                    <div class="position-detail">
                        <span class="detail-label">PnL:</span>
                        <span class="detail-value ${position.pnl_usd >= 0 ? 'positive' : 'negative'}">
                            $${position.pnl_usd?.toFixed(2) || '0.00'}
                        </span>
                    </div>
                </div>
                <div class="position-actions">
                    <button class="position-btn" onclick="liveTradingInterface.closePosition('${position.side}')">
                        Close ${position.side}
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateOrdersDisplay() {
        const container = document.getElementById('orders-list');
        if (!container) return;

        if (this.openOrders.length === 0) {
            container.innerHTML = '<div class="no-data">No open orders</div>';
            return;
        }

        container.innerHTML = this.openOrders.map(order => `
            <div class="order-item">
                <div class="order-header">
                    <span class="order-symbol">${order.symbol}</span>
                    <span class="order-side ${order.side}">${order.side.toUpperCase()}</span>
                    <span class="order-type">${order.type.toUpperCase()}</span>
                </div>
                <div class="order-details">
                    <div class="order-detail">
                        <span class="detail-label">Amount:</span>
                        <span class="detail-value">${order.amount}</span>
                    </div>
                    <div class="order-detail">
                        <span class="detail-label">Price:</span>
                        <span class="detail-value">$${order.price?.toFixed(6) || 'Market'}</span>
                    </div>
                    <div class="order-detail">
                        <span class="detail-label">Filled:</span>
                        <span class="detail-value">${order.filled || 0}</span>
                    </div>
                    <div class="order-detail">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">${order.status}</span>
                    </div>
                </div>
                <div class="order-actions">
                    <button class="order-btn cancel" onclick="liveTradingInterface.cancelOrder('${order.id}')">
                        Cancel
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateOrderBookDisplay() {
        const asksContainer = document.getElementById('orderbook-asks');
        const bidsContainer = document.getElementById('orderbook-bids');
        const currentPriceElement = document.getElementById('current-price');

        if (asksContainer && this.orderBook.asks) {
            asksContainer.innerHTML = this.orderBook.asks.slice(0, 10).reverse().map(([price, amount]) => `
                <tr class="ask-row">
                    <td>${parseFloat(price).toFixed(6)}</td>
                    <td>${parseFloat(amount).toFixed(0)}</td>
                </tr>
            `).join('');
        }

        if (bidsContainer && this.orderBook.bids) {
            bidsContainer.innerHTML = this.orderBook.bids.slice(0, 10).map(([price, amount]) => `
                <tr class="bid-row">
                    <td>${parseFloat(price).toFixed(6)}</td>
                    <td>${parseFloat(amount).toFixed(0)}</td>
                </tr>
            `).join('');
        }

        if (currentPriceElement && this.orderBook.bids && this.orderBook.asks) {
            const midPrice = (parseFloat(this.orderBook.bids[0]?.[0] || 0) + parseFloat(this.orderBook.asks[0]?.[0] || 0)) / 2;
            currentPriceElement.textContent = `$${midPrice.toFixed(6)}`;
        }
    }

    updateRecentTradesDisplay() {
        const container = document.getElementById('recent-trades-list');
        if (!container) return;

        if (this.recentTrades.length === 0) {
            container.innerHTML = '<div class="no-data">No recent trades</div>';
            return;
        }

        container.innerHTML = this.recentTrades.slice(0, 20).map(trade => `
            <tr class="${trade.side}-row">
                <td>${parseFloat(trade.price).toFixed(6)}</td>
                <td>${parseFloat(trade.amount).toFixed(0)}</td>
                <td>${new Date(trade.timestamp).toLocaleTimeString()}</td>
            </tr>
        `).join('');
    }

    async closePosition(side) {
        if (!confirm(`Close ${side} position?`)) {
            return;
        }

        try {
            const response = await fetch('/api/trading/close-position', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    exchange: this.currentExchange,
                    position_side: side
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`${side} position closed successfully`, 'success');
                this.refreshData();
            } else {
                this.showNotification(`Failed to close position: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Close position error:', error);
            this.showNotification(`Error closing position: ${error.message}`, 'error');
        }
    }

    async cancelOrder(orderId) {
        if (!confirm('Cancel this order?')) {
            return;
        }

        try {
            const response = await fetch('/api/trading/cancel-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    exchange: this.currentExchange,
                    order_id: orderId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Order cancelled successfully', 'success');
                this.refreshData();
            } else {
                this.showNotification(`Failed to cancel order: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Cancel order error:', error);
            this.showNotification(`Error cancelling order: ${error.message}`, 'error');
        }
    }
}

// Initialize the enhanced live trading interface when the page loads
let liveTradingInterface;

document.addEventListener('DOMContentLoaded', function() {
    liveTradingInterface = new EnhancedLiveTradingInterface();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .no-data {
        text-align: center;
        color: #888;
        padding: 20px;
        font-style: italic;
    }

    .status-connected {
        color: #00ff88;
    }

    .status-disconnected {
        color: #ff4757;
    }
`;
document.head.appendChild(style);
