// Money Circle Club Dashboard JavaScript

// Global variables
let currentUser = null;
let notifications = [];
let activityFeed = [];

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeClubDashboard();
    setupEventListeners();
    loadNotifications();
    startRealTimeUpdates();
});

function initializeClubDashboard() {
    console.log('🚀 Initializing Club Dashboard...');
    
    // Load initial data
    refreshActivityFeed();
    refreshLeaderboard();
    
    // Setup modal event listeners
    setupModalEventListeners();
}

function setupEventListeners() {
    // Strategy proposal form
    const proposeForm = document.getElementById('proposeStrategyForm');
    if (proposeForm) {
        proposeForm.addEventListener('submit', handleStrategyProposal);
    }
    
    // Vote form
    const voteForm = document.getElementById('voteForm');
    if (voteForm) {
        voteForm.addEventListener('submit', handleVote);
    }
    
    // Follow strategy form
    const followForm = document.getElementById('followStrategyForm');
    if (followForm) {
        followForm.addEventListener('submit', handleFollowStrategy);
    }
    
    // Activity feed filters
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterActivityFeed(this.dataset.filter);
        });
    });
}

function setupModalEventListeners() {
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target.id);
        }
    });
    
    // Close modals with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal[style*="block"]');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// Strategy Proposal Functions
function showProposeStrategyModal() {
    document.getElementById('proposeStrategyModal').style.display = 'block';
}

async function handleStrategyProposal(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const proposalData = {
        title: formData.get('title'),
        description: formData.get('description'),
        strategy_type: formData.get('strategy_type'),
        risk_level: formData.get('risk_level'),
        expected_return: parseFloat(formData.get('expected_return')),
        max_drawdown: parseFloat(formData.get('max_drawdown')),
        time_horizon: formData.get('time_horizon'),
        parameters: formData.get('parameters') || '{}'
    };
    
    // Validate parameters JSON
    try {
        JSON.parse(proposalData.parameters);
    } catch (e) {
        showNotification('Invalid JSON in parameters field', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/club/strategy/propose', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(proposalData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Strategy proposal submitted successfully!', 'success');
            closeModal('proposeStrategyModal');
            event.target.reset();
            refreshDashboard();
        } else {
            showNotification(`Failed to submit proposal: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error submitting proposal:', error);
        showNotification('Error submitting proposal', 'error');
    }
}

// Voting Functions
function castVote(strategyId, vote) {
    // Get strategy title for confirmation
    const strategyCard = event.target.closest('.voting-strategy-card');
    const strategyTitle = strategyCard.querySelector('h4').textContent;
    
    // Set modal data
    document.getElementById('voteStrategyId').value = strategyId;
    document.getElementById('voteType').value = vote;
    document.getElementById('voteAction').textContent = vote.toUpperCase();
    document.getElementById('voteStrategyTitle').textContent = strategyTitle;
    
    // Show modal
    document.getElementById('voteModal').style.display = 'block';
}

async function handleVote(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const voteData = {
        strategy_id: parseInt(formData.get('strategy_id')),
        vote: formData.get('vote'),
        reasoning: formData.get('reasoning') || ''
    };
    
    try {
        const response = await fetch('/api/club/strategy/vote', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(voteData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(`Vote cast successfully: ${voteData.vote}`, 'success');
            closeModal('voteModal');
            event.target.reset();
            refreshDashboard();
        } else {
            showNotification(`Failed to cast vote: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error casting vote:', error);
        showNotification('Error casting vote', 'error');
    }
}

// Admin Review Functions
async function reviewStrategy(strategyId, action) {
    const notes = prompt(`Enter review notes for ${action}:`);
    if (notes === null) return; // User cancelled
    
    try {
        const response = await fetch('/api/club/strategy/admin_review', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                strategy_id: strategyId,
                action: action,
                review_notes: notes
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(`Strategy ${action}d successfully`, 'success');
            refreshDashboard();
        } else {
            showNotification(`Failed to ${action} strategy: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error reviewing strategy:', error);
        showNotification('Error reviewing strategy', 'error');
    }
}

// Social Trading Functions
function followStrategy(strategyId) {
    document.getElementById('followStrategyId').value = strategyId;
    document.getElementById('followStrategyModal').style.display = 'block';
}

function showFollowStrategyModal() {
    // Show modal with strategy selection
    document.getElementById('followStrategyModal').style.display = 'block';
}

async function handleFollowStrategy(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const followData = {
        strategy_id: parseInt(formData.get('strategy_id')),
        auto_execute: formData.get('auto_execute') === 'on',
        allocation_percentage: parseFloat(formData.get('allocation_percentage'))
    };
    
    try {
        const response = await fetch('/api/club/social/follow_strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(followData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Strategy followed successfully!', 'success');
            closeModal('followStrategyModal');
            event.target.reset();
            refreshDashboard();
        } else {
            showNotification(`Failed to follow strategy: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error following strategy:', error);
        showNotification('Error following strategy', 'error');
    }
}

// Activity Feed Functions
async function refreshActivityFeed() {
    try {
        const response = await fetch('/api/club/social/activity_feed?limit=20');
        const result = await response.json();
        
        if (result.success) {
            activityFeed = result.activities;
            renderActivityFeed(activityFeed);
        }
    } catch (error) {
        console.error('Error refreshing activity feed:', error);
    }
}

function renderActivityFeed(activities) {
    const activityList = document.querySelector('.activity-list');
    if (!activityList) return;
    
    if (activities.length === 0) {
        activityList.innerHTML = '<div class="empty-state">No recent activity</div>';
        return;
    }
    
    const activitiesHtml = activities.map(activity => `
        <div class="activity-item" data-type="${activity.activity_type}">
            <div class="activity-avatar">
                <span class="avatar-initial">${activity.display_name[0].toUpperCase()}</span>
            </div>
            <div class="activity-content">
                <div class="activity-text">
                    <strong>${activity.display_name}</strong> ${activity.formatted_message}
                </div>
                <div class="activity-time">${formatTimeAgo(activity.timestamp)}</div>
            </div>
            <div class="activity-type-icon">
                ${getActivityIcon(activity.activity_type)}
            </div>
        </div>
    `).join('');
    
    activityList.innerHTML = activitiesHtml;
}

function filterActivityFeed(filter) {
    // Update filter button states
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Filter activities
    let filteredActivities = activityFeed;
    if (filter !== 'all') {
        filteredActivities = activityFeed.filter(activity => 
            activity.activity_type === filter || 
            (filter === 'strategies' && ['strategy_proposal', 'strategy_follow', 'vote'].includes(activity.activity_type))
        );
    }
    
    renderActivityFeed(filteredActivities);
}

// Leaderboard Functions
async function refreshLeaderboard() {
    try {
        const response = await fetch('/api/club/social/leaderboard?metric=reputation&limit=10');
        const result = await response.json();
        
        if (result.success) {
            renderLeaderboard(result.leaderboard);
        }
    } catch (error) {
        console.error('Error refreshing leaderboard:', error);
    }
}

async function updateLeaderboard(metric) {
    try {
        const response = await fetch(`/api/club/social/leaderboard?metric=${metric}&limit=10`);
        const result = await response.json();
        
        if (result.success) {
            renderLeaderboard(result.leaderboard);
        }
    } catch (error) {
        console.error('Error updating leaderboard:', error);
    }
}

function renderLeaderboard(leaderboard) {
    const leaderboardList = document.querySelector('.leaderboard-list');
    if (!leaderboardList) return;
    
    if (leaderboard.length === 0) {
        leaderboardList.innerHTML = '<div class="empty-state">No leaderboard data</div>';
        return;
    }
    
    const leaderboardHtml = leaderboard.map(member => {
        let rankClass = '';
        if (member.rank === 1) rankClass = 'gold';
        else if (member.rank === 2) rankClass = 'silver';
        else if (member.rank === 3) rankClass = 'bronze';
        
        return `
            <div class="leaderboard-item ${rankClass}">
                <div class="rank">#${member.rank}</div>
                <div class="member-info">
                    <div class="member-name">${member.display_name}</div>
                    <div class="member-stats">
                        <span class="reputation">⭐ ${member.reputation_score.toFixed(1)}</span>
                        <span class="trades">📊 ${member.total_trades} trades</span>
                    </div>
                </div>
                <div class="member-score">${member.reputation_score.toFixed(0)}</div>
            </div>
        `;
    }).join('');
    
    leaderboardList.innerHTML = leaderboardHtml;
}

// Notification Functions
async function loadNotifications() {
    try {
        const response = await fetch('/api/club/notifications?limit=5');
        const result = await response.json();
        
        if (result.success) {
            notifications = result.notifications;
            renderNotifications(notifications);
        }
    } catch (error) {
        console.error('Error loading notifications:', error);
    }
}

function renderNotifications(notifications) {
    const notificationList = document.getElementById('notificationList');
    if (!notificationList) return;
    
    if (notifications.length === 0) {
        notificationList.innerHTML = '<div class="empty-state">No new notifications</div>';
        return;
    }
    
    const notificationsHtml = notifications.map(notification => `
        <div class="notification-item ${notification.is_read ? 'read' : 'unread'}">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-content">${notification.content}</div>
            <div class="notification-time">${formatTimeAgo(notification.timestamp)}</div>
        </div>
    `).join('');
    
    notificationList.innerHTML = notificationsHtml;
}

// Utility Functions
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function showMemberDirectoryModal() {
    // Redirect to member directory page
    window.location.href = '/club/members';
}

function refreshDashboard() {
    // Refresh all dashboard components
    refreshActivityFeed();
    refreshLeaderboard();
    loadNotifications();
    
    // Reload the page to get updated strategy data
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

function startRealTimeUpdates() {
    // Poll for updates every 30 seconds
    setInterval(() => {
        refreshActivityFeed();
        loadNotifications();
    }, 30000);
}

function formatTimeAgo(timestamp) {
    try {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (days > 0) return `${days}d ago`;
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;
        return 'Just now';
    } catch (e) {
        return 'Unknown';
    }
}

function getActivityIcon(activityType) {
    const icons = {
        'strategy_proposal': '📝',
        'vote': '🗳️',
        'strategy_follow': '👥',
        'trade': '💹',
        'comment': '💬',
        'admin_review': '⚖️'
    };
    return icons[activityType] || '📊';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    
    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #f44336, #da190b)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
