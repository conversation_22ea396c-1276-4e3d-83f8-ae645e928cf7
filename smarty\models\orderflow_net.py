"""
OrderFlow neural network model for the smart-trader system.
"""

import logging
import os
from typing import Dict, Any, List, Optional, Tuple, Union
import numpy as np

logger = logging.getLogger(__name__)

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    logger.warning("PyTorch not available. OrderFlowNet will run in dummy mode.")
    TORCH_AVAILABLE = False


class OrderFlowNet:
    """
    Neural network model for order flow analysis.
    
    This model takes order book data and recent trades to predict
    short-term price movements.
    """
    
    def __init__(
        self,
        model_path: str = "models/orderflow_net.pt",
        device: str = "cpu",
        feature_names: List[str] = None,
        dummy_mode: bool = False
    ):
        """
        Initialize the OrderFlow model.
        
        Args:
            model_path: Path to the PyTorch model file
            device: Device to run inference on ('cpu' or 'cuda')
            feature_names: Names of input features in order
            dummy_mode: Whether to run in dummy mode (for testing)
        """
        self.model_path = model_path
        self.device = device
        self.dummy_mode = dummy_mode or not TORCH_AVAILABLE
        
        # Default feature names if not provided
        self.feature_names = feature_names or [
            "bid_ask_spread",
            "mid_price",
            "bid_volume_1",
            "ask_volume_1",
            "bid_volume_2",
            "ask_volume_2",
            "bid_volume_3",
            "ask_volume_3",
            "trade_flow_1min",
            "trade_flow_5min",
            "price_change_1min",
            "volume_change_1min"
        ]
        
        # Load model if not in dummy mode
        self.model = None
        if not self.dummy_mode:
            self._load_model()
        
        # Feature normalization parameters
        self.feature_means = np.zeros(len(self.feature_names))
        self.feature_stds = np.ones(len(self.feature_names))
        
        # Prediction history
        self.prediction_history = []
    
    def _load_model(self) -> None:
        """
        Load the PyTorch model.
        """
        try:
            if not os.path.exists(self.model_path):
                logger.warning(f"Model file not found: {self.model_path}")
                self.dummy_mode = True
                return
            
            self.model = torch.load(self.model_path, map_location=self.device)
            self.model.eval()
            logger.info(f"Loaded OrderFlowNet model from {self.model_path}")
            
            # Load normalization parameters if available
            norm_path = self.model_path.replace(".pt", "_norm.npz")
            if os.path.exists(norm_path):
                norm_data = np.load(norm_path)
                self.feature_means = norm_data["means"]
                self.feature_stds = norm_data["stds"]
                logger.info("Loaded feature normalization parameters")
        
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            self.dummy_mode = True
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """
        Normalize input features.
        
        Args:
            features: Raw input features
            
        Returns:
            Normalized features
        """
        return (features - self.feature_means) / self.feature_stds
    
    def _prepare_input(self, features: Dict[str, Any]) -> Optional[torch.Tensor]:
        """
        Prepare input tensor from features dictionary.
        
        Args:
            features: Dictionary of input features
            
        Returns:
            Input tensor or None if features are missing
        """
        if self.dummy_mode:
            return None
        
        # Extract features in the correct order
        feature_values = []
        for name in self.feature_names:
            if name not in features:
                logger.warning(f"Missing feature: {name}")
                return None
            feature_values.append(features[name])
        
        # Convert to numpy array and normalize
        feature_array = np.array(feature_values, dtype=np.float32)
        normalized_features = self._normalize_features(feature_array)
        
        # Convert to PyTorch tensor
        input_tensor = torch.tensor(normalized_features, dtype=torch.float32).unsqueeze(0)
        return input_tensor.to(self.device)
    
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.
        
        Args:
            features: Dictionary of input features
            
        Returns:
            Dictionary of prediction results including:
                - 'delta_price_60s': Predicted price change in 60 seconds
                - 'confidence': Prediction confidence (0-1)
                - 'direction': 'up', 'down', or 'neutral'
        """
        # Run in dummy mode if needed
        if self.dummy_mode:
            return self._dummy_predict(features)
        
        # Prepare input tensor
        input_tensor = self._prepare_input(features)
        if input_tensor is None:
            return self._dummy_predict(features)
        
        # Run inference
        try:
            with torch.no_grad():
                output = self.model(input_tensor)
                
                # Extract predictions
                delta_price = float(output[0][0].item())
                confidence = float(torch.sigmoid(output[0][1]).item())
                
                # Determine direction
                if delta_price > 0.001:
                    direction = "up"
                elif delta_price < -0.001:
                    direction = "down"
                else:
                    direction = "neutral"
                
                # Store prediction in history
                self.prediction_history.append(delta_price)
                if len(self.prediction_history) > 100:
                    self.prediction_history.pop(0)
                
                return {
                    'delta_price_60s': delta_price,
                    'confidence': confidence,
                    'direction': direction
                }
        
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return self._dummy_predict(features)
    
    def _dummy_predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a dummy prediction when the model is not available.
        
        Args:
            features: Dictionary of input features
            
        Returns:
            Dictionary of dummy prediction results
        """
        # Use simple heuristics based on available features
        bid_ask_spread = features.get('bid_ask_spread', 0.0)
        mid_price = features.get('mid_price', 0.0)
        
        bid_volume = features.get('bid_volume_1', 0.0)
        ask_volume = features.get('ask_volume_1', 0.0)
        
        # Simple imbalance-based prediction
        volume_imbalance = 0.0
        if bid_volume + ask_volume > 0:
            volume_imbalance = (bid_volume - ask_volume) / (bid_volume + ask_volume)
        
        # Scale to a reasonable delta price (0.1% of price * imbalance)
        delta_price = mid_price * 0.001 * volume_imbalance
        
        # Add some noise
        delta_price += np.random.normal(0, 0.0001 * mid_price)
        
        # Determine direction
        if delta_price > 0.001:
            direction = "up"
        elif delta_price < -0.001:
            direction = "down"
        else:
            direction = "neutral"
        
        return {
            'delta_price_60s': float(delta_price),
            'confidence': 0.5,  # Medium confidence for dummy predictions
            'direction': direction
        }


class OrderFlowNetModel(nn.Module):
    """
    PyTorch model definition for OrderFlowNet.
    
    This is included here for reference and to allow creating new models.
    """
    
    def __init__(self, input_size: int = 12, hidden_size: int = 64):
        """
        Initialize the model.
        
        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
        """
        super(OrderFlowNetModel, self).__init__()
        
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, 2)  # Output: [delta_price, confidence]
        
        self.dropout = nn.Dropout(0.2)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Output tensor
        """
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        return x
