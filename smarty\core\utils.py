"""
Utility functions for the smart-trader system.

This module provides backward compatibility for the original utils.py interface
while delegating to the new modular utility structure in core.utils package.

For new code, consider importing directly from core.utils submodules:
- core.utils.time_utils
- core.utils.logging_utils
- core.utils.auth_utils
- core.utils.async_utils
- core.utils.math_utils
"""

import warnings

# Import all functions from the new modular structure for backward compatibility
from core.utils.time_utils import *
from core.utils.logging_utils import *
from core.utils.auth_utils import *
from core.utils.async_utils import *
from core.utils.math_utils import *

# Issue deprecation warning for direct imports from this module
warnings.warn(
    "Importing from core.utils is deprecated. "
    "Please import from core.utils submodules instead: "
    "core.utils.time_utils, core.utils.math_utils, etc.",
    DeprecationWarning,
    stacklevel=2
)

# Export all functions for backward compatibility
__all__ = [
    # Time utilities
    "utc_timestamp",
    "iso_timestamp",
    "timestamp_to_datetime",
    "datetime_to_timestamp",
    "format_duration",

    # Logging utilities
    "setup_logging",
    "get_logger",
    "log_function_call",
    "log_performance_metrics",
    "configure_third_party_loggers",
    "add_context_to_logger",

    # Authentication utilities
    "generate_signature",
    "generate_signature_base64",
    "create_api_signature",
    "validate_signature",
    "generate_nonce",
    "hash_password",
    "verify_password",
    "create_jwt_payload",

    # Async utilities
    "retry_async",
    "retry_async_decorator",
    "timer",
    "timeout_after",
    "gather_with_concurrency",
    "run_periodic",
    "AsyncContextManager",
    "TaskManager",

    # Math utilities
    "calculate_vwap",
    "calculate_rolling_vwap",
    "calculate_rsi",
    "calculate_rolling_rsi",
    "calculate_imbalance",
    "normalize_array",
    "format_price",
    "round_quantity",
    "calculate_percentage_change",
    "calculate_sharpe_ratio",
    "calculate_order_flow_imbalance",
    "validate_arrays",
]
