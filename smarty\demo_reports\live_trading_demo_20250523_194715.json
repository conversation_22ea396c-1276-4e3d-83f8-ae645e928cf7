{
  "demo_info": {
    "start_time": "2025-05-23T19:46:11.684204",
    "end_time": "2025-05-23T19:47:15.050052",
    "duration_seconds": 63.365848,
    "duration_minutes": 1.0560974666666667
  },
  "demo_statistics": {
    "signals_generated": 18,
    "models_executed": 30,
    "predictions_made": 30,
    "alerts_triggered": 0,
    "dashboard_updates": 6,
    "performance_snapshots": [
      {
        "timestamp": "2025-05-23T19:46:15.051628",
        "total_predictions": 5,
        "total_signals": 3,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      },
      {
        "timestamp": "2025-05-23T19:46:25.063345",
        "total_predictions": 10,
        "total_signals": 6,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      },
      {
        "timestamp": "2025-05-23T19:46:35.065700",
        "total_predictions": 15,
        "total_signals": 9,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      },
      {
        "timestamp": "2025-05-23T19:46:45.052646",
        "total_predictions": 20,
        "total_signals": 12,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      },
      {
        "timestamp": "2025-05-23T19:46:55.053011",
        "total_predictions": 25,
        "total_signals": 15,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      },
      {
        "timestamp": "2025-05-23T19:47:05.052112",
        "total_predictions": 30,
        "total_signals": 18,
        "active_models": 5,
        "avg_latency": 90.0,
        "active_alerts": 0
      }
    ]
  },
  "performance_summary": {
    "timestamp": "2025-05-23T19:47:15.050052",
    "uptime_hours": 0.01666789888888889,
    "models": {
      "total_models": 5,
      "active_models": 5,
      "total_predictions": 30,
      "avg_accuracy": 0.0,
      "avg_latency_ms": 90.0
    },
    "signals": {
      "total_signal_sources": 3,
      "total_signals": 18,
      "avg_signal_accuracy": 0.0
    },
    "system_health": {
      "uptime_seconds": 30.006113,
      "total_memory_mb": 0.0,
      "cpu_usage_percent": 0.0,
      "active_models": 5,
      "failed_models": 0,
      "message_queue_size": 0,
      "last_health_check": 