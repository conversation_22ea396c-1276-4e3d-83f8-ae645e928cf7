<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section h2 {
            color: #ffff00;
            margin-top: 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.pass { background: #004400; color: #00ff00; }
        .status.fail { background: #440000; color: #ff0000; }
        .status.pending { background: #444400; color: #ffff00; }
        .data-display {
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0088ff;
        }
        .websocket-log {
            background: #1a1a2e;
            border: 1px solid #16213e;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Smart Trader Frontend Test Suite</h1>
        
        <div class="test-section">
            <h2>📡 API Endpoint Tests</h2>
            <button onclick="testAllAPIs()">Test All APIs</button>
            <button onclick="clearResults()">Clear Results</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🔌 WebSocket Connection Test</h2>
            <button onclick="testWebSocket()">Test WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <div id="websocket-status" class="status pending">Not tested</div>
            <div id="websocket-log" class="websocket-log"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Live Data Display</h2>
            <button onclick="fetchLiveData()">Fetch Live Data</button>
            <div id="live-data-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 Signal Detection Test</h2>
            <button onclick="testSignalDetection()">Check for Signals</button>
            <div id="signal-results"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let wsMessageCount = 0;
        
        const API_ENDPOINTS = [
            '/api/market',
            '/api/signals',
            '/api/trades',
            '/api/stats',
            '/api/orderbook',
            '/api/recent-trades',
            '/api/ai-analysis',
            '/api/market-sentiment',
            '/api/debug'
        ];
        
        function log(message, elementId = null) {
            console.log(message);
            if (elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML += `${new Date().toLocaleTimeString()}: ${message}\n`;
                    element.scrollTop = element.scrollHeight;
                }
            }
        }
        
        async function testAPI(endpoint) {
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                return {
                    endpoint,
                    status: response.status,
                    success: response.ok,
                    dataKeys: Object.keys(data),
                    dataSize: JSON.stringify(data).length,
                    sampleData: JSON.stringify(data, null, 2).substring(0, 200) + '...'
                };
            } catch (error) {
                return {
                    endpoint,
                    success: false,
                    error: error.message
                };
            }
        }
        
        async function testAllAPIs() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="status pending">Testing APIs...</div>';
            
            const results = [];
            for (const endpoint of API_ENDPOINTS) {
                const result = await testAPI(endpoint);
                results.push(result);
            }
            
            let html = '';
            let passCount = 0;
            
            results.forEach(result => {
                const statusClass = result.success ? 'pass' : 'fail';
                if (result.success) passCount++;
                
                html += `
                    <div class="status ${statusClass}">
                        ${result.success ? '✅' : '❌'} ${result.endpoint} 
                        ${result.success ? `(${result.dataKeys.length} keys, ${result.dataSize} bytes)` : `- ${result.error}`}
                    </div>
                `;
                
                if (result.success && result.sampleData) {
                    html += `<div class="data-display">${result.sampleData}</div>`;
                }
            });
            
            html = `<div class="status ${passCount === results.length ? 'pass' : 'fail'}">
                        API Tests: ${passCount}/${results.length} passed
                    </div>` + html;
            
            resultsDiv.innerHTML = html;
        }
        
        function testWebSocket() {
            const statusDiv = document.getElementById('websocket-status');
            const logDiv = document.getElementById('websocket-log');
            
            if (ws) {
                ws.close();
            }
            
            statusDiv.className = 'status pending';
            statusDiv.textContent = 'Connecting...';
            logDiv.innerHTML = '';
            wsMessageCount = 0;
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            log(`Connecting to: ${wsUrl}`, 'websocket-log');
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                statusDiv.className = 'status pass';
                statusDiv.textContent = '✅ Connected - Waiting for data...';
                log('✅ WebSocket connected successfully', 'websocket-log');
            };
            
            ws.onmessage = function(event) {
                wsMessageCount++;
                statusDiv.textContent = `✅ Connected - Received ${wsMessageCount} messages`;
                
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 Message ${wsMessageCount}: ${Object.keys(data).join(', ')}`, 'websocket-log');
                    
                    // Log specific data types
                    if (data.signals && data.signals.length > 0) {
                        log(`🎯 SIGNALS DETECTED: ${data.signals.length} signals`, 'websocket-log');
                        data.signals.forEach((signal, i) => {
                            log(`   Signal ${i+1}: ${signal.action} ${signal.symbol} @ ${signal.price}`, 'websocket-log');
                        });
                    }
                    
                    if (data.market) {
                        log(`📊 Market: ${data.market.symbol} = ${data.market.price}`, 'websocket-log');
                    }
                    
                    if (data.debug) {
                        log(`🔍 Debug: ${data.debug.system_status}, ${data.debug.recent_market_data} recent msgs`, 'websocket-log');
                    }
                    
                } catch (error) {
                    log(`❌ Error parsing message: ${error.message}`, 'websocket-log');
                }
            };
            
            ws.onclose = function() {
                statusDiv.className = 'status fail';
                statusDiv.textContent = '❌ Disconnected';
                log('❌ WebSocket disconnected', 'websocket-log');
            };
            
            ws.onerror = function(error) {
                statusDiv.className = 'status fail';
                statusDiv.textContent = '❌ Connection error';
                log(`❌ WebSocket error: ${error}`, 'websocket-log');
            };
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        async function fetchLiveData() {
            const resultsDiv = document.getElementById('live-data-results');
            resultsDiv.innerHTML = '<div class="status pending">Fetching live data...</div>';
            
            try {
                // Fetch multiple endpoints
                const [market, signals, debug] = await Promise.all([
                    fetch('/api/market').then(r => r.json()),
                    fetch('/api/signals').then(r => r.json()),
                    fetch('/api/debug').then(r => r.json())
                ]);
                
                let html = '<div class="status pass">✅ Live data fetched successfully</div>';
                
                html += `<div class="data-display">
<strong>Market Data:</strong>
${JSON.stringify(market, null, 2)}

<strong>Signals (${signals.length}):</strong>
${JSON.stringify(signals, null, 2)}

<strong>Debug Info:</strong>
${JSON.stringify(debug, null, 2)}
                </div>`;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status fail">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testSignalDetection() {
            const resultsDiv = document.getElementById('signal-results');
            resultsDiv.innerHTML = '<div class="status pending">Checking for signals...</div>';
            
            try {
                const [signals, debug] = await Promise.all([
                    fetch('/api/signals').then(r => r.json()),
                    fetch('/api/debug').then(r => r.json())
                ]);
                
                let html = '';
                
                if (signals.length > 0) {
                    html += `<div class="status pass">✅ Found ${signals.length} signals</div>`;
                    signals.forEach((signal, i) => {
                        html += `<div class="data-display">
Signal ${i+1}: ${signal.action} ${signal.symbol}
Price: ${signal.price}
Score: ${signal.score}
Time: ${signal.timestamp}
Rationale: ${signal.rationale}
                        </div>`;
                    });
                } else {
                    html += '<div class="status fail">❌ No signals found</div>';
                }
                
                // Add debug info
                html += `<div class="data-display">
<strong>Debug Information:</strong>
System Status: ${debug.system_status || 'Unknown'}
Recent Market Data: ${debug.recent_market_data || 0} messages
Total Signals in DB: ${debug.signal_count || 0}
Latest Signal: ${debug.latest_signal ? 
    `${debug.latest_signal.timestamp} - ${debug.latest_signal.data.action}` : 'None'}
                </div>`;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status fail">❌ Error: ${error.message}</div>`;
            }
        }
        
        function clearResults() {
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('live-data-results').innerHTML = '';
            document.getElementById('signal-results').innerHTML = '';
        }
        
        // Auto-start WebSocket test
        window.onload = function() {
            log('🧪 Frontend test page loaded');
            // Automatically test WebSocket connection
            setTimeout(testWebSocket, 1000);
        };
    </script>
</body>
</html>
