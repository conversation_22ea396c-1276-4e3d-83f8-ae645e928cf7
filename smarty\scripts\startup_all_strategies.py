#!/usr/bin/env python3
"""
Startup All Strategies Script
Automatically starts all strategies in background upon system startup
"""

import asyncio
import subprocess
import time
import signal
import sys
from pathlib import Path
import colorama
from colorama import Fore, Style

colorama.init(autoreset=True)

class StrategyStartupManager:
    """Manage startup of all trading strategies."""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
        # Startup sequence with dependencies
        self.startup_sequence = [
            {
                'name': 'data_producer',
                'command': 'python feeds/htx_data_producer.py',
                'description': 'HTX Data Producer',
                'startup_delay': 3,
                'required': True,
                'log_file': 'logs/data_producer_startup.log'
            },
            {
                'name': 'dashboard',
                'command': 'python live_dashboard.py',
                'description': 'Live Dashboard',
                'startup_delay': 2,
                'required': True,
                'log_file': 'logs/dashboard_startup.log'
            },
            {
                'name': 'smart_model_integrated',
                'command': 'python orchestrator.py --debug',
                'description': 'Smart Model Integrated Strategy',
                'startup_delay': 5,
                'required': False,
                'log_file': 'logs/smart_model_startup.log'
            },
            {
                'name': 'smart_strategy_only',
                'command': 'python run_smart_strategy_live.py',
                'description': 'Smart Strategy Only',
                'startup_delay': 3,
                'required': False,
                'log_file': 'logs/smart_strategy_startup.log'
            },
            {
                'name': 'order_flow',
                'command': 'python live_dataframe_strategy_runner.py',
                'description': 'Order Flow Strategy',
                'startup_delay': 3,
                'required': False,
                'log_file': 'logs/order_flow_startup.log'
            }
        ]
    
    def print_banner(self):
        """Print startup banner."""
        banner = f"""
{Fore.CYAN + Style.BRIGHT}
╔══════════════════════════════════════════════════════════════╗
║                🚀 STRATEGY STARTUP MANAGER                  ║
║              Auto-Start All Trading Strategies              ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
{Fore.GREEN}🎯 Starting Components:{Style.RESET_ALL}
  • HTX Data Producer (Required)
  • Live Dashboard (Required)
  • Smart Model Integrated Strategy
  • Smart Strategy Only
  • Order Flow Strategy

{Fore.YELLOW}📊 All strategies will run in background mode{Style.RESET_ALL}
{Fore.WHITE}{'='*60}{Style.RESET_ALL}
"""
        print(banner)
    
    def start_component(self, component: dict) -> bool:
        """Start a single component."""
        try:
            print(f"{Fore.YELLOW}🚀 Starting {component['description']}...{Style.RESET_ALL}")
            
            # Ensure logs directory exists
            Path('logs').mkdir(exist_ok=True)
            
            # Open log file
            log_file = open(component['log_file'], 'w')
            
            # Start process
            process = subprocess.Popen(
                component['command'].split(),
                cwd=Path.cwd(),
                stdout=log_file,
                stderr=subprocess.STDOUT
            )
            
            # Wait for startup
            time.sleep(component.get('startup_delay', 2))
            
            if process.poll() is None:
                self.processes[component['name']] = {
                    'process': process,
                    'log_file': log_file,
                    'log_path': component['log_file'],
                    'description': component['description'],
                    'required': component.get('required', False)
                }
                print(f"{Fore.GREEN}✅ {component['description']} started (PID: {process.pid}){Style.RESET_ALL}")
                print(f"{Fore.CYAN}📝 Logs: {component['log_file']}{Style.RESET_ALL}")
                return True
            else:
                log_file.close()
                print(f"{Fore.RED}❌ {component['description']} failed to start{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start {component['description']}: {e}{Style.RESET_ALL}")
            return False
    
    async def start_all_strategies(self):
        """Start all strategies in sequence."""
        self.print_banner()
        
        failed_required = []
        failed_optional = []
        
        for component in self.startup_sequence:
            success = self.start_component(component)
            
            if not success:
                if component.get('required', False):
                    failed_required.append(component['name'])
                else:
                    failed_optional.append(component['name'])
            
            # Brief pause between starts
            await asyncio.sleep(1)
        
        # Report results
        self.print_startup_summary(failed_required, failed_optional)
        
        # Start monitoring
        if not failed_required:
            await self.monitor_strategies()
        else:
            print(f"{Fore.RED}❌ Critical components failed. Shutting down.{Style.RESET_ALL}")
            await self.shutdown_all()
    
    def print_startup_summary(self, failed_required: list, failed_optional: list):
        """Print startup summary."""
        print(f"\n{Fore.CYAN + Style.BRIGHT}📊 STARTUP SUMMARY{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'='*50}{Style.RESET_ALL}")
        
        running_count = len(self.processes)
        total_count = len(self.startup_sequence)
        
        print(f"Total Components: {total_count}")
        print(f"Successfully Started: {Fore.GREEN}{running_count}{Style.RESET_ALL}")
        print(f"Failed: {Fore.RED}{total_count - running_count}{Style.RESET_ALL}")
        
        if failed_required:
            print(f"\n{Fore.RED}❌ Failed Required Components:{Style.RESET_ALL}")
            for name in failed_required:
                print(f"  • {name}")
        
        if failed_optional:
            print(f"\n{Fore.YELLOW}⚠️ Failed Optional Components:{Style.RESET_ALL}")
            for name in failed_optional:
                print(f"  • {name}")
        
        if self.processes:
            print(f"\n{Fore.GREEN}✅ Running Components:{Style.RESET_ALL}")
            for name, info in self.processes.items():
                pid = info['process'].pid
                print(f"  • {info['description']} (PID: {pid})")
        
        if not failed_required:
            print(f"\n{Fore.GREEN}🌐 Dashboard URL: http://localhost:8082{Style.RESET_ALL}")
            print(f"{Fore.GREEN}🔐 Login: epinnox / securepass123{Style.RESET_ALL}")
    
    async def monitor_strategies(self):
        """Monitor running strategies."""
        print(f"\n{Fore.CYAN}📡 Starting strategy monitoring...{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Press Ctrl+C to shutdown all strategies{Style.RESET_ALL}")
        
        try:
            while self.running:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Check for failed processes
                failed_processes = []
                for name, info in list(self.processes.items()):
                    if info['process'].poll() is not None:
                        failed_processes.append(name)
                        info['log_file'].close()
                        del self.processes[name]
                
                if failed_processes:
                    for name in failed_processes:
                        print(f"{Fore.RED}⚠️ Component {name} has stopped unexpectedly{Style.RESET_ALL}")
                
        except asyncio.CancelledError:
            pass
    
    async def shutdown_all(self):
        """Shutdown all running strategies."""
        print(f"\n{Fore.YELLOW}🛑 Shutting down all strategies...{Style.RESET_ALL}")
        self.running = False
        
        for name, info in self.processes.items():
            try:
                process = info['process']
                log_file = info['log_file']
                
                print(f"{Fore.YELLOW}🛑 Stopping {name}...{Style.RESET_ALL}")
                
                # Terminate process
                process.terminate()
                
                try:
                    process.wait(timeout=10)
                    print(f"{Fore.GREEN}✅ {name} stopped gracefully{Style.RESET_ALL}")
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                    print(f"{Fore.YELLOW}⚠️ {name} force killed{Style.RESET_ALL}")
                
                # Close log file
                log_file.close()
                
            except Exception as e:
                print(f"{Fore.RED}❌ Error stopping {name}: {e}{Style.RESET_ALL}")
        
        print(f"{Fore.GREEN}✅ All strategies stopped{Style.RESET_ALL}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n{Fore.YELLOW}📡 Received signal {signum}. Initiating shutdown...{Style.RESET_ALL}")
        self.running = False

async def main():
    """Main entry point."""
    manager = StrategyStartupManager()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        await manager.start_all_strategies()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Keyboard interrupt received{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")
    finally:
        await manager.shutdown_all()

if __name__ == "__main__":
    asyncio.run(main())
