#!/usr/bin/env python3
"""
Enhanced Backtest Runner for Smart-Trader System

This script provides comprehensive backtesting capabilities including:
- Multiple strategy testing
- Parameter optimization
- Walk-forward analysis
- Performance comparison
- Advanced analytics
"""

import os
import json
import logging
import asyncio
import argparse
import yaml
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple

from backtester.backtester import Backtester
from backtester.visualizer import BacktestVisualizer
from backtester.optimizer import StrategyOptimizer
from backtester.analytics import EnhancedAnalytics
from backtester.strategies import (
    simple_moving_average_crossover,
    bollinger_bands_strategy,
    rsi_strategy,
    enhanced_multi_signal_strategy,
    model_ensemble_strategy,
    smart_model_integrated_strategy
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class EnhancedBacktestRunner:
    """Enhanced backtest runner with advanced analytics and optimization."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the enhanced backtest runner."""
        self.config = self._load_config(config_path)
        self.results = {}
        self.analytics = EnhancedAnalytics()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}

    async def run_comprehensive_backtest(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        strategies: List[str] = None,
        optimize: bool = False,
        walk_forward: bool = False,
        output_dir: str = "results/enhanced_backtest"
    ) -> Dict[str, Any]:
        """
        Run comprehensive backtesting with multiple strategies and analysis.

        Args:
            symbols: List of symbols to test
            start_date: Start date for backtesting (YYYY-MM-DD)
            end_date: End date for backtesting (YYYY-MM-DD)
            strategies: List of strategy names to test
            optimize: Whether to run parameter optimization
            walk_forward: Whether to run walk-forward analysis
            output_dir: Output directory for results

        Returns:
            Dictionary containing all results
        """
        logger.info("🚀 Starting comprehensive backtesting...")

        # Default strategies if none specified
        if strategies is None:
            strategies = ['smart', 'ensemble', 'multi', 'sma', 'rsi', 'bollinger']

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Strategy mapping
        strategy_map = {
            'sma': simple_moving_average_crossover,
            'bollinger': bollinger_bands_strategy,
            'rsi': rsi_strategy,
            'multi': enhanced_multi_signal_strategy,
            'ensemble': model_ensemble_strategy,
            'smart': smart_model_integrated_strategy
        }

        results = {
            'strategy_results': {},
            'comparison': {},
            'optimization': {},
            'walk_forward': {},
            'analytics': {}
        }

        # Test each strategy
        for strategy_name in strategies:
            if strategy_name not in strategy_map:
                logger.warning(f"Unknown strategy: {strategy_name}")
                continue

            logger.info(f"📊 Testing strategy: {strategy_name}")

            # Run basic backtest
            strategy_results = await self._run_strategy_backtest(
                strategy_map[strategy_name],
                strategy_name,
                symbols,
                start_date,
                end_date,
                output_dir
            )

            if strategy_results:
                results['strategy_results'][strategy_name] = strategy_results

                # Run optimization if requested
                if optimize and strategy_name in ['smart', 'ensemble', 'multi']:
                    logger.info(f"🔧 Optimizing strategy: {strategy_name}")
                    opt_results = await self._run_optimization(
                        strategy_map[strategy_name],
                        strategy_name,
                        symbols,
                        start_date,
                        end_date,
                        output_dir
                    )
                    if opt_results:
                        results['optimization'][strategy_name] = opt_results

                # Run walk-forward analysis if requested
                if walk_forward and strategy_name in ['smart', 'ensemble']:
                    logger.info(f"📈 Running walk-forward analysis: {strategy_name}")
                    wf_results = await self._run_walk_forward_analysis(
                        strategy_map[strategy_name],
                        strategy_name,
                        symbols,
                        start_date,
                        end_date,
                        output_dir
                    )
                    if wf_results:
                        results['walk_forward'][strategy_name] = wf_results

        # Generate comparison analysis
        if len(results['strategy_results']) > 1:
            logger.info("📊 Generating strategy comparison...")
            results['comparison'] = self._generate_strategy_comparison(
                results['strategy_results']
            )

        # Generate advanced analytics
        logger.info("📈 Generating advanced analytics...")
        results['analytics'] = await self._generate_advanced_analytics(
            results['strategy_results'],
            symbols,
            start_date,
            end_date
        )

        # Save comprehensive results
        self._save_comprehensive_results(results, output_dir)

        # Generate visualizations
        await self._generate_comprehensive_visualizations(results, output_dir)

        logger.info("✅ Comprehensive backtesting completed!")
        return results

    async def _run_strategy_backtest(
        self,
        strategy_func,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run backtest for a single strategy."""
        try:
            # Create backtester
            backtester = Backtester(
                config=self.config,
                output_dir=os.path.join(output_dir, strategy_name)
            )

            # Initialize backtester
            await backtester.initialize()

            # Load data
            await backtester.load_data(
                symbols=symbols,
                start_date=start_date,
                end_date=end_date
            )

            # Create signal generator
            async def signal_generator(timestamp, symbols):
                return await strategy_func(timestamp, symbols)

            # Run backtest
            success = await backtester.run_backtest(signal_generator=signal_generator)

            if success:
                return {
                    'metrics': backtester.metrics,
                    'equity_curve': backtester.equity_curve,
                    'trades': backtester.trades,
                    'signals': backtester.signals
                }
            else:
                logger.error(f"Backtest failed for strategy: {strategy_name}")
                return None

        except Exception as e:
            logger.error(f"Error running backtest for {strategy_name}: {e}")
            return None

    async def _run_optimization(
        self,
        strategy_func,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run parameter optimization for a strategy."""
        try:
            # Define parameter ranges based on strategy
            param_ranges = self._get_optimization_parameters(strategy_name)

            if not param_ranges:
                logger.warning(f"No optimization parameters defined for {strategy_name}")
                return None

            # Create backtester for optimization
            backtester = Backtester(
                config=self.config,
                output_dir=os.path.join(output_dir, f"{strategy_name}_optimization")
            )

            await backtester.initialize()
            await backtester.load_data(symbols, start_date, end_date)

            # Create optimizer
            optimizer = StrategyOptimizer(
                backtester=backtester,
                strategy_func=strategy_func,
                param_ranges=param_ranges,
                optimization_target='sharpe_ratio',
                output_dir=os.path.join(output_dir, f"{strategy_name}_optimization")
            )

            # Run optimization (use grid search for smaller parameter spaces)
            if self._get_parameter_space_size(param_ranges) <= 100:
                results = await optimizer.grid_search(verbose=True)
            else:
                results = await optimizer.genetic_algorithm(
                    population_size=20,
                    generations=10,
                    verbose=True
                )

            return results

        except Exception as e:
            logger.error(f"Error running optimization for {strategy_name}: {e}")
            return None

    def _get_optimization_parameters(self, strategy_name: str) -> Dict[str, List]:
        """Get optimization parameter ranges for different strategies."""
        param_ranges = {
            'smart': {
                'technical_weight': [0.2, 0.3, 0.4, 0.5],
                'vwap_weight': [0.1, 0.2, 0.3],
                'volatility_weight': [0.1, 0.15, 0.2, 0.25],
                'funding_weight': [0.05, 0.1, 0.15],
                'rsi_weight': [0.1, 0.15, 0.2],
                'oi_weight': [0.05, 0.1, 0.15]
            },
            'ensemble': {
                'vwap_weight': [0.8, 1.0, 1.2, 1.5],
                'rsi_weight': [1.0, 1.2, 1.5, 1.8],
                'volatility_weight': [0.8, 1.0, 1.2],
                'funding_weight': [0.6, 0.8, 1.0],
                'oi_weight': [0.5, 0.7, 0.9]
            },
            'multi': {
                'sma_fast': [5, 10, 15, 20],
                'sma_slow': [20, 30, 40, 50],
                'rsi_period': [10, 14, 18, 22],
                'bb_period': [15, 20, 25, 30]
            }
        }

        return param_ranges.get(strategy_name, {})

    def _get_parameter_space_size(self, param_ranges: Dict[str, List]) -> int:
        """Calculate the size of the parameter space."""
        size = 1
        for param_values in param_ranges.values():
            size *= len(param_values)
        return size

    async def _run_walk_forward_analysis(
        self,
        strategy_func,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str,
        window_months: int = 6,
        step_months: int = 1
    ) -> Optional[Dict[str, Any]]:
        """Run walk-forward analysis."""
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")

            results = []
            current_start = start_dt

            while current_start + timedelta(days=window_months*30) <= end_dt:
                # Define in-sample and out-of-sample periods
                in_sample_end = current_start + timedelta(days=window_months*30*0.8)  # 80% for training
                out_sample_start = in_sample_end
                out_sample_end = min(
                    current_start + timedelta(days=window_months*30),
                    end_dt
                )

                logger.info(f"Walk-forward period: {current_start.date()} to {out_sample_end.date()}")

                # Run in-sample optimization
                if strategy_name in ['smart', 'ensemble']:
                    param_ranges = self._get_optimization_parameters(strategy_name)
                    if param_ranges:
                        # Create backtester for in-sample
                        backtester = Backtester(config=self.config)
                        await backtester.initialize()
                        await backtester.load_data(
                            symbols,
                            current_start.strftime("%Y-%m-%d"),
                            in_sample_end.strftime("%Y-%m-%d")
                        )

                        # Quick optimization (smaller parameter space)
                        optimizer = StrategyOptimizer(
                            backtester=backtester,
                            strategy_func=strategy_func,
                            param_ranges=param_ranges,
                            optimization_target='sharpe_ratio'
                        )

                        opt_results = await optimizer.grid_search(verbose=False)
                        best_params = opt_results.get('best_params', {})
                    else:
                        best_params = {}
                else:
                    best_params = {}

                # Test on out-of-sample data
                backtester = Backtester(config=self.config)
                await backtester.initialize()
                await backtester.load_data(
                    symbols,
                    out_sample_start.strftime("%Y-%m-%d"),
                    out_sample_end.strftime("%Y-%m-%d")
                )

                async def signal_generator(timestamp, symbols_list):
                    return await strategy_func(timestamp, symbols_list, **best_params)

                success = await backtester.run_backtest(signal_generator=signal_generator)

                if success:
                    results.append({
                        'period_start': current_start.isoformat(),
                        'period_end': out_sample_end.isoformat(),
                        'in_sample_end': in_sample_end.isoformat(),
                        'out_sample_start': out_sample_start.isoformat(),
                        'parameters': best_params,
                        'metrics': backtester.metrics
                    })

                # Move to next period
                current_start += timedelta(days=step_months*30)

            return {
                'periods': results,
                'summary': self._summarize_walk_forward_results(results)
            }

        except Exception as e:
            logger.error(f"Error in walk-forward analysis for {strategy_name}: {e}")
            return None

    def _summarize_walk_forward_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Summarize walk-forward analysis results."""
        if not results:
            return {}

        # Extract metrics
        returns = [r['metrics'].get('total_return', 0) for r in results]
        sharpe_ratios = [r['metrics'].get('sharpe_ratio', 0) for r in results]
        max_drawdowns = [r['metrics'].get('max_drawdown', 0) for r in results]

        return {
            'total_periods': len(results),
            'avg_return': np.mean(returns),
            'std_return': np.std(returns),
            'avg_sharpe': np.mean(sharpe_ratios),
            'std_sharpe': np.std(sharpe_ratios),
            'avg_max_drawdown': np.mean(max_drawdowns),
            'worst_drawdown': min(max_drawdowns),
            'positive_periods': sum(1 for r in returns if r > 0),
            'win_rate': sum(1 for r in returns if r > 0) / len(returns) if returns else 0
        }

    def _generate_strategy_comparison(self, strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comparison analysis between strategies."""
        comparison = {
            'ranking': {},
            'metrics_comparison': {},
            'risk_return_analysis': {}
        }

        # Extract key metrics for comparison
        strategies = list(strategy_results.keys())
        metrics_to_compare = [
            'total_return', 'sharpe_ratio', 'max_drawdown',
            'win_rate', 'total_trades', 'volatility'
        ]

        # Create comparison table
        comparison_data = {}
        for metric in metrics_to_compare:
            comparison_data[metric] = {}
            for strategy in strategies:
                metrics = strategy_results[strategy].get('metrics', {})
                if isinstance(metrics, dict):
                    comparison_data[metric][strategy] = metrics.get(metric, 0)
                else:
                    # Handle PerformanceMetrics object
                    comparison_data[metric][strategy] = getattr(metrics, metric, 0)

        comparison['metrics_comparison'] = comparison_data

        # Rank strategies by different criteria
        ranking_criteria = ['total_return', 'sharpe_ratio', 'max_drawdown']
        for criterion in ranking_criteria:
            if criterion in comparison_data:
                # Sort strategies by criterion (descending for returns/sharpe, ascending for drawdown)
                reverse_sort = criterion != 'max_drawdown'
                sorted_strategies = sorted(
                    strategies,
                    key=lambda s: comparison_data[criterion].get(s, 0),
                    reverse=reverse_sort
                )
                comparison['ranking'][criterion] = sorted_strategies

        # Risk-return analysis
        risk_return = {}
        for strategy in strategies:
            total_return = comparison_data.get('total_return', {}).get(strategy, 0)
            volatility = comparison_data.get('volatility', {}).get(strategy, 0)
            max_dd = comparison_data.get('max_drawdown', {}).get(strategy, 0)

            risk_return[strategy] = {
                'return': total_return,
                'volatility': volatility,
                'max_drawdown': max_dd,
                'return_to_risk': total_return / volatility if volatility > 0 else 0,
                'calmar_ratio': total_return / abs(max_dd) if max_dd != 0 else 0
            }

        comparison['risk_return_analysis'] = risk_return

        return comparison

    async def _generate_advanced_analytics(
        self,
        strategy_results: Dict[str, Any],
        symbols: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate advanced analytics across all strategies."""
        analytics = {
            'market_analysis': {},
            'signal_analysis': {},
            'performance_attribution': {},
            'risk_analysis': {}
        }

        # Market analysis
        analytics['market_analysis'] = {
            'test_period': f"{start_date} to {end_date}",
            'symbols_tested': symbols,
            'total_strategies': len(strategy_results)
        }

        # Signal analysis
        signal_stats = {}
        for strategy_name, results in strategy_results.items():
            signals = results.get('signals', [])
            if signals:
                buy_signals = sum(1 for s in signals if s.get('action') == 'BUY')
                sell_signals = sum(1 for s in signals if s.get('action') == 'SELL')
                signal_stats[strategy_name] = {
                    'total_signals': len(signals),
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals,
                    'signal_ratio': buy_signals / sell_signals if sell_signals > 0 else float('inf')
                }

        analytics['signal_analysis'] = signal_stats

        # Performance attribution
        best_strategy = None
        best_return = float('-inf')
        for strategy_name, results in strategy_results.items():
            metrics = results.get('metrics', {})
            total_return = metrics.get('total_return', 0) if isinstance(metrics, dict) else getattr(metrics, 'total_return', 0)
            if total_return > best_return:
                best_return = total_return
                best_strategy = strategy_name

        analytics['performance_attribution'] = {
            'best_strategy': best_strategy,
            'best_return': best_return,
            'strategy_count': len(strategy_results)
        }

        return analytics

    def _save_comprehensive_results(self, results: Dict[str, Any], output_dir: str):
        """Save comprehensive results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save main results
        results_file = os.path.join(output_dir, f"comprehensive_results_{timestamp}.json")

        # Convert to serializable format
        serializable_results = self._make_serializable(results)

        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)

        # Save comparison summary
        if 'comparison' in results and results['comparison']:
            comparison_file = os.path.join(output_dir, f"strategy_comparison_{timestamp}.json")
            with open(comparison_file, 'w') as f:
                json.dump(results['comparison'], f, indent=2)

        # Generate text report
        report_file = os.path.join(output_dir, f"comprehensive_report_{timestamp}.txt")
        self._generate_text_report(results, report_file)

        logger.info(f"Comprehensive results saved to {output_dir}")

    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    def _generate_text_report(self, results: Dict[str, Any], report_file: str):
        """Generate a comprehensive text report."""
        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("COMPREHENSIVE BACKTESTING REPORT\n")
            f.write("=" * 80 + "\n\n")

            # Strategy results summary
            if 'strategy_results' in results:
                f.write("📊 STRATEGY PERFORMANCE SUMMARY\n")
                f.write("-" * 40 + "\n")

                for strategy_name, strategy_data in results['strategy_results'].items():
                    metrics = strategy_data.get('metrics', {})
                    if isinstance(metrics, dict):
                        total_return = metrics.get('total_return', 0)
                        sharpe_ratio = metrics.get('sharpe_ratio', 0)
                        max_drawdown = metrics.get('max_drawdown', 0)
                    else:
                        total_return = getattr(metrics, 'total_return', 0)
                        sharpe_ratio = getattr(metrics, 'sharpe_ratio', 0)
                        max_drawdown = getattr(metrics, 'max_drawdown', 0)

                    f.write(f"\n{strategy_name.upper()}:\n")
                    f.write(f"  Total Return: {total_return:.2%}\n")
                    f.write(f"  Sharpe Ratio: {sharpe_ratio:.3f}\n")
                    f.write(f"  Max Drawdown: {max_drawdown:.2%}\n")

            # Strategy ranking
            if 'comparison' in results and 'ranking' in results['comparison']:
                f.write("\n\n🏆 STRATEGY RANKINGS\n")
                f.write("-" * 40 + "\n")

                for criterion, ranking in results['comparison']['ranking'].items():
                    f.write(f"\nBy {criterion.replace('_', ' ').title()}:\n")
                    for i, strategy in enumerate(ranking, 1):
                        f.write(f"  {i}. {strategy}\n")

            # Advanced analytics
            if 'analytics' in results:
                f.write("\n\n📈 ADVANCED ANALYTICS\n")
                f.write("-" * 40 + "\n")

                analytics = results['analytics']
                if 'market_analysis' in analytics:
                    market = analytics['market_analysis']
                    f.write(f"\nTest Period: {market.get('test_period', 'N/A')}\n")
                    f.write(f"Symbols Tested: {', '.join(market.get('symbols_tested', []))}\n")
                    f.write(f"Total Strategies: {market.get('total_strategies', 0)}\n")

                if 'performance_attribution' in analytics:
                    perf = analytics['performance_attribution']
                    f.write(f"\nBest Strategy: {perf.get('best_strategy', 'N/A')}\n")
                    f.write(f"Best Return: {perf.get('best_return', 0):.2%}\n")

            f.write("\n" + "=" * 80 + "\n")

    async def _generate_comprehensive_visualizations(self, results: Dict[str, Any], output_dir: str):
        """Generate comprehensive visualizations."""
        try:
            plots_dir = os.path.join(output_dir, 'plots')
            os.makedirs(plots_dir, exist_ok=True)

            # Generate individual strategy plots
            for strategy_name, strategy_data in results.get('strategy_results', {}).items():
                strategy_plots_dir = os.path.join(plots_dir, strategy_name)
                os.makedirs(strategy_plots_dir, exist_ok=True)

                # Create visualizer for this strategy
                visualizer = BacktestVisualizer(
                    results_dir=os.path.join(output_dir, strategy_name),
                    output_dir=strategy_plots_dir
                )

                # Load data and generate plots
                await visualizer.load_data()
                visualizer.plot_equity_curve(
                    save_path=os.path.join(strategy_plots_dir, f"{strategy_name}_equity_curve.png")
                )
                visualizer.plot_drawdown(
                    save_path=os.path.join(strategy_plots_dir, f"{strategy_name}_drawdown.png")
                )

            logger.info(f"Visualizations saved to {plots_dir}")

        except Exception as e:
            logger.error(f"Error generating visualizations: {e}")


async def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Enhanced Backtest Runner")
    parser.add_argument('--symbols', nargs='+', default=['BTC-USDT'], help='Symbols to test')
    parser.add_argument('--start-date', default='2023-01-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2023-12-31', help='End date (YYYY-MM-DD)')
    parser.add_argument('--strategies', nargs='+', default=['smart', 'ensemble'], help='Strategies to test')
    parser.add_argument('--optimize', action='store_true', help='Run parameter optimization')
    parser.add_argument('--walk-forward', action='store_true', help='Run walk-forward analysis')
    parser.add_argument('--output-dir', default='results/enhanced_backtest', help='Output directory')
    parser.add_argument('--config', default='config.yaml', help='Config file path')

    args = parser.parse_args()

    # Create enhanced backtest runner
    runner = EnhancedBacktestRunner(config_path=args.config)

    # Run comprehensive backtesting
    results = await runner.run_comprehensive_backtest(
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        strategies=args.strategies,
        optimize=args.optimize,
        walk_forward=args.walk_forward,
        output_dir=args.output_dir
    )

    print("\n🎉 Enhanced backtesting completed!")
    print(f"📁 Results saved to: {args.output_dir}")

    # Print summary
    if 'comparison' in results and 'ranking' in results['comparison']:
        print("\n🏆 Top Strategies by Sharpe Ratio:")
        sharpe_ranking = results['comparison']['ranking'].get('sharpe_ratio', [])
        for i, strategy in enumerate(sharpe_ranking[:3], 1):
            print(f"  {i}. {strategy}")


if __name__ == "__main__":
    asyncio.run(main())
