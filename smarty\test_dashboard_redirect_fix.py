#!/usr/bin/env python3
"""
Test the dashboard redirect fix for Money Circle onboarding system.
This test specifically focuses on the issue where users are redirected 
back to agreement page instead of accessing the dashboard.
"""

import requests
import sys
import time
import random
import string

def generate_test_user():
    """Generate unique test user data."""
    suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return {
        'username': f'testuser_{suffix}',
        'email': f'test_{suffix}@example.com',
        'password': 'TestPass123',
        'confirm_password': 'TestPass123'
    }

def test_dashboard_redirect_fix():
    """Test the complete flow with focus on dashboard access."""
    print("🔧 Testing Dashboard Redirect Fix")
    print("=" * 50)
    
    # Generate unique test user
    test_user = generate_test_user()
    print(f"📋 Testing with user: {test_user['username']} ({test_user['email']})")
    
    session = requests.Session()
    
    try:
        # Step 1: Register user
        print("\n1️⃣ Registering user...")
        register_response = session.post(
            'http://localhost:8084/register',
            data=test_user,
            allow_redirects=False
        )
        
        if register_response.status_code != 302:
            print(f"❌ Registration failed: {register_response.status_code}")
            return False
        
        print("✅ Registration successful")
        
        # Step 2: Accept agreement
        print("2️⃣ Accepting agreement...")
        agreement_data = {
            'agreement_read': 'on',
            'risk_acknowledgment': 'on',
            'age_confirmation': 'on',
            'digital_signature': f'{test_user["username"]} Test User'
        }
        
        accept_response = session.post(
            'http://localhost:8084/agreement',
            data=agreement_data,
            allow_redirects=False
        )
        
        if accept_response.status_code != 302:
            print(f"❌ Agreement acceptance failed: {accept_response.status_code}")
            return False
        
        location = accept_response.headers.get('Location', '')
        if '/welcome' not in location:
            print(f"❌ Agreement didn't redirect to welcome: {location}")
            return False
        
        print("✅ Agreement accepted, redirected to welcome")
        
        # Step 3: Access welcome page
        print("3️⃣ Accessing welcome page...")
        welcome_response = session.get('http://localhost:8084/welcome')
        
        if welcome_response.status_code != 200:
            print(f"❌ Welcome page failed: {welcome_response.status_code}")
            return False
        
        print("✅ Welcome page accessible")
        
        # Step 4: Test dashboard access (THE CRITICAL TEST)
        print("4️⃣ Testing dashboard access (CRITICAL TEST)...")
        dashboard_response = session.get(
            'http://localhost:8084/dashboard',
            allow_redirects=False
        )
        
        print(f"📊 Dashboard response status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("🎉 SUCCESS! Dashboard accessible directly!")
            return True
        elif dashboard_response.status_code == 302:
            redirect_location = dashboard_response.headers.get('Location', '')
            print(f"🔄 Dashboard redirected to: {redirect_location}")
            
            if '/agreement' in redirect_location:
                print("❌ FAILED! Dashboard still redirecting to agreement page")
                print("🔍 This indicates the authentication middleware is not working correctly")
                return False
            elif '/login' in redirect_location:
                print("❌ FAILED! Dashboard redirecting to login (session issue)")
                return False
            else:
                print(f"❓ Unexpected redirect: {redirect_location}")
                return False
        else:
            print(f"❌ FAILED! Unexpected dashboard response: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_existing_user_dashboard_access():
    """Test dashboard access for existing user (dem29)."""
    print("\n🔧 Testing Existing User Dashboard Access")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as existing user
        print("1️⃣ Logging in as existing user (dem29)...")
        login_data = {
            'username': 'dem29',
            'password': 'password123'
        }
        
        login_response = session.post(
            'http://localhost:8084/login',
            data=login_data,
            allow_redirects=False
        )
        
        if login_response.status_code != 302:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("✅ Login successful")
        
        # Test dashboard access
        print("2️⃣ Testing dashboard access...")
        dashboard_response = session.get(
            'http://localhost:8084/dashboard',
            allow_redirects=False
        )
        
        print(f"📊 Dashboard response status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("🎉 SUCCESS! Existing user can access dashboard!")
            return True
        elif dashboard_response.status_code == 302:
            redirect_location = dashboard_response.headers.get('Location', '')
            print(f"🔄 Dashboard redirected to: {redirect_location}")
            
            if '/agreement' in redirect_location:
                print("❌ FAILED! Existing user still redirected to agreement")
                return False
            else:
                print(f"❓ Unexpected redirect: {redirect_location}")
                return False
        else:
            print(f"❌ FAILED! Unexpected response: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run dashboard redirect fix tests."""
    print("🚀 MONEY CIRCLE DASHBOARD REDIRECT FIX - TESTING")
    print("=" * 60)
    
    tests = [
        ("New User Dashboard Access", test_dashboard_redirect_fix),
        ("Existing User Dashboard Access", test_existing_user_dashboard_access),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        if test_func():
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Dashboard redirect issue is FIXED!")
        return 0
    else:
        print("❌ DASHBOARD REDIRECT ISSUE STILL EXISTS")
        print("🔍 Need to investigate authentication middleware logic")
        return 1

if __name__ == "__main__":
    sys.exit(main())
