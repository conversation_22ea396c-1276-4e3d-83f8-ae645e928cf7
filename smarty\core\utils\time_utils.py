"""
Time utility functions for the smart-trader system.
"""

from datetime import datetime, timezone
from typing import Union


def utc_timestamp() -> int:
    """
    Get current UTC timestamp in milliseconds.

    Returns:
        UTC timestamp in milliseconds
    """
    return int(datetime.now(timezone.utc).timestamp() * 1000)


def iso_timestamp() -> str:
    """
    Get current UTC timestamp in ISO format.

    Returns:
        ISO formatted timestamp
    """
    return datetime.now(timezone.utc).isoformat()


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """
    Convert timestamp to datetime object.
    
    Args:
        timestamp: Unix timestamp (seconds or milliseconds)
        
    Returns:
        Datetime object in UTC
    """
    # Handle both seconds and milliseconds
    if timestamp > 1e10:  # Likely milliseconds
        timestamp = timestamp / 1000
    
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    Convert datetime to UTC timestamp in milliseconds.
    
    Args:
        dt: Datetime object
        
    Returns:
        UTC timestamp in milliseconds
    """
    return int(dt.timestamp() * 1000)


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 1:
        return f"{seconds * 1000:.1f}ms"
    elif seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


if __name__ == "__main__":
    # Test time utilities
    print("Testing time utilities:")
    
    # Test timestamp functions
    ts = utc_timestamp()
    iso = iso_timestamp()
    print(f"UTC timestamp: {ts}")
    print(f"ISO timestamp: {iso}")
    
    # Test conversions
    dt = timestamp_to_datetime(ts)
    ts_back = datetime_to_timestamp(dt)
    print(f"Timestamp -> DateTime -> Timestamp: {ts} -> {dt} -> {ts_back}")
    
    # Test duration formatting
    durations = [0.001, 0.5, 1.5, 65.3, 3661.5]
    for duration in durations:
        formatted = format_duration(duration)
        print(f"Duration {duration}s -> {formatted}")
