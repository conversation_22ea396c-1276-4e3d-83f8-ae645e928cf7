#!/usr/bin/env python3
"""
Simple demo data seeding script for Money Circle.
Works with existing database schema and creates basic demo data.
"""

import sqlite3
import random
import hashlib
from datetime import datetime, timedelta
import logging
import bcrypt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleDataSeeder:
    def __init__(self, db_path='data/money_circle.db'):
        self.db_path = db_path
        self.conn = None

        # Simple demo users
        self.demo_users = [
            ('trader_alex', '<EMAIL>', 'admin'),
            ('crypto_sarah', '<EMAIL>', 'member'),
            ('quant_mike', '<EMAIL>', 'member'),
            ('forex_emma', '<EMAIL>', 'member'),
            ('options_david', '<EMAIL>', 'member'),
            ('swing_lisa', '<EMAIL>', 'member'),
            ('momentum_james', '<EMAIL>', 'member'),
            ('value_maria', '<EMAIL>', 'member'),
            ('algo_robert', '<EMAIL>', 'admin'),
            ('scalp_jenny', '<EMAIL>', 'member'),
        ]

    def connect_db(self):
        """Connect to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            logger.info(f"Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False

    def hash_password(self, password):
        """Hash password using bcrypt (compatible with Money Circle auth)."""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

    def clear_demo_data(self):
        """Clear existing demo data."""
        try:
            # Clear demo users (keep epinnox admin)
            self.conn.execute("DELETE FROM users WHERE username != 'epinnox'")
            self.conn.commit()
            logger.info("Cleared existing demo data")
        except Exception as e:
            logger.error(f"Error clearing data: {e}")

    def create_demo_users(self):
        """Create demo user accounts."""
        try:
            password_hash = self.hash_password("securepass123")

            for username, email, role in self.demo_users:
                self.conn.execute("""
                    INSERT OR REPLACE INTO users (username, email, hashed_password, role, date_joined, agreement_accepted)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    username,
                    email,
                    password_hash,
                    role,
                    datetime.now().isoformat(),
                    True
                ))

            self.conn.commit()
            logger.info(f"Created {len(self.demo_users)} demo users")

        except Exception as e:
            logger.error(f"Error creating demo users: {e}")

    def create_demo_strategies(self):
        """Create demo trading strategies."""
        try:
            users = self.conn.execute("SELECT id FROM users WHERE username != 'epinnox'").fetchall()

            strategies = [
                ('Momentum Breakout Pro', 'High-frequency momentum strategy', 'day_trading', 'high'),
                ('Crypto Swing Master', 'Multi-timeframe crypto analysis', 'swing_trading', 'high'),
                ('Dividend Growth Elite', 'Long-term dividend strategy', 'long_term', 'low'),
                ('Forex Scalping Bot', 'Automated forex scalping', 'day_trading', 'medium'),
                ('Options Income Generator', 'Conservative options strategy', 'options', 'medium'),
            ]

            for title, description, strategy_type, risk_level in strategies:
                creator = random.choice(users)
                creator_id = creator[0]

                self.conn.execute("""
                    INSERT INTO strategy_proposals
                    (title, name, description, proposed_by, strategy_type, risk_level, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    title,
                    title,
                    description,
                    creator_id,
                    strategy_type,
                    risk_level,
                    'approved',
                    datetime.now().isoformat()
                ))

            self.conn.commit()
            logger.info(f"Created {len(strategies)} demo strategies")

        except Exception as e:
            logger.error(f"Error creating demo strategies: {e}")

    def create_demo_trades(self):
        """Create demo trading history."""
        try:
            users = self.conn.execute("SELECT id FROM users WHERE username != 'epinnox'").fetchall()

            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']

            for user in users:
                user_id = user[0]

                # Create 10-20 trades per user
                for _ in range(random.randint(10, 20)):
                    symbol = random.choice(symbols)
                    side = random.choice(['buy', 'sell'])
                    size = random.uniform(0.1, 10.0)
                    price = random.uniform(100, 50000)

                    trade_date = datetime.now() - timedelta(days=random.randint(1, 90))

                    self.conn.execute("""
                        INSERT INTO user_trades
                        (user_id, exchange_name, symbol, side, size, price, fee, order_type, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        'HTX',
                        symbol,
                        side,
                        size,
                        price,
                        price * size * 0.001,  # 0.1% fee
                        'market',
                        trade_date.isoformat()
                    ))

            self.conn.commit()
            logger.info("Created demo trading history")

        except Exception as e:
            logger.error(f"Error creating demo trades: {e}")

    def create_demo_positions(self):
        """Create demo positions."""
        try:
            users = self.conn.execute("SELECT id FROM users WHERE username != 'epinnox'").fetchall()

            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']

            for user in users[:5]:  # Only some users have open positions
                user_id = user[0]

                # Create 1-3 positions per user
                for _ in range(random.randint(1, 3)):
                    symbol = random.choice(symbols)
                    side = random.choice(['long', 'short'])
                    size = random.uniform(0.1, 5.0)
                    entry_price = random.uniform(100, 50000)
                    current_price = entry_price * random.uniform(0.95, 1.05)

                    pnl = (current_price - entry_price) * size
                    if side == 'short':
                        pnl = -pnl

                    self.conn.execute("""
                        INSERT INTO user_positions
                        (user_id, exchange_name, symbol, side, size, entry_price, current_price, pnl, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        'HTX',
                        symbol,
                        side,
                        size,
                        entry_price,
                        current_price,
                        pnl,
                        'open',
                        datetime.now().isoformat()
                    ))

            self.conn.commit()
            logger.info("Created demo positions")

        except Exception as e:
            logger.error(f"Error creating demo positions: {e}")

    def run_simple_seed(self):
        """Run the simple seeding process."""
        logger.info("Starting simple Money Circle database seeding...")

        if not self.connect_db():
            return False

        try:
            # Step 1: Clear existing data
            logger.info("Step 1: Clearing existing demo data...")
            self.clear_demo_data()

            # Step 2: Create demo users
            logger.info("Step 2: Creating demo users...")
            self.create_demo_users()

            # Step 3: Create demo strategies
            logger.info("Step 3: Creating demo strategies...")
            self.create_demo_strategies()

            # Step 4: Create demo trades
            logger.info("Step 4: Creating demo trades...")
            self.create_demo_trades()

            # Step 5: Create demo positions
            logger.info("Step 5: Creating demo positions...")
            self.create_demo_positions()

            logger.info("Simple database seeding completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Seeding failed: {e}")
            return False

        finally:
            if self.conn:
                self.conn.close()

def main():
    """Main function to run the simple seeding script."""
    seeder = SimpleDataSeeder()
    success = seeder.run_simple_seed()

    if success:
        print("\nSIMPLE MONEY CIRCLE DEMO DATA CREATED!")
        print("=" * 50)
        print("10 demo user accounts created")
        print("5 trading strategies created")
        print("Demo trading history and positions")
        print("\nLogin Credentials:")
        print("   Username: Any demo username (e.g., trader_alex)")
        print("   Password: securepass123")
        print("\nPlatform ready at: http://localhost:8084")
        return 0
    else:
        print("\nSIMPLE SEEDING FAILED - Check logs for details")
        return 1

if __name__ == "__main__":
    exit(main())
