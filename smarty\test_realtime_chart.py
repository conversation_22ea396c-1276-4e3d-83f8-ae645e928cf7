#!/usr/bin/env python3
"""
Real-Time Price Chart Performance Test
Test the live data flow and chart update performance.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
import sqlite3

async def test_chart_data_flow():
    """Test the real-time data flow for the price chart."""
    print("🧪 TESTING REAL-TIME PRICE CHART PERFORMANCE")
    print("=" * 60)
    
    # Test 1: Check SQLite data freshness
    print("\n📊 TEST 1: SQLite Data Freshness")
    print("-" * 30)
    
    try:
        conn = sqlite3.connect("data/bus.db")
        cursor = conn.cursor()
        
        # Check latest BTC-USDT data
        cursor.execute("""
            SELECT stream, ts, payload FROM messages
            WHERE stream LIKE 'market.BTC-USDT.%'
            ORDER BY ts DESC LIMIT 5
        """)
        
        btc_data = cursor.fetchall()
        if btc_data:
            latest_time = datetime.fromtimestamp(btc_data[0][1])
            age = (datetime.now() - latest_time).total_seconds()
            print(f"✅ Latest BTC-USDT data: {age:.1f}s ago")
            print(f"   Stream: {btc_data[0][0]}")
            
            # Show recent data points
            for i, (stream, ts, payload) in enumerate(btc_data):
                data_time = datetime.fromtimestamp(ts)
                age = (datetime.now() - data_time).total_seconds()
                try:
                    data = json.loads(payload)
                    if 'tick' in data:
                        tick = data['tick']
                        if 'bids' in tick and tick['bids']:
                            price = tick['bids'][0][0]
                            print(f"   {i+1}. {data_time.strftime('%H:%M:%S')} - ${price:,.2f} ({age:.1f}s ago)")
                        elif 'close' in tick:
                            price = tick['close']
                            print(f"   {i+1}. {data_time.strftime('%H:%M:%S')} - ${price:,.2f} ({age:.1f}s ago)")
                except:
                    print(f"   {i+1}. {data_time.strftime('%H:%M:%S')} - Data parsing error")
        else:
            print("❌ No BTC-USDT data found!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ SQLite test failed: {e}")
    
    # Test 2: API Response Time
    print("\n⚡ TEST 2: API Response Time")
    print("-" * 30)
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8083/api/market-data') as response:
                if response.status == 200:
                    data = await response.json()
                    response_time = (time.time() - start_time) * 1000
                    print(f"✅ API Response: {response_time:.1f}ms")
                    print(f"   Price: {data.get('price', 'N/A')}")
                    print(f"   Symbol: {data.get('symbol', 'N/A')}")
                    print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
                    print(f"   Is Live: {data.get('is_live', False)}")
                    print(f"   Data Age: {data.get('data_age_seconds', 999):.1f}s")
                    print(f"   Source: {data.get('source', 'Unknown')}")
                else:
                    print(f"❌ API Error: {response.status}")
    except Exception as e:
        print(f"❌ API test failed: {e}")
    
    # Test 3: WebSocket Connection
    print("\n🔌 TEST 3: WebSocket Connection")
    print("-" * 30)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.ws_connect('ws://localhost:8083/ws') as ws:
                print("✅ WebSocket connected")
                
                # Wait for a few messages
                message_count = 0
                start_time = time.time()
                
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        try:
                            data = json.loads(msg.data)
                            message_count += 1
                            
                            if 'market' in data:
                                market = data['market']
                                print(f"📈 Message {message_count}: {market.get('symbol', 'N/A')} @ {market.get('price', 'N/A')} ({market.get('timestamp', 'N/A')})")
                            
                            # Test for 10 seconds or 5 messages
                            if message_count >= 5 or (time.time() - start_time) > 10:
                                break
                                
                        except json.JSONDecodeError:
                            print(f"⚠️ Invalid JSON message: {msg.data[:100]}...")
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        print(f"❌ WebSocket error: {ws.exception()}")
                        break
                
                if message_count > 0:
                    rate = message_count / (time.time() - start_time)
                    print(f"✅ Received {message_count} messages ({rate:.1f} msg/sec)")
                else:
                    print("⚠️ No messages received in 10 seconds")
                    
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
    
    # Test 4: Chart Update Simulation
    print("\n📊 TEST 4: Chart Update Simulation")
    print("-" * 30)
    
    try:
        # Simulate multiple rapid API calls (like chart updates)
        update_times = []
        
        async with aiohttp.ClientSession() as session:
            for i in range(10):
                start_time = time.time()
                async with session.get('http://localhost:8083/api/market-data') as response:
                    if response.status == 200:
                        data = await response.json()
                        response_time = (time.time() - start_time) * 1000
                        update_times.append(response_time)
                        
                        if i == 0:  # Show first response details
                            print(f"📈 Sample Update: {data.get('symbol', 'N/A')} @ {data.get('price', 'N/A')}")
                
                await asyncio.sleep(0.1)  # 100ms between requests
        
        if update_times:
            avg_time = sum(update_times) / len(update_times)
            max_time = max(update_times)
            min_time = min(update_times)
            
            print(f"✅ Chart Update Performance:")
            print(f"   Average: {avg_time:.1f}ms")
            print(f"   Min: {min_time:.1f}ms")
            print(f"   Max: {max_time:.1f}ms")
            
            if avg_time < 50:
                print("🚀 Excellent performance for real-time charts!")
            elif avg_time < 100:
                print("✅ Good performance for real-time charts")
            else:
                print("⚠️ Performance may impact chart smoothness")
        
    except Exception as e:
        print(f"❌ Chart simulation failed: {e}")
    
    # Test 5: Symbol Switching Performance
    print("\n🔄 TEST 5: Symbol Switching Performance")
    print("-" * 30)
    
    try:
        symbols = ["BTC-USDT", "ETH-USDT"]
        
        async with aiohttp.ClientSession() as session:
            for symbol in symbols:
                # Simulate symbol change
                start_time = time.time()
                async with session.post('http://localhost:8083/api/symbol/select', 
                                      json={"symbol": symbol}) as response:
                    switch_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        print(f"✅ {symbol} switch: {switch_time:.1f}ms")
                        
                        # Get data for new symbol
                        await asyncio.sleep(0.5)  # Wait for data to update
                        async with session.get('http://localhost:8083/api/market-data') as data_response:
                            if data_response.status == 200:
                                data = await data_response.json()
                                print(f"   Data: {data.get('symbol', 'N/A')} @ {data.get('price', 'N/A')}")
                    else:
                        print(f"❌ {symbol} switch failed: {response.status}")
                
                await asyncio.sleep(1)  # Wait between switches
        
    except Exception as e:
        print(f"❌ Symbol switching test failed: {e}")
    
    print("\n" + "=" * 60)
    print("✅ REAL-TIME CHART PERFORMANCE TEST COMPLETE")
    
    # Summary and recommendations
    print("\n💡 PERFORMANCE SUMMARY:")
    print("1. Check data freshness - should be < 30s for live charts")
    print("2. API response time - should be < 100ms for smooth updates")
    print("3. WebSocket messages - should receive regular updates")
    print("4. Chart updates - should average < 50ms for best experience")
    print("5. Symbol switching - should be < 500ms for good UX")

if __name__ == "__main__":
    asyncio.run(test_chart_data_flow())
