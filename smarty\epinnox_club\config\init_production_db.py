#!/usr/bin/env python3
import sqlite3
from pathlib import Path

def init_production_database():
    db_path = Path('data/money_circle_production.db')
    db_path.parent.mkdir(exist_ok=True)
    
    with sqlite3.connect(db_path) as conn:
        # Enable WAL mode for better performance
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=memory')
        
        print("[OK] Production database initialized")

if __name__ == '__main__':
    init_production_database()
