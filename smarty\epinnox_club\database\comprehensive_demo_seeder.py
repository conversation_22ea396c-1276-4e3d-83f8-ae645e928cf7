#!/usr/bin/env python3
"""
Comprehensive Demo Data Seeder for Money Circle
Creates realistic demo data for showcasing all platform features.
"""

import sqlite3
import random
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import List, Dict, Any
import bcrypt

class ComprehensiveDemoSeeder:
    """Comprehensive demo data seeder for Money Circle."""

    def __init__(self, db_path: str = "data/money_circle.db"):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.execute("PRAGMA foreign_keys = ON")

        # Demo data configuration
        self.member_count = 18
        self.trading_history_months = 4
        self.strategies_count = 10
        self.exchanges = ['HTX', 'Binance', 'Bybit']
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT']

        # Base prices for realistic data
        self.base_prices = {
            'BTCUSDT': 50000,
            'ETHUSDT': 3000,
            'ADAUSDT': 0.5,
            'SOLUSDT': 100,
            'DOTUSDT': 8,
            'LINKUSDT': 15
        }

    def seed_all_data(self):
        """Seed all demo data."""
        print("🌱 Starting comprehensive demo data seeding...")

        # Clear existing demo data
        self.clear_demo_data()

        # Seed core data
        members = self.seed_members()
        strategies = self.seed_strategies()

        # Seed trading data
        self.seed_exchange_accounts(members)
        self.seed_trading_history(members)
        self.seed_positions(members)

        # Seed club features
        self.seed_strategy_following(members, strategies)
        self.seed_club_analytics()
        self.seed_notifications(members)

        # Seed social features
        self.seed_member_connections(members)
        self.seed_activity_feed(members)

        self.conn.commit()
        print("✅ Comprehensive demo data seeding completed!")

        # Generate summary report
        self.generate_seeding_report()

    def clear_demo_data(self):
        """Clear existing demo data."""
        print("🧹 Clearing existing demo data...")

        # List of tables to clear (in dependency order)
        tables_to_clear = [
            'notifications', 'member_activities', 'user_connections',
            'strategy_following', 'user_trades', 'user_positions',
            'user_exchanges', 'strategy_proposals', 'users'
        ]

        for table in tables_to_clear:
            try:
                self.conn.execute(f"DELETE FROM {table}")
                print(f"   Cleared {table}")
            except sqlite3.OperationalError:
                # Table might not exist yet
                pass

    def seed_members(self) -> List[Dict]:
        """Seed member accounts."""
        print("👥 Seeding member accounts...")

        members = []

        # Create diverse member profiles
        member_profiles = [
            {"username": "alex_trader", "email": "<EMAIL>", "role": "admin", "bio": "Lead trader with 5+ years experience"},
            {"username": "sarah_crypto", "email": "<EMAIL>", "role": "member", "bio": "DeFi specialist and yield farmer"},
            {"username": "mike_scalper", "email": "<EMAIL>", "role": "member", "bio": "High-frequency scalping expert"},
            {"username": "emma_hodler", "email": "<EMAIL>", "role": "member", "bio": "Long-term investment strategist"},
            {"username": "david_quant", "email": "<EMAIL>", "role": "member", "bio": "Quantitative analyst and algo trader"},
            {"username": "lisa_swing", "email": "<EMAIL>", "role": "member", "bio": "Swing trading and technical analysis"},
            {"username": "james_futures", "email": "<EMAIL>", "role": "member", "bio": "Futures and derivatives specialist"},
            {"username": "anna_defi", "email": "<EMAIL>", "role": "member", "bio": "DeFi protocols and liquidity mining"},
            {"username": "tom_arbitrage", "email": "<EMAIL>", "role": "member", "bio": "Cross-exchange arbitrage trader"},
            {"username": "kelly_options", "email": "<EMAIL>", "role": "member", "bio": "Options trading and volatility strategies"},
            {"username": "ryan_momentum", "email": "<EMAIL>", "role": "member", "bio": "Momentum and breakout strategies"},
            {"username": "sophia_ai", "email": "<EMAIL>", "role": "member", "bio": "AI/ML trading algorithms"},
            {"username": "carlos_macro", "email": "<EMAIL>", "role": "member", "bio": "Macro economic analysis"},
            {"username": "nina_social", "email": "<EMAIL>", "role": "member", "bio": "Social sentiment trading"},
            {"username": "ben_grid", "email": "<EMAIL>", "role": "member", "bio": "Grid and DCA strategies"},
            {"username": "zoe_nft", "email": "<EMAIL>", "role": "member", "bio": "NFT and gaming token specialist"},
            {"username": "max_whale", "email": "<EMAIL>", "role": "member", "bio": "Large position management"},
            {"username": "ivy_research", "email": "<EMAIL>", "role": "viewer", "bio": "Market research and analysis"}
        ]

        for i, profile in enumerate(member_profiles):
            # Hash password (all demo accounts use 'demo123')
            password_hash = bcrypt.hashpw('demo123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Create user
            cursor = self.conn.execute("""
                INSERT INTO users (username, email, hashed_password, role, date_joined, last_login, is_active, agreement_accepted)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                profile['username'],
                profile['email'],
                password_hash,
                profile['role'],
                (datetime.now() - timedelta(days=random.randint(30, 120))).isoformat(),
                (datetime.now() - timedelta(days=random.randint(0, 7))).isoformat(),
                True,
                True
            ))

            user_id = cursor.lastrowid

            # Record agreement acceptance
            self.conn.execute("""
                INSERT INTO membership_agreements (user_id, agreement_text, ip_address, user_agent, agreed_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                user_id,
                f"Money Circle Investment Club Membership Agreement v1.0\nDigital Signature: {profile['username']}\nDemo Account",
                f"192.168.1.{random.randint(100, 200)}",
                "Mozilla/5.0 (Demo Browser)",
                (datetime.now() - timedelta(days=random.randint(30, 120))).isoformat()
            ))

            members.append({
                'user_id': user_id,
                'username': profile['username'],
                'email': profile['email'],
                'role': profile['role'],
                'bio': profile['bio']
            })

            print(f"   Created member: {profile['username']} ({profile['role']})")

        return members

    def seed_strategies(self) -> List[Dict]:
        """Seed trading strategies."""
        print("📊 Seeding trading strategies...")

        strategies = []

        strategy_templates = [
            {"name": "BTC Momentum Breakout", "description": "Captures momentum breakouts in Bitcoin with dynamic stop-losses", "risk_level": "Medium"},
            {"name": "ETH DeFi Yield Strategy", "description": "Combines ETH trading with DeFi yield opportunities", "risk_level": "High"},
            {"name": "Multi-Asset Grid Bot", "description": "Grid trading across multiple cryptocurrency pairs", "risk_level": "Low"},
            {"name": "Futures Arbitrage", "description": "Exploits price differences between spot and futures markets", "risk_level": "Medium"},
            {"name": "AI Sentiment Scalper", "description": "Uses AI to analyze social sentiment for scalping opportunities", "risk_level": "High"},
            {"name": "Conservative DCA", "description": "Dollar-cost averaging with risk management", "risk_level": "Low"},
            {"name": "Volatility Harvester", "description": "Profits from high volatility periods using options-like strategies", "risk_level": "High"},
            {"name": "Cross-Exchange Arbitrage", "description": "Automated arbitrage across multiple exchanges", "risk_level": "Medium"},
            {"name": "Trend Following Suite", "description": "Multi-timeframe trend following with adaptive parameters", "risk_level": "Medium"},
            {"name": "Mean Reversion Scalper", "description": "High-frequency mean reversion on major pairs", "risk_level": "High"}
        ]

        for i, template in enumerate(strategy_templates):
            # Generate realistic performance metrics
            total_return = random.uniform(-15, 45)  # -15% to +45% total return
            win_rate = random.uniform(45, 75)  # 45% to 75% win rate
            max_drawdown = random.uniform(5, 25)  # 5% to 25% max drawdown
            sharpe_ratio = random.uniform(0.5, 2.5)  # 0.5 to 2.5 Sharpe ratio

            cursor = self.conn.execute("""
                INSERT INTO strategy_proposals (
                    name, description, proposed_by, status, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                template['name'],
                template['description'],
                random.randint(1, min(5, len(strategy_templates))),  # Random creator from first 5 members
                'approved',
                (datetime.now() - timedelta(days=random.randint(60, 180))).isoformat()
            ))

            strategy_id = cursor.lastrowid
            strategies.append({
                'strategy_id': strategy_id,
                'name': template['name'],
                'risk_level': template['risk_level'],
                'total_return': total_return
            })

            print(f"   Created strategy: {template['name']} ({total_return:+.1f}% return)")

        return strategies

    def seed_exchange_accounts(self, members: List[Dict]):
        """Seed exchange account connections."""
        print("🔗 Seeding exchange accounts...")

        for member in members:
            # Each member has 1-3 exchange accounts
            num_exchanges = random.randint(1, 3)
            member_exchanges = random.sample(self.exchanges, num_exchanges)

            for exchange in member_exchanges:
                # Generate fake encrypted credentials
                fake_api_key = f"demo_{exchange.lower()}_{secrets.token_hex(16)}"
                fake_secret = secrets.token_hex(32)

                self.conn.execute("""
                    INSERT INTO user_exchanges (
                        user_id, exchange_name, api_key_encrypted, secret_key_encrypted,
                        is_active, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    member['user_id'],
                    exchange,
                    fake_api_key.encode(),  # Store as blob
                    fake_secret.encode(),   # Store as blob
                    True,
                    (datetime.now() - timedelta(days=random.randint(10, 60))).isoformat()
                ))

                print(f"   Connected {member['username']} to {exchange}")

    def seed_trading_history(self, members: List[Dict]):
        """Seed realistic trading history."""
        print("💹 Seeding trading history...")

        total_trades = 0
        for member in members:
            # Generate 20-100 trades per member over the past few months
            num_trades = random.randint(20, 100)

            for _ in range(num_trades):
                # Random trade parameters
                symbol = random.choice(self.symbols)
                exchange = random.choice(self.exchanges)
                side = random.choice(['buy', 'sell'])

                # Generate realistic trade size and price
                base_price = self.base_prices[symbol]
                price_variation = random.uniform(0.8, 1.2)
                price = base_price * price_variation

                # Trade size varies by symbol
                if 'BTC' in symbol:
                    size = random.uniform(0.001, 0.1)
                elif 'ETH' in symbol:
                    size = random.uniform(0.01, 1.0)
                else:
                    size = random.uniform(1, 1000)

                # Calculate fee (0.1% typical)
                fee = price * size * 0.001

                # Random timestamp within last 4 months
                trade_time = datetime.now() - timedelta(
                    days=random.randint(0, 120),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )

                # Random strategy assignment
                strategies = ['Manual', 'BTC Momentum', 'Grid Bot', 'DCA Strategy', 'Scalping']
                strategy = random.choice(strategies)

                self.conn.execute("""
                    INSERT INTO user_trades (
                        user_id, exchange_name, symbol, side, size, price, fee,
                        order_type, strategy_name, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    member['user_id'],
                    exchange,
                    symbol,
                    side,
                    size,
                    price,
                    fee,
                    random.choice(['market', 'limit']),
                    strategy,
                    trade_time.isoformat()
                ))

                total_trades += 1

        print(f"   Generated {total_trades} trades across {len(members)} members")

    def seed_positions(self, members: List[Dict]):
        """Seed current positions."""
        print("📈 Seeding current positions...")

        total_positions = 0
        for member in members:
            # 30% chance of having open positions
            if random.random() < 0.3:
                num_positions = random.randint(1, 3)

                for _ in range(num_positions):
                    symbol = random.choice(self.symbols)
                    exchange = random.choice(self.exchanges)

                    # Position details
                    side = random.choice(['long', 'short'])
                    size = random.uniform(0.01, 1.0) if 'BTC' in symbol else random.uniform(0.1, 10.0)
                    entry_price = self.base_prices[symbol] * random.uniform(0.9, 1.1)
                    current_price = entry_price * random.uniform(0.95, 1.05)

                    # Calculate unrealized PnL
                    if side == 'long':
                        unrealized_pnl = (current_price - entry_price) * size
                    else:
                        unrealized_pnl = (entry_price - current_price) * size

                    self.conn.execute("""
                        INSERT INTO user_positions (
                            user_id, exchange_name, symbol, side, size, entry_price,
                            current_price, pnl, status, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        member['user_id'],
                        exchange,
                        symbol,
                        side,
                        size,
                        entry_price,
                        current_price,
                        unrealized_pnl,
                        'open',
                        datetime.now().isoformat()
                    ))

                    total_positions += 1

        print(f"   Generated {total_positions} open positions")

    def seed_strategy_following(self, members: List[Dict], strategies: List[Dict]):
        """Seed strategy following relationships."""
        print("👥 Seeding strategy following...")

        total_follows = 0
        for member in members:
            # Each member follows 2-5 strategies
            num_follows = random.randint(2, 5)
            followed_strategies = random.sample(strategies, min(num_follows, len(strategies)))

            for strategy in followed_strategies:
                self.conn.execute("""
                    INSERT INTO strategy_following (
                        user_id, strategy_id, is_active, created_at
                    ) VALUES (?, ?, ?, ?)
                """, (
                    member['user_id'],
                    strategy['strategy_id'],
                    True,
                    (datetime.now() - timedelta(days=random.randint(1, 60))).isoformat()
                ))

                total_follows += 1

        print(f"   Generated {total_follows} strategy follows")

    def seed_club_analytics(self):
        """Seed club-wide analytics data."""
        print("📊 Seeding club analytics...")

        # This would typically be calculated from actual data
        # For demo purposes, we'll create some summary records
        analytics_data = {
            'total_members': self.member_count,
            'total_strategies': self.strategies_count,
            'total_volume_30d': random.uniform(1000000, 5000000),
            'avg_return_30d': random.uniform(-5, 15),
            'top_performer': 'alex_trader'
        }

        print(f"   Club analytics: {analytics_data['total_members']} members, ${analytics_data['total_volume_30d']:,.0f} volume")

    def seed_notifications(self, members: List[Dict]):
        """Seed notifications."""
        print("🔔 Seeding notifications...")

        notification_templates = [
            "New strategy 'BTC Momentum Breakout' is now available",
            "Your position in ETHUSDT has gained +5.2%",
            "Weekly club performance report is ready",
            "alex_trader started following your strategy",
            "Market alert: High volatility detected in BTCUSDT",
            "Your stop-loss order for ADAUSDT was executed",
            "New member sarah_crypto joined the club",
            "Strategy 'Grid Bot' achieved 15% monthly return"
        ]

        total_notifications = 0
        for member in members:
            # Generate 3-8 notifications per member
            num_notifications = random.randint(3, 8)

            for _ in range(num_notifications):
                message = random.choice(notification_templates)
                is_read = random.choice([True, False])

                self.conn.execute("""
                    INSERT INTO notifications (
                        user_id, message, is_read, created_at
                    ) VALUES (?, ?, ?, ?)
                """, (
                    member['user_id'],
                    message,
                    is_read,
                    (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat()
                ))

                total_notifications += 1

        print(f"   Generated {total_notifications} notifications")

    def seed_member_connections(self, members: List[Dict]):
        """Seed member connections/following."""
        print("🤝 Seeding member connections...")

        total_connections = 0
        for member in members:
            # Each member connects with 3-8 other members
            num_connections = random.randint(3, 8)
            other_members = [m for m in members if m['user_id'] != member['user_id']]
            connections = random.sample(other_members, min(num_connections, len(other_members)))

            for connection in connections:
                # Avoid duplicate connections
                existing = self.conn.execute("""
                    SELECT 1 FROM user_connections
                    WHERE (user_id = ? AND connected_user_id = ?)
                    OR (user_id = ? AND connected_user_id = ?)
                """, (member['user_id'], connection['user_id'], connection['user_id'], member['user_id'])).fetchone()

                if not existing:
                    self.conn.execute("""
                        INSERT INTO user_connections (
                            user_id, connected_user_id, connection_type, created_at
                        ) VALUES (?, ?, ?, ?)
                    """, (
                        member['user_id'],
                        connection['user_id'],
                        'follow',
                        (datetime.now() - timedelta(days=random.randint(1, 90))).isoformat()
                    ))

                    total_connections += 1

        print(f"   Generated {total_connections} member connections")

    def seed_activity_feed(self, members: List[Dict]):
        """Seed activity feed."""
        print("📰 Seeding activity feed...")

        activity_templates = [
            "{username} placed a buy order for BTCUSDT",
            "{username} achieved 12% profit on ETHUSDT position",
            "{username} started following 'Grid Bot' strategy",
            "{username} shared a new trading insight",
            "{username} joined the Momentum Trading discussion",
            "{username} updated their risk management settings",
            "{username} completed a successful arbitrage trade",
            "{username} reached 75% win rate this month"
        ]

        total_activities = 0
        for _ in range(50):  # Generate 50 recent activities
            member = random.choice(members)
            template = random.choice(activity_templates)
            activity = template.format(username=member['username'])

            self.conn.execute("""
                INSERT INTO member_activities (
                    user_id, activity_type, activity_data, timestamp, is_public
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                member['user_id'],
                'trade',  # Could be 'trade', 'strategy', 'social', etc.
                activity,
                (datetime.now() - timedelta(days=random.randint(0, 7))).isoformat(),
                True
            ))

            total_activities += 1

        print(f"   Generated {total_activities} activity feed items")

    def generate_seeding_report(self):
        """Generate a summary report of seeded data."""
        print("\n" + "="*60)
        print("📋 DEMO DATA SEEDING REPORT")
        print("="*60)

        # Count records in each table
        tables_to_check = [
            'users', 'exchange_accounts', 'user_trades', 'user_positions',
            'club_strategies', 'strategy_following', 'notifications'
        ]

        for table in tables_to_check:
            try:
                cursor = self.conn.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"📊 {table}: {count} records")
            except sqlite3.OperationalError:
                print(f"❌ {table}: Table not found")

        print("\n🎯 Demo Login Credentials:")
        print("   Username: alex_trader (admin)")
        print("   Username: sarah_crypto (member)")
        print("   Username: mike_scalper (member)")
        print("   Password: demo123 (for all accounts)")

        print("\n✅ Demo environment ready for showcase!")
        print("="*60)

def main():
    """Run the comprehensive demo seeder."""
    seeder = ComprehensiveDemoSeeder()
    seeder.seed_all_data()

if __name__ == "__main__":
    main()
