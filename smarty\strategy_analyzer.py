#!/usr/bin/env python3
"""
Strategy Analysis Tool for Smart-Trader System

This tool analyzes strategy performance, identifies issues, and suggests improvements.
"""

import os
import json
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

from core.feature_store import feature_store
from backtester.strategies import smart_model_integrated_strategy
from backtester.backtester import Backtester

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StrategyAnalyzer:
    """Analyze strategy performance and identify improvement opportunities."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the strategy analyzer."""
        self.config = self._load_config(config_path)
        self.analysis_results = {}

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            import yaml
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}

    async def analyze_strategy_performance(
        self,
        strategy_name: str = "smart",
        symbols: List[str] = ["BTC-USDT"],
        start_date: str = "2023-01-01",
        end_date: str = "2023-12-31",
        output_dir: str = "results/strategy_analysis"
    ) -> Dict[str, Any]:
        """
        Comprehensive strategy performance analysis.

        Args:
            strategy_name: Name of strategy to analyze
            symbols: List of symbols to analyze
            start_date: Start date for analysis
            end_date: End date for analysis
            output_dir: Output directory for results

        Returns:
            Dictionary containing analysis results
        """
        logger.info(f"🔍 Analyzing {strategy_name} strategy performance...")

        os.makedirs(output_dir, exist_ok=True)

        analysis = {
            'strategy_name': strategy_name,
            'symbols': symbols,
            'period': f"{start_date} to {end_date}",
            'signal_analysis': {},
            'model_performance': {},
            'risk_analysis': {},
            'improvement_suggestions': [],
            'detailed_breakdown': {}
        }

        # Run backtest to get baseline performance
        baseline_results = await self._run_baseline_backtest(
            strategy_name, symbols, start_date, end_date, output_dir
        )

        if baseline_results:
            analysis['baseline_performance'] = baseline_results

            # Analyze signals
            signal_analysis = await self._analyze_signals(
                baseline_results.get('signals', []),
                baseline_results.get('trades', [])
            )
            analysis['signal_analysis'] = signal_analysis

            # Analyze model contributions
            model_analysis = await self._analyze_model_contributions(
                symbols, start_date, end_date
            )
            analysis['model_performance'] = model_analysis

            # Risk analysis
            risk_analysis = self._analyze_risk_metrics(baseline_results)
            analysis['risk_analysis'] = risk_analysis

            # Generate improvement suggestions
            suggestions = self._generate_improvement_suggestions(
                baseline_results, signal_analysis, model_analysis, risk_analysis
            )
            analysis['improvement_suggestions'] = suggestions

            # Detailed breakdown
            breakdown = await self._generate_detailed_breakdown(
                baseline_results, symbols, start_date, end_date
            )
            analysis['detailed_breakdown'] = breakdown

        # Save analysis results
        self._save_analysis_results(analysis, output_dir)

        # Generate visualizations
        await self._generate_analysis_visualizations(analysis, output_dir)

        logger.info("✅ Strategy analysis completed!")
        return analysis

    async def _run_baseline_backtest(
        self,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run baseline backtest for analysis."""
        try:
            # Create backtester
            backtester = Backtester(
                config=self.config,
                output_dir=os.path.join(output_dir, "baseline")
            )

            await backtester.initialize()
            await backtester.load_data(symbols, start_date, end_date)

            # Use smart strategy as default
            async def signal_generator(timestamp, symbols_list):
                return await smart_model_integrated_strategy(timestamp, symbols_list)

            success = await backtester.run_backtest(signal_generator=signal_generator)

            if success:
                return {
                    'metrics': backtester.metrics,
                    'equity_curve': backtester.equity_curve,
                    'trades': backtester.trades,
                    'signals': backtester.signals
                }
            else:
                logger.error("Baseline backtest failed")
                return None

        except Exception as e:
            logger.error(f"Error running baseline backtest: {e}")
            return None

    async def _analyze_signals(
        self,
        signals: List[Dict],
        trades: List[Dict]
    ) -> Dict[str, Any]:
        """Analyze signal quality and effectiveness."""
        if not signals:
            return {'error': 'No signals to analyze'}

        analysis = {
            'total_signals': len(signals),
            'signal_distribution': {},
            'signal_timing': {},
            'signal_accuracy': {},
            'signal_strength': {}
        }

        # Signal distribution
        buy_signals = sum(1 for s in signals if s.get('action') == 'BUY')
        sell_signals = sum(1 for s in signals if s.get('action') == 'SELL')
        hold_signals = len(signals) - buy_signals - sell_signals

        analysis['signal_distribution'] = {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'hold_signals': hold_signals,
            'buy_ratio': buy_signals / len(signals) if signals else 0,
            'sell_ratio': sell_signals / len(signals) if signals else 0
        }

        # Signal strength analysis
        signal_scores = [s.get('score', 0) for s in signals if s.get('score') is not None]
        if signal_scores:
            analysis['signal_strength'] = {
                'avg_score': np.mean(signal_scores),
                'std_score': np.std(signal_scores),
                'min_score': min(signal_scores),
                'max_score': max(signal_scores),
                'strong_signals': sum(1 for s in signal_scores if abs(s) > 0.5),
                'weak_signals': sum(1 for s in signal_scores if abs(s) < 0.2)
            }

        # Signal timing analysis
        if trades:
            profitable_trades = [t for t in trades if t.get('pnl', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl', 0) < 0]

            analysis['signal_accuracy'] = {
                'total_trades': len(trades),
                'profitable_trades': len(profitable_trades),
                'losing_trades': len(losing_trades),
                'win_rate': len(profitable_trades) / len(trades) if trades else 0,
                'avg_profit': np.mean([t.get('pnl', 0) for t in profitable_trades]) if profitable_trades else 0,
                'avg_loss': np.mean([t.get('pnl', 0) for t in losing_trades]) if losing_trades else 0
            }

        return analysis

    async def _analyze_model_contributions(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Analyze individual model contributions to strategy performance."""
        analysis = {
            'model_availability': {},
            'model_signal_quality': {},
            'model_correlation': {}
        }

        # Check which models are providing data
        model_keys = [
            'vwap_deviation_prediction',
            'volatility_regime_prediction',
            'funding_momentum_prediction',
            'oi_momentum_prediction',
            'rsi_model_prediction',
            'meta_ensemble_prediction'
        ]

        for symbol in symbols:
            symbol_analysis = {}

            for model_key in model_keys:
                try:
                    # Check if model data is available
                    model_data = await feature_store.get(symbol, model_key)
                    symbol_analysis[model_key] = {
                        'available': model_data is not None,
                        'data_type': type(model_data).__name__ if model_data else None,
                        'last_update': datetime.now().isoformat()
                    }

                    if model_data:
                        # Analyze model data quality
                        if isinstance(model_data, dict):
                            symbol_analysis[model_key]['keys'] = list(model_data.keys())
                            if 'signal' in model_data:
                                symbol_analysis[model_key]['signal'] = model_data['signal']
                            if 'confidence' in model_data:
                                symbol_analysis[model_key]['confidence'] = model_data['confidence']

                except Exception as e:
                    symbol_analysis[model_key] = {
                        'available': False,
                        'error': str(e)
                    }

            analysis['model_availability'][symbol] = symbol_analysis

        return analysis

    def _analyze_risk_metrics(self, baseline_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze risk characteristics of the strategy."""
        metrics = baseline_results.get('metrics', {})
        equity_curve = baseline_results.get('equity_curve', [])

        analysis = {
            'drawdown_analysis': {},
            'volatility_analysis': {},
            'risk_adjusted_returns': {}
        }

        if isinstance(metrics, dict):
            # Basic risk metrics
            analysis['risk_adjusted_returns'] = {
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'total_return': metrics.get('total_return', 0),
                'max_drawdown': metrics.get('max_drawdown', 0),
                'volatility': metrics.get('volatility', 0)
            }
        else:
            # Handle PerformanceMetrics object
            analysis['risk_adjusted_returns'] = {
                'sharpe_ratio': getattr(metrics, 'sharpe_ratio', 0),
                'total_return': getattr(metrics, 'total_return', 0),
                'max_drawdown': getattr(metrics, 'max_drawdown', 0),
                'volatility': getattr(metrics, 'volatility', 0)
            }

        # Analyze equity curve for drawdown patterns
        if equity_curve and len(equity_curve) > 1:
            equity_values = [point[1] if isinstance(point, (list, tuple)) else point for point in equity_curve]

            # Calculate rolling drawdowns
            peak = equity_values[0]
            drawdowns = []
            for value in equity_values:
                if value > peak:
                    peak = value
                drawdown = (value - peak) / peak
                drawdowns.append(drawdown)

            analysis['drawdown_analysis'] = {
                'max_drawdown': min(drawdowns),
                'avg_drawdown': np.mean([d for d in drawdowns if d < 0]),
                'drawdown_periods': sum(1 for d in drawdowns if d < -0.05),  # Periods with >5% drawdown
                'recovery_periods': self._calculate_recovery_periods(drawdowns)
            }

            # Volatility analysis
            returns = []
            for i in range(1, len(equity_values)):
                ret = (equity_values[i] - equity_values[i-1]) / equity_values[i-1]
                returns.append(ret)

            if returns:
                analysis['volatility_analysis'] = {
                    'daily_volatility': np.std(returns),
                    'annualized_volatility': np.std(returns) * np.sqrt(252),
                    'positive_days': sum(1 for r in returns if r > 0),
                    'negative_days': sum(1 for r in returns if r < 0),
                    'largest_gain': max(returns),
                    'largest_loss': min(returns)
                }

        return analysis

    def _calculate_recovery_periods(self, drawdowns: List[float]) -> Dict[str, Any]:
        """Calculate drawdown recovery periods."""
        recovery_periods = []
        in_drawdown = False
        drawdown_start = 0

        for i, dd in enumerate(drawdowns):
            if dd < 0 and not in_drawdown:
                in_drawdown = True
                drawdown_start = i
            elif dd >= 0 and in_drawdown:
                in_drawdown = False
                recovery_periods.append(i - drawdown_start)

        if recovery_periods:
            return {
                'avg_recovery_periods': np.mean(recovery_periods),
                'max_recovery_periods': max(recovery_periods),
                'total_drawdown_events': len(recovery_periods)
            }
        else:
            return {'avg_recovery_periods': 0, 'max_recovery_periods': 0, 'total_drawdown_events': 0}

    def _generate_improvement_suggestions(
        self,
        baseline_results: Dict[str, Any],
        signal_analysis: Dict[str, Any],
        model_analysis: Dict[str, Any],
        risk_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate specific improvement suggestions based on analysis."""
        suggestions = []

        # Analyze performance issues
        metrics = baseline_results.get('metrics', {})
        total_return = metrics.get('total_return', 0) if isinstance(metrics, dict) else getattr(metrics, 'total_return', 0)
        sharpe_ratio = metrics.get('sharpe_ratio', 0) if isinstance(metrics, dict) else getattr(metrics, 'sharpe_ratio', 0)
        win_rate = signal_analysis.get('signal_accuracy', {}).get('win_rate', 0)

        # Poor returns
        if total_return < -0.1:  # Less than -10%
            suggestions.append({
                'category': 'Performance',
                'priority': 'High',
                'issue': 'Significant negative returns',
                'suggestion': 'Review signal generation logic and consider reducing position sizes or adding stop-losses',
                'metric': f'Total Return: {total_return:.2%}'
            })

        # Poor Sharpe ratio
        if sharpe_ratio < 0:
            suggestions.append({
                'category': 'Risk-Adjusted Returns',
                'priority': 'High',
                'issue': 'Negative Sharpe ratio indicates poor risk-adjusted returns',
                'suggestion': 'Improve signal quality or reduce position volatility through better risk management',
                'metric': f'Sharpe Ratio: {sharpe_ratio:.3f}'
            })

        # Low win rate
        if win_rate < 0.4:
            suggestions.append({
                'category': 'Signal Quality',
                'priority': 'Medium',
                'issue': 'Low win rate suggests poor signal timing',
                'suggestion': 'Analyze signal thresholds and consider adding confirmation filters',
                'metric': f'Win Rate: {win_rate:.2%}'
            })

        # Model availability issues
        for symbol, models in model_analysis.get('model_availability', {}).items():
            unavailable_models = [model for model, data in models.items() if not data.get('available', False)]
            if unavailable_models:
                suggestions.append({
                    'category': 'Model Integration',
                    'priority': 'Medium',
                    'issue': f'Models not providing data for {symbol}',
                    'suggestion': f'Check and fix these models: {", ".join(unavailable_models)}',
                    'metric': f'{len(unavailable_models)} models unavailable'
                })

        # Signal strength issues
        signal_strength = signal_analysis.get('signal_strength', {})
        weak_signals = signal_strength.get('weak_signals', 0)
        total_signals = signal_analysis.get('total_signals', 1)

        if weak_signals / total_signals > 0.5:
            suggestions.append({
                'category': 'Signal Strength',
                'priority': 'Medium',
                'issue': 'Many weak signals generated',
                'suggestion': 'Increase signal thresholds or improve model weights to generate stronger signals',
                'metric': f'{weak_signals}/{total_signals} weak signals'
            })

        # Risk management issues
        max_drawdown = risk_analysis.get('risk_adjusted_returns', {}).get('max_drawdown', 0)
        if abs(max_drawdown) > 0.2:  # More than 20% drawdown
            suggestions.append({
                'category': 'Risk Management',
                'priority': 'High',
                'issue': 'Excessive drawdown',
                'suggestion': 'Implement stricter position sizing and stop-loss mechanisms',
                'metric': f'Max Drawdown: {max_drawdown:.2%}'
            })

        return suggestions

    async def _generate_detailed_breakdown(
        self,
        baseline_results: Dict[str, Any],
        symbols: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate detailed performance breakdown."""
        breakdown = {
            'time_series_analysis': {},
            'symbol_performance': {},
            'trade_analysis': {},
            'signal_timing': {}
        }

        trades = baseline_results.get('trades', [])
        equity_curve = baseline_results.get('equity_curve', [])

        # Trade analysis
        if trades:
            profitable_trades = [t for t in trades if t.get('pnl', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl', 0) < 0]

            breakdown['trade_analysis'] = {
                'total_trades': len(trades),
                'profitable_trades': len(profitable_trades),
                'losing_trades': len(losing_trades),
                'avg_profit_per_trade': np.mean([t.get('pnl', 0) for t in profitable_trades]) if profitable_trades else 0,
                'avg_loss_per_trade': np.mean([t.get('pnl', 0) for t in losing_trades]) if losing_trades else 0,
                'largest_win': max([t.get('pnl', 0) for t in trades]) if trades else 0,
                'largest_loss': min([t.get('pnl', 0) for t in trades]) if trades else 0,
                'profit_factor': sum([t.get('pnl', 0) for t in profitable_trades]) / abs(sum([t.get('pnl', 0) for t in losing_trades])) if losing_trades else float('inf')
            }

            # Symbol-specific performance
            symbol_performance = {}
            for symbol in symbols:
                symbol_trades = [t for t in trades if t.get('symbol') == symbol]
                if symbol_trades:
                    symbol_pnl = sum([t.get('pnl', 0) for t in symbol_trades])
                    symbol_performance[symbol] = {
                        'trades': len(symbol_trades),
                        'total_pnl': symbol_pnl,
                        'avg_pnl': symbol_pnl / len(symbol_trades),
                        'win_rate': sum(1 for t in symbol_trades if t.get('pnl', 0) > 0) / len(symbol_trades)
                    }

            breakdown['symbol_performance'] = symbol_performance

        # Time series analysis
        if equity_curve and len(equity_curve) > 1:
            # Convert equity curve to time series
            timestamps = []
            values = []

            for point in equity_curve:
                if isinstance(point, (list, tuple)) and len(point) >= 2:
                    timestamps.append(point[0])
                    values.append(point[1])

            if timestamps and values:
                # Calculate monthly returns
                df = pd.DataFrame({'timestamp': timestamps, 'equity': values})
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)

                # Resample to monthly
                monthly_equity = df.resample('M').last()
                monthly_returns = monthly_equity['equity'].pct_change().dropna()

                breakdown['time_series_analysis'] = {
                    'monthly_returns': monthly_returns.to_dict(),
                    'best_month': monthly_returns.max() if len(monthly_returns) > 0 else 0,
                    'worst_month': monthly_returns.min() if len(monthly_returns) > 0 else 0,
                    'positive_months': sum(1 for r in monthly_returns if r > 0),
                    'negative_months': sum(1 for r in monthly_returns if r < 0),
                    'avg_monthly_return': monthly_returns.mean() if len(monthly_returns) > 0 else 0
                }

        return breakdown

    def _save_analysis_results(self, analysis: Dict[str, Any], output_dir: str):
        """Save analysis results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save main analysis
        analysis_file = os.path.join(output_dir, f"strategy_analysis_{timestamp}.json")

        # Convert to serializable format
        serializable_analysis = self._make_serializable(analysis)

        with open(analysis_file, 'w') as f:
            json.dump(serializable_analysis, f, indent=2)

        # Generate text report
        report_file = os.path.join(output_dir, f"analysis_report_{timestamp}.txt")
        self._generate_analysis_report(analysis, report_file)

        # Save improvement suggestions separately
        suggestions_file = os.path.join(output_dir, f"improvement_suggestions_{timestamp}.json")
        with open(suggestions_file, 'w') as f:
            json.dump(analysis.get('improvement_suggestions', []), f, indent=2)

        logger.info(f"Analysis results saved to {output_dir}")

    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        else:
            return obj

    def _generate_analysis_report(self, analysis: Dict[str, Any], report_file: str):
        """Generate a comprehensive analysis report."""
        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("STRATEGY ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")

            # Basic info
            f.write(f"Strategy: {analysis.get('strategy_name', 'Unknown')}\n")
            f.write(f"Symbols: {', '.join(analysis.get('symbols', []))}\n")
            f.write(f"Period: {analysis.get('period', 'Unknown')}\n\n")

            # Performance summary
            if 'baseline_performance' in analysis:
                metrics = analysis['baseline_performance'].get('metrics', {})
                f.write("📊 PERFORMANCE SUMMARY\n")
                f.write("-" * 40 + "\n")

                if isinstance(metrics, dict):
                    total_return = metrics.get('total_return', 0)
                    sharpe_ratio = metrics.get('sharpe_ratio', 0)
                    max_drawdown = metrics.get('max_drawdown', 0)
                    win_rate = metrics.get('win_rate', 0)
                else:
                    total_return = getattr(metrics, 'total_return', 0)
                    sharpe_ratio = getattr(metrics, 'sharpe_ratio', 0)
                    max_drawdown = getattr(metrics, 'max_drawdown', 0)
                    win_rate = getattr(metrics, 'win_rate', 0)

                f.write(f"Total Return: {total_return:.2%}\n")
                f.write(f"Sharpe Ratio: {sharpe_ratio:.3f}\n")
                f.write(f"Max Drawdown: {max_drawdown:.2%}\n")
                f.write(f"Win Rate: {win_rate:.2%}\n\n")

            # Signal analysis
            if 'signal_analysis' in analysis:
                signal_data = analysis['signal_analysis']
                f.write("📈 SIGNAL ANALYSIS\n")
                f.write("-" * 40 + "\n")

                dist = signal_data.get('signal_distribution', {})
                f.write(f"Total Signals: {signal_data.get('total_signals', 0)}\n")
                f.write(f"Buy Signals: {dist.get('buy_signals', 0)} ({dist.get('buy_ratio', 0):.1%})\n")
                f.write(f"Sell Signals: {dist.get('sell_signals', 0)} ({dist.get('sell_ratio', 0):.1%})\n")

                strength = signal_data.get('signal_strength', {})
                if strength:
                    f.write(f"Average Signal Score: {strength.get('avg_score', 0):.3f}\n")
                    f.write(f"Strong Signals: {strength.get('strong_signals', 0)}\n")
                    f.write(f"Weak Signals: {strength.get('weak_signals', 0)}\n")

                f.write("\n")

            # Model performance
            if 'model_performance' in analysis:
                f.write("🤖 MODEL PERFORMANCE\n")
                f.write("-" * 40 + "\n")

                model_data = analysis['model_performance']
                for symbol, models in model_data.get('model_availability', {}).items():
                    f.write(f"\n{symbol}:\n")
                    for model_name, model_info in models.items():
                        status = "✅" if model_info.get('available', False) else "❌"
                        f.write(f"  {status} {model_name}\n")

                f.write("\n")

            # Improvement suggestions
            if 'improvement_suggestions' in analysis:
                suggestions = analysis['improvement_suggestions']
                f.write("💡 IMPROVEMENT SUGGESTIONS\n")
                f.write("-" * 40 + "\n")

                for i, suggestion in enumerate(suggestions, 1):
                    priority = suggestion.get('priority', 'Medium')
                    category = suggestion.get('category', 'General')
                    issue = suggestion.get('issue', 'Unknown issue')
                    fix = suggestion.get('suggestion', 'No suggestion')
                    metric = suggestion.get('metric', '')

                    f.write(f"\n{i}. [{priority}] {category}\n")
                    f.write(f"   Issue: {issue}\n")
                    f.write(f"   Fix: {fix}\n")
                    if metric:
                        f.write(f"   Metric: {metric}\n")

                f.write("\n")

            f.write("=" * 80 + "\n")

    async def _generate_analysis_visualizations(self, analysis: Dict[str, Any], output_dir: str):
        """Generate analysis visualizations."""
        try:
            plots_dir = os.path.join(output_dir, 'plots')
            os.makedirs(plots_dir, exist_ok=True)

            # Set style
            plt.style.use('seaborn-v0_8-darkgrid')

            # Signal distribution pie chart
            signal_data = analysis.get('signal_analysis', {})
            dist = signal_data.get('signal_distribution', {})

            if dist and any(dist.values()):
                fig, ax = plt.subplots(figsize=(8, 6))

                labels = []
                sizes = []

                if dist.get('buy_signals', 0) > 0:
                    labels.append(f"Buy ({dist['buy_signals']})")
                    sizes.append(dist['buy_signals'])

                if dist.get('sell_signals', 0) > 0:
                    labels.append(f"Sell ({dist['sell_signals']})")
                    sizes.append(dist['sell_signals'])

                if dist.get('hold_signals', 0) > 0:
                    labels.append(f"Hold ({dist['hold_signals']})")
                    sizes.append(dist['hold_signals'])

                if labels and sizes:
                    ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
                    ax.set_title('Signal Distribution')
                    plt.savefig(os.path.join(plots_dir, 'signal_distribution.png'), dpi=300, bbox_inches='tight')
                    plt.close()

            # Model availability heatmap
            model_data = analysis.get('model_performance', {})
            model_availability = model_data.get('model_availability', {})

            if model_availability:
                # Create availability matrix
                symbols = list(model_availability.keys())
                models = set()
                for symbol_models in model_availability.values():
                    models.update(symbol_models.keys())
                models = sorted(list(models))

                availability_matrix = []
                for symbol in symbols:
                    row = []
                    for model in models:
                        available = model_availability[symbol].get(model, {}).get('available', False)
                        row.append(1 if available else 0)
                    availability_matrix.append(row)

                if availability_matrix:
                    fig, ax = plt.subplots(figsize=(12, 6))
                    sns.heatmap(
                        availability_matrix,
                        xticklabels=[m.replace('_', ' ').title() for m in models],
                        yticklabels=symbols,
                        annot=True,
                        cmap='RdYlGn',
                        ax=ax
                    )
                    ax.set_title('Model Availability by Symbol')
                    plt.xticks(rotation=45, ha='right')
                    plt.savefig(os.path.join(plots_dir, 'model_availability.png'), dpi=300, bbox_inches='tight')
                    plt.close()

            logger.info(f"Analysis visualizations saved to {plots_dir}")

        except Exception as e:
            logger.error(f"Error generating analysis visualizations: {e}")


async def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Strategy Analyzer")
    parser.add_argument('--strategy', default='smart', help='Strategy to analyze')
    parser.add_argument('--symbols', nargs='+', default=['BTC-USDT'], help='Symbols to analyze')
    parser.add_argument('--start-date', default='2023-01-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2023-12-31', help='End date (YYYY-MM-DD)')
    parser.add_argument('--output-dir', default='results/strategy_analysis', help='Output directory')
    parser.add_argument('--config', default='config.yaml', help='Config file path')

    args = parser.parse_args()

    # Create analyzer
    analyzer = StrategyAnalyzer(config_path=args.config)

    # Run analysis
    results = await analyzer.analyze_strategy_performance(
        strategy_name=args.strategy,
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        output_dir=args.output_dir
    )

    print("\n🔍 Strategy Analysis Completed!")
    print(f"📁 Results saved to: {args.output_dir}")

    # Print key findings
    if 'improvement_suggestions' in results:
        suggestions = results['improvement_suggestions']
        high_priority = [s for s in suggestions if s.get('priority') == 'High']

        if high_priority:
            print(f"\n🚨 High Priority Issues Found: {len(high_priority)}")
            for suggestion in high_priority[:3]:  # Show top 3
                print(f"  • {suggestion.get('issue', 'Unknown')}")

        print(f"\n💡 Total Improvement Suggestions: {len(suggestions)}")


if __name__ == "__main__":
    asyncio.run(main())
