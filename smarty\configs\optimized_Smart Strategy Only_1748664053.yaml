market_data:
  fallback_source: binance
  max_data_age: 10
  min_update_frequency: 1
  primary_source: htx
  symbols:
  - BTC-USDT
metadata:
  account_balance: 100.0
  generated_at: '2025-05-30T23:00:47.158674'
  optimization_target: live_trading_safety
  optimizer_version: '1.0'
monitoring:
  alerts:
    connection_issues: true
    daily_loss_limit: 4.0
    large_loss: 2.0
  log_level: INFO
  save_trades: true
  track_performance: true
safety:
  cooling_off_period: 1800
  emergency_stop_loss: 10.0
  manual_override: true
  max_consecutive_losses: 3
  max_total_exposure: 60.0
strategies:
  order_flow:
    allocation: 20.0
    enabled: true
    max_position_size: 8.0
    min_confidence: 0.8
    risk_multiplier: 0.6
  smart_model_integrated:
    allocation: 50.0
    enabled: true
    max_position_size: 15.0
    min_confidence: 0.75
    risk_multiplier: 1.0
  smart_strategy_only:
    allocation: 30.0
    enabled: true
    max_position_size: 10.0
    min_confidence: 0.7
    risk_multiplier: 0.8
trading:
  account_balance: 100.0
  leverage: 1
  max_daily_loss: 5.0
  max_position_size: 2.0
  min_position_size: 2.0
  mode: live
  position_sizing:
    base_position_size: 5.0
    confidence_multiplier:
      '0.6': 0.8
      '0.7': 1.0
      '0.8': 1.2
      '0.9': 1.5
    drawdown_adjustment: true
    max_position_size: 2.0
    method: fixed_dollar
    min_position_size: 2.0
    volatility_adjustment: true
    winning_streak_adjustment: false
  risk_management:
    correlation_limit: 0.7
    daily_trade_limit: 10
    hourly_trade_limit: 3
    max_daily_loss: 5.0
    max_drawdown: 15.0
    minimum_gap_minutes: 15
    news_filter: true
    position_sizing: fixed_dollar
    stop_loss_percent: 1.0
    take_profit_percent: 2.0
    trailing_distance: 0.5
    trailing_stop: true
    volatility_filter: true
    weekend_trading: false
  trading_schedule:
    active_hours:
      end: '23:59'
      start: 00:00
    avoid_periods:
    - description: Low liquidity
      end: 06:00
      start: 02:00
    high_activity_periods:
    - description: Asian session
      end: '12:00'
      start: 08:00
    - description: European session
      end: '17:00'
      start: '13:00'
    - description: US session
      end: '24:00'
      start: '20:00'
    holiday_trading: false
    timezone: UTC
    weekend_trading: false
