{% extends "base.html" %}

{% block title %}Money Circle - Login{% endblock %}

{% block description %}Login to Money Circle Investment Club - Professional Trading Platform{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/auth.css">
{% endblock %}

{% block body_class %}auth-page login-page{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <div class="logo-icon">E</div>
                <h1>Money Circle</h1>
            </div>
            <h2>Investment Club Login</h2>
            <p>Access your professional trading dashboard</p>
        </div>
        
        {% if error %}
        <div class="error-message">
            {% if error == 'missing_fields' %}
                Please fill in all required fields.
            {% elif error == 'invalid_credentials' %}
                Invalid username or password. Please try again.
            {% elif error == 'server_error' %}
                <PERSON><PERSON> failed. Please try again later.
            {% else %}
                {{ error }}
            {% endif %}
        </div>
        {% endif %}
        
        <form method="post" action="/login" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required 
                       placeholder="Enter your username"
                       autocomplete="username">
                <div class="input-icon">👤</div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required 
                       placeholder="Enter your password"
                       autocomplete="current-password">
                <div class="input-icon">🔒</div>
            </div>
            
            <button type="submit" class="auth-btn">
                <span class="btn-text">Login</span>
                <span class="btn-loading" style="display: none;">Logging in...</span>
            </button>
        </form>
        
        <div class="auth-links">
            <p>Don't have an account? <a href="/register">Register here</a></p>
        </div>
        
        <div class="demo-accounts">
            <h4>Demo Accounts</h4>
            <p>Try these demo accounts to explore the platform:</p>
            <div class="demo-list">
                <div class="demo-account" onclick="fillDemoCredentials('trader_alex', 'securepass123')">
                    <strong>trader_alex</strong> - Algorithmic Trader
                </div>
                <div class="demo-account" onclick="fillDemoCredentials('crypto_sarah', 'securepass123')">
                    <strong>crypto_sarah</strong> - Swing Trader
                </div>
                <div class="demo-account" onclick="fillDemoCredentials('epinnox', 'securepass123')">
                    <strong>epinnox</strong> - Admin Account
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Login form handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.auth-form');
    const submitBtn = document.querySelector('.auth-btn');
    const btnText = document.querySelector('.btn-text');
    const btnLoading = document.querySelector('.btn-loading');
    
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline';
        
        // Re-enable after 5 seconds as fallback
        setTimeout(() => {
            submitBtn.disabled = false;
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }, 5000);
    });
    
    // Focus first input
    document.getElementById('username').focus();
});

// Demo account helper
function fillDemoCredentials(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    
    // Add visual feedback
    const demoAccounts = document.querySelectorAll('.demo-account');
    demoAccounts.forEach(account => account.classList.remove('selected'));
    event.target.classList.add('selected');
    
    // Show success message
    MoneyCircle.ui.showToast(`Demo credentials filled for ${username}`, 'success', 2000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key submits form
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
        const form = document.querySelector('.auth-form');
        if (form && document.activeElement.tagName !== 'BUTTON') {
            form.submit();
        }
    }
});
</script>

<style>
/* Additional login-specific styles */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    font-size: 1.5rem;
}

.auth-header h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.auth-header h2 {
    color: #e2e8f0;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0.5rem 0;
}

.auth-header p {
    color: #94a3b8;
    font-size: 1rem;
    margin: 0;
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 1.2rem;
    pointer-events: none;
}

.demo-accounts {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-accounts h4 {
    color: #8b5cf6;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.demo-accounts p {
    color: #94a3b8;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.demo-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.demo-account {
    padding: 0.75rem 1rem;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #e2e8f0;
}

.demo-account:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: #8b5cf6;
    transform: translateY(-1px);
}

.demo-account.selected {
    background: rgba(139, 92, 246, 0.3);
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

.demo-account strong {
    color: #8b5cf6;
}
</style>
{% endblock %}
