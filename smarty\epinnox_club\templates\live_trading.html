<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Trading - Money Circle</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <style>
        .live-trading-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto 1fr;
            gap: 20px;
            padding: 20px;
            height: calc(100vh - 100px);
        }

        .position-panel {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #333;
        }

        .position-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .position-title {
            font-size: 24px;
            font-weight: 700;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .live-indicator {
            width: 12px;
            height: 12px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .position-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-label {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #fff;
        }

        .stat-value.positive { color: #00ff88; }
        .stat-value.negative { color: #ff4757; }

        .automation-panel {
            background: linear-gradient(135deg, #2c2c54 0%, #40407a 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #333;
        }

        .controls-panel {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #333;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .automation-grid {
            display: grid;
            gap: 15px;
        }

        .automation-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .automation-checkbox {
            width: 18px;
            height: 18px;
            margin-right: 10px;
        }

        .automation-label {
            flex: 1;
            color: #fff;
            font-size: 14px;
        }

        .automation-input {
            width: 80px;
            padding: 5px 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
        }

        .control-buttons {
            display: grid;
            gap: 15px;
        }

        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-long {
            background: linear-gradient(135deg, #00ff88, #00d4aa);
            color: #000;
        }

        .btn-short {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: #fff;
        }

        .btn-close {
            background: linear-gradient(135deg, #ffa502, #ff6348);
            color: #fff;
        }

        .btn-emergency {
            background: linear-gradient(135deg, #ff4757, #c44569);
            color: #fff;
            font-weight: 700;
            text-transform: uppercase;
        }

        .btn-emergency:hover {
            background: linear-gradient(135deg, #ff3742, #b33654);
            transform: scale(1.05);
        }

        .alerts-section {
            grid-column: 1 / -1;
            background: rgba(255, 77, 87, 0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 77, 87, 0.3);
            max-height: 150px;
            overflow-y: auto;
        }

        .alert-item {
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 77, 87, 0.2);
            border-radius: 6px;
            color: #fff;
            font-size: 13px;
            border-left: 3px solid #ff4757;
        }

        .market-data {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 15px;
        }

        .price-display {
            font-size: 28px;
            font-weight: 700;
            color: #00ff88;
        }

        .price-change {
            font-size: 16px;
            font-weight: 600;
        }

        .price-change.positive { color: #00ff88; }
        .price-change.negative { color: #ff4757; }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #888;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
        }

        .status-dot.disconnected {
            background: #ff4757;
        }

        .leverage-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .leverage-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            font-size: 12px;
        }

        .leverage-btn.active {
            background: #00ff88;
            color: #000;
        }

        .order-size-input {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #fff;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <h1>💰 Money Circle</h1>
                <span class="header-subtitle">Live Trading Interface</span>
            </div>
            <div class="header-right">
                <div class="connection-status">
                    <div class="status-dot" id="connectionStatus"></div>
                    <span id="connectionText">Connected to HTX</span>
                </div>
                <div class="user-info">
                    <span>{{ username }}</span>
                    <a href="/logout" class="logout-btn">Logout</a>
                </div>
            </div>
        </header>

        <!-- Live Trading Interface -->
        <div class="live-trading-container">
            <!-- Position Panel -->
            <div class="position-panel">
                <div class="position-header">
                    <div class="position-title">
                        <div class="live-indicator"></div>
                        DOGE/USDT Futures Position
                    </div>
                    <div class="market-data">
                        <div class="price-display" id="currentPrice">$0.000000</div>
                        <div class="price-change" id="priceChange">+0.00%</div>
                    </div>
                </div>

                <div class="position-stats">
                    <div class="stat-card">
                        <div class="stat-label">Position Size</div>
                        <div class="stat-value" id="positionSize">0 DOGE</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Entry Price</div>
                        <div class="stat-value" id="entryPrice">$0.000000</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">PnL (USD)</div>
                        <div class="stat-value" id="pnlUsd">$0.00</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">PnL (%)</div>
                        <div class="stat-value" id="pnlPercent">0.00%</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Leverage</div>
                        <div class="stat-value" id="leverage">1x</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Account Balance</div>
                        <div class="stat-value" id="accountBalance">$0.00</div>
                    </div>
                </div>

                <!-- Alerts Section -->
                <div class="alerts-section" id="alertsSection" style="display: none;">
                    <h4>🚨 Active Alerts</h4>
                    <div id="alertsList"></div>
                </div>
            </div>

            <!-- Automation Panel -->
            <div class="automation-panel">
                <div class="panel-title">
                    🤖 Automated Functions
                </div>
                
                <div class="automation-grid">
                    <!-- Auto-Close Functions -->
                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoClosePnlUsd">
                        <label class="automation-label">Auto-close by PnL ($)</label>
                        <input type="number" class="automation-input" id="pnlUsdThreshold" placeholder="100" step="10">
                    </div>

                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoClosePnlPercent">
                        <label class="automation-label">Auto-close by PnL (%)</label>
                        <input type="number" class="automation-input" id="pnlPercentThreshold" placeholder="5" step="0.5">
                    </div>

                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoCloseTime">
                        <label class="automation-label">Auto-close by time (min)</label>
                        <input type="number" class="automation-input" id="timeThreshold" placeholder="30" step="5">
                    </div>

                    <!-- Post-Order Automation -->
                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoTakeProfit">
                        <label class="automation-label">Auto Take Profit (%)</label>
                        <input type="number" class="automation-input" id="takeProfitPercent" placeholder="2" step="0.1">
                    </div>

                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoStopLoss">
                        <label class="automation-label">Auto Stop Loss (%)</label>
                        <input type="number" class="automation-input" id="stopLossPercent" placeholder="1" step="0.1">
                    </div>

                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="trailingStopLoss">
                        <label class="automation-label">Trailing Stop Loss (%)</label>
                        <input type="number" class="automation-input" id="trailingPercent" placeholder="0.5" step="0.1">
                    </div>

                    <!-- Scalping Features -->
                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="breakevenStop">
                        <label class="automation-label">Break-even stop (%)</label>
                        <input type="number" class="automation-input" id="breakevenThreshold" placeholder="1" step="0.1">
                    </div>

                    <div class="automation-item">
                        <input type="checkbox" class="automation-checkbox" id="autoScalingOut">
                        <label class="automation-label">Auto-scaling out (%)</label>
                        <input type="number" class="automation-input" id="scalingPercent" placeholder="1.5" step="0.1">
                    </div>
                </div>
            </div>

            <!-- Manual Controls Panel -->
            <div class="controls-panel">
                <div class="panel-title">
                    📊 Manual Trading Controls
                </div>

                <!-- Leverage Selection -->
                <div class="leverage-selector">
                    <span style="color: #fff; font-size: 14px;">Leverage:</span>
                    <div class="leverage-btn" data-leverage="5">5x</div>
                    <div class="leverage-btn active" data-leverage="10">10x</div>
                    <div class="leverage-btn" data-leverage="20">20x</div>
                    <div class="leverage-btn" data-leverage="50">50x</div>
                </div>

                <!-- Order Size -->
                <input type="number" class="order-size-input" id="orderSize" placeholder="Order Size (DOGE)" step="100">

                <!-- Trading Buttons -->
                <div class="control-buttons">
                    <button class="control-btn btn-long" onclick="placeLongOrder()">
                        📈 LONG DOGE/USDT
                    </button>
                    
                    <button class="control-btn btn-short" onclick="placeShortOrder()">
                        📉 SHORT DOGE/USDT
                    </button>
                    
                    <button class="control-btn btn-close" onclick="closePosition()">
                        🔄 CLOSE POSITION
                    </button>
                    
                    <button class="control-btn btn-emergency" onclick="emergencyCloseAll()">
                        🚨 EMERGENCY CLOSE ALL
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let selectedLeverage = 10;
        let tradingState = {};
        let wsConnection = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeLeverageSelector();
            initializeAutomationControls();
            connectWebSocket();
            startDataUpdates();
        });

        function initializeLeverageSelector() {
            document.querySelectorAll('.leverage-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.leverage-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedLeverage = parseInt(this.dataset.leverage);
                });
            });
        }

        function initializeAutomationControls() {
            // Add event listeners for automation checkboxes
            document.querySelectorAll('.automation-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateAutomationSettings();
                });
            });

            // Add event listeners for automation inputs
            document.querySelectorAll('.automation-input').forEach(input => {
                input.addEventListener('change', function() {
                    updateAutomationSettings();
                });
            });
        }

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/live-trading`;
            
            wsConnection = new WebSocket(wsUrl);
            
            wsConnection.onopen = function() {
                updateConnectionStatus(true);
            };
            
            wsConnection.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateTradingInterface(data);
            };
            
            wsConnection.onclose = function() {
                updateConnectionStatus(false);
                // Reconnect after 3 seconds
                setTimeout(connectWebSocket, 3000);
            };
        }

        function updateConnectionStatus(connected) {
            const statusDot = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusDot.classList.remove('disconnected');
                statusText.textContent = 'Connected to HTX';
            } else {
                statusDot.classList.add('disconnected');
                statusText.textContent = 'Disconnected';
            }
        }

        function updateTradingInterface(data) {
            tradingState = data;
            
            // Update market data
            if (data.current_price) {
                document.getElementById('currentPrice').textContent = `$${data.current_price.toFixed(6)}`;
            }
            
            // Update position data
            if (data.position) {
                const pos = data.position;
                document.getElementById('positionSize').textContent = `${pos.size} DOGE`;
                document.getElementById('entryPrice').textContent = `$${pos.entry_price.toFixed(6)}`;
                
                const pnlUsdElement = document.getElementById('pnlUsd');
                pnlUsdElement.textContent = `$${pos.pnl_usd.toFixed(2)}`;
                pnlUsdElement.className = `stat-value ${pos.pnl_usd >= 0 ? 'positive' : 'negative'}`;
                
                const pnlPercentElement = document.getElementById('pnlPercent');
                pnlPercentElement.textContent = `${pos.pnl_percent.toFixed(2)}%`;
                pnlPercentElement.className = `stat-value ${pos.pnl_percent >= 0 ? 'positive' : 'negative'}`;
                
                document.getElementById('leverage').textContent = `${pos.leverage}x`;
            } else {
                // No position
                document.getElementById('positionSize').textContent = '0 DOGE';
                document.getElementById('entryPrice').textContent = '$0.000000';
                document.getElementById('pnlUsd').textContent = '$0.00';
                document.getElementById('pnlPercent').textContent = '0.00%';
                document.getElementById('leverage').textContent = '1x';
            }
            
            // Update account balance
            if (data.account_balance) {
                document.getElementById('accountBalance').textContent = `$${data.account_balance.total_balance.toFixed(2)}`;
            }
            
            // Update alerts
            updateAlerts(data.active_alerts || []);
        }

        function updateAlerts(alerts) {
            const alertsSection = document.getElementById('alertsSection');
            const alertsList = document.getElementById('alertsList');
            
            if (alerts.length > 0) {
                alertsSection.style.display = 'block';
                alertsList.innerHTML = alerts.map(alert => 
                    `<div class="alert-item">${alert}</div>`
                ).join('');
            } else {
                alertsSection.style.display = 'none';
            }
        }

        function updateAutomationSettings() {
            const settings = {};
            
            // Collect all automation settings
            if (document.getElementById('autoClosePnlUsd').checked) {
                settings.auto_close_pnl_usd = {
                    enabled: true,
                    threshold_usd: parseFloat(document.getElementById('pnlUsdThreshold').value) || 100
                };
            }
            
            if (document.getElementById('autoClosePnlPercent').checked) {
                settings.auto_close_pnl_percent = {
                    enabled: true,
                    threshold_percent: parseFloat(document.getElementById('pnlPercentThreshold').value) || 5
                };
            }
            
            if (document.getElementById('trailingStopLoss').checked) {
                settings.trailing_stop_loss = {
                    enabled: true,
                    trail_distance_percent: parseFloat(document.getElementById('trailingPercent').value) || 0.5
                };
            }
            
            // Send to server
            fetch('/api/live-trading/automation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            });
        }

        async function placeLongOrder() {
            const orderSize = parseFloat(document.getElementById('orderSize').value);
            if (!orderSize) {
                alert('Please enter order size');
                return;
            }
            
            const response = await fetch('/api/live-trading/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    side: 'buy',
                    amount: orderSize,
                    leverage: selectedLeverage
                })
            });
            
            const result = await response.json();
            if (result.success) {
                alert('Long order placed successfully!');
            } else {
                alert(`Order failed: ${result.error}`);
            }
        }

        async function placeShortOrder() {
            const orderSize = parseFloat(document.getElementById('orderSize').value);
            if (!orderSize) {
                alert('Please enter order size');
                return;
            }
            
            const response = await fetch('/api/live-trading/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    side: 'sell',
                    amount: orderSize,
                    leverage: selectedLeverage
                })
            });
            
            const result = await response.json();
            if (result.success) {
                alert('Short order placed successfully!');
            } else {
                alert(`Order failed: ${result.error}`);
            }
        }

        async function closePosition() {
            if (!tradingState.position) {
                alert('No open position to close');
                return;
            }
            
            if (!confirm('Are you sure you want to close the current position?')) {
                return;
            }
            
            const response = await fetch('/api/live-trading/close-position', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const result = await response.json();
            if (result.success) {
                alert('Position closed successfully!');
            } else {
                alert(`Close failed: ${result.error}`);
            }
        }

        async function emergencyCloseAll() {
            if (!confirm('🚨 EMERGENCY CLOSE ALL POSITIONS?\n\nThis will immediately close all open positions. This action cannot be undone.')) {
                return;
            }
            
            const response = await fetch('/api/live-trading/emergency-close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const result = await response.json();
            if (result.success) {
                alert('🚨 Emergency close executed!');
            } else {
                alert(`Emergency close failed: ${result.error}`);
            }
        }

        function startDataUpdates() {
            // Update data every second
            setInterval(() => {
                if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                    wsConnection.send(JSON.stringify({type: 'get_trading_state'}));
                }
            }, 1000);
        }
    </script>
</body>
</html>
