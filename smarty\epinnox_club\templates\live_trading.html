<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Trading - Money Circle</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/unified_header.css">
    <script src="/static/js/header_navigation.js"></script>
    <!-- TradingView Charting Library -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <style>
        /* Professional Trading Platform Layout */
        body {
            margin: 0;
            padding: 0;
            background: #0b0e11;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        .trading-platform {
            display: grid;
            grid-template-areas:
                "header header header header"
                "market-info market-info market-info market-info"
                "chart chart orderbook positions"
                "chart chart trading-panel positions"
                "footer footer footer footer";
            grid-template-columns: 2fr 2fr 1fr 1fr;
            grid-template-rows: auto auto 2fr 1fr auto;
            height: 100vh;
            gap: 1px;
            background: #1a1a1a;
        }

        .trading-header {
            grid-area: header;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-bottom: 1px solid #333;
        }

        .market-info-bar {
            grid-area: market-info;
            background: #0f1419;
            padding: 12px 20px;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chart-container {
            grid-area: chart;
            background: #0f1419;
            position: relative;
            min-height: 400px;
        }

        .orderbook-container {
            grid-area: orderbook;
            background: #0f1419;
            border-left: 1px solid #333;
            display: flex;
            flex-direction: column;
        }

        .trading-panel {
            grid-area: trading-panel;
            background: #0f1419;
            border-left: 1px solid #333;
            border-top: 1px solid #333;
            padding: 20px;
        }

        .positions-container {
            grid-area: positions;
            background: #0f1419;
            border-left: 1px solid #333;
            display: flex;
            flex-direction: column;
        }

        .trading-footer {
            grid-area: footer;
            background: #0b0e11;
            border-top: 1px solid #333;
        }

        /* Market Info Bar Styling */
        .market-ticker {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .ticker-symbol {
            font-size: 18px;
            font-weight: 700;
            color: #ffffff;
        }

        .ticker-price {
            font-size: 24px;
            font-weight: 700;
            color: #00ff88;
        }

        .ticker-change {
            font-size: 14px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .ticker-change.positive {
            color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }

        .ticker-change.negative {
            color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
        }

        .market-stats {
            display: flex;
            gap: 20px;
            font-size: 12px;
            color: #888;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-label {
            margin-bottom: 2px;
        }

        .stat-value {
            color: #ffffff;
            font-weight: 600;
        }

        /* Chart Container */
        .chart-header {
            padding: 15px 20px;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chart-btn.active {
            background: #00ff88;
            color: #000000;
        }

        #tradingview_chart {
            height: calc(100% - 60px);
        }

        /* Order Book Styling */
        .orderbook-header {
            padding: 15px 20px;
            border-bottom: 1px solid #333;
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .orderbook-content {
            flex: 1;
            padding: 10px;
        }

        .orderbook-table {
            width: 100%;
            font-size: 12px;
        }

        .orderbook-table th {
            color: #888;
            font-weight: 500;
            padding: 8px 4px;
            text-align: right;
        }

        .orderbook-table td {
            padding: 2px 4px;
            text-align: right;
        }

        .ask-row {
            color: #ff4757;
        }

        .bid-row {
            color: #00ff88;
        }

        .current-price {
            text-align: center;
            padding: 10px;
            font-size: 16px;
            font-weight: 700;
            color: #00ff88;
            border: 1px solid #333;
            margin: 10px 0;
        }

        /* Trading Panel Styling */
        .trading-panel-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #333;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .trading-tabs {
            display: flex;
            gap: 2px;
            margin-bottom: 20px;
        }

        .trading-tab {
            flex: 1;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #888;
            text-align: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .trading-tab.active {
            background: #00ff88;
            color: #000000;
            border-color: #00ff88;
        }

        .trading-tab.buy {
            border-radius: 4px 0 0 4px;
        }

        .trading-tab.sell {
            border-radius: 0 4px 4px 0;
        }

        .order-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-label {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
        }

        .form-input {
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: #ffffff;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.05);
        }

        .leverage-selector {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-top: 5px;
        }

        .leverage-btn {
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: #888;
            font-size: 12px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
        }

        .leverage-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .leverage-btn.active {
            background: #00ff88;
            color: #000000;
            border-color: #00ff88;
        }

        .order-summary {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .summary-label {
            color: #888;
        }

        .summary-value {
            color: #ffffff;
            font-weight: 500;
        }

        .order-button {
            padding: 15px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .order-button.buy {
            background: linear-gradient(135deg, #00ff88, #00d4aa);
            color: #000000;
        }

        .order-button.sell {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: #ffffff;
        }

        .order-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .order-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Enhanced Form Controls */
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin: 10px 0;
        }

        .form-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #00ff88;
        }

        .checkbox-label {
            color: #ffffff;
            font-size: 14px;
        }

        /* Connection Status */
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            color: #00ff88;
        }

        .status-disconnected {
            color: #ff4757;
        }

        /* Enhanced Position Controls */
        .positions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #333;
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .position-controls {
            display: flex;
            gap: 10px;
        }

        .position-control-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .position-control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Position Tabs */
        .positions-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid #333;
        }

        .position-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            color: #888;
            border-right: 1px solid #333;
            transition: all 0.3s ease;
        }

        .position-tab:last-child {
            border-right: none;
        }

        .position-tab.active {
            color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }

        .position-tab:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Tab Content */
        .tab-content {
            display: none;
            padding: 15px;
        }

        .tab-content.active {
            display: block;
        }

        /* Enhanced Order Book */
        .orderbook-table {
            width: 100%;
            font-size: 11px;
            font-family: 'Courier New', monospace;
        }

        .orderbook-table th {
            color: #888;
            font-weight: 500;
            padding: 6px 4px;
            text-align: right;
            border-bottom: 1px solid #333;
        }

        .orderbook-table td {
            padding: 1px 4px;
            text-align: right;
        }

        .ask-row {
            color: #ff4757;
            background: rgba(255, 71, 87, 0.05);
        }

        .bid-row {
            color: #00ff88;
            background: rgba(0, 255, 136, 0.05);
        }

        .current-price {
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: 700;
            color: #00ff88;
            border: 1px solid #333;
            margin: 8px 0;
            background: rgba(0, 255, 136, 0.1);
        }

        /* Enhanced Position Items */
        .position-item, .order-item {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .position-item:hover, .order-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .position-header, .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .position-symbol, .order-symbol {
            font-size: 13px;
            font-weight: 600;
            color: #ffffff;
        }

        .position-side, .order-side {
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }

        .position-side.long, .order-side.buy {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }

        .position-side.short, .order-side.sell {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
        }

        .order-type {
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .position-details, .order-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            font-size: 11px;
            margin-bottom: 8px;
        }

        .position-detail, .order-detail {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            color: #888;
        }

        .detail-value {
            color: #ffffff;
            font-weight: 500;
        }

        .detail-value.positive {
            color: #00ff88;
        }

        .detail-value.negative {
            color: #ff4757;
        }

        .position-actions, .order-actions {
            display: flex;
            gap: 6px;
        }

        .position-btn, .order-btn {
            flex: 1;
            padding: 6px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.05);
            color: #ffffff;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .position-btn:hover, .order-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .position-btn.close, .order-btn.cancel {
            background: rgba(255, 71, 87, 0.2);
            border-color: #ff4757;
            color: #ff4757;
        }

        .position-btn.close:hover, .order-btn.cancel:hover {
            background: rgba(255, 71, 87, 0.3);
        }

        /* No Data State */
        .no-data {
            text-align: center;
            color: #888;
            padding: 20px;
            font-style: italic;
            font-size: 12px;
        }

        /* Positions Container */
        .positions-header {
            padding: 15px 20px;
            border-bottom: 1px solid #333;
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .positions-content {
            flex: 1;
            padding: 15px;
        }

        .position-item {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .position-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .position-symbol {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .position-side {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .position-side.long {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }

        .position-side.short {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
        }

        .position-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 12px;
        }

        .position-detail {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            color: #888;
        }

        .detail-value {
            color: #ffffff;
            font-weight: 500;
        }

        .detail-value.positive {
            color: #00ff88;
        }

        .detail-value.negative {
            color: #ff4757;
        }

        .position-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .position-btn {
            flex: 1;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.05);
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .position-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .position-btn.close {
            background: rgba(255, 71, 87, 0.2);
            border-color: #ff4757;
            color: #ff4757;
        }

        .position-btn.close:hover {
            background: rgba(255, 71, 87, 0.3);
        }

        /* Automation Panel */
        .automation-panel {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .automation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .automation-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .automation-toggle {
            width: 40px;
            height: 20px;
            background: #333;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .automation-toggle.active {
            background: #00ff88;
        }

        .automation-toggle::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: #ffffff;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
        }

        .automation-toggle.active::after {
            left: 22px;
        }

        .automation-controls {
            display: grid;
            gap: 10px;
        }

        .automation-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
        }

        .automation-label {
            font-size: 12px;
            color: #888;
        }

        .automation-input {
            width: 80px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .trading-platform {
                grid-template-areas:
                    "header header"
                    "market-info market-info"
                    "chart chart"
                    "orderbook trading-panel"
                    "positions positions"
                    "footer footer";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto 2fr 1fr 1fr auto;
            }
        }

        @media (max-width: 768px) {
            .trading-platform {
                grid-template-areas:
                    "header"
                    "market-info"
                    "chart"
                    "trading-panel"
                    "orderbook"
                    "positions"
                    "footer";
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 2fr auto auto auto auto;
            }
        }
    </style>
</head>
<body>
    <div class="trading-platform">
        <!-- Header -->
        <div class="trading-header">
            {% include 'components/header.html' %}
        </div>

        <!-- Market Info Bar -->
        <div class="market-info-bar">
            <div class="market-ticker">
                <div class="ticker-symbol" id="market-symbol">DOGE/USDT</div>
                <div class="ticker-price" id="market-price">$0.000000</div>
                <div class="ticker-change" id="market-change">+0.00%</div>
            </div>
            <div class="connection-status">
                <span id="connection-status">🔴 Connecting...</span>
            </div>
            <div class="market-stats">
                <div class="stat-item">
                    <div class="stat-label">24h High</div>
                    <div class="stat-value" id="high24h">$0.000000</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">24h Low</div>
                    <div class="stat-value" id="low24h">$0.000000</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">24h Volume</div>
                    <div class="stat-value" id="volume24h">0</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Funding Rate</div>
                    <div class="stat-value" id="fundingRate">0.0000%</div>
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container">
            <div class="chart-header">
                <div class="chart-title">DOGE/USDT Perpetual</div>
                <div class="chart-controls">
                    <div class="chart-btn active" data-timeframe="1m">1m</div>
                    <div class="chart-btn" data-timeframe="5m">5m</div>
                    <div class="chart-btn" data-timeframe="15m">15m</div>
                    <div class="chart-btn" data-timeframe="1h">1h</div>
                    <div class="chart-btn" data-timeframe="4h">4h</div>
                    <div class="chart-btn" data-timeframe="1d">1D</div>
                </div>
            </div>
            <div id="tradingview_chart"></div>
        </div>

        <!-- Order Book -->
        <div class="orderbook-container">
            <div class="orderbook-header">Order Book</div>
            <div class="orderbook-content">
                <table class="orderbook-table">
                    <thead>
                        <tr>
                            <th>Price (USDT)</th>
                            <th>Size (DOGE)</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody id="orderbook-asks">
                        <!-- Ask orders will be populated here -->
                    </tbody>
                </table>
                <div class="current-price" id="orderbookPrice">$0.000000</div>
                <table class="orderbook-table">
                    <tbody id="orderbook-bids">
                        <!-- Bid orders will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Trading Panel -->
        <div class="trading-panel">
            <div class="trading-panel-header">
                <div class="panel-title">🚀 HTX Futures Trading</div>
                <div class="trading-tabs">
                    <div class="trading-tab buy active" id="buy-tab">Buy / Long</div>
                    <div class="trading-tab sell" id="sell-tab">Sell / Short</div>
                </div>
            </div>

            <form class="order-form" id="orderForm">
                <!-- Order Type Selection -->
                <div class="form-group">
                    <label class="form-label">Order Type</label>
                    <select class="form-input" id="order-type">
                        <option value="market">Market Order</option>
                        <option value="limit">Limit Order</option>
                        <option value="stop">Stop Order</option>
                    </select>
                </div>

                <!-- Leverage Selection -->
                <div class="form-group">
                    <label class="form-label">Leverage</label>
                    <div class="leverage-selector">
                        <div class="leverage-btn" data-leverage="1">1x</div>
                        <div class="leverage-btn" data-leverage="5">5x</div>
                        <div class="leverage-btn active" data-leverage="10">10x</div>
                        <div class="leverage-btn" data-leverage="20">20x</div>
                        <div class="leverage-btn" data-leverage="50">50x</div>
                        <div class="leverage-btn" data-leverage="100">100x</div>
                    </div>
                </div>

                <!-- Order Amount -->
                <div class="form-group">
                    <label class="form-label">Amount (DOGE)</label>
                    <input type="number" class="form-input" id="order-amount" placeholder="0" step="1" min="1">
                </div>

                <!-- Price (for limit orders) -->
                <div class="form-group" id="price-group" style="display: none;">
                    <label class="form-label">Price (USDT)</label>
                    <input type="number" class="form-input" id="order-price" placeholder="0.000000" step="0.000001">
                </div>

                <!-- Stop Price (for stop orders) -->
                <div class="form-group" id="stop-price-group" style="display: none;">
                    <label class="form-label">Stop Price (USDT)</label>
                    <input type="number" class="form-input" id="stop-price" placeholder="0.000000" step="0.000001">
                </div>

                <!-- Advanced Options -->
                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="reduce-only">
                        <span class="checkbox-label">Reduce Only</span>
                    </label>
                </div>

                <!-- Order Summary -->
                <div class="order-summary">
                    <div class="summary-row">
                        <span class="summary-label">Amount:</span>
                        <span class="summary-value" id="summary-amount">0 DOGE</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Price:</span>
                        <span class="summary-value" id="summary-price">$0.000000</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Notional:</span>
                        <span class="summary-value" id="summary-notional">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Margin:</span>
                        <span class="summary-value" id="summary-margin">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Leverage:</span>
                        <span class="summary-value" id="summary-leverage">10x</span>
                    </div>
                </div>

                <button type="button" class="order-button buy" id="place-order-btn">
                    BUY / LONG
                </button>
            </form>

            <!-- Automation Panel -->
            <div class="automation-panel">
                <div class="automation-header">
                    <div class="automation-title">🤖 Smart Trading</div>
                    <div class="automation-toggle" id="automationToggle"></div>
                </div>
                <div class="automation-controls" id="automationControls">
                    <div class="automation-item">
                        <span class="automation-label">Take Profit (%)</span>
                        <input type="number" class="automation-input" id="takeProfitPercent" placeholder="2.0" step="0.1">
                    </div>
                    <div class="automation-item">
                        <span class="automation-label">Stop Loss (%)</span>
                        <input type="number" class="automation-input" id="stopLossPercent" placeholder="1.0" step="0.1">
                    </div>
                    <div class="automation-item">
                        <span class="automation-label">Trailing Stop (%)</span>
                        <input type="number" class="automation-input" id="trailingStopPercent" placeholder="0.5" step="0.1">
                    </div>
                </div>
            </div>
        </div>

        <!-- Positions Container -->
        <div class="positions-container">
            <div class="positions-header">
                <span>📊 Positions & Orders</span>
                <div class="position-controls">
                    <button class="position-control-btn" id="close-all-positions">Close All</button>
                    <button class="position-control-btn" id="cancel-all-orders">Cancel All</button>
                </div>
            </div>
            <div class="positions-tabs">
                <div class="position-tab active" data-tab="positions">Positions</div>
                <div class="position-tab" data-tab="orders">Open Orders</div>
                <div class="position-tab" data-tab="history">History</div>
            </div>
            <div class="positions-content">
                <div class="tab-content active" id="positions-tab">
                    <div id="positions-list">
                        <!-- Positions will be populated here -->
                    </div>
                </div>
                <div class="tab-content" id="orders-tab">
                    <div id="orders-list">
                        <!-- Orders will be populated here -->
                    </div>
                </div>
                <div class="tab-content" id="history-tab">
                    <div id="history-list">
                        <!-- Trade history will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="trading-footer">
            {% include 'components/footer.html' %}
        </div>
    </div>

    <!-- Load Enhanced Live Trading JavaScript -->
    <script src="/static/js/live_trading_enhanced.js"></script>

    <script>
        // Professional Trading Platform JavaScript
        let selectedLeverage = 10;
        let selectedSide = 'buy';
        let tradingState = {};
        let wsConnection = null;
        let tradingViewWidget = null;

        // Initialize the trading platform
        document.addEventListener('DOMContentLoaded', function() {
            initializeHeader();
            initializeTradingInterface();
            initializeTradingViewChart();
            connectWebSocket();
            startDataUpdates();

            // Initialize enhanced live trading interface
            console.log('🚀 Enhanced Live Trading Interface loaded');
        });

        function initializeHeader() {
            console.log('Initializing header navigation for live trading page');

            // Initialize header navigation if available
            if (typeof initializeHeaderNavigation === 'function') {
                initializeHeaderNavigation();
                console.log('Header navigation initialized');
            } else {
                console.warn('initializeHeaderNavigation function not found');
                // Fallback initialization
                initializeHeaderFallback();
            }
        }

        function initializeHeaderFallback() {
            console.log('Using fallback header initialization');

            // Initialize notification bell
            const notificationBell = document.getElementById('notification-badge');
            if (notificationBell) {
                notificationBell.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Notification bell clicked');
                    if (typeof toggleNotifications === 'function') {
                        toggleNotifications();
                    } else {
                        console.warn('toggleNotifications function not available');
                    }
                });
                console.log('Notification bell initialized');
            }

            // Initialize user dropdown
            const userDropdown = document.querySelector('.user-profile-dropdown');
            if (userDropdown) {
                userDropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('User dropdown clicked');
                    if (typeof toggleUserDropdown === 'function') {
                        toggleUserDropdown();
                    } else {
                        console.warn('toggleUserDropdown function not available');
                    }
                });
                console.log('User dropdown initialized');
            }

            // Initialize mobile menu toggle
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Mobile toggle clicked');
                    if (typeof toggleMobileMenu === 'function') {
                        toggleMobileMenu();
                    } else {
                        console.warn('toggleMobileMenu function not available');
                    }
                });
                console.log('Mobile menu toggle initialized');
            }

            // Add global click handler to close dropdowns
            document.addEventListener('click', function(e) {
                // Close all dropdowns when clicking outside
                if (typeof closeAllDropdowns === 'function') {
                    const isClickOnDropdownTrigger = e.target.closest('.user-profile-dropdown, #notification-badge, .mobile-menu-toggle');
                    if (!isClickOnDropdownTrigger) {
                        closeAllDropdowns();
                    }
                }
            });

            console.log('Fallback header initialization complete');
        }

        function initializeTradingInterface() {
            // Initialize trading tabs
            document.querySelectorAll('.trading-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.trading-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    selectedSide = this.dataset.side;
                    updateOrderButton();
                });
            });

            // Initialize leverage selector
            document.querySelectorAll('.leverage-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.leverage-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedLeverage = parseInt(this.dataset.leverage);
                    updateOrderSummary();
                });
            });

            // Initialize chart timeframe controls
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    updateChartTimeframe(this.dataset.timeframe);
                });
            });

            // Initialize order form inputs
            document.getElementById('orderSize').addEventListener('input', updateOrderSummary);
            document.getElementById('orderValue').addEventListener('input', updateOrderSummary);

            // Initialize order button
            document.getElementById('orderButton').addEventListener('click', placeOrder);

            // Initialize automation toggle
            document.getElementById('automationToggle').addEventListener('click', toggleAutomation);

            updateOrderButton();
        }

        function initializeTradingViewChart() {
            // Initialize TradingView chart widget
            tradingViewWidget = new TradingView.widget({
                "width": "100%",
                "height": "100%",
                "symbol": "BINANCE:DOGEUSDT",
                "interval": "1",
                "timezone": "Etc/UTC",
                "theme": "dark",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#0f1419",
                "enable_publishing": false,
                "hide_top_toolbar": false,
                "hide_legend": true,
                "save_image": false,
                "container_id": "tradingview_chart",
                "studies": [
                    "Volume@tv-basicstudies"
                ],
                "overrides": {
                    "paneProperties.background": "#0f1419",
                    "paneProperties.vertGridProperties.color": "#333",
                    "paneProperties.horzGridProperties.color": "#333",
                    "symbolWatermarkProperties.transparency": 90,
                    "scalesProperties.textColor": "#AAA"
                }
            });
        }

        function updateChartTimeframe(timeframe) {
            if (tradingViewWidget) {
                tradingViewWidget.chart().setResolution(timeframe);
            }
        }

        function updateOrderButton() {
            const button = document.getElementById('orderButton');
            if (selectedSide === 'buy') {
                button.textContent = 'Buy DOGE';
                button.className = 'order-button buy';
            } else {
                button.textContent = 'Sell DOGE';
                button.className = 'order-button sell';
            }
        }

        function updateOrderSummary() {
            const orderSize = parseFloat(document.getElementById('orderSize').value) || 0;
            const orderValue = parseFloat(document.getElementById('orderValue').value) || 0;
            const currentPrice = tradingState.current_price || 0;

            // Calculate order cost and fees
            const orderCost = orderSize * currentPrice;
            const estimatedFee = orderCost * 0.001; // 0.1% fee

            document.getElementById('orderCost').textContent = `$${orderCost.toFixed(2)}`;
            document.getElementById('estimatedFee').textContent = `$${estimatedFee.toFixed(2)}`;

            // Update available balance from trading state
            if (tradingState.account_balance) {
                document.getElementById('availableBalance').textContent = `$${tradingState.account_balance.available_balance.toFixed(2)}`;
            }
        }

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/live-trading`;

            wsConnection = new WebSocket(wsUrl);

            wsConnection.onopen = function() {
                console.log('WebSocket connected');
            };

            wsConnection.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateTradingInterface(data);
            };

            wsConnection.onclose = function() {
                console.log('WebSocket disconnected, reconnecting...');
                setTimeout(connectWebSocket, 3000);
            };
        }

        function updateTradingInterface(data) {
            tradingState = data;

            // Update market data
            if (data.current_price) {
                document.getElementById('currentPrice').textContent = `$${data.current_price.toFixed(6)}`;
                document.getElementById('orderbookPrice').textContent = `$${data.current_price.toFixed(6)}`;
            }

            // Update market stats (mock data for now)
            document.getElementById('high24h').textContent = `$${(data.current_price * 1.05).toFixed(6)}`;
            document.getElementById('low24h').textContent = `$${(data.current_price * 0.95).toFixed(6)}`;
            document.getElementById('volume24h').textContent = '1,234,567';
            document.getElementById('fundingRate').textContent = '0.0100%';

            // Update price change
            const priceChangeElement = document.getElementById('priceChange');
            const changePercent = Math.random() * 4 - 2; // Mock change
            priceChangeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%`;
            priceChangeElement.className = `ticker-change ${changePercent >= 0 ? 'positive' : 'negative'}`;

            // Update positions
            updatePositionsDisplay(data.position);

            // Update order summary
            updateOrderSummary();

            // Update order book (mock data)
            updateOrderBook();
        }

        function updatePositionsDisplay(position) {
            const positionsContent = document.getElementById('positionsContent');

            if (position) {
                positionsContent.innerHTML = `
                    <div class="position-item">
                        <div class="position-header">
                            <div class="position-symbol">DOGE/USDT</div>
                            <div class="position-side ${position.side}">${position.side.toUpperCase()}</div>
                        </div>
                        <div class="position-details">
                            <div class="position-detail">
                                <span class="detail-label">Size:</span>
                                <span class="detail-value">${position.size} DOGE</span>
                            </div>
                            <div class="position-detail">
                                <span class="detail-label">Entry Price:</span>
                                <span class="detail-value">$${position.entry_price.toFixed(6)}</span>
                            </div>
                            <div class="position-detail">
                                <span class="detail-label">Mark Price:</span>
                                <span class="detail-value">$${position.mark_price.toFixed(6)}</span>
                            </div>
                            <div class="position-detail">
                                <span class="detail-label">PnL:</span>
                                <span class="detail-value ${position.pnl_usd >= 0 ? 'positive' : 'negative'}">
                                    $${position.pnl_usd.toFixed(2)} (${position.pnl_percent.toFixed(2)}%)
                                </span>
                            </div>
                        </div>
                        <div class="position-actions">
                            <button class="position-btn" onclick="addToPosition()">Add</button>
                            <button class="position-btn" onclick="reducePosition()">Reduce</button>
                            <button class="position-btn close" onclick="closePosition()">Close</button>
                        </div>
                    </div>
                `;
            } else {
                positionsContent.innerHTML = '<div style="text-align: center; color: #888; padding: 20px;">No open positions</div>';
            }
        }

        function updateOrderBook() {
            // Mock order book data
            const asks = [];
            const bids = [];
            const basePrice = tradingState.current_price || 0.1;

            for (let i = 0; i < 10; i++) {
                asks.push({
                    price: basePrice + (i + 1) * 0.0001,
                    size: Math.random() * 10000 + 1000,
                    total: 0
                });
                bids.push({
                    price: basePrice - (i + 1) * 0.0001,
                    size: Math.random() * 10000 + 1000,
                    total: 0
                });
            }

            // Update asks
            const asksHtml = asks.map(ask => `
                <tr class="ask-row">
                    <td>${ask.price.toFixed(6)}</td>
                    <td>${ask.size.toFixed(0)}</td>
                    <td>${(ask.price * ask.size).toFixed(2)}</td>
                </tr>
            `).join('');

            // Update bids
            const bidsHtml = bids.map(bid => `
                <tr class="bid-row">
                    <td>${bid.price.toFixed(6)}</td>
                    <td>${bid.size.toFixed(0)}</td>
                    <td>${(bid.price * bid.size).toFixed(2)}</td>
                </tr>
            `).join('');

            document.getElementById('orderbook-asks').innerHTML = asksHtml;
            document.getElementById('orderbook-bids').innerHTML = bidsHtml;
        }

        async function placeOrder() {
            const orderSize = parseFloat(document.getElementById('orderSize').value);
            if (!orderSize || orderSize <= 0) {
                alert('Please enter a valid order size');
                return;
            }

            const orderData = {
                side: selectedSide,
                amount: orderSize,
                leverage: selectedLeverage
            };

            try {
                const response = await fetch('/api/live-trading/order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (result.success) {
                    alert(`${selectedSide.toUpperCase()} order placed successfully!`);
                    document.getElementById('orderSize').value = '';
                    document.getElementById('orderValue').value = '';
                } else {
                    alert(`Order failed: ${result.error}`);
                }
            } catch (error) {
                alert(`Order failed: ${error.message}`);
            }
        }

        async function closePosition() {
            if (!confirm('Are you sure you want to close this position?')) {
                return;
            }

            try {
                const response = await fetch('/api/live-trading/close-position', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    alert('Position closed successfully!');
                } else {
                    alert(`Close failed: ${result.error}`);
                }
            } catch (error) {
                alert(`Close failed: ${error.message}`);
            }
        }

        function addToPosition() {
            // Implementation for adding to position
            alert('Add to position functionality - Coming soon!');
        }

        function reducePosition() {
            // Implementation for reducing position
            alert('Reduce position functionality - Coming soon!');
        }

        function toggleAutomation() {
            const toggle = document.getElementById('automationToggle');
            toggle.classList.toggle('active');

            const isActive = toggle.classList.contains('active');
            const controls = document.getElementById('automationControls');
            controls.style.display = isActive ? 'grid' : 'none';

            if (isActive) {
                updateAutomationSettings();
            }
        }

        async function updateAutomationSettings() {
            const settings = {
                auto_take_profit: {
                    enabled: true,
                    threshold_percent: parseFloat(document.getElementById('takeProfitPercent').value) || 2.0
                },
                auto_stop_loss: {
                    enabled: true,
                    threshold_percent: parseFloat(document.getElementById('stopLossPercent').value) || 1.0
                },
                trailing_stop_loss: {
                    enabled: true,
                    trail_distance_percent: parseFloat(document.getElementById('trailingStopPercent').value) || 0.5
                }
            };

            try {
                const response = await fetch('/api/live-trading/automation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                });

                const result = await response.json();
                if (!result.success) {
                    console.error('Failed to update automation settings:', result.error);
                }
            } catch (error) {
                console.error('Automation update error:', error);
            }
        }

        function startDataUpdates() {
            // Update data every second
            setInterval(() => {
                if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                    wsConnection.send(JSON.stringify({type: 'get_trading_state'}));
                }
            }, 1000);
        }
    </script>
</body>
</html>
