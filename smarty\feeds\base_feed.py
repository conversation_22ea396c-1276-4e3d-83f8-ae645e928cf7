"""
Base feed class for the smart-trader system.

This module provides a base class for data feeds.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable

logger = logging.getLogger(__name__)

class BaseFeed:
    """
    Base class for data feeds.
    
    This class provides a base implementation for data feeds.
    """
    
    def __init__(self, name: str, symbols: List[str] = None):
        """
        Initialize the feed.
        
        Args:
            name: Feed name
            symbols: List of symbols to subscribe to
        """
        self.name = name
        self.symbols = symbols or []
        self.running = False
        self.callbacks = {}
    
    async def start(self):
        """Start the feed."""
        if self.running:
            logger.warning(f"Feed {self.name} is already running")
            return
        
        self.running = True
        logger.info(f"Starting feed {self.name}")
        
        try:
            await self._start()
        except Exception as e:
            logger.error(f"Error starting feed {self.name}: {e}")
            self.running = False
    
    async def stop(self):
        """Stop the feed."""
        if not self.running:
            logger.warning(f"Feed {self.name} is not running")
            return
        
        self.running = False
        logger.info(f"Stopping feed {self.name}")
        
        try:
            await self._stop()
        except Exception as e:
            logger.error(f"Error stopping feed {self.name}: {e}")
    
    async def _start(self):
        """
        Start the feed (implementation).
        
        This method should be overridden by subclasses.
        """
        pass
    
    async def _stop(self):
        """
        Stop the feed (implementation).
        
        This method should be overridden by subclasses.
        """
        pass
    
    def subscribe(self, event_type: str, callback: Callable):
        """
        Subscribe to an event.
        
        Args:
            event_type: Event type (e.g., "kline", "trade", "orderbook")
            callback: Callback function to call when the event occurs
        """
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        
        self.callbacks[event_type].append(callback)
        logger.debug(f"Subscribed to {event_type} events in feed {self.name}")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """
        Unsubscribe from an event.
        
        Args:
            event_type: Event type (e.g., "kline", "trade", "orderbook")
            callback: Callback function to unsubscribe
        """
        if event_type in self.callbacks:
            if callback in self.callbacks[event_type]:
                self.callbacks[event_type].remove(callback)
                logger.debug(f"Unsubscribed from {event_type} events in feed {self.name}")
    
    def _emit(self, event_type: str, data: Any):
        """
        Emit an event.
        
        Args:
            event_type: Event type (e.g., "kline", "trade", "orderbook")
            data: Event data
        """
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in {event_type} callback: {e}")
    
    def add_symbol(self, symbol: str):
        """
        Add a symbol to the feed.
        
        Args:
            symbol: Symbol to add
        """
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            logger.debug(f"Added symbol {symbol} to feed {self.name}")
    
    def remove_symbol(self, symbol: str):
        """
        Remove a symbol from the feed.
        
        Args:
            symbol: Symbol to remove
        """
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            logger.debug(f"Removed symbol {symbol} from feed {self.name}")
    
    def get_symbols(self) -> List[str]:
        """
        Get the list of symbols.
        
        Returns:
            List of symbols
        """
        return self.symbols.copy()
    
    def is_running(self) -> bool:
        """
        Check if the feed is running.
        
        Returns:
            True if the feed is running, False otherwise
        """
        return self.running
