"""
Backtesting framework for the smart-trader system.

This module provides a backtesting framework for testing trading strategies
with historical data.
"""

import os
import json
import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Callable

from core.events import Order, OrderResponse, Fill, Signal, Side, OrderType, Position
from core.feature_store import feature_store
from executors.htx_executor import HTXExecutor
from position_manager import PositionManager
from .analytics import EnhancedAnalytics, PerformanceMetrics

logger = logging.getLogger(__name__)


class Backtester:
    """
    Backtesting framework for the smart-trader system.

    This class provides functionality for backtesting trading strategies
    with historical data.
    """

    def __init__(
        self,
        config: Dict[str, Any],
        data_dir: str = "data/historical",
        output_dir: str = "results/backtest"
    ):
        """
        Initialize the backtester.

        Args:
            config: Configuration dictionary
            data_dir: Directory containing historical data
            output_dir: Directory for storing backtest results
        """
        self.config = config
        self.data_dir = data_dir
        self.output_dir = output_dir

        # Create directories if they don't exist
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.executor = None
        self.position_manager = None

        # Backtest parameters
        self.start_date = None
        self.end_date = None
        self.symbols = []
        self.initial_balance = 100.0  # Default initial balance

        # Data storage
        self.historical_data = {}  # Symbol -> DataFrame
        self.signals = []  # List of signals generated during backtest
        self.trades = []  # List of trades executed during backtest
        self.equity_curve = []  # List of (timestamp, equity) tuples

        # Performance metrics
        self.metrics = {}
        self.enhanced_analytics = EnhancedAnalytics()

        # Callback functions
        self.signal_generator = None  # Function that generates signals

    async def initialize(self):
        """Initialize the backtester components."""
        # Create executor with simulation mode enabled
        self.executor = HTXExecutor(
            client=None,  # No client needed for backtesting
            config=self.config,
            simulation_mode=True
        )

        # Set initial balance
        self.executor.sim_balance = self.initial_balance

        # Create position manager
        self.position_manager = PositionManager(
            executor=self.executor,
            config=self.config
        )

        # Set position manager on executor
        self.executor.position_manager = self.position_manager

        # Start position manager
        await self.position_manager.start()

        logger.info(f"Backtester initialized with {len(self.symbols)} symbols and ${self.initial_balance:.2f} initial balance")

    def reset(self):
        """
        Reset the backtester state for a new backtest run.

        This method resets the backtester state while preserving loaded data,
        allowing for multiple backtest runs without reloading data.
        """
        # Reset data structures
        self.signals = []
        self.trades = []
        self.equity_curve = []
        self.metrics = {}

        # Reset executor if it exists
        if self.executor:
            self.executor.sim_balance = self.initial_balance
            self.executor.sim_positions = {}

        logger.info("Backtester state reset for new run")

    async def load_data(self, symbols: List[str], start_date: str, end_date: str):
        """
        Load historical data for the specified symbols and date range.

        Args:
            symbols: List of symbols to load data for
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
        """
        self.symbols = symbols
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")

        for symbol in symbols:
            # Load data from CSV file
            file_path = os.path.join(self.data_dir, f"{symbol.replace('-', '_')}.csv")

            if not os.path.exists(file_path):
                logger.warning(f"Historical data file not found: {file_path}")
                continue

            try:
                # Load data into DataFrame
                df = pd.read_csv(file_path)

                # Convert timestamp to datetime
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                elif 'time' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['time'])
                    df = df.drop(columns=['time'])
                else:
                    # If no timestamp column, assume first column is timestamp
                    df['timestamp'] = pd.to_datetime(df.iloc[:, 0])

                # Filter data by date range
                df = df[(df['timestamp'] >= self.start_date) & (df['timestamp'] <= self.end_date)]

                # Ensure required columns exist
                required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                for col in required_columns:
                    if col not in df.columns:
                        if col == 'timestamp' and 'time' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['time'])
                        else:
                            logger.warning(f"Missing required column {col} in {file_path}")

                # Sort by timestamp
                df = df.sort_values('timestamp')

                # Store data
                self.historical_data[symbol] = df

                logger.info(f"Loaded {len(df)} data points for {symbol} from {start_date} to {end_date}")

            except Exception as e:
                logger.error(f"Error loading data for {symbol}: {e}")

        # Check if any data was loaded
        if not self.historical_data:
            logger.error("No historical data loaded. Cannot proceed with backtest.")
            return False

        return True

    async def run_backtest(self, signal_generator: Callable = None):
        """
        Run the backtest.

        Args:
            signal_generator: Function that generates signals based on market data
        """
        if not self.historical_data:
            logger.error("No historical data loaded. Cannot run backtest.")
            return False

        # Set signal generator
        self.signal_generator = signal_generator

        # Initialize feature store with historical data
        await self._initialize_feature_store()

        # Initialize components
        await self.initialize()

        # Get the earliest and latest timestamps across all symbols
        min_timestamp = min(df['timestamp'].min() for df in self.historical_data.values())
        max_timestamp = max(df['timestamp'].max() for df in self.historical_data.values())

        # Create a list of all timestamps in chronological order
        all_timestamps = []
        for symbol, df in self.historical_data.items():
            all_timestamps.extend(df['timestamp'].tolist())
        all_timestamps = sorted(set(all_timestamps))

        # Initialize equity curve with initial balance
        self.equity_curve = [(min_timestamp, self.initial_balance)]

        # Process each timestamp
        logger.info(f"Running backtest from {min_timestamp} to {max_timestamp}")

        for timestamp in all_timestamps:
            # Update market data in feature store
            await self._update_market_data(timestamp)

            # Generate signals if signal generator is provided
            if self.signal_generator:
                signals = await self.signal_generator(timestamp, self.symbols)
                if signals:
                    for signal in signals:
                        # Execute signal
                        await self._execute_signal(signal)

            # Update positions
            await self._update_positions(timestamp)

            # Record equity
            # Start with the cash balance
            equity = self.executor.sim_balance

            # Add unrealized P&L from open positions
            for symbol, position in self.executor.sim_positions.items():
                # Add unrealized P&L to equity
                current_price = await feature_store.get(symbol, "close")
                if current_price:
                    unrealized_pnl = self._calculate_unrealized_pnl(position, float(current_price))
                    equity += unrealized_pnl

            # Ensure equity is never negative (can happen with extreme leverage)
            equity = max(0.01, equity)

            # Add to equity curve
            self.equity_curve.append((timestamp, equity))

        # Calculate performance metrics
        self._calculate_metrics()

        # Save results
        self._save_results()

        # Stop position manager
        await self.position_manager.stop()

        logger.info(f"Backtest completed. Final balance: ${self.executor.sim_balance:.2f}")

        return True

    async def _initialize_feature_store(self):
        """Initialize feature store with historical data."""
        # Clear feature store
        await feature_store.clear()

        # Initialize with first data point for each symbol
        for symbol, df in self.historical_data.items():
            if len(df) > 0:
                first_row = df.iloc[0]

                # Set initial price data
                await feature_store.set(symbol, "open", float(first_row['open']))
                await feature_store.set(symbol, "high", float(first_row['high']))
                await feature_store.set(symbol, "low", float(first_row['low']))
                await feature_store.set(symbol, "close", float(first_row['close']))
                await feature_store.set(symbol, "volume", float(first_row['volume']))
                await feature_store.set(symbol, "last_price", float(first_row['close']))

                # Set timestamp
                await feature_store.set(symbol, "last_update", first_row['timestamp'].isoformat())

    async def _update_market_data(self, timestamp: datetime):
        """
        Update market data in feature store for the given timestamp.

        Args:
            timestamp: Current timestamp
        """
        for symbol, df in self.historical_data.items():
            # Get data for the current timestamp
            data = df[df['timestamp'] == timestamp]

            if len(data) > 0:
                row = data.iloc[0]

                # Update price data
                await feature_store.set(symbol, "open", float(row['open']))
                await feature_store.set(symbol, "high", float(row['high']))
                await feature_store.set(symbol, "low", float(row['low']))
                await feature_store.set(symbol, "close", float(row['close']))
                await feature_store.set(symbol, "volume", float(row['volume']))
                await feature_store.set(symbol, "last_price", float(row['close']))

                # Update timestamp
                await feature_store.set(symbol, "last_update", timestamp.isoformat())

                # Add to time series
                await feature_store.add_time_series(symbol, "close_prices", float(row['close']), timestamp)

    async def _update_positions(self, timestamp: datetime):
        """
        Update positions based on current market data.

        Args:
            timestamp: Current timestamp
        """
        # Update each position with current price
        for symbol in self.symbols:
            # Get current price
            current_price = await feature_store.get(symbol, "close")
            if current_price:
                # Update position manager with current price
                await self.position_manager.on_price_update(symbol, float(current_price))

    async def _execute_signal(self, signal: Signal):
        """
        Execute a trading signal.

        Args:
            signal: Trading signal
        """
        # Execute signal
        response = await self.executor.execute(signal)

        if response:
            # Record trade
            trade = {
                "symbol": signal.symbol,
                "side": signal.action.value,
                "quantity": getattr(response, 'quantity', 0.0),
                "price": getattr(response, 'price', 0.0),
                "timestamp": signal.timestamp.isoformat(),
                "order_id": getattr(response, 'order_id', ''),
                "signal_score": signal.score,
                "signal_source": signal.source,
                "signal_rationale": signal.rationale
            }

            self.trades.append(trade)

            # Record signal
            self.signals.append({
                "symbol": signal.symbol,
                "action": signal.action.value,
                "score": signal.score,
                "timestamp": signal.timestamp.isoformat(),
                "source": signal.source,
                "rationale": signal.rationale
            })

    def _calculate_unrealized_pnl(self, position: Position, current_price: float) -> float:
        """
        Calculate unrealized P&L for a position.

        Args:
            position: Position object
            current_price: Current price

        Returns:
            Unrealized P&L
        """
        if position.side == Side.BUY:
            return (current_price - position.entry_price) * position.size
        else:
            return (position.entry_price - current_price) * position.size

    def _calculate_metrics(self):
        """Calculate comprehensive performance metrics using enhanced analytics."""
        if not self.equity_curve:
            logger.warning("No equity curve data. Cannot calculate metrics.")
            return

        # Extract balance history and timestamps
        timestamps = [item[0] for item in self.equity_curve]
        balance_history = [item[1] for item in self.equity_curve]

        # Calculate comprehensive metrics using enhanced analytics
        enhanced_metrics = self.enhanced_analytics.calculate_comprehensive_metrics(
            balance_history=balance_history,
            trade_history=self.trades,
            timestamps=timestamps,
            benchmark_returns=None  # Could add benchmark data later
        )

        # Store both basic and enhanced metrics
        self.metrics = {
            # Basic metrics for backward compatibility
            'total_return': enhanced_metrics.total_return,
            'annual_return': enhanced_metrics.annualized_return,
            'annual_volatility': enhanced_metrics.volatility,
            'sharpe_ratio': enhanced_metrics.sharpe_ratio,
            'max_drawdown': enhanced_metrics.max_drawdown,
            'win_rate': enhanced_metrics.win_rate,
            'total_trades': enhanced_metrics.total_trades,
            'final_equity': enhanced_metrics.final_balance,
            'initial_equity': enhanced_metrics.initial_balance,
            'final_balance': enhanced_metrics.final_balance,
            'initial_balance': enhanced_metrics.initial_balance,

            # Enhanced metrics
            'enhanced_metrics': enhanced_metrics
        }

        # Generate and log comprehensive performance report
        performance_report = self.enhanced_analytics.generate_performance_report(enhanced_metrics)
        logger.info("Enhanced Performance Report Generated")

        # Log key metrics
        logger.info(f"Performance Summary: Total Return: {enhanced_metrics.total_return:.2%}, "
                   f"Sharpe Ratio: {enhanced_metrics.sharpe_ratio:.2f}, "
                   f"Max Drawdown: {enhanced_metrics.max_drawdown:.2%}, "
                   f"Win Rate: {enhanced_metrics.win_rate:.2%}")

        # Store the full report for later use
        self.performance_report = performance_report

    def _save_results(self):
        """Save backtest results to files."""
        # Create timestamp for result files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save equity curve
        equity_df = pd.DataFrame(self.equity_curve, columns=['timestamp', 'equity'])
        equity_file = os.path.join(self.output_dir, f"equity_curve_{timestamp}.csv")
        equity_df.to_csv(equity_file, index=False)

        # Save trades
        trades_file = os.path.join(self.output_dir, f"trades_{timestamp}.json")
        with open(trades_file, 'w') as f:
            json.dump(self.trades, f, indent=2)

        # Save signals
        signals_file = os.path.join(self.output_dir, f"signals_{timestamp}.json")
        with open(signals_file, 'w') as f:
            json.dump(self.signals, f, indent=2)

        # Save metrics (convert enhanced metrics to dict for JSON serialization)
        metrics_file = os.path.join(self.output_dir, f"metrics_{timestamp}.json")
        metrics_to_save = self.metrics.copy()
        if 'enhanced_metrics' in metrics_to_save:
            # Convert enhanced metrics dataclass to dict
            enhanced_metrics = metrics_to_save['enhanced_metrics']
            metrics_to_save['enhanced_metrics'] = {
                'total_return': enhanced_metrics.total_return,
                'annualized_return': enhanced_metrics.annualized_return,
                'volatility': enhanced_metrics.volatility,
                'sharpe_ratio': enhanced_metrics.sharpe_ratio,
                'sortino_ratio': enhanced_metrics.sortino_ratio,
                'max_drawdown': enhanced_metrics.max_drawdown,
                'calmar_ratio': enhanced_metrics.calmar_ratio,
                'total_trades': enhanced_metrics.total_trades,
                'winning_trades': enhanced_metrics.winning_trades,
                'losing_trades': enhanced_metrics.losing_trades,
                'win_rate': enhanced_metrics.win_rate,
                'avg_win': enhanced_metrics.avg_win,
                'avg_loss': enhanced_metrics.avg_loss,
                'profit_factor': enhanced_metrics.profit_factor,
                'var_95': enhanced_metrics.var_95,
                'cvar_95': enhanced_metrics.cvar_95,
                'beta': enhanced_metrics.beta,
                'alpha': enhanced_metrics.alpha,
                'information_ratio': enhanced_metrics.information_ratio,
                'treynor_ratio': enhanced_metrics.treynor_ratio,
                'omega_ratio': enhanced_metrics.omega_ratio,
                'tail_ratio': enhanced_metrics.tail_ratio,
                'avg_trade_duration': enhanced_metrics.avg_trade_duration,
                'max_trade_duration': enhanced_metrics.max_trade_duration,
                'min_trade_duration': enhanced_metrics.min_trade_duration,
                'initial_balance': enhanced_metrics.initial_balance,
                'final_balance': enhanced_metrics.final_balance,
                'peak_balance': enhanced_metrics.peak_balance,
                'skewness': enhanced_metrics.skewness,
                'kurtosis': enhanced_metrics.kurtosis,
                'stability': enhanced_metrics.stability
            }

        with open(metrics_file, 'w') as f:
            json.dump(metrics_to_save, f, indent=2)

        # Save performance report
        if hasattr(self, 'performance_report'):
            report_file = os.path.join(self.output_dir, f"performance_report_{timestamp}.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(self.performance_report)

        logger.info(f"Backtest results saved to {self.output_dir}")

        # Return paths to result files
        result_files = {
            'equity_curve': equity_file,
            'trades': trades_file,
            'signals': signals_file,
            'metrics': metrics_file
        }

        if hasattr(self, 'performance_report'):
            result_files['performance_report'] = report_file

        return result_files
