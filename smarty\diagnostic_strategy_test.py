#!/usr/bin/env python3
"""
Strategy process diagnostic test for Epinnox Investment Club dashboard.
Tests strategy detection, management, and process tracking.
"""

import psutil
import subprocess
import time
import requests
import json

def find_running_strategies():
    """Find currently running strategy processes."""
    print("🔍 Scanning for Running Strategy Processes...")
    
    strategy_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if proc.info['name'] in ['python.exe', 'python']:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # Look for strategy-related processes
                strategy_indicators = [
                    'htx_data_producer',
                    'orchestrator',
                    'run_smart_strategy',
                    'bollinger',
                    'rsi_strategy'
                ]
                
                for indicator in strategy_indicators:
                    if indicator in cmdline.lower():
                        strategy_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline,
                            'create_time': proc.info['create_time'],
                            'type': indicator
                        })
                        break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if strategy_processes:
        print(f"   ✅ Found {len(strategy_processes)} strategy processes:")
        for proc in strategy_processes:
            runtime = time.time() - proc['create_time']
            print(f"   PID {proc['pid']}: {proc['type']} (running {runtime:.0f}s)")
            print(f"      Command: {proc['cmdline'][:80]}...")
    else:
        print("   ❌ No strategy processes found")
    
    return strategy_processes

def test_dashboard_strategy_detection():
    """Test if dashboard can detect running strategies."""
    print("\n🔍 Testing Dashboard Strategy Detection...")
    
    try:
        # Test strategy status endpoint
        response = requests.get('http://localhost:8082/api/strategy/status', timeout=5)
        
        if response.status_code == 401:
            print("   🔐 Authentication required - cannot test strategy detection")
            return False
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ Dashboard API accessible")
            print(f"   Current Strategy: {data.get('current_strategy', 'None')}")
            print(f"   Strategy Running: {data.get('strategy_running', False)}")
            print(f"   Active Processes: {data.get('active_processes', 0)}")
            
            if 'process_details' in data:
                print("   Process Details:")
                for name, details in data['process_details'].items():
                    status = "Running" if details.get('running') else "Stopped"
                    pid = details.get('pid', 'N/A')
                    print(f"      {name}: {status} (PID: {pid})")
            
            return data.get('strategy_running', False)
        else:
            print(f"   ❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error testing dashboard: {e}")
        return False

def test_strategy_commands():
    """Test available strategy commands."""
    print("\n🔍 Testing Strategy Commands...")
    
    # Import the dashboard to get strategy commands
    try:
        import sys
        sys.path.append('.')
        from live_dashboard import LiveDashboard
        
        dashboard = LiveDashboard()
        commands = dashboard.strategy_commands
        
        print(f"   ✅ Found {len(commands)} strategy commands:")
        for name, command in commands.items():
            print(f"      {name}: {command}")
        
        return commands
    except Exception as e:
        print(f"   ❌ Error loading strategy commands: {e}")
        return {}

def test_data_flow():
    """Test if data is flowing to the database."""
    print("\n🔍 Testing Data Flow...")
    
    try:
        import sqlite3
        from datetime import datetime
        
        conn = sqlite3.connect('data/bus.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Check recent messages
        cursor.execute("SELECT COUNT(*) as count FROM messages WHERE ts > ?", 
                      (time.time() - 300,))  # Last 5 minutes
        recent_count = cursor.fetchone()['count']
        
        # Check latest message
        cursor.execute("SELECT MAX(ts) as latest FROM messages")
        latest_ts = cursor.fetchone()['latest']
        
        if latest_ts:
            latest_time = datetime.fromtimestamp(latest_ts)
            age_seconds = time.time() - latest_ts
            
            print(f"   ✅ Recent messages (5min): {recent_count}")
            print(f"   ✅ Latest data: {latest_time.strftime('%H:%M:%S')} ({age_seconds:.1f}s ago)")
            
            if age_seconds < 60:
                print("   ✅ Data flow is LIVE")
                return True
            else:
                print("   ⚠️ Data flow is STALE")
                return False
        else:
            print("   ❌ No data found in database")
            return False
        
        conn.close()
    except Exception as e:
        print(f"   ❌ Error testing data flow: {e}")
        return False

def test_specific_endpoints():
    """Test specific dashboard endpoints that might be stuck on 'Loading...'"""
    print("\n🔍 Testing Specific Dashboard Endpoints...")
    
    endpoints = {
        '/api/orderbook': 'Order Book',
        '/api/recent-trades': 'Recent Trades',
        '/api/debug': 'Debug Info',
        '/api/ai-analysis': 'AI Analysis',
        '/api/market-sentiment': 'Market Sentiment'
    }
    
    results = {}
    for endpoint, name in endpoints.items():
        try:
            response = requests.get(f'http://localhost:8082{endpoint}', timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                
                if endpoint == '/api/orderbook':
                    bids = len(data.get('bids', []))
                    asks = len(data.get('asks', []))
                    results[name] = f"✅ {bids} bids, {asks} asks"
                elif endpoint == '/api/recent-trades':
                    trades = len(data) if isinstance(data, list) else 0
                    results[name] = f"✅ {trades} trades"
                elif endpoint == '/api/debug':
                    status = data.get('system_status', 'Unknown')
                    results[name] = f"✅ Status: {status}"
                else:
                    results[name] = f"✅ {len(str(data))} bytes"
            elif response.status_code == 401:
                results[name] = "🔐 Auth required"
            else:
                results[name] = f"❌ Status {response.status_code}"
        except Exception as e:
            results[name] = f"❌ {str(e)[:50]}"
    
    for name, result in results.items():
        print(f"   {name}: {result}")
    
    return results

def main():
    """Run comprehensive strategy diagnostic."""
    print("🎯 EPINNOX STRATEGY DIAGNOSTIC")
    print("=" * 50)
    
    # Test 1: Find running processes
    running_strategies = find_running_strategies()
    
    # Test 2: Test dashboard strategy detection
    dashboard_detects = test_dashboard_strategy_detection()
    
    # Test 3: Test strategy commands
    strategy_commands = test_strategy_commands()
    
    # Test 4: Test data flow
    data_flowing = test_data_flow()
    
    # Test 5: Test specific endpoints
    endpoint_results = test_specific_endpoints()
    
    print("\n📊 STRATEGY DIAGNOSTIC SUMMARY")
    print("=" * 40)
    print(f"Running Strategy Processes: {len(running_strategies)}")
    print(f"Dashboard Strategy Detection: {'✅' if dashboard_detects else '❌'}")
    print(f"Available Strategy Commands: {len(strategy_commands)}")
    print(f"Live Data Flow: {'✅' if data_flowing else '❌'}")
    
    # Analyze endpoint issues
    loading_issues = [name for name, result in endpoint_results.items() 
                     if '❌' in result or 'Auth required' in result]
    
    if loading_issues:
        print(f"\n⚠️ Components with Loading Issues:")
        for issue in loading_issues:
            print(f"   - {issue}: {endpoint_results[issue]}")
    else:
        print(f"\n✅ All endpoints responding correctly")
    
    # Overall assessment
    if len(running_strategies) > 0 and data_flowing:
        print(f"\n✅ DIAGNOSIS: System is operational")
        print(f"   - Strategy processes are running")
        print(f"   - Data is flowing to database")
        if not dashboard_detects:
            print(f"   ⚠️ Dashboard may need authentication to show strategy status")
    else:
        print(f"\n❌ DIAGNOSIS: Issues detected")
        if len(running_strategies) == 0:
            print(f"   - No strategy processes running")
        if not data_flowing:
            print(f"   - Data flow is stale or stopped")

if __name__ == "__main__":
    main()
