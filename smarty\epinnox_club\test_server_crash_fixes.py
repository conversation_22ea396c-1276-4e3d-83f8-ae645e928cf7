#!/usr/bin/env python3
"""
Test Server Crash and Exchange Removal Fixes
Comprehensive test for server stability and exchange management
"""

import asyncio
import logging
import sys
import json
import aiohttp
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8086"

class ServerCrashFixTester:
    """Test server crash and exchange removal fixes."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = None
        self.session_cookie = None
        self.test_results = {}
        logger.info("Server Crash Fix Tester initialized")
    
    async def run_all_tests(self):
        """Run all server stability tests."""
        try:
            logger.info("=" * 70)
            logger.info("SERVER CRASH AND EXCHANGE REMOVAL FIXES TEST SUITE")
            logger.info("=" * 70)
            
            # Create session
            self.session = aiohttp.ClientSession()
            
            # Test 1: Login and get session
            await self._test_login_and_session()
            
            # Test 2: Exchange account listing
            await self._test_exchange_listing()
            
            # Test 3: Exchange removal (safe)
            await self._test_exchange_removal_safety()
            
            # Test 4: Server stability under errors
            await self._test_server_stability()
            
            # Test 5: HTX integration preservation
            await self._test_htx_integration()
            
            # Test 6: Database persistence
            await self._test_database_persistence()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        finally:
            if self.session:
                await self.session.close()
        
        return True
    
    async def _test_login_and_session(self):
        """Test login and session management."""
        logger.info("[TEST 1] Testing Login and Session Management...")
        
        try:
            # Login to get session
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            async with self.session.post(f"{BASE_URL}/login", data=login_data) as response:
                if response.status == 302:  # Redirect after successful login
                    # Get session cookie
                    cookies = response.cookies
                    if 'session_id' in cookies:
                        self.session_cookie = cookies['session_id'].value
                        logger.info(f"✅ Login successful, session: {self.session_cookie[:16]}...")
                        self.test_results['login_session'] = 'PASS'
                    else:
                        logger.error("❌ No session cookie received")
                        self.test_results['login_session'] = 'FAIL'
                else:
                    logger.error(f"❌ Login failed: {response.status}")
                    self.test_results['login_session'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Login test failed: {e}")
            self.test_results['login_session'] = 'FAIL'
    
    async def _test_exchange_listing(self):
        """Test exchange account listing."""
        logger.info("[TEST 2] Testing Exchange Account Listing...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping exchange listing test")
                self.test_results['exchange_listing'] = 'SKIP'
                return
            
            # Set session cookie for authenticated requests
            self.session.cookie_jar.update_cookies({'session_id': self.session_cookie})
            
            async with self.session.get(f"{BASE_URL}/api/exchanges/list") as response:
                if response.status == 200:
                    exchanges_data = await response.json()
                    if 'exchanges' in exchanges_data:
                        exchanges = exchanges_data['exchanges']
                        logger.info(f"✅ Exchange listing successful: {len(exchanges)} exchanges")
                        
                        # Check that only HTX remains (corrupted Binance should be gone)
                        htx_found = any(ex.get('exchange_name') == 'HTX' for ex in exchanges)
                        binance_found = any(ex.get('exchange_name') == 'Binance' for ex in exchanges)
                        
                        if htx_found and not binance_found:
                            logger.info("✅ Cleanup successful: HTX present, corrupted Binance removed")
                            self.test_results['exchange_listing'] = 'PASS'
                        elif htx_found and binance_found:
                            logger.warning("⚠️ HTX present but Binance still exists")
                            self.test_results['exchange_listing'] = 'PARTIAL'
                        else:
                            logger.error("❌ HTX not found in exchange list")
                            self.test_results['exchange_listing'] = 'FAIL'
                    else:
                        logger.error("❌ Invalid exchange listing response format")
                        self.test_results['exchange_listing'] = 'FAIL'
                else:
                    logger.error(f"❌ Exchange listing failed: {response.status}")
                    self.test_results['exchange_listing'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Exchange listing test failed: {e}")
            self.test_results['exchange_listing'] = 'FAIL'
    
    async def _test_exchange_removal_safety(self):
        """Test exchange removal safety (should not crash server)."""
        logger.info("[TEST 3] Testing Exchange Removal Safety...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping removal safety test")
                self.test_results['removal_safety'] = 'SKIP'
                return
            
            # Try to remove a non-existent exchange (should handle gracefully)
            fake_exchange_id = 99999
            
            async with self.session.delete(f"{BASE_URL}/api/exchanges/remove/{fake_exchange_id}") as response:
                # Server should not crash and should return appropriate error
                if response.status in [404, 400, 500]:
                    response_data = await response.json()
                    if 'error' in response_data or 'success' in response_data:
                        logger.info("✅ Exchange removal handles non-existent IDs gracefully")
                        self.test_results['removal_safety'] = 'PASS'
                    else:
                        logger.warning("⚠️ Unexpected response format for non-existent exchange")
                        self.test_results['removal_safety'] = 'PARTIAL'
                else:
                    logger.error(f"❌ Unexpected status for non-existent exchange: {response.status}")
                    self.test_results['removal_safety'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Exchange removal safety test failed: {e}")
            self.test_results['removal_safety'] = 'FAIL'
    
    async def _test_server_stability(self):
        """Test server stability under various error conditions."""
        logger.info("[TEST 4] Testing Server Stability Under Errors...")
        
        try:
            stability_tests = [
                # Test invalid JSON
                ('/api/exchanges/add', {'invalid': 'json', 'missing': 'fields'}),
                # Test invalid exchange ID
                ('/api/exchanges/details/invalid_id', None),
                # Test missing authentication
                ('/api/exchanges/list', None, False),  # No auth
            ]
            
            passed_tests = 0
            total_tests = len(stability_tests)
            
            for i, test_data in enumerate(stability_tests):
                if len(test_data) == 3:
                    endpoint, data, use_auth = test_data
                else:
                    endpoint, data = test_data
                    use_auth = True
                
                logger.info(f"🔍 Stability test {i+1}/{total_tests}: {endpoint}")
                
                try:
                    # Prepare headers
                    headers = {}
                    if use_auth and self.session_cookie:
                        headers['Cookie'] = f'session_id={self.session_cookie}'
                    
                    if data:
                        async with self.session.post(f"{BASE_URL}{endpoint}", 
                                                   json=data, headers=headers) as response:
                            # Server should respond (not crash)
                            if response.status in [200, 400, 401, 404, 500]:
                                logger.info(f"✅ Server stable for {endpoint}")
                                passed_tests += 1
                            else:
                                logger.warning(f"⚠️ Unexpected status {response.status} for {endpoint}")
                    else:
                        async with self.session.get(f"{BASE_URL}{endpoint}", 
                                                  headers=headers) as response:
                            # Server should respond (not crash)
                            if response.status in [200, 400, 401, 404, 500]:
                                logger.info(f"✅ Server stable for {endpoint}")
                                passed_tests += 1
                            else:
                                logger.warning(f"⚠️ Unexpected status {response.status} for {endpoint}")
                                
                except Exception as test_error:
                    logger.warning(f"⚠️ Stability test error for {endpoint}: {test_error}")
                    # Server didn't crash, just had an error
                    passed_tests += 1
            
            if passed_tests == total_tests:
                logger.info("✅ Server stability tests passed")
                self.test_results['server_stability'] = 'PASS'
            elif passed_tests > 0:
                logger.info(f"⚠️ Server stability partial: {passed_tests}/{total_tests}")
                self.test_results['server_stability'] = 'PARTIAL'
            else:
                logger.error("❌ Server stability tests failed")
                self.test_results['server_stability'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Server stability test failed: {e}")
            self.test_results['server_stability'] = 'FAIL'
    
    async def _test_htx_integration(self):
        """Test HTX integration preservation."""
        logger.info("[TEST 5] Testing HTX Integration Preservation...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping HTX integration test")
                self.test_results['htx_integration'] = 'SKIP'
                return
            
            # Test HTX account info
            async with self.session.get(f"{BASE_URL}/api/trading/account-info/HTX") as response:
                if response.status == 200:
                    account_data = await response.json()
                    if 'success' in account_data and account_data['success']:
                        logger.info("✅ HTX account info accessible")
                        htx_result = 'PASS'
                    else:
                        logger.warning("⚠️ HTX account info returned error")
                        htx_result = 'PARTIAL'
                else:
                    logger.error(f"❌ HTX account info failed: {response.status}")
                    htx_result = 'FAIL'
            
            # Test HTX balance
            async with self.session.get(f"{BASE_URL}/api/balance/HTX") as response:
                if response.status == 200:
                    balance_data = await response.json()
                    if 'balance' in balance_data or 'data' in balance_data:
                        logger.info("✅ HTX balance accessible")
                        balance_result = 'PASS'
                    else:
                        logger.warning("⚠️ HTX balance returned unexpected format")
                        balance_result = 'PARTIAL'
                else:
                    logger.warning(f"⚠️ HTX balance failed: {response.status}")
                    balance_result = 'PARTIAL'  # May be expected for some API restrictions
            
            # Overall HTX integration result
            if htx_result == 'PASS' and balance_result == 'PASS':
                self.test_results['htx_integration'] = 'PASS'
            elif htx_result == 'PASS' or balance_result == 'PASS':
                self.test_results['htx_integration'] = 'PARTIAL'
            else:
                self.test_results['htx_integration'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"HTX integration test failed: {e}")
            self.test_results['htx_integration'] = 'FAIL'
    
    async def _test_database_persistence(self):
        """Test database persistence and connection handling."""
        logger.info("[TEST 6] Testing Database Persistence...")
        
        try:
            # Test multiple rapid requests to check database connection stability
            rapid_requests = []
            for i in range(5):
                rapid_requests.append(
                    self.session.get(f"{BASE_URL}/api/exchanges/list")
                )
            
            # Execute all requests concurrently
            responses = await asyncio.gather(*rapid_requests, return_exceptions=True)
            
            success_count = 0
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    logger.warning(f"⚠️ Rapid request {i+1} failed: {response}")
                else:
                    if response.status == 200:
                        success_count += 1
                        logger.info(f"✅ Rapid request {i+1} successful")
                    else:
                        logger.warning(f"⚠️ Rapid request {i+1} status: {response.status}")
                    await response.release()
            
            if success_count >= 4:  # Allow 1 failure
                logger.info("✅ Database persistence tests passed")
                self.test_results['database_persistence'] = 'PASS'
            elif success_count >= 2:
                logger.info("⚠️ Database persistence partially working")
                self.test_results['database_persistence'] = 'PARTIAL'
            else:
                logger.error("❌ Database persistence tests failed")
                self.test_results['database_persistence'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Database persistence test failed: {e}")
            self.test_results['database_persistence'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("SERVER CRASH AND EXCHANGE REMOVAL FIXES TEST RESULTS")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] Server crash and exchange removal fixes working!")
            logger.info("\nThe Money Circle platform should now have:")
            logger.info("- Stable server operation without crashes")
            logger.info("- Safe exchange account removal functionality")
            logger.info("- Preserved HTX futures trading integration")
            logger.info("- Robust error handling and database persistence")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical issues still need attention")
        
        logger.info("=" * 70)

async def main():
    """Main test function."""
    tester = ServerCrashFixTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 Server crash and exchange removal fixes test suite completed!")
    else:
        print("\n❌ Server crash and exchange removal fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
