#!/usr/bin/env python3
"""
🎯 Real-Time Strategy Performance Monitor

Monitors live trading strategies in real-time, tracks performance metrics,
and provides instant alerts for the Epinnox investment club dashboard.
"""

import asyncio
import json
import time
import logging
import sqlite3
import websockets
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import numpy as np

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RealTimeMetrics:
    """Real-time performance metrics."""
    strategy_name: str
    timestamp: float
    current_pnl: float
    daily_pnl: float
    total_trades: int
    win_rate: float
    current_drawdown: float
    sharpe_ratio: float
    last_signal_time: float
    last_signal_action: str
    confidence_score: float
    risk_level: str

@dataclass
class PerformanceAlert:
    """Performance alert."""
    alert_type: str
    severity: str  # INFO, WARNING, CRITICAL
    message: str
    timestamp: float
    strategy_name: str
    metric_value: float
    threshold: float

class RealTimePerformanceMonitor:
    """Monitor strategy performance in real-time."""
    
    def __init__(self, db_path: str = "data/bus.db"):
        self.db_path = db_path
        self.monitoring = False
        self.metrics_history = {}
        self.alerts = []
        self.websocket_clients = set()
        
        # Performance thresholds
        self.thresholds = {
            "max_daily_loss": 5.0,      # $5 daily loss limit
            "max_drawdown": 10.0,       # 10% drawdown limit
            "min_win_rate": 0.4,        # 40% minimum win rate
            "max_consecutive_losses": 3, # 3 consecutive losses
            "min_confidence": 0.6,      # 60% minimum confidence
            "max_position_risk": 2.0    # $2 maximum position risk
        }
    
    async def start_monitoring(self):
        """Start real-time performance monitoring."""
        logger.info("🎯 Starting real-time performance monitoring")
        self.monitoring = True
        
        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self._monitor_performance_loop()),
            asyncio.create_task(self._monitor_alerts_loop()),
            asyncio.create_task(self._websocket_server())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("🛑 Stopping performance monitoring")
            self.monitoring = False
    
    async def _monitor_performance_loop(self):
        """Main performance monitoring loop."""
        logger.info("📊 Performance monitoring loop started")
        
        while self.monitoring:
            try:
                # Get active strategies
                strategies = await self._get_active_strategies()
                
                for strategy_name in strategies:
                    # Calculate real-time metrics
                    metrics = await self._calculate_real_time_metrics(strategy_name)
                    
                    # Store metrics
                    if strategy_name not in self.metrics_history:
                        self.metrics_history[strategy_name] = []
                    
                    self.metrics_history[strategy_name].append(metrics)
                    
                    # Keep only last 1000 metrics per strategy
                    if len(self.metrics_history[strategy_name]) > 1000:
                        self.metrics_history[strategy_name] = self.metrics_history[strategy_name][-1000:]
                    
                    # Check for alerts
                    await self._check_performance_alerts(metrics)
                    
                    # Broadcast to WebSocket clients
                    await self._broadcast_metrics(metrics)
                
                # Wait before next update
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Performance monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def _monitor_alerts_loop(self):
        """Monitor and process alerts."""
        logger.info("🚨 Alert monitoring loop started")
        
        while self.monitoring:
            try:
                # Process pending alerts
                if self.alerts:
                    recent_alerts = [a for a in self.alerts if time.time() - a.timestamp < 300]  # Last 5 minutes
                    
                    # Group alerts by severity
                    critical_alerts = [a for a in recent_alerts if a.severity == "CRITICAL"]
                    warning_alerts = [a for a in recent_alerts if a.severity == "WARNING"]
                    
                    if critical_alerts:
                        await self._handle_critical_alerts(critical_alerts)
                    
                    if warning_alerts:
                        await self._handle_warning_alerts(warning_alerts)
                
                await asyncio.sleep(30)  # Check alerts every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Alert monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _websocket_server(self):
        """WebSocket server for real-time updates."""
        async def handle_client(websocket, path):
            logger.info(f"📡 WebSocket client connected: {websocket.remote_address}")
            self.websocket_clients.add(websocket)
            
            try:
                # Send initial data
                await self._send_initial_data(websocket)
                
                # Keep connection alive
                async for message in websocket:
                    # Handle client messages if needed
                    pass
                    
            except websockets.exceptions.ConnectionClosed:
                logger.info(f"📡 WebSocket client disconnected: {websocket.remote_address}")
            finally:
                self.websocket_clients.discard(websocket)
        
        # Start WebSocket server on port 8083
        try:
            server = await websockets.serve(handle_client, "localhost", 8083)
            logger.info("📡 WebSocket server started on ws://localhost:8083")
            await server.wait_closed()
        except Exception as e:
            logger.error(f"❌ WebSocket server error: {e}")
    
    async def _get_active_strategies(self) -> List[str]:
        """Get list of currently active strategies."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get recent strategy activity
            cutoff_time = time.time() - 300  # Last 5 minutes
            cursor.execute("""
                SELECT DISTINCT json_extract(message, '$.strategy') as strategy
                FROM messages 
                WHERE topic LIKE '%signals%' 
                AND timestamp > ?
                AND json_extract(message, '$.strategy') IS NOT NULL
            """, (cutoff_time,))
            
            strategies = [row[0] for row in cursor.fetchall() if row[0]]
            conn.close()
            
            return strategies
            
        except Exception as e:
            logger.error(f"❌ Failed to get active strategies: {e}")
            return []
    
    async def _calculate_real_time_metrics(self, strategy_name: str) -> RealTimeMetrics:
        """Calculate real-time performance metrics for a strategy."""
        try:
            # Get recent performance data
            performance_data = await self._get_recent_performance_data(strategy_name)
            
            if not performance_data:
                return self._create_empty_metrics(strategy_name)
            
            # Calculate metrics
            trades = [d for d in performance_data if d.get('action') in ['BUY', 'SELL']]
            signals = [d for d in performance_data if d.get('signal_type')]
            
            # Basic metrics
            total_trades = len(trades)
            winning_trades = sum(1 for t in trades if t.get('pnl', 0) > 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # P&L calculations
            current_pnl = sum(t.get('pnl', 0) for t in trades)
            daily_trades = [t for t in trades if time.time() - t.get('timestamp', 0) < 86400]
            daily_pnl = sum(t.get('pnl', 0) for t in daily_trades)
            
            # Drawdown calculation
            pnls = [t.get('pnl', 0) for t in trades]
            if pnls:
                cumulative_pnl = np.cumsum(pnls)
                running_max = np.maximum.accumulate(cumulative_pnl)
                drawdowns = (running_max - cumulative_pnl) / np.maximum(running_max, 1)
                current_drawdown = drawdowns[-1] if len(drawdowns) > 0 else 0
            else:
                current_drawdown = 0
            
            # Sharpe ratio (simplified)
            returns = np.array(pnls) if pnls else np.array([0])
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            
            # Last signal info
            last_signal = signals[-1] if signals else {}
            last_signal_time = last_signal.get('timestamp', 0)
            last_signal_action = last_signal.get('action', 'NONE')
            confidence_score = last_signal.get('confidence', 0)
            
            # Risk assessment
            risk_level = self._assess_risk_level(current_drawdown, daily_pnl, win_rate)
            
            return RealTimeMetrics(
                strategy_name=strategy_name,
                timestamp=time.time(),
                current_pnl=current_pnl,
                daily_pnl=daily_pnl,
                total_trades=total_trades,
                win_rate=win_rate,
                current_drawdown=current_drawdown,
                sharpe_ratio=sharpe_ratio,
                last_signal_time=last_signal_time,
                last_signal_action=last_signal_action,
                confidence_score=confidence_score,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate metrics for {strategy_name}: {e}")
            return self._create_empty_metrics(strategy_name)
    
    async def _get_recent_performance_data(self, strategy_name: str) -> List[Dict]:
        """Get recent performance data for a strategy."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get data from last 24 hours
            cutoff_time = time.time() - 86400
            
            cursor.execute("""
                SELECT message FROM messages 
                WHERE (topic LIKE '%signals%' OR topic LIKE '%trades%')
                AND timestamp > ? 
                ORDER BY timestamp DESC
                LIMIT 1000
            """, (cutoff_time,))
            
            messages = cursor.fetchall()
            conn.close()
            
            # Parse and filter messages
            performance_data = []
            for msg in messages:
                try:
                    data = json.loads(msg[0])
                    if data.get('strategy') == strategy_name:
                        performance_data.append(data)
                except:
                    continue
            
            return performance_data
            
        except Exception as e:
            logger.error(f"❌ Failed to get performance data: {e}")
            return []
    
    def _create_empty_metrics(self, strategy_name: str) -> RealTimeMetrics:
        """Create empty metrics for inactive strategies."""
        return RealTimeMetrics(
            strategy_name=strategy_name,
            timestamp=time.time(),
            current_pnl=0.0,
            daily_pnl=0.0,
            total_trades=0,
            win_rate=0.0,
            current_drawdown=0.0,
            sharpe_ratio=0.0,
            last_signal_time=0.0,
            last_signal_action="NONE",
            confidence_score=0.0,
            risk_level="LOW"
        )
    
    def _assess_risk_level(self, drawdown: float, daily_pnl: float, win_rate: float) -> str:
        """Assess current risk level."""
        if drawdown > 0.15 or daily_pnl < -5.0:
            return "HIGH"
        elif drawdown > 0.1 or daily_pnl < -3.0 or win_rate < 0.3:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _check_performance_alerts(self, metrics: RealTimeMetrics):
        """Check for performance alerts."""
        alerts = []
        
        # Daily loss alert
        if metrics.daily_pnl < -self.thresholds["max_daily_loss"]:
            alerts.append(PerformanceAlert(
                alert_type="DAILY_LOSS",
                severity="CRITICAL",
                message=f"Daily loss ${abs(metrics.daily_pnl):.2f} exceeds limit ${self.thresholds['max_daily_loss']:.2f}",
                timestamp=time.time(),
                strategy_name=metrics.strategy_name,
                metric_value=abs(metrics.daily_pnl),
                threshold=self.thresholds["max_daily_loss"]
            ))
        
        # Drawdown alert
        if metrics.current_drawdown > self.thresholds["max_drawdown"] / 100:
            alerts.append(PerformanceAlert(
                alert_type="DRAWDOWN",
                severity="WARNING",
                message=f"Drawdown {metrics.current_drawdown:.1%} exceeds limit {self.thresholds['max_drawdown']:.1%}",
                timestamp=time.time(),
                strategy_name=metrics.strategy_name,
                metric_value=metrics.current_drawdown * 100,
                threshold=self.thresholds["max_drawdown"]
            ))
        
        # Win rate alert
        if metrics.total_trades >= 5 and metrics.win_rate < self.thresholds["min_win_rate"]:
            alerts.append(PerformanceAlert(
                alert_type="WIN_RATE",
                severity="WARNING",
                message=f"Win rate {metrics.win_rate:.1%} below minimum {self.thresholds['min_win_rate']:.1%}",
                timestamp=time.time(),
                strategy_name=metrics.strategy_name,
                metric_value=metrics.win_rate * 100,
                threshold=self.thresholds["min_win_rate"] * 100
            ))
        
        # Add alerts to queue
        self.alerts.extend(alerts)
        
        # Log alerts
        for alert in alerts:
            if alert.severity == "CRITICAL":
                logger.error(f"🚨 CRITICAL: {alert.message}")
            else:
                logger.warning(f"⚠️ WARNING: {alert.message}")
    
    async def _handle_critical_alerts(self, alerts: List[PerformanceAlert]):
        """Handle critical performance alerts."""
        logger.error(f"🚨 Handling {len(alerts)} critical alerts")
        
        for alert in alerts:
            if alert.alert_type == "DAILY_LOSS":
                # Consider stopping strategy for the day
                logger.error(f"💰 Strategy {alert.strategy_name} hit daily loss limit")
                # Could implement automatic strategy stopping here
    
    async def _handle_warning_alerts(self, alerts: List[PerformanceAlert]):
        """Handle warning alerts."""
        logger.warning(f"⚠️ Processing {len(alerts)} warning alerts")
        
        # Group alerts by strategy
        strategy_alerts = {}
        for alert in alerts:
            if alert.strategy_name not in strategy_alerts:
                strategy_alerts[alert.strategy_name] = []
            strategy_alerts[alert.strategy_name].append(alert)
        
        # Log strategy-specific warnings
        for strategy, strategy_alert_list in strategy_alerts.items():
            logger.warning(f"⚠️ Strategy {strategy} has {len(strategy_alert_list)} warnings")
    
    async def _broadcast_metrics(self, metrics: RealTimeMetrics):
        """Broadcast metrics to WebSocket clients."""
        if not self.websocket_clients:
            return
        
        message = {
            "type": "performance_update",
            "data": asdict(metrics),
            "timestamp": time.time()
        }
        
        # Send to all connected clients
        disconnected_clients = set()
        for client in self.websocket_clients:
            try:
                await client.send(json.dumps(message))
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        self.websocket_clients -= disconnected_clients
    
    async def _send_initial_data(self, websocket):
        """Send initial data to new WebSocket client."""
        initial_data = {
            "type": "initial_data",
            "metrics_history": {
                strategy: [asdict(m) for m in metrics[-10:]]  # Last 10 metrics
                for strategy, metrics in self.metrics_history.items()
            },
            "recent_alerts": [asdict(a) for a in self.alerts[-20:]],  # Last 20 alerts
            "thresholds": self.thresholds,
            "timestamp": time.time()
        }
        
        await websocket.send(json.dumps(initial_data))
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get current performance summary."""
        summary = {
            "timestamp": time.time(),
            "active_strategies": len(self.metrics_history),
            "total_alerts": len(self.alerts),
            "critical_alerts": len([a for a in self.alerts if a.severity == "CRITICAL"]),
            "strategies": {}
        }
        
        for strategy, metrics_list in self.metrics_history.items():
            if metrics_list:
                latest = metrics_list[-1]
                summary["strategies"][strategy] = {
                    "current_pnl": latest.current_pnl,
                    "daily_pnl": latest.daily_pnl,
                    "win_rate": latest.win_rate,
                    "risk_level": latest.risk_level,
                    "last_update": latest.timestamp
                }
        
        return summary

async def main():
    """Main monitor runner."""
    logger.info("🎯 Real-Time Strategy Performance Monitor")
    logger.info("=" * 50)
    logger.info("📡 WebSocket server will be available at ws://localhost:8083")
    logger.info("🔄 Performance updates every 5 seconds")
    logger.info("🚨 Alert monitoring every 30 seconds")
    logger.info("")
    
    monitor = RealTimePerformanceMonitor()
    
    try:
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        logger.info("🛑 Performance monitoring stopped")

if __name__ == "__main__":
    asyncio.run(main())
