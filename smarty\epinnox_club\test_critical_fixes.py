#!/usr/bin/env python3
"""
Test Critical Database and CSRF Fixes
Comprehensive test for all the critical issues identified in the logs
"""

import asyncio
import logging
import sys
import time
import aiohttp
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8086"  # Using the correct port from logs

class CriticalFixesTester:
    """Test all critical fixes."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = None
        self.csrf_token = None
        self.test_results = {}
        logger.info("Critical Fixes Tester initialized")
    
    async def run_all_tests(self):
        """Run all critical fix tests."""
        try:
            logger.info("=" * 70)
            logger.info("MONEY CIRCLE CRITICAL FIXES TEST SUITE")
            logger.info("=" * 70)
            
            # Create session
            self.session = aiohttp.ClientSession()
            
            # Test 1: Database Connection Issues
            await self._test_database_connections()
            
            # Test 2: CSRF Token Integration
            await self._test_csrf_integration()
            
            # Test 3: Encryption/Decryption Fixes
            await self._test_encryption_fixes()
            
            # Test 4: API Endpoints with CSRF
            await self._test_api_endpoints_with_csrf()
            
            # Test 5: Exchange Balance API
            await self._test_exchange_balance_api()
            
            # Test 6: Portfolio API
            await self._test_portfolio_api()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        finally:
            if self.session:
                await self.session.close()
        
        return True
    
    async def _test_database_connections(self):
        """Test database connection fixes."""
        logger.info("[TEST 1] Testing Database Connection Fixes...")
        
        try:
            from database.models import DatabaseManager
            from auth.user_manager import UserManager
            from dashboards.personal_dashboard import PersonalDashboard
            
            # Test database manager
            db = DatabaseManager()
            if db.ensure_connection():
                logger.info("✅ Database manager connection working")
                db_result = 'PASS'
            else:
                logger.error("❌ Database manager connection failed")
                db_result = 'FAIL'
            
            # Test user manager
            user_manager = UserManager()
            if user_manager._ensure_connection():
                logger.info("✅ User manager connection working")
                user_result = 'PASS'
            else:
                logger.error("❌ User manager connection failed")
                user_result = 'FAIL'
            
            # Test personal dashboard
            dashboard = PersonalDashboard()
            metrics = dashboard._get_default_performance_metrics()
            if metrics and 'daily_pnl' in metrics:
                logger.info("✅ Personal dashboard default metrics working")
                dashboard_result = 'PASS'
            else:
                logger.error("❌ Personal dashboard metrics failed")
                dashboard_result = 'FAIL'
            
            # Overall result
            if db_result == 'PASS' and user_result == 'PASS' and dashboard_result == 'PASS':
                self.test_results['database_connections'] = 'PASS'
            elif db_result == 'PASS' or user_result == 'PASS':
                self.test_results['database_connections'] = 'PARTIAL'
            else:
                self.test_results['database_connections'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            self.test_results['database_connections'] = 'FAIL'
    
    async def _test_csrf_integration(self):
        """Test CSRF token integration."""
        logger.info("[TEST 2] Testing CSRF Token Integration...")
        
        try:
            # Test CSRF token API
            async with self.session.get(f"{BASE_URL}/api/csrf-token") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'csrf_token' in data and data['csrf_token']:
                        self.csrf_token = data['csrf_token']
                        logger.info(f"✅ CSRF token received: {self.csrf_token[:16]}...")
                        
                        # Test CSRF fix files exist
                        csrf_fix_file = Path('static/js/csrf_fix.js')
                        dashboard_csrf_file = Path('static/js/dashboard_csrf_fix.js')
                        
                        if csrf_fix_file.exists() and dashboard_csrf_file.exists():
                            logger.info("✅ CSRF fix files exist")
                            self.test_results['csrf_integration'] = 'PASS'
                        else:
                            logger.warning("⚠️ Some CSRF fix files missing")
                            self.test_results['csrf_integration'] = 'PARTIAL'
                    else:
                        logger.error("❌ CSRF token not in response")
                        self.test_results['csrf_integration'] = 'FAIL'
                else:
                    logger.error(f"❌ CSRF token API failed: {response.status}")
                    self.test_results['csrf_integration'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"CSRF integration test failed: {e}")
            self.test_results['csrf_integration'] = 'FAIL'
    
    async def _test_encryption_fixes(self):
        """Test encryption/decryption fixes."""
        logger.info("[TEST 3] Testing Encryption/Decryption Fixes...")
        
        try:
            from exchanges.encryption_utils import EncryptionManager
            
            # Create encryption manager
            encryption_manager = EncryptionManager()
            logger.info("✅ Encryption manager created")
            
            # Test empty data handling
            empty_result = encryption_manager.decrypt(b'')
            if empty_result == '':
                logger.info("✅ Empty data handling working")
            else:
                logger.error("❌ Empty data handling failed")
                self.test_results['encryption_fixes'] = 'FAIL'
                return
            
            # Test normal encryption/decryption
            test_data = "test_api_key_12345"
            encrypted = encryption_manager.encrypt(test_data)
            decrypted = encryption_manager.decrypt(encrypted)
            
            if decrypted == test_data:
                logger.info("✅ Normal encryption/decryption working")
            else:
                logger.error("❌ Normal encryption/decryption failed")
                self.test_results['encryption_fixes'] = 'FAIL'
                return
            
            # Test API credential handling with empty data
            empty_creds = encryption_manager.decrypt_api_credentials({})
            if (empty_creds and 'api_key' in empty_creds and 
                empty_creds['api_key'] == '' and empty_creds['secret_key'] == ''):
                logger.info("✅ Empty API credentials handling working")
                self.test_results['encryption_fixes'] = 'PASS'
            else:
                logger.error("❌ Empty API credentials handling failed")
                self.test_results['encryption_fixes'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Encryption fixes test failed: {e}")
            self.test_results['encryption_fixes'] = 'FAIL'
    
    async def _test_api_endpoints_with_csrf(self):
        """Test API endpoints with CSRF tokens."""
        logger.info("[TEST 4] Testing API Endpoints with CSRF...")
        
        try:
            if not self.csrf_token:
                logger.warning("⚠️ No CSRF token available, skipping API tests")
                self.test_results['api_endpoints_csrf'] = 'SKIP'
                return
            
            headers = {
                'X-CSRF-Token': self.csrf_token,
                'Content-Type': 'application/json'
            }
            
            # Test symbols API (GET - no CSRF needed)
            async with self.session.get(f"{BASE_URL}/api/symbols") as response:
                if response.status == 200:
                    logger.info("✅ Symbols API working")
                    symbols_result = 'PASS'
                else:
                    logger.warning(f"⚠️ Symbols API returned {response.status}")
                    symbols_result = 'PARTIAL'
            
            # Test exchange test endpoint (POST - needs CSRF)
            async with self.session.post(
                f"{BASE_URL}/api/exchanges/test/42",
                headers=headers,
                json={}
            ) as response:
                if response.status in [200, 401, 404]:  # Not 403 (CSRF error)
                    logger.info("✅ Exchange test API accepts CSRF token")
                    test_result = 'PASS'
                elif response.status == 403:
                    error_data = await response.json()
                    if 'CSRF' in error_data.get('error', ''):
                        logger.error("❌ CSRF token still being rejected")
                        test_result = 'FAIL'
                    else:
                        logger.info("✅ CSRF token accepted, other authorization issue")
                        test_result = 'PASS'
                else:
                    logger.warning(f"⚠️ Exchange test API returned {response.status}")
                    test_result = 'PARTIAL'
            
            # Overall result
            if symbols_result == 'PASS' and test_result == 'PASS':
                self.test_results['api_endpoints_csrf'] = 'PASS'
            elif symbols_result == 'PASS' or test_result == 'PASS':
                self.test_results['api_endpoints_csrf'] = 'PARTIAL'
            else:
                self.test_results['api_endpoints_csrf'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"API endpoints CSRF test failed: {e}")
            self.test_results['api_endpoints_csrf'] = 'FAIL'
    
    async def _test_exchange_balance_api(self):
        """Test exchange balance API with CSRF."""
        logger.info("[TEST 5] Testing Exchange Balance API...")
        
        try:
            if not self.csrf_token:
                logger.warning("⚠️ No CSRF token available, skipping balance test")
                self.test_results['exchange_balance_api'] = 'SKIP'
                return
            
            headers = {
                'X-CSRF-Token': self.csrf_token,
                'Content-Type': 'application/json'
            }
            
            async with self.session.post(
                f"{BASE_URL}/api/exchanges/balance/42",
                headers=headers,
                json={}
            ) as response:
                if response.status in [200, 401, 404]:  # Not 403 (CSRF error)
                    logger.info("✅ Exchange balance API accepts CSRF token")
                    self.test_results['exchange_balance_api'] = 'PASS'
                elif response.status == 403:
                    error_data = await response.json()
                    if 'CSRF' in error_data.get('error', ''):
                        logger.error("❌ CSRF token still being rejected")
                        self.test_results['exchange_balance_api'] = 'FAIL'
                    else:
                        logger.info("✅ CSRF token accepted, other authorization issue")
                        self.test_results['exchange_balance_api'] = 'PASS'
                else:
                    logger.warning(f"⚠️ Exchange balance API returned {response.status}")
                    self.test_results['exchange_balance_api'] = 'PARTIAL'
                    
        except Exception as e:
            logger.error(f"Exchange balance API test failed: {e}")
            self.test_results['exchange_balance_api'] = 'FAIL'
    
    async def _test_portfolio_api(self):
        """Test portfolio API for database connection issues."""
        logger.info("[TEST 6] Testing Portfolio API...")
        
        try:
            async with self.session.get(f"{BASE_URL}/api/portfolio") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data:
                        logger.info("✅ Portfolio API working without database errors")
                        self.test_results['portfolio_api'] = 'PASS'
                    else:
                        logger.warning(f"⚠️ Portfolio API returned error: {data['error']}")
                        self.test_results['portfolio_api'] = 'PARTIAL'
                else:
                    logger.warning(f"⚠️ Portfolio API returned {response.status}")
                    self.test_results['portfolio_api'] = 'PARTIAL'
                    
        except Exception as e:
            logger.error(f"Portfolio API test failed: {e}")
            self.test_results['portfolio_api'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("CRITICAL FIXES TEST RESULTS SUMMARY")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] All critical fixes are working!")
            logger.info("\nThe Money Circle platform should now work without:")
            logger.info("- Database connection errors")
            logger.info("- CSRF token missing errors")
            logger.info("- Encryption decryption failures")
            logger.info("- Rate limiting blocks for localhost")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most critical fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical issues still need attention")
        
        logger.info("=" * 70)

async def main():
    """Main test function."""
    tester = CriticalFixesTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 Critical fixes test suite completed!")
        print("You can now test the Money Circle dashboard at http://localhost:8086")
    else:
        print("\n❌ Critical fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
