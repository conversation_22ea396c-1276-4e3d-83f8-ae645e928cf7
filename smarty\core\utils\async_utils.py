"""
Async utility functions for the smart-trader system.
"""

import asyncio
import functools
import logging
import time
from typing import Any, Callable, Optional, TypeVar, Union, Dict
from datetime import datetime

logger = logging.getLogger(__name__)

T = TypeVar('T')


async def retry_async(
    func: Callable[..., T],
    retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    on_retry: Optional[Callable[[int, Exception], None]] = None
) -> T:
    """
    Retry an async function with exponential backoff.

    Args:
        func: Async function to retry
        retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exceptions to catch and retry
        on_retry: Optional callback called on each retry

    Returns:
        Result of the function call

    Raises:
        Last exception if all retries fail
    """
    last_exception = None
    current_delay = delay

    for attempt in range(retries + 1):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func()
            else:
                return func()
        except exceptions as e:
            last_exception = e
            
            if attempt == retries:
                logger.error(f"Function {func.__name__} failed after {retries} retries: {e}")
                break
            
            if on_retry:
                on_retry(attempt + 1, e)
            
            logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                         f"Retrying in {current_delay:.2f}s")
            
            await asyncio.sleep(current_delay)
            current_delay *= backoff_factor

    raise last_exception


def retry_async_decorator(
    retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator version of retry_async.
    
    Args:
        retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_async(
                lambda: func(*args, **kwargs),
                retries=retries,
                delay=delay,
                backoff_factor=backoff_factor,
                exceptions=exceptions
            )
        return wrapper
    return decorator


def timer(
    enable_store: bool = True,
    store_key: Optional[str] = None,
    log_result: bool = True
):
    """
    Enhanced timer decorator with improved error handling and optional feature store.

    Args:
        enable_store: Whether to store timing in feature store
        store_key: Custom key for feature store (uses function name if None)
        log_result: Whether to log the timing result

    Returns:
        Decorator function
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = store_key or func.__name__
            
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                execution_time = time.time() - start_time
                
                if log_result:
                    logger.debug(f"{function_name} executed in {execution_time:.4f}s")
                
                # Store in feature store if enabled
                if enable_store:
                    try:
                        # Import here to avoid circular imports
                        from core.feature_store import FeatureStore
                        feature_store = FeatureStore.get_instance()
                        
                        if feature_store:
                            # Use asyncio.create_task to avoid blocking
                            asyncio.create_task(
                                feature_store.store(
                                    f"timing.{function_name}",
                                    time.time(),
                                    {"execution_time": execution_time}
                                )
                            )
                    except Exception as e:
                        logger.warning(f"Failed to store timing for {function_name}: {e}")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{function_name} failed after {execution_time:.4f}s: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = store_key or func.__name__
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if log_result:
                    logger.debug(f"{function_name} executed in {execution_time:.4f}s")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{function_name} failed after {execution_time:.4f}s: {e}")
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


async def timeout_after(seconds: float, coro):
    """
    Run a coroutine with a timeout.
    
    Args:
        seconds: Timeout in seconds
        coro: Coroutine to run
        
    Returns:
        Result of the coroutine
        
    Raises:
        asyncio.TimeoutError: If timeout is exceeded
    """
    return await asyncio.wait_for(coro, timeout=seconds)


async def gather_with_concurrency(limit: int, *coroutines):
    """
    Run coroutines with limited concurrency.
    
    Args:
        limit: Maximum number of concurrent coroutines
        *coroutines: Coroutines to run
        
    Returns:
        List of results
    """
    semaphore = asyncio.Semaphore(limit)
    
    async def sem_coro(coro):
        async with semaphore:
            return await coro
    
    return await asyncio.gather(*[sem_coro(coro) for coro in coroutines])


async def run_periodic(
    func: Callable,
    interval: float,
    max_iterations: Optional[int] = None,
    stop_event: Optional[asyncio.Event] = None
):
    """
    Run a function periodically.
    
    Args:
        func: Function to run (can be sync or async)
        interval: Interval between runs in seconds
        max_iterations: Maximum number of iterations (None for infinite)
        stop_event: Event to stop the periodic execution
    """
    iteration = 0
    
    while True:
        # Check stop conditions
        if stop_event and stop_event.is_set():
            break
        if max_iterations and iteration >= max_iterations:
            break
        
        try:
            if asyncio.iscoroutinefunction(func):
                await func()
            else:
                func()
        except Exception as e:
            logger.error(f"Error in periodic function {func.__name__}: {e}")
        
        iteration += 1
        await asyncio.sleep(interval)


class AsyncContextManager:
    """
    Base class for async context managers with proper cleanup.
    """
    
    async def __aenter__(self):
        await self.setup()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        return False
    
    async def setup(self):
        """Override this method to implement setup logic."""
        pass
    
    async def cleanup(self):
        """Override this method to implement cleanup logic."""
        pass


class TaskManager:
    """
    Manage asyncio tasks with proper cleanup.
    """
    
    def __init__(self):
        self.tasks = set()
    
    def create_task(self, coro, name: Optional[str] = None) -> asyncio.Task:
        """Create and track a task."""
        task = asyncio.create_task(coro, name=name)
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        return task
    
    async def cancel_all(self, timeout: float = 5.0):
        """Cancel all tracked tasks."""
        if not self.tasks:
            return
        
        # Cancel all tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Wait for cancellation with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*self.tasks, return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.warning(f"Some tasks did not cancel within {timeout}s")
        
        self.tasks.clear()
    
    def __len__(self):
        return len(self.tasks)


if __name__ == "__main__":
    # Test async utilities
    print("Testing async utilities:")
    
    async def test_retry():
        """Test retry functionality."""
        attempt_count = 0
        
        async def failing_function():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise ValueError(f"Attempt {attempt_count} failed")
            return f"Success on attempt {attempt_count}"
        
        result = await retry_async(failing_function, retries=3, delay=0.1)
        print(f"Retry test result: {result}")
    
    @timer(enable_store=False, log_result=True)
    async def test_timer():
        """Test timer decorator."""
        await asyncio.sleep(0.1)
        return "Timer test completed"
    
    async def test_timeout():
        """Test timeout functionality."""
        try:
            await timeout_after(0.1, asyncio.sleep(0.2))
        except asyncio.TimeoutError:
            print("Timeout test: Successfully caught timeout")
    
    async def test_concurrency():
        """Test concurrency limiting."""
        async def slow_task(n):
            await asyncio.sleep(0.1)
            return n * 2
        
        tasks = [slow_task(i) for i in range(5)]
        results = await gather_with_concurrency(2, *tasks)
        print(f"Concurrency test results: {results}")
    
    async def test_task_manager():
        """Test task manager."""
        manager = TaskManager()
        
        async def dummy_task(n):
            await asyncio.sleep(0.1)
            return n
        
        # Create some tasks
        for i in range(3):
            manager.create_task(dummy_task(i))
        
        print(f"Created {len(manager)} tasks")
        await manager.cancel_all()
        print(f"Tasks after cancellation: {len(manager)}")
    
    async def run_tests():
        """Run all tests."""
        await test_retry()
        await test_timer()
        await test_timeout()
        await test_concurrency()
        await test_task_manager()
        print("Async utility tests completed")
    
    # Run tests
    asyncio.run(run_tests())
