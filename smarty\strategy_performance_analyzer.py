#!/usr/bin/env python3
"""
📊 Strategy Performance Analyzer

Advanced analytics and optimization system for live trading strategies.
Analyzes real-time performance, optimizes parameters, and provides
actionable insights for the Epinnox investment club.
"""

import asyncio
import json
import time
import logging
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Strategy performance metrics."""
    strategy_name: str
    timestamp: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    max_drawdown: float
    sharpe_ratio: float
    avg_trade_duration: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    confidence_score: float

@dataclass
class OptimizationSuggestion:
    """Strategy optimization suggestion."""
    parameter: str
    current_value: Any
    suggested_value: Any
    expected_improvement: float
    confidence: float
    reasoning: str

class StrategyPerformanceAnalyzer:
    """Advanced strategy performance analysis and optimization."""
    
    def __init__(self, db_path: str = "data/bus.db"):
        self.db_path = db_path
        self.performance_history = []
        self.optimization_suggestions = {}
        
    async def analyze_strategy_performance(self, strategy_name: str, 
                                         hours_back: int = 24) -> PerformanceMetrics:
        """Analyze strategy performance over specified time period."""
        logger.info(f"📊 Analyzing performance for {strategy_name} (last {hours_back}h)")
        
        try:
            # Get performance data from database
            performance_data = await self._get_performance_data(strategy_name, hours_back)
            
            if not performance_data:
                logger.warning(f"No performance data found for {strategy_name}")
                return self._create_empty_metrics(strategy_name)
            
            # Calculate comprehensive metrics
            metrics = self._calculate_performance_metrics(strategy_name, performance_data)
            
            # Store for historical analysis
            self.performance_history.append(metrics)
            
            logger.info(f"✅ Performance analysis complete for {strategy_name}")
            logger.info(f"   Win Rate: {metrics.win_rate:.1%}")
            logger.info(f"   Total P&L: ${metrics.total_pnl:.2f}")
            logger.info(f"   Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Performance analysis failed: {e}")
            return self._create_empty_metrics(strategy_name)
    
    async def generate_optimization_suggestions(self, strategy_name: str, 
                                              metrics: PerformanceMetrics) -> List[OptimizationSuggestion]:
        """Generate optimization suggestions based on performance analysis."""
        logger.info(f"🔧 Generating optimization suggestions for {strategy_name}")
        
        suggestions = []
        
        try:
            # Analyze win rate optimization
            if metrics.win_rate < 0.5:
                suggestions.append(OptimizationSuggestion(
                    parameter="signal_threshold",
                    current_value="0.3",
                    suggested_value="0.4",
                    expected_improvement=0.15,
                    confidence=0.8,
                    reasoning="Low win rate suggests signals are too aggressive. Increase threshold for higher quality signals."
                ))
            
            # Analyze drawdown optimization
            if metrics.max_drawdown > 0.1:  # 10%
                suggestions.append(OptimizationSuggestion(
                    parameter="position_size",
                    current_value="20.0",
                    suggested_value="15.0",
                    expected_improvement=0.25,
                    confidence=0.9,
                    reasoning="High drawdown indicates excessive risk. Reduce position size for better risk management."
                ))
            
            # Analyze Sharpe ratio optimization
            if metrics.sharpe_ratio < 1.0:
                suggestions.append(OptimizationSuggestion(
                    parameter="stop_loss_percent",
                    current_value="1.5",
                    suggested_value="1.0",
                    expected_improvement=0.2,
                    confidence=0.7,
                    reasoning="Low Sharpe ratio suggests poor risk-adjusted returns. Tighter stop loss may improve efficiency."
                ))
            
            # Analyze trade frequency optimization
            if metrics.total_trades < 5:  # Low activity
                suggestions.append(OptimizationSuggestion(
                    parameter="signal_generation_interval",
                    current_value="30",
                    suggested_value="15",
                    expected_improvement=0.3,
                    confidence=0.6,
                    reasoning="Low trade frequency. Increase signal generation frequency for more opportunities."
                ))
            
            # Store suggestions
            self.optimization_suggestions[strategy_name] = suggestions
            
            logger.info(f"✅ Generated {len(suggestions)} optimization suggestions")
            for suggestion in suggestions:
                logger.info(f"   🔧 {suggestion.parameter}: {suggestion.current_value} → {suggestion.suggested_value}")
                logger.info(f"      Expected improvement: {suggestion.expected_improvement:.1%}")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"❌ Optimization suggestion generation failed: {e}")
            return []
    
    async def optimize_live_configuration(self, strategy_name: str) -> Dict[str, Any]:
        """Generate optimized configuration for live trading."""
        logger.info(f"⚙️ Optimizing live configuration for {strategy_name}")
        
        try:
            # Get current performance
            metrics = await self.analyze_strategy_performance(strategy_name)
            suggestions = await self.generate_optimization_suggestions(strategy_name, metrics)
            
            # Load current configuration
            current_config = self._load_current_config()
            
            # Apply optimization suggestions
            optimized_config = current_config.copy()
            
            for suggestion in suggestions:
                if suggestion.confidence > 0.7:  # Only apply high-confidence suggestions
                    self._apply_optimization(optimized_config, suggestion)
            
            # Add performance-based risk adjustments
            optimized_config = self._apply_risk_adjustments(optimized_config, metrics)
            
            # Save optimized configuration
            await self._save_optimized_config(strategy_name, optimized_config)
            
            logger.info(f"✅ Optimized configuration generated for {strategy_name}")
            return optimized_config
            
        except Exception as e:
            logger.error(f"❌ Configuration optimization failed: {e}")
            return {}
    
    async def generate_performance_report(self, strategy_name: str) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        logger.info(f"📋 Generating performance report for {strategy_name}")
        
        try:
            # Get performance metrics
            metrics = await self.analyze_strategy_performance(strategy_name)
            suggestions = await self.generate_optimization_suggestions(strategy_name, metrics)
            
            # Calculate additional analytics
            risk_assessment = self._assess_risk_profile(metrics)
            market_conditions = await self._analyze_market_conditions()
            
            report = {
                "strategy_name": strategy_name,
                "generated_at": datetime.now().isoformat(),
                "performance_metrics": asdict(metrics),
                "optimization_suggestions": [asdict(s) for s in suggestions],
                "risk_assessment": risk_assessment,
                "market_conditions": market_conditions,
                "recommendations": self._generate_recommendations(metrics, suggestions)
            }
            
            # Save report
            report_file = f"reports/performance_report_{strategy_name}_{int(time.time())}.json"
            Path("reports").mkdir(exist_ok=True)
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"✅ Performance report saved to {report_file}")
            return report
            
        except Exception as e:
            logger.error(f"❌ Performance report generation failed: {e}")
            return {}
    
    async def _get_performance_data(self, strategy_name: str, hours_back: int) -> List[Dict]:
        """Get performance data from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get signals and trades data
            cutoff_time = time.time() - (hours_back * 3600)
            
            cursor.execute("""
                SELECT * FROM messages 
                WHERE topic LIKE '%signals%' 
                AND timestamp > ? 
                ORDER BY timestamp DESC
            """, (cutoff_time,))
            
            signals = cursor.fetchall()
            conn.close()
            
            # Process signals data
            performance_data = []
            for signal in signals:
                try:
                    data = json.loads(signal[2])  # message content
                    if data.get('strategy') == strategy_name:
                        performance_data.append(data)
                except:
                    continue
            
            return performance_data
            
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            return []
    
    def _calculate_performance_metrics(self, strategy_name: str, data: List[Dict]) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics."""
        if not data:
            return self._create_empty_metrics(strategy_name)
        
        # Extract trade information
        trades = []
        for item in data:
            if item.get('action') in ['BUY', 'SELL']:
                trades.append(item)
        
        if not trades:
            return self._create_empty_metrics(strategy_name)
        
        # Calculate basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t.get('pnl', 0) > 0)
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate P&L metrics
        pnls = [t.get('pnl', 0) for t in trades]
        total_pnl = sum(pnls)
        
        # Calculate drawdown
        cumulative_pnl = np.cumsum(pnls)
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdowns = (running_max - cumulative_pnl) / np.maximum(running_max, 1)
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
        
        # Calculate Sharpe ratio (simplified)
        returns = np.array(pnls)
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        # Calculate trade duration and win/loss averages
        durations = [t.get('duration', 0) for t in trades if t.get('duration')]
        avg_trade_duration = np.mean(durations) if durations else 0
        
        wins = [p for p in pnls if p > 0]
        losses = [p for p in pnls if p < 0]
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # Calculate profit factor
        gross_profit = sum(wins)
        gross_loss = abs(sum(losses))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Calculate confidence score
        confidence_scores = [t.get('confidence', 0) for t in trades]
        confidence_score = np.mean(confidence_scores) if confidence_scores else 0
        
        return PerformanceMetrics(
            strategy_name=strategy_name,
            timestamp=datetime.now().isoformat(),
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            total_pnl=total_pnl,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            avg_trade_duration=avg_trade_duration,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            confidence_score=confidence_score
        )
    
    def _create_empty_metrics(self, strategy_name: str) -> PerformanceMetrics:
        """Create empty metrics for strategies with no data."""
        return PerformanceMetrics(
            strategy_name=strategy_name,
            timestamp=datetime.now().isoformat(),
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            win_rate=0.0,
            total_pnl=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            avg_trade_duration=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            profit_factor=0.0,
            confidence_score=0.0
        )
    
    def _load_current_config(self) -> Dict[str, Any]:
        """Load current trading configuration."""
        try:
            with open("trading_config_live.yaml", 'r') as f:
                import yaml
                return yaml.safe_load(f)
        except:
            # Return default configuration
            return {
                "trading": {
                    "account_balance": 100.0,
                    "max_position_size": 20.0,
                    "min_position_size": 5.0
                },
                "strategies": {
                    "smart_model_integrated": {
                        "allocation": 40.0,
                        "min_confidence": 0.7
                    }
                }
            }
    
    def _apply_optimization(self, config: Dict[str, Any], suggestion: OptimizationSuggestion):
        """Apply optimization suggestion to configuration."""
        # This would apply the specific optimization based on the parameter
        logger.info(f"🔧 Applying optimization: {suggestion.parameter} = {suggestion.suggested_value}")
    
    def _apply_risk_adjustments(self, config: Dict[str, Any], metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Apply risk-based adjustments to configuration."""
        # Adjust position sizing based on performance
        if metrics.max_drawdown > 0.15:  # High drawdown
            config["trading"]["max_position_size"] *= 0.8
        elif metrics.sharpe_ratio > 2.0:  # Excellent performance
            config["trading"]["max_position_size"] *= 1.2
        
        return config
    
    async def _save_optimized_config(self, strategy_name: str, config: Dict[str, Any]):
        """Save optimized configuration."""
        config_file = f"configs/optimized_{strategy_name}_{int(time.time())}.yaml"
        Path("configs").mkdir(exist_ok=True)
        
        import yaml
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        logger.info(f"💾 Optimized configuration saved to {config_file}")
    
    def _assess_risk_profile(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Assess risk profile based on performance metrics."""
        risk_level = "LOW"
        if metrics.max_drawdown > 0.1:
            risk_level = "MEDIUM"
        if metrics.max_drawdown > 0.2:
            risk_level = "HIGH"
        
        return {
            "risk_level": risk_level,
            "max_drawdown": metrics.max_drawdown,
            "volatility": abs(metrics.avg_win - metrics.avg_loss),
            "consistency": metrics.win_rate
        }
    
    async def _analyze_market_conditions(self) -> Dict[str, Any]:
        """Analyze current market conditions."""
        return {
            "trend": "BULLISH",  # Would be calculated from market data
            "volatility": "MEDIUM",
            "volume": "HIGH",
            "sentiment": "POSITIVE"
        }
    
    def _generate_recommendations(self, metrics: PerformanceMetrics, 
                                suggestions: List[OptimizationSuggestion]) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if metrics.win_rate < 0.4:
            recommendations.append("Consider reducing trade frequency and increasing signal quality thresholds")
        
        if metrics.max_drawdown > 0.15:
            recommendations.append("Implement stricter risk management with smaller position sizes")
        
        if metrics.total_trades < 3:
            recommendations.append("Increase market exposure with more frequent signal generation")
        
        if len(suggestions) > 0:
            recommendations.append(f"Apply {len(suggestions)} optimization suggestions for improved performance")
        
        return recommendations

async def main():
    """Main performance analysis runner."""
    logger.info("📊 Strategy Performance Analyzer")
    logger.info("=" * 50)
    
    analyzer = StrategyPerformanceAnalyzer()
    
    # Analyze all strategies
    strategies = ["Smart Model Integrated", "Smart Strategy Only", "Order Flow"]
    
    for strategy in strategies:
        logger.info(f"\n🎯 Analyzing {strategy}")
        
        # Generate performance report
        report = await analyzer.generate_performance_report(strategy)
        
        # Generate optimized configuration
        optimized_config = await analyzer.optimize_live_configuration(strategy)
        
        logger.info(f"✅ Analysis complete for {strategy}")
    
    logger.info("\n🎉 Performance analysis complete for all strategies")

if __name__ == "__main__":
    asyncio.run(main())
