#!/bin/bash
# Money Circle Production Deployment Script
# Complete automated deployment for production environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=""
EMAIL=""
ENVIRONMENT="production"
APP_USER="money-circle"
APP_DIR="/opt/money-circle"

# Logging
LOG_FILE="/var/log/money-circle-deployment.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo_error "This script must be run as root"
        exit 1
    fi
}

# Get deployment configuration
get_config() {
    echo_info "Money Circle Production Deployment Configuration"
    echo "=============================================="
    
    read -p "Enter your domain name (e.g., money-circle.yourdomain.com): " DOMAIN
    read -p "Enter your email address (for SSL certificates): " EMAIL
    
    if [[ -z "$DOMAIN" || -z "$EMAIL" ]]; then
        echo_error "Domain and email are required"
        exit 1
    fi
    
    echo_info "Configuration:"
    echo "  Domain: $DOMAIN"
    echo "  Email: $EMAIL"
    echo "  App Directory: $APP_DIR"
    echo "  App User: $APP_USER"
    
    read -p "Continue with deployment? (y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        echo_info "Deployment cancelled"
        exit 0
    fi
}

# Update system packages
update_system() {
    echo_info "Updating system packages..."
    
    apt-get update
    apt-get upgrade -y
    apt-get autoremove -y
    
    echo_success "System packages updated"
}

# Install required packages
install_packages() {
    echo_info "Installing required packages..."
    
    apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        nginx \
        ufw \
        openssl \
        curl \
        htop \
        supervisor \
        logrotate \
        fail2ban \
        unattended-upgrades \
        certbot \
        python3-certbot-nginx \
        git \
        rsync \
        cron
    
    echo_success "Required packages installed"
}

# Create application user
create_app_user() {
    echo_info "Creating application user..."
    
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d "$APP_DIR" -m "$APP_USER"
        echo_success "Application user '$APP_USER' created"
    else
        echo_warning "Application user '$APP_USER' already exists"
    fi
}

# Setup application directory
setup_app_directory() {
    echo_info "Setting up application directory..."
    
    # Create directory structure
    mkdir -p "$APP_DIR"/{data,logs,backups,ssl,static,uploads}
    
    # Copy application files
    if [[ -d "$(pwd)" ]]; then
        rsync -av --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' \
              "$(pwd)/" "$APP_DIR/"
    else
        echo_error "Source directory not found"
        exit 1
    fi
    
    # Set ownership
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    
    # Set permissions
    chmod 755 "$APP_DIR"
    chmod 700 "$APP_DIR/ssl"
    chmod 755 "$APP_DIR"/{data,logs,backups,static,uploads}
    
    echo_success "Application directory setup complete"
}

# Setup Python environment
setup_python_env() {
    echo_info "Setting up Python virtual environment..."
    
    cd "$APP_DIR"
    
    # Create virtual environment
    sudo -u "$APP_USER" python3 -m venv venv
    
    # Install Python dependencies
    sudo -u "$APP_USER" ./venv/bin/pip install --upgrade pip
    sudo -u "$APP_USER" ./venv/bin/pip install -r requirements.txt
    
    echo_success "Python environment setup complete"
}

# Setup SSL certificates
setup_ssl() {
    echo_info "Setting up SSL certificates..."
    
    # Stop nginx if running
    systemctl stop nginx 2>/dev/null || true
    
    # Get Let's Encrypt certificate
    certbot certonly --standalone \
        --non-interactive \
        --agree-tos \
        --email "$EMAIL" \
        -d "$DOMAIN"
    
    # Copy certificates to application directory
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$APP_DIR/ssl/money_circle.crt"
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$APP_DIR/ssl/money_circle.key"
    
    # Set permissions
    chown "$APP_USER:$APP_USER" "$APP_DIR/ssl/money_circle".*
    chmod 600 "$APP_DIR/ssl/money_circle.key"
    chmod 644 "$APP_DIR/ssl/money_circle.crt"
    
    echo_success "SSL certificates configured"
}

# Configure nginx
configure_nginx() {
    echo_info "Configuring nginx..."
    
    cat > /etc/nginx/sites-available/money-circle << EOF
# Money Circle Investment Club Platform
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    ssl_certificate $APP_DIR/ssl/money_circle.crt;
    ssl_certificate_key $APP_DIR/ssl/money_circle.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;

    location /static/ {
        alias $APP_DIR/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /health {
        proxy_pass http://127.0.0.1:8086;
        access_log off;
    }

    location / {
        proxy_pass http://127.0.0.1:8086;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # Enable site
    ln -sf /etc/nginx/sites-available/money-circle /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    nginx -t
    
    echo_success "Nginx configured"
}

# Setup systemd service
setup_systemd() {
    echo_info "Setting up systemd service..."
    
    cat > /etc/systemd/system/money-circle.service << EOF
[Unit]
Description=Money Circle Investment Club Platform
After=network.target
Wants=network.target

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=$APP_DIR/.env.production
ExecStart=$APP_DIR/venv/bin/python app.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR

LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable money-circle
    
    echo_success "Systemd service configured"
}

# Setup firewall
setup_firewall() {
    echo_info "Setting up firewall..."
    
    ufw --force enable
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow ssh
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    echo_success "Firewall configured"
}

# Setup database
setup_database() {
    echo_info "Setting up production database..."
    
    cd "$APP_DIR"
    sudo -u "$APP_USER" ./venv/bin/python deployment/database_production_setup.py
    
    echo_success "Database setup complete"
}

# Create production environment file
create_env_file() {
    echo_info "Creating production environment file..."
    
    JWT_SECRET=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    
    cat > "$APP_DIR/.env.production" << EOF
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8086
JWT_SECRET=$JWT_SECRET
DOMAIN=$DOMAIN
SSL_ENABLED=true
DATABASE_PATH=data/money_circle.db
BUS_DATABASE_PATH=data/bus.db
BACKUP_ENABLED=true
MONITORING_ENABLED=true
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
EOF

    chown "$APP_USER:$APP_USER" "$APP_DIR/.env.production"
    chmod 600 "$APP_DIR/.env.production"
    
    echo_success "Environment file created"
}

# Setup monitoring
setup_monitoring() {
    echo_info "Setting up monitoring..."
    
    # Setup log rotation
    cat > /etc/logrotate.d/money-circle << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        systemctl reload money-circle
    endscript
}
EOF

    # Setup cron for backups
    echo "0 */6 * * * $APP_USER cd $APP_DIR && ./venv/bin/python -c \"from backup.backup_manager import BackupManager; import asyncio; asyncio.run(BackupManager({'backup_dir': 'backups'}).create_full_backup())\"" > /etc/cron.d/money-circle-backup
    
    echo_success "Monitoring setup complete"
}

# Start services
start_services() {
    echo_info "Starting services..."
    
    systemctl start money-circle
    systemctl start nginx
    
    # Wait for services to start
    sleep 5
    
    # Check service status
    if systemctl is-active --quiet money-circle; then
        echo_success "Money Circle service started"
    else
        echo_error "Money Circle service failed to start"
        systemctl status money-circle
        exit 1
    fi
    
    if systemctl is-active --quiet nginx; then
        echo_success "Nginx service started"
    else
        echo_error "Nginx service failed to start"
        systemctl status nginx
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    echo_info "Verifying deployment..."
    
    # Test health endpoint
    if curl -f -s "https://$DOMAIN/health" > /dev/null; then
        echo_success "Health check passed"
    else
        echo_error "Health check failed"
        exit 1
    fi
    
    # Test SSL certificate
    if echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates; then
        echo_success "SSL certificate valid"
    else
        echo_warning "SSL certificate verification failed"
    fi
    
    echo_success "Deployment verification complete"
}

# Main deployment function
main() {
    echo_info "Starting Money Circle Production Deployment"
    echo "============================================="
    
    check_root
    get_config
    
    echo_info "Beginning deployment process..."
    
    update_system
    install_packages
    create_app_user
    setup_app_directory
    setup_python_env
    create_env_file
    setup_database
    setup_ssl
    configure_nginx
    setup_systemd
    setup_firewall
    setup_monitoring
    start_services
    verify_deployment
    
    echo_success "Money Circle Production Deployment Complete!"
    echo ""
    echo "🎉 DEPLOYMENT SUCCESSFUL!"
    echo "========================"
    echo "Platform URL: https://$DOMAIN"
    echo "Admin Login: epinnox / securepass123"
    echo ""
    echo "📊 Service Status:"
    echo "  Money Circle: $(systemctl is-active money-circle)"
    echo "  Nginx: $(systemctl is-active nginx)"
    echo ""
    echo "📁 Important Paths:"
    echo "  Application: $APP_DIR"
    echo "  Logs: $APP_DIR/logs/"
    echo "  Backups: $APP_DIR/backups/"
    echo "  SSL Certificates: $APP_DIR/ssl/"
    echo ""
    echo "🔧 Management Commands:"
    echo "  Service Status: systemctl status money-circle"
    echo "  View Logs: journalctl -u money-circle -f"
    echo "  Restart Service: systemctl restart money-circle"
    echo "  Nginx Status: systemctl status nginx"
    echo ""
    echo "🔒 Security:"
    echo "  Firewall: $(ufw status | head -1)"
    echo "  SSL Certificate: Valid until $(openssl x509 -enddate -noout -in $APP_DIR/ssl/money_circle.crt | cut -d= -f2)"
    echo ""
    echo "💾 Backups:"
    echo "  Automated backups run every 6 hours"
    echo "  Manual backup: cd $APP_DIR && sudo -u $APP_USER ./venv/bin/python deployment/database_production_setup.py"
    echo ""
    echo "🎯 Next Steps:"
    echo "  1. Test all platform features"
    echo "  2. Configure external monitoring (optional)"
    echo "  3. Set up remote backup storage (recommended)"
    echo "  4. Update DNS records if needed"
    echo "  5. Notify Epinnox investment club members"
}

# Run main function
main "$@"
