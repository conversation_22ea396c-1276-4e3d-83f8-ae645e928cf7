"""
Core package for the smart-trader system.
"""

from .events import (
    Side, OrderType, Kline, Trade, OrderbookLevel, OrderbookDelta,
    Position, Signal, Order, OrderResponse, Fill, FeatureSnapshot,
    LLMRequest, LLMResponse
)
from .feature_store import FeatureStore, feature_store
from .rule_engine import RuleEngine
from .utils import (
    setup_logging, utc_timestamp, iso_timestamp, generate_signature,
    retry_async, calculate_vwap, calculate_rsi, calculate_order_flow_imbalance,
    timer
)

__all__ = [
    'Side', 'OrderType', 'Kline', 'Trade', 'OrderbookLevel', 'OrderbookDelta',
    'Position', 'Signal', 'Order', 'OrderResponse', 'Fill', 'FeatureSnapshot',
    'LLMRequest', 'LLMResponse', 'FeatureStore', 'feature_store', 'RuleEngine',
    'setup_logging', 'utc_timestamp', 'iso_timestamp', 'generate_signature',
    'retry_async', 'calculate_vwap', 'calculate_rsi', 'calculate_order_flow_imbalance',
    'timer'
]
