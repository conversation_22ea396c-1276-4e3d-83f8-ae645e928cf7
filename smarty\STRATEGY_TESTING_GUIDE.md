# 🎯 Epinnox Strategy Testing Guide

This guide explains how to test all trading strategies to ensure they're operational when run through the dashboard interface.

## 📋 Overview

We've created comprehensive test scripts for each strategy to verify:
- ✅ Database connectivity
- ✅ Strategy startup capability  
- ✅ Process management
- ✅ Data flow monitoring
- ✅ Component functionality
- ✅ Graceful shutdown

## 🧪 Available Test Scripts

### Individual Strategy Tests

1. **`test_strategy_smart_model_integrated.py`**
   - Tests the full orchestrator.py system with LLM integration
   - Monitors LLM consumer, feature store, model predictions
   - Test duration: 60 seconds

2. **`test_strategy_smart_strategy_only.py`**
   - Tests run_smart_strategy_live.py (technical analysis only)
   - Monitors technical indicators and signal generation
   - Test duration: 45 seconds

3. **`test_strategy_data_producer.py`**
   - Tests data producer based strategies (RSI, Bollinger Bands, etc.)
   - Monitors websocket connections and market data feeds
   - Test duration: 30 seconds
   - Usage: `python test_strategy_data_producer.py "Strategy Name"`

4. **`test_strategy_order_flow.py`**
   - Tests live_dataframe_strategy_runner.py
   - Monitors order flow analysis and dataframe operations
   - Test duration: 45 seconds

### Comprehensive Test Runners

5. **`test_all_strategies.py`**
   - Runs tests for all strategies in priority order
   - Generates comprehensive reports
   - Saves detailed JSON results
   - Usage: `python test_all_strategies.py [--strategies "Strategy1" "Strategy2"]`

6. **`run_strategy_tests.py`**
   - Simplified test runner for individual or all strategies
   - Usage: `python run_strategy_tests.py <strategy_name|all>`

### Quick Status Checks

7. **`check_strategy_status.py`**
   - Quick status check for dashboard integration
   - Returns JSON status for a strategy
   - Usage: `python check_strategy_status.py "Strategy Name"`

## 🎯 Strategy Coverage

| Strategy | Test Script | Command | Status |
|----------|-------------|---------|--------|
| Smart Model Integrated | test_strategy_smart_model_integrated.py | `python orchestrator.py` | ✅ |
| Smart Strategy Only | test_strategy_smart_strategy_only.py | `python run_smart_strategy_live.py` | ✅ |
| RSI Strategy | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| Bollinger Bands | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| Multi-Signal | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| Ensemble Model | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| SMA Crossover | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| VWAP Strategy | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| Scalper Strategy | test_strategy_data_producer.py | `python feeds/htx_data_producer.py` | ✅ |
| Order Flow | test_strategy_order_flow.py | `python live_dataframe_strategy_runner.py` | ✅ |

## 🚀 Quick Start

### Test All Strategies
```bash
# Run comprehensive test suite
python test_all_strategies.py

# Run simplified test suite
python run_strategy_tests.py all
```

### Test Individual Strategy
```bash
# Test Smart Model Integrated
python test_strategy_smart_model_integrated.py

# Test Smart Strategy Only
python test_strategy_smart_strategy_only.py

# Test RSI Strategy
python test_strategy_data_producer.py "RSI Strategy"

# Test Order Flow
python test_strategy_order_flow.py
```

### Quick Status Check
```bash
# Check if strategy is ready
python check_strategy_status.py "Smart Model Integrated"
```

## 📊 Test Results

### JSON Output Files
- `test_results_<strategy_name>.json` - Detailed test results
- `strategy_test_results_<timestamp>.json` - Comprehensive summary
- `quick_test_<strategy_name>.json` - Quick status results

### Test Metrics
Each test provides:
- **Database Connection**: ✅/❌
- **Strategy Startup**: ✅/❌  
- **Process Running**: ✅/❌
- **Data Flow**: Message counts and timestamps
- **Component Status**: Individual component health
- **Strategy Shutdown**: ✅/❌
- **Overall Success**: ✅/❌

## 🔧 Dashboard Integration

### From Dashboard Interface
The dashboard can call these tests to verify strategy readiness:

```python
# Quick status check
result = subprocess.run([
    "python", "check_strategy_status.py", strategy_name
], capture_output=True, text=True)

status = json.loads(result.stdout)
```

### Status Codes
- `0` - Strategy ready/running
- `1` - Warning (no data flow)
- `2` - Error (database/startup issues)

## 🎯 Best Practices

### Before Starting Strategy
1. Run quick status check: `python check_strategy_status.py "Strategy Name"`
2. Verify database connectivity
3. Check for existing processes
4. Ensure data producer is running

### After Starting Strategy
1. Monitor process status
2. Check data flow in database
3. Verify signal generation
4. Monitor component health

### Troubleshooting
1. Check database connection: `data/bus.db`
2. Verify process is running: `ps aux | grep python`
3. Check logs for errors
4. Restart data producer if needed

## 📈 Performance Monitoring

### Test Duration Guidelines
- **Quick Status Check**: < 5 seconds
- **Data Producer Tests**: 30 seconds
- **Strategy Tests**: 45-60 seconds
- **Full Test Suite**: 5-10 minutes

### Success Criteria
- ✅ Database accessible
- ✅ Strategy starts successfully
- ✅ Process remains running
- ✅ Data flows to database
- ✅ Components function properly
- ✅ Strategy stops gracefully

## 🎉 Next Steps

1. **Run Full Test Suite**: `python test_all_strategies.py`
2. **Fix Any Failed Strategies**: Check logs and error messages
3. **Integrate with Dashboard**: Use status checks in UI
4. **Monitor Live Performance**: Track strategy metrics
5. **Regular Testing**: Run tests before important trading sessions

## 📞 Support

If strategies fail tests:
1. Check database connectivity
2. Verify all dependencies installed
3. Ensure data producer is running
4. Check for port conflicts
5. Review error logs and fix issues

The testing system ensures all strategies are operational and ready for live trading through the dashboard interface! 🚀
