#!/usr/bin/env python
"""
Migration script to move data from the original SQLiteBus to the optimized SQLiteBus.

This script:
1. Reads all messages from the original SQLiteBus database
2. Writes them to the optimized SQLiteBus database
3. Updates the configuration to use the optimized SQLiteBus

Usage:
    python migrate_to_optimized_bus.py [--config CONFIG_PATH]
"""

import argparse
import asyncio
import json
import logging
import os
import sqlite3
import sys
import time
import yaml
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from optimized_databus import OptimizedSQLiteBus
from pipeline.databus import SQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Configuration dictionary
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}


def save_config(config: Dict[str, Any], config_path: str) -> bool:
    """
    Save configuration to YAML file.

    Args:
        config: Configuration dictionary
        config_path: Path to the configuration file

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create backup
        backup_path = f"{config_path}.bak"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                with open(backup_path, 'w') as f_bak:
                    f_bak.write(f.read())
            logger.info(f"Created backup of configuration at {backup_path}")

        # Save new config
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)

        return True
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return False


def read_messages_from_sqlite(db_path: str) -> List[Tuple[str, float, Dict[str, Any]]]:
    """
    Read all messages from a SQLite database.

    Args:
        db_path: Path to the SQLite database

    Returns:
        List of (stream, timestamp, payload) tuples
    """
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return []

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get all messages
        cursor.execute("SELECT stream, ts, payload FROM messages ORDER BY id")

        messages = []
        for stream, ts, payload_str in cursor.fetchall():
            try:
                payload = json.loads(payload_str)
                messages.append((stream, ts, payload))
            except json.JSONDecodeError:
                logger.warning(f"Failed to decode payload for message in stream {stream} at {ts}")

        conn.close()

        return messages

    except Exception as e:
        logger.error(f"Error reading messages from SQLite: {e}")
        return []


def migrate_data(source_path: str, target_path: str, batch_size: int = 100,
                compression: bool = False, compression_level: int = 6,
                compression_threshold: int = 1024) -> int:
    """
    Migrate data from source database to target database.

    Args:
        source_path: Path to the source SQLite database
        target_path: Path to the target SQLite database
        batch_size: Number of messages to migrate in each batch
        compression: Whether to compress message payloads
        compression_level: Compression level (1-9, where 9 is highest)
        compression_threshold: Minimum size in bytes for compression

    Returns:
        Number of messages migrated
    """
    # Read messages from source
    logger.info(f"Reading messages from {source_path}")
    messages = read_messages_from_sqlite(source_path)
    logger.info(f"Read {len(messages)} messages from source database")

    if not messages:
        return 0

    # Create optimized bus
    optimized_bus = OptimizedSQLiteBus(
        path=target_path,
        poll_interval=0.1,
        batch_size=batch_size,
        batch_timeout=0.5,
        compression=compression,
        compression_level=compression_level,
        compression_threshold=compression_threshold
    )

    # Write messages to target
    logger.info(f"Writing messages to {target_path}")
    count = 0

    try:
        for stream, ts, payload in messages:
            optimized_bus.publish(stream, ts, payload)
            count += 1

            # Log progress
            if count % 1000 == 0:
                logger.info(f"Migrated {count}/{len(messages)} messages")

        # Wait for all messages to be written
        time.sleep(2.0)

        # Close the bus
        optimized_bus.close()

        logger.info(f"Successfully migrated {count} messages to {target_path}")
        return count

    except Exception as e:
        logger.error(f"Error migrating data: {e}")
        optimized_bus.close()
        return count


def update_config(config: Dict[str, Any], compression: bool = False,
                  compression_level: int = 6, compression_threshold: int = 1024) -> Dict[str, Any]:
    """
    Update configuration to use the optimized SQLiteBus.

    Args:
        config: Configuration dictionary
        compression: Whether to enable compression
        compression_level: Compression level (1-9)
        compression_threshold: Minimum size for compression

    Returns:
        Updated configuration dictionary
    """
    # Make a copy of the config
    new_config = config.copy()

    # Get current SQLite settings
    sqlite_config = new_config.get("pipeline", {}).get("sqlite", {})

    # Set up optimized SQLite settings
    if "pipeline" not in new_config:
        new_config["pipeline"] = {}

    # Change bus type to optimized_sqlite
    new_config["pipeline"]["bus"] = "optimized_sqlite"

    # Set up optimized SQLite config if not present
    if "optimized_sqlite" not in new_config["pipeline"]:
        new_config["pipeline"]["optimized_sqlite"] = {
            "path": sqlite_config.get("path", "data/optimized_bus.db").replace("bus.db", "optimized_bus.db"),
            "poll_interval": 0.1,
            "batch_size": 50,
            "batch_timeout": 0.5,
            "cache_size": 10000,
            "mmap_size": 30000000,
            "busy_timeout": 5000,
            "synchronous": "NORMAL",
            "compression": compression,
            "compression_level": compression_level,
            "compression_threshold": compression_threshold
        }
    else:
        # Update compression settings in existing config
        new_config["pipeline"]["optimized_sqlite"]["compression"] = compression
        new_config["pipeline"]["optimized_sqlite"]["compression_level"] = compression_level
        new_config["pipeline"]["optimized_sqlite"]["compression_threshold"] = compression_threshold

    return new_config


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Migrate from SQLiteBus to OptimizedSQLiteBus")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--source", help="Path to source database (overrides config)")
    parser.add_argument("--target", help="Path to target database (overrides config)")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for migration")
    parser.add_argument("--update-config", action="store_true", help="Update configuration file")
    parser.add_argument("--compression", action="store_true", help="Enable compression for migrated data")
    parser.add_argument("--compression-level", type=int, default=6, help="Compression level (1-9)")
    parser.add_argument("--compression-threshold", type=int, default=1024, help="Minimum size for compression")
    parser.add_argument("--stats", action="store_true", help="Show database statistics after migration")

    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Get database paths
    source_path = args.source
    if not source_path:
        source_path = config.get("pipeline", {}).get("sqlite", {}).get("path", "data/bus.db")

    target_path = args.target
    if not target_path:
        target_path = source_path.replace("bus.db", "optimized_bus.db")

    # Migrate data
    migrated = migrate_data(
        source_path=source_path,
        target_path=target_path,
        batch_size=args.batch_size,
        compression=args.compression,
        compression_level=args.compression_level,
        compression_threshold=args.compression_threshold
    )

    # Update configuration if requested
    if args.update_config and migrated > 0:
        logger.info("Updating configuration to use OptimizedSQLiteBus")
        new_config = update_config(
            config=config,
            compression=args.compression,
            compression_level=args.compression_level,
            compression_threshold=args.compression_threshold
        )
        if save_config(new_config, args.config):
            logger.info(f"Configuration updated: {args.config}")
        else:
            logger.error("Failed to update configuration")

    # Show statistics if requested
    if args.stats and migrated > 0:
        logger.info("Getting database statistics...")
        bus = OptimizedSQLiteBus(
            path=target_path,
            compression=args.compression,
            compression_level=args.compression_level,
            compression_threshold=args.compression_threshold
        )
        stats = bus.get_database_stats()
        bus.close()

        print("\n=== Database Statistics ===")
        print(f"Total messages: {stats['total_messages']}")
        print(f"Database size: {stats['database_size_bytes']} bytes ({stats['database_size_mb']:.2f} MB)")

        if 'compression_stats' in stats and stats['compression_stats']:
            compressed = stats['compression_stats']['compressed_messages']
            ratio = stats['compression_stats']['compression_ratio'] * 100
            print(f"Compressed messages: {compressed} ({ratio:.2f}%)")

        print("\nMessage counts by stream:")
        for stream, count in sorted(stats['stream_counts'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {stream}: {count}")

        print("\nFor more detailed statistics, run: python monitor_bus.py --db-path {target_path}")

    logger.info("Migration complete")


if __name__ == "__main__":
    main()
