#!/usr/bin/env python3
"""
Comprehensive test suite for the Smart-Trader system.
Tests all components, APIs, and real-time functionality.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
import sqlite3
import os

# Fix Windows event loop issue
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

class SmartTraderTester:
    def __init__(self):
        self.control_center_url = "http://localhost:8082"
        self.dashboard_url = "http://localhost:8080"
        self.results = {}

    async def run_all_tests(self):
        """Run comprehensive test suite."""
        print("🧪 SMART-TRADER SYSTEM COMPREHENSIVE TEST SUITE")
        print("=" * 60)

        tests = [
            ("Control Center API", self.test_control_center_api),
            ("System Status", self.test_system_status),
            ("Database Integration", self.test_database),
            ("Market Data Flow", self.test_market_data),
            ("Model Outputs", self.test_model_outputs),
            ("LLM Integration", self.test_llm_integration),
            ("Dashboard Connectivity", self.test_dashboard),
            ("Real-time Updates", self.test_realtime_updates),
            ("Account Integration", self.test_account_integration),
            ("Position Management", self.test_position_management)
        ]

        for test_name, test_func in tests:
            print(f"\n🔍 Testing: {test_name}")
            print("-" * 40)
            try:
                result = await test_func()
                self.results[test_name] = {"status": "PASS", "details": result}
                print(f"✅ {test_name}: PASSED")
            except Exception as e:
                self.results[test_name] = {"status": "FAIL", "error": str(e)}
                print(f"❌ {test_name}: FAILED - {e}")

        await self.print_summary()

    async def test_control_center_api(self):
        """Test Control Center API endpoints."""
        async with aiohttp.ClientSession() as session:
            # Test status endpoint
            async with session.get(f"{self.control_center_url}/api/status") as resp:
                if resp.status != 200:
                    raise Exception(f"Status endpoint failed: {resp.status}")
                data = await resp.json()

                return {
                    "status_code": resp.status,
                    "smart_trader_available": data.get("smart_trader_available"),
                    "orchestrator_running": data["system_status"]["orchestrator"]["running"],
                    "testnet_running": data["system_status"]["testnet"]["running"]
                }

    async def test_system_status(self):
        """Test system component status."""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.control_center_url}/api/status") as resp:
                data = await resp.json()

                status = data["system_status"]
                components = ["orchestrator", "testnet"]
                running_components = []

                for component in components:
                    if status[component]["running"]:
                        running_components.append(component)

                return {
                    "total_components": len(components),
                    "running_components": len(running_components),
                    "active_components": running_components,
                    "smart_trader_available": data.get("smart_trader_available", False)
                }

    async def test_database(self):
        """Test SQLite database integration."""
        db_path = "data/bus.db"
        if not os.path.exists(db_path):
            raise Exception("Database file not found")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        # Check for recent messages (ts is Unix timestamp)
        import time
        one_hour_ago = time.time() - 3600
        cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (one_hour_ago,))
        recent_messages = cursor.fetchone()[0]

        conn.close()

        return {
            "database_exists": True,
            "tables_found": len(tables),
            "table_names": tables,
            "recent_messages": recent_messages
        }

    async def test_market_data(self):
        """Test market data flow."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check for market data messages
        ten_minutes_ago = time.time() - 600
        cursor.execute("""
            SELECT stream, COUNT(*) as count
            FROM messages
            WHERE ts > ?
            AND stream LIKE 'market.%'
            GROUP BY stream
        """, (ten_minutes_ago,))

        market_data = cursor.fetchall()

        # Check for funding rate data
        one_hour_ago = time.time() - 3600
        cursor.execute("""
            SELECT COUNT(*)
            FROM messages
            WHERE stream = 'features.funding'
            AND ts > ?
        """, (one_hour_ago,))

        funding_messages = cursor.fetchone()[0]

        conn.close()

        return {
            "market_channels": len(market_data),
            "market_data_flows": dict(market_data),
            "funding_rate_updates": funding_messages
        }

    async def test_model_outputs(self):
        """Test model output generation."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check for model feature outputs
        feature_channels = [
            'features.vwap',
            'features.volatility',
            'features.funding',
            'features.sentiment'
        ]

        thirty_minutes_ago = time.time() - 1800
        model_outputs = {}
        for channel in feature_channels:
            cursor.execute("""
                SELECT COUNT(*)
                FROM messages
                WHERE stream = ?
                AND ts > ?
            """, (channel, thirty_minutes_ago))

            count = cursor.fetchone()[0]
            model_outputs[channel] = count

        conn.close()

        return {
            "active_models": len([k for k, v in model_outputs.items() if v > 0]),
            "model_outputs": model_outputs
        }

    async def test_llm_integration(self):
        """Test LLM integration."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check for LLM signals
        thirty_minutes_ago = time.time() - 1800
        cursor.execute("""
            SELECT COUNT(*)
            FROM messages
            WHERE stream = 'signals.fused'
            AND ts > ?
        """, (thirty_minutes_ago,))

        llm_signals = cursor.fetchone()[0]

        # Get latest LLM signal
        cursor.execute("""
            SELECT payload, ts
            FROM messages
            WHERE stream = 'signals.fused'
            ORDER BY ts DESC
            LIMIT 1
        """)

        latest_signal = cursor.fetchone()

        conn.close()

        return {
            "llm_signals_generated": llm_signals,
            "latest_signal_time": latest_signal[1] if latest_signal else None,
            "has_recent_signals": llm_signals > 0
        }

    async def test_dashboard(self):
        """Test dashboard connectivity."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.dashboard_url}/") as resp:
                    dashboard_status = resp.status

                # Test WebSocket endpoint
                async with session.get(f"{self.dashboard_url}/ws") as resp:
                    ws_status = resp.status

                return {
                    "dashboard_accessible": dashboard_status == 200,
                    "websocket_endpoint": ws_status in [200, 101],  # 101 = switching protocols
                    "dashboard_url": self.dashboard_url
                }
        except Exception as e:
            return {
                "dashboard_accessible": False,
                "error": str(e)
            }

    async def test_realtime_updates(self):
        """Test real-time data updates."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get message counts before waiting
        five_minutes_ago = time.time() - 300
        cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (five_minutes_ago,))
        initial_count = cursor.fetchone()[0]

        # Wait a bit for new messages
        await asyncio.sleep(3)

        five_minutes_ago = time.time() - 300
        cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (five_minutes_ago,))
        final_count = cursor.fetchone()[0]

        # Check latest message timestamp
        cursor.execute("SELECT MAX(ts) FROM messages")
        latest_timestamp = cursor.fetchone()[0]

        conn.close()

        return {
            "initial_messages": initial_count,
            "final_messages": final_count,
            "new_messages": final_count - initial_count,
            "latest_update": latest_timestamp,
            "system_active": final_count > initial_count
        }

    async def test_account_integration(self):
        """Test account integration."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check for account state messages
        cursor.execute("""
            SELECT payload, ts
            FROM messages
            WHERE stream = 'account.state'
            ORDER BY ts DESC
            LIMIT 1
        """)

        account_data = cursor.fetchone()

        conn.close()

        if account_data:
            try:
                account_info = json.loads(account_data[0])
                return {
                    "account_connected": True,
                    "balance": account_info.get("total", account_info.get("balance", "Unknown")),
                    "available": account_info.get("available", "Unknown"),
                    "last_update": datetime.fromtimestamp(account_data[1]).isoformat()
                }
            except Exception as e:
                return {"account_connected": False, "error": f"Invalid account data: {e}"}
        else:
            return {"account_connected": False, "error": "No account data found"}

    async def test_position_management(self):
        """Test position management system."""
        db_path = "data/bus.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check for position monitoring
        thirty_minutes_ago = time.time() - 1800
        cursor.execute("""
            SELECT COUNT(*)
            FROM messages
            WHERE stream = 'positions.state'
            AND ts > ?
        """, (thirty_minutes_ago,))

        position_updates = cursor.fetchone()[0]

        conn.close()

        return {
            "position_monitoring_active": position_updates >= 0,
            "position_updates": position_updates
        }

    async def print_summary(self):
        """Print test results summary."""
        print("\n" + "=" * 60)
        print("🎯 TEST RESULTS SUMMARY")
        print("=" * 60)

        passed = sum(1 for r in self.results.values() if r["status"] == "PASS")
        total = len(self.results)

        print(f"📊 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        print()

        for test_name, result in self.results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {test_name}: {result['status']}")

            if result["status"] == "PASS" and "details" in result:
                details = result["details"]
                for key, value in details.items():
                    print(f"   • {key}: {value}")
            elif result["status"] == "FAIL":
                print(f"   • Error: {result['error']}")
            print()

        if passed == total:
            print("🎉 ALL TESTS PASSED! Your Smart-Trader system is fully operational!")
        else:
            print(f"⚠️  {total - passed} tests failed. Check the details above.")

async def main():
    """Run the test suite."""
    tester = SmartTraderTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
