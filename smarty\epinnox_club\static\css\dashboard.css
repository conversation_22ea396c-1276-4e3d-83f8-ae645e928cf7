/* Money Circle Dashboard Styles - Component Specific */

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Dashboard Grid Layout - Responsive */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-gap);
    margin-bottom: 30px;
}

/* Responsive grid layout */
@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

/* Portfolio Overview Section */
.portfolio-overview {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.portfolio-overview h2 {
    color: var(--warning-400, #FFD700);
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.portfolio-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.portfolio-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    min-height: var(--touch-target-min);
    touch-action: manipulation;
}

/* Touch-friendly hover alternatives */
@media (hover: hover) {
    .portfolio-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 215, 0, 0.1);
        border-color: rgba(255, 215, 0, 0.3);
    }
}

@media (hover: none) {
    .portfolio-card:focus,
    .portfolio-card:active {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 215, 0, 0.3);
        transform: scale(0.98);
    }
}

.portfolio-card h3 {
    color: #94a3b8;
    font-size: 0.9em;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.portfolio-card .value {
    color: #ffffff;
    font-size: 1.8em;
    font-weight: 700;
    margin-bottom: 8px;
}

.portfolio-card .change {
    font-size: 0.9em;
    font-weight: 600;
}

.portfolio-card .change.positive {
    color: #4CAF50;
}

.portfolio-card .change.negative {
    color: #f44336;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 30px;
}

.header-left h1 {
    color: #FFD700;
    font-size: 2em;
    margin-bottom: 5px;
}

.user-info {
    color: #94a3b8;
    font-size: 0.9em;
}

.header-right {
    display: flex;
    gap: 20px;
}

.nav-link {
    color: #e2e8f0;
    text-decoration: none;
    padding: 12px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: var(--touch-target-min);
    display: inline-flex;
    align-items: center;
    touch-action: manipulation;
}

/* Touch-friendly navigation */
@media (hover: hover) {
    .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #FFD700;
    }
}

@media (hover: none) {
    .nav-link:focus,
    .nav-link:active {
        background: rgba(255, 255, 255, 0.1);
        color: #FFD700;
        transform: scale(0.98);
    }
}

/* Portfolio Stats (Alternative Layout) */

.portfolio-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stat-card h3 {
    color: #94a3b8;
    font-size: 0.9em;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 2em;
    font-weight: 700;
    color: #ffffff;
}

.stat-value.positive {
    color: #4CAF50;
}

.stat-value.negative {
    color: #f44336;
}

/* Exchange Connections Section */
.exchange-connections {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.exchange-connections h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.exchange-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

/* Trading Interface Section */
.trading-interface {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.trading-interface h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.trading-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
    min-height: var(--touch-target-min);
    touch-action: manipulation;
}

.tab-btn.active {
    color: #FFD700;
    border-bottom-color: #FFD700;
}

/* Touch-friendly tab buttons */
@media (hover: hover) {
    .tab-btn:hover {
        color: #ffffff;
    }
}

@media (hover: none) {
    .tab-btn:focus,
    .tab-btn:active {
        color: #ffffff;
        background: rgba(255, 255, 255, 0.05);
    }
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.order-form {
    max-width: 400px;
}

.order-type-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.order-type-btn {
    flex: 1;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.order-type-btn.active {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000000;
    border-color: #FFD700;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    color: #94a3b8;
    margin-bottom: 5px;
    font-size: 0.9em;
    font-weight: 500;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.place-order-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    min-height: var(--touch-target-comfortable);
    touch-action: manipulation;
    font-size: 16px;
}

/* Touch-friendly order button */
@media (hover: hover) {
    .place-order-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }
}

@media (hover: none) {
    .place-order-btn:focus,
    .place-order-btn:active {
        transform: scale(0.98);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }
}

/* Market Data Section */
.market-data {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.market-data h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.market-widgets {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-gap);
}

/* Responsive market widgets */
@media (min-width: 768px) {
    .market-widgets {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
}

@media (min-width: 1024px) {
    .market-widgets {
        grid-template-columns: 2fr 1fr 1fr;
    }
}

.market-widget {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.market-widget h3 {
    color: #94a3b8;
    margin-bottom: 15px;
    font-size: 1em;
    font-weight: 600;
}

.market-widget canvas {
    width: 100% !important;
    height: 200px !important;
}

.orderbook {
    max-height: 200px;
    overflow-y: auto;
}

.trades-list {
    max-height: 200px;
    overflow-y: auto;
}

/* Performance Analytics Section */
.performance-analytics {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.performance-analytics h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
}

.analytics-card h4 {
    color: #94a3b8;
    margin-bottom: 10px;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.analytics-card .metric {
    color: #ffffff;
    font-size: 1.6em;
    font-weight: 700;
}

.exchange-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.exchange-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
}

.exchange-card h4 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1.1em;
    font-weight: 600;
}

.exchange-card.add-exchange {
    border: 2px dashed rgba(255, 215, 0, 0.3);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 150px;
}

.exchange-card.add-exchange:hover {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.05);
}

.exchange-card.add-exchange p {
    color: #94a3b8;
    margin: 10px 0;
    font-size: 0.9em;
}

.exchange-card.add-exchange button {
    padding: 10px 20px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000000;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.exchange-card.add-exchange button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.exchange-card.connected {
    border-color: rgba(76, 175, 80, 0.3);
}

.exchange-card.disconnected {
    border-color: rgba(244, 67, 54, 0.3);
}

.exchange-card h4 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.connection-status {
    margin-bottom: 15px;
    font-size: 0.9em;
}

.balance-summary {
    margin-bottom: 15px;
}

.balance-item {
    color: #94a3b8;
    font-size: 0.85em;
    margin-bottom: 5px;
}

.exchange-actions {
    display: flex;
    gap: 10px;
}

.exchange-actions button {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.2);
    color: #e2e8f0;
    cursor: pointer;
    font-size: 0.8em;
    transition: all 0.3s ease;
}

.exchange-actions button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #FFD700;
}

.add-exchange-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-exchange-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

/* Tables */
.positions-table, .trades-table {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    color: #FFD700;
    text-align: left;
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.5px;
}

table td {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: #e2e8f0;
}

table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.buy {
    color: #4CAF50;
}

.sell {
    color: #f44336;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: rgba(0, 0, 0, 0.9);
    margin: 10% auto;
    padding: 30px;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-content h3 {
    color: #FFD700;
    margin-bottom: 20px;
    text-align: center;
}

.modal-content select,
.modal-content input {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
}

.modal-content select:focus,
.modal-content input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.modal-buttons button[type="submit"] {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
}

.modal-buttons button[type="button"] {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-buttons button:hover {
    transform: translateY(-1px);
}

/* Trading Interface */
.trading-interface {
    margin-bottom: 40px;
}

.trading-interface h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.trading-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.trading-form {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.trading-form h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.trading-form select,
.trading-form input {
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 14px;
}

.trading-form select:focus,
.trading-form input:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.trade-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.trade-btn.buy {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.trade-btn.sell {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.trade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.market-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.market-info h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.price-tickers {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ticker {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ticker .symbol {
    font-weight: 600;
    color: #e2e8f0;
}

.ticker .price {
    font-weight: 700;
    color: #ffffff;
    text-align: center;
}

.ticker .change {
    text-align: right;
    font-weight: 600;
}

.ticker .change.positive {
    color: #4CAF50;
}

.ticker .change.negative {
    color: #f44336;
}

/* Responsive design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .market-widgets {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
    }

    .portfolio-cards {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .exchange-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr 1fr;
    }

    .market-widgets {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .trading-tabs {
        flex-wrap: wrap;
        gap: 5px;
    }

    .tab-btn {
        padding: 8px 15px;
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 10px;
    }

    .portfolio-cards {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .order-type-selector {
        flex-direction: column;
        gap: 8px;
    }

    .trading-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        width: 100%;
        text-align: center;
    }
}

/* Real-time Market Data Enhancements */
.market-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-label, .quality-label, .health-label {
    color: #cccccc;
    font-size: 0.9rem;
    font-weight: 500;
}

.source-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 1px solid;
}

.source-indicator.active {
    background: rgba(0, 255, 136, 0.2);
    border-color: #00ff88;
    color: #00ff88;
    animation: pulse 2s infinite;
}

.source-indicator.inactive {
    background: rgba(255, 68, 68, 0.2);
    border-color: #ff4444;
    color: #ff4444;
}

.data-quality {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quality-score {
    color: #FFD700;
    font-weight: bold;
}

/* Enhanced Price Tickers */
.price-tickers.enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.ticker-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
}

.ticker-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.2);
}

.ticker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.ticker-header .symbol {
    font-size: 1.1rem;
    font-weight: bold;
    color: #ffffff;
}

.data-source {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
}

.data-source.quality-excellent {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
}

.data-source.quality-good {
    background: rgba(0, 191, 255, 0.2);
    color: #00bfff;
}

.data-source.quality-fair {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.data-source.quality-poor {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
}

.price-section {
    margin: 10px 0;
}

.price-section .price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #ffffff;
    display: block;
    margin-bottom: 5px;
}

.bid-ask {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #cccccc;
}

.bid {
    color: #00ff88;
}

.ask {
    color: #ff4444;
}

.change-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.change {
    font-size: 1rem;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.change.positive {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
}

.change.negative {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
}

.volume {
    font-size: 0.9rem;
    color: #cccccc;
}

.data-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #999999;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.latency {
    color: #00bfff;
}

.last-update {
    color: #ffa500;
}

/* Market Health Indicator */
.market-health {
    margin-top: 15px;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.health-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.health-status.healthy {
    color: #00ff88;
    font-weight: bold;
}

.health-status.degraded {
    color: #ffa500;
    font-weight: bold;
}

.health-status.unhealthy {
    color: #ff4444;
    font-weight: bold;
}

.health-score {
    color: #FFD700;
    font-weight: bold;
}

/* Price Change Animations */
.price-up {
    animation: priceUp 1s ease-out;
}

.price-down {
    animation: priceDown 1s ease-out;
}

@keyframes priceUp {
    0% { background-color: rgba(0, 255, 136, 0.3); }
    100% { background-color: transparent; }
}

@keyframes priceDown {
    0% { background-color: rgba(255, 68, 68, 0.3); }
    100% { background-color: transparent; }
}

/* Status Indicator Pulse Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.active {
    background: #00ff88;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

.status-indicator.inactive {
    background: #ff4444;
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.5);
}

/* ===== ENHANCED TRADING CONTROLS ===== */

/* Quick Trade Section */
.quick-trade-section {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.quick-trade-section h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.quick-trade-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quick-trade-group {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.quick-trade-group:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 215, 0, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.symbol-header {
    font-weight: 600;
    color: #FFD700;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.price-display {
    color: #e2e8f0;
    margin-bottom: 12px;
    font-size: 1.2em;
    font-weight: 500;
}

.button-group {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.quick-buy-btn, .quick-sell-btn {
    flex: 1;
    padding: 10px 12px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px; /* Touch target */
    font-size: 14px;
}

.quick-buy-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.quick-buy-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.quick-sell-btn {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.quick-sell-btn:hover {
    background: linear-gradient(135deg, #da190b, #c62828);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

/* Risk Management Controls */
.risk-controls {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 20px;
}

.risk-controls h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.2em;
    font-weight: 600;
}

.risk-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.risk-metrics .metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.risk-metrics .metric label {
    color: #94a3b8;
    font-size: 0.9em;
}

.risk-value {
    color: #FFD700;
    font-weight: 600;
}

.pnl-value.positive {
    color: #4CAF50;
}

.pnl-value.negative {
    color: #f44336;
}

.drawdown-value {
    color: #f44336;
}

.risk-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.risk-actions button {
    flex: 1;
    min-width: 140px;
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
}

.risk-actions button:first-child {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.risk-actions button:nth-child(2) {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
}

.risk-actions button:last-child {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.risk-actions button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Position Management Controls */
.position-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.position-actions button {
    flex: 1;
    min-width: 80px;
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 36px;
}

.btn-modify {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.btn-close {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.btn-hedge {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
}

.position-actions button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Modal Styles for Trading Controls */
.trade-confirmation-modal,
.confirmation-modal,
.position-modification-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.trade-confirmation-modal .modal-content,
.confirmation-modal .modal-content,
.position-modification-modal .modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    border-radius: 16px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.trade-confirmation-modal h3,
.confirmation-modal h3,
.position-modification-modal h3 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 1.3em;
    font-weight: 600;
}

.trade-details {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.trade-details p {
    color: #e2e8f0;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-confirm, .btn-cancel {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    min-width: 100px;
}

.btn-confirm {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.btn-cancel {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.btn-confirm:hover, .btn-cancel:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.notification.error {
    background: linear-gradient(135deg, #f44336, #da190b);
}

.notification.info {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
