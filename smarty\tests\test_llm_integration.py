"""
Integration test for the LLM-based trading flow.
"""

import unittest
import asyncio
import time
import os
import sys
from datetime import datetime
from unittest.mock import MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import components
import yaml

# Create mock classes instead of importing
class Signal:
    def __init__(self, symbol, timestamp, action, score, confidence, source, rationale, metadata=None):
        self.symbol = symbol
        self.timestamp = timestamp
        self.action = action
        self.score = score
        self.confidence = confidence
        self.source = source
        self.rationale = rationale
        self.metadata = metadata or {}

class Side:
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class OptimizedSQLiteBus:
    def __init__(self, path, poll_interval, batch_size, batch_timeout):
        self.path = path
        self.poll_interval = poll_interval
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.messages = {}

    def publish(self, stream, ts, payload):
        if stream not in self.messages:
            self.messages[stream] = []
        self.messages[stream].append((stream, ts, payload))

    def get_messages(self, stream, limit=10):
        return self.messages.get(stream, [])[:limit]

    def close(self):
        pass


class MockHTXClient:
    """Mock HTX client for testing."""

    def __init__(self):
        self.orders = []
        self.positions = []
        self.account_info = {
            "total_equity": 100.0,
            "available_balance": 90.0
        }
        self.authenticated = True
        self.CHANNEL_POSITION = "position.{symbol}"

    async def get_account_info(self):
        """Get account information."""
        return self.account_info

    async def get_positions(self):
        """Get positions."""
        return self.positions

    async def get_open_orders(self):
        """Get open orders."""
        return self.orders

    async def subscribe(self, channel):
        """Subscribe to a channel."""
        pass

    async def close(self):
        """Close the client."""
        pass

    async def get_next_market_message(self, timeout=1.0):
        """Get next market message."""
        return None

    async def get_next_private_message(self, timeout=1.0):
        """Get next private message."""
        return None


class MockExecutor:
    """Mock executor for testing."""

    def __init__(self):
        self.executed_signals = []

    async def execute(self, signal):
        """Execute a signal."""
        self.executed_signals.append(signal)

        # Return a mock response
        return MagicMock(
            order_id="test-order-123",
            quantity=0.01,
            price=50000.0
        )





class AsyncTestCase(unittest.TestCase):
    """Base class for async tests."""

    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        super().setUp()

    def tearDown(self):
        self.loop.close()
        super().tearDown()

    def run_async(self, coro):
        return self.loop.run_until_complete(coro)


class TestLLMIntegration(AsyncTestCase):
    """Integration test for the LLM-based trading flow."""

    def setUp(self):
        """Set up test fixtures."""
        # Call parent setUp to initialize the loop
        super().setUp()

        # Create a temporary database
        self.db_path = "test_integration.db"

        # Create a message bus
        self.bus = OptimizedSQLiteBus(
            path=self.db_path,
            poll_interval=0.01,
            batch_size=10,
            batch_timeout=0.1
        )

        # Create a mock config
        self.config = {
            "symbols": ["BTC-USDT"],
            "trading_enabled": True,
            "simulation_mode": True,
            "max_positions": 1,

            # LLM settings
            "llm_model_path": "models/test.gguf",
            "llm_prompt_path": "llm/prompts/test_prompt.yaml",
            "llm_threads": 1,
            "llm_gpu_layers": 0,
            "dummy_llm": True,

            # LLM Consumer settings
            "model_path": "models/test.gguf",
            "prompt_path": "llm/prompts/test_prompt.yaml",
            "n_ctx": 1024,
            "max_tokens": 64,
            "temperature": 0.0,
            "call_interval_s": 1,  # Short interval for testing
            "dummy_mode": True,

            # Health check settings
            "health_check_interval": 1,
            "llm_error_threshold": 0.05,
            "llm_latency_threshold": 5.0,
            "llm_confidence_threshold": 0.6,

            # Adaptive throttling settings
            "adaptive_throttle": True,
            "min_throttle_interval": 0.5,
            "max_throttle_interval": 5.0,
            "throttle_volatility_factor": 2.0
        }

        # Create a mock HTX client
        self.htx_client = MockHTXClient()

        # Create a mock executor
        self.executor = MockExecutor()

        # Create a temporary prompt file
        os.makedirs("llm/prompts", exist_ok=True)
        with open("llm/prompts/test_prompt.yaml", "w") as f:
            yaml.dump({"template": "Symbol: {symbol}\nAction:"}, f)

    def tearDown(self):
        """Tear down test fixtures."""
        # Close the bus
        self.bus.close()

        # Remove the temporary database
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        # Remove the temporary prompt file
        if os.path.exists("llm/prompts/test_prompt.yaml"):
            os.remove("llm/prompts/test_prompt.yaml")

    def test_end_to_end_flow(self):
        """Test the end-to-end flow from fused signal to LLM to executor."""
        async def _test():
            """Async test implementation."""
            # Create a mock orchestrator
            class MockOrchestrator:
                def __init__(self, config):
                    self.config = config
                    self.bus = None
                    self.htx_client = None
                    self.executor = None
                    self.llm_consumer = MagicMock()
                    self.llm_consumer.start = MagicMock()
                    self.llm_consumer.stop = MagicMock()

                async def _on_llm_signal(self, _ts, payload):
                    # Simulate the LLM signal handler
                    symbol = payload.get("symbol", "BTC-USDT")
                    action_str = payload.get("action", "HOLD")
                    confidence = payload.get("confidence", 0.5)
                    rationale = payload.get("rationale", "")

                    # Convert action string to Side enum
                    if action_str.upper() == "BUY":
                        action = Side.BUY
                    elif action_str.upper() == "SELL":
                        action = Side.SELL
                    else:
                        action = Side.HOLD

                    # Create signal object
                    signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=action,
                        score=confidence * (1 if action == Side.BUY else -1 if action == Side.SELL else 0),
                        confidence=confidence,
                        source="llm",
                        rationale=rationale
                    )

                    # Execute signal
                    await self.executor.execute(signal)

            # Create an orchestrator with our mocks
            orchestrator = MockOrchestrator(self.config)
            orchestrator.bus = self.bus
            orchestrator.htx_client = self.htx_client
            orchestrator.executor = self.executor

            # Start the LLM consumer
            orchestrator.llm_consumer.start()

            # Publish account state
            self.bus.publish("account.state", time.time(), {
                "total": 100.0,
                "available": 90.0,
                "reserved": 10.0,
                "timestamp": datetime.now().isoformat()
            })

            # Publish a fused signal
            fused_signal = {
                "symbol": "BTC-USDT",
                "score": 0.8,
                "decision": "BUY",
                "confidence": 0.9,
                "timestamp": datetime.now().isoformat(),
                "source": "fused"
            }
            self.bus.publish("signals.fused", time.time(), fused_signal)

            # Wait for processing
            await asyncio.sleep(0.1)

            # Manually publish an LLM signal (since we don't have a real LLM Consumer)
            llm_signal = {
                "symbol": "BTC-USDT",
                "action": "BUY",
                "confidence": 0.85,
                "rationale": "Test rationale",
                "timestamp": datetime.now().isoformat(),
                "source": "llm",
                "original_signal": fused_signal
            }
            self.bus.publish("signals.llm", time.time(), llm_signal)

            # Wait for processing
            await asyncio.sleep(0.1)

            # Check that an LLM signal was published
            messages = self.bus.get_messages("signals.llm", limit=1)
            self.assertTrue(len(messages) > 0, "No LLM signal was published")

            # Get the LLM signal
            _, _, llm_signal = messages[0]

            # Manually call the LLM signal handler
            await orchestrator._on_llm_signal(time.time(), llm_signal)

            # Check that the signal was executed
            self.assertTrue(len(self.executor.executed_signals) > 0, "No signal was executed")
            executed_signal = self.executor.executed_signals[0]

            # Check the executed signal
            self.assertEqual(executed_signal.symbol, "BTC-USDT")
            self.assertEqual(executed_signal.source, "llm")

            # Stop the LLM consumer
            orchestrator.llm_consumer.stop()

        # Run the async test
        self.run_async(_test())





if __name__ == "__main__":
    unittest.main()
