#!/usr/bin/env python3
"""
Test script for Smart-Trader Control Center endpoints.
"""

import asyncio
import aiohttp
import json
import platform

# Fix Windows aiodns issue
if platform.system() == 'Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

async def test_endpoints():
    """Test the control center endpoints."""
    base_url = "http://localhost:8082"

    async with aiohttp.ClientSession() as session:
        print("🧪 Testing Smart-Trader Control Center Endpoints")
        print("=" * 60)

        # Test status endpoint
        print("1. Testing status endpoint...")
        async with session.get(f"{base_url}/api/status") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Status: {response.status}")
                print(f"   Smart-trader available: {data['smart_trader_available']}")
                print(f"   Testnet running: {data['system_status']['testnet']['running']}")
                print(f"   Live trading running: {data['system_status']['live']['running']}")
                print(f"   Orchestrator running: {data['system_status']['orchestrator']['running']}")
            else:
                print(f"❌ Status endpoint failed: {response.status}")

        print()

        # Test testnet status
        print("2. Testing testnet status endpoint...")
        async with session.get(f"{base_url}/api/testnet/status") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Testnet status: {response.status}")
                print(f"   Running: {data['running']}")
                print(f"   PID: {data['pid']}")
            else:
                print(f"❌ Testnet status failed: {response.status}")

        print()

        # Test live trading status
        print("3. Testing live trading status endpoint...")
        async with session.get(f"{base_url}/api/live/status") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Live status: {response.status}")
                print(f"   Running: {data['running']}")
                print(f"   PID: {data['pid']}")
            else:
                print(f"❌ Live status failed: {response.status}")

        print()

        # Test backtest status
        print("4. Testing backtest status endpoint...")
        async with session.get(f"{base_url}/api/backtest/status") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Backtest status: {response.status}")
                print(f"   Running: {data['running']}")
                print(f"   PID: {data['pid']}")
            else:
                print(f"❌ Backtest status failed: {response.status}")

        print()
        print("=" * 60)
        print("🎯 All endpoint tests completed!")
        print()
        print("Available endpoints:")
        print("  • GET  /api/status - System status")
        print("  • POST /api/testnet/start - Start testnet trading")
        print("  • POST /api/testnet/stop - Stop testnet trading")
        print("  • POST /api/live/start - Start live trading")
        print("  • POST /api/live/stop - Stop live trading")
        print("  • POST /api/backtest/start - Start backtesting")
        print("  • POST /api/backtest/stop - Stop backtesting")
        print("  • POST /api/orchestrator/start - Start orchestrator")
        print("  • POST /api/orchestrator/stop - Stop orchestrator")
        print("  • POST /api/data/start - Start data service")
        print("  • POST /api/data/stop - Stop data service")

if __name__ == "__main__":
    asyncio.run(test_endpoints())
