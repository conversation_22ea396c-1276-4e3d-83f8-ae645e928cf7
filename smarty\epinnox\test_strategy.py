#!/usr/bin/env python3
"""
Test script for Epinnox Trading Strategy

This script tests the core components of the Epinnox strategy
without requiring live exchange connections.
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the parent directory to Python path to enable imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# Now import from epinnox package
from epinnox.core.utils import setup_logging, calculate_rsi
from epinnox.core.feature_store import feature_store
from epinnox.models.rsi import RSIModel
from epinnox.models.vwap_deviation import VWAPDeviationModel
from epinnox.models.funding_momentum import FundingMomentumModel
from epinnox.models.open_interest_momentum import OpenInterestMomentumModel
from epinnox.strategy import EpinnoxSmartStrategy

# Setup logging
setup_logging("INFO")
logger = logging.getLogger(__name__)


async def test_feature_store():
    """Test the feature store functionality."""
    logger.info("🧪 Testing Feature Store...")

    symbol = "BTC/USDT"
    exchange = "binance"

    # Test basic set/get
    await feature_store.set(symbol, "test_value", 42.0, exchange=exchange)
    value = await feature_store.get(symbol, "test_value", exchange=exchange)
    assert value == 42.0, f"Expected 42.0, got {value}"

    # Test time series
    for i in range(10):
        timestamp = datetime.now() - timedelta(minutes=i)
        await feature_store.add_time_series(symbol, "prices", 50000 + i * 100, timestamp, exchange=exchange)

    # Get time series
    prices = await feature_store.get_time_series(symbol, "prices", exchange=exchange)
    assert len(prices) == 10, f"Expected 10 prices, got {len(prices)}"

    logger.info("✅ Feature Store tests passed!")


async def test_rsi_model():
    """Test the RSI model."""
    logger.info("🧪 Testing RSI Model...")

    # Generate sample price data
    np.random.seed(42)
    base_price = 50000
    prices = [base_price]

    for i in range(50):
        change = np.random.normal(0, 100)  # Random price changes
        new_price = max(prices[-1] + change, 1000)  # Ensure positive prices
        prices.append(new_price)

    # Test RSI model
    rsi_model = RSIModel()
    features = {
        'symbol': 'BTC/USDT',
        'close_prices': prices,
        'timestamp': datetime.now(),
        'exchange': 'binance'
    }

    prediction = await rsi_model.predict(features)

    # Validate prediction
    assert 'rsi' in prediction, "RSI value missing from prediction"
    assert 0 <= prediction['rsi'] <= 100, f"RSI should be 0-100, got {prediction['rsi']}"
    assert 'signal_strength' in prediction, "Signal strength missing"
    assert -1 <= prediction['signal_strength'] <= 1, "Signal strength should be -1 to 1"

    logger.info(f"✅ RSI Model test passed! RSI: {prediction['rsi']:.2f}, Signal: {prediction['signal_strength']:.3f}")


async def test_vwap_model():
    """Test the VWAP deviation model."""
    logger.info("🧪 Testing VWAP Model...")

    vwap_model = VWAPDeviationModel()

    # Test with sample data
    features = {
        'symbol': 'BTC/USDT',
        'close_price': 50000,
        'volume': 100,
        'timestamp': datetime.now(),
        'exchange': 'binance'
    }

    prediction = await vwap_model.predict(features)

    # Validate prediction
    assert 'signal' in prediction, "Signal missing from VWAP prediction"
    assert 'action' in prediction, "Action missing from VWAP prediction"
    assert prediction['action'] in ['BUY', 'SELL', 'HOLD'], f"Invalid action: {prediction['action']}"

    logger.info(f"✅ VWAP Model test passed! Signal: {prediction['signal']}, Action: {prediction['action']}")


async def test_funding_model():
    """Test the funding momentum model."""
    logger.info("🧪 Testing Funding Model...")

    funding_model = FundingMomentumModel()

    # Set up some funding rate data in feature store
    symbol = "BTC/USDT"
    exchange = "binance"

    # Simulate funding rate data
    funding_rates = [0.0001, 0.0002, 0.0003, 0.0002, 0.0001]
    for i, rate in enumerate(funding_rates):
        timestamp = datetime.now() - timedelta(minutes=i)
        await feature_store.set(symbol, "funding_rate", rate, exchange=exchange)
        await asyncio.sleep(0.01)  # Small delay to ensure different timestamps

    features = {
        'symbol': symbol,
        'timestamp': datetime.now(),
        'exchange': exchange,
        'funding_rate': 0.0001
    }

    prediction = await funding_model.predict(features)

    # Validate prediction
    assert 'action' in prediction, "Action missing from funding prediction"
    assert prediction['action'] in ['BUY', 'SELL', 'HOLD'], f"Invalid action: {prediction['action']}"

    logger.info(f"✅ Funding Model test passed! Action: {prediction['action']}, Confidence: {prediction.get('confidence', 0):.3f}")


async def test_oi_model():
    """Test the open interest momentum model."""
    logger.info("🧪 Testing Open Interest Model...")

    oi_model = OpenInterestMomentumModel()

    # Set up some OI data in feature store
    symbol = "BTC/USDT"
    exchange = "binance"

    # Simulate open interest data
    oi_values = [1000000, 1001000, 1002000, 1001500, 1000500]
    for i, oi in enumerate(oi_values):
        timestamp = datetime.now() - timedelta(minutes=i)
        await feature_store.set(symbol, "open_interest", oi, exchange=exchange)
        await asyncio.sleep(0.01)  # Small delay

    features = {
        'symbol': symbol,
        'timestamp': datetime.now(),
        'exchange': exchange,
        'open_interest': 1000000
    }

    prediction = await oi_model.predict(features)

    # Validate prediction
    assert 'action' in prediction, "Action missing from OI prediction"
    assert prediction['action'] in ['BUY', 'SELL', 'HOLD'], f"Invalid action: {prediction['action']}"

    logger.info(f"✅ OI Model test passed! Action: {prediction['action']}, Confidence: {prediction.get('confidence', 0):.3f}")


async def test_smart_strategy():
    """Test the complete smart strategy."""
    logger.info("🧪 Testing Smart Strategy...")

    # Initialize strategy
    config = {
        "weights": {
            "technical": 0.3,
            "vwap": 0.2,
            "rsi_model": 0.15,
            "funding": 0.1,
            "open_interest": 0.1,
            "volatility": 0.1,
            "ensemble": 0.05
        },
        "base_buy_threshold": 0.3,
        "base_sell_threshold": -0.3
    }

    strategy = EpinnoxSmartStrategy(config)

    # Set up test data in feature store
    symbol = "BTC/USDT"
    exchange = "binance"

    # Add price data
    base_price = 50000
    for i in range(50):
        price = base_price + np.random.normal(0, 100)
        timestamp = datetime.now() - timedelta(minutes=i)
        await feature_store.set(symbol, "close", price, exchange=exchange)
        await feature_store.add_time_series(symbol, "close_prices", price, timestamp, exchange=exchange)
        await feature_store.set(symbol, "volume", 100 + np.random.uniform(0, 50), exchange=exchange)

    # Generate signals
    signals = await strategy.generate_signals(
        timestamp=datetime.now(),
        symbols=[symbol],
        exchange=exchange
    )

    logger.info(f"✅ Smart Strategy test completed! Generated {len(signals)} signals")

    for signal in signals:
        logger.info(f"   📈 {signal.action} {signal.symbol} (score: {signal.score:.3f})")
        logger.info(f"      Rationale: {signal.rationale}")


async def test_utils():
    """Test utility functions."""
    logger.info("🧪 Testing Utilities...")

    # Test RSI calculation
    prices = np.array([44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 47.69, 46.49, 46.26])
    rsi_values = calculate_rsi(prices, 14)

    assert len(rsi_values) == len(prices), "RSI array length mismatch"
    assert 0 <= rsi_values[-1] <= 100, f"RSI should be 0-100, got {rsi_values[-1]}"

    logger.info(f"✅ Utilities test passed! RSI: {rsi_values[-1]:.2f}")


async def run_all_tests():
    """Run all tests."""
    logger.info("🚀 Starting Epinnox Strategy Tests...")

    try:
        await test_utils()
        await test_feature_store()
        await test_rsi_model()
        await test_vwap_model()
        await test_funding_model()
        await test_oi_model()
        await test_smart_strategy()

        logger.info("🎉 All tests passed successfully!")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_all_tests())
