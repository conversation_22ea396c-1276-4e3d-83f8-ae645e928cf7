// Money Circle Personal Dashboard JavaScript

// Global variables
let websocket = null;
let portfolioData = {};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    connectWebSocket();
    setupEventListeners();
});

function initializeDashboard() {
    console.log('🚀 Initializing Money Circle Personal Dashboard');

    // Load initial portfolio data
    loadPortfolioData();

    // Setup auto-refresh
    setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
}

function connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    try {
        websocket = new WebSocket(wsUrl);

        websocket.onopen = function(event) {
            console.log('📡 WebSocket connected');
        };

        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        websocket.onclose = function(event) {
            console.log('📡 WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(connectWebSocket, 5000);
        };

        websocket.onerror = function(error) {
            console.error('WebSocket error:', error);
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
    }
}

function handleWebSocketMessage(data) {
    console.log('📨 WebSocket message received:', data);

    // Update portfolio data if received
    if (data.portfolio) {
        portfolioData = data.portfolio;
        updatePortfolioDisplay();
    }

    // Handle real-time price updates
    if (data.prices) {
        updatePriceDisplays(data.prices);
    }

    // Handle position updates
    if (data.positions) {
        updatePositionsTable(data.positions);
    }
}

async function loadPortfolioData() {
    try {
        const response = await fetch('/api/portfolio');
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                portfolioData = result.portfolio;
                updatePortfolioDisplay();
                updateMarketData();
            } else {
                console.error('Portfolio API error:', result.error);
            }
        } else {
            console.error('Failed to load portfolio data');
        }
    } catch (error) {
        console.error('Error loading portfolio data:', error);
    }
}

function updatePortfolioDisplay() {
    // Update portfolio stats
    updateElement('total-balance', `$${portfolioData.total_balance_usd?.toFixed(2) || '0.00'}`);
    updateElement('daily-pnl', `$${portfolioData.performance?.daily_pnl?.toFixed(2) || '0.00'}`);
    updateElement('win-rate', `${portfolioData.performance?.win_rate?.toFixed(1) || '0.0'}%`);
    updateElement('total-trades', portfolioData.performance?.total_trades || '0');

    // Update exchange cards
    updateExchangeCards();

    // Update positions table
    updatePositionsTable(portfolioData.positions || []);

    // Update trades table
    updateTradesTable(portfolioData.recent_trades || []);
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function updateExchangeCards() {
    // This would update the exchange connection status and balances
    console.log('Updating exchange cards...');
}

function updatePositionsTable(positions) {
    console.log('Updating positions table with', positions.length, 'positions');
    // Implementation would update the positions table
}

function updateTradesTable(trades) {
    console.log('Updating trades table with', trades.length, 'trades');
    // Implementation would update the trades table
}

function updatePriceDisplays(prices) {
    // Update any price displays on the dashboard
    console.log('Updating price displays:', prices);
}

function updateMarketData() {
    // Update market data from portfolio data
    if (portfolioData.market_data) {
        const marketData = portfolioData.market_data;

        // Update price tickers for major symbols
        Object.keys(marketData).forEach(symbol => {
            const data = marketData[symbol];
            if (data.price) {
                updatePriceTicker(symbol, data.price, data.change_24h);
            }
        });

        // Update order book if available
        if (marketData['BTC-USDT'] && marketData['BTC-USDT'].orderbook) {
            updateOrderBookDisplay(marketData['BTC-USDT'].orderbook);
        }

        // Update recent trades if available
        if (marketData['BTC-USDT'] && marketData['BTC-USDT'].recent_trades) {
            updateRecentTradesDisplay(marketData['BTC-USDT'].recent_trades);
        }
    }
}

function updatePriceTicker(symbol, price, change24h) {
    // Update price ticker elements
    const priceElement = document.getElementById(`price-${symbol}`);
    const changeElement = document.getElementById(`change-${symbol}`);

    if (priceElement) {
        priceElement.textContent = `$${price.toFixed(2)}`;
    }

    if (changeElement) {
        const changePercent = change24h || 0;
        changeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%`;
        changeElement.className = changePercent >= 0 ? 'positive' : 'negative';
    }
}

function updateOrderBookDisplay(orderbook) {
    const orderbookElement = document.getElementById('orderbook');
    if (!orderbookElement || !orderbook) return;

    const { bids = [], asks = [] } = orderbook;

    let html = `
        <div class="orderbook-header">
            <div class="orderbook-column">Price (USDT)</div>
            <div class="orderbook-column">Amount</div>
            <div class="orderbook-column">Total</div>
        </div>
        <div class="orderbook-asks">
    `;

    // Display asks (sell orders) - reverse order to show highest first
    asks.slice(0, 8).reverse().forEach(([price, amount]) => {
        const total = (price * amount).toFixed(2);
        html += `
            <div class="orderbook-row ask">
                <span class="price">${price}</span>
                <span class="amount">${amount}</span>
                <span class="total">${total}</span>
            </div>
        `;
    });

    html += `
        </div>
        <div class="orderbook-spread">
            <div class="spread-info">Spread: $${(asks[0]?.[0] - bids[0]?.[0] || 0).toFixed(2)}</div>
        </div>
        <div class="orderbook-bids">
    `;

    // Display bids (buy orders)
    bids.slice(0, 8).forEach(([price, amount]) => {
        const total = (price * amount).toFixed(2);
        html += `
            <div class="orderbook-row bid">
                <span class="price">${price}</span>
                <span class="amount">${amount}</span>
                <span class="total">${total}</span>
            </div>
        `;
    });

    html += '</div>';
    orderbookElement.innerHTML = html;
}

function updateRecentTradesDisplay(trades) {
    const tradesElement = document.getElementById('trades-list');
    if (!tradesElement || !trades) return;

    let html = `
        <div class="trades-header">
            <div class="trade-column">Price</div>
            <div class="trade-column">Amount</div>
            <div class="trade-column">Time</div>
        </div>
        <div class="trades-container">
    `;

    trades.slice(0, 8).forEach(trade => {
        const time = new Date(trade.timestamp * 1000).toLocaleTimeString();
        const sideClass = trade.side === 'buy' ? 'buy-trade' : 'sell-trade';

        html += `
            <div class="trade-row ${sideClass}">
                <span class="trade-price">${trade.price}</span>
                <span class="trade-amount">${trade.amount}</span>
                <span class="trade-time">${time}</span>
            </div>
        `;
    });

    html += '</div>';
    tradesElement.innerHTML = html;
}

function setupEventListeners() {
    // Add exchange button
    const addExchangeBtn = document.querySelector('.add-exchange-btn');
    if (addExchangeBtn) {
        addExchangeBtn.addEventListener('click', showAddExchangeModal);
    }

    // Add exchange form
    const addExchangeForm = document.getElementById('addExchangeForm');
    if (addExchangeForm) {
        addExchangeForm.addEventListener('submit', handleAddExchange);
    }

    // Trading form
    const tradingForm = document.getElementById('tradingForm');
    if (tradingForm) {
        tradingForm.addEventListener('submit', handleTradingForm);
    }

    // Modal close events
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('addExchangeModal');
        if (event.target === modal) {
            hideAddExchangeModal();
        }
    });
}

function showAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function hideAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'none';
        // Reset form
        const form = document.getElementById('addExchangeForm');
        if (form) {
            form.reset();
        }
    }
}

async function handleAddExchange(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const exchangeData = {
        exchange: formData.get('exchange'),
        api_key: formData.get('api_key'),
        secret_key: formData.get('secret_key'),
        passphrase: formData.get('passphrase')
    };

    try {
        const response = await fetch('/api/exchange/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exchangeData)
        });

        if (response.ok) {
            console.log('✅ Exchange account added successfully');
            hideAddExchangeModal();
            refreshDashboard();
            showNotification('Exchange account added successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to add exchange account:', error);
            showNotification('Failed to add exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error adding exchange account:', error);
        showNotification('Error adding exchange account', 'error');
    }
}

async function removeExchange(exchangeId) {
    if (!confirm('Are you sure you want to remove this exchange account?')) {
        return;
    }

    try {
        const response = await fetch(`/api/exchange/${exchangeId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            console.log('✅ Exchange account removed successfully');
            refreshDashboard();
            showNotification('Exchange account removed successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to remove exchange account:', error);
            showNotification('Failed to remove exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error removing exchange account:', error);
        showNotification('Error removing exchange account', 'error');
    }
}

async function refreshExchange(exchangeName) {
    console.log(`🔄 Refreshing ${exchangeName} data...`);

    try {
        const response = await fetch(`/api/balance/${exchangeName}`);
        if (response.ok) {
            const balanceData = await response.json();
            console.log('✅ Exchange data refreshed:', balanceData);
            // Update the specific exchange card
            showNotification(`${exchangeName} data refreshed!`, 'success');
        } else {
            console.error('Failed to refresh exchange data');
            showNotification('Failed to refresh exchange data', 'error');
        }
    } catch (error) {
        console.error('Error refreshing exchange data:', error);
        showNotification('Error refreshing exchange data', 'error');
    }
}

async function closePosition(symbol, exchange = 'HTX') {
    if (!confirm(`Are you sure you want to close your ${symbol} position?`)) {
        return;
    }

    try {
        console.log(`🔄 Closing position for ${symbol}...`);

        const response = await fetch('/api/trading/close_position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                percentage: 100.0
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Position closed successfully for ${symbol}`, 'success');
            refreshDashboard();
        } else {
            showNotification(`Failed to close position: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error closing position:', error);
        showNotification('Error closing position', 'error');
    }
}

// Trading functions
async function placeMarketOrder(exchange, symbol, side, amount, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/market_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Market order placed: ${side} ${amount} ${symbol}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing market order:', error);
        showNotification('Error placing market order', 'error');
        return null;
    }
}

async function placeLimitOrder(exchange, symbol, side, amount, price, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/limit_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                price: price,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Limit order placed: ${side} ${amount} ${symbol} @ $${price}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing limit order:', error);
        showNotification('Error placing limit order', 'error');
        return null;
    }
}

async function setStopLoss(exchange, symbol, stopPrice) {
    try {
        const response = await fetch('/api/trading/stop_loss', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                stop_price: stopPrice
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Stop loss set for ${symbol} @ $${stopPrice}`, 'success');
            return true;
        } else {
            showNotification(`Failed to set stop loss: ${result.error}`, 'error');
            return false;
        }
    } catch (error) {
        console.error('Error setting stop loss:', error);
        showNotification('Error setting stop loss', 'error');
        return false;
    }
}

async function setTakeProfit(exchange, symbol, targetPrice) {
    try {
        const response = await fetch('/api/trading/take_profit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                target_price: targetPrice
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Take profit set for ${symbol} @ $${targetPrice}`, 'success');
            return true;
        } else {
            showNotification(`Failed to set take profit: ${result.error}`, 'error');
            return false;
        }
    } catch (error) {
        console.error('Error setting take profit:', error);
        showNotification('Error setting take profit', 'error');
        return false;
    }
}

function refreshDashboard() {
    console.log('🔄 Refreshing dashboard data...');
    loadPortfolioData();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #f44336, #da190b)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
    }

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatPercentage(value) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
}

function formatNumber(value, decimals = 2) {
    return value.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

async function handleTradingForm(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const orderData = {
        exchange: formData.get('exchange'),
        symbol: formData.get('symbol'),
        side: formData.get('side'),
        amount: parseFloat(formData.get('amount')),
        order_type: formData.get('order_type'),
        price: formData.get('price') ? parseFloat(formData.get('price')) : null
    };

    // Validate form data
    if (!orderData.exchange || !orderData.symbol || !orderData.side || !orderData.amount || !orderData.order_type) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    if (orderData.order_type === 'limit' && !orderData.price) {
        showNotification('Price is required for limit orders', 'error');
        return;
    }

    try {
        let result;

        if (orderData.order_type === 'market') {
            result = await placeMarketOrder(
                orderData.exchange,
                orderData.symbol,
                orderData.side,
                orderData.amount
            );
        } else if (orderData.order_type === 'limit') {
            result = await placeLimitOrder(
                orderData.exchange,
                orderData.symbol,
                orderData.side,
                orderData.amount,
                orderData.price
            );
        }

        if (result) {
            // Reset form on success
            event.target.reset();
        }

    } catch (error) {
        console.error('Trading form error:', error);
        showNotification('Error placing order', 'error');
    }
}

function showQuickTrade() {
    // Quick trade functionality - could open a modal with preset amounts
    showNotification('Quick trade feature coming soon!', 'info');
}
