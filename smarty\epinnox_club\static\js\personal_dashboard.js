// Money Circle Personal Dashboard JavaScript

// Global variables
let websocket = null;
let portfolioData = {};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    connectWebSocket();
    setupEventListeners();
});

function initializeDashboard() {
    console.log('🚀 Initializing Money Circle Personal Dashboard');

    // Load initial portfolio data
    loadPortfolioData();

    // Setup auto-refresh
    setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
}

function connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    try {
        websocket = new WebSocket(wsUrl);

        websocket.onopen = function(event) {
            console.log('📡 WebSocket connected');
        };

        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        websocket.onclose = function(event) {
            console.log('📡 WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(connectWebSocket, 5000);
        };

        websocket.onerror = function(error) {
            console.error('WebSocket error:', error);
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
    }
}

function handleWebSocketMessage(data) {
    console.log('📨 WebSocket message received:', data);

    // Update portfolio data if received
    if (data.portfolio) {
        portfolioData = data.portfolio;
        updatePortfolioDisplay();
    }

    // Handle real-time price updates
    if (data.prices) {
        updatePriceDisplays(data.prices);
    }

    // Handle position updates
    if (data.positions) {
        updatePositionsTable(data.positions);
    }
}

async function loadPortfolioData() {
    try {
        const response = await fetch('/api/portfolio');
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                portfolioData = result.portfolio;
                updatePortfolioDisplay();
                updateMarketData();
            } else {
                console.error('Portfolio API error:', result.error);
            }
        } else {
            console.error('Failed to load portfolio data');
        }
    } catch (error) {
        console.error('Error loading portfolio data:', error);
    }
}

function updatePortfolioDisplay() {
    // Update portfolio stats
    updateElement('total-balance', `$${portfolioData.total_balance_usd?.toFixed(2) || '0.00'}`);
    updateElement('daily-pnl', `$${portfolioData.performance?.daily_pnl?.toFixed(2) || '0.00'}`);
    updateElement('win-rate', `${portfolioData.performance?.win_rate?.toFixed(1) || '0.0'}%`);
    updateElement('total-trades', portfolioData.performance?.total_trades || '0');

    // Update exchange cards
    updateExchangeCards();

    // Update positions table
    updatePositionsTable(portfolioData.positions || []);

    // Update trades table
    updateTradesTable(portfolioData.recent_trades || []);
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function updateExchangeCards() {
    // This would update the exchange connection status and balances
    console.log('Updating exchange cards...');
}

function updatePositionsTable(positions) {
    console.log('Updating positions table with', positions.length, 'positions');
    // Implementation would update the positions table
}

function updateTradesTable(trades) {
    console.log('Updating trades table with', trades.length, 'trades');
    // Implementation would update the trades table
}

function updatePriceDisplays(prices) {
    // Update any price displays on the dashboard
    console.log('Updating price displays:', prices);
}

function updateMarketData() {
    // Update market data from portfolio data
    if (portfolioData.market_data) {
        const marketData = portfolioData.market_data;

        // Update price tickers for major symbols
        Object.keys(marketData).forEach(symbol => {
            const data = marketData[symbol];
            if (data.price) {
                updatePriceTicker(symbol, data.price, data.change_24h);
            }
        });

        // Update order book if available
        if (marketData['BTC-USDT'] && marketData['BTC-USDT'].orderbook) {
            updateOrderBookDisplay(marketData['BTC-USDT'].orderbook);
        }

        // Update recent trades if available
        if (marketData['BTC-USDT'] && marketData['BTC-USDT'].recent_trades) {
            updateRecentTradesDisplay(marketData['BTC-USDT'].recent_trades);
        }
    }
}

function updatePriceTicker(symbol, price, change24h) {
    // Update price ticker elements
    const priceElement = document.getElementById(`price-${symbol}`);
    const changeElement = document.getElementById(`change-${symbol}`);

    if (priceElement) {
        priceElement.textContent = `$${price.toFixed(2)}`;
    }

    if (changeElement) {
        const changePercent = change24h || 0;
        changeElement.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%`;
        changeElement.className = changePercent >= 0 ? 'positive' : 'negative';
    }
}

function updateOrderBookDisplay(orderbook) {
    const orderbookElement = document.getElementById('orderbook');
    if (!orderbookElement || !orderbook) return;

    const { bids = [], asks = [] } = orderbook;

    let html = `
        <div class="orderbook-header">
            <div class="orderbook-column">Price (USDT)</div>
            <div class="orderbook-column">Amount</div>
            <div class="orderbook-column">Total</div>
        </div>
        <div class="orderbook-asks">
    `;

    // Display asks (sell orders) - reverse order to show highest first
    asks.slice(0, 8).reverse().forEach(([price, amount]) => {
        const total = (price * amount).toFixed(2);
        html += `
            <div class="orderbook-row ask">
                <span class="price">${price}</span>
                <span class="amount">${amount}</span>
                <span class="total">${total}</span>
            </div>
        `;
    });

    html += `
        </div>
        <div class="orderbook-spread">
            <div class="spread-info">Spread: $${(asks[0]?.[0] - bids[0]?.[0] || 0).toFixed(2)}</div>
        </div>
        <div class="orderbook-bids">
    `;

    // Display bids (buy orders)
    bids.slice(0, 8).forEach(([price, amount]) => {
        const total = (price * amount).toFixed(2);
        html += `
            <div class="orderbook-row bid">
                <span class="price">${price}</span>
                <span class="amount">${amount}</span>
                <span class="total">${total}</span>
            </div>
        `;
    });

    html += '</div>';
    orderbookElement.innerHTML = html;
}

function updateRecentTradesDisplay(trades) {
    const tradesElement = document.getElementById('trades-list');
    if (!tradesElement || !trades) return;

    let html = `
        <div class="trades-header">
            <div class="trade-column">Price</div>
            <div class="trade-column">Amount</div>
            <div class="trade-column">Time</div>
        </div>
        <div class="trades-container">
    `;

    trades.slice(0, 8).forEach(trade => {
        const time = new Date(trade.timestamp * 1000).toLocaleTimeString();
        const sideClass = trade.side === 'buy' ? 'buy-trade' : 'sell-trade';

        html += `
            <div class="trade-row ${sideClass}">
                <span class="trade-price">${trade.price}</span>
                <span class="trade-amount">${trade.amount}</span>
                <span class="trade-time">${time}</span>
            </div>
        `;
    });

    html += '</div>';
    tradesElement.innerHTML = html;
}

function setupEventListeners() {
    // Add exchange button
    const addExchangeBtn = document.querySelector('.add-exchange-btn');
    if (addExchangeBtn) {
        addExchangeBtn.addEventListener('click', showAddExchangeModal);
    }

    // Add exchange form
    const addExchangeForm = document.getElementById('addExchangeForm');
    if (addExchangeForm) {
        addExchangeForm.addEventListener('submit', handleAddExchange);
    }

    // Trading form
    const tradingForm = document.getElementById('tradingForm');
    if (tradingForm) {
        tradingForm.addEventListener('submit', handleTradingForm);
    }

    // Quick trade buttons
    setupQuickTradeButtons();

    // Position management buttons
    setupPositionManagement();

    // Risk management controls
    setupRiskManagement();

    // Order type buttons
    setupOrderTypeButtons();

    // Modal close events
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('addExchangeModal');
        if (event.target === modal) {
            hideAddExchangeModal();
        }
    });
}

function showAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

function hideAddExchangeModal() {
    const modal = document.getElementById('addExchangeModal');
    if (modal) {
        modal.style.display = 'none';
        // Reset form
        const form = document.getElementById('addExchangeForm');
        if (form) {
            form.reset();
        }
    }
}

async function handleAddExchange(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const exchangeData = {
        exchange: formData.get('exchange'),
        api_key: formData.get('api_key'),
        secret_key: formData.get('secret_key'),
        passphrase: formData.get('passphrase')
    };

    try {
        const response = await fetch('/api/exchange/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exchangeData)
        });

        if (response.ok) {
            console.log('✅ Exchange account added successfully');
            hideAddExchangeModal();
            refreshDashboard();
            showNotification('Exchange account added successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to add exchange account:', error);
            showNotification('Failed to add exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error adding exchange account:', error);
        showNotification('Error adding exchange account', 'error');
    }
}

async function removeExchange(exchangeId) {
    if (!confirm('Are you sure you want to remove this exchange account?')) {
        return;
    }

    try {
        const response = await fetch(`/api/exchange/${exchangeId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            console.log('✅ Exchange account removed successfully');
            refreshDashboard();
            showNotification('Exchange account removed successfully!', 'success');
        } else {
            const error = await response.json();
            console.error('Failed to remove exchange account:', error);
            showNotification('Failed to remove exchange account: ' + error.message, 'error');
        }
    } catch (error) {
        console.error('Error removing exchange account:', error);
        showNotification('Error removing exchange account', 'error');
    }
}

async function refreshExchange(exchangeName) {
    console.log(`🔄 Refreshing ${exchangeName} data...`);

    try {
        const response = await fetch(`/api/balance/${exchangeName}`);
        if (response.ok) {
            const balanceData = await response.json();
            console.log('✅ Exchange data refreshed:', balanceData);
            // Update the specific exchange card
            showNotification(`${exchangeName} data refreshed!`, 'success');
        } else {
            console.error('Failed to refresh exchange data');
            showNotification('Failed to refresh exchange data', 'error');
        }
    } catch (error) {
        console.error('Error refreshing exchange data:', error);
        showNotification('Error refreshing exchange data', 'error');
    }
}

async function closePosition(symbol, exchange = 'HTX') {
    if (!confirm(`Are you sure you want to close your ${symbol} position?`)) {
        return;
    }

    try {
        console.log(`🔄 Closing position for ${symbol}...`);

        const response = await fetch('/api/trading/close_position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                percentage: 100.0
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Position closed successfully for ${symbol}`, 'success');
            refreshDashboard();
        } else {
            showNotification(`Failed to close position: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error closing position:', error);
        showNotification('Error closing position', 'error');
    }
}

// Trading functions
async function placeMarketOrder(exchange, symbol, side, amount, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/market_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Market order placed: ${side} ${amount} ${symbol}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing market order:', error);
        showNotification('Error placing market order', 'error');
        return null;
    }
}

async function placeLimitOrder(exchange, symbol, side, amount, price, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/limit_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                price: price,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Limit order placed: ${side} ${amount} ${symbol} @ $${price}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing limit order:', error);
        showNotification('Error placing limit order', 'error');
        return null;
    }
}

async function setStopLoss(exchange, symbol, stopPrice) {
    try {
        const response = await fetch('/api/trading/stop_loss', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                stop_price: stopPrice
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Stop loss set for ${symbol} @ $${stopPrice}`, 'success');
            return true;
        } else {
            showNotification(`Failed to set stop loss: ${result.error}`, 'error');
            return false;
        }
    } catch (error) {
        console.error('Error setting stop loss:', error);
        showNotification('Error setting stop loss', 'error');
        return false;
    }
}

async function setTakeProfit(exchange, symbol, targetPrice) {
    try {
        const response = await fetch('/api/trading/take_profit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                target_price: targetPrice
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Take profit set for ${symbol} @ $${targetPrice}`, 'success');
            return true;
        } else {
            showNotification(`Failed to set take profit: ${result.error}`, 'error');
            return false;
        }
    } catch (error) {
        console.error('Error setting take profit:', error);
        showNotification('Error setting take profit', 'error');
        return false;
    }
}

function refreshDashboard() {
    console.log('🔄 Refreshing dashboard data...');
    loadPortfolioData();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #f44336, #da190b)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
    }

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// ===== ENHANCED TRADING CONTROLS =====

function setupQuickTradeButtons() {
    console.log('🚀 Setting up quick trade buttons');

    // Quick buy/sell buttons for major pairs
    const quickTradeContainer = document.querySelector('.quick-trade-buttons');
    if (quickTradeContainer) {
        const majorPairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT'];

        majorPairs.forEach(symbol => {
            const buttonGroup = document.createElement('div');
            buttonGroup.className = 'quick-trade-group';
            buttonGroup.innerHTML = `
                <div class="symbol-header">${symbol}</div>
                <div class="price-display" id="price-${symbol}">Loading...</div>
                <div class="button-group">
                    <button class="quick-buy-btn" onclick="quickTrade('${symbol}', 'buy')">
                        🟢 Quick Buy
                    </button>
                    <button class="quick-sell-btn" onclick="quickTrade('${symbol}', 'sell')">
                        🔴 Quick Sell
                    </button>
                </div>
            `;
            quickTradeContainer.appendChild(buttonGroup);
        });
    }
}

function setupPositionManagement() {
    console.log('📊 Setting up position management');

    // Add position action buttons to existing positions
    const positionCards = document.querySelectorAll('.position-card');
    positionCards.forEach(card => {
        const symbol = card.dataset.symbol;
        const positionId = card.dataset.positionId;

        if (symbol && positionId) {
            const actionsDiv = card.querySelector('.position-actions') || createPositionActions(card);

            actionsDiv.innerHTML = `
                <button class="btn-modify" onclick="modifyPosition('${positionId}', '${symbol}')">
                    ⚙️ Modify
                </button>
                <button class="btn-close" onclick="closePosition('${positionId}', '${symbol}')">
                    ❌ Close
                </button>
                <button class="btn-hedge" onclick="hedgePosition('${positionId}', '${symbol}')">
                    🛡️ Hedge
                </button>
            `;
        }
    });
}

function setupRiskManagement() {
    console.log('🛡️ Setting up risk management controls');

    // Add risk controls to trading interface
    const tradingInterface = document.querySelector('.trading-interface');
    if (tradingInterface) {
        const riskControlsDiv = document.createElement('div');
        riskControlsDiv.className = 'risk-controls';
        riskControlsDiv.innerHTML = `
            <h3>🛡️ Risk Management</h3>
            <div class="risk-metrics">
                <div class="metric">
                    <label>Portfolio Risk:</label>
                    <span id="portfolio-risk" class="risk-value">Loading...</span>
                </div>
                <div class="metric">
                    <label>Daily P&L:</label>
                    <span id="daily-pnl" class="pnl-value">Loading...</span>
                </div>
                <div class="metric">
                    <label>Max Drawdown:</label>
                    <span id="max-drawdown" class="drawdown-value">Loading...</span>
                </div>
            </div>
            <div class="risk-actions">
                <button onclick="setGlobalStopLoss()">🛑 Global Stop Loss</button>
                <button onclick="emergencyCloseAll()">🚨 Emergency Close All</button>
                <button onclick="showRiskSettings()">⚙️ Risk Settings</button>
            </div>
        `;
        tradingInterface.appendChild(riskControlsDiv);
    }
}

// ===== TRADING EXECUTION FUNCTIONS =====

async function quickTrade(symbol, side) {
    console.log(`🚀 Quick ${side} for ${symbol}`);

    try {
        // Get current market price
        const marketData = await fetchMarketData(symbol);
        if (!marketData) {
            throw new Error('Unable to fetch market data');
        }

        // Show quick trade confirmation
        const confirmed = await showQuickTradeConfirmation(symbol, side, marketData.price);
        if (!confirmed) return;

        // Get default trade amount from user settings
        const amount = getDefaultTradeAmount(symbol);

        // Execute market order
        const result = await executeMarketOrder(symbol, side, amount);

        if (result.success) {
            showNotification(`✅ ${side.toUpperCase()} order executed for ${symbol}`, 'success');
            refreshPortfolioData();
        } else {
            throw new Error(result.error || 'Order execution failed');
        }

    } catch (error) {
        console.error('Quick trade error:', error);
        showNotification(`❌ Quick trade failed: ${error.message}`, 'error');
    }
}

async function executeMarketOrder(symbol, side, amount, exchange = null) {
    console.log(`📈 Executing market order: ${side} ${amount} ${symbol}`);

    try {
        const response = await fetch('/api/trading/market_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                symbol: symbol,
                side: side,
                amount: amount,
                exchange: exchange || getPreferredExchange(symbol),
                strategy_name: 'Manual_Quick_Trade'
            })
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || 'Network error');
        }

        return result;

    } catch (error) {
        console.error('Market order execution error:', error);
        return { success: false, error: error.message };
    }
}

async function executeLimitOrder(symbol, side, amount, price, exchange = null) {
    console.log(`📊 Executing limit order: ${side} ${amount} ${symbol} @ ${price}`);

    try {
        const response = await fetch('/api/trading/limit_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                symbol: symbol,
                side: side,
                amount: amount,
                price: price,
                exchange: exchange || getPreferredExchange(symbol),
                strategy_name: 'Manual_Limit_Trade'
            })
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || 'Network error');
        }

        return result;

    } catch (error) {
        console.error('Limit order execution error:', error);
        return { success: false, error: error.message };
    }
}

// ===== POSITION MANAGEMENT FUNCTIONS =====

async function closePosition(positionId, symbol) {
    console.log(`❌ Closing position: ${positionId} for ${symbol}`);

    try {
        const confirmed = await showConfirmation(
            `Close Position`,
            `Are you sure you want to close your ${symbol} position?`
        );

        if (!confirmed) return;

        const response = await fetch('/api/trading/close_position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                position_id: positionId,
                symbol: symbol
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`✅ Position closed for ${symbol}`, 'success');
            refreshPortfolioData();
        } else {
            throw new Error(result.error || 'Failed to close position');
        }

    } catch (error) {
        console.error('Close position error:', error);
        showNotification(`❌ Failed to close position: ${error.message}`, 'error');
    }
}

async function modifyPosition(positionId, symbol) {
    console.log(`⚙️ Modifying position: ${positionId} for ${symbol}`);

    // Show position modification modal
    showPositionModificationModal(positionId, symbol);
}

async function hedgePosition(positionId, symbol) {
    console.log(`🛡️ Hedging position: ${positionId} for ${symbol}`);

    try {
        // Get current position details
        const position = await getPositionDetails(positionId);
        if (!position) {
            throw new Error('Position not found');
        }

        // Calculate hedge amount (opposite side, same size)
        const hedgeSide = position.side === 'long' ? 'sell' : 'buy';
        const hedgeAmount = position.size;

        const confirmed = await showConfirmation(
            `Hedge Position`,
            `Create a ${hedgeSide} order for ${hedgeAmount} ${symbol} to hedge your position?`
        );

        if (!confirmed) return;

        // Execute hedge order
        const result = await executeMarketOrder(symbol, hedgeSide, hedgeAmount);

        if (result.success) {
            showNotification(`✅ Hedge order executed for ${symbol}`, 'success');
            refreshPortfolioData();
        } else {
            throw new Error(result.error || 'Hedge order failed');
        }

    } catch (error) {
        console.error('Hedge position error:', error);
        showNotification(`❌ Failed to hedge position: ${error.message}`, 'error');
    }
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatPercentage(value) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
}

function formatNumber(value, decimals = 2) {
    return value.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

// ===== SUPPORTING UTILITY FUNCTIONS =====

async function fetchMarketData(symbol) {
    try {
        const response = await fetch(`/api/market/data/${symbol}`);
        if (!response.ok) throw new Error('Market data fetch failed');
        return await response.json();
    } catch (error) {
        console.error('Market data fetch error:', error);
        return null;
    }
}

function getDefaultTradeAmount(symbol) {
    // Get from user settings or use defaults
    const defaults = {
        'BTCUSDT': 0.001,
        'ETHUSDT': 0.01,
        'ADAUSDT': 10,
        'SOLUSDT': 0.1
    };
    return defaults[symbol] || 0.01;
}

function getPreferredExchange(symbol) {
    // Get user's preferred exchange for symbol or use default
    return 'HTX'; // Default to HTX for now
}

async function showQuickTradeConfirmation(symbol, side, price) {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'trade-confirmation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>Confirm Quick Trade</h3>
                <div class="trade-details">
                    <p><strong>Symbol:</strong> ${symbol}</p>
                    <p><strong>Side:</strong> ${side.toUpperCase()}</p>
                    <p><strong>Price:</strong> $${price.toFixed(4)}</p>
                    <p><strong>Amount:</strong> ${getDefaultTradeAmount(symbol)}</p>
                </div>
                <div class="modal-actions">
                    <button class="btn-confirm" onclick="confirmTrade(true)">✅ Confirm</button>
                    <button class="btn-cancel" onclick="confirmTrade(false)">❌ Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        window.confirmTrade = (confirmed) => {
            document.body.removeChild(modal);
            delete window.confirmTrade;
            resolve(confirmed);
        };
    });
}

async function showConfirmation(title, message) {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'confirmation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>${title}</h3>
                <p>${message}</p>
                <div class="modal-actions">
                    <button class="btn-confirm" onclick="confirmAction(true)">✅ Yes</button>
                    <button class="btn-cancel" onclick="confirmAction(false)">❌ No</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        window.confirmAction = (confirmed) => {
            document.body.removeChild(modal);
            delete window.confirmAction;
            resolve(confirmed);
        };
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Add to notification container or body
    const container = document.querySelector('.notification-container') || document.body;
    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

async function getPositionDetails(positionId) {
    try {
        const response = await fetch(`/api/trading/position/${positionId}`);
        if (!response.ok) throw new Error('Position fetch failed');
        return await response.json();
    } catch (error) {
        console.error('Position details fetch error:', error);
        return null;
    }
}

function createPositionActions(card) {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'position-actions';
    card.appendChild(actionsDiv);
    return actionsDiv;
}

function showPositionModificationModal(positionId, symbol) {
    const modal = document.createElement('div');
    modal.className = 'position-modification-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>Modify Position - ${symbol}</h3>
            <div class="modification-options">
                <div class="option-group">
                    <label>Stop Loss:</label>
                    <input type="number" id="stop-loss-price" placeholder="Stop loss price" step="0.0001">
                    <button onclick="setStopLoss('${positionId}')">Set Stop Loss</button>
                </div>
                <div class="option-group">
                    <label>Take Profit:</label>
                    <input type="number" id="take-profit-price" placeholder="Take profit price" step="0.0001">
                    <button onclick="setTakeProfit('${positionId}')">Set Take Profit</button>
                </div>
                <div class="option-group">
                    <label>Partial Close:</label>
                    <input type="number" id="partial-amount" placeholder="Amount to close" step="0.001">
                    <button onclick="partialClose('${positionId}')">Partial Close</button>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="closeModal()">Close</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal function
    window.closeModal = () => {
        document.body.removeChild(modal);
        delete window.closeModal;
    };
}

// ===== RISK MANAGEMENT FUNCTIONS =====

async function setGlobalStopLoss() {
    const stopLossPercent = prompt('Enter global stop loss percentage (e.g., 5 for 5%):');
    if (!stopLossPercent) return;

    try {
        const response = await fetch('/api/trading/global_stop_loss', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ stop_loss_percent: parseFloat(stopLossPercent) })
        });

        const result = await response.json();
        if (result.success) {
            showNotification(`✅ Global stop loss set to ${stopLossPercent}%`, 'success');
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showNotification(`❌ Failed to set global stop loss: ${error.message}`, 'error');
    }
}

async function emergencyCloseAll() {
    const confirmed = await showConfirmation(
        'Emergency Close All',
        'This will close ALL open positions immediately. Are you sure?'
    );

    if (!confirmed) return;

    try {
        const response = await fetch('/api/trading/emergency_close_all', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        const result = await response.json();
        if (result.success) {
            showNotification('🚨 All positions closed successfully', 'success');
            refreshPortfolioData();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showNotification(`❌ Emergency close failed: ${error.message}`, 'error');
    }
}

function showRiskSettings() {
    // Implementation for risk settings modal
    console.log('🛡️ Opening risk settings modal');
    // TODO: Implement risk settings modal
}

// ===== MISSING FUNCTIONS FOR HTML TEMPLATE =====

function switchTab(tabName) {
    console.log(`🔄 Switching to ${tabName} tab`);

    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content
    const selectedTab = document.getElementById(`${tabName}-tab`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to clicked button
    const clickedButton = event.target;
    if (clickedButton) {
        clickedButton.classList.add('active');
    }
}

function placeMarketOrder() {
    console.log('📈 Placing market order from form');

    // Get form values
    const symbol = document.getElementById('market-symbol')?.value;
    const amount = parseFloat(document.getElementById('market-amount')?.value);

    // Get selected order type (buy/sell)
    const activeOrderType = document.querySelector('.order-type-btn.active');
    const side = activeOrderType?.dataset.type || 'buy';

    if (!symbol || !amount || amount <= 0) {
        showNotification('Please enter valid symbol and amount', 'error');
        return;
    }

    // Use default exchange (HTX) for now
    const exchange = 'HTX';

    // Call the existing function with parameters
    placeMarketOrderWithParams(exchange, symbol, side, amount, 'Manual');
}

function placeLimitOrder() {
    console.log('📊 Placing limit order from form');

    // Get form values
    const symbol = document.getElementById('limit-symbol')?.value;
    const price = parseFloat(document.getElementById('limit-price')?.value);
    const amount = parseFloat(document.getElementById('limit-amount')?.value);

    // Get selected order type (buy/sell)
    const activeOrderType = document.querySelector('#limit-tab .order-type-btn.active');
    const side = activeOrderType?.dataset.type || 'buy';

    if (!symbol || !price || !amount || price <= 0 || amount <= 0) {
        showNotification('Please enter valid symbol, price, and amount', 'error');
        return;
    }

    // Use default exchange (HTX) for now
    const exchange = 'HTX';

    // Call the existing function with parameters
    placeLimitOrderWithParams(exchange, symbol, side, amount, price, 'Manual');
}

// Rename existing functions to avoid conflicts
async function placeMarketOrderWithParams(exchange, symbol, side, amount, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/market_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Market order placed: ${side} ${amount} ${symbol}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing market order:', error);
        showNotification('Error placing market order', 'error');
        return null;
    }
}

async function placeLimitOrderWithParams(exchange, symbol, side, amount, price, strategy = 'Manual') {
    try {
        const response = await fetch('/api/trading/limit_order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exchange: exchange,
                symbol: symbol,
                side: side,
                amount: amount,
                price: price,
                strategy: strategy
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Limit order placed: ${side} ${amount} ${symbol} @ $${price}`, 'success');
            refreshDashboard();
            return result.order;
        } else {
            showNotification(`Failed to place order: ${result.error}`, 'error');
            return null;
        }
    } catch (error) {
        console.error('Error placing limit order:', error);
        showNotification('Error placing limit order', 'error');
        return null;
    }
}

function closeModal(modalId) {
    console.log(`🔒 Closing modal: ${modalId}`);
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Setup order type button functionality
function setupOrderTypeButtons() {
    const orderTypeButtons = document.querySelectorAll('.order-type-btn');
    orderTypeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from siblings
            const siblings = this.parentNode.querySelectorAll('.order-type-btn');
            siblings.forEach(sibling => sibling.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');
        });
    });
}

async function handleTradingForm(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const orderData = {
        exchange: formData.get('exchange'),
        symbol: formData.get('symbol'),
        side: formData.get('side'),
        amount: parseFloat(formData.get('amount')),
        order_type: formData.get('order_type'),
        price: formData.get('price') ? parseFloat(formData.get('price')) : null
    };

    // Validate form data
    if (!orderData.exchange || !orderData.symbol || !orderData.side || !orderData.amount || !orderData.order_type) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    if (orderData.order_type === 'limit' && !orderData.price) {
        showNotification('Price is required for limit orders', 'error');
        return;
    }

    try {
        let result;

        if (orderData.order_type === 'market') {
            result = await placeMarketOrder(
                orderData.exchange,
                orderData.symbol,
                orderData.side,
                orderData.amount
            );
        } else if (orderData.order_type === 'limit') {
            result = await placeLimitOrder(
                orderData.exchange,
                orderData.symbol,
                orderData.side,
                orderData.amount,
                orderData.price
            );
        }

        if (result) {
            // Reset form on success
            event.target.reset();
        }

    } catch (error) {
        console.error('Trading form error:', error);
        showNotification('Error placing order', 'error');
    }
}

function showQuickTrade() {
    // Quick trade functionality - could open a modal with preset amounts
    showNotification('Quick trade feature coming soon!', 'info');
}
