#!/usr/bin/env python3
"""
Test Smart Strategy Signal Generation

Quick test to see if we can generate signals with current market data.
"""

import asyncio
import logging
from datetime import datetime
from backtester.strategies import smart_model_integrated_strategy

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_signal_generation():
    """Test signal generation with current market data."""
    logger.info("🧪 Testing Smart Strategy Signal Generation...")
    
    try:
        # Try to generate a signal
        signals = await smart_model_integrated_strategy(
            timestamp=datetime.now(),
            symbols=["BTC-USDT"],
            signal_source="test"
        )
        
        if signals:
            signal = signals[0]
            logger.info(f"✅ Signal generated!")
            logger.info(f"   Action: {signal.action}")
            logger.info(f"   Symbol: {signal.symbol}")
            logger.info(f"   Score: {signal.score:.3f}")
            logger.info(f"   Rationale: {signal.rationale}")
        else:
            logger.warning("⚠️  No signals generated")
            
    except Exception as e:
        logger.error(f"❌ Error generating signal: {e}")

if __name__ == "__main__":
    asyncio.run(test_signal_generation())
