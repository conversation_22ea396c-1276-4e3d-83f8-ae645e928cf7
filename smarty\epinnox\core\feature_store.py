"""
Feature store for the Epinnox trading system.
Provides in-memory storage with optional Redis caching.

Enhanced for CCXT integration and multi-exchange support.
"""

import asyncio
import json
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Tuple

logger = logging.getLogger(__name__)


class FeatureStore:
    """
    In-memory feature store with optional Redis caching.

    This class provides a centralized store for all features and data
    used by the trading system. It supports namespaced keys and
    time-series data.
    
    Enhanced for CCXT multi-exchange support.
    """

    def __init__(self, use_redis: bool = False, redis_url: str = "redis://localhost:6379/0"):
        """
        Initialize the feature store.

        Args:
            use_redis: Whether to use Redis as a cache
            redis_url: Redis connection URL
        """
        self._store: Dict[str, Dict[str, Any]] = {}
        self._time_series: Dict[str, List[Tuple[datetime, Any]]] = {}
        self._use_redis = use_redis
        self._redis_client = None

        # Use a thread-safe lock instead of asyncio.Lock to avoid event loop issues
        self._lock = threading.Lock()

        if use_redis:
            try:
                import redis
                self._redis_client = redis.from_url(redis_url)
                logger.info(f"Connected to Redis at {redis_url}")
            except ImportError:
                logger.warning("Redis package not installed. Running without Redis cache.")
                self._use_redis = False
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self._use_redis = False

    async def _get_lock(self):
        """
        Get the thread-safe lock.

        Returns:
            The threading.Lock instance
        """
        return self._lock

    async def set(self, symbol: str, key: str, value: Any, exchange: str = "default") -> None:
        """
        Set a value in the feature store.

        Args:
            symbol: The trading symbol (e.g., "BTC-USDT")
            key: The feature key (can be namespaced with dots, e.g., "ml.rsi.value")
            value: The value to store
            exchange: Exchange identifier for multi-exchange support
        """
        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            # Create exchange-specific namespace
            store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
            
            if store_key not in self._store:
                self._store[store_key] = {}

            self._store[store_key][key] = value

            # Store in Redis if enabled
            if self._use_redis and self._redis_client:
                redis_key = f"{store_key}:{key}"
                try:
                    self._redis_client.set(redis_key, json.dumps(value))
                except Exception as e:
                    logger.error(f"Failed to set value in Redis: {e}")

    async def get(self, symbol: str, key: str, default: Any = None, exchange: str = "default") -> Any:
        """
        Get a value from the feature store.

        Args:
            symbol: The trading symbol
            key: The feature key
            default: Default value if key doesn't exist
            exchange: Exchange identifier for multi-exchange support

        Returns:
            The stored value or default
        """
        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            # Create exchange-specific namespace
            store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
            
            if store_key not in self._store or key not in self._store[store_key]:
                # Try to get from Redis if enabled
                if self._use_redis and self._redis_client:
                    redis_key = f"{store_key}:{key}"
                    try:
                        value = self._redis_client.get(redis_key)
                        if value:
                            return json.loads(value)
                    except Exception as e:
                        logger.error(f"Failed to get value from Redis: {e}")

                return default

            return self._store[store_key][key]

    async def add_time_series(self, symbol: str, key: str, value: Any, timestamp: Optional[datetime] = None, exchange: str = "default") -> None:
        """
        Add a value to a time series.

        Args:
            symbol: The trading symbol
            key: The feature key
            value: The value to store
            timestamp: The timestamp (defaults to now)
            exchange: Exchange identifier for multi-exchange support
        """
        if timestamp is None:
            timestamp = datetime.now()

        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            # Create exchange-specific namespace
            store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
            series_key = f"{store_key}:{key}"
            
            if series_key not in self._time_series:
                self._time_series[series_key] = []

            self._time_series[series_key].append((timestamp, value))

            # Limit the size of the time series to prevent memory issues
            max_size = 10000  # Configurable
            if len(self._time_series[series_key]) > max_size:
                self._time_series[series_key] = self._time_series[series_key][-max_size:]

    async def get_time_series(
        self,
        symbol: str,
        key: str,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        limit: Optional[int] = None,
        exchange: str = "default"
    ) -> List[Tuple[datetime, Any]]:
        """
        Get values from a time series within a time range.

        Args:
            symbol: The trading symbol
            key: The feature key
            start: Start time (inclusive)
            end: End time (inclusive)
            limit: Maximum number of items to return (most recent first)
            exchange: Exchange identifier for multi-exchange support

        Returns:
            List of (timestamp, value) tuples
        """
        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            # Create exchange-specific namespace
            store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
            series_key = f"{store_key}:{key}"
            
            if series_key not in self._time_series:
                return []

            result = self._time_series[series_key]

            if start:
                # Convert start to datetime if it's not already
                if not isinstance(start, datetime):
                    if isinstance(start, (int, float)):
                        start = datetime.fromtimestamp(start)
                    else:
                        try:
                            start = datetime.fromisoformat(str(start))
                        except (ValueError, TypeError):
                            # If conversion fails, use a very old date
                            start = datetime(1970, 1, 1)

                result = [item for item in result if item[0] >= start]

            if end:
                # Convert end to datetime if it's not already
                if not isinstance(end, datetime):
                    if isinstance(end, (int, float)):
                        end = datetime.fromtimestamp(end)
                    else:
                        try:
                            end = datetime.fromisoformat(str(end))
                        except (ValueError, TypeError):
                            # If conversion fails, use a future date
                            end = datetime(2100, 1, 1)

                result = [item for item in result if item[0] <= end]

            # Sort by timestamp (newest first)
            result = sorted(result, key=lambda x: x[0], reverse=True)

            # Apply limit if specified
            if limit and limit > 0:
                result = result[:limit]

            # Return in chronological order
            return sorted(result, key=lambda x: x[0])

    async def get_snapshot(self, symbol: str, exchange: str = "default") -> Dict[str, Any]:
        """
        Get a snapshot of all features for a symbol.

        Args:
            symbol: The trading symbol
            exchange: Exchange identifier for multi-exchange support

        Returns:
            Dictionary of all features
        """
        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            # Create exchange-specific namespace
            store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
            
            if store_key not in self._store:
                return {}

            return self._store[store_key].copy()

    async def clear(self, symbol: Optional[str] = None, exchange: str = "default") -> None:
        """
        Clear the feature store.

        Args:
            symbol: If provided, only clear data for this symbol
            exchange: Exchange identifier for multi-exchange support
        """
        lock = await self._get_lock()
        with lock:  # Use with instead of async with for threading.Lock
            if symbol:
                # Create exchange-specific namespace
                store_key = f"{exchange}:{symbol}" if exchange != "default" else symbol
                
                if store_key in self._store:
                    del self._store[store_key]

                # Clear time series for this symbol
                keys_to_delete = [k for k in self._time_series if k.startswith(f"{store_key}:")]
                for key in keys_to_delete:
                    del self._time_series[key]

                # Clear from Redis if enabled
                if self._use_redis and self._redis_client:
                    try:
                        redis_keys = self._redis_client.keys(f"{store_key}:*")
                        if redis_keys:
                            self._redis_client.delete(*redis_keys)
                    except Exception as e:
                        logger.error(f"Failed to clear Redis keys: {e}")
            else:
                self._store.clear()
                self._time_series.clear()

                # Clear all from Redis if enabled
                if self._use_redis and self._redis_client:
                    try:
                        self._redis_client.flushdb()
                    except Exception as e:
                        logger.error(f"Failed to flush Redis DB: {e}")


# Global instance for convenience
feature_store = FeatureStore()
