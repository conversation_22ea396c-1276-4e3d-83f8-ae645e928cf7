#!/usr/bin/env python3
"""
Monitor Real-Time Chart Data Flow
Simple monitoring script to check if live data is flowing for the price chart.
"""

import sqlite3
import time
import json
from datetime import datetime

def monitor_data_flow():
    """Monitor the live data flow for chart testing."""
    print("🔄 MONITORING LIVE DATA FLOW FOR PRICE CHART")
    print("Press Ctrl+C to stop")
    print("=" * 60)
    
    conn = sqlite3.connect("data/bus.db")
    last_count = 0
    
    try:
        while True:
            cursor = conn.cursor()
            
            # Get total message count
            cursor.execute("SELECT COUNT(*) FROM messages")
            total_count = cursor.fetchone()[0]
            
            # Get latest BTC data
            cursor.execute("""
                SELECT stream, ts, payload FROM messages 
                WHERE stream LIKE 'market.BTC-USDT.%' 
                ORDER BY ts DESC LIMIT 1
            """)
            latest = cursor.fetchone()
            
            if latest:
                stream, ts, payload = latest
                data_time = datetime.fromtimestamp(ts)
                age = (datetime.now() - data_time).total_seconds()
                
                new_messages = total_count - last_count
                
                # Determine status
                if age < 30:
                    status = "🟢 LIVE"
                elif age < 300:
                    status = "🟡 STALE"
                else:
                    status = "🔴 OLD"
                
                # Try to extract price
                try:
                    data = json.loads(payload)
                    tick = data.get('tick', {})
                    
                    if 'bids' in tick and tick['bids']:
                        price = f"${tick['bids'][0][0]:,.2f}"
                    elif 'close' in tick:
                        price = f"${tick['close']:,.2f}"
                    else:
                        price = "N/A"
                except:
                    price = "Parse Error"
                
                print(f"{datetime.now().strftime('%H:%M:%S')} | {status} | "
                      f"Total: {total_count:,} (+{new_messages}) | "
                      f"Latest: {age:.1f}s ago | "
                      f"Price: {price} | "
                      f"{stream}")
                
                last_count = total_count
            else:
                print(f"{datetime.now().strftime('%H:%M:%S')} | 🔴 NO DATA | Total: {total_count:,}")
            
            time.sleep(3)
            
    except KeyboardInterrupt:
        print("\n✅ Monitoring stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    monitor_data_flow()
