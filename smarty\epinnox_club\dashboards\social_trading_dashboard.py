"""
Social Trading Dashboard
Strategy sharing, copying, and community features for Money Circle members
"""

import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from aiohttp import web
import aiohttp_jinja2

logger = logging.getLogger(__name__)

class SocialTradingDashboard:
    def __init__(self, db_manager, live_trading_interface, strategy_engine):
        self.db_manager = db_manager
        self.live_trading = live_trading_interface
        self.strategy_engine = strategy_engine
        logger.info("[SOCIAL] Social Trading Dashboard initialized")

    async def dashboard_page(self, request: web.Request) -> web.Response:
        """Serve the social trading dashboard page with enhanced error handling."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Check role-based access (members and admins only)
            user_role = user.get('role', 'viewer')
            if user_role not in ['member', 'admin']:
                return web.Response(status=302, headers={'Location': '/dashboard'})

            # Get social trading data with error handling
            try:
                social_data = await self._get_social_trading_data(user.get('user_id'))
            except Exception as data_error:
                logger.error(f"[SOCIAL] Error getting social trading data: {data_error}")
                # Provide fallback data
                social_data = self._get_fallback_social_data()

            # Render the social trading dashboard
            context = {
                'user': user,
                'social_data': json.dumps(social_data),
                'current_path': '/social-trading',
                'page_title': 'Social Trading Hub'
            }

            try:
                return aiohttp_jinja2.render_template('social_trading_dashboard.html', request, context)
            except Exception as template_error:
                logger.error(f"[SOCIAL] Template rendering error: {template_error}")
                # Return error page or redirect
                return web.Response(
                    text=f"Social Trading Dashboard temporarily unavailable. Error: {template_error}",
                    status=500,
                    content_type='text/html'
                )

        except Exception as e:
            logger.error(f"Social trading dashboard error: {e}")
            return web.Response(status=500, text="Internal server error")

    def _get_fallback_social_data(self) -> Dict[str, Any]:
        """Get fallback social trading data when database is unavailable."""
        return {
            'top_performers': [
                {
                    'rank': 1,
                    'username': 'crypto_master',
                    'total_return': 24.5,
                    'followers': 156
                },
                {
                    'rank': 2,
                    'username': 'trend_trader',
                    'total_return': 18.7,
                    'followers': 89
                }
            ],
            'followed_strategies': [],
            'shared_strategies': [],
            'community_activity': [
                {
                    'type': 'strategy_shared',
                    'username': 'crypto_master',
                    'content': 'Momentum Scalping Strategy',
                    'timestamp': datetime.now().isoformat(),
                    'icon': '📈'
                }
            ],
            'strategy_leaderboard': [
                {
                    'title': 'DOGE Momentum Strategy',
                    'creator': 'crypto_master',
                    'followers': 45,
                    'return_rate': 15.2
                }
            ],
            'social_metrics': {
                'followers': 0,
                'following': 0,
                'strategies_shared': 0,
                'reputation_score': 0
            },
            'discussion_threads': [],
            'last_updated': datetime.now().isoformat(),
            'data_source': 'fallback'
        }

    async def _get_social_trading_data(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive social trading data."""
        try:
            # Use dedicated database connection
            conn = sqlite3.connect('data/money_circle.db')
            conn.row_factory = sqlite3.Row

            # Get top performers
            top_performers = self._get_top_performers(conn)

            # Get user's followed strategies
            followed_strategies = self._get_followed_strategies(conn, user_id)

            # Get user's shared strategies
            shared_strategies = self._get_user_shared_strategies(conn, user_id)

            # Get community activity
            community_activity = self._get_community_activity(conn)

            # Get strategy leaderboard
            strategy_leaderboard = self._get_strategy_leaderboard(conn)

            # Get social metrics
            social_metrics = self._get_social_metrics(conn, user_id)

            # Get discussion threads
            discussion_threads = self._get_discussion_threads(conn)

            return {
                'top_performers': top_performers,
                'followed_strategies': followed_strategies,
                'shared_strategies': shared_strategies,
                'community_activity': community_activity,
                'strategy_leaderboard': strategy_leaderboard,
                'social_metrics': social_metrics,
                'discussion_threads': discussion_threads,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting social trading data: {e}")
            return {
                'top_performers': [],
                'followed_strategies': [],
                'shared_strategies': [],
                'community_activity': [],
                'strategy_leaderboard': [],
                'social_metrics': {},
                'discussion_threads': [],
                'last_updated': datetime.now().isoformat()
            }
        finally:
            if conn:
                conn.close()

    def _get_top_performers(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get top performing traders in the community."""
        try:
            cursor = conn.execute("""
                SELECT
                    u.username, u.id as user_id,
                    COUNT(DISTINCT sp.id) as strategies_shared,
                    COUNT(DISTINCT sf.follower_id) as followers,
                    AVG(sp.expected_return) as avg_return,
                    SUM(CASE WHEN ut.pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    COUNT(ut.id) as total_trades
                FROM users u
                LEFT JOIN strategy_proposals sp ON u.id = sp.creator_id
                LEFT JOIN strategy_following sf ON sp.id = sf.strategy_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id AND ut.timestamp >= date('now', '-30 days')
                WHERE u.role IN ('member', 'admin')
                GROUP BY u.id, u.username
                HAVING total_trades > 0
                ORDER BY avg_return DESC, followers DESC
                LIMIT 10
            """, ())

            performers = []
            for row in cursor.fetchall():
                win_rate = (row['winning_trades'] / row['total_trades']) * 100 if row['total_trades'] > 0 else 0

                performers.append({
                    'user_id': row['user_id'],
                    'username': row['username'],
                    'strategies_shared': row['strategies_shared'] or 0,
                    'followers': row['followers'] or 0,
                    'avg_return': round(row['avg_return'] or 0, 2),
                    'win_rate': round(win_rate, 1),
                    'total_trades': row['total_trades'] or 0,
                    'rank': len(performers) + 1
                })

            return performers

        except sqlite3.OperationalError:
            logger.warning("Social trading tables not found")
            # Return sample data for demonstration
            return [
                {
                    'user_id': 1,
                    'username': 'crypto_master',
                    'strategies_shared': 5,
                    'followers': 23,
                    'avg_return': 15.8,
                    'win_rate': 72.5,
                    'total_trades': 45,
                    'rank': 1
                },
                {
                    'user_id': 2,
                    'username': 'trend_trader',
                    'strategies_shared': 3,
                    'followers': 18,
                    'avg_return': 12.4,
                    'win_rate': 68.2,
                    'total_trades': 38,
                    'rank': 2
                }
            ]

    def _get_followed_strategies(self, conn: sqlite3.Connection, user_id: int) -> List[Dict[str, Any]]:
        """Get strategies that user is following."""
        try:
            cursor = conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.expected_return,
                    sp.risk_level, sp.strategy_type, sp.created_at,
                    u.username as creator_name,
                    sf.created_at as followed_since,
                    sf.auto_copy_enabled
                FROM strategy_following sf
                JOIN strategy_proposals sp ON sf.strategy_id = sp.id
                JOIN users u ON sp.creator_id = u.id
                WHERE sf.follower_id = ? AND sf.is_active = TRUE
                ORDER BY sf.created_at DESC
            """, (user_id,))

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row['id'],
                    'title': row['title'],
                    'description': row['description'],
                    'expected_return': row['expected_return'],
                    'risk_level': row['risk_level'],
                    'strategy_type': row['strategy_type'],
                    'creator_name': row['creator_name'],
                    'followed_since': row['followed_since'],
                    'auto_copy_enabled': bool(row['auto_copy_enabled']),
                    'created_at': row['created_at']
                })

            return strategies

        except sqlite3.OperationalError:
            logger.warning("Strategy following tables not found")
            return []

    def _get_user_shared_strategies(self, conn: sqlite3.Connection, user_id: int) -> List[Dict[str, Any]]:
        """Get strategies shared by the user."""
        try:
            cursor = conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.expected_return,
                    sp.risk_level, sp.strategy_type, sp.status, sp.created_at,
                    COUNT(DISTINCT sf.follower_id) as followers,
                    AVG(CASE WHEN ut.pnl IS NOT NULL THEN ut.pnl ELSE 0 END) as avg_performance
                FROM strategy_proposals sp
                LEFT JOIN strategy_following sf ON sp.id = sf.strategy_id AND sf.is_active = TRUE
                LEFT JOIN user_trades ut ON sp.creator_id = ut.user_id AND ut.strategy_name = sp.title
                WHERE sp.creator_id = ?
                GROUP BY sp.id
                ORDER BY sp.created_at DESC
            """, (user_id,))

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row['id'],
                    'title': row['title'],
                    'description': row['description'],
                    'expected_return': row['expected_return'],
                    'risk_level': row['risk_level'],
                    'strategy_type': row['strategy_type'],
                    'status': row['status'],
                    'followers': row['followers'] or 0,
                    'avg_performance': round(row['avg_performance'] or 0, 2),
                    'created_at': row['created_at']
                })

            return strategies

        except sqlite3.OperationalError:
            logger.warning("Strategy proposals tables not found")
            return []

    def _get_community_activity(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get recent community activity."""
        try:
            # Combine different types of activities
            activities = []

            # Recent strategy shares
            cursor = conn.execute("""
                SELECT
                    'strategy_shared' as activity_type,
                    u.username, sp.title as content,
                    sp.created_at as timestamp
                FROM strategy_proposals sp
                JOIN users u ON sp.creator_id = u.id
                WHERE sp.created_at >= date('now', '-7 days')
                ORDER BY sp.created_at DESC
                LIMIT 10
            """, ())

            for row in cursor.fetchall():
                activities.append({
                    'type': row['activity_type'],
                    'username': row['username'],
                    'content': row['content'],
                    'timestamp': row['timestamp'],
                    'icon': '📈'
                })

            # Recent follows
            cursor = conn.execute("""
                SELECT
                    'strategy_followed' as activity_type,
                    u1.username as follower, u2.username as creator,
                    sp.title as strategy_title,
                    sf.created_at as timestamp
                FROM strategy_following sf
                JOIN users u1 ON sf.follower_id = u1.id
                JOIN strategy_proposals sp ON sf.strategy_id = sp.id
                JOIN users u2 ON sp.creator_id = u2.id
                WHERE sf.created_at >= date('now', '-7 days')
                ORDER BY sf.created_at DESC
                LIMIT 10
            """, ())

            for row in cursor.fetchall():
                activities.append({
                    'type': row['activity_type'],
                    'username': row['follower'],
                    'content': f"followed {row['creator']}'s strategy: {row['strategy_title']}",
                    'timestamp': row['timestamp'],
                    'icon': '👥'
                })

            # Sort by timestamp and return latest 20
            activities.sort(key=lambda x: x['timestamp'], reverse=True)
            return activities[:20]

        except sqlite3.OperationalError:
            logger.warning("Community activity tables not found")
            return [
                {
                    'type': 'strategy_shared',
                    'username': 'crypto_master',
                    'content': 'Momentum Scalping Strategy',
                    'timestamp': datetime.now().isoformat(),
                    'icon': '📈'
                },
                {
                    'type': 'strategy_followed',
                    'username': 'trend_trader',
                    'content': "followed crypto_master's strategy: DCA Bitcoin",
                    'timestamp': (datetime.now() - timedelta(hours=2)).isoformat(),
                    'icon': '👥'
                }
            ]

    def _get_strategy_leaderboard(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get strategy performance leaderboard."""
        try:
            cursor = conn.execute("""
                SELECT
                    sp.id, sp.title, sp.expected_return, sp.risk_level,
                    u.username as creator_name,
                    COUNT(DISTINCT sf.follower_id) as followers,
                    AVG(ut.pnl) as avg_performance,
                    COUNT(ut.id) as total_trades
                FROM strategy_proposals sp
                JOIN users u ON sp.creator_id = u.id
                LEFT JOIN strategy_following sf ON sp.id = sf.strategy_id AND sf.is_active = TRUE
                LEFT JOIN user_trades ut ON sp.creator_id = ut.user_id AND ut.strategy_name = sp.title
                WHERE sp.status = 'approved'
                GROUP BY sp.id
                HAVING total_trades > 0
                ORDER BY avg_performance DESC, followers DESC
                LIMIT 15
            """, ())

            leaderboard = []
            for i, row in enumerate(cursor.fetchall()):
                leaderboard.append({
                    'rank': i + 1,
                    'id': row['id'],
                    'title': row['title'],
                    'creator_name': row['creator_name'],
                    'expected_return': row['expected_return'],
                    'risk_level': row['risk_level'],
                    'followers': row['followers'] or 0,
                    'avg_performance': round(row['avg_performance'] or 0, 2),
                    'total_trades': row['total_trades'] or 0
                })

            return leaderboard

        except sqlite3.OperationalError:
            logger.warning("Strategy leaderboard tables not found")
            return []

    def _get_social_metrics(self, conn: sqlite3.Connection, user_id: int) -> Dict[str, Any]:
        """Get user's social trading metrics."""
        try:
            # Get follower count
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT sf.follower_id) as followers
                FROM strategy_proposals sp
                JOIN strategy_following sf ON sp.id = sf.strategy_id
                WHERE sp.creator_id = ? AND sf.is_active = TRUE
            """, (user_id,))

            followers = cursor.fetchone()['followers'] or 0

            # Get following count
            cursor = conn.execute("""
                SELECT COUNT(*) as following
                FROM strategy_following
                WHERE follower_id = ? AND is_active = TRUE
            """, (user_id,))

            following = cursor.fetchone()['following'] or 0

            # Get strategies shared
            cursor = conn.execute("""
                SELECT COUNT(*) as strategies_shared
                FROM strategy_proposals
                WHERE creator_id = ?
            """, (user_id,))

            strategies_shared = cursor.fetchone()['strategies_shared'] or 0

            return {
                'followers': followers,
                'following': following,
                'strategies_shared': strategies_shared,
                'reputation_score': min(100, (followers * 2) + (strategies_shared * 5))
            }

        except sqlite3.OperationalError:
            logger.warning("Social metrics tables not found")
            return {
                'followers': 0,
                'following': 0,
                'strategies_shared': 0,
                'reputation_score': 0
            }

    def _get_discussion_threads(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get recent discussion threads."""
        try:
            # This would integrate with a forum/discussion system
            # For now, return sample data
            return [
                {
                    'id': 1,
                    'title': 'Best strategies for volatile markets?',
                    'author': 'crypto_newbie',
                    'replies': 12,
                    'last_activity': datetime.now().isoformat(),
                    'category': 'Strategy Discussion'
                },
                {
                    'id': 2,
                    'title': 'Risk management techniques',
                    'author': 'risk_manager',
                    'replies': 8,
                    'last_activity': (datetime.now() - timedelta(hours=3)).isoformat(),
                    'category': 'Risk Management'
                }
            ]

        except Exception as e:
            logger.warning(f"Discussion threads error: {e}")
            return []

    # API endpoints for social trading actions
    async def api_follow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to follow a strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Not authenticated'}, status=401)

            data = await request.json()
            strategy_id = data.get('strategy_id')
            auto_copy = data.get('auto_copy', False)

            # TODO: Implement strategy following logic
            return web.json_response({'success': True, 'message': 'Strategy followed'})

        except Exception as e:
            logger.error(f"Follow strategy API error: {e}")
            return web.json_response({'error': 'Failed to follow strategy'}, status=500)

    async def api_unfollow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to unfollow a strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Not authenticated'}, status=401)

            strategy_id = request.match_info['strategy_id']

            # TODO: Implement strategy unfollowing logic
            return web.json_response({'success': True, 'message': 'Strategy unfollowed'})

        except Exception as e:
            logger.error(f"Unfollow strategy API error: {e}")
            return web.json_response({'error': 'Failed to unfollow strategy'}, status=500)

    async def api_share_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to share a strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Not authenticated'}, status=401)

            data = await request.json()

            # TODO: Implement strategy sharing logic
            return web.json_response({'success': True, 'message': 'Strategy shared'})

        except Exception as e:
            logger.error(f"Share strategy API error: {e}")
            return web.json_response({'error': 'Failed to share strategy'}, status=500)
