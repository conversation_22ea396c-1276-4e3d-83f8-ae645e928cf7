#!/usr/bin/env python3
"""
Money Circle Startup Script
Quick startup script to launch the Money Circle platform.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'aiohttp',
        'aiohttp_cors', 
        'aiohttp_session',
        'aiohttp_jinja2',
        'cryptography',
        'bcrypt',
        'ccxt'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error("❌ Missing required packages:")
        for package in missing_packages:
            logger.error(f"   - {package}")
        logger.error("\n💡 Install missing packages with:")
        logger.error("   pip install -r requirements.txt")
        return False
    
    logger.info("✅ All required dependencies are installed")
    return True

def setup_directories():
    """Create required directories."""
    directories = [
        'data',
        'logs',
        'static/css',
        'static/js',
        'static/images',
        'templates'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ Required directories created")

def initialize_database():
    """Initialize the database with required tables."""
    try:
        from database.models import DatabaseManager
        
        db_path = "data/money_circle.db"
        db_manager = DatabaseManager(db_path)
        logger.info("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False

def create_default_admin():
    """Create default admin user if none exists."""
    try:
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        
        db_manager = DatabaseManager("data/money_circle.db")
        user_manager = UserManager(db_manager)
        
        # The UserManager automatically creates default admin in __init__
        logger.info("✅ Default admin user ready (epinnox/securepass123)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create default admin: {e}")
        return False

async def start_application():
    """Start the Money Circle application."""
    try:
        from app import MoneyCircleApp
        
        # Create and start the application
        app = MoneyCircleApp('development')
        await app.start_server()
        
    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        raise

def main():
    """Main startup function."""
    logger.info("🚀 Starting Money Circle Investment Club Platform...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Initialize database
    if not initialize_database():
        sys.exit(1)
    
    # Create default admin
    if not create_default_admin():
        sys.exit(1)
    
    # Start the application
    logger.info("🌐 Launching Money Circle server...")
    logger.info("📍 Platform will be available at: http://localhost:8084")
    logger.info("🔐 Default login: epinnox / securepass123")
    logger.info("🎯 Press Ctrl+C to stop the server")
    
    try:
        asyncio.run(start_application())
    except KeyboardInterrupt:
        logger.info("🛑 Money Circle server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
