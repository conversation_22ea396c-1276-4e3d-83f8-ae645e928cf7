#!/usr/bin/env python3
"""
Smart Trader System Launcher

This script starts the complete Smart Trader system including:
- Market Data Feeds (HTX/Binance fallback)
- Web Dashboard (localhost:8081)
- AI Trading Strategy (DataFrame-based)
- Real-time Signal Generation

Usage:
    python start_smart_trader.py [--help]
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_trader_main import main

if __name__ == "__main__":
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎯 SMART TRADER SYSTEM                    ║
    ║                  AI-Powered Live Trading                     ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  🚀 Starting complete trading system...                      ║
    ║  🌐 Dashboard: http://localhost:8082                         ║
    ║  📊 Live data feeds: Automatic                               ║
    ║  🧠 AI strategy: DataFrame-based analysis                    ║
    ║  💰 Trading: Configurable (simulation/live)                 ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Smart Trader stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
