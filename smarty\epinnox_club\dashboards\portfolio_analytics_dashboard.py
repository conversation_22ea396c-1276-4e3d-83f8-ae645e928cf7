"""
Advanced Portfolio Analytics Dashboard
Comprehensive portfolio analysis and performance tracking for Money Circle members
"""

import json
import sqlite3
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from aiohttp import web
import aiohttp_jinja2

logger = logging.getLogger(__name__)

class PortfolioAnalyticsDashboard:
    def __init__(self, db_manager, market_data_manager, live_trading_interface):
        self.db_manager = db_manager
        self.market_data = market_data_manager
        self.live_trading = live_trading_interface
        logger.info("[PORTFOLIO] Advanced Portfolio Analytics Dashboard initialized")

    async def dashboard_page(self, request: web.Request) -> web.Response:
        """Serve the portfolio analytics dashboard page."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})
            
            # Check role-based access (members and admins only)
            user_role = user.get('role', 'viewer')
            if user_role not in ['member', 'admin']:
                return web.Response(status=302, headers={'Location': '/dashboard'})
            
            # Get comprehensive portfolio analytics data
            portfolio_data = await self._get_portfolio_analytics_data(user.get('user_id'))
            
            # Render the portfolio analytics dashboard
            context = {
                'user': user,
                'portfolio_data': json.dumps(portfolio_data),
                'current_path': '/portfolio-analytics'
            }
            
            return aiohttp_jinja2.render_template('portfolio_analytics_dashboard.html', request, context)
            
        except Exception as e:
            logger.error(f"Portfolio analytics dashboard error: {e}")
            return web.Response(status=500, text="Internal server error")

    async def _get_portfolio_analytics_data(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive portfolio analytics data."""
        try:
            # Use dedicated database connection
            conn = sqlite3.connect('data/money_circle.db')
            conn.row_factory = sqlite3.Row
            
            # Get portfolio overview
            portfolio_overview = self._get_portfolio_overview(conn, user_id)
            
            # Get performance metrics
            performance_metrics = self._get_performance_metrics(conn, user_id)
            
            # Get risk analysis
            risk_analysis = self._get_risk_analysis(conn, user_id)
            
            # Get asset allocation
            asset_allocation = self._get_asset_allocation(conn, user_id)
            
            # Get trading history analysis
            trading_history = self._get_trading_history_analysis(conn, user_id)
            
            # Get benchmark comparison
            benchmark_comparison = self._get_benchmark_comparison(conn, user_id)
            
            # Get optimization suggestions
            optimization_suggestions = self._get_optimization_suggestions(conn, user_id)
            
            # Get real-time market data
            market_overview = self._get_market_overview()
            
            return {
                'portfolio_overview': portfolio_overview,
                'performance_metrics': performance_metrics,
                'risk_analysis': risk_analysis,
                'asset_allocation': asset_allocation,
                'trading_history': trading_history,
                'benchmark_comparison': benchmark_comparison,
                'optimization_suggestions': optimization_suggestions,
                'market_overview': market_overview,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio analytics data: {e}")
            return {
                'portfolio_overview': {},
                'performance_metrics': {},
                'risk_analysis': {},
                'asset_allocation': [],
                'trading_history': [],
                'benchmark_comparison': {},
                'optimization_suggestions': [],
                'market_overview': {},
                'last_updated': datetime.now().isoformat()
            }
        finally:
            if conn:
                conn.close()

    def _get_portfolio_overview(self, conn: sqlite3.Connection, user_id: int) -> Dict[str, Any]:
        """Get portfolio overview with current values and P&L."""
        try:
            # Get current portfolio value
            cursor = conn.execute("""
                SELECT 
                    SUM(CASE WHEN side = 'long' THEN size * current_price ELSE -size * current_price END) as total_value,
                    SUM(unrealized_pnl) as unrealized_pnl,
                    COUNT(*) as open_positions
                FROM user_positions
                WHERE user_id = ? AND status = 'open'
            """, (user_id,))
            
            portfolio_data = cursor.fetchone()
            
            # Get realized P&L for the period
            cursor = conn.execute("""
                SELECT 
                    SUM(pnl) as realized_pnl,
                    COUNT(*) as total_trades
                FROM user_trades
                WHERE user_id = ? AND timestamp >= date('now', '-30 days')
            """, (user_id,))
            
            trading_data = cursor.fetchone()
            
            # Calculate portfolio metrics
            total_value = portfolio_data['total_value'] or 0
            unrealized_pnl = portfolio_data['unrealized_pnl'] or 0
            realized_pnl = trading_data['realized_pnl'] or 0
            total_pnl = unrealized_pnl + realized_pnl
            
            # Get initial investment (simplified calculation)
            initial_investment = 10000.0  # Default starting amount
            
            return {
                'total_value': round(total_value, 2),
                'initial_investment': initial_investment,
                'total_pnl': round(total_pnl, 2),
                'unrealized_pnl': round(unrealized_pnl, 2),
                'realized_pnl': round(realized_pnl, 2),
                'total_return_pct': round((total_pnl / initial_investment) * 100, 2) if initial_investment > 0 else 0,
                'open_positions': portfolio_data['open_positions'] or 0,
                'total_trades': trading_data['total_trades'] or 0
            }
            
        except sqlite3.OperationalError:
            logger.warning("Portfolio tables not found")
            return {
                'total_value': 10000.0,
                'initial_investment': 10000.0,
                'total_pnl': 0.0,
                'unrealized_pnl': 0.0,
                'realized_pnl': 0.0,
                'total_return_pct': 0.0,
                'open_positions': 0,
                'total_trades': 0
            }

    def _get_performance_metrics(self, conn: sqlite3.Connection, user_id: int) -> Dict[str, Any]:
        """Get detailed performance metrics."""
        try:
            # Get daily returns for the last 30 days
            cursor = conn.execute("""
                SELECT 
                    DATE(timestamp) as trade_date,
                    SUM(pnl) as daily_pnl
                FROM user_trades
                WHERE user_id = ? AND timestamp >= date('now', '-30 days')
                GROUP BY DATE(timestamp)
                ORDER BY trade_date
            """, (user_id,))
            
            daily_returns = []
            for row in cursor.fetchall():
                daily_returns.append(row['daily_pnl'])
            
            if len(daily_returns) > 1:
                # Calculate performance metrics
                returns_array = np.array(daily_returns)
                
                # Sharpe ratio (simplified)
                avg_return = np.mean(returns_array)
                std_return = np.std(returns_array)
                sharpe_ratio = (avg_return / std_return) if std_return > 0 else 0
                
                # Maximum drawdown
                cumulative_returns = np.cumsum(returns_array)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdown = cumulative_returns - running_max
                max_drawdown = np.min(drawdown)
                
                # Win rate
                winning_days = len([r for r in daily_returns if r > 0])
                win_rate = (winning_days / len(daily_returns)) * 100
                
                return {
                    'avg_daily_return': round(avg_return, 2),
                    'volatility': round(std_return, 2),
                    'sharpe_ratio': round(sharpe_ratio, 3),
                    'max_drawdown': round(max_drawdown, 2),
                    'win_rate': round(win_rate, 2),
                    'best_day': round(np.max(returns_array), 2),
                    'worst_day': round(np.min(returns_array), 2),
                    'total_trading_days': len(daily_returns)
                }
            else:
                return {
                    'avg_daily_return': 0.0,
                    'volatility': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'win_rate': 0.0,
                    'best_day': 0.0,
                    'worst_day': 0.0,
                    'total_trading_days': 0
                }
                
        except (sqlite3.OperationalError, Exception) as e:
            logger.warning(f"Performance metrics calculation error: {e}")
            return {}

    def _get_risk_analysis(self, conn: sqlite3.Connection, user_id: int) -> Dict[str, Any]:
        """Get risk analysis metrics."""
        try:
            # Get position sizes and concentrations
            cursor = conn.execute("""
                SELECT 
                    symbol,
                    SUM(ABS(size * current_price)) as position_value,
                    COUNT(*) as position_count
                FROM user_positions
                WHERE user_id = ? AND status = 'open'
                GROUP BY symbol
                ORDER BY position_value DESC
            """, (user_id,))
            
            positions = cursor.fetchall()
            
            if positions:
                total_portfolio_value = sum(pos['position_value'] for pos in positions)
                
                # Calculate concentration risk
                largest_position_pct = (positions[0]['position_value'] / total_portfolio_value) * 100 if total_portfolio_value > 0 else 0
                
                # Risk score (simplified)
                risk_score = min(100, largest_position_pct + (len(positions) * 5))
                
                return {
                    'total_positions': len(positions),
                    'largest_position_pct': round(largest_position_pct, 2),
                    'portfolio_concentration': 'High' if largest_position_pct > 30 else 'Medium' if largest_position_pct > 15 else 'Low',
                    'risk_score': round(risk_score, 1),
                    'risk_level': 'High' if risk_score > 70 else 'Medium' if risk_score > 40 else 'Low',
                    'diversification_score': round(100 - largest_position_pct, 1)
                }
            else:
                return {
                    'total_positions': 0,
                    'largest_position_pct': 0,
                    'portfolio_concentration': 'N/A',
                    'risk_score': 0,
                    'risk_level': 'Low',
                    'diversification_score': 100
                }
                
        except sqlite3.OperationalError:
            logger.warning("Risk analysis tables not found")
            return {}

    def _get_asset_allocation(self, conn: sqlite3.Connection, user_id: int) -> List[Dict[str, Any]]:
        """Get current asset allocation breakdown."""
        try:
            cursor = conn.execute("""
                SELECT 
                    symbol,
                    SUM(CASE WHEN side = 'long' THEN size * current_price ELSE -size * current_price END) as position_value,
                    AVG(current_price) as avg_price,
                    SUM(unrealized_pnl) as unrealized_pnl
                FROM user_positions
                WHERE user_id = ? AND status = 'open'
                GROUP BY symbol
                ORDER BY ABS(position_value) DESC
            """, (user_id,))
            
            allocations = []
            total_value = 0
            
            for row in cursor.fetchall():
                position_value = abs(row['position_value'])
                total_value += position_value
                
                allocations.append({
                    'symbol': row['symbol'],
                    'value': round(position_value, 2),
                    'avg_price': round(row['avg_price'], 2),
                    'unrealized_pnl': round(row['unrealized_pnl'], 2),
                    'pnl_pct': 0  # Will be calculated after total_value is known
                })
            
            # Calculate percentages
            for allocation in allocations:
                allocation['percentage'] = round((allocation['value'] / total_value) * 100, 2) if total_value > 0 else 0
                allocation['pnl_pct'] = round((allocation['unrealized_pnl'] / allocation['value']) * 100, 2) if allocation['value'] > 0 else 0
            
            return allocations
            
        except sqlite3.OperationalError:
            logger.warning("Asset allocation tables not found")
            return []

    def _get_trading_history_analysis(self, conn: sqlite3.Connection, user_id: int) -> List[Dict[str, Any]]:
        """Get trading history with analysis."""
        try:
            cursor = conn.execute("""
                SELECT 
                    symbol, side, size, price, pnl, strategy_name, timestamp
                FROM user_trades
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT 50
            """, (user_id,))
            
            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'symbol': row['symbol'],
                    'side': row['side'],
                    'size': row['size'],
                    'price': row['price'],
                    'pnl': round(row['pnl'], 2),
                    'strategy_name': row['strategy_name'],
                    'timestamp': row['timestamp'],
                    'pnl_pct': round((row['pnl'] / (row['size'] * row['price'])) * 100, 2) if row['size'] and row['price'] else 0
                })
            
            return trades
            
        except sqlite3.OperationalError:
            logger.warning("Trading history tables not found")
            return []

    def _get_benchmark_comparison(self, conn: sqlite3.Connection, user_id: int) -> Dict[str, Any]:
        """Get benchmark comparison data."""
        try:
            # Simplified benchmark comparison (would integrate with real market data)
            return {
                'user_return_30d': 5.2,
                'btc_return_30d': 3.8,
                'eth_return_30d': 4.1,
                'sp500_return_30d': 2.1,
                'outperformance_vs_btc': 1.4,
                'outperformance_vs_eth': 1.1,
                'outperformance_vs_sp500': 3.1,
                'alpha': 0.8,
                'beta': 1.2
            }
        except Exception as e:
            logger.warning(f"Benchmark comparison error: {e}")
            return {}

    def _get_optimization_suggestions(self, conn: sqlite3.Connection, user_id: int) -> List[Dict[str, Any]]:
        """Get portfolio optimization suggestions."""
        try:
            suggestions = [
                {
                    'type': 'rebalancing',
                    'priority': 'high',
                    'title': 'Portfolio Rebalancing Recommended',
                    'description': 'Your BTC allocation is 45% of portfolio. Consider rebalancing to target 30%.',
                    'action': 'Reduce BTC position by $2,500',
                    'impact': 'Improved diversification'
                },
                {
                    'type': 'risk_management',
                    'priority': 'medium',
                    'title': 'Stop Loss Optimization',
                    'description': 'Consider tightening stop losses on ETH positions to 3%.',
                    'action': 'Update stop loss orders',
                    'impact': 'Reduced downside risk'
                },
                {
                    'type': 'opportunity',
                    'priority': 'low',
                    'title': 'Correlation Analysis',
                    'description': 'Your positions show high correlation. Consider adding uncorrelated assets.',
                    'action': 'Explore DeFi or commodities',
                    'impact': 'Enhanced diversification'
                }
            ]
            
            return suggestions
            
        except Exception as e:
            logger.warning(f"Optimization suggestions error: {e}")
            return []

    def _get_market_overview(self) -> Dict[str, Any]:
        """Get current market overview."""
        try:
            # Get market data from the market data manager
            if hasattr(self.market_data, 'get_market_overview'):
                return self.market_data.get_market_overview()
            else:
                # Return sample market data
                return {
                    'btc_price': 45000.0,
                    'eth_price': 3200.0,
                    'market_cap': 1800000000000,
                    'total_volume_24h': 85000000000,
                    'fear_greed_index': 65,
                    'market_sentiment': 'bullish'
                }
        except Exception as e:
            logger.error(f"Error getting market overview: {e}")
            return {}
