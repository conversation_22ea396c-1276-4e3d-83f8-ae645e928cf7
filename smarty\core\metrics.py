"""
Metrics collection for the smart-trader system.

This module provides utilities for collecting and exposing metrics.
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

from core.feature_store import feature_store

logger = logging.getLogger(__name__)


class Metrics:
    """
    Metrics collection for the smart-trader system.
    
    This class provides utilities for collecting and exposing metrics.
    """
    
    @staticmethod
    async def increment(symbol: str, key: str, value: float = 1.0) -> None:
        """
        Increment a counter metric.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            value: Value to increment by
        """
        try:
            # Get current value
            current = await feature_store.get(symbol, key) or 0
            
            # Increment
            await feature_store.set(symbol, key, current + value)
            
            # Log
            logger.debug(f"Incremented metric {key} for {symbol} by {value}")
        except Exception as e:
            logger.error(f"Error incrementing metric {key} for {symbol}: {e}")
    
    @staticmethod
    async def set(symbol: str, key: str, value: Any) -> None:
        """
        Set a gauge metric.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            value: Value to set
        """
        try:
            # Set value
            await feature_store.set(symbol, key, value)
            
            # Log
            logger.debug(f"Set metric {key} for {symbol} to {value}")
        except Exception as e:
            logger.error(f"Error setting metric {key} for {symbol}: {e}")
    
    @staticmethod
    async def record_latency(symbol: str, key: str, start_time: float) -> float:
        """
        Record latency for an operation.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            start_time: Start time in seconds
            
        Returns:
            Latency in seconds
        """
        try:
            # Calculate latency
            latency = time.time() - start_time
            
            # Store in feature store
            await feature_store.add_time_series(symbol, key, latency, datetime.now())
            
            # Log
            logger.debug(f"Recorded latency {key} for {symbol}: {latency:.4f}s")
            
            return latency
        except Exception as e:
            logger.error(f"Error recording latency {key} for {symbol}: {e}")
            return 0.0
    
    @staticmethod
    async def get_counter(symbol: str, key: str) -> float:
        """
        Get a counter metric.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            
        Returns:
            Counter value
        """
        try:
            return await feature_store.get(symbol, key) or 0
        except Exception as e:
            logger.error(f"Error getting counter {key} for {symbol}: {e}")
            return 0
    
    @staticmethod
    async def get_gauge(symbol: str, key: str) -> Any:
        """
        Get a gauge metric.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            
        Returns:
            Gauge value
        """
        try:
            return await feature_store.get(symbol, key)
        except Exception as e:
            logger.error(f"Error getting gauge {key} for {symbol}: {e}")
            return None
    
    @staticmethod
    async def get_latency_stats(symbol: str, key: str, window_minutes: int = 10) -> Dict[str, float]:
        """
        Get latency statistics.
        
        Args:
            symbol: Trading symbol
            key: Metric key
            window_minutes: Time window in minutes
            
        Returns:
            Dictionary with latency statistics
        """
        try:
            # Get time series
            start_time = datetime.now().timestamp() - (window_minutes * 60)
            series = await feature_store.get_time_series(
                symbol, key, 
                start=datetime.fromtimestamp(start_time)
            )
            
            if not series:
                return {
                    "count": 0,
                    "min": 0.0,
                    "max": 0.0,
                    "avg": 0.0,
                    "p95": 0.0,
                    "p99": 0.0
                }
            
            # Extract values
            values = [float(value) for _, value in series]
            
            # Calculate statistics
            values.sort()
            count = len(values)
            min_val = values[0] if count > 0 else 0.0
            max_val = values[-1] if count > 0 else 0.0
            avg_val = sum(values) / count if count > 0 else 0.0
            p95 = values[int(count * 0.95)] if count > 0 else 0.0
            p99 = values[int(count * 0.99)] if count > 0 else 0.0
            
            return {
                "count": count,
                "min": min_val,
                "max": max_val,
                "avg": avg_val,
                "p95": p95,
                "p99": p99
            }
        except Exception as e:
            logger.error(f"Error getting latency stats {key} for {symbol}: {e}")
            return {
                "count": 0,
                "min": 0.0,
                "max": 0.0,
                "avg": 0.0,
                "p95": 0.0,
                "p99": 0.0
            }


# Convenience functions
async def increment(symbol: str, key: str, value: float = 1.0) -> None:
    """Increment a counter metric."""
    await Metrics.increment(symbol, key, value)

async def set_metric(symbol: str, key: str, value: Any) -> None:
    """Set a gauge metric."""
    await Metrics.set(symbol, key, value)

async def record_latency(symbol: str, key: str, start_time: float) -> float:
    """Record latency for an operation."""
    return await Metrics.record_latency(symbol, key, start_time)

async def get_counter(symbol: str, key: str) -> float:
    """Get a counter metric."""
    return await Metrics.get_counter(symbol, key)

async def get_gauge(symbol: str, key: str) -> Any:
    """Get a gauge metric."""
    return await Metrics.get_gauge(symbol, key)

async def get_latency_stats(symbol: str, key: str, window_minutes: int = 10) -> Dict[str, float]:
    """Get latency statistics."""
    return await Metrics.get_latency_stats(symbol, key, window_minutes)
