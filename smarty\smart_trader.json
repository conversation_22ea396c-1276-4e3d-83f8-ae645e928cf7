{"timestamp": "2025-05-23T22:21:04.253834", "level": "INFO", "logger": "config_manager", "message": "Loading configuration from: config.yaml", "module": "config_manager", "function": "load_config", "line": 150, "extra": {}}
{"timestamp": "2025-05-23T22:24:50.901887", "level": "INFO", "logger": "config_manager", "message": "Loading configuration from: config.yaml", "module": "config_manager", "function": "load_config", "line": 150, "extra": {}}
{"timestamp": "2025-05-23T22:26:26.274047", "level": "INFO", "logger": "config_manager", "message": "Loading configuration from: config.yaml", "module": "config_manager", "function": "load_config", "line": 150, "extra": {}}
{"timestamp": "2025-05-23T22:27:10.433576", "level": "INFO", "logger": "__main__", "message": "\ud83d\udcdd Using basic configuration for now (enhanced config coming soon)", "module": "web_control_center_multipage", "function": "__init__", "line": 59, "extra": {}}
{"timestamp": "2025-05-23T22:30:40.764343", "level": "INFO", "logger": "__main__", "message": "Using basic configuration for now (enhanced config coming soon)", "module": "web_control_center_multipage", "function": "__init__", "line": 59, "extra": {}}
{"timestamp": "2025-05-23T22:31:47.174177", "level": "INFO", "logger": "__main__", "message": "Using basic configuration for now (enhanced config coming soon)", "module": "web_control_center_multipage", "function": "__init__", "line": 59, "extra": {}}
{"timestamp": "2025-05-23T22:32:24.482255", "level": "INFO", "logger": "__main__", "message": "Using basic configuration for now (enhanced config coming soon)", "module": "web_control_center_multipage", "function": "__init__", "line": 59, "extra": {}}
