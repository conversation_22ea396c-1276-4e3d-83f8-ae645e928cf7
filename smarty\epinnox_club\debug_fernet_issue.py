#!/usr/bin/env python3
"""
Debug script to isolate the <PERSON><PERSON><PERSON> key issue.
"""

import sys
import traceback
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_config():
    """Test config import."""
    try:
        print("Testing config import...")
        from config import get_config
        config = get_config('development')
        print(f"✅ Config loaded: SECRET_KEY type = {type(config.SECRET_KEY)}")
        print(f"✅ SECRET_KEY length = {len(config.SECRET_KEY)}")
        return True
    except Exception as e:
        print(f"❌ Config error: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Test database import."""
    try:
        print("Testing database import...")
        from database.models import DatabaseManager
        db = DatabaseManager("data/test_money_circle.db")
        print("✅ Database manager created")
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        traceback.print_exc()
        return False

def test_encryption():
    """Test encryption utilities import."""
    try:
        print("Testing encryption import...")
        from exchanges.encryption_utils import get_encryption_manager
        manager = get_encryption_manager()
        print("✅ Encryption manager created")
        return True
    except Exception as e:
        print(f"❌ Encryption error: {e}")
        traceback.print_exc()
        return False

def test_user_manager():
    """Test user manager import."""
    try:
        print("Testing user manager import...")
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        
        db = DatabaseManager("data/test_money_circle.db")
        user_manager = UserManager(db)
        print("✅ User manager created")
        return True
    except Exception as e:
        print(f"❌ User manager error: {e}")
        traceback.print_exc()
        return False

def test_exchange_manager():
    """Test exchange manager import."""
    try:
        print("Testing exchange manager import...")
        from database.models import DatabaseManager
        from exchanges.account_manager import ExchangeAccountManager
        
        db = DatabaseManager("data/test_money_circle.db")
        exchange_manager = ExchangeAccountManager(db)
        print("✅ Exchange manager created")
        return True
    except Exception as e:
        print(f"❌ Exchange manager error: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test app creation."""
    try:
        print("Testing app creation...")
        from app import MoneyCircleApp
        
        app = MoneyCircleApp('development')
        print("✅ App created")
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🔍 Debugging Fernet key issue...")
    
    tests = [
        test_config,
        test_database,
        test_encryption,
        test_user_manager,
        test_exchange_manager,
        test_app_creation
    ]
    
    for test in tests:
        print(f"\n{'='*50}")
        if not test():
            print(f"❌ Test failed: {test.__name__}")
            break
        print(f"✅ Test passed: {test.__name__}")
    
    print(f"\n{'='*50}")
    print("🏁 Debug complete")

if __name__ == '__main__':
    main()
