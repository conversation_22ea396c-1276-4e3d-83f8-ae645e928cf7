#!/usr/bin/env python3
"""
Money Circle Real-Time Notification Manager
Advanced notification system with WebSocket delivery, email alerts, and priority management.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import aiohttp
from aiohttp import web

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    TRADE_EXECUTED = "trade_executed"
    RISK_ALERT = "risk_alert"
    STRATEGY_SIGNAL = "strategy_signal"
    PORTFOLIO_UPDATE = "portfolio_update"
    CLUB_ANNOUNCEMENT = "club_announcement"
    STRATEGY_VOTE = "strategy_vote"
    MEMBER_ACTIVITY = "member_activity"
    SYSTEM_ALERT = "system_alert"
    PRICE_ALERT = "price_alert"
    EMERGENCY_STOP = "emergency_stop"

class NotificationPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class DeliveryChannel(Enum):
    WEBSOCKET = "websocket"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"

@dataclass
class Notification:
    """Enhanced notification with delivery tracking."""
    id: str
    user_id: int
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    data: Dict[str, Any]
    channels: List[DeliveryChannel]
    created_at: datetime
    expires_at: Optional[datetime]
    delivered_at: Optional[datetime]
    read_at: Optional[datetime]
    delivery_attempts: int
    max_attempts: int
    metadata: Dict[str, Any]

class NotificationManager:
    """Advanced real-time notification management system."""

    def __init__(self, db_manager):
        self.db = db_manager

        # WebSocket connections
        self.websocket_connections: Dict[int, Set[web.WebSocketResponse]] = {}

        # Notification queues
        self.notification_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queue: asyncio.Queue = asyncio.Queue()

        # Delivery tracking
        self.delivery_active = False
        self.delivery_stats = {
            'total_sent': 0,
            'total_delivered': 0,
            'total_failed': 0,
            'websocket_delivered': 0,
            'email_delivered': 0
        }

        # Rate limiting
        self.rate_limits = {
            NotificationPriority.LOW: 10,      # 10 per minute
            NotificationPriority.MEDIUM: 30,   # 30 per minute
            NotificationPriority.HIGH: 60,     # 60 per minute
            NotificationPriority.CRITICAL: 0   # No limit
        }

        # User preferences
        self.user_preferences: Dict[int, Dict[str, Any]] = {}

        logger.info("[NOTIFY] Notification Manager initialized")

    async def start_notification_system(self):
        """Start the notification delivery system."""
        if self.delivery_active:
            return

        self.delivery_active = True
        logger.info("[NOTIFY] Starting notification delivery system...")

        # Start delivery tasks
        tasks = [
            self._notification_delivery_loop(),
            self._priority_notification_loop(),
            self._cleanup_expired_notifications(),
            self._update_delivery_stats()
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def send_notification(self, user_id: int, notification_type: NotificationType,
                              title: str, message: str, data: Dict[str, Any] = None,
                              priority: NotificationPriority = NotificationPriority.MEDIUM,
                              channels: List[DeliveryChannel] = None) -> str:
        """Send notification to user with specified priority and channels."""
        try:
            # Generate notification ID
            notification_id = f"notif_{user_id}_{int(datetime.now().timestamp() * 1000)}"

            # Default channels if not specified
            if channels is None:
                channels = [DeliveryChannel.WEBSOCKET, DeliveryChannel.IN_APP]

            # Check user preferences
            user_prefs = await self._get_user_preferences(user_id)
            channels = self._filter_channels_by_preferences(channels, user_prefs, notification_type)

            # Create notification
            notification = Notification(
                id=notification_id,
                user_id=user_id,
                type=notification_type,
                priority=priority,
                title=title,
                message=message,
                data=data or {},
                channels=channels,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=24),  # 24 hour expiry
                delivered_at=None,
                read_at=None,
                delivery_attempts=0,
                max_attempts=3,
                metadata={}
            )

            # Store in database
            await self._store_notification(notification)

            # Queue for delivery
            if priority == NotificationPriority.CRITICAL:
                await self.priority_queue.put(notification)
            else:
                await self.notification_queue.put(notification)

            logger.info(f"📨 Notification queued: {notification_id} for user {user_id}")
            return notification_id

        except Exception as e:
            logger.error(f"❌ Error sending notification: {e}")
            return None

    async def send_trade_notification(self, user_id: int, trade_data: Dict[str, Any]):
        """Send trade execution notification."""
        title = f"Trade Executed: {trade_data.get('side', '').upper()} {trade_data.get('symbol', '')}"
        message = f"Successfully executed {trade_data.get('side')} order for {trade_data.get('amount')} {trade_data.get('symbol')} at ${trade_data.get('price', 0):.4f}"

        await self.send_notification(
            user_id=user_id,
            notification_type=NotificationType.TRADE_EXECUTED,
            title=title,
            message=message,
            data=trade_data,
            priority=NotificationPriority.HIGH,
            channels=[DeliveryChannel.WEBSOCKET, DeliveryChannel.IN_APP, DeliveryChannel.EMAIL]
        )

    async def send_risk_alert(self, user_id: int, risk_data: Dict[str, Any]):
        """Send risk management alert."""
        risk_level = risk_data.get('risk_level', 'medium')
        title = f"🚨 Risk Alert: {risk_level.upper()} Risk Level"
        message = f"Portfolio risk score: {risk_data.get('risk_score', 0):.0f}/100. {risk_data.get('warning', '')}"

        priority = NotificationPriority.CRITICAL if risk_level == 'critical' else NotificationPriority.HIGH

        await self.send_notification(
            user_id=user_id,
            notification_type=NotificationType.RISK_ALERT,
            title=title,
            message=message,
            data=risk_data,
            priority=priority,
            channels=[DeliveryChannel.WEBSOCKET, DeliveryChannel.IN_APP, DeliveryChannel.EMAIL]
        )

    async def send_strategy_signal(self, user_id: int, signal_data: Dict[str, Any]):
        """Send strategy signal notification."""
        strategy_name = signal_data.get('strategy_name', 'Unknown')
        action = signal_data.get('action', 'HOLD')
        symbol = signal_data.get('symbol', '')
        confidence = signal_data.get('confidence', 0)

        title = f"📊 Strategy Signal: {strategy_name}"
        message = f"{action} signal for {symbol} (Confidence: {confidence:.1%})"

        await self.send_notification(
            user_id=user_id,
            notification_type=NotificationType.STRATEGY_SIGNAL,
            title=title,
            message=message,
            data=signal_data,
            priority=NotificationPriority.MEDIUM,
            channels=[DeliveryChannel.WEBSOCKET, DeliveryChannel.IN_APP]
        )

    async def send_club_announcement(self, announcement_data: Dict[str, Any]):
        """Send club-wide announcement to all members."""
        title = announcement_data.get('title', 'Club Announcement')
        message = announcement_data.get('message', '')

        # Get all active club members
        active_members = await self._get_active_club_members()

        # Send to all members
        for user_id in active_members:
            await self.send_notification(
                user_id=user_id,
                notification_type=NotificationType.CLUB_ANNOUNCEMENT,
                title=title,
                message=message,
                data=announcement_data,
                priority=NotificationPriority.MEDIUM,
                channels=[DeliveryChannel.WEBSOCKET, DeliveryChannel.IN_APP, DeliveryChannel.EMAIL]
            )

    async def register_websocket(self, user_id: int, websocket: web.WebSocketResponse):
        """Register WebSocket connection for user."""
        if user_id not in self.websocket_connections:
            self.websocket_connections[user_id] = set()

        self.websocket_connections[user_id].add(websocket)
        logger.info(f"🔌 WebSocket registered for user {user_id}")

        # Send pending notifications
        await self._send_pending_notifications(user_id, websocket)

    async def unregister_websocket(self, user_id: int, websocket: web.WebSocketResponse):
        """Unregister WebSocket connection for user."""
        if user_id in self.websocket_connections:
            self.websocket_connections[user_id].discard(websocket)
            if not self.websocket_connections[user_id]:
                del self.websocket_connections[user_id]

        logger.info(f"🔌 WebSocket unregistered for user {user_id}")

    async def _notification_delivery_loop(self):
        """Main notification delivery loop."""
        while self.delivery_active:
            try:
                # Get notification from queue
                try:
                    notification = await asyncio.wait_for(self.notification_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # Deliver notification
                await self._deliver_notification(notification)

            except Exception as e:
                logger.error(f"❌ Error in notification delivery loop: {e}")
                await asyncio.sleep(1)

    async def _priority_notification_loop(self):
        """Priority notification delivery loop."""
        while self.delivery_active:
            try:
                # Get priority notification from queue
                try:
                    notification = await asyncio.wait_for(self.priority_queue.get(), timeout=0.5)
                except asyncio.TimeoutError:
                    continue

                # Deliver immediately
                await self._deliver_notification(notification)

            except Exception as e:
                logger.error(f"❌ Error in priority notification loop: {e}")
                await asyncio.sleep(0.5)

    async def _deliver_notification(self, notification: Notification):
        """Deliver notification through specified channels."""
        try:
            delivery_success = False

            # Try each delivery channel
            for channel in notification.channels:
                try:
                    if channel == DeliveryChannel.WEBSOCKET:
                        success = await self._deliver_websocket(notification)
                        if success:
                            self.delivery_stats['websocket_delivered'] += 1
                            delivery_success = True

                    elif channel == DeliveryChannel.EMAIL:
                        success = await self._deliver_email(notification)
                        if success:
                            self.delivery_stats['email_delivered'] += 1
                            delivery_success = True

                    elif channel == DeliveryChannel.IN_APP:
                        success = await self._deliver_in_app(notification)
                        if success:
                            delivery_success = True

                except Exception as e:
                    logger.error(f"❌ Error delivering via {channel.value}: {e}")

            # Update delivery status
            if delivery_success:
                notification.delivered_at = datetime.now()
                self.delivery_stats['total_delivered'] += 1
            else:
                notification.delivery_attempts += 1
                self.delivery_stats['total_failed'] += 1

                # Retry if under max attempts
                if notification.delivery_attempts < notification.max_attempts:
                    await asyncio.sleep(5)  # Wait 5 seconds before retry
                    await self.notification_queue.put(notification)

            # Update in database
            await self._update_notification_status(notification)

        except Exception as e:
            logger.error(f"❌ Error delivering notification {notification.id}: {e}")

    async def _deliver_websocket(self, notification: Notification) -> bool:
        """Deliver notification via WebSocket."""
        try:
            user_id = notification.user_id
            if user_id not in self.websocket_connections:
                return False

            # Prepare WebSocket message
            ws_message = {
                'type': 'notification',
                'id': notification.id,
                'notification_type': notification.type.value,
                'priority': notification.priority.value,
                'title': notification.title,
                'message': notification.message,
                'data': notification.data,
                'timestamp': notification.created_at.isoformat()
            }

            # Send to all user's WebSocket connections
            connections = list(self.websocket_connections[user_id])
            for ws in connections:
                try:
                    if ws.closed:
                        self.websocket_connections[user_id].discard(ws)
                        continue

                    await ws.send_str(json.dumps(ws_message))

                except Exception as e:
                    logger.error(f"❌ Error sending WebSocket message: {e}")
                    self.websocket_connections[user_id].discard(ws)

            return len(connections) > 0

        except Exception as e:
            logger.error(f"❌ Error in WebSocket delivery: {e}")
            return False

    async def _deliver_email(self, notification: Notification) -> bool:
        """Deliver notification via email."""
        try:
            # This would integrate with an email service
            # For now, just log the email delivery
            logger.info(f"📧 Email notification sent to user {notification.user_id}: {notification.title}")
            return True

        except Exception as e:
            logger.error(f"❌ Error in email delivery: {e}")
            return False

    async def _deliver_in_app(self, notification: Notification) -> bool:
        """Store notification for in-app delivery."""
        try:
            # Store in database for in-app notifications
            await self._store_notification(notification)
            return True

        except Exception as e:
            logger.error(f"❌ Error in in-app delivery: {e}")
            return False

    async def _store_notification(self, notification: Notification):
        """Store notification in database."""
        try:
            self.db.conn.execute("""
                INSERT OR REPLACE INTO notifications
                (id, user_id, type, priority, title, message, data, channels,
                 created_at, expires_at, delivered_at, read_at, delivery_attempts, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                notification.id, notification.user_id, notification.type.value,
                notification.priority.value, notification.title, notification.message,
                json.dumps(notification.data), json.dumps([c.value for c in notification.channels]),
                notification.created_at, notification.expires_at, notification.delivered_at,
                notification.read_at, notification.delivery_attempts, json.dumps(notification.metadata)
            ))
            self.db.conn.commit()

        except Exception as e:
            logger.error(f"❌ Error storing notification: {e}")

    async def _update_notification_status(self, notification: Notification):
        """Update notification delivery status in database."""
        try:
            self.db.conn.execute("""
                UPDATE notifications
                SET delivered_at = ?, delivery_attempts = ?
                WHERE id = ?
            """, (notification.delivered_at, notification.delivery_attempts, notification.id))
            self.db.conn.commit()

        except Exception as e:
            logger.error(f"❌ Error updating notification status: {e}")

    async def _get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """Get user notification preferences."""
        if user_id in self.user_preferences:
            return self.user_preferences[user_id]

        # Default preferences
        return {
            'websocket_enabled': True,
            'email_enabled': True,
            'trade_notifications': True,
            'risk_alerts': True,
            'strategy_signals': True,
            'club_announcements': True
        }

    def _filter_channels_by_preferences(self, channels: List[DeliveryChannel],
                                      preferences: Dict[str, Any],
                                      notification_type: NotificationType) -> List[DeliveryChannel]:
        """Filter delivery channels based on user preferences."""
        filtered_channels = []

        for channel in channels:
            if channel == DeliveryChannel.WEBSOCKET and preferences.get('websocket_enabled', True):
                filtered_channels.append(channel)
            elif channel == DeliveryChannel.EMAIL and preferences.get('email_enabled', True):
                filtered_channels.append(channel)
            elif channel == DeliveryChannel.IN_APP:
                filtered_channels.append(channel)  # Always allow in-app

        return filtered_channels

    async def _get_active_club_members(self) -> List[int]:
        """Get list of active club members."""
        try:
            cursor = self.db.conn.execute("""
                SELECT user_id FROM users WHERE is_active = TRUE
            """)
            return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"[ERROR] Error getting active members: {e}")
            return []

    async def _send_pending_notifications(self, user_id: int, websocket: web.WebSocketResponse):
        """Send pending notifications to newly connected WebSocket."""
        try:
            # Get recent unread notifications (last 24 hours)
            cutoff_date = datetime.now() - timedelta(hours=24)
            cursor = self.db.conn.execute("""
                SELECT * FROM notifications
                WHERE user_id = ? AND is_read = FALSE AND created_at > ?
                ORDER BY created_at DESC LIMIT 10
            """, (user_id, cutoff_date))

            for row in cursor.fetchall():
                # Convert row to notification and send
                pass  # Implementation would convert DB row to notification object

        except Exception as e:
            logger.error(f"❌ Error sending pending notifications: {e}")

    async def _cleanup_expired_notifications(self):
        """Clean up expired notifications."""
        while self.delivery_active:
            try:
                # Use a fresh database connection for background tasks
                import sqlite3
                conn = sqlite3.connect('data/money_circle.db')

                try:
                    # Remove old notifications (older than 7 days)
                    cutoff_date = datetime.now() - timedelta(days=7)
                    conn.execute("""
                        DELETE FROM notifications WHERE created_at < ?
                    """, (cutoff_date,))
                    conn.commit()
                except sqlite3.OperationalError:
                    # Table doesn't exist, skip cleanup
                    pass
                finally:
                    conn.close()

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                logger.error(f"[ERROR] Error cleaning up notifications: {e}")
                await asyncio.sleep(1800)  # Retry in 30 minutes

    async def _update_delivery_stats(self):
        """Update delivery statistics."""
        while self.delivery_active:
            try:
                self.delivery_stats['total_sent'] = self.delivery_stats['total_delivered'] + self.delivery_stats['total_failed']

                await asyncio.sleep(60)  # Update every minute

            except Exception as e:
                logger.error(f"❌ Error updating delivery stats: {e}")
                await asyncio.sleep(60)

    async def mark_notification_read(self, notification_id: str, user_id: int) -> bool:
        """Mark notification as read."""
        try:
            self.db.conn.execute("""
                UPDATE notifications
                SET read_at = ?
                WHERE id = ? AND user_id = ?
            """, (datetime.now(), notification_id, user_id))
            self.db.conn.commit()

            return True

        except Exception as e:
            logger.error(f"❌ Error marking notification as read: {e}")
            return False

    def get_delivery_stats(self) -> Dict[str, Any]:
        """Get notification delivery statistics."""
        return self.delivery_stats.copy()

    async def stop_notification_system(self):
        """Stop the notification system."""
        self.delivery_active = False
        logger.info("🛑 Notification system stopped")
