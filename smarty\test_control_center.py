#!/usr/bin/env python3
"""
Test script for the Smart-Trader Control Center API endpoints.
"""

import asyncio
import aiohttp
import json

async def test_endpoints():
    """Test the control center endpoints."""
    base_url = "http://localhost:8081"

    async with aiohttp.ClientSession() as session:
        print("🧪 Testing Smart-Trader Control Center API...")

        # Test status endpoint
        print("\n1. Testing status endpoint...")
        async with session.get(f"{base_url}/api/status") as resp:
            status = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Smart-trader available: {status.get('smart_trader_available')}")

        # Test orchestrator start
        print("\n2. Testing orchestrator start...")
        async with session.post(f"{base_url}/api/orchestrator/start") as resp:
            result = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {result}")

        # Wait a moment
        await asyncio.sleep(2)

        # Test testnet start
        print("\n3. Testing testnet start...")
        async with session.post(f"{base_url}/api/testnet/start") as resp:
            result = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {result}")

        # Test status again
        print("\n4. Testing status after starts...")
        async with session.get(f"{base_url}/api/status") as resp:
            status = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Orchestrator running: {status['system_status']['orchestrator']['running']}")
            print(f"   Testnet running: {status['system_status']['testnet']['running']}")

if __name__ == "__main__":
    asyncio.run(test_endpoints())
