#!/usr/bin/env python3
"""
Test HTX exchange credentials directly.
This script tests HTX credentials without sandbox mode.
"""

import ccxt
import yaml
import sys
import os
from pathlib import Path

def load_credentials():
    """Load credentials from credentials.yaml"""
    cred_file = Path("credentials.yaml")
    if not cred_file.exists():
        print("❌ credentials.yaml not found")
        return None

    with open(cred_file, 'r') as f:
        creds = yaml.safe_load(f)

    return creds

def test_htx_credentials(api_key, secret_key):
    """Test HTX credentials with production mode (no sandbox)"""
    print("🔧 Testing HTX credentials...")
    print(f"📊 API Key: {api_key[:8]}...")
    print(f"🔐 Secret Key: {secret_key[:8]}...")

    try:
        # Create HTX exchange instance (production mode)
        exchange = ccxt.huobi({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': False,  # Use production mode
            'enableRateLimit': True,
            'timeout': 30000,  # 30 second timeout
        })

        print("✅ Exchange instance created successfully")
        print(f"📍 Exchange ID: {exchange.id}")
        print(f"🌐 URLs: {exchange.urls}")

        # Test API connectivity
        print("\n🔍 Testing API connectivity...")

        try:
            # Test with a simple API call that doesn't require special permissions
            markets = exchange.load_markets()
            print(f"✅ Markets loaded successfully: {len(markets)} markets")

            # Test account access
            print("\n💰 Testing account access...")
            balance = exchange.fetch_balance()
            print("✅ Balance fetched successfully")

            # Show some balance info (without revealing actual amounts)
            total_currencies = len([k for k, v in balance.get('total', {}).items() if v > 0])
            print(f"📊 Account has balances in {total_currencies} currencies")

            return True

        except Exception as api_error:
            print(f"⚠️ API call failed: {api_error}")

            # Check if it's a credential error vs other API error
            error_msg = str(api_error).lower()
            if any(keyword in error_msg for keyword in ['invalid', 'unauthorized', 'forbidden', 'signature']):
                print("❌ Credential validation failed - Invalid credentials")
                return False
            else:
                print("⚠️ API error but credentials appear valid")
                print("   This might be due to rate limits, permissions, or network issues")
                return True

    except Exception as e:
        print(f"❌ Failed to create exchange instance: {e}")
        return False

def main():
    print("🚀 HTX Credential Validation Test")
    print("=" * 50)

    # Load credentials
    creds = load_credentials()
    if not creds:
        sys.exit(1)

    # Get HTX credentials from accounts structure
    htx_creds = None

    # Look for accounts with huobi exchange
    accounts = creds.get('accounts', [])
    default_account = creds.get('default_account', 'EPX')

    # First try to find the default account
    for account in accounts:
        if account.get('name') == default_account and account.get('exchange') == 'huobi':
            htx_creds = account
            print(f"📋 Found default HTX account: {account.get('name')} - {account.get('description')}")
            break

    # If no default account found, try any huobi account
    if not htx_creds:
        for account in accounts:
            if account.get('exchange') == 'huobi':
                htx_creds = account
                print(f"📋 Found HTX account: {account.get('name')} - {account.get('description')}")
                break

    if not htx_creds:
        print("❌ No HTX/Huobi accounts found in credentials.yaml")
        print("   Looked for accounts with exchange: huobi")
        sys.exit(1)

    # Extract API credentials
    api_key = htx_creds.get('api_key')
    secret_key = htx_creds.get('secret_key')

    if not api_key or not secret_key:
        print("❌ Missing api_key or secret_key in credentials")
        sys.exit(1)

    # Test credentials
    success = test_htx_credentials(api_key, secret_key)

    print("\n" + "=" * 50)
    if success:
        print("🎉 HTX credentials validation: SUCCESS")
        print("✅ Credentials are valid and can be used in production mode")
    else:
        print("❌ HTX credentials validation: FAILED")
        print("❌ Please check your API key and secret key")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
