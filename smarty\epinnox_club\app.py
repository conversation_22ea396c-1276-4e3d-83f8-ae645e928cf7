#!/usr/bin/env python3
"""
Money Circle Investment Club Platform
Main application entry point for the multi-user trading platform.
"""

import asyncio
import json
import logging
import sqlite3
import sys
import os
from pathlib import Path
from typing import Callable, List, Dict
from datetime import datetime
from aiohttp import web, WSMsgType
import aiohttp_cors
from aiohttp_session import setup
from aiohttp_session.cookie_storage import EncryptedCookieStorage
import aiohttp_jinja2
import jinja2

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import get_config
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from auth.user_manager import UserManager
from auth.decorators import AuthMiddleware
from exchanges.account_manager import ExchangeAccountManager
from dashboards.personal_dashboard import PersonalDashboard
from dashboards.club_dashboard import ClubDashboard
from dashboards.enhanced_strategy_marketplace import EnhancedStrategyMarketplace
from dashboards.enhanced_member_directory import EnhancedMemberDirectory
from dashboards.enhanced_club_analytics import EnhancedClubAnalytics
from dashboards.admin_dashboard import AdminDashboard
from dashboards.auto_trader_dashboard import AutoTraderDashboard
from dashboards.signals_dashboard import SignalsDashboard
from dashboards.portfolio_analytics_dashboard import PortfolioAnalyticsDashboard
from dashboards.social_trading_dashboard import SocialTradingDashboard
from club.strategy_governance import StrategyGovernance

# Production deployment components
from security.security_manager import SecurityManager
# PerformanceMonitor disabled to prevent middleware conflicts
from backup.backup_manager import BackupManager
from deployment.production_config import ProductionConfigManager
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

# Live trading components
from trading.htx_futures_client import HTXFuturesClient
from trading.advanced_trading_interface import AdvancedTradingInterface

# Phase 3 Production Components
PHASE3_AVAILABLE = False
ProductionEnvironmentManager = None
AdvancedRiskManager = None
TradingAnalytics = None
StrategyIntegrationManager = None

try:
    from config.production_env import ProductionEnvironmentManager, get_production_config
    from trading.advanced_risk_manager import AdvancedRiskManager
    from analytics.trading_analytics import TradingAnalytics
    from strategies.strategy_integration_manager import StrategyIntegrationManager
    PHASE3_AVAILABLE = True
    print("[PHASE3] Phase 3 components available")
except ImportError as e:
    print(f"[PHASE3] Phase 3 components not available: {e}")
    PHASE3_AVAILABLE = False

# Configure logging with proper encoding for Windows
import sys
import io

# Create a custom stream handler that handles Unicode properly on Windows
class SafeStreamHandler(logging.StreamHandler):
    def __init__(self, stream=None):
        if stream is None:
            stream = sys.stdout
        # Wrap the stream to handle encoding issues
        if hasattr(stream, 'buffer'):
            stream = io.TextIOWrapper(stream.buffer, encoding='utf-8', errors='replace')
        super().__init__(stream)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        SafeStreamHandler(),
        logging.FileHandler('logs/money_circle.log', encoding='utf-8', errors='replace')
    ]
)
logger = logging.getLogger(__name__)

# Production middleware completely disabled to prevent conflicts
logger.info("[INFO] Production middleware imports disabled")

class MoneyCircleApp:
    """Main Money Circle application."""

    def __init__(self, config_name: str = None):
        self.config = get_config(config_name)
        self.config.init_directories()

        # Initialize core components
        self.db_manager = DatabaseManager(self.config.DATABASE_PATH)
        self.club_db_manager = ClubDatabaseManager(self.db_manager)
        self.user_manager = UserManager(self.db_manager)
        self.exchange_manager = ExchangeAccountManager(self.db_manager)

        # Initialize dashboards
        self.personal_dashboard = PersonalDashboard(
            self.db_manager,
            self.user_manager,
            self.exchange_manager
        )
        self.club_dashboard = ClubDashboard(self.db_manager)

        # Initialize enhanced club features
        self.enhanced_strategy_marketplace = EnhancedStrategyMarketplace(self.db_manager)
        self.enhanced_member_directory = EnhancedMemberDirectory(self.db_manager)
        self.enhanced_club_analytics = EnhancedClubAnalytics(self.db_manager)
        self.admin_dashboard = AdminDashboard(self.db_manager)

        # Initialize club features
        self.strategy_governance = StrategyGovernance(self.db_manager)
        self.social_trading = SocialTrading(self.db_manager)
        self.club_analytics = ClubAnalytics(self.db_manager)

        # Initialize production deployment components
        try:
            from dataclasses import asdict

            # Initialize production configuration
            self.production_config = ProductionConfigManager()

            # Get configuration objects
            security_config = self.production_config.get_security_config()
            # monitoring_config disabled - performance monitor not used
            backup_config = self.production_config.get_backup_config()

            # Initialize production components (performance monitor disabled)
            self.security_manager = SecurityManager(asdict(security_config))
            self.performance_monitor = None  # Disabled to prevent middleware conflicts
            self.backup_manager = BackupManager(asdict(backup_config))

            logger.info("[OK] Production deployment components initialized")
        except Exception as e:
            logger.warning(f"[WARNING] Production deployment components not available: {e}")
            self.production_config = None
            self.security_manager = None
            self.performance_monitor = None
            self.backup_manager = None

        # Initialize Phase 3 production components
        if PHASE3_AVAILABLE:
            try:
                # Initialize production environment manager
                self.production_env_manager = ProductionEnvironmentManager()

                # Initialize advanced risk manager
                risk_config = self.production_env_manager.get_risk_config()
                self.advanced_risk_manager = AdvancedRiskManager(risk_config.__dict__)

                # Initialize trading analytics
                self.trading_analytics = TradingAnalytics()

                # Initialize strategy integration manager
                self.strategy_integration_manager = StrategyIntegrationManager()

                logger.info("[PHASE3] ✅ Production components initialized")
                logger.info(f"[PHASE3] Environment: {self.production_env_manager.environment}")
                logger.info(f"[PHASE3] Live trading enabled: {self.production_env_manager.is_live_trading_enabled()}")

            except Exception as e:
                logger.error(f"[PHASE3] ❌ Error initializing Phase 3 components: {e}")
                self.production_env_manager = None
                self.advanced_risk_manager = None
                self.trading_analytics = None
                self.strategy_integration_manager = None
        else:
            self.production_env_manager = None
            self.advanced_risk_manager = None
            self.trading_analytics = None
            self.strategy_integration_manager = None

        # Initialize advanced trading components
        try:
            from trading.live_trading_interface import LiveTradingInterface
            from strategies.live_strategy_engine import LiveStrategyEngine
            from notifications.notification_manager import NotificationManager
            from market_data.advanced_market_data_manager import AdvancedMarketDataManager
            from market_data.market_data_api import MarketDataAPI
            from market_data.websocket_streamer import MarketDataWebSocketStreamer

            # Initialize market data system
            market_data_config = {
                'symbols': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
                'update_interval': 1.0,
                'max_trade_history': 100
            }
            self.market_data_manager = AdvancedMarketDataManager(market_data_config)
            self.market_data_api = MarketDataAPI(self.market_data_manager)
            self.websocket_streamer = MarketDataWebSocketStreamer(self.market_data_manager)

            # Initialize other components
            self.notification_manager = NotificationManager(self.db_manager)
            self.live_trading_interface = LiveTradingInterface(
                self.db_manager, self.exchange_manager, self.user_manager
            )
            self.live_strategy_engine = LiveStrategyEngine(
                self.db_manager, self.live_trading_interface, self.market_data_manager
            )

            # Initialize Phase 2 dashboards (Auto Trader and Signals)
            self.auto_trader_dashboard = AutoTraderDashboard(
                self.db_manager, self.live_trading_interface,
                self.live_strategy_engine, self.market_data_manager
            )
            self.signals_dashboard = SignalsDashboard(
                self.db_manager, self.market_data_manager, self.live_strategy_engine
            )

            # Initialize Phase 3 dashboards (Portfolio Analytics and Social Trading)
            self.portfolio_analytics_dashboard = PortfolioAnalyticsDashboard(
                self.db_manager, self.market_data_manager, self.live_trading_interface
            )
            self.social_trading_dashboard = SocialTradingDashboard(
                self.db_manager, self.live_trading_interface, self.live_strategy_engine
            )

            logger.info("[OK] Advanced trading components with real-time market data initialized")

            # Initialize live trading system for epinnox account
            self._init_live_trading_for_epinnox()

        except Exception as e:
            logger.warning(f"[WARNING] Advanced trading components not available: {e}")
            self.notification_manager = None
            self.live_trading_interface = None
            self.live_strategy_engine = None
            self.market_data_manager = None
            self.market_data_api = None
            self.websocket_streamer = None
            self.auto_trader_dashboard = None
            self.signals_dashboard = None
            self.portfolio_analytics_dashboard = None
            self.social_trading_dashboard = None
            self.htx_futures_client = None
            self.advanced_trading_interface = None

        # WebSocket connections
        self.websockets = set()

        # Initialize Phase 3 components if available
        self._initialize_phase3_components()

        # Ensure admin user exists
        self._ensure_admin_user()

        logger.info(f"[STARTUP] Money Circle initialized with config: {config_name or 'default'}")

    def _initialize_phase3_components(self):
        """Initialize Phase 3 production components."""
        if PHASE3_AVAILABLE:
            try:
                # Load risk management configuration
                risk_config_path = Path('config/risk_management.json')
                if risk_config_path.exists():
                    with open(risk_config_path, 'r') as f:
                        risk_config_data = json.load(f)

                    # Extract risk limits
                    risk_config = {
                        'max_position_size': risk_config_data['position_limits']['max_position_size_usd'],
                        'max_leverage': risk_config_data['position_limits']['max_leverage'],
                        'max_daily_loss': risk_config_data['loss_limits']['max_daily_loss_usd'],
                        'max_drawdown_pct': risk_config_data['loss_limits']['max_drawdown_pct'],
                        'stop_loss_pct': risk_config_data['loss_limits']['stop_loss_pct'],
                        'take_profit_pct': risk_config_data['loss_limits']['take_profit_pct']
                    }
                else:
                    # Default risk configuration
                    risk_config = {
                        'max_position_size': 1000.0,
                        'max_leverage': 10,
                        'max_daily_loss': 500.0,
                        'max_drawdown_pct': 5.0,
                        'stop_loss_pct': 2.0,
                        'take_profit_pct': 4.0
                    }

                # Initialize production environment manager
                self.production_env_manager = ProductionEnvironmentManager()
                logger.info("[PHASE3] Production environment manager initialized")

                # Initialize advanced risk manager
                self.advanced_risk_manager = AdvancedRiskManager(risk_config)
                logger.info("[PHASE3] Advanced risk manager initialized")

                # Initialize trading analytics
                self.trading_analytics = TradingAnalytics()
                logger.info("[PHASE3] Trading analytics initialized")

                # Initialize strategy integration manager
                self.strategy_integration_manager = StrategyIntegrationManager()
                logger.info("[PHASE3] Strategy integration manager initialized")

                logger.info("[PHASE3] All Phase 3 components initialized successfully")

            except Exception as e:
                logger.error(f"[PHASE3] Failed to initialize Phase 3 components: {e}")
                self.production_env_manager = None
                self.advanced_risk_manager = None
                self.trading_analytics = None
                self.strategy_integration_manager = None
        else:
            logger.info("[PHASE3] Phase 3 components not available")
            self.production_env_manager = None
            self.advanced_risk_manager = None
            self.trading_analytics = None
            self.strategy_integration_manager = None

    def _ensure_admin_user(self):
        """Ensure admin user exists for testing."""
        try:
            if self.db_manager.connect():
                # Check if epinnox user exists and update to admin
                cursor = self.db_manager.conn.execute(
                    "SELECT id, role FROM users WHERE username = 'epinnox'"
                )
                user = cursor.fetchone()

                if user:
                    user_id, current_role = user
                    if current_role != 'admin':
                        self.db_manager.conn.execute(
                            "UPDATE users SET role = 'admin' WHERE id = ?",
                            (user_id,)
                        )
                        self.db_manager.conn.commit()
                        logger.info("[ADMIN] Updated epinnox user to admin role")
                    else:
                        logger.info("[ADMIN] Admin user epinnox already exists")
                else:
                    logger.info("[ADMIN] User epinnox not found")

                self.db_manager.conn.close()
        except Exception as e:
            logger.error(f"Error ensuring admin user: {e}")

    def _init_live_trading_for_epinnox(self):
        """Initialize live trading system specifically for epinnox account."""
        try:
            # Get HTX API credentials from environment or config
            htx_api_key = os.getenv("HTX_API_KEY", "nbtycf4rw2-72d300ec-fb900970-27ef8")
            htx_secret = os.getenv("HTX_SECRET", "b4d92e15-523563a0-72a16ad9-9a275")
            htx_passphrase = os.getenv("HTX_PASSPHRASE", "")

            # Determine if we should use live trading mode
            live_trading_mode = os.getenv("LIVE_TRADING_MODE", "false").lower() == "true"

            # Initialize HTX Futures client with live credentials
            self.htx_futures_client = HTXFuturesClient(
                api_key=htx_api_key,
                secret=htx_secret,
                passphrase=htx_passphrase,
                testnet=not live_trading_mode  # False for live trading, True for testnet
            )

            logger.info(f"[LIVE] HTX Futures client initialized - Mode: {'LIVE' if live_trading_mode else 'TESTNET'}")
            logger.info(f"[LIVE] API Key: {htx_api_key[:8]}..." if htx_api_key else "[LIVE] No API key provided")

            # Initialize advanced trading interface
            self.advanced_trading_interface = AdvancedTradingInterface(self.htx_futures_client)

            logger.info("[LIVE] Live trading system initialized for epinnox account")
            logger.info("[LIVE] HTX Futures client ready (read-only mode)")
            logger.info("[LIVE] Advanced trading interface with automation features ready")

        except Exception as e:
            logger.warning(f"[LIVE] Live trading system not available: {e}")
            self.htx_futures_client = None
            self.advanced_trading_interface = None

    async def create_app(self) -> web.Application:
        """Create and configure the web application."""
        app = web.Application()

        # Setup session middleware with encryption
        # aiohttp_session expects a 32-byte key, not base64 encoded
        if isinstance(self.config.SECRET_KEY, str):
            # Convert hex string to bytes and pad/truncate to 32 bytes
            secret_key = bytes.fromhex(self.config.SECRET_KEY)[:32]
            secret_key = secret_key.ljust(32, b'\0')  # Pad with zeros if needed
        else:
            secret_key = self.config.SECRET_KEY[:32].ljust(32, b'\0')

        setup(app, EncryptedCookieStorage(secret_key))

        # Setup Jinja2 templates
        template_path = Path(__file__).parent / 'templates'
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(template_path)),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        aiohttp_jinja2.setup(
            app,
            loader=jinja2.FileSystemLoader(str(template_path))
        )

        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Setup authentication middleware
        @web.middleware
        async def auth_middleware(request: web.Request, handler: Callable) -> web.Response:
            """Authentication middleware for aiohttp."""
            path = request.path

            # Public routes that don't require authentication
            public_routes = ['/login', '/logout', '/register', '/agreement', '/welcome', '/static/', '/favicon.ico', '/health']

            # Skip authentication for public routes
            if any(path.startswith(route) for route in public_routes):
                return await handler(request)

            # Check for session
            session_id = request.cookies.get('session_id')
            if session_id:
                session = self.user_manager.validate_session(session_id)
                if session:
                    request['user'] = session
                    request['user_id'] = session['user_id']  # Add user_id for trading API

                    # Check if user needs to complete onboarding
                    # Define routes that don't require agreement acceptance
                    agreement_exempt_routes = ['/agreement', '/welcome', '/logout']

                    # For ALL protected routes, always get fresh agreement status from database
                    if path not in agreement_exempt_routes:
                        try:
                            agreement_status = self.user_manager.get_agreement_status(session['user_id'])
                            if not agreement_status:
                                logger.info(f"[AUTH] User {session['username']} redirected to agreement from {path}")
                                return web.Response(status=302, headers={'Location': '/agreement'})
                        except Exception as e:
                            logger.error(f"[AUTH] Error checking agreement status for {session['username']}: {e}")
                            # If we can't check agreement status due to DB issues, allow access but log the error
                            logger.warning(f"[AUTH] Allowing access to {path} due to DB connection issue")

                    return await handler(request)

            # Redirect to login for protected routes
            if request.method == 'GET' and not path.startswith('/api/'):
                return web.Response(status=302, headers={'Location': '/login'})
            else:
                # Return 401 for API routes
                return web.json_response({'error': 'Authentication required'}, status=401)

        app.middlewares.append(auth_middleware)

        # Production middleware completely disabled to prevent middleware conflicts
        logger.info("[INFO] Production middleware disabled - preventing middleware conflicts")

        # Add production deployment middleware with proper wrappers
        if self.security_manager:
            # Create proper middleware wrappers to fix parameter passing
            @web.middleware
            async def rate_limit_wrapper(request, handler):
                return await self.security_manager.rate_limit_middleware(request, handler)

            @web.middleware
            async def csrf_protection_wrapper(request, handler):
                return await self.security_manager.csrf_protection_middleware(request, handler)

            @web.middleware
            async def security_headers_wrapper(request, handler):
                return await self.security_manager.security_headers_middleware(request, handler)

            app.middlewares.append(rate_limit_wrapper)
            app.middlewares.append(csrf_protection_wrapper)
            app.middlewares.append(security_headers_wrapper)
            logger.info("[OK] Security middleware enabled with proper wrappers")

        # Performance middleware disabled for now - focusing on core functionality
        logger.info("[INFO] Performance middleware disabled - core platform ready")

        # Setup routes
        self._setup_routes(app)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

        # Static files
        static_path = Path(__file__).parent / 'static'
        app.router.add_static('/static/', path=str(static_path), name='static')

        logger.info("[OK] Web application configured")
        return app

    def _setup_routes(self, app: web.Application):
        """Setup application routes."""
        # Authentication routes
        app.router.add_get('/', self.serve_home_page)
        app.router.add_get('/home', self.serve_home_page)
        app.router.add_get('/login', self.serve_login)
        app.router.add_post('/login', self.handle_login)
        app.router.add_get('/logout', self.handle_logout)
        app.router.add_get('/register', self.serve_register)
        app.router.add_post('/register', self.handle_register)
        app.router.add_get('/agreement', self.serve_agreement)
        app.router.add_post('/agreement', self.handle_agreement)
        app.router.add_get('/welcome', self.serve_welcome)

        # Dashboard routes
        app.router.add_get('/dashboard', self.personal_dashboard.serve_personal_dashboard)
        app.router.add_get('/personal', self.personal_dashboard.serve_personal_dashboard)

        # Club routes
        app.router.add_get('/club', self.club_dashboard.serve_club_dashboard)
        app.router.add_get('/club/strategies', self.enhanced_strategy_marketplace.serve_strategy_marketplace)
        app.router.add_get('/club/members', self.enhanced_member_directory.serve_member_directory)
        app.router.add_get('/club/analytics', self.enhanced_club_analytics.serve_club_analytics)

        # Phase 2 routes - Auto Trader and Signals (members and admins only)
        if self.auto_trader_dashboard:
            app.router.add_get('/auto-trader', self.auto_trader_dashboard.dashboard_page)
        if self.signals_dashboard:
            app.router.add_get('/signals', self.signals_dashboard.dashboard_page)

        # Phase 3 routes - Portfolio Analytics and Social Trading (members and admins only)
        if self.portfolio_analytics_dashboard:
            app.router.add_get('/portfolio-analytics', self.portfolio_analytics_dashboard.dashboard_page)
        if self.social_trading_dashboard:
            app.router.add_get('/social-trading', self.social_trading_dashboard.dashboard_page)

        # API routes
        app.router.add_get('/api/portfolio', self.api_get_portfolio)

        # Enhanced Exchange Management API routes
        app.router.add_post('/api/exchanges/test-connection', self.api_test_exchange_connection)
        app.router.add_post('/api/exchanges/add', self.api_add_exchange_enhanced)
        app.router.add_get('/api/exchanges/list', self.api_list_user_exchanges)
        app.router.add_get('/api/exchanges/details/{exchange_id}', self.api_get_exchange_details)
        app.router.add_post('/api/exchanges/refresh/{exchange_name}', self.api_refresh_exchange)
        app.router.add_post('/api/exchanges/balance/{exchange_id}', self.api_refresh_exchange_balance)
        app.router.add_post('/api/exchanges/test/{exchange_id}', self.api_test_existing_exchange)
        app.router.add_delete('/api/exchanges/remove/{exchange_id}', self.api_remove_exchange_enhanced)

        # Enhanced Live Trading API routes
        app.router.add_post('/api/trading/place-order', self.api_place_enhanced_order)
        app.router.add_post('/api/trading/place-market-order', self.api_place_market_order)
        app.router.add_post('/api/trading/place-limit-order', self.api_place_limit_order)
        app.router.add_post('/api/trading/place-stop-order', self.api_place_stop_order)
        app.router.add_post('/api/trading/close-position', self.api_close_position)
        app.router.add_post('/api/trading/cancel-order', self.api_cancel_order)
        app.router.add_post('/api/trading/cancel-all-orders', self.api_cancel_all_orders)
        app.router.add_get('/api/trading/positions/{exchange_name}', self.api_get_live_positions)
        app.router.add_get('/api/trading/orders/{exchange_name}', self.api_get_open_orders)
        app.router.add_get('/api/trading/orderbook/{exchange_name}/{symbol}', self.api_get_orderbook)
        app.router.add_get('/api/trading/recent-trades/{exchange_name}/{symbol}', self.api_get_recent_trades)
        app.router.add_get('/api/trading/account-info/{exchange_name}', self.api_get_account_info)

        # Legacy exchange routes (for backward compatibility)
        app.router.add_post('/api/exchange/add', self.api_add_exchange)
        app.router.add_delete('/api/exchange/{exchange_id}', self.api_remove_exchange)
        app.router.add_get('/api/balance/{exchange_name}', self.api_get_balance)
        app.router.add_get('/api/positions', self.api_get_positions)
        app.router.add_post('/api/order', self.api_place_order)

        # Enhanced Trading API routes
        from api.trading_api import TradingAPI
        self.trading_api = TradingAPI(self.db_manager)

        app.router.add_post('/api/trading/market_order', self.trading_api.market_order)
        app.router.add_post('/api/trading/limit_order', self.trading_api.limit_order)
        app.router.add_post('/api/trading/close_position', self.trading_api.close_position)
        app.router.add_get('/api/trading/position/{position_id}', self.trading_api.get_position)
        app.router.add_post('/api/trading/global_stop_loss', self.trading_api.global_stop_loss)
        app.router.add_post('/api/trading/emergency_close_all', self.trading_api.emergency_close_all)
        app.router.add_get('/api/market/data/{symbol}', self.trading_api.market_data)

        # Legacy trading routes (for backward compatibility)
        app.router.add_post('/api/trading/stop_loss', self.api_set_stop_loss)
        app.router.add_post('/api/trading/take_profit', self.api_set_take_profit)
        app.router.add_get('/api/trading/trades', self.api_get_user_trades)

        # Market data API routes
        app.router.add_get('/api/market/data/{symbol}', self.api_get_market_data)
        app.router.add_get('/api/market/orderbook/{symbol}', self.api_get_orderbook)
        app.router.add_get('/api/market/trades/{symbol}', self.api_get_recent_trades)

        # Club API routes
        # Strategy governance
        app.router.add_post('/api/club/strategy/propose', self.api_propose_strategy)
        app.router.add_post('/api/club/strategy/vote', self.api_cast_vote)
        app.router.add_post('/api/club/strategy/admin_review', self.api_admin_review_strategy)
        app.router.add_get('/api/club/strategy/{strategy_id}/discussions', self.api_get_strategy_discussions)
        app.router.add_post('/api/club/strategy/{strategy_id}/discuss', self.api_add_strategy_discussion)
        app.router.add_get('/api/club/strategy/{strategy_id}/votes', self.api_get_strategy_votes)

        # Social trading
        app.router.add_post('/api/club/social/follow_strategy', self.api_follow_strategy)
        app.router.add_post('/api/club/social/unfollow_strategy', self.api_unfollow_strategy)
        app.router.add_get('/api/club/social/activity_feed', self.api_get_activity_feed)
        app.router.add_get('/api/club/social/leaderboard', self.api_get_leaderboard)
        app.router.add_get('/api/club/social/member_profile/{user_id}', self.api_get_member_profile)
        app.router.add_post('/api/club/social/update_profile', self.api_update_member_profile)

        # Club analytics
        app.router.add_get('/api/club/analytics/overview', self.api_get_club_overview)
        app.router.add_get('/api/club/analytics/performance', self.api_get_performance_report)
        app.router.add_get('/api/club/analytics/monthly_report', self.api_get_monthly_report)

        # Notifications
        app.router.add_get('/api/club/notifications', self.api_get_notifications)
        app.router.add_post('/api/club/notifications/{notification_id}/read', self.api_mark_notification_read)

        # Enhanced notification routes for header functionality
        app.router.add_get('/api/notifications/recent', self.api_notifications_recent)
        app.router.add_post('/api/notifications/{notification_id}/read', self.api_notifications_mark_read)

        # Enhanced Strategy Marketplace API routes
        app.router.add_post('/api/strategies/{strategy_id}/follow', self.api_follow_strategy_enhanced)
        app.router.add_post('/api/strategies/{strategy_id}/unfollow', self.api_unfollow_strategy_enhanced)
        app.router.add_get('/api/strategies/{strategy_id}/details', self.api_get_strategy_details)

        # Enhanced Member Directory API routes
        app.router.add_post('/api/members/{member_id}/connect', self.api_connect_member)
        app.router.add_post('/api/members/{member_id}/disconnect', self.api_disconnect_member)
        app.router.add_get('/api/members/{member_id}/profile', self.api_get_member_profile_enhanced)
        app.router.add_post('/api/members/{member_id}/message', self.api_send_member_message)

        # Phase 2 API routes - Auto Trader and Signals
        if self.signals_dashboard:
            app.router.add_post('/api/signals/follow', self.signals_dashboard.api_follow_signal)
            app.router.add_post('/api/signals/{signal_id}/unfollow', self.signals_dashboard.api_unfollow_signal)
            app.router.add_post('/api/signals/preferences', self.signals_dashboard.api_update_signal_preferences)

        # Phase 3 API routes - Social Trading
        if self.social_trading_dashboard:
            app.router.add_post('/api/social/follow-strategy', self.social_trading_dashboard.api_follow_strategy)
            app.router.add_post('/api/social/{strategy_id}/unfollow', self.social_trading_dashboard.api_unfollow_strategy)
            app.router.add_post('/api/social/share-strategy', self.social_trading_dashboard.api_share_strategy)

        # Advanced Trading API routes (if available)
        if self.live_trading_interface:
            app.router.add_post('/api/personal/place_order', self.api_place_advanced_order)
            app.router.add_get('/api/personal/real_time_data', self.api_get_real_time_data)
            app.router.add_get('/api/personal/risk_metrics', self.api_get_risk_metrics)
            app.router.add_get('/api/personal/active_orders', self.api_get_active_orders)

        # Strategy Engine API routes (if available)
        if self.live_strategy_engine:
            app.router.add_post('/api/strategies/register', self.api_register_strategy)
            app.router.add_post('/api/strategies/{strategy_id}/activate', self.api_activate_strategy)
            app.router.add_post('/api/strategies/{strategy_id}/pause', self.api_pause_strategy)
            app.router.add_get('/api/strategies/performance', self.api_get_strategy_performance)

        # Market Data API routes (if available)
        if self.market_data_api:
            self.market_data_api.setup_routes(app)
            logger.info("[OK] Market Data API routes configured")

        # WebSocket routes (if available)
        if self.websocket_streamer:
            # Setup WebSocket routes synchronously
            app.router.add_get('/ws/market', self.websocket_streamer.handle_market_websocket)
            app.router.add_get('/ws/market/{symbol}', self.websocket_streamer.handle_symbol_websocket)
            logger.info("[OK] Market Data WebSocket routes configured")

        # Legacy WebSocket
        app.router.add_get('/ws', self.websocket_handler)

        # Admin routes (admin only)
        app.router.add_get('/admin', self.serve_admin_dashboard_with_auth)
        app.router.add_get('/api/admin/users', self.api_get_users)
        app.router.add_post('/api/admin/user/{user_id}/role', self.api_update_user_role)
        app.router.add_get('/api/notifications/count', self.api_get_notification_count)
        app.router.add_get('/api/user/current', self.api_get_current_user)

        # Production deployment routes
        app.router.add_get('/health', self.api_health_check)
        app.router.add_get('/api/system/status', self.api_system_status)
        app.router.add_get('/api/system/metrics', self.api_system_metrics)
        app.router.add_post('/api/system/backup', self.api_create_backup)
        app.router.add_get('/api/system/backups', self.api_list_backups)
        app.router.add_post('/api/system/restore/{backup_name}', self.api_restore_backup)

        # Phase 3 API endpoints
        if PHASE3_AVAILABLE:
            # Risk management endpoints
            app.router.add_get('/api/risk/summary', self.api_risk_summary)
            app.router.add_get('/api/risk/metrics', self.api_risk_metrics)
            app.router.add_post('/api/risk/update-limits', self.api_update_risk_limits)

            # Trading analytics endpoints
            app.router.add_get('/api/analytics/performance', self.api_analytics_performance)
            app.router.add_get('/api/analytics/trades', self.api_analytics_trades)
            app.router.add_get('/api/analytics/report', self.api_analytics_report)

            # Strategy management endpoints
            app.router.add_get('/api/strategies/status', self.api_strategies_status)
            app.router.add_post('/api/strategies/start', self.api_strategies_start)
            app.router.add_post('/api/strategies/stop', self.api_strategies_stop)
            app.router.add_get('/api/strategies/signals', self.api_strategies_signals)

            # Production environment endpoints
            app.router.add_get('/api/production/environment', self.api_production_environment)
            app.router.add_get('/api/production/config', self.api_production_config)

            logger.info("[PHASE3] Phase 3 API endpoints configured")

        # Symbol and market data endpoints
        app.router.add_get('/api/symbols', self.api_get_symbols)
        app.router.add_get('/api/market/ticker/{symbol}', self.api_get_market_ticker)
        app.router.add_get('/api/market/data/{symbol}', self.api_get_market_data)

        # Live Trading routes (epinnox admin only)
        if self.htx_futures_client and self.advanced_trading_interface:
            app.router.add_get('/live-trading', self.serve_live_trading)
            app.router.add_get('/ws/live-trading', self.handle_live_trading_websocket)
            app.router.add_post('/api/live-trading/order', self.api_place_live_order)
            app.router.add_post('/api/live-trading/close-position', self.api_close_live_position)
            app.router.add_post('/api/live-trading/emergency-close', self.api_emergency_close_all)
            app.router.add_post('/api/live-trading/automation', self.api_update_automation)
            app.router.add_get('/api/live-trading/state', self.api_get_trading_state)
            logger.info("[LIVE] Live trading routes configured")

        logger.info("[OK] Routes configured")

    async def serve_home_page(self, request: web.Request) -> web.Response:
        """Serve the Money Circle home page."""
        try:
            # Check if user is already logged in
            user = request.get('user')

            if user:
                # User is logged in, redirect to dashboard
                return web.Response(status=302, headers={'Location': '/dashboard'})

            # User is not logged in, show home page
            template = self.jinja_env.get_template('home.html')
            html_content = template.render(
                request=request,
                page_title='Money Circle - Professional Investment Club Platform'
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Home page error: {e}")
            # Fallback to login page if home page fails
            return web.Response(status=302, headers={'Location': '/login'})

    async def redirect_to_dashboard(self, request: web.Request) -> web.Response:
        """Redirect root to dashboard (legacy function)."""
        return web.Response(status=302, headers={'Location': '/dashboard'})

    async def serve_login(self, request: web.Request) -> web.Response:
        """Serve login page."""
        try:
            # Get error message from query params
            error = request.query.get('error', '')
            error_messages = {
                'missing_fields': 'Please fill in all required fields.',
                'invalid_credentials': 'Invalid username or password. Please try again.',
                'server_error': 'Login failed. Please try again later.',
                'csrf_error': 'Security token missing. Please try again.'
            }

            error_text = error_messages.get(error, '')

            # Generate CSRF token for the form
            csrf_token = self.security_manager.generate_csrf_token("anonymous")

            # Load and render template
            template = self.jinja_env.get_template('login.html')
            html_content = template.render(
                error=error_text,
                csrf_token=csrf_token,
                request=request
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Login page error: {e}")
            # Fallback to simple login page
            html_content = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Money Circle - Login</title>
                <link rel="stylesheet" href="/static/css/auth.css">
            </head>
            <body>
                <div class="auth-container">
                    <div class="auth-card">
                        <h1>💰 Money Circle</h1>
                        <h2>Investment Club Login</h2>
                        <form method="post" action="/login">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" id="password" name="password" required>
                            </div>
                            <button type="submit" class="auth-btn">Login</button>
                        </form>
                        <p><a href="/register">Don't have an account? Register here</a></p>
                    </div>
                </div>
            </body>
            </html>
            """
            return web.Response(text=html_content, content_type='text/html')

    async def handle_login(self, request: web.Request) -> web.Response:
        """Handle login form submission."""
        try:
            data = await request.post()
            username = data.get('username', '').strip()
            password = data.get('password', '')
            csrf_token = data.get('csrf_token', '')

            # Check for missing fields
            if not username or not password:
                return web.Response(status=302, headers={'Location': '/login?error=missing_fields'})

            # CSRF token validation is handled by middleware, but we can add extra validation here if needed
            if not csrf_token:
                logger.warning(f"[SECURITY] Login attempt without CSRF token from {request.remote}")
                return web.Response(status=302, headers={'Location': '/login?error=csrf_error'})

            # Get client IP for rate limiting
            ip_address = request.remote or '127.0.0.1'

            # Authenticate user
            user = self.user_manager.authenticate_user(username, password, ip_address)
            if not user:
                return web.Response(status=302, headers={'Location': '/login?error=invalid_credentials'})

            # Create session
            session_id = self.user_manager.create_session(user)

            # Set session cookie and redirect
            response = web.Response(status=302, headers={'Location': '/dashboard'})
            response.set_cookie(
                'session_id',
                session_id,
                max_age=self.config.SESSION_TIMEOUT,
                httponly=True,
                secure=not self.config.DEBUG
            )

            logger.info(f"[OK] User logged in: {username}")
            return response

        except Exception as e:
            logger.error(f"Login error: {e}")
            return web.Response(status=302, headers={'Location': '/login?error=server_error'})

    async def handle_logout(self, request: web.Request) -> web.Response:
        """Handle logout."""
        session_id = request.cookies.get('session_id')
        if session_id:
            self.user_manager.destroy_session(session_id)

        response = web.Response(status=302, headers={'Location': '/login'})
        response.del_cookie('session_id')
        return response

    async def serve_register(self, request: web.Request) -> web.Response:
        """Serve registration page."""
        try:
            # Get error message from query params
            error = request.query.get('error', '')
            error_messages = {
                'missing_fields': 'Please fill in all required fields.',
                'password_mismatch': 'Passwords do not match.',
                'rate_limit': 'Too many registration attempts. Please try again later.',
                'server_error': 'Registration failed. Please try again.',
                'csrf_error': 'Security token missing. Please try again.'
            }

            error_text = error_messages.get(error, '')

            # Preserve form data on error
            username = request.query.get('username', '')
            email = request.query.get('email', '')

            # Generate CSRF token for the form
            csrf_token = self.security_manager.generate_csrf_token("anonymous")

            # Load and render template
            template = self.jinja_env.get_template('register.html')
            html_content = template.render(
                error=error_text,
                username=username,
                email=email,
                csrf_token=csrf_token
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Registration page error: {e}")
            return web.Response(text="Registration temporarily unavailable", status=500)

    async def handle_register(self, request: web.Request) -> web.Response:
        """Handle registration form submission."""
        try:
            data = await request.post()
            username = data.get('username', '').strip()
            email = data.get('email', '').strip()
            password = data.get('password', '')
            confirm_password = data.get('confirm_password', '')

            # Basic validation
            if not all([username, email, password, confirm_password]):
                return web.Response(status=302, headers={
                    'Location': f'/register?error=missing_fields&username={username}&email={email}'
                })

            if password != confirm_password:
                return web.Response(status=302, headers={
                    'Location': f'/register?error=password_mismatch&username={username}&email={email}'
                })

            # Get client IP for rate limiting
            ip_address = request.remote or '127.0.0.1'

            # Register user
            success, message, user = self.user_manager.register_user(username, email, password, ip_address)

            if not success:
                if 'rate limit' in message.lower():
                    error_code = 'rate_limit'
                else:
                    error_code = 'server_error'

                return web.Response(status=302, headers={
                    'Location': f'/register?error={error_code}&username={username}&email={email}'
                })

            # Auto-login the new user
            session_id = self.user_manager.create_session(user)

            # Set session cookie and redirect to agreement
            response = web.Response(status=302, headers={'Location': '/agreement'})
            response.set_cookie(
                'session_id',
                session_id,
                max_age=self.config.SESSION_TIMEOUT,
                httponly=True,
                secure=not self.config.DEBUG
            )

            logger.info(f"[OK] New user registered and logged in: {username}")
            return response

        except Exception as e:
            logger.error(f"Registration error: {e}")
            return web.Response(status=302, headers={'Location': '/register?error=server_error'})

    async def serve_agreement(self, request: web.Request) -> web.Response:
        """Serve membership agreement page."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get error message from query params
            error = request.query.get('error', '')
            error_messages = {
                'missing_fields': 'Please complete all required fields and checkboxes.',
                'invalid_signature': 'Please enter your full legal name as your digital signature.',
                'server_error': 'Agreement processing failed. Please try again.'
            }

            error_text = error_messages.get(error, '')

            # Generate CSRF token for the form
            csrf_token = self.security_manager.generate_csrf_token(session_id)

            # Load and render template
            template = self.jinja_env.get_template('agreement.html')
            html_content = template.render(
                error=error_text,
                user=session,
                csrf_token=csrf_token
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Agreement page error: {e}")
            return web.Response(text="Agreement page temporarily unavailable", status=500)

    async def handle_agreement(self, request: web.Request) -> web.Response:
        """Handle agreement acceptance."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            data = await request.post()

            # Validate CSRF token
            csrf_token = data.get('csrf_token', '')
            if not self.security_manager.validate_csrf_token(session_id, csrf_token):
                return web.json_response({'error': 'CSRF token required'}, status=403)

            # Validate required fields
            required_checkboxes = ['agreement_read', 'risk_acknowledgment', 'age_confirmation']
            digital_signature = data.get('digital_signature', '').strip()

            # Check all checkboxes are checked
            for checkbox in required_checkboxes:
                if not data.get(checkbox):
                    return web.Response(status=302, headers={'Location': '/agreement?error=missing_fields'})

            # Validate digital signature
            if not digital_signature or len(digital_signature) < 2:
                return web.Response(status=302, headers={'Location': '/agreement?error=invalid_signature'})

            # Get client info for legal compliance
            ip_address = request.remote or '127.0.0.1'
            user_agent = request.headers.get('User-Agent', '')

            # Create agreement text for storage
            agreement_text = f"""
Money Circle Investment Club Membership Agreement v1.0

Digital Signature: {digital_signature}
Agreement Read: {data.get('agreement_read') == 'on'}
Risk Acknowledgment: {data.get('risk_acknowledgment') == 'on'}
Age Confirmation: {data.get('age_confirmation') == 'on'}

User Agent: {user_agent}
IP Address: {ip_address}
Timestamp: {datetime.now().isoformat()}
            """.strip()

            # Record agreement acceptance
            success = self.user_manager.record_agreement_acceptance(
                user_id=session['user_id'],
                agreement_text=agreement_text,
                ip_address=ip_address,
                user_agent=user_agent
            )

            if not success:
                return web.Response(status=302, headers={'Location': '/agreement?error=server_error'})

            # Update last login
            self.user_manager.update_last_login(session['user_id'])

            logger.info(f"✅ Agreement accepted by user: {session['username']}")

            # Redirect to welcome page
            return web.Response(status=302, headers={'Location': '/welcome'})

        except Exception as e:
            logger.error(f"Agreement processing error: {e}")
            return web.Response(status=302, headers={'Location': '/agreement?error=server_error'})

    async def serve_welcome(self, request: web.Request) -> web.Response:
        """Serve welcome page after successful registration and agreement."""
        try:
            # Check if user is logged in
            session_id = request.cookies.get('session_id')
            if not session_id:
                return web.Response(status=302, headers={'Location': '/login'})

            session = self.user_manager.validate_session(session_id)
            if not session:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get fresh user information from database
            user = self.user_manager.get_user_by_id(session['user_id'])
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Double-check agreement status from database (fresh data)
            agreement_status = self.user_manager.get_agreement_status(session['user_id'])
            if not agreement_status:
                return web.Response(status=302, headers={'Location': '/agreement'})

            # Load and render template
            template = self.jinja_env.get_template('welcome.html')
            html_content = template.render(
                user=user,
                session=session
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Welcome page error: {e}")
            return web.Response(text="Welcome page temporarily unavailable", status=500)

    async def websocket_handler(self, request: web.Request) -> web.WebSocketResponse:
        """Handle WebSocket connections."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.add(ws)
        logger.info(f"[WS] WebSocket connected (total: {len(self.websockets)})")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # Handle incoming messages
                    pass
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websockets.discard(ws)
            logger.info(f"[WS] WebSocket disconnected (total: {len(self.websockets)})")

        return ws

    # API endpoints implementation
    async def api_get_portfolio(self, request: web.Request) -> web.Response:
        """API endpoint to get user portfolio."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            portfolio_data = await self.personal_dashboard._get_portfolio_data(user_id)

            return web.json_response({
                'success': True,
                'portfolio': portfolio_data
            })

        except Exception as e:
            logger.error(f"Portfolio API error: {e}")
            return web.json_response({'error': 'Failed to fetch portfolio'}, status=500)

    async def api_add_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to add exchange account."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'api_key', 'secret_key']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Add exchange account
            success = self.exchange_manager.add_exchange_account(
                user_id=user_id,
                exchange_name=data['exchange'],
                api_key=data['api_key'],
                secret_key=data['secret_key'],
                passphrase=data.get('passphrase')
            )

            if success:
                return web.json_response({'success': True, 'message': 'Exchange account added'})
            else:
                return web.json_response({'error': 'Failed to add exchange account'}, status=400)

        except Exception as e:
            logger.error(f"Add exchange API error: {e}")
            return web.json_response({'error': 'Failed to add exchange account'}, status=500)

    async def api_remove_exchange(self, request: web.Request) -> web.Response:
        """API endpoint to remove exchange account."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])
            user_id = user['user_id']

            success = self.exchange_manager.remove_exchange_account(user_id, exchange_id)

            if success:
                return web.json_response({'success': True, 'message': 'Exchange account removed'})
            else:
                return web.json_response({'error': 'Failed to remove exchange account'}, status=400)

        except Exception as e:
            logger.error(f"Remove exchange API error: {e}")
            return web.json_response({'error': 'Failed to remove exchange account'}, status=500)

    async def api_get_balance(self, request: web.Request) -> web.Response:
        """API endpoint to get exchange balance."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']
            user_id = user['user_id']

            balance = self.exchange_manager.get_user_balance(user_id, exchange_name)

            if balance:
                return web.json_response({'success': True, 'balance': balance})
            else:
                return web.json_response({'error': 'Failed to fetch balance'}, status=400)

        except Exception as e:
            logger.error(f"Balance API error: {e}")
            return web.json_response({'error': 'Failed to fetch balance'}, status=500)

    async def api_get_positions(self, request: web.Request) -> web.Response:
        """API endpoint to get user positions."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']

            # Get positions from all connected exchanges
            exchanges = self.exchange_manager.get_user_exchanges(user_id)
            all_positions = []

            for exchange_account in exchanges:
                positions = self.exchange_manager.get_user_positions(user_id, exchange_account.exchange_name)
                all_positions.extend(positions)

            return web.json_response({'success': True, 'positions': all_positions})

        except Exception as e:
            logger.error(f"Positions API error: {e}")
            return web.json_response({'error': 'Failed to fetch positions'}, status=500)

    async def api_place_order(self, request: web.Request) -> web.Response:
        """API endpoint to place order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount', 'order_type']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place order
            order = self.exchange_manager.place_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                order_type=data['order_type'],
                side=data['side'],
                amount=float(data['amount']),
                price=float(data['price']) if data.get('price') else None
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place order'}, status=400)

        except Exception as e:
            logger.error(f"Place order API error: {e}")
            return web.json_response({'error': 'Failed to place order'}, status=500)

    # Trading API endpoints
    async def api_place_market_order(self, request: web.Request) -> web.Response:
        """API endpoint to place market order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place market order through personal trader
            order = await self.personal_dashboard.personal_trader.place_market_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                side=data['side'],
                amount=float(data['amount']),
                strategy_name=data.get('strategy', 'Manual')
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place market order'}, status=400)

        except Exception as e:
            logger.error(f"Market order API error: {e}")
            return web.json_response({'error': 'Failed to place market order'}, status=500)

    async def api_place_limit_order(self, request: web.Request) -> web.Response:
        """API endpoint to place limit order."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount', 'price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Place limit order through personal trader
            order = await self.personal_dashboard.personal_trader.place_limit_order(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                side=data['side'],
                amount=float(data['amount']),
                price=float(data['price']),
                strategy_name=data.get('strategy', 'Manual')
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place limit order'}, status=400)

        except Exception as e:
            logger.error(f"Limit order API error: {e}")
            return web.json_response({'error': 'Failed to place limit order'}, status=500)

    async def api_close_position(self, request: web.Request) -> web.Response:
        """API endpoint to close position."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['exchange', 'symbol']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Close position through personal trader
            order = await self.personal_dashboard.personal_trader.close_position(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                percentage=float(data.get('percentage', 100.0))
            )

            if order:
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to close position'}, status=400)

        except Exception as e:
            logger.error(f"Close position API error: {e}")
            return web.json_response({'error': 'Failed to close position'}, status=500)

    async def api_set_stop_loss(self, request: web.Request) -> web.Response:
        """API endpoint to set stop loss."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            required_fields = ['exchange', 'symbol', 'stop_price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            success = await self.personal_dashboard.personal_trader.set_stop_loss(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                stop_price=float(data['stop_price'])
            )

            if success:
                return web.json_response({'success': True, 'message': 'Stop loss set'})
            else:
                return web.json_response({'error': 'Failed to set stop loss'}, status=400)

        except Exception as e:
            logger.error(f"Stop loss API error: {e}")
            return web.json_response({'error': 'Failed to set stop loss'}, status=500)

    async def api_set_take_profit(self, request: web.Request) -> web.Response:
        """API endpoint to set take profit."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            required_fields = ['exchange', 'symbol', 'target_price']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            success = await self.personal_dashboard.personal_trader.set_take_profit(
                user_id=user_id,
                exchange_name=data['exchange'],
                symbol=data['symbol'],
                target_price=float(data['target_price'])
            )

            if success:
                return web.json_response({'success': True, 'message': 'Take profit set'})
            else:
                return web.json_response({'error': 'Failed to set take profit'}, status=400)

        except Exception as e:
            logger.error(f"Take profit API error: {e}")
            return web.json_response({'error': 'Failed to set take profit'}, status=500)

    async def api_get_user_trades(self, request: web.Request) -> web.Response:
        """API endpoint to get user trades."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            limit = int(request.query.get('limit', 50))

            trades = await self.personal_dashboard.personal_trader.get_user_trades(user_id, limit)

            return web.json_response({'success': True, 'trades': trades})

        except Exception as e:
            logger.error(f"Get trades API error: {e}")
            return web.json_response({'error': 'Failed to get trades'}, status=500)

    # Market data API endpoints
    async def api_get_market_data(self, request: web.Request) -> web.Response:
        """API endpoint to get market data."""
        try:
            symbol = request.match_info['symbol']

            market_data = await self.personal_dashboard.market_data_bus.get_latest_market_data(symbol)

            if market_data:
                return web.json_response({'success': True, 'data': market_data})
            else:
                return web.json_response({'error': 'No market data available'}, status=404)

        except Exception as e:
            logger.error(f"Market data API error: {e}")
            return web.json_response({'error': 'Failed to get market data'}, status=500)

    async def api_get_orderbook(self, request: web.Request) -> web.Response:
        """API endpoint to get order book."""
        try:
            symbol = request.match_info['symbol']

            orderbook = await self.personal_dashboard.market_data_bus.get_latest_orderbook(symbol)

            if orderbook:
                return web.json_response({'success': True, 'orderbook': orderbook})
            else:
                return web.json_response({'error': 'No orderbook data available'}, status=404)

        except Exception as e:
            logger.error(f"Orderbook API error: {e}")
            return web.json_response({'error': 'Failed to get orderbook'}, status=500)

    async def api_get_recent_trades(self, request: web.Request) -> web.Response:
        """API endpoint to get recent trades."""
        try:
            symbol = request.match_info['symbol']
            limit = int(request.query.get('limit', 20))

            trades = await self.personal_dashboard.market_data_bus.get_recent_trades(symbol, limit)

            return web.json_response({'success': True, 'trades': trades})

        except Exception as e:
            logger.error(f"Recent trades API error: {e}")
            return web.json_response({'error': 'Failed to get recent trades'}, status=500)

    async def serve_admin_dashboard_with_auth(self, request: web.Request) -> web.Response:
        """Serve admin dashboard with role-based access control."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Check if user is admin
            if user.get('role') != 'admin':
                # Redirect non-admin users to dashboard
                return web.Response(status=302, headers={'Location': '/dashboard'})

            # User is admin, serve the admin dashboard
            return await self.admin_dashboard.dashboard_page(request)

        except Exception as e:
            logger.error(f"Admin dashboard error: {e}")
            return web.Response(status=302, headers={'Location': '/dashboard'})

    async def serve_admin_dashboard(self, request: web.Request) -> web.Response:
        """Serve admin dashboard."""
        return web.Response(text="Admin dashboard - Coming soon!", content_type='text/html')

    async def api_get_users(self, request: web.Request) -> web.Response:
        """API endpoint to get all users (admin only)."""
        try:
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if not self.db_manager.connect():
                return web.json_response({'error': 'Database connection failed'}, status=500)

            # Get pagination parameters
            page = int(request.query.get('page', 1))
            limit = int(request.query.get('limit', 20))
            offset = (page - 1) * limit

            # Get users with pagination
            cursor = self.db_manager.conn.execute("""
                SELECT id, username, email, role, date_joined, last_login, is_active
                FROM users
                ORDER BY date_joined DESC
                LIMIT ? OFFSET ?
            """, (limit, offset))

            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'role': row[3],
                    'date_joined': row[4],
                    'last_login': row[5],
                    'is_active': bool(row[6])
                })

            # Get total count
            cursor = self.db_manager.conn.execute("SELECT COUNT(*) FROM users")
            total_count = cursor.fetchone()[0]

            return web.json_response({
                'users': users,
                'total': total_count,
                'page': page,
                'limit': limit,
                'total_pages': (total_count + limit - 1) // limit
            })

        except Exception as e:
            logger.error(f"Get users API error: {e}")
            return web.json_response({'error': 'Failed to get users'}, status=500)
        finally:
            if self.db_manager.conn:
                self.db_manager.conn.close()

    async def api_update_user_role(self, request: web.Request) -> web.Response:
        """API endpoint to update user role (admin only)."""
        try:
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            user_id = request.match_info['user_id']
            data = await request.json()
            new_role = data.get('role')

            if new_role not in ['admin', 'member', 'viewer']:
                return web.json_response({'error': 'Invalid role'}, status=400)

            if not self.db_manager.connect():
                return web.json_response({'error': 'Database connection failed'}, status=500)

            # Update user role
            cursor = self.db_manager.conn.execute("""
                UPDATE users SET role = ? WHERE id = ?
            """, (new_role, user_id))

            if cursor.rowcount == 0:
                return web.json_response({'error': 'User not found'}, status=404)

            self.db_manager.conn.commit()

            # Get updated user info
            cursor = self.db_manager.conn.execute("""
                SELECT username, email, role FROM users WHERE id = ?
            """, (user_id,))

            updated_user = cursor.fetchone()

            return web.json_response({
                'message': 'User role updated successfully',
                'user': {
                    'id': user_id,
                    'username': updated_user[0],
                    'email': updated_user[1],
                    'role': updated_user[2]
                }
            })

        except Exception as e:
            logger.error(f"Update user role API error: {e}")
            return web.json_response({'error': 'Failed to update user role'}, status=500)
        finally:
            if self.db_manager.conn:
                self.db_manager.conn.close()

    async def api_get_current_user(self, request: web.Request) -> web.Response:
        """API endpoint to get current user data."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Not authenticated'}, status=401)

            return web.json_response({
                'username': user.get('username'),
                'email': user.get('email'),
                'role': user.get('role'),
                'user_id': user.get('user_id')
            })
        except Exception as e:
            logger.error(f"Get current user API error: {e}")
            return web.json_response({'error': 'Failed to get user data'}, status=500)

    async def api_get_notification_count(self, request: web.Request) -> web.Response:
        """API endpoint to get notification count."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Not authenticated'}, status=401)

            user_id = user.get('user_id')
            if not self.db_manager.connect():
                return web.json_response({'error': 'Database connection failed'}, status=500)

            # Check if notifications table exists
            count = 0
            try:
                cursor = self.db_manager.conn.execute("""
                    SELECT COUNT(*) FROM notifications
                    WHERE user_id = ? AND is_read = FALSE
                """, (user_id,))
                result = cursor.fetchone()
                count = result[0] if result else 0
            except sqlite3.OperationalError:
                # Notifications table doesn't exist yet
                count = 0

            return web.json_response({'count': count})

        except Exception as e:
            logger.error(f"Get notification count API error: {e}")
            return web.json_response({'error': 'Failed to get notification count'}, status=500)
        finally:
            if self.db_manager.conn:
                self.db_manager.conn.close()

    # Club page routes
    async def serve_strategy_marketplace(self, request: web.Request) -> web.Response:
        """Serve strategy marketplace page."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get all approved strategies
            approved_strategies = self.strategy_governance.get_approved_strategies()

            # Get user's followed strategies
            user_followed = []
            try:
                cursor = self.db_manager.conn.execute("""
                    SELECT strategy_id FROM strategy_following
                    WHERE user_id = ? AND is_active = TRUE
                """, (user['user_id'],))
                user_followed = [row[0] for row in cursor.fetchall()]
            except:
                pass

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Strategy Marketplace - Money Circle</title>
                <link rel="stylesheet" href="/static/css/dashboard.css">
                <link rel="stylesheet" href="/static/css/club.css">
            </head>
            <body>
                <div class="marketplace-container">
                    <header class="marketplace-header">
                        <div class="header-content">
                            <h1>🎯 Strategy Marketplace</h1>
                            <p>Discover and follow proven trading strategies from our community</p>
                        </div>
                        <nav class="breadcrumb">
                            <a href="/dashboard">Dashboard</a> →
                            <a href="/club">Club</a> →
                            <span>Strategy Marketplace</span>
                        </nav>
                    </header>

                    <div class="marketplace-filters">
                        <div class="filter-group">
                            <label>Risk Level:</label>
                            <select id="riskFilter">
                                <option value="">All Risk Levels</option>
                                <option value="low">Low Risk</option>
                                <option value="medium">Medium Risk</option>
                                <option value="high">High Risk</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Strategy Type:</label>
                            <select id="typeFilter">
                                <option value="">All Types</option>
                                <option value="scalping">Scalping</option>
                                <option value="swing">Swing Trading</option>
                                <option value="trend">Trend Following</option>
                                <option value="arbitrage">Arbitrage</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Sort By:</label>
                            <select id="sortFilter">
                                <option value="performance">Performance</option>
                                <option value="followers">Most Followed</option>
                                <option value="recent">Recently Added</option>
                            </select>
                        </div>
                    </div>

                    <div class="strategies-grid">
                        {self._render_strategy_cards(approved_strategies, user_followed)}
                    </div>
                </div>

                <script>
                    function followStrategy(strategyId) {{
                        fetch('/api/club/social/follow_strategy', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}},
                            body: JSON.stringify({{strategy_id: strategyId}})
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                location.reload();
                            }} else {{
                                alert('Failed to follow strategy');
                            }}
                        }});
                    }}

                    function unfollowStrategy(strategyId) {{
                        fetch('/api/club/social/unfollow_strategy', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}},
                            body: JSON.stringify({{strategy_id: strategyId}})
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                location.reload();
                            }} else {{
                                alert('Failed to unfollow strategy');
                            }}
                        }});
                    }}
                </script>
            </body>
            </html>
            """

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Strategy marketplace error: {e}")
            return web.Response(text="Strategy marketplace temporarily unavailable", status=500)

    def _render_strategy_cards(self, strategies: List[Dict], user_followed: List[int]) -> str:
        """Render strategy cards for marketplace."""
        if not strategies:
            return '<div class="no-strategies">No strategies available yet. Be the first to propose one!</div>'

        cards_html = ""
        for strategy in strategies:
            is_followed = strategy.get('id') in user_followed
            follow_button = f"""
                <button class="btn-secondary" onclick="unfollowStrategy({strategy.get('id')})">
                    ✓ Following
                </button>
            """ if is_followed else f"""
                <button class="btn-primary" onclick="followStrategy({strategy.get('id')})">
                    Follow Strategy
                </button>
            """

            cards_html += f"""
            <div class="strategy-card">
                <div class="strategy-header">
                    <h3>{strategy.get('title', 'Untitled Strategy')}</h3>
                    <span class="risk-badge risk-{strategy.get('risk_level', 'medium')}">{strategy.get('risk_level', 'Medium').title()}</span>
                </div>
                <div class="strategy-meta">
                    <span class="strategy-type">{strategy.get('strategy_type', 'Unknown').title()}</span>
                    <span class="followers-count">{strategy.get('follower_count', 0)} followers</span>
                </div>
                <p class="strategy-description">{strategy.get('description', 'No description available.')[:150]}...</p>
                <div class="strategy-stats">
                    <div class="stat">
                        <label>Expected Return:</label>
                        <span class="positive">{strategy.get('expected_return', 0):.1f}%</span>
                    </div>
                    <div class="stat">
                        <label>Max Drawdown:</label>
                        <span class="negative">{strategy.get('max_drawdown', 0):.1f}%</span>
                    </div>
                    <div class="stat">
                        <label>Time Horizon:</label>
                        <span>{strategy.get('time_horizon', 'Unknown')}</span>
                    </div>
                </div>
                <div class="strategy-actions">
                    {follow_button}
                    <button class="btn-outline" onclick="viewStrategyDetails({strategy.get('id')})">
                        View Details
                    </button>
                </div>
            </div>
            """

        return cards_html

    async def serve_member_directory(self, request: web.Request) -> web.Response:
        """Serve member directory page."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get member leaderboard and profiles
            leaderboard = self.social_trading.get_member_leaderboard('reputation', 'all_time', 50)

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Member Directory - Money Circle</title>
                <link rel="stylesheet" href="/static/css/dashboard.css">
                <link rel="stylesheet" href="/static/css/club.css">
            </head>
            <body>
                <div class="directory-container">
                    <header class="directory-header">
                        <div class="header-content">
                            <h1>👥 Member Directory</h1>
                            <p>Connect with fellow Money Circle investment club members</p>
                        </div>
                        <nav class="breadcrumb">
                            <a href="/dashboard">Dashboard</a> →
                            <a href="/club">Club</a> →
                            <span>Member Directory</span>
                        </nav>
                    </header>

                    <div class="directory-filters">
                        <div class="filter-group">
                            <label>Sort By:</label>
                            <select id="sortFilter">
                                <option value="reputation">Reputation</option>
                                <option value="performance">Performance</option>
                                <option value="activity">Recent Activity</option>
                                <option value="joined">Recently Joined</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Role:</label>
                            <select id="roleFilter">
                                <option value="">All Members</option>
                                <option value="admin">Administrators</option>
                                <option value="member">Members</option>
                                <option value="viewer">Viewers</option>
                            </select>
                        </div>
                        <div class="search-group">
                            <input type="text" id="memberSearch" placeholder="Search members...">
                        </div>
                    </div>

                    <div class="members-grid">
                        {self._render_member_cards(leaderboard)}
                    </div>
                </div>

                <script>
                    function viewMemberProfile(userId) {{
                        window.location.href = `/club/member/${{userId}}`;
                    }}

                    function followMember(userId) {{
                        // Implement member following functionality
                        alert('Member following feature coming soon!');
                    }}
                </script>
            </body>
            </html>
            """

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Member directory error: {e}")
            return web.Response(text="Member directory temporarily unavailable", status=500)

    def _render_member_cards(self, members: List[Dict]) -> str:
        """Render member cards for directory."""
        if not members:
            return '<div class="no-members">No members found.</div>'

        cards_html = ""
        for member in members:
            reputation_score = member.get('reputation_score', 0)
            performance = member.get('performance', 0)

            cards_html += f"""
            <div class="member-card">
                <div class="member-avatar">
                    <div class="avatar-circle">{member.get('username', 'U')[0].upper()}</div>
                </div>
                <div class="member-info">
                    <h3>{member.get('username', 'Unknown')}</h3>
                    <span class="member-role">{member.get('role', 'member').title()}</span>
                    <div class="member-stats">
                        <div class="stat">
                            <label>Reputation:</label>
                            <span class="reputation-score">{reputation_score:.0f}</span>
                        </div>
                        <div class="stat">
                            <label>Performance:</label>
                            <span class="{'positive' if performance >= 0 else 'negative'}">{performance:.1f}%</span>
                        </div>
                        <div class="stat">
                            <label>Strategies:</label>
                            <span>{member.get('strategy_count', 0)}</span>
                        </div>
                    </div>
                    <div class="member-actions">
                        <button class="btn-primary" onclick="viewMemberProfile({member.get('user_id', 0)})">
                            View Profile
                        </button>
                        <button class="btn-outline" onclick="followMember({member.get('user_id', 0)})">
                            Follow
                        </button>
                    </div>
                </div>
            </div>
            """

        return cards_html

    async def serve_club_analytics(self, request: web.Request) -> web.Response:
        """Serve club analytics page."""
        try:
            user = request.get('user')
            if not user:
                return web.Response(status=302, headers={'Location': '/login'})

            # Get club analytics data
            overview = self.club_analytics.generate_club_overview()
            performance_report = self.club_analytics.generate_performance_report(30)

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Club Analytics - Money Circle</title>
                <link rel="stylesheet" href="/static/css/dashboard.css">
                <link rel="stylesheet" href="/static/css/club.css">
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            </head>
            <body>
                <div class="analytics-container">
                    <header class="analytics-header">
                        <div class="header-content">
                            <h1>📊 Club Analytics</h1>
                            <p>Comprehensive performance insights for Money Circle Investment Club</p>
                        </div>
                        <nav class="breadcrumb">
                            <a href="/dashboard">Dashboard</a> →
                            <a href="/club">Club</a> →
                            <span>Analytics</span>
                        </nav>
                    </header>

                    <div class="analytics-overview">
                        <div class="metric-card">
                            <h3>Total Members</h3>
                            <div class="metric-value">{overview.get('total_members', 0)}</div>
                            <div class="metric-change positive">+{overview.get('new_members_this_month', 0)} this month</div>
                        </div>
                        <div class="metric-card">
                            <h3>Active Strategies</h3>
                            <div class="metric-value">{overview.get('active_strategies', 0)}</div>
                            <div class="metric-change">{overview.get('strategies_this_month', 0)} new this month</div>
                        </div>
                        <div class="metric-card">
                            <h3>Total Portfolio Value</h3>
                            <div class="metric-value">${overview.get('total_portfolio_value', 0):,.2f}</div>
                            <div class="metric-change {'positive' if overview.get('portfolio_change', 0) >= 0 else 'negative'}">
                                {overview.get('portfolio_change', 0):+.2f}% this month
                            </div>
                        </div>
                        <div class="metric-card">
                            <h3>Club Performance</h3>
                            <div class="metric-value">{performance_report.get('total_return', 0):.2f}%</div>
                            <div class="metric-change {'positive' if performance_report.get('total_return', 0) >= 0 else 'negative'}">
                                30-day return
                            </div>
                        </div>
                    </div>

                    <div class="analytics-charts">
                        <div class="chart-container">
                            <h3>Portfolio Performance (30 Days)</h3>
                            <canvas id="performanceChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <h3>Strategy Distribution</h3>
                            <canvas id="strategyChart"></canvas>
                        </div>
                    </div>

                    <div class="analytics-tables">
                        <div class="table-container">
                            <h3>Top Performing Strategies</h3>
                            <table class="analytics-table">
                                <thead>
                                    <tr>
                                        <th>Strategy</th>
                                        <th>Return</th>
                                        <th>Followers</th>
                                        <th>Risk Level</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {self._render_top_strategies(performance_report.get('top_strategies', []))}
                                </tbody>
                            </table>
                        </div>
                        <div class="table-container">
                            <h3>Member Leaderboard</h3>
                            <table class="analytics-table">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Reputation</th>
                                        <th>Performance</th>
                                        <th>Strategies</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {self._render_member_leaderboard(overview.get('top_members', []))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <script>
                    // Performance Chart
                    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
                    new Chart(performanceCtx, {{
                        type: 'line',
                        data: {{
                            labels: {json.dumps(performance_report.get('dates', []))},
                            datasets: [{{
                                label: 'Portfolio Value',
                                data: {json.dumps(performance_report.get('values', []))},
                                borderColor: '#FFD700',
                                backgroundColor: 'rgba(255, 215, 0, 0.1)',
                                tension: 0.4
                            }}]
                        }},
                        options: {{
                            responsive: true,
                            plugins: {{
                                legend: {{
                                    labels: {{
                                        color: '#ffffff'
                                    }}
                                }}
                            }},
                            scales: {{
                                y: {{
                                    ticks: {{
                                        color: '#ffffff'
                                    }}
                                }},
                                x: {{
                                    ticks: {{
                                        color: '#ffffff'
                                    }}
                                }}
                            }}
                        }}
                    }});

                    // Strategy Distribution Chart
                    const strategyCtx = document.getElementById('strategyChart').getContext('2d');
                    new Chart(strategyCtx, {{
                        type: 'doughnut',
                        data: {{
                            labels: {json.dumps(overview.get('strategy_types', []))},
                            datasets: [{{
                                data: {json.dumps(overview.get('strategy_counts', []))},
                                backgroundColor: ['#FFD700', '#FFA000', '#FF8F00', '#FF6F00', '#E65100']
                            }}]
                        }},
                        options: {{
                            responsive: true,
                            plugins: {{
                                legend: {{
                                    labels: {{
                                        color: '#ffffff'
                                    }}
                                }}
                            }}
                        }}
                    }});
                </script>
            </body>
            </html>
            """

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Club analytics error: {e}")
            return web.Response(text="Club analytics temporarily unavailable", status=500)

    def _render_top_strategies(self, strategies: List[Dict]) -> str:
        """Render top strategies table rows."""
        if not strategies:
            return '<tr><td colspan="4">No strategies available</td></tr>'

        rows_html = ""
        for strategy in strategies[:5]:  # Top 5
            rows_html += f"""
            <tr>
                <td>{strategy.get('title', 'Unknown')}</td>
                <td class="{'positive' if strategy.get('return', 0) >= 0 else 'negative'}">
                    {strategy.get('return', 0):.2f}%
                </td>
                <td>{strategy.get('followers', 0)}</td>
                <td><span class="risk-badge risk-{strategy.get('risk_level', 'medium')}">{strategy.get('risk_level', 'Medium').title()}</span></td>
            </tr>
            """
        return rows_html

    def _render_member_leaderboard(self, members: List[Dict]) -> str:
        """Render member leaderboard table rows."""
        if not members:
            return '<tr><td colspan="4">No members available</td></tr>'

        rows_html = ""
        for member in members[:5]:  # Top 5
            rows_html += f"""
            <tr>
                <td>{member.get('username', 'Unknown')}</td>
                <td>{member.get('reputation_score', 0):.0f}</td>
                <td class="{'positive' if member.get('performance', 0) >= 0 else 'negative'}">
                    {member.get('performance', 0):.2f}%
                </td>
                <td>{member.get('strategy_count', 0)}</td>
            </tr>
            """
        return rows_html

    # Strategy Governance API endpoints
    async def api_propose_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to propose new strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['title', 'description', 'strategy_type', 'risk_level',
                             'expected_return', 'max_drawdown', 'time_horizon']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Submit proposal
            strategy_id = self.strategy_governance.submit_strategy_proposal(
                user_id=user_id,
                title=data['title'],
                description=data['description'],
                strategy_type=data['strategy_type'],
                risk_level=data['risk_level'],
                expected_return=float(data['expected_return']),
                max_drawdown=float(data['max_drawdown']),
                time_horizon=data['time_horizon'],
                parameters=json.loads(data.get('parameters', '{}'))
            )

            if strategy_id:
                return web.json_response({'success': True, 'strategy_id': strategy_id})
            else:
                return web.json_response({'error': 'Failed to submit proposal'}, status=400)

        except Exception as e:
            logger.error(f"Propose strategy API error: {e}")
            return web.json_response({'error': 'Failed to submit proposal'}, status=500)

    async def api_cast_vote(self, request: web.Request) -> web.Response:
        """API endpoint to cast vote on strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            # Validate required fields
            required_fields = ['strategy_id', 'vote']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Cast vote
            success = self.strategy_governance.cast_vote(
                user_id=user_id,
                strategy_id=int(data['strategy_id']),
                vote=data['vote'],
                reasoning=data.get('reasoning', '')
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to cast vote'}, status=400)

        except Exception as e:
            logger.error(f"Cast vote API error: {e}")
            return web.json_response({'error': 'Failed to cast vote'}, status=500)

    async def api_admin_review_strategy(self, request: web.Request) -> web.Response:
        """API endpoint for admin strategy review."""
        try:
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            data = await request.json()
            admin_user_id = user['user_id']

            # Validate required fields
            required_fields = ['strategy_id', 'action']
            for field in required_fields:
                if not data.get(field):
                    return web.json_response({'error': f'Missing {field}'}, status=400)

            # Review strategy
            success = self.strategy_governance.admin_review_strategy(
                admin_user_id=admin_user_id,
                strategy_id=int(data['strategy_id']),
                action=data['action'],
                review_notes=data.get('review_notes', '')
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to review strategy'}, status=400)

        except Exception as e:
            logger.error(f"Admin review API error: {e}")
            return web.json_response({'error': 'Failed to review strategy'}, status=500)

    async def api_get_strategy_discussions(self, request: web.Request) -> web.Response:
        """API endpoint to get strategy discussions."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            discussions = self.strategy_governance.get_strategy_discussions(strategy_id)

            return web.json_response({'success': True, 'discussions': discussions})

        except Exception as e:
            logger.error(f"Get discussions API error: {e}")
            return web.json_response({'error': 'Failed to get discussions'}, status=500)

    async def api_add_strategy_discussion(self, request: web.Request) -> web.Response:
        """API endpoint to add strategy discussion."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            strategy_id = int(request.match_info['strategy_id'])
            data = await request.json()
            user_id = user['user_id']

            discussion_id = self.strategy_governance.add_strategy_discussion(
                user_id=user_id,
                strategy_id=strategy_id,
                content=data['content'],
                parent_id=data.get('parent_id')
            )

            if discussion_id:
                return web.json_response({'success': True, 'discussion_id': discussion_id})
            else:
                return web.json_response({'error': 'Failed to add discussion'}, status=400)

        except Exception as e:
            logger.error(f"Add discussion API error: {e}")
            return web.json_response({'error': 'Failed to add discussion'}, status=500)

    async def api_get_strategy_votes(self, request: web.Request) -> web.Response:
        """API endpoint to get strategy votes."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            votes = self.strategy_governance.get_strategy_votes(strategy_id)

            return web.json_response({'success': True, 'votes': votes})

        except Exception as e:
            logger.error(f"Get votes API error: {e}")
            return web.json_response({'error': 'Failed to get votes'}, status=500)

    # Social Trading API endpoints
    async def api_follow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to follow strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.follow_strategy(
                user_id=user_id,
                strategy_id=int(data['strategy_id']),
                auto_execute=data.get('auto_execute', False),
                allocation_percentage=float(data.get('allocation_percentage', 0.0))
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to follow strategy'}, status=400)

        except Exception as e:
            logger.error(f"Follow strategy API error: {e}")
            return web.json_response({'error': 'Failed to follow strategy'}, status=500)

    async def api_unfollow_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to unfollow strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.unfollow_strategy(
                user_id=user_id,
                strategy_id=int(data['strategy_id'])
            )

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to unfollow strategy'}, status=400)

        except Exception as e:
            logger.error(f"Unfollow strategy API error: {e}")
            return web.json_response({'error': 'Failed to unfollow strategy'}, status=500)

    async def api_get_activity_feed(self, request: web.Request) -> web.Response:
        """API endpoint to get activity feed."""
        try:
            user_id = request.query.get('user_id')
            limit = int(request.query.get('limit', 50))

            if user_id:
                user_id = int(user_id)

            activities = self.social_trading.get_member_activity_feed(user_id, limit)

            return web.json_response({'success': True, 'activities': activities})

        except Exception as e:
            logger.error(f"Activity feed API error: {e}")
            return web.json_response({'error': 'Failed to get activity feed'}, status=500)

    async def api_get_leaderboard(self, request: web.Request) -> web.Response:
        """API endpoint to get member leaderboard."""
        try:
            metric = request.query.get('metric', 'reputation')
            period = request.query.get('period', 'all_time')
            limit = int(request.query.get('limit', 20))

            leaderboard = self.social_trading.get_member_leaderboard(metric, period, limit)

            return web.json_response({'success': True, 'leaderboard': leaderboard})

        except Exception as e:
            logger.error(f"Leaderboard API error: {e}")
            return web.json_response({'error': 'Failed to get leaderboard'}, status=500)

    async def api_get_member_profile(self, request: web.Request) -> web.Response:
        """API endpoint to get member profile."""
        try:
            user_id = int(request.match_info['user_id'])
            profile = self.social_trading.get_member_profile(user_id)

            if profile:
                return web.json_response({'success': True, 'profile': profile})
            else:
                return web.json_response({'error': 'Profile not found'}, status=404)

        except Exception as e:
            logger.error(f"Get profile API error: {e}")
            return web.json_response({'error': 'Failed to get profile'}, status=500)

    async def api_update_member_profile(self, request: web.Request) -> web.Response:
        """API endpoint to update member profile."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            user_id = user['user_id']

            success = self.social_trading.update_member_profile(user_id, data)

            if success:
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to update profile'}, status=400)

        except Exception as e:
            logger.error(f"Update profile API error: {e}")
            return web.json_response({'error': 'Failed to update profile'}, status=500)

    # Club Analytics API endpoints
    async def api_get_club_overview(self, request: web.Request) -> web.Response:
        """API endpoint to get club overview."""
        try:
            overview = self.club_analytics.generate_club_overview()
            return web.json_response({'success': True, 'overview': overview})

        except Exception as e:
            logger.error(f"Club overview API error: {e}")
            return web.json_response({'error': 'Failed to get club overview'}, status=500)

    async def api_get_performance_report(self, request: web.Request) -> web.Response:
        """API endpoint to get performance report."""
        try:
            period_days = int(request.query.get('period_days', 30))
            report = self.club_analytics.generate_performance_report(period_days)

            return web.json_response({'success': True, 'report': report})

        except Exception as e:
            logger.error(f"Performance report API error: {e}")
            return web.json_response({'error': 'Failed to get performance report'}, status=500)

    async def api_get_monthly_report(self, request: web.Request) -> web.Response:
        """API endpoint to get monthly report."""
        try:
            report = self.club_analytics.generate_monthly_report()
            return web.json_response({'success': True, 'report': report})

        except Exception as e:
            logger.error(f"Monthly report API error: {e}")
            return web.json_response({'error': 'Failed to get monthly report'}, status=500)

    # Notifications API endpoints
    async def api_get_notifications(self, request: web.Request) -> web.Response:
        """API endpoint to get user notifications."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            limit = int(request.query.get('limit', 20))

            # Get notifications from database
            cursor = self.db_manager.conn.execute("""
                SELECT id, notification_type, title, content, related_id,
                       is_read, timestamp
                FROM club_notifications
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (user_id, limit))

            notifications = []
            for row in cursor.fetchall():
                notifications.append({
                    'id': row[0],
                    'notification_type': row[1],
                    'title': row[2],
                    'content': row[3],
                    'related_id': row[4],
                    'is_read': row[5],
                    'timestamp': row[6]
                })

            return web.json_response({'success': True, 'notifications': notifications})

        except Exception as e:
            logger.error(f"Notifications API error: {e}")
            return web.json_response({'error': 'Failed to get notifications'}, status=500)

    async def api_mark_notification_read(self, request: web.Request) -> web.Response:
        """API endpoint to mark notification as read."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            notification_id = int(request.match_info['notification_id'])
            user_id = user['user_id']

            # Mark notification as read
            self.db_manager.conn.execute("""
                UPDATE club_notifications
                SET is_read = TRUE
                WHERE id = ? AND user_id = ?
            """, (notification_id, user_id))

            self.db_manager.conn.commit()

            return web.json_response({'success': True})

        except Exception as e:
            logger.error(f"Mark notification read API error: {e}")
            return web.json_response({'error': 'Failed to mark notification as read'}, status=500)

    # Enhanced notification API endpoints for header functionality
    async def api_notifications_recent(self, request: web.Request) -> web.Response:
        """API endpoint to get recent notifications for header dropdown."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            user_id = user['user_id']
            limit = int(request.query.get('limit', 10))

            if not self.db_manager.connect():
                return web.json_response({'error': 'Database connection failed'}, status=500)

            # Try to get notifications from club_notifications table first
            try:
                cursor = self.db_manager.conn.execute("""
                    SELECT id, notification_type as type, title, content as message,
                           is_read, timestamp as created_at
                    FROM club_notifications
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (user_id, limit))

                notifications = []
                for row in cursor.fetchall():
                    notifications.append({
                        'id': row[0],
                        'type': row[1] or 'system',
                        'title': row[2] or 'Notification',
                        'message': row[3] or '',
                        'is_read': bool(row[4]),
                        'created_at': row[5]
                    })

                # If no notifications found, create some sample ones for demo
                if not notifications:
                    notifications = self._create_sample_notifications(user_id)

                return web.json_response(notifications)

            except sqlite3.OperationalError:
                # Table doesn't exist, create sample notifications
                notifications = self._create_sample_notifications(user_id)
                return web.json_response(notifications)

        except Exception as e:
            logger.error(f"Recent notifications API error: {e}")
            return web.json_response({'error': 'Failed to get notifications'}, status=500)

    async def api_notifications_mark_read(self, request: web.Request) -> web.Response:
        """API endpoint to mark notification as read (enhanced version)."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            notification_id = request.match_info['notification_id']
            user_id = user['user_id']

            if not self.db_manager.connect():
                return web.json_response({'error': 'Database connection failed'}, status=500)

            # Try to mark notification as read
            try:
                self.db_manager.conn.execute("""
                    UPDATE club_notifications
                    SET is_read = TRUE
                    WHERE id = ? AND user_id = ?
                """, (notification_id, user_id))

                self.db_manager.conn.commit()
                return web.json_response({'success': True})

            except sqlite3.OperationalError:
                # Table doesn't exist, just return success
                return web.json_response({'success': True})

        except Exception as e:
            logger.error(f"Mark notification read API error: {e}")
            return web.json_response({'error': 'Failed to mark notification as read'}, status=500)

    def _create_sample_notifications(self, user_id: int) -> list:
        """Create sample notifications for demo purposes."""
        from datetime import datetime, timedelta

        sample_notifications = [
            {
                'id': 1,
                'type': 'trade',
                'title': 'Trade Executed',
                'message': 'Your DOGE/USDT long position has been opened successfully.',
                'is_read': False,
                'created_at': (datetime.now() - timedelta(minutes=5)).isoformat()
            },
            {
                'id': 2,
                'type': 'alert',
                'title': 'Price Alert',
                'message': 'DOGE/USDT has reached your target price of $0.12.',
                'is_read': False,
                'created_at': (datetime.now() - timedelta(minutes=15)).isoformat()
            },
            {
                'id': 3,
                'type': 'system',
                'title': 'System Update',
                'message': 'Money Circle platform has been updated with new features.',
                'is_read': True,
                'created_at': (datetime.now() - timedelta(hours=2)).isoformat()
            },
            {
                'id': 4,
                'type': 'success',
                'title': 'Profit Target Hit',
                'message': 'Your automated take profit order has been executed.',
                'is_read': False,
                'created_at': (datetime.now() - timedelta(hours=1)).isoformat()
            },
            {
                'id': 5,
                'type': 'message',
                'title': 'Welcome to Live Trading',
                'message': 'Your live trading interface is now active and ready to use.',
                'is_read': True,
                'created_at': (datetime.now() - timedelta(days=1)).isoformat()
            }
        ]

        return sample_notifications

    async def start_server(self):
        """Start the Money Circle server with production configuration."""
        app = await self.create_app()

        runner = web.AppRunner(app)
        await runner.setup()

        # Configure SSL if certificates are provided
        ssl_context = None
        if hasattr(self.config, 'SSL_CERT_PATH') and hasattr(self.config, 'SSL_KEY_PATH'):
            if self.config.SSL_CERT_PATH and self.config.SSL_KEY_PATH:
                try:
                    import ssl
                    ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
                    ssl_context.load_cert_chain(self.config.SSL_CERT_PATH, self.config.SSL_KEY_PATH)
                    logger.info("[SSL] SSL/TLS encryption enabled")
                except Exception as e:
                    logger.warning(f"[SSL] Failed to load SSL certificates: {e}")
                    ssl_context = None

        # Get host and port from environment (Render/Railway compatibility)
        host = os.getenv('HOST', self.config.HOST)
        port = int(os.getenv('PORT', self.config.PORT))

        # Initialize database for production (PostgreSQL on Render)
        if os.getenv('ENVIRONMENT') == 'production' and os.getenv('DATABASE_URL'):
            logger.info("[DATABASE] Initializing PostgreSQL database for production...")
            try:
                from render_database_setup import RenderDatabaseSetup
                db_setup = RenderDatabaseSetup()
                if db_setup.run_setup():
                    logger.info("[DATABASE] PostgreSQL database initialized successfully")
                else:
                    logger.warning("[DATABASE] Database initialization had issues, but continuing...")
            except Exception as e:
                logger.warning(f"[DATABASE] Database setup warning: {e}, continuing with existing database...")

        # Create site with or without SSL
        if ssl_context:
            site = web.TCPSite(runner, host, port, ssl_context=ssl_context)
            protocol = "https"
        else:
            site = web.TCPSite(runner, host, port)
            protocol = "http"

        await site.start()

        # Log server startup information
        server_url = f"{protocol}://{host}:{port}"
        logger.info(f"[SERVER] Money Circle running at {server_url}")

        # Production vs Development mode
        if not self.config.DEBUG:
            logger.info(f"[PRODUCTION] Running in production mode")
            logger.info(f"[SECURITY] Security headers enabled")
            if hasattr(self.config, 'ENABLE_COMPRESSION') and self.config.ENABLE_COMPRESSION:
                logger.info(f"[PERFORMANCE] Compression enabled")
            if ssl_context:
                logger.info(f"[SECURITY] HTTPS encryption active")
        else:
            logger.info(f"[DEVELOPMENT] Running in development mode")

        logger.info(f"[READY] Platform ready for Money Circle investment club members")

        # Start advanced trading systems if available
        if self.market_data_manager:
            asyncio.create_task(self.market_data_manager.start())
            logger.info("[DATA] Advanced market data manager started")

        if self.websocket_streamer:
            # Subscribe to market data updates
            await self.market_data_manager.subscribe(self.websocket_streamer._on_market_data_update)
            logger.info("[WS] Market data WebSocket streamer subscribed to updates")

        if self.notification_manager:
            asyncio.create_task(self.notification_manager.start_notification_system())
            logger.info("[NOTIFY] Notification system started")

        if self.live_trading_interface:
            asyncio.create_task(self.live_trading_interface.start_real_time_monitoring())
            logger.info("[TRADING] Live trading monitoring started")

        if self.live_strategy_engine:
            asyncio.create_task(self.live_strategy_engine.start_strategy_automation())
            logger.info("[STRATEGY] Strategy automation started")

        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Money Circle shutting down...")

            # Stop advanced systems
            if self.market_data_manager:
                await self.market_data_manager.stop()
                logger.info("📊 Market data manager stopped")
            if self.notification_manager:
                await self.notification_manager.stop_notification_system()
            if self.live_trading_interface:
                await self.live_trading_interface.stop_monitoring()
            if self.live_strategy_engine:
                await self.live_strategy_engine.stop_automation()

        finally:
            await runner.cleanup()

    # Advanced Trading API Endpoints
    async def api_place_advanced_order(self, request: web.Request) -> web.Response:
        """API endpoint for advanced order placement with risk management."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_trading_interface:
                return web.json_response({'error': 'Advanced trading not available'}, status=503)

            data = await request.json()
            user_id = user['user_id']

            # Place advanced order
            order = await self.live_trading_interface.place_advanced_order(user_id, data)

            if order:
                # Send notification
                if self.notification_manager:
                    await self.notification_manager.send_trade_notification(user_id, {
                        'symbol': order.symbol,
                        'side': order.side,
                        'amount': order.amount,
                        'price': order.price,
                        'order_id': order.id
                    })

                return web.json_response({
                    'success': True,
                    'order_id': order.id,
                    'status': order.status.value
                })
            else:
                return web.json_response({'error': 'Failed to place order'}, status=400)

        except Exception as e:
            logger.error(f"Advanced order API error: {e}")
            return web.json_response({'error': 'Failed to place order'}, status=500)

    async def api_get_real_time_data(self, request: web.Request) -> web.Response:
        """API endpoint for real-time trading data."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_trading_interface:
                return web.json_response({'error': 'Live trading not available'}, status=503)

            user_id = user['user_id']

            # Get real-time data
            risk_metrics = self.live_trading_interface.get_user_risk_metrics(user_id)
            active_orders = self.live_trading_interface.get_active_orders(user_id)

            return web.json_response({
                'success': True,
                'risk': {
                    'score': risk_metrics.risk_score if risk_metrics else 0,
                    'level': risk_metrics.risk_level.value if risk_metrics else 'low',
                    'daily_pnl': risk_metrics.daily_pnl if risk_metrics else 0,
                    'daily_pnl_percent': risk_metrics.daily_pnl_percent if risk_metrics else 0
                },
                'orders': [{
                    'id': order.id,
                    'symbol': order.symbol,
                    'side': order.side,
                    'amount': order.amount,
                    'status': order.status.value
                } for order in active_orders],
                'metrics': [
                    f"${risk_metrics.portfolio_value:,.2f}" if risk_metrics else "$0.00",
                    f"${risk_metrics.daily_pnl:,.2f}" if risk_metrics else "$0.00",
                    f"{risk_metrics.daily_pnl_percent:.2f}%" if risk_metrics else "0.00%",
                    f"{risk_metrics.risk_score:.0f}/100" if risk_metrics else "0/100"
                ]
            })

        except Exception as e:
            logger.error(f"Real-time data API error: {e}")
            return web.json_response({'error': 'Failed to get real-time data'}, status=500)

    async def api_register_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to register a new strategy for automation."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_strategy_engine:
                return web.json_response({'error': 'Strategy engine not available'}, status=503)

            data = await request.json()

            # Register strategy
            success = await self.live_strategy_engine.register_strategy(data)

            if success:
                return web.json_response({'success': True, 'message': 'Strategy registered'})
            else:
                return web.json_response({'error': 'Failed to register strategy'}, status=400)

        except Exception as e:
            logger.error(f"Register strategy API error: {e}")
            return web.json_response({'error': 'Failed to register strategy'}, status=500)

    async def api_activate_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to activate a strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_strategy_engine:
                return web.json_response({'error': 'Strategy engine not available'}, status=503)

            strategy_id = request.match_info['strategy_id']
            user_id = user['user_id']

            # Activate strategy
            success = await self.live_strategy_engine.activate_strategy(strategy_id, user_id)

            if success:
                return web.json_response({'success': True, 'message': 'Strategy activated'})
            else:
                return web.json_response({'error': 'Failed to activate strategy'}, status=400)

        except Exception as e:
            logger.error(f"Activate strategy API error: {e}")
            return web.json_response({'error': 'Failed to activate strategy'}, status=500)

    async def api_get_risk_metrics(self, request: web.Request) -> web.Response:
        """API endpoint to get user risk metrics."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_trading_interface:
                return web.json_response({'error': 'Live trading not available'}, status=503)

            user_id = user['user_id']
            risk_metrics = self.live_trading_interface.get_user_risk_metrics(user_id)

            if risk_metrics:
                return web.json_response({
                    'success': True,
                    'risk_metrics': {
                        'portfolio_value': risk_metrics.portfolio_value,
                        'daily_pnl': risk_metrics.daily_pnl,
                        'daily_pnl_percent': risk_metrics.daily_pnl_percent,
                        'risk_score': risk_metrics.risk_score,
                        'risk_level': risk_metrics.risk_level.value,
                        'warnings': risk_metrics.warnings
                    }
                })
            else:
                return web.json_response({
                    'success': True,
                    'risk_metrics': None
                })

        except Exception as e:
            logger.error(f"Risk metrics API error: {e}")
            return web.json_response({'error': 'Failed to get risk metrics'}, status=500)

    async def api_get_active_orders(self, request: web.Request) -> web.Response:
        """API endpoint to get user's active orders."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_trading_interface:
                return web.json_response({'error': 'Live trading not available'}, status=503)

            user_id = user['user_id']
            active_orders = self.live_trading_interface.get_active_orders(user_id)

            return web.json_response({
                'success': True,
                'orders': [{
                    'id': order.id,
                    'symbol': order.symbol,
                    'side': order.side,
                    'amount': order.amount,
                    'price': order.price,
                    'status': order.status.value,
                    'timestamp': order.timestamp.isoformat()
                } for order in active_orders]
            })

        except Exception as e:
            logger.error(f"Active orders API error: {e}")
            return web.json_response({'error': 'Failed to get active orders'}, status=500)

    async def api_pause_strategy(self, request: web.Request) -> web.Response:
        """API endpoint to pause a strategy."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_strategy_engine:
                return web.json_response({'error': 'Strategy engine not available'}, status=503)

            strategy_id = request.match_info['strategy_id']

            # For now, just return success (implementation would pause the strategy)
            return web.json_response({'success': True, 'message': 'Strategy paused'})

        except Exception as e:
            logger.error(f"Pause strategy API error: {e}")
            return web.json_response({'error': 'Failed to pause strategy'}, status=500)

    async def api_get_strategy_performance(self, request: web.Request) -> web.Response:
        """API endpoint to get strategy performance metrics."""
        try:
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if not self.live_strategy_engine:
                return web.json_response({'error': 'Strategy engine not available'}, status=503)

            # Get all strategy performances
            performances = {}
            for strategy_id, performance in self.live_strategy_engine.strategy_performances.items():
                performances[strategy_id] = {
                    'total_trades': performance.total_trades,
                    'win_rate': performance.win_rate,
                    'total_pnl': performance.total_pnl,
                    'status': performance.status.value
                }

            return web.json_response({
                'success': True,
                'performances': performances
            })

        except Exception as e:
            logger.error(f"Strategy performance API error: {e}")
            return web.json_response({'error': 'Failed to get strategy performance'}, status=500)

    # Enhanced Strategy Marketplace API endpoints
    async def api_follow_strategy_enhanced(self, request: web.Request) -> web.Response:
        """API endpoint to follow a strategy (enhanced)."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check if strategy exists
            cursor = self.db_manager.conn.execute("""
                SELECT id FROM strategy_proposals WHERE id = ? AND status = 'approved'
            """, (strategy_id,))

            if not cursor.fetchone():
                return web.json_response({'error': 'Strategy not found'}, status=404)

            # Add to following
            cursor = self.db_manager.conn.execute("""
                INSERT OR REPLACE INTO strategy_following
                (user_id, strategy_id, is_active, started_at)
                VALUES (?, ?, TRUE, datetime('now'))
            """, (user['user_id'], strategy_id))
            self.db_manager.conn.commit()

            return web.json_response({'success': True, 'message': 'Strategy followed successfully'})

        except Exception as e:
            logger.error(f"Follow strategy API error: {e}")
            return web.json_response({'error': 'Failed to follow strategy'}, status=500)

    async def api_unfollow_strategy_enhanced(self, request: web.Request) -> web.Response:
        """API endpoint to unfollow a strategy (enhanced)."""
        try:
            strategy_id = int(request.match_info['strategy_id'])
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Remove from following
            cursor = self.db_manager.conn.execute("""
                UPDATE strategy_following
                SET is_active = FALSE
                WHERE user_id = ? AND strategy_id = ?
            """, (user['user_id'], strategy_id))
            self.db_manager.conn.commit()

            return web.json_response({'success': True, 'message': 'Strategy unfollowed successfully'})

        except Exception as e:
            logger.error(f"Unfollow strategy API error: {e}")
            return web.json_response({'error': 'Failed to unfollow strategy'}, status=500)

    async def api_get_strategy_details(self, request: web.Request) -> web.Response:
        """API endpoint to get detailed strategy information."""
        try:
            strategy_id = int(request.match_info['strategy_id'])

            cursor = self.db_manager.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type, sp.risk_level,
                    sp.expected_return, sp.max_drawdown, sp.time_horizon, sp.created_at,
                    u.username, mp.display_name,
                    AVG(spr.total_return) as avg_return,
                    AVG(spr.win_rate) as avg_win_rate,
                    COUNT(spr.id) as total_trades
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.id = ? AND sp.status = 'approved'
                GROUP BY sp.id
            """, (strategy_id,))

            row = cursor.fetchone()
            if not row:
                return web.json_response({'error': 'Strategy not found'}, status=404)

            strategy_details = {
                'id': row[0],
                'title': row[1],
                'description': row[2],
                'strategy_type': row[3],
                'risk_level': row[4],
                'expected_return': row[5],
                'max_drawdown': row[6],
                'time_horizon': row[7],
                'created_at': row[8],
                'creator_username': row[9],
                'creator_display_name': row[10] or row[9],
                'avg_return': row[11] or 0.0,
                'avg_win_rate': row[12] or 0.0,
                'total_trades': row[13] or 0
            }

            return web.json_response({'success': True, 'data': strategy_details})

        except Exception as e:
            logger.error(f"Strategy details API error: {e}")
            return web.json_response({'error': 'Failed to get strategy details'}, status=500)

    # Enhanced Member Directory API endpoints
    async def api_connect_member(self, request: web.Request) -> web.Response:
        """API endpoint to connect with a member."""
        try:
            member_id = int(request.match_info['member_id'])
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Add connection (using strategy_following table as proxy for member connections)
            cursor = self.db_manager.conn.execute("""
                INSERT OR REPLACE INTO strategy_following
                (follower_id, strategy_creator_id, is_active, created_at)
                VALUES (?, ?, TRUE, datetime('now'))
            """, (user['user_id'], member_id))
            self.db_manager.conn.commit()

            return web.json_response({'success': True, 'message': 'Connected successfully'})

        except Exception as e:
            logger.error(f"Connect member API error: {e}")
            return web.json_response({'error': 'Failed to connect'}, status=500)

    async def api_disconnect_member(self, request: web.Request) -> web.Response:
        """API endpoint to disconnect from a member."""
        try:
            member_id = int(request.match_info['member_id'])
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Remove connection
            cursor = self.db_manager.conn.execute("""
                UPDATE strategy_following
                SET is_active = FALSE
                WHERE follower_id = ? AND strategy_creator_id = ?
            """, (user['user_id'], member_id))
            self.db_manager.conn.commit()

            return web.json_response({'success': True, 'message': 'Disconnected successfully'})

        except Exception as e:
            logger.error(f"Disconnect member API error: {e}")
            return web.json_response({'error': 'Failed to disconnect'}, status=500)

    async def api_get_member_profile_enhanced(self, request: web.Request) -> web.Response:
        """API endpoint to get enhanced member profile."""
        try:
            member_id = int(request.match_info['member_id'])

            cursor = self.db_manager.conn.execute("""
                SELECT
                    u.id, u.username, u.email, u.created_at,
                    mp.display_name, mp.bio, mp.location, mp.specialization,
                    mp.experience_level, mp.verified,
                    AVG(tp.total_return) as avg_return,
                    AVG(tp.win_rate) as avg_win_rate,
                    COUNT(DISTINCT sf.follower_id) as followers_count,
                    COUNT(DISTINCT sp.id) as strategies_count
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                LEFT JOIN strategy_following sf ON u.id = sf.strategy_creator_id
                LEFT JOIN strategy_proposals sp ON u.id = sp.user_id AND sp.status = 'approved'
                WHERE u.id = ?
                GROUP BY u.id
            """, (member_id,))

            row = cursor.fetchone()
            if not row:
                return web.json_response({'error': 'Member not found'}, status=404)

            member_profile = {
                'id': row[0],
                'username': row[1],
                'email': row[2],
                'created_at': row[3],
                'display_name': row[4] or row[1],
                'bio': row[5] or '',
                'location': row[6] or '',
                'specialization': row[7] or 'general',
                'experience_level': row[8] or 'beginner',
                'verified': bool(row[9]),
                'avg_return': row[10] or 0.0,
                'avg_win_rate': row[11] or 0.0,
                'followers_count': row[12] or 0,
                'strategies_count': row[13] or 0
            }

            return web.json_response({'success': True, 'data': member_profile})

        except Exception as e:
            logger.error(f"Member profile API error: {e}")
            return web.json_response({'error': 'Failed to get member profile'}, status=500)

    async def api_send_member_message(self, request: web.Request) -> web.Response:
        """API endpoint to send a message to a member."""
        try:
            member_id = int(request.match_info['member_id'])
            user = request.get('user')
            if not user:
                return web.json_response({'error': 'Authentication required'}, status=401)

            data = await request.json()
            subject = data.get('subject', '').strip()
            content = data.get('content', '').strip()

            if not subject or not content:
                return web.json_response({'error': 'Subject and content are required'}, status=400)

            # Store message (using notifications table as proxy)
            cursor = self.db_manager.conn.execute("""
                INSERT INTO notifications
                (user_id, type, title, message, created_at, is_read)
                VALUES (?, 'message', ?, ?, datetime('now'), FALSE)
            """, (member_id, f"Message from {user['username']}: {subject}", content))
            self.db_manager.conn.commit()

            return web.json_response({'success': True, 'message': 'Message sent successfully'})

        except Exception as e:
            logger.error(f"Send message API error: {e}")
            return web.json_response({'error': 'Failed to send message'}, status=500)

    # Enhanced Exchange Management API endpoints
    async def api_test_exchange_connection(self, request: web.Request) -> web.Response:
        """Test exchange connection with provided credentials."""
        try:
            data = await request.json()

            exchange_type = data.get('exchangeType')
            api_key = data.get('apiKey')
            api_secret = data.get('apiSecret')
            passphrase = data.get('passphrase')
            environment = data.get('environment', 'testnet')

            if not exchange_type or not api_key or not api_secret:
                return web.json_response({
                    'success': False,
                    'error': 'Exchange type, API key, and secret are required'
                }, status=400)

            # Test the connection using the exchange manager
            success = self.exchange_manager._test_credentials(
                exchange_type, api_key, api_secret, passphrase
            )

            if success:
                return web.json_response({
                    'success': True,
                    'message': f'Successfully connected to {exchange_type}. API credentials are valid.'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Invalid API credentials or connection failed'
                })

        except Exception as e:
            logger.error(f"Exchange connection test error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Connection test failed: {str(e)}'
            }, status=500)

    async def api_add_exchange_enhanced(self, request: web.Request) -> web.Response:
        """Add exchange account with enhanced validation."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_type = data.get('exchangeType')
            api_key = data.get('apiKey')
            api_secret = data.get('apiSecret')
            passphrase = data.get('passphrase')
            environment = data.get('environment', 'testnet')

            if not exchange_type or not api_key or not api_secret:
                return web.json_response({
                    'success': False,
                    'error': 'Exchange type, API key, and secret are required'
                }, status=400)

            # Add the exchange account
            success = self.exchange_manager.add_exchange_account(
                user_id, exchange_type, api_key, api_secret, passphrase
            )

            if success:
                return web.json_response({
                    'success': True,
                    'message': f'{exchange_type} account added successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to add exchange account'
                })

        except Exception as e:
            logger.error(f"Add exchange error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to add exchange: {str(e)}'
            }, status=500)

    async def api_list_user_exchanges(self, request: web.Request) -> web.Response:
        """List all exchange accounts for the user."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchanges = self.exchange_manager.get_user_exchanges(user_id)

            # Format exchanges for frontend
            formatted_exchanges = []
            for exchange in exchanges:
                # Test connection status
                try:
                    balance = self.exchange_manager.get_user_balance(user_id, exchange.exchange_name)
                    connected = balance is not None
                except:
                    connected = False

                formatted_exchanges.append({
                    'id': exchange.id,
                    'exchange_name': exchange.exchange_name,
                    'connected': connected,
                    'balance': balance if connected else None,
                    'created_at': exchange.created_at,
                    'is_active': exchange.is_active
                })

            return web.json_response({
                'success': True,
                'exchanges': formatted_exchanges
            })

        except Exception as e:
            logger.error(f"List exchanges error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to list exchanges: {str(e)}'
            }, status=500)

    async def api_get_exchange_details(self, request: web.Request) -> web.Response:
        """Get detailed information about a specific exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])

            # Get exchange details from database
            cursor = self.db_manager.conn.execute("""
                SELECT id, user_id, exchange_name, is_active, created_at
                FROM user_exchanges
                WHERE id = ? AND user_id = ? AND is_active = 1
            """, (exchange_id, user_id))

            row = cursor.fetchone()
            if not row:
                return web.json_response({
                    'success': False,
                    'error': 'Exchange not found'
                }, status=404)

            exchange_data = {
                'id': row[0],
                'user_id': row[1],
                'exchange_name': row[2],
                'is_active': bool(row[3]),
                'created_at': row[4]
            }

            # Test connection and get balance
            try:
                balance = self.exchange_manager.get_user_balance(user_id, exchange_data['exchange_name'])
                exchange_data['connected'] = balance is not None
                exchange_data['balance'] = balance
                exchange_data['last_sync'] = datetime.now().isoformat()
            except:
                exchange_data['connected'] = False
                exchange_data['balance'] = None
                exchange_data['last_sync'] = None

            return web.json_response({
                'success': True,
                'exchange': exchange_data
            })

        except Exception as e:
            logger.error(f"Get exchange details error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to get exchange details: {str(e)}'
            }, status=500)

    async def api_refresh_exchange(self, request: web.Request) -> web.Response:
        """Refresh exchange connection and data."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']

            # Refresh balance
            balance = self.exchange_manager.get_user_balance(user_id, exchange_name)

            if balance:
                return web.json_response({
                    'success': True,
                    'message': f'{exchange_name} refreshed successfully',
                    'balance': balance
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': f'Failed to refresh {exchange_name}'
                })

        except Exception as e:
            logger.error(f"Refresh exchange error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to refresh exchange: {str(e)}'
            }, status=500)

    async def api_refresh_exchange_balance(self, request: web.Request) -> web.Response:
        """Refresh balance for a specific exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])

            # Get exchange name from ID
            cursor = self.db_manager.conn.execute("""
                SELECT exchange_name FROM user_exchanges
                WHERE id = ? AND user_id = ? AND is_active = 1
            """, (exchange_id, user_id))

            row = cursor.fetchone()
            if not row:
                return web.json_response({
                    'success': False,
                    'error': 'Exchange not found'
                }, status=404)

            exchange_name = row[0]

            # Refresh balance
            balance = self.exchange_manager.get_user_balance(user_id, exchange_name)

            if balance:
                return web.json_response({
                    'success': True,
                    'message': 'Balance refreshed successfully',
                    'balance': balance
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to refresh balance'
                })

        except Exception as e:
            logger.error(f"Refresh balance error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to refresh balance: {str(e)}'
            }, status=500)

    async def api_test_existing_exchange(self, request: web.Request) -> web.Response:
        """Test connection for an existing exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])

            # Get exchange details
            cursor = self.db_manager.conn.execute("""
                SELECT exchange_name FROM user_exchanges
                WHERE id = ? AND user_id = ? AND is_active = 1
            """, (exchange_id, user_id))

            row = cursor.fetchone()
            if not row:
                return web.json_response({
                    'success': False,
                    'error': 'Exchange not found'
                }, status=404)

            exchange_name = row[0]

            # Test connection by trying to get balance
            try:
                balance = self.exchange_manager.get_user_balance(user_id, exchange_name)
                if balance is not None:
                    return web.json_response({
                        'success': True,
                        'message': 'Connection test successful'
                    })
                else:
                    return web.json_response({
                        'success': False,
                        'error': 'Connection test failed'
                    })
            except Exception as test_error:
                return web.json_response({
                    'success': False,
                    'error': f'Connection test failed: {str(test_error)}'
                })

        except Exception as e:
            logger.error(f"Test exchange error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to test exchange: {str(e)}'
            }, status=500)

    async def api_remove_exchange_enhanced(self, request: web.Request) -> web.Response:
        """Remove exchange account with enhanced validation."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_id = int(request.match_info['exchange_id'])

            # Remove the exchange
            success = self.exchange_manager.remove_exchange_account(user_id, exchange_id)

            if success:
                return web.json_response({
                    'success': True,
                    'message': 'Exchange account removed successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to remove exchange account'
                })

        except Exception as e:
            logger.error(f"Remove exchange error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Failed to remove exchange: {str(e)}'
            }, status=500)

    # Enhanced Live Trading API endpoints
    async def api_place_enhanced_order(self, request: web.Request) -> web.Response:
        """Place order with enhanced risk management and validation."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            # Validate required fields
            required_fields = ['exchange', 'symbol', 'side', 'amount', 'type']
            for field in required_fields:
                if field not in data:
                    return web.json_response({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }, status=400)

            # Use live trading interface for enhanced order placement
            if hasattr(self, 'live_trading_interface'):
                order = await self.live_trading_interface.place_advanced_order(user_id, data)

                if order:
                    return web.json_response({
                        'success': True,
                        'order': {
                            'id': order.id,
                            'symbol': order.symbol,
                            'side': order.side,
                            'amount': order.amount,
                            'price': order.price,
                            'status': order.status.value,
                            'risk_level': order.risk_level.value
                        }
                    })
                else:
                    return web.json_response({
                        'success': False,
                        'error': 'Failed to place order - risk checks failed or exchange error'
                    })
            else:
                # Fallback to basic order placement
                order = self.exchange_manager.place_order(
                    user_id, data['exchange'], data['symbol'],
                    data['type'], data['side'], data['amount'],
                    data.get('price')
                )

                if order:
                    return web.json_response({'success': True, 'order': order})
                else:
                    return web.json_response({'success': False, 'error': 'Failed to place order'})

        except Exception as e:
            logger.error(f"Enhanced order placement error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Order placement failed: {str(e)}'
            }, status=500)

    async def api_place_market_order(self, request: web.Request) -> web.Response:
        """Place market order for HTX futures."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')
            symbol = data.get('symbol', 'DOGE/USDT:USDT')
            side = data.get('side')  # 'buy' or 'sell'
            amount = float(data.get('amount', 0))
            leverage = int(data.get('leverage', 10))
            reduce_only = data.get('reduce_only', False)

            if not side or amount <= 0:
                return web.json_response({
                    'success': False,
                    'error': 'Invalid side or amount'
                }, status=400)

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Place market order
            order = await htx_client.place_market_order(side, amount, leverage, reduce_only)

            if order:
                return web.json_response({
                    'success': True,
                    'message': f'Market order placed: {side} {amount} {symbol}',
                    'order': order
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to place market order'
                })

        except Exception as e:
            logger.error(f"Market order error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Market order failed: {str(e)}'
            }, status=500)

    async def api_place_limit_order(self, request: web.Request) -> web.Response:
        """Place limit order for HTX futures."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')
            symbol = data.get('symbol', 'DOGE/USDT:USDT')
            side = data.get('side')  # 'buy' or 'sell'
            amount = float(data.get('amount', 0))
            price = float(data.get('price', 0))
            leverage = int(data.get('leverage', 10))
            reduce_only = data.get('reduce_only', False)

            if not side or amount <= 0 or price <= 0:
                return web.json_response({
                    'success': False,
                    'error': 'Invalid side, amount, or price'
                }, status=400)

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Place limit order
            order = await htx_client.place_limit_order(side, amount, price, leverage, reduce_only)

            if order:
                return web.json_response({
                    'success': True,
                    'message': f'Limit order placed: {side} {amount} {symbol} at ${price}',
                    'order': order
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to place limit order'
                })

        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Limit order failed: {str(e)}'
            }, status=500)

    async def api_place_stop_order(self, request: web.Request) -> web.Response:
        """Place stop-loss order for HTX futures."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')
            symbol = data.get('symbol', 'DOGE/USDT:USDT')
            side = data.get('side')  # 'buy' or 'sell'
            amount = float(data.get('amount', 0))
            stop_price = float(data.get('stop_price', 0))
            leverage = int(data.get('leverage', 10))

            if not side or amount <= 0 or stop_price <= 0:
                return web.json_response({
                    'success': False,
                    'error': 'Invalid side, amount, or stop price'
                }, status=400)

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Place stop order
            order = await htx_client.place_stop_order(side, amount, stop_price, leverage)

            if order:
                return web.json_response({
                    'success': True,
                    'message': f'Stop order placed: {side} {amount} {symbol} at stop ${stop_price}',
                    'order': order
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to place stop order'
                })

        except Exception as e:
            logger.error(f"Stop order error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Stop order failed: {str(e)}'
            }, status=500)

    async def api_close_position(self, request: web.Request) -> web.Response:
        """Close position for HTX futures."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')
            position_side = data.get('position_side')  # 'long' or 'short' or None for all

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Close position
            result = await htx_client.close_position(position_side)

            if result:
                return web.json_response({
                    'success': True,
                    'message': f'Position closed successfully',
                    'orders': result
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'No positions to close or close failed'
                })

        except Exception as e:
            logger.error(f"Close position error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Close position failed: {str(e)}'
            }, status=500)

    async def api_cancel_order(self, request: web.Request) -> web.Response:
        """Cancel a specific order."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')
            order_id = data.get('order_id')

            if not order_id:
                return web.json_response({
                    'success': False,
                    'error': 'Order ID is required'
                }, status=400)

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Cancel order
            success = await htx_client.cancel_order(order_id)

            if success:
                return web.json_response({
                    'success': True,
                    'message': f'Order {order_id} cancelled successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to cancel order'
                })

        except Exception as e:
            logger.error(f"Cancel order error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Cancel order failed: {str(e)}'
            }, status=500)

    async def api_cancel_all_orders(self, request: web.Request) -> web.Response:
        """Cancel all open orders."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            data = await request.json()

            exchange_name = data.get('exchange', 'HTX')

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Cancel all orders
            success = await htx_client.cancel_all_orders()

            if success:
                return web.json_response({
                    'success': True,
                    'message': 'All orders cancelled successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to cancel orders'
                })

        except Exception as e:
            logger.error(f"Cancel all orders error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Cancel all orders failed: {str(e)}'
            }, status=500)

    async def api_get_live_positions(self, request: web.Request) -> web.Response:
        """Get live positions for an exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Get positions
            positions = await htx_client.get_positions()

            return web.json_response({
                'success': True,
                'positions': positions or []
            })

        except Exception as e:
            logger.error(f"Get positions error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Get positions failed: {str(e)}'
            }, status=500)

    async def api_get_open_orders(self, request: web.Request) -> web.Response:
        """Get open orders for an exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Get open orders
            orders = await htx_client.get_open_orders()

            return web.json_response({
                'success': True,
                'orders': orders or []
            })

        except Exception as e:
            logger.error(f"Get open orders error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Get open orders failed: {str(e)}'
            }, status=500)

    async def api_get_orderbook(self, request: web.Request) -> web.Response:
        """Get order book for a symbol."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']
            symbol = request.match_info['symbol']

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Get order book
            orderbook = await htx_client.get_order_book()

            return web.json_response({
                'success': True,
                'orderbook': orderbook or {'bids': [], 'asks': []}
            })

        except Exception as e:
            logger.error(f"Get orderbook error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Get orderbook failed: {str(e)}'
            }, status=500)

    async def api_get_recent_trades(self, request: web.Request) -> web.Response:
        """Get recent trades for a symbol."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']
            symbol = request.match_info['symbol']

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Get recent trades
            trades = await htx_client.get_recent_trades()

            return web.json_response({
                'success': True,
                'trades': trades or []
            })

        except Exception as e:
            logger.error(f"Get recent trades error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Get recent trades failed: {str(e)}'
            }, status=500)

    async def api_get_account_info(self, request: web.Request) -> web.Response:
        """Get account information for an exchange."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'success': False, 'error': 'Authentication required'}, status=401)

            exchange_name = request.match_info['exchange_name']

            # Get HTX client for the user
            htx_client = self.exchange_manager.get_htx_client(user_id)
            if not htx_client:
                return web.json_response({
                    'success': False,
                    'error': 'HTX client not available - please add HTX exchange account'
                })

            # Get account info
            account_info = await htx_client.get_account_info()

            return web.json_response({
                'success': True,
                'account_info': account_info or {}
            })

        except Exception as e:
            logger.error(f"Get account info error: {e}")
            return web.json_response({
                'success': False,
                'error': f'Get account info failed: {str(e)}'
            }, status=500)

    # Production deployment API endpoints
    async def api_health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint for production monitoring."""
        try:
            health_status = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'environment': 'production' if not self.config.DEBUG else 'development'
            }

            # Check database connectivity
            try:
                if self.db_manager.connect():
                    self.db_manager.conn.execute("SELECT 1").fetchone()
                    self.db_manager.conn.close()
                    health_status['database'] = 'healthy'
                else:
                    health_status['database'] = 'unhealthy'
                    health_status['status'] = 'degraded'
            except Exception as e:
                health_status['database'] = f'error: {str(e)}'
                health_status['status'] = 'unhealthy'

            # Check market data connections
            if self.market_data_manager:
                health_status['market_data'] = 'connected'
            else:
                health_status['market_data'] = 'unavailable'

            return web.json_response(health_status)

        except Exception as e:
            return web.json_response({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)

    async def api_system_status(self, request: web.Request) -> web.Response:
        """Get comprehensive system status."""
        try:
            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            status = {
                'timestamp': datetime.now().isoformat(),
                'platform': 'Money Circle Investment Club',
                'version': '1.0.0',
                'environment': 'production' if not self.config.DEBUG else 'development'
            }

            # Get production component status
            if self.production_config:
                status['configuration'] = self.production_config.get_config_summary()

            if self.security_manager:
                status['security'] = self.security_manager.get_security_metrics()

            if self.performance_monitor:
                status['performance'] = self.performance_monitor.get_performance_report()

            return web.json_response(status)

        except Exception as e:
            logger.error(f"System status API error: {e}")
            return web.json_response({'error': 'Failed to get system status'}, status=500)

    # Phase 3 API endpoints
    async def api_risk_summary(self, request: web.Request) -> web.Response:
        """Get risk management summary."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.advanced_risk_manager:
                risk_summary = self.advanced_risk_manager.get_risk_summary()
                return web.json_response({
                    'success': True,
                    'risk_summary': risk_summary
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Risk management system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Risk summary API error: {e}")
            return web.json_response({'error': 'Failed to get risk summary'}, status=500)

    async def api_risk_metrics(self, request: web.Request) -> web.Response:
        """Get detailed risk metrics."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.advanced_risk_manager:
                # Get current risk metrics
                metrics = {
                    'portfolio_risk': self.advanced_risk_manager.portfolio_risk.__dict__ if self.advanced_risk_manager.portfolio_risk else None,
                    'position_risks': {k: v.__dict__ for k, v in self.advanced_risk_manager.position_risks.items()},
                    'risk_limits': {
                        'max_position_size': self.advanced_risk_manager.max_position_size,
                        'max_leverage': self.advanced_risk_manager.max_leverage,
                        'max_daily_loss': self.advanced_risk_manager.max_daily_loss
                    },
                    'monitoring_active': self.advanced_risk_manager.monitoring_active
                }

                return web.json_response({
                    'success': True,
                    'metrics': metrics
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Risk management system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Risk metrics API error: {e}")
            return web.json_response({'error': 'Failed to get risk metrics'}, status=500)

    async def api_update_risk_limits(self, request: web.Request) -> web.Response:
        """Update risk management limits."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            data = await request.json()

            if self.advanced_risk_manager:
                # Update risk limits
                if 'max_position_size' in data:
                    self.advanced_risk_manager.max_position_size = float(data['max_position_size'])
                if 'max_leverage' in data:
                    self.advanced_risk_manager.max_leverage = int(data['max_leverage'])
                if 'max_daily_loss' in data:
                    self.advanced_risk_manager.max_daily_loss = float(data['max_daily_loss'])

                return web.json_response({
                    'success': True,
                    'message': 'Risk limits updated successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Risk management system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Update risk limits API error: {e}")
            return web.json_response({'error': 'Failed to update risk limits'}, status=500)

    async def api_analytics_performance(self, request: web.Request) -> web.Response:
        """Get trading performance analytics."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.trading_analytics:
                # Get query parameters
                days_back = int(request.query.get('days', 30))
                strategy = request.query.get('strategy')

                # Generate performance report
                report = await self.trading_analytics.get_performance_report(strategy, days_back)

                return web.json_response({
                    'success': True,
                    'performance': report
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Trading analytics system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Analytics performance API error: {e}")
            return web.json_response({'error': 'Failed to get performance analytics'}, status=500)

    async def api_analytics_trades(self, request: web.Request) -> web.Response:
        """Get trade history for analytics."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.trading_analytics:
                # Get query parameters
                days_back = int(request.query.get('days', 30))

                # Load trades
                trades = await self.trading_analytics.load_trades_from_db(days_back)

                return web.json_response({
                    'success': True,
                    'trades': [trade.__dict__ for trade in trades]
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Trading analytics system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Analytics trades API error: {e}")
            return web.json_response({'error': 'Failed to get trade history'}, status=500)

    async def api_analytics_report(self, request: web.Request) -> web.Response:
        """Get comprehensive analytics report."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.trading_analytics:
                # Get query parameters
                days_back = int(request.query.get('days', 30))
                strategy = request.query.get('strategy')

                # Generate comprehensive report
                report = await self.trading_analytics.get_performance_report(strategy, days_back)

                return web.json_response({
                    'success': True,
                    'report': report
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Trading analytics system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Analytics report API error: {e}")
            return web.json_response({'error': 'Failed to generate analytics report'}, status=500)

    async def api_strategies_status(self, request: web.Request) -> web.Response:
        """Get strategy integration status."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.strategy_integration_manager:
                status = self.strategy_integration_manager.get_strategy_status()

                return web.json_response({
                    'success': True,
                    'status': status
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Strategy integration system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Strategy status API error: {e}")
            return web.json_response({'error': 'Failed to get strategy status'}, status=500)

    async def api_strategies_start(self, request: web.Request) -> web.Response:
        """Start strategy monitoring."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if self.strategy_integration_manager:
                # Start strategy monitoring in background
                asyncio.create_task(self.strategy_integration_manager.start_strategy_monitoring())

                return web.json_response({
                    'success': True,
                    'message': 'Strategy monitoring started'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Strategy integration system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Start strategies API error: {e}")
            return web.json_response({'error': 'Failed to start strategies'}, status=500)

    async def api_strategies_stop(self, request: web.Request) -> web.Response:
        """Stop strategy monitoring."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if self.strategy_integration_manager:
                self.strategy_integration_manager.stop_strategy_monitoring()

                return web.json_response({
                    'success': True,
                    'message': 'Strategy monitoring stopped'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Strategy integration system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Stop strategies API error: {e}")
            return web.json_response({'error': 'Failed to stop strategies'}, status=500)

    async def api_strategies_signals(self, request: web.Request) -> web.Response:
        """Get recent strategy signals."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            if self.strategy_integration_manager:
                # Get recent signals (mock implementation)
                signals = []
                for name, performance in self.strategy_integration_manager.strategy_performance.items():
                    signals.append({
                        'strategy_name': name,
                        'last_signal_time': performance.last_signal_time.isoformat() if performance.last_signal_time else None,
                        'signals_generated': performance.signals_generated,
                        'signals_executed': performance.signals_executed,
                        'status': performance.status.value
                    })

                return web.json_response({
                    'success': True,
                    'signals': signals
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Strategy integration system not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Strategy signals API error: {e}")
            return web.json_response({'error': 'Failed to get strategy signals'}, status=500)

    async def api_production_environment(self, request: web.Request) -> web.Response:
        """Get production environment status."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if self.production_env_manager:
                status = self.production_env_manager.get_environment_status()

                return web.json_response({
                    'success': True,
                    'environment': status
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Production environment manager not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Production environment API error: {e}")
            return web.json_response({'error': 'Failed to get environment status'}, status=500)

    async def api_production_config(self, request: web.Request) -> web.Response:
        """Get production configuration summary."""
        try:
            user_id = request.get('user_id')
            if not user_id:
                return web.json_response({'error': 'Authentication required'}, status=401)

            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if self.production_env_manager:
                config_summary = {
                    'environment': self.production_env_manager.environment,
                    'live_trading_enabled': self.production_env_manager.is_live_trading_enabled(),
                    'htx_environment': self.production_env_manager.config.exchanges.htx.environment,
                    'phase3_available': PHASE3_AVAILABLE,
                    'components': {
                        'risk_manager': self.advanced_risk_manager is not None,
                        'trading_analytics': self.trading_analytics is not None,
                        'strategy_integration': self.strategy_integration_manager is not None
                    }
                }

                return web.json_response({
                    'success': True,
                    'config': config_summary
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Production environment manager not available'
                }, status=503)

        except Exception as e:
            logger.error(f"Production config API error: {e}")
            return web.json_response({'error': 'Failed to get production config'}, status=500)

    # Symbol and Market Data API endpoints
    async def api_get_symbols(self, request: web.Request) -> web.Response:
        """Get list of supported trading symbols."""
        try:
            # Load symbols from configuration
            symbols_config_path = Path('config/symbols.json')
            if symbols_config_path.exists():
                with open(symbols_config_path, 'r') as f:
                    symbols_config = json.load(f)

                return web.json_response({
                    'success': True,
                    'symbols': symbols_config['supported_symbols'],
                    'default_symbol': symbols_config['default_symbol'],
                    'symbol_mapping': symbols_config['symbol_mapping']
                })
            else:
                # Fallback symbols
                fallback_symbols = [
                    "DOGE/USDT:USDT",
                    "BTC/USDT:USDT",
                    "ETH/USDT:USDT",
                    "BNB/USDT:USDT",
                    "ADA/USDT:USDT",
                    "SOL/USDT:USDT",
                    "XRP/USDT:USDT",
                    "DOT/USDT:USDT",
                    "AVAX/USDT:USDT"
                ]

                return web.json_response({
                    'success': True,
                    'symbols': fallback_symbols,
                    'default_symbol': 'DOGE/USDT:USDT'
                })

        except Exception as e:
            logger.error(f"Get symbols API error: {e}")
            return web.json_response({'error': 'Failed to get symbols'}, status=500)

    async def api_get_market_ticker(self, request: web.Request) -> web.Response:
        """Get market ticker data for a specific symbol."""
        try:
            symbol = request.match_info['symbol']

            # Normalize symbol format
            if ':' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT:USDT"
            elif '/' in symbol and ':' not in symbol:
                symbol = f"{symbol}:USDT"

            # Try to get real market data from HTX client
            if self.htx_futures_client:
                try:
                    market_data = await self.htx_futures_client.get_market_data(symbol)
                    if market_data:
                        return web.json_response({
                            'success': True,
                            'symbol': symbol,
                            'data': market_data,
                            'source': 'htx_live'
                        })
                except Exception as e:
                    logger.warning(f"HTX market data error for {symbol}: {e}")

            # Try to get data from market data manager
            if self.market_data_manager:
                try:
                    tick = self.market_data_manager.get_market_tick(symbol)
                    if tick:
                        return web.json_response({
                            'success': True,
                            'symbol': symbol,
                            'data': {
                                'price': tick.price,
                                'bid': tick.bid,
                                'ask': tick.ask,
                                'volume_24h': tick.volume_24h,
                                'change_24h': tick.change_24h_percent,
                                'high_24h': tick.high_24h,
                                'low_24h': tick.low_24h,
                                'timestamp': tick.timestamp.isoformat()
                            },
                            'source': 'market_data_manager'
                        })
                except Exception as e:
                    logger.warning(f"Market data manager error for {symbol}: {e}")

            # Fallback to mock data
            mock_data = self._generate_mock_market_data(symbol)
            return web.json_response({
                'success': True,
                'symbol': symbol,
                'data': mock_data,
                'source': 'mock'
            })

        except Exception as e:
            logger.error(f"Get market ticker API error: {e}")
            return web.json_response({'error': 'Failed to get market ticker'}, status=500)

    async def api_get_market_data(self, request: web.Request) -> web.Response:
        """Get comprehensive market data for a symbol."""
        try:
            symbol = request.match_info['symbol']

            # Normalize symbol format
            if ':' not in symbol and symbol.endswith('USDT'):
                base = symbol[:-4]
                symbol = f"{base}/USDT:USDT"
            elif '/' in symbol and ':' not in symbol:
                symbol = f"{symbol}:USDT"

            # Get comprehensive market data
            market_data = {
                'symbol': symbol,
                'ticker': None,
                'orderbook': None,
                'recent_trades': None,
                'timestamp': datetime.now().isoformat()
            }

            # Try to get ticker data
            try:
                if self.htx_futures_client:
                    ticker_data = await self.htx_futures_client.get_market_data(symbol)
                    market_data['ticker'] = ticker_data
                elif self.market_data_manager:
                    tick = self.market_data_manager.get_market_tick(symbol)
                    if tick:
                        market_data['ticker'] = {
                            'price': tick.price,
                            'bid': tick.bid,
                            'ask': tick.ask,
                            'volume_24h': tick.volume_24h,
                            'change_24h': tick.change_24h_percent,
                            'high_24h': tick.high_24h,
                            'low_24h': tick.low_24h
                        }

                if not market_data['ticker']:
                    market_data['ticker'] = self._generate_mock_market_data(symbol)

            except Exception as e:
                logger.warning(f"Ticker data error for {symbol}: {e}")
                market_data['ticker'] = self._generate_mock_market_data(symbol)

            # Generate mock orderbook and trades for now
            market_data['orderbook'] = self._generate_mock_orderbook(symbol, market_data['ticker']['price'])
            market_data['recent_trades'] = self._generate_mock_recent_trades(symbol, market_data['ticker']['price'])

            return web.json_response({
                'success': True,
                'data': market_data,
                'source': 'comprehensive'
            })

        except Exception as e:
            logger.error(f"Get market data API error: {e}")
            return web.json_response({'error': 'Failed to get market data'}, status=500)

    def _generate_mock_market_data(self, symbol: str) -> dict:
        """Generate mock market data for testing."""
        base_prices = {
            'DOGE/USDT:USDT': 0.3850,
            'BTC/USDT:USDT': 43250.00,
            'ETH/USDT:USDT': 2650.00,
            'BNB/USDT:USDT': 315.50,
            'ADA/USDT:USDT': 0.4820,
            'SOL/USDT:USDT': 98.75,
            'XRP/USDT:USDT': 0.6150,
            'DOT/USDT:USDT': 7.25,
            'AVAX/USDT:USDT': 38.90
        }

        base_price = base_prices.get(symbol, 1.0)

        # Add some random variation
        import random
        variation = (random.random() - 0.5) * 0.02  # ±1%
        price = base_price * (1 + variation)

        return {
            'price': round(price, 6),
            'bid': round(price * 0.9995, 6),
            'ask': round(price * 1.0005, 6),
            'volume_24h': round(random.random() * 10000000, 0),
            'change_24h': round((random.random() * 10 - 5), 2),  # ±5%
            'high_24h': round(price * 1.05, 6),
            'low_24h': round(price * 0.95, 6),
            'timestamp': datetime.now().isoformat()
        }

    def _generate_mock_orderbook(self, symbol: str, base_price: float) -> dict:
        """Generate mock orderbook data."""
        import random

        asks = []
        bids = []

        for i in range(10):
            ask_price = base_price + (i + 1) * base_price * 0.0001
            bid_price = base_price - (i + 1) * base_price * 0.0001

            asks.append({
                'price': round(ask_price, 6),
                'size': round(random.random() * 10000 + 1000, 0),
                'total': round((ask_price * (random.random() * 10000 + 1000)), 2)
            })

            bids.append({
                'price': round(bid_price, 6),
                'size': round(random.random() * 10000 + 1000, 0),
                'total': round((bid_price * (random.random() * 10000 + 1000)), 2)
            })

        return {
            'asks': asks,
            'bids': bids,
            'timestamp': datetime.now().isoformat()
        }

    def _generate_mock_recent_trades(self, symbol: str, base_price: float) -> list:
        """Generate mock recent trades data."""
        import random
        from datetime import timedelta

        trades = []
        current_time = datetime.now()

        for i in range(20):
            trade_time = current_time - timedelta(seconds=i * 5)
            price_variation = (random.random() - 0.5) * 0.001  # ±0.1%
            trade_price = base_price * (1 + price_variation)

            trades.append({
                'price': round(trade_price, 6),
                'size': round(random.random() * 1000 + 100, 0),
                'side': 'buy' if random.random() > 0.5 else 'sell',
                'timestamp': trade_time.isoformat()
            })

        return trades

    async def api_system_metrics(self, request: web.Request) -> web.Response:
        """Get real-time system metrics."""
        try:
            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if not self.performance_monitor:
                return web.json_response({'error': 'Performance monitoring not available'}, status=503)

            metrics = await self.performance_monitor.collect_system_metrics()
            return web.json_response(metrics)

        except Exception as e:
            logger.error(f"System metrics API error: {e}")
            return web.json_response({'error': 'Failed to get system metrics'}, status=500)

    async def api_create_backup(self, request: web.Request) -> web.Response:
        """Create system backup."""
        try:
            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if not self.backup_manager:
                return web.json_response({'error': 'Backup system not available'}, status=503)

            backup_info = await self.backup_manager.create_full_backup()
            return web.json_response(backup_info)

        except Exception as e:
            logger.error(f"Create backup API error: {e}")
            return web.json_response({'error': 'Failed to create backup'}, status=500)

    async def api_list_backups(self, request: web.Request) -> web.Response:
        """List available backups."""
        try:
            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if not self.backup_manager:
                return web.json_response({'error': 'Backup system not available'}, status=503)

            backups = self.backup_manager.list_backups()
            return web.json_response({'backups': backups})

        except Exception as e:
            logger.error(f"List backups API error: {e}")
            return web.json_response({'error': 'Failed to list backups'}, status=500)

    async def api_restore_backup(self, request: web.Request) -> web.Response:
        """Restore from backup."""
        try:
            # Check admin access
            user = request.get('user')
            if not user or user.get('role') != 'admin':
                return web.json_response({'error': 'Admin access required'}, status=403)

            if not self.backup_manager:
                return web.json_response({'error': 'Backup system not available'}, status=503)

            backup_name = request.match_info['backup_name']
            restore_info = await self.backup_manager.restore_backup(backup_name)
            return web.json_response(restore_info)

        except Exception as e:
            logger.error(f"Restore backup API error: {e}")
            return web.json_response({'error': 'Failed to restore backup'}, status=500)

    # Live Trading Handler Methods

    async def serve_live_trading(self, request: web.Request) -> web.Response:
        """Serve live trading interface (epinnox admin only)."""
        try:
            # Check if user is epinnox admin
            user = request.get('user')
            if not user or user.get('username') != 'epinnox' or user.get('role') != 'admin':
                return web.Response(status=302, headers={'Location': '/dashboard'})

            # Load and render live trading template
            template = self.jinja_env.get_template('live_trading.html')
            html_content = template.render(
                username=user.get('username'),
                request=request
            )

            return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            logger.error(f"Live trading page error: {e}")
            return web.Response(status=302, headers={'Location': '/dashboard'})

    async def handle_live_trading_websocket(self, request: web.Request) -> web.WebSocketResponse:
        """Handle live trading WebSocket connection."""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        try:
            # Check authentication
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                await ws.close(code=web.WSMsgType.CLOSE, message=b'Unauthorized')
                return ws

            logger.info(f"[LIVE] WebSocket connected: {user.get('username')}")

            # Start live trading monitoring if not already running
            if self.advanced_trading_interface and not self.advanced_trading_interface.running:
                asyncio.create_task(self.advanced_trading_interface.start_monitoring())

            # Send initial trading state
            if self.advanced_trading_interface:
                trading_state = self.advanced_trading_interface.get_trading_state()
                await ws.send_str(json.dumps(trading_state))

            # Handle WebSocket messages
            async for msg in ws:
                if msg.type == web.WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)

                        if data.get('type') == 'get_trading_state':
                            if self.advanced_trading_interface:
                                trading_state = self.advanced_trading_interface.get_trading_state()
                                await ws.send_str(json.dumps(trading_state))

                    except json.JSONDecodeError:
                        logger.warning("[LIVE] Invalid WebSocket message format")

                elif msg.type == web.WSMsgType.ERROR:
                    logger.error(f"[LIVE] WebSocket error: {ws.exception()}")
                    break

        except Exception as e:
            logger.error(f"[LIVE] WebSocket error: {e}")
        finally:
            logger.info("[LIVE] WebSocket disconnected")

        return ws

    async def api_place_live_order(self, request: web.Request) -> web.Response:
        """API endpoint to place live trading order."""
        try:
            # Check authentication and authorization
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                return web.json_response({'error': 'Unauthorized'}, status=401)

            if not self.htx_futures_client:
                return web.json_response({'error': 'Live trading not available'}, status=503)

            data = await request.json()
            side = data.get('side')  # 'buy' or 'sell'
            amount = float(data.get('amount', 0))
            leverage = int(data.get('leverage', 10))

            if not side or amount <= 0:
                return web.json_response({'error': 'Invalid order parameters'}, status=400)

            # Place order
            order = await self.htx_futures_client.place_market_order(side, amount, leverage)

            if order:
                logger.info(f"[LIVE] Order placed: {side} {amount} DOGE at {leverage}x leverage")
                return web.json_response({'success': True, 'order': order})
            else:
                return web.json_response({'error': 'Failed to place order'}, status=500)

        except Exception as e:
            logger.error(f"[LIVE] Order placement error: {e}")
            return web.json_response({'error': 'Order placement failed'}, status=500)

    async def api_close_live_position(self, request: web.Request) -> web.Response:
        """API endpoint to close live position."""
        try:
            # Check authentication and authorization
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                return web.json_response({'error': 'Unauthorized'}, status=401)

            if not self.htx_futures_client:
                return web.json_response({'error': 'Live trading not available'}, status=503)

            # Close position
            success = await self.htx_futures_client.close_position()

            if success:
                logger.info("[LIVE] Position closed successfully")
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Failed to close position'}, status=500)

        except Exception as e:
            logger.error(f"[LIVE] Position close error: {e}")
            return web.json_response({'error': 'Position close failed'}, status=500)

    async def api_emergency_close_all(self, request: web.Request) -> web.Response:
        """API endpoint for emergency close all positions."""
        try:
            # Check authentication and authorization
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                return web.json_response({'error': 'Unauthorized'}, status=401)

            if not self.advanced_trading_interface:
                return web.json_response({'error': 'Advanced trading not available'}, status=503)

            # Emergency close all
            success = await self.advanced_trading_interface.emergency_close_all()

            if success:
                logger.warning("[LIVE] Emergency close all executed")
                return web.json_response({'success': True})
            else:
                return web.json_response({'error': 'Emergency close failed'}, status=500)

        except Exception as e:
            logger.error(f"[LIVE] Emergency close error: {e}")
            return web.json_response({'error': 'Emergency close failed'}, status=500)

    async def api_update_automation(self, request: web.Request) -> web.Response:
        """API endpoint to update automation settings."""
        try:
            # Check authentication and authorization
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                return web.json_response({'error': 'Unauthorized'}, status=401)

            if not self.advanced_trading_interface:
                return web.json_response({'error': 'Advanced trading not available'}, status=503)

            data = await request.json()

            # Update automation settings
            from trading.advanced_trading_interface import AutomationFeature

            for setting_name, config in data.items():
                if config.get('enabled'):
                    try:
                        feature = AutomationFeature(setting_name)
                        parameters = {k: v for k, v in config.items() if k != 'enabled'}
                        self.advanced_trading_interface.enable_automation(feature, parameters)
                    except ValueError:
                        logger.warning(f"[LIVE] Unknown automation feature: {setting_name}")

            logger.info(f"[LIVE] Automation settings updated: {list(data.keys())}")
            return web.json_response({'success': True})

        except Exception as e:
            logger.error(f"[LIVE] Automation update error: {e}")
            return web.json_response({'error': 'Automation update failed'}, status=500)

    async def api_get_trading_state(self, request: web.Request) -> web.Response:
        """API endpoint to get current trading state."""
        try:
            # Check authentication and authorization
            user = request.get('user')
            if not user or user.get('username') != 'epinnox':
                return web.json_response({'error': 'Unauthorized'}, status=401)

            if not self.advanced_trading_interface:
                return web.json_response({'error': 'Advanced trading not available'}, status=503)

            trading_state = self.advanced_trading_interface.get_trading_state()
            return web.json_response(trading_state)

        except Exception as e:
            logger.error(f"[LIVE] Trading state error: {e}")
            return web.json_response({'error': 'Failed to get trading state'}, status=500)

    async def start_server(self):
        """Start the Money Circle server."""
        try:
            app = await self.create_app()

            # Start the web server FIRST
            runner = web.AppRunner(app)
            await runner.setup()

            site = web.TCPSite(runner, self.config.HOST, self.config.PORT)
            await site.start()

            logger.info(f"[SERVER] Money Circle running at http://{self.config.HOST}:{self.config.PORT}")
            logger.info(f"[DEVELOPMENT] Running in {'development' if self.config.DEBUG else 'production'} mode")
            logger.info("[READY] Platform ready for Money Circle investment club members")
            logger.info("[LIVE] Live trading interface available at /live-trading (epinnox only)")

            # Start background tasks AFTER web server is running
            asyncio.create_task(self._start_background_tasks())

            # Keep the server running
            while True:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Server startup error: {e}")
            raise

    async def _start_background_tasks(self):
        """Start background tasks after web server is running."""
        try:
            logger.info("[BACKGROUND] Starting background tasks...")

            # Start market data manager
            if self.market_data_manager:
                try:
                    await self.market_data_manager.start()
                    logger.info("[DATA] Advanced market data manager started")
                except Exception as e:
                    logger.warning(f"[DATA] Market data manager startup warning: {e}")

            # Start websocket streamer
            if self.websocket_streamer:
                try:
                    await self.websocket_streamer.start()
                    logger.info("[WS] Market data WebSocket streamer subscribed to updates")
                except Exception as e:
                    logger.warning(f"[WS] WebSocket streamer startup warning: {e}")

            # Start notification manager
            if self.notification_manager:
                try:
                    await self.notification_manager.start()
                    logger.info("[NOTIFY] Notification system started")
                except Exception as e:
                    logger.warning(f"[NOTIFY] Notification manager startup warning: {e}")

            # Start live trading interface
            if self.live_trading_interface:
                try:
                    await self.live_trading_interface.start()
                    logger.info("[TRADING] Live trading monitoring started")
                except Exception as e:
                    logger.warning(f"[TRADING] Live trading interface startup warning: {e}")

            # Start strategy engine
            if self.live_strategy_engine:
                try:
                    await self.live_strategy_engine.start()
                    logger.info("[STRATEGY] Strategy automation started")
                except Exception as e:
                    logger.warning(f"[STRATEGY] Strategy engine startup warning: {e}")

            # Start live trading monitoring for epinnox
            if self.htx_futures_client and self.advanced_trading_interface:
                try:
                    asyncio.create_task(self.htx_futures_client.start_monitoring())
                    logger.info("[LIVE] HTX Futures monitoring started for epinnox")
                except Exception as e:
                    logger.warning(f"[LIVE] HTX Futures monitoring startup warning: {e}")

            logger.info("[BACKGROUND] All background tasks started successfully")

        except Exception as e:
            logger.error(f"[BACKGROUND] Background tasks startup error: {e}")
            # Don't raise - let web server continue running

async def main():
    """Main entry point."""
    config_name = os.getenv('FLASK_ENV', 'development')
    app = MoneyCircleApp(config_name)
    await app.start_server()

if __name__ == '__main__':
    asyncio.run(main())
