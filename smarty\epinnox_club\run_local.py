#!/usr/bin/env python3
"""
Local Development Runner for Money Circle
Simplified startup script for local testing
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_local_environment():
    """Setup local development environment variables."""
    logger.info("🔧 Setting up local development environment...")

    # Set local development environment variables
    os.environ['ENVIRONMENT'] = 'development'
    os.environ['DEBUG'] = 'true'
    os.environ['HOST'] = 'localhost'
    os.environ['PORT'] = '8086'
    os.environ['JWT_SECRET'] = 'local-development-secret-key-not-for-production-use-only'
    os.environ['SESSION_TIMEOUT'] = '7200'
    os.environ['LIVE_TRADING_ENABLED'] = 'false'  # Safe for local testing
    os.environ['TESTNET_MODE'] = 'true'
    os.environ['MONITORING_ENABLED'] = 'true'
    os.environ['BACKUP_ENABLED'] = 'false'  # Skip backups for local dev

    logger.info("✅ Local environment configured")

def check_dependencies():
    """Check if required dependencies are installed."""
    logger.info("📦 Checking dependencies...")

    required_modules = [
        'aiohttp',
        'aiohttp_jinja2',
        'aiosqlite',
        'bcrypt',
        'jwt',
        'ccxt',
        'jinja2',
        'cryptography'
    ]

    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        logger.error(f"❌ Missing dependencies: {missing_modules}")
        logger.info("💡 Run: pip install -r requirements.txt")
        return False

    logger.info("✅ All dependencies available")
    return True

def setup_directories():
    """Create necessary directories for local development."""
    logger.info("📁 Setting up directories...")

    directories = ['data', 'logs', 'static/uploads']

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

    logger.info("✅ Directories created")

def check_database():
    """Check if database exists and is accessible."""
    logger.info("🗄️ Checking database...")

    db_path = Path('data/money_circle.db')

    if not db_path.exists():
        logger.info("📝 Database not found, will be created on startup")
        return True

    try:
        import sqlite3
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()

        if tables:
            logger.info(f"✅ Database found with {len(tables)} tables")
        else:
            logger.info("📝 Empty database found, will be initialized")

        return True

    except Exception as e:
        logger.warning(f"⚠️ Database check warning: {e}")
        return True  # Continue anyway

async def start_money_circle():
    """Start the Money Circle application."""
    logger.info("🚀 Starting Money Circle Investment Club Platform...")

    try:
        # Import and start the main application
        from app import MoneyCircleApp

        # Create application instance
        app = MoneyCircleApp('development')
        logger.info("✅ Money Circle application created")

        # Start the server
        await app.start_server()

    except KeyboardInterrupt:
        logger.info("🛑 Money Circle stopped by user")
    except Exception as e:
        logger.error(f"❌ Error starting Money Circle: {e}")
        logger.info("💡 Check the error above and ensure all dependencies are installed")
        raise

def main():
    """Main entry point for local development."""
    print("💰 Money Circle Investment Club Platform")
    print("🖥️ Local Development Mode")
    print("=" * 50)

    # Setup local environment
    setup_local_environment()

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first:")
        print("   pip install -r requirements.txt")
        return 1

    # Setup directories
    setup_directories()

    # Check database
    check_database()

    # Start the application
    print("\n🌐 Starting Money Circle server...")
    print("📍 Platform will be available at: http://localhost:8086")
    print("🔐 Default login: epinnox / securepass123")
    print("🎯 Press Ctrl+C to stop the server")
    print("🧪 Run 'python test_fixes.py' in another terminal to test fixes")
    print("\n" + "=" * 50)

    try:
        asyncio.run(start_money_circle())
    except KeyboardInterrupt:
        print("\n🛑 Money Circle server stopped")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        return 1

    return 0

if __name__ == '__main__':
    sys.exit(main())
