#!/usr/bin/env python3
"""
Simple Money Circle Platform Test
Quick test to verify core functionality works without middleware issues
"""

import asyncio
import aiohttp
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_platform():
    """Test core Money Circle platform functionality."""
    logger.info("🧪 Testing Money Circle Core Platform")
    logger.info("=" * 40)
    
    base_url = "http://localhost:8086"
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # Test 1: Health endpoint
            logger.info("📋 Testing health endpoint...")
            try:
                async with session.get(f"{base_url}/health") as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        logger.info(f"✅ Health endpoint: {data.get('status', 'unknown')}")
                    else:
                        logger.error(f"❌ Health endpoint failed: {resp.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Health endpoint error: {e}")
                return False
            
            # Test 2: Homepage
            logger.info("📋 Testing homepage...")
            try:
                async with session.get(base_url, allow_redirects=False) as resp:
                    if resp.status == 302:
                        logger.info("✅ Homepage redirects correctly")
                    else:
                        logger.error(f"❌ Homepage failed: {resp.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Homepage error: {e}")
                return False
            
            # Test 3: Login page
            logger.info("📋 Testing login page...")
            try:
                async with session.get(f"{base_url}/login") as resp:
                    if resp.status == 200:
                        logger.info("✅ Login page loads correctly")
                    else:
                        logger.error(f"❌ Login page failed: {resp.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Login page error: {e}")
                return False
            
            logger.info("\n🎉 ALL CORE TESTS PASSED!")
            logger.info("✅ Money Circle platform is working correctly")
            logger.info("✅ No middleware errors")
            logger.info("✅ Ready for full testing")
            
            return True
    
    except Exception as e:
        logger.error(f"❌ Test session error: {e}")
        return False

async def main():
    """Main test function."""
    print("🧪 Money Circle Core Platform Test")
    print("Testing without performance middleware")
    print()
    
    try:
        success = await test_platform()
        
        if success:
            print("\n🎯 PLATFORM READY!")
            print("🌐 Access: http://localhost:8086")
            print("🔐 Login: epinnox / securepass123")
            print("📊 Test all features:")
            print("   • Admin Dashboard")
            print("   • Auto Trader")
            print("   • Trading Signals") 
            print("   • Portfolio Analytics")
            print("   • Social Trading")
            print("\n🚀 Ready for GitHub and Render deployment!")
        else:
            print("\n❌ Platform has issues - check server logs")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        return 1

if __name__ == '__main__':
    exit(asyncio.run(main()))
