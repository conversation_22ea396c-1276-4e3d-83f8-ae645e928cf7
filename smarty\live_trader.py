"""
Enhanced live trading system for the smart-trader.

Integrates real-time model execution, monitoring, and risk management
for live trading with comprehensive observability.
"""

import asyncio
import logging
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path

from orchestrator import Orchestrator
from monitoring.model_monitor import ModelPerformanceMonitor
# Note: SignalDashboard removed - using unified dashboard instead
from core.feature_store import feature_store
from core.utils import setup_logging

logger = logging.getLogger(__name__)


class LiveTradingSystem:
    """
    Enhanced live trading system with integrated monitoring and risk management.

    Features:
    - Real-time model execution
    - Performance monitoring
    - Web dashboard
    - Risk management
    - Alert system
    - Trade logging
    """

    def __init__(self, config_path: str):
        """
        Initialize the live trading system.

        Args:
            config_path: Path to configuration file
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()

        # Setup logging
        log_level = getattr(logging, self.config.get("log_level", "INFO"))
        setup_logging(level=log_level)

        # Initialize components
        self.orchestrator = Orchestrator(self.config)
        self.monitor = ModelPerformanceMonitor(self.config)
        # Note: Using unified dashboard instead of SignalDashboard

        # Trading state
        self.running = False
        self.start_time = datetime.now()
        self.trade_count = 0
        self.total_pnl = 0.0

        # Performance tracking
        self.daily_stats = {
            "trades": 0,
            "profitable_trades": 0,
            "total_pnl": 0.0,
            "max_drawdown": 0.0,
            "start_balance": 0.0,
            "current_balance": 0.0
        }

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Validate required configuration
            self._validate_config(config)

            logger.info(f"Loaded configuration from {self.config_path}")
            return config

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

    def _validate_config(self, config: Dict[str, Any]) -> None:
        """Validate configuration parameters."""
        required_keys = ["trading"]

        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required configuration key: {key}")

        # Set default symbols if not provided
        if "symbols" not in config:
            config["symbols"] = ["BTC-USDT"]
            logger.info("No symbols specified, using default: BTC-USDT")

        # Validate trading configuration
        trading_config = config["trading"]
        if not isinstance(trading_config.get("enabled"), bool):
            raise ValueError("trading.enabled must be a boolean")

        if trading_config.get("enabled") and not trading_config.get("simulation_mode", True):
            # Additional validation for live trading
            if not config.get("api_key") or not config.get("api_secret"):
                raise ValueError("API credentials required for live trading")

            logger.warning("⚠️  LIVE TRADING MODE ENABLED - REAL MONEY AT RISK ⚠️")

    async def start(self) -> None:
        """Start the live trading system."""
        logger.info("🚀 Starting Enhanced Live Trading System")

        # Log configuration summary
        self._log_startup_summary()

        try:
            # Start monitoring
            await self.monitor.start()
            logger.info("✅ Model performance monitor started")

            # Note: Unified dashboard runs separately
            logger.info("💡 Use unified dashboard at http://localhost:8081")

            # Start orchestrator
            await self.orchestrator.start()
            logger.info("✅ Trading orchestrator started")

            self.running = True

            # Start additional monitoring tasks
            await self._start_monitoring_tasks()

            logger.info("🎯 Live trading system fully operational")

            # Keep running until stopped
            while self.running:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Error in live trading system: {e}")
            raise
        finally:
            await self.stop()

    async def stop(self) -> None:
        """Stop the live trading system."""
        logger.info("🛑 Stopping live trading system")

        self.running = False

        try:
            # Stop orchestrator
            await self.orchestrator.stop()
            logger.info("✅ Trading orchestrator stopped")

            # Note: Unified dashboard runs separately
            logger.info("💡 Unified dashboard continues running")

            # Generate final report
            await self._generate_final_report()

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

        logger.info("🏁 Live trading system stopped")

    async def _start_monitoring_tasks(self) -> None:
        """Start additional monitoring tasks."""
        # Performance tracking task
        asyncio.create_task(self._track_performance())

        # Daily reset task
        asyncio.create_task(self._daily_reset_task())

        # Health monitoring task
        asyncio.create_task(self._health_monitoring_task())

        # Trade logging task
        asyncio.create_task(self._trade_logging_task())

    async def _track_performance(self) -> None:
        """Track trading performance metrics."""
        while self.running:
            try:
                # Get current account balance
                balance = await self._get_current_balance()
                if balance:
                    self.daily_stats["current_balance"] = balance

                    # Calculate drawdown
                    if self.daily_stats["start_balance"] > 0:
                        drawdown = (self.daily_stats["start_balance"] - balance) / self.daily_stats["start_balance"]
                        self.daily_stats["max_drawdown"] = max(self.daily_stats["max_drawdown"], drawdown)

                # Update performance metrics in feature store
                await feature_store.set("system", "daily_stats", self.daily_stats)

                await asyncio.sleep(60)  # Update every minute

            except Exception as e:
                logger.error(f"Error tracking performance: {e}")
                await asyncio.sleep(60)

    async def _daily_reset_task(self) -> None:
        """Reset daily statistics at midnight."""
        while self.running:
            try:
                now = datetime.now()

                # Check if it's a new day
                if now.hour == 0 and now.minute == 0:
                    await self._reset_daily_stats()
                    await asyncio.sleep(60)  # Sleep for a minute to avoid multiple resets

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in daily reset task: {e}")
                await asyncio.sleep(60)

    async def _health_monitoring_task(self) -> None:
        """Monitor system health and send alerts."""
        while self.running:
            try:
                # Check system health
                health = await self.monitor.get_system_health()

                # Check for critical issues
                if health.failed_models > 0:
                    logger.warning(f"⚠️ {health.failed_models} models have failed")

                if health.active_models == 0:
                    logger.error("🚨 No active models - trading may be impaired")

                # Check account balance
                balance = await self._get_current_balance()
                if balance and balance < 10:  # Less than $10
                    logger.warning(f"⚠️ Low account balance: ${balance:.2f}")

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(300)

    async def _trade_logging_task(self) -> None:
        """Log trading activity and performance."""
        while self.running:
            try:
                # Get recent trades from feature store
                trades = await feature_store.get("system", "recent_trades")

                if trades:
                    for trade in trades:
                        # Log trade details
                        logger.info(f"📊 Trade: {trade.get('symbol')} {trade.get('action')} "
                                  f"{trade.get('size')} @ {trade.get('price')} "
                                  f"PnL: ${trade.get('pnl', 0):.2f}")

                        # Update daily stats
                        self.daily_stats["trades"] += 1
                        if trade.get('pnl', 0) > 0:
                            self.daily_stats["profitable_trades"] += 1

                        self.daily_stats["total_pnl"] += trade.get('pnl', 0)

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in trade logging: {e}")
                await asyncio.sleep(30)

    async def _get_current_balance(self) -> Optional[float]:
        """Get current account balance."""
        try:
            # Get balance from orchestrator's executor
            if hasattr(self.orchestrator, 'executor') and hasattr(self.orchestrator.executor, 'get_balance'):
                balance_info = await self.orchestrator.executor.get_balance()
                return balance_info.get('available_balance', 0.0)

            return None

        except Exception as e:
            logger.error(f"Error getting current balance: {e}")
            return None

    async def _reset_daily_stats(self) -> None:
        """Reset daily statistics."""
        logger.info("🔄 Resetting daily statistics")

        # Save yesterday's stats
        yesterday_stats = self.daily_stats.copy()
        await feature_store.set("system", f"daily_stats_{datetime.now().strftime('%Y%m%d')}", yesterday_stats)

        # Reset current stats
        current_balance = await self._get_current_balance()
        self.daily_stats = {
            "trades": 0,
            "profitable_trades": 0,
            "total_pnl": 0.0,
            "max_drawdown": 0.0,
            "start_balance": current_balance or 0.0,
            "current_balance": current_balance or 0.0
        }

    async def _generate_final_report(self) -> None:
        """Generate final trading report."""
        logger.info("📊 Generating final trading report")

        try:
            # Get performance summary
            summary = await self.monitor.get_performance_summary()

            # Calculate session statistics
            session_duration = (datetime.now() - self.start_time).total_seconds() / 3600  # hours

            report = {
                "session_summary": {
                    "start_time": self.start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_hours": round(session_duration, 2),
                    "total_trades": self.daily_stats["trades"],
                    "profitable_trades": self.daily_stats["profitable_trades"],
                    "win_rate": (self.daily_stats["profitable_trades"] / max(self.daily_stats["trades"], 1)) * 100,
                    "total_pnl": self.daily_stats["total_pnl"],
                    "max_drawdown": self.daily_stats["max_drawdown"] * 100
                },
                "model_performance": summary.get("models", {}),
                "signal_performance": summary.get("signals", {}),
                "system_health": summary.get("system_health", {})
            }

            # Save report
            report_file = f"reports/trading_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            Path(report_file).parent.mkdir(parents=True, exist_ok=True)

            import json
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"📄 Final report saved to {report_file}")

            # Log summary
            logger.info(f"📈 Session Summary:")
            logger.info(f"   Duration: {session_duration:.1f} hours")
            logger.info(f"   Total Trades: {self.daily_stats['trades']}")
            logger.info(f"   Win Rate: {(self.daily_stats['profitable_trades'] / max(self.daily_stats['trades'], 1)) * 100:.1f}%")
            logger.info(f"   Total PnL: ${self.daily_stats['total_pnl']:.2f}")
            logger.info(f"   Max Drawdown: {self.daily_stats['max_drawdown'] * 100:.1f}%")

        except Exception as e:
            logger.error(f"Error generating final report: {e}")

    def _log_startup_summary(self) -> None:
        """Log startup configuration summary."""
        trading_config = self.config.get("trading", {})

        logger.info("🔧 Configuration Summary:")
        logger.info(f"   Symbols: {', '.join(self.config.get('symbols', []))}")
        logger.info(f"   Trading Enabled: {trading_config.get('enabled', False)}")
        logger.info(f"   Simulation Mode: {trading_config.get('simulation_mode', True)}")
        logger.info(f"   Max Positions: {trading_config.get('max_positions', 1)}")

        # Log enabled models
        enabled_models = [key.replace('enable_', '').replace('_model', '')
                         for key, value in self.config.items()
                         if key.startswith('enable_') and key.endswith('_model') and value]

        logger.info(f"   Enabled Models: {', '.join(enabled_models)}")

        # Log dashboard info
        dashboard_config = self.config.get("dashboard", {})
        if dashboard_config.get("enabled", True):
            host = dashboard_config.get("host", "localhost")
            port = dashboard_config.get("port", 8080)
            logger.info(f"   Dashboard: http://{host}:{port}")


async def main():
    """Main entry point for live trading."""
    import sys

    if len(sys.argv) != 2:
        print("Usage: python live_trader.py <config_file>")
        sys.exit(1)

    config_file = sys.argv[1]

    # Create and start live trading system
    live_trader = LiveTradingSystem(config_file)
    await live_trader.start()


if __name__ == "__main__":
    asyncio.run(main())
