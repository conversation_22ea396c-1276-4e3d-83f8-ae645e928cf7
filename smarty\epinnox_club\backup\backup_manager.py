"""
Backup Manager
Automated backup and recovery system for Money Circle production deployment
"""

import os
import shutil
import sqlite3
import gzip
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import hashlib

logger = logging.getLogger(__name__)

class BackupManager:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_dir = config.get('backup_dir', 'backups')
        self.max_backups = config.get('max_backups', 30)
        self.backup_interval = config.get('backup_interval', 3600)  # 1 hour
        self.compression_enabled = config.get('compression_enabled', True)
        
        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)
        
        logger.info(f"[BACKUP] Backup Manager initialized - Directory: {self.backup_dir}")

    async def create_full_backup(self) -> Dict[str, Any]:
        """Create a complete system backup."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"money_circle_backup_{timestamp}"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            logger.info(f"[BACKUP] Starting full backup: {backup_name}")
            
            # Create backup directory
            os.makedirs(backup_path, exist_ok=True)
            
            # Backup components
            backup_info = {
                'timestamp': timestamp,
                'backup_name': backup_name,
                'backup_path': backup_path,
                'components': {}
            }
            
            # 1. Database backup
            db_backup_info = await self._backup_database(backup_path)
            backup_info['components']['database'] = db_backup_info
            
            # 2. Configuration backup
            config_backup_info = await self._backup_configuration(backup_path)
            backup_info['components']['configuration'] = config_backup_info
            
            # 3. Static files backup
            static_backup_info = await self._backup_static_files(backup_path)
            backup_info['components']['static_files'] = static_backup_info
            
            # 4. Logs backup
            logs_backup_info = await self._backup_logs(backup_path)
            backup_info['components']['logs'] = logs_backup_info
            
            # 5. User data backup
            user_data_backup_info = await self._backup_user_data(backup_path)
            backup_info['components']['user_data'] = user_data_backup_info
            
            # Create backup manifest
            manifest_path = os.path.join(backup_path, 'backup_manifest.json')
            with open(manifest_path, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            # Calculate backup size and checksum
            backup_size = self._calculate_directory_size(backup_path)
            backup_checksum = await self._calculate_backup_checksum(backup_path)
            
            backup_info['size_bytes'] = backup_size
            backup_info['checksum'] = backup_checksum
            backup_info['status'] = 'completed'
            
            # Compress backup if enabled
            if self.compression_enabled:
                compressed_path = await self._compress_backup(backup_path)
                backup_info['compressed_path'] = compressed_path
                backup_info['compressed_size'] = os.path.getsize(compressed_path)
                
                # Remove uncompressed backup
                shutil.rmtree(backup_path)
                backup_info['backup_path'] = compressed_path
            
            logger.info(f"[BACKUP] Full backup completed: {backup_name} ({backup_size} bytes)")
            
            # Clean up old backups
            await self._cleanup_old_backups()
            
            return backup_info
            
        except Exception as e:
            logger.error(f"[BACKUP] Full backup failed: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def _backup_database(self, backup_path: str) -> Dict[str, Any]:
        """Backup all databases."""
        try:
            db_backup_dir = os.path.join(backup_path, 'databases')
            os.makedirs(db_backup_dir, exist_ok=True)
            
            databases = [
                'data/money_circle.db',
                'data/bus.db'
            ]
            
            backed_up_dbs = []
            
            for db_path in databases:
                if os.path.exists(db_path):
                    db_name = os.path.basename(db_path)
                    backup_db_path = os.path.join(db_backup_dir, db_name)
                    
                    # Create database backup using SQLite backup API
                    await self._backup_sqlite_database(db_path, backup_db_path)
                    
                    # Verify backup
                    if await self._verify_database_backup(backup_db_path):
                        backed_up_dbs.append({
                            'original_path': db_path,
                            'backup_path': backup_db_path,
                            'size_bytes': os.path.getsize(backup_db_path),
                            'status': 'success'
                        })
                    else:
                        backed_up_dbs.append({
                            'original_path': db_path,
                            'backup_path': backup_db_path,
                            'status': 'verification_failed'
                        })
            
            return {
                'status': 'completed',
                'databases': backed_up_dbs,
                'total_databases': len(backed_up_dbs)
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Database backup failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _backup_sqlite_database(self, source_path: str, backup_path: str):
        """Backup SQLite database using proper backup API."""
        try:
            # Use SQLite backup API for consistent backup
            source_conn = sqlite3.connect(source_path)
            backup_conn = sqlite3.connect(backup_path)
            
            # Perform backup
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            logger.info(f"[BACKUP] Database backed up: {source_path} -> {backup_path}")
            
        except Exception as e:
            logger.error(f"[BACKUP] SQLite backup failed for {source_path}: {e}")
            raise

    async def _verify_database_backup(self, backup_path: str) -> bool:
        """Verify database backup integrity."""
        try:
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            
            # Check database integrity
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            conn.close()
            
            return result[0] == 'ok'
            
        except Exception as e:
            logger.error(f"[BACKUP] Database verification failed for {backup_path}: {e}")
            return False

    async def _backup_configuration(self, backup_path: str) -> Dict[str, Any]:
        """Backup configuration files."""
        try:
            config_backup_dir = os.path.join(backup_path, 'configuration')
            os.makedirs(config_backup_dir, exist_ok=True)
            
            config_files = [
                'app.py',
                'requirements.txt',
                'config.json'
            ]
            
            backed_up_configs = []
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    backup_file_path = os.path.join(config_backup_dir, config_file)
                    os.makedirs(os.path.dirname(backup_file_path), exist_ok=True)
                    shutil.copy2(config_file, backup_file_path)
                    
                    backed_up_configs.append({
                        'file': config_file,
                        'backup_path': backup_file_path,
                        'size_bytes': os.path.getsize(backup_file_path)
                    })
            
            return {
                'status': 'completed',
                'files': backed_up_configs,
                'total_files': len(backed_up_configs)
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Configuration backup failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _backup_static_files(self, backup_path: str) -> Dict[str, Any]:
        """Backup static files and templates."""
        try:
            static_backup_dir = os.path.join(backup_path, 'static_files')
            os.makedirs(static_backup_dir, exist_ok=True)
            
            static_dirs = ['static', 'templates']
            backed_up_dirs = []
            
            for static_dir in static_dirs:
                if os.path.exists(static_dir):
                    backup_static_path = os.path.join(static_backup_dir, static_dir)
                    shutil.copytree(static_dir, backup_static_path)
                    
                    dir_size = self._calculate_directory_size(backup_static_path)
                    backed_up_dirs.append({
                        'directory': static_dir,
                        'backup_path': backup_static_path,
                        'size_bytes': dir_size
                    })
            
            return {
                'status': 'completed',
                'directories': backed_up_dirs,
                'total_directories': len(backed_up_dirs)
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Static files backup failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _backup_logs(self, backup_path: str) -> Dict[str, Any]:
        """Backup log files."""
        try:
            logs_backup_dir = os.path.join(backup_path, 'logs')
            os.makedirs(logs_backup_dir, exist_ok=True)
            
            log_files = []
            
            # Find log files
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            backed_up_logs = []
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    relative_path = os.path.relpath(log_file)
                    backup_log_path = os.path.join(logs_backup_dir, relative_path)
                    os.makedirs(os.path.dirname(backup_log_path), exist_ok=True)
                    shutil.copy2(log_file, backup_log_path)
                    
                    backed_up_logs.append({
                        'file': log_file,
                        'backup_path': backup_log_path,
                        'size_bytes': os.path.getsize(backup_log_path)
                    })
            
            return {
                'status': 'completed',
                'files': backed_up_logs,
                'total_files': len(backed_up_logs)
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Logs backup failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _backup_user_data(self, backup_path: str) -> Dict[str, Any]:
        """Backup user-specific data and uploads."""
        try:
            user_data_backup_dir = os.path.join(backup_path, 'user_data')
            os.makedirs(user_data_backup_dir, exist_ok=True)
            
            # Backup data directory if it exists
            data_dirs = ['data', 'uploads']
            backed_up_data = []
            
            for data_dir in data_dirs:
                if os.path.exists(data_dir) and os.path.isdir(data_dir):
                    backup_data_path = os.path.join(user_data_backup_dir, data_dir)
                    
                    # Copy directory contents (excluding database files already backed up)
                    for item in os.listdir(data_dir):
                        item_path = os.path.join(data_dir, item)
                        if not item.endswith('.db'):  # Skip database files
                            backup_item_path = os.path.join(backup_data_path, item)
                            os.makedirs(os.path.dirname(backup_item_path), exist_ok=True)
                            
                            if os.path.isfile(item_path):
                                shutil.copy2(item_path, backup_item_path)
                            elif os.path.isdir(item_path):
                                shutil.copytree(item_path, backup_item_path)
                    
                    if os.path.exists(backup_data_path):
                        dir_size = self._calculate_directory_size(backup_data_path)
                        backed_up_data.append({
                            'directory': data_dir,
                            'backup_path': backup_data_path,
                            'size_bytes': dir_size
                        })
            
            return {
                'status': 'completed',
                'directories': backed_up_data,
                'total_directories': len(backed_up_data)
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] User data backup failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    def _calculate_directory_size(self, directory: str) -> int:
        """Calculate total size of directory."""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return total_size

    async def _calculate_backup_checksum(self, backup_path: str) -> str:
        """Calculate checksum for backup verification."""
        try:
            hash_md5 = hashlib.md5()
            
            for root, dirs, files in os.walk(backup_path):
                for file in sorted(files):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'rb') as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            hash_md5.update(chunk)
            
            return hash_md5.hexdigest()
            
        except Exception as e:
            logger.error(f"[BACKUP] Checksum calculation failed: {e}")
            return ""

    async def _compress_backup(self, backup_path: str) -> str:
        """Compress backup directory."""
        try:
            compressed_path = f"{backup_path}.tar.gz"
            
            # Create compressed archive
            shutil.make_archive(backup_path, 'gztar', backup_path)
            
            logger.info(f"[BACKUP] Backup compressed: {compressed_path}")
            return compressed_path
            
        except Exception as e:
            logger.error(f"[BACKUP] Compression failed: {e}")
            return backup_path

    async def _cleanup_old_backups(self):
        """Remove old backups to maintain storage limits."""
        try:
            # Get all backup files
            backup_files = []
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                if os.path.isfile(item_path) or os.path.isdir(item_path):
                    backup_files.append({
                        'path': item_path,
                        'name': item,
                        'mtime': os.path.getmtime(item_path)
                    })
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x['mtime'], reverse=True)
            
            # Remove old backups if we exceed the limit
            if len(backup_files) > self.max_backups:
                for backup_file in backup_files[self.max_backups:]:
                    if os.path.isdir(backup_file['path']):
                        shutil.rmtree(backup_file['path'])
                    else:
                        os.remove(backup_file['path'])
                    
                    logger.info(f"[BACKUP] Removed old backup: {backup_file['name']}")
            
        except Exception as e:
            logger.error(f"[BACKUP] Cleanup failed: {e}")

    async def restore_backup(self, backup_name: str) -> Dict[str, Any]:
        """Restore from backup."""
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            if not os.path.exists(backup_path):
                return {'status': 'failed', 'error': 'Backup not found'}
            
            logger.info(f"[BACKUP] Starting restore from: {backup_name}")
            
            # If compressed, extract first
            if backup_path.endswith('.tar.gz'):
                extract_path = backup_path.replace('.tar.gz', '')
                shutil.unpack_archive(backup_path, extract_path)
                backup_path = extract_path
            
            # Read backup manifest
            manifest_path = os.path.join(backup_path, 'backup_manifest.json')
            if not os.path.exists(manifest_path):
                return {'status': 'failed', 'error': 'Invalid backup - no manifest found'}
            
            with open(manifest_path, 'r') as f:
                backup_info = json.load(f)
            
            # Restore components
            restore_results = {}
            
            # Restore databases
            if 'database' in backup_info['components']:
                restore_results['database'] = await self._restore_databases(backup_path)
            
            # Restore configuration
            if 'configuration' in backup_info['components']:
                restore_results['configuration'] = await self._restore_configuration(backup_path)
            
            # Restore static files
            if 'static_files' in backup_info['components']:
                restore_results['static_files'] = await self._restore_static_files(backup_path)
            
            logger.info(f"[BACKUP] Restore completed from: {backup_name}")
            
            return {
                'status': 'completed',
                'backup_info': backup_info,
                'restore_results': restore_results
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Restore failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _restore_databases(self, backup_path: str) -> Dict[str, Any]:
        """Restore databases from backup."""
        try:
            db_backup_dir = os.path.join(backup_path, 'databases')
            
            if not os.path.exists(db_backup_dir):
                return {'status': 'skipped', 'reason': 'No database backup found'}
            
            restored_dbs = []
            
            for db_file in os.listdir(db_backup_dir):
                backup_db_path = os.path.join(db_backup_dir, db_file)
                restore_db_path = os.path.join('data', db_file)
                
                # Create data directory if it doesn't exist
                os.makedirs('data', exist_ok=True)
                
                # Restore database
                shutil.copy2(backup_db_path, restore_db_path)
                
                restored_dbs.append({
                    'database': db_file,
                    'restored_to': restore_db_path,
                    'status': 'success'
                })
            
            return {
                'status': 'completed',
                'databases': restored_dbs
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Database restore failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _restore_configuration(self, backup_path: str) -> Dict[str, Any]:
        """Restore configuration files from backup."""
        try:
            config_backup_dir = os.path.join(backup_path, 'configuration')
            
            if not os.path.exists(config_backup_dir):
                return {'status': 'skipped', 'reason': 'No configuration backup found'}
            
            restored_configs = []
            
            for config_file in os.listdir(config_backup_dir):
                backup_config_path = os.path.join(config_backup_dir, config_file)
                restore_config_path = config_file
                
                # Restore configuration file
                shutil.copy2(backup_config_path, restore_config_path)
                
                restored_configs.append({
                    'file': config_file,
                    'restored_to': restore_config_path,
                    'status': 'success'
                })
            
            return {
                'status': 'completed',
                'files': restored_configs
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Configuration restore failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    async def _restore_static_files(self, backup_path: str) -> Dict[str, Any]:
        """Restore static files from backup."""
        try:
            static_backup_dir = os.path.join(backup_path, 'static_files')
            
            if not os.path.exists(static_backup_dir):
                return {'status': 'skipped', 'reason': 'No static files backup found'}
            
            restored_dirs = []
            
            for static_dir in os.listdir(static_backup_dir):
                backup_static_path = os.path.join(static_backup_dir, static_dir)
                restore_static_path = static_dir
                
                # Remove existing directory if it exists
                if os.path.exists(restore_static_path):
                    shutil.rmtree(restore_static_path)
                
                # Restore static directory
                shutil.copytree(backup_static_path, restore_static_path)
                
                restored_dirs.append({
                    'directory': static_dir,
                    'restored_to': restore_static_path,
                    'status': 'success'
                })
            
            return {
                'status': 'completed',
                'directories': restored_dirs
            }
            
        except Exception as e:
            logger.error(f"[BACKUP] Static files restore failed: {e}")
            return {'status': 'failed', 'error': str(e)}

    def list_backups(self) -> List[Dict[str, Any]]:
        """List all available backups."""
        try:
            backups = []
            
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                if os.path.isfile(item_path) or os.path.isdir(item_path):
                    stat = os.stat(item_path)
                    
                    backups.append({
                        'name': item,
                        'path': item_path,
                        'size_bytes': stat.st_size if os.path.isfile(item_path) else self._calculate_directory_size(item_path),
                        'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'type': 'compressed' if item.endswith('.tar.gz') else 'directory'
                    })
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"[BACKUP] Failed to list backups: {e}")
            return []

    async def start_automated_backups(self):
        """Start automated backup schedule."""
        logger.info(f"[BACKUP] Starting automated backups every {self.backup_interval} seconds")
        
        while True:
            try:
                await asyncio.sleep(self.backup_interval)
                await self.create_full_backup()
            except Exception as e:
                logger.error(f"[BACKUP] Automated backup failed: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
