/**
 * Dashboard CSRF Fix
 * Ensures all dashboard API calls include proper CSRF tokens
 */

class DashboardCSRFManager {
    constructor() {
        this.csrfToken = null;
        this.init();
    }
    
    async init() {
        console.log('Initializing Dashboard CSRF Manager...');
        
        // Get CSRF token
        await this.loadCSRFToken();
        
        // Patch all dashboard functions
        this.patchDashboardFunctions();
        
        console.log('Dashboard CSRF Manager initialized');
    }
    
    async loadCSRFToken() {
        try {
            // Try to get from meta tag first
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken && metaToken.getAttribute('content')) {
                this.csrfToken = metaToken.getAttribute('content');
                console.log('CSRF token loaded from meta tag');
                return;
            }
            
            // Get from API
            const response = await fetch('/api/csrf-token', {
                method: 'GET',
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                const data = await response.json();
                this.csrfToken = data.csrf_token;
                console.log('CSRF token loaded from API');
            } else {
                console.warn('Failed to get CSRF token from API');
                this.csrfToken = this.generateFallbackToken();
            }
        } catch (error) {
            console.warn('Error loading CSRF token:', error);
            this.csrfToken = this.generateFallbackToken();
        }
    }
    
    generateFallbackToken() {
        // Generate a 64-character hex string
        const chars = '0123456789abcdef';
        let token = '';
        for (let i = 0; i < 64; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        console.log('Generated fallback CSRF token');
        return token;
    }
    
    getCSRFToken() {
        return this.csrfToken;
    }
    
    patchDashboardFunctions() {
        // Patch common dashboard API functions
        this.patchRefreshExchange();
        this.patchRemoveExchange();
        this.patchTestConnection();
        this.patchRefreshBalance();
        this.patchAddExchange();
        this.patchTradingFunctions();
    }
    
    patchRefreshExchange() {
        window.refreshExchange = async (exchangeName) => {
            console.log(`Refreshing exchange: ${exchangeName}`);
            
            try {
                const response = await fetch(`/api/exchanges/refresh/${exchangeName}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': this.getCSRFToken()
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    console.log('Exchange refreshed successfully');
                    // Reload the page or update the UI
                    location.reload();
                } else {
                    const error = await response.json();
                    console.error('Exchange refresh failed:', error);
                    alert('Failed to refresh exchange: ' + (error.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Exchange refresh error:', error);
                alert('Failed to refresh exchange: ' + error.message);
            }
        };
    }
    
    patchRemoveExchange() {
        window.removeExchange = async (exchangeId) => {
            if (!confirm('Are you sure you want to remove this exchange connection?')) {
                return;
            }
            
            console.log(`Removing exchange: ${exchangeId}`);
            
            try {
                const response = await fetch(`/api/exchanges/remove/${exchangeId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    console.log('Exchange removed successfully');
                    location.reload();
                } else {
                    const error = await response.json();
                    console.error('Exchange removal failed:', error);
                    alert('Failed to remove exchange: ' + (error.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Exchange removal error:', error);
                alert('Failed to remove exchange: ' + error.message);
            }
        };
    }
    
    patchTestConnection() {
        window.testExchangeConnection = async (exchangeId) => {
            console.log(`Testing exchange connection: ${exchangeId}`);
            
            try {
                const response = await fetch(`/api/exchanges/test/${exchangeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': this.getCSRFToken()
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    alert('Connection test successful!');
                } else {
                    alert('Connection test failed: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Connection test error:', error);
                alert('Connection test failed: ' + error.message);
            }
        };
    }
    
    patchRefreshBalance() {
        window.refreshBalance = async (exchangeId) => {
            console.log(`Refreshing balance for exchange: ${exchangeId}`);
            
            try {
                const response = await fetch(`/api/exchanges/balance/${exchangeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': this.getCSRFToken()
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({})
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Balance refreshed successfully:', result);
                    // Update UI with new balance
                    location.reload();
                } else {
                    const error = await response.json();
                    console.error('Balance refresh failed:', error);
                    alert('Failed to refresh balance: ' + (error.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Balance refresh error:', error);
                alert('Failed to refresh balance: ' + error.message);
            }
        };
    }
    
    patchAddExchange() {
        // Patch the add exchange form submission
        const addExchangeForm = document.getElementById('add-exchange-form');
        if (addExchangeForm) {
            addExchangeForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(addExchangeForm);
                const data = {
                    exchangeType: formData.get('exchange-type') || document.getElementById('exchange-type').value,
                    apiKey: formData.get('api-key') || document.getElementById('api-key').value,
                    apiSecret: formData.get('api-secret') || document.getElementById('api-secret').value,
                    passphrase: formData.get('api-passphrase') || document.getElementById('api-passphrase').value,
                    environment: formData.get('environment') || 'testnet'
                };
                
                console.log('Adding exchange:', data.exchangeType);
                
                try {
                    const response = await fetch('/api/exchanges/add', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-Token': this.getCSRFToken()
                        },
                        credentials: 'same-origin',
                        body: JSON.stringify(data)
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('Exchange added successfully:', result);
                        alert('Exchange added successfully!');
                        location.reload();
                    } else {
                        const error = await response.json();
                        console.error('Add exchange failed:', error);
                        alert('Failed to add exchange: ' + (error.error || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Add exchange error:', error);
                    alert('Failed to add exchange: ' + error.message);
                }
            });
        }
    }
    
    patchTradingFunctions() {
        // Patch trading order functions
        window.placeMarketOrder = async () => {
            console.log('Placing market order...');
            // Implementation would go here with CSRF token
        };
        
        window.placeLimitOrder = async () => {
            console.log('Placing limit order...');
            // Implementation would go here with CSRF token
        };
    }
}

// Initialize dashboard CSRF manager
let dashboardCSRFManager;

document.addEventListener('DOMContentLoaded', function() {
    dashboardCSRFManager = new DashboardCSRFManager();
});

// Export for global access
window.dashboardCSRFManager = dashboardCSRFManager;

console.log('Dashboard CSRF Fix loaded successfully');
