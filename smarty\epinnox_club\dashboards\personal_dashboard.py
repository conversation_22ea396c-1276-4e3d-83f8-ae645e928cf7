#!/usr/bin/env python3
"""
Money Circle Personal Dashboard
Individual user dashboard with portfolio and trading interface.
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
import aiohttp_jinja2
from auth.decorators import require_auth, get_current_user
from exchanges.account_manager import ExchangeAccountManager
from database.models import DatabaseManager
from market_data.bus_integration import get_market_data_bus
from trading.personal_trader import PersonalTrader

logger = logging.getLogger(__name__)

class PersonalDashboard:
    """Personal dashboard for individual users."""

    def __init__(self, db_manager: DatabaseManager, user_manager, exchange_manager: ExchangeAccountManager):
        self.db = db_manager
        self.user_manager = user_manager
        self.exchange_manager = exchange_manager

        # Initialize market data integration
        self.market_data_bus = get_market_data_bus()

        # Initialize personal trader
        self.personal_trader = PersonalTrader(db_manager, exchange_manager)

        # Market data cache
        self.market_data_cache = {}
        self.last_market_update = 0

    async def serve_personal_dashboard(self, request: web.Request) -> web.Response:
        """Serve the personal dashboard page."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get user's exchange accounts with enhanced data
        exchange_objects = self.exchange_manager.get_user_exchanges(user['user_id'])
        exchanges = []
        for exchange in exchange_objects:
            # Test connection status
            try:
                balance = self.exchange_manager.get_user_balance(user['user_id'], exchange.exchange_name)
                connected = balance is not None
                balance_data = balance if connected else None
            except Exception as e:
                logger.warning(f"Failed to get balance for {exchange.exchange_name}: {e}")
                connected = False
                balance_data = None

            exchanges.append({
                'id': exchange.id,
                'exchange_name': exchange.exchange_name,
                'is_active': exchange.is_active,
                'created_at': exchange.created_at,
                'connected': connected,
                'balance': balance_data
            })

        # Get portfolio data
        portfolio_data = await self._get_portfolio_data(user['user_id'])

        # Prepare template context
        context = {
            'user': user,
            'exchanges': exchanges,
            'portfolio': portfolio_data,
            'current_path': request.path  # Add current path for navigation
        }

        # Render using template
        return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)

    async def _get_portfolio_data(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive portfolio data for user."""
        portfolio = {
            'total_value': 0.0,
            'available_balance': 0.0,
            'open_positions': 0,
            'daily_pnl': 0.0,
            'daily_change': 0.0,
            'win_rate': 0.0,
            'avg_trade': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'exchanges': {},
            'positions': [],
            'recent_trades': [],
            'market_data': {},
            'performance': {
                'daily_pnl': 0.0,
                'weekly_pnl': 0.0,
                'monthly_pnl': 0.0,
                'total_trades': 0,
                'win_rate': 0.0
            }
        }

        try:
            # Get user's exchanges
            exchanges = self.exchange_manager.get_user_exchanges(user_id)

            for exchange_account in exchanges:
                exchange_name = exchange_account.exchange_name

                # Get balance
                balance = self.exchange_manager.get_user_balance(user_id, exchange_name)
                if balance:
                    portfolio['exchanges'][exchange_name] = {
                        'balance': balance,
                        'connected': True
                    }

                    # Add to total balance (simplified - would need price conversion)
                    usdt_balance = balance['total'].get('USDT', 0)
                    portfolio['total_value'] += usdt_balance
                    portfolio['available_balance'] += balance['free'].get('USDT', 0)

                # Get positions
                positions = self.exchange_manager.get_user_positions(user_id, exchange_name)
                for position in positions:
                    position['exchange'] = exchange_name
                    portfolio['positions'].append(position)

            # Get recent trades from database and personal trader
            portfolio['recent_trades'] = await self.personal_trader.get_user_trades(user_id, 20)

            # Get positions from personal trader
            db_positions = await self.personal_trader.get_user_positions(user_id)
            portfolio['positions'].extend(db_positions)

            # Get live market data
            portfolio['market_data'] = await self._get_live_market_data()

            # Calculate performance metrics
            performance = self._calculate_performance_metrics(user_id)
            portfolio['performance'] = performance

            # Update top-level portfolio fields
            portfolio['daily_pnl'] = performance['daily_pnl']
            portfolio['win_rate'] = performance['win_rate']
            portfolio['avg_trade'] = performance.get('avg_trade', 0.0)
            portfolio['max_drawdown'] = performance.get('max_drawdown', 0.0)
            portfolio['sharpe_ratio'] = performance.get('sharpe_ratio', 0.0)
            portfolio['open_positions'] = len(portfolio['positions'])

            # Calculate daily change percentage
            if portfolio['total_value'] > 0:
                portfolio['daily_change'] = (performance['daily_pnl'] / portfolio['total_value']) * 100
            else:
                portfolio['daily_change'] = 0.0

        except Exception as e:
            logger.error(f"Error getting portfolio data: {e}")

        return portfolio

    async def _get_live_market_data(self) -> Dict[str, Any]:
        """Get live market data from the SQLite bus or fallback sources."""
        try:
            # Connect to market data bus if not connected
            if not await self.market_data_bus.connect():
                logger.warning("Failed to connect to market data bus, using fallback data")
                return self._get_fallback_market_data()

            # Get latest market data for major symbols
            symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT']
            market_data = {}

            for symbol in symbols:
                # Get latest price data
                latest_data = await self.market_data_bus.get_latest_market_data(symbol)
                if latest_data:
                    market_data[symbol] = {
                        'price': latest_data.get('price', 0),
                        'change_24h': latest_data.get('change_24h', 0),
                        'volume_24h': latest_data.get('volume_24h', 0),
                        'timestamp': latest_data.get('timestamp', 0),
                        'symbol': symbol
                    }

                    # Get order book
                    orderbook = await self.market_data_bus.get_latest_orderbook(symbol)
                    if orderbook:
                        market_data[symbol]['orderbook'] = {
                            'bids': orderbook.get('bids', [])[:10],  # Top 10 bids
                            'asks': orderbook.get('asks', [])[:10]   # Top 10 asks
                        }

                    # Get recent trades
                    recent_trades = await self.market_data_bus.get_recent_trades(symbol, 8)
                    if recent_trades:
                        market_data[symbol]['recent_trades'] = recent_trades

            return market_data if market_data else self._get_fallback_market_data()

        except Exception as e:
            logger.error(f"Error getting live market data: {e}")
            return self._get_fallback_market_data()

    def _get_fallback_market_data(self) -> Dict[str, Any]:
        """Get fallback market data when live data is unavailable."""
        import random
        import time

        symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT']
        base_prices = {'BTC-USDT': 50000, 'ETH-USDT': 3000, 'BNB-USDT': 300}
        market_data = {}

        for symbol in symbols:
            base_price = base_prices[symbol]
            current_price = base_price * (1 + random.uniform(-0.05, 0.05))
            change_24h = random.uniform(-5.0, 5.0)

            # Generate realistic orderbook
            bids = []
            asks = []
            for i in range(10):
                bid_price = current_price * (1 - (i + 1) * 0.001)
                ask_price = current_price * (1 + (i + 1) * 0.001)
                bids.append([round(bid_price, 2), round(random.uniform(0.1, 2.0), 4)])
                asks.append([round(ask_price, 2), round(random.uniform(0.1, 2.0), 4)])

            # Generate recent trades
            recent_trades = []
            for i in range(8):
                trade_price = current_price * (1 + random.uniform(-0.01, 0.01))
                recent_trades.append({
                    'price': round(trade_price, 2),
                    'amount': round(random.uniform(0.01, 1.0), 4),
                    'side': random.choice(['buy', 'sell']),
                    'timestamp': time.time() - (i * 30)
                })

            market_data[symbol] = {
                'price': round(current_price, 2),
                'change_24h': round(change_24h, 2),
                'volume_24h': round(random.uniform(1000000, 10000000), 2),
                'timestamp': time.time(),
                'symbol': symbol,
                'orderbook': {'bids': bids, 'asks': asks},
                'recent_trades': recent_trades
            }

        return market_data

    def _get_recent_trades(self, user_id: int, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trades for user."""
        try:
            cursor = self.db.conn.execute("""
                SELECT exchange_name, symbol, side, size, price, fee,
                       order_type, strategy_name, timestamp
                FROM user_trades
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (user_id, limit))

            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'exchange': row[0],
                    'symbol': row[1],
                    'side': row[2],
                    'size': row[3],
                    'price': row[4],
                    'fee': row[5],
                    'order_type': row[6],
                    'strategy': row[7],
                    'timestamp': row[8]
                })

            return trades

        except Exception as e:
            logger.error(f"Error getting recent trades: {e}")
            return []

    def _calculate_performance_metrics(self, user_id: int) -> Dict[str, float]:
        """Calculate performance metrics for user."""
        try:
            # Ensure database connection is active
            if not self.db.ensure_connection():
                logger.error("Failed to ensure database connection")
                return self._get_default_performance_metrics()
            # Get trades for different time periods
            now = datetime.now()
            day_ago = now - timedelta(days=1)
            week_ago = now - timedelta(days=7)
            month_ago = now - timedelta(days=30)

            # Daily P&L
            cursor = self.db.conn.execute("""
                SELECT SUM((price * size) * CASE WHEN side = 'sell' THEN 1 ELSE -1 END) - SUM(fee)
                FROM user_trades
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, day_ago.isoformat()))
            daily_pnl = cursor.fetchone()[0] or 0.0

            # Weekly P&L
            cursor = self.db.conn.execute("""
                SELECT SUM((price * size) * CASE WHEN side = 'sell' THEN 1 ELSE -1 END) - SUM(fee)
                FROM user_trades
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, week_ago.isoformat()))
            weekly_pnl = cursor.fetchone()[0] or 0.0

            # Monthly P&L
            cursor = self.db.conn.execute("""
                SELECT SUM((price * size) * CASE WHEN side = 'sell' THEN 1 ELSE -1 END) - SUM(fee)
                FROM user_trades
                WHERE user_id = ? AND timestamp >= ?
            """, (user_id, month_ago.isoformat()))
            monthly_pnl = cursor.fetchone()[0] or 0.0

            # Total trades
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM user_trades WHERE user_id = ?
            """, (user_id,))
            total_trades = cursor.fetchone()[0] or 0

            # Win rate (simplified calculation)
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM user_trades
                WHERE user_id = ? AND side = 'sell'
                AND (price * size) > (
                    SELECT AVG(price * size) FROM user_trades
                    WHERE user_id = ? AND side = 'buy'
                )
            """, (user_id, user_id))
            winning_trades = cursor.fetchone()[0] or 0
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0.0

            # Calculate average trade
            avg_trade = (monthly_pnl / total_trades) if total_trades > 0 else 0.0

            # Simplified max drawdown calculation
            max_drawdown = abs(min(daily_pnl, weekly_pnl, monthly_pnl, 0)) / 100 if monthly_pnl != 0 else 0.0

            # Simplified Sharpe ratio calculation
            sharpe_ratio = (monthly_pnl / 30) / max(abs(daily_pnl), 1) if daily_pnl != 0 else 0.0

            return {
                'daily_pnl': daily_pnl,
                'weekly_pnl': weekly_pnl,
                'monthly_pnl': monthly_pnl,
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_trade': avg_trade,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio
            }

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._get_default_performance_metrics()

    def _get_default_performance_metrics(self) -> Dict[str, float]:
        """Get default performance metrics when database is unavailable."""
        return {
            'daily_pnl': 0.0,
            'weekly_pnl': 0.0,
            'monthly_pnl': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'avg_trade': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }

    # Note: HTML rendering now handled by templates/personal_dashboard.html

    def _render_exchange_cards(self, exchanges: List, portfolio_exchanges: Dict) -> str:
        """Render exchange account cards."""
        if not exchanges:
            return "<p>No exchange accounts connected. Add one to start trading!</p>"

        cards_html = ""
        for exchange in exchanges:
            exchange_name = exchange.exchange_name
            portfolio_data = portfolio_exchanges.get(exchange_name, {})
            connected = portfolio_data.get('connected', False)
            balance = portfolio_data.get('balance', {})

            cards_html += f"""
            <div class="exchange-card {'connected' if connected else 'disconnected'}">
                <h4>{exchange_name}</h4>
                <div class="connection-status">
                    {'🟢 Connected' if connected else '🔴 Disconnected'}
                </div>
                {self._render_balance_summary(balance) if connected else ''}
                <div class="exchange-actions">
                    <button onclick="refreshExchange('{exchange_name}')">🔄 Refresh</button>
                    <button onclick="removeExchange({exchange.id})">🗑️ Remove</button>
                </div>
            </div>
            """

        return cards_html

    def _render_balance_summary(self, balance: Dict) -> str:
        """Render balance summary."""
        if not balance or 'total' not in balance:
            return ""

        total_balance = balance['total']
        major_assets = ['USDT', 'BTC', 'ETH', 'BNB']

        balance_html = "<div class='balance-summary'>"
        for asset in major_assets:
            if asset in total_balance and total_balance[asset] > 0:
                balance_html += f"<div class='balance-item'>{asset}: {total_balance[asset]:.4f}</div>"
        balance_html += "</div>"

        return balance_html

    def _render_positions_table(self, positions: List) -> str:
        """Render positions table."""
        if not positions:
            return "<p>No open positions</p>"

        table_html = """
        <table>
            <thead>
                <tr>
                    <th>Exchange</th>
                    <th>Symbol</th>
                    <th>Side</th>
                    <th>Size</th>
                    <th>Entry Price</th>
                    <th>Current Price</th>
                    <th>P&L</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
        """

        for position in positions:
            pnl_class = 'positive' if position.get('unrealizedPnl', 0) >= 0 else 'negative'
            table_html += f"""
            <tr>
                <td>{position.get('exchange', 'N/A')}</td>
                <td>{position.get('symbol', 'N/A')}</td>
                <td>{position.get('side', 'N/A')}</td>
                <td>{position.get('size', 0):.4f}</td>
                <td>${position.get('entryPrice', 0):.4f}</td>
                <td>${position.get('markPrice', 0):.4f}</td>
                <td class="{pnl_class}">${position.get('unrealizedPnl', 0):.2f}</td>
                <td>
                    <button onclick="closePosition('{position.get('symbol')}')">Close</button>
                </td>
            </tr>
            """

        table_html += "</tbody></table>"
        return table_html

    def _render_trades_table(self, trades: List) -> str:
        """Render recent trades table."""
        if not trades:
            return "<p>No recent trades</p>"

        table_html = """
        <table>
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Exchange</th>
                    <th>Symbol</th>
                    <th>Side</th>
                    <th>Size</th>
                    <th>Price</th>
                    <th>Strategy</th>
                </tr>
            </thead>
            <tbody>
        """

        for trade in trades[:10]:  # Show last 10 trades
            table_html += f"""
            <tr>
                <td>{trade.get('timestamp', 'N/A')}</td>
                <td>{trade.get('exchange', 'N/A')}</td>
                <td>{trade.get('symbol', 'N/A')}</td>
                <td class="{trade.get('side', '').lower()}">{trade.get('side', 'N/A')}</td>
                <td>{trade.get('size', 0):.4f}</td>
                <td>${trade.get('price', 0):.4f}</td>
                <td>{trade.get('strategy', 'Manual')}</td>
            </tr>
            """

        table_html += "</tbody></table>"
        return table_html
