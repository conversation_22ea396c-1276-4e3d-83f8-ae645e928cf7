#!/usr/bin/env python3
"""
Advanced Analytics Engine for Smart-Trader System

This module provides comprehensive analytics capabilities including:
- Real-time performance tracking
- Risk analytics
- Model performance comparison
- Portfolio analytics
- Market regime detection
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import sqlite3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    total_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    current_streak: int
    max_winning_streak: int
    max_losing_streak: int


@dataclass
class RiskMetrics:
    """Risk metrics data structure."""
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    expected_shortfall: float
    volatility: float
    beta: float
    correlation_btc: float
    max_position_size: float
    current_exposure: float
    risk_score: float


@dataclass
class ModelPerformance:
    """Model performance tracking."""
    model_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    signal_count: int
    profitable_signals: int
    avg_return_per_signal: float
    last_signal_time: datetime
    confidence_score: float
    contribution_to_pnl: float


class AdvancedAnalytics:
    """
    Advanced analytics engine for comprehensive trading system analysis.

    Features:
    - Real-time performance tracking
    - Risk analytics and monitoring
    - Model performance comparison
    - Portfolio analytics
    - Market regime detection
    - Sentiment analysis integration
    """

    def __init__(self, db_path: str = "analytics.db"):
        """Initialize the analytics engine."""
        self.db_path = db_path
        self.performance_history = []
        self.risk_history = []
        self.model_performances = {}
        self.market_regime = "UNKNOWN"
        self.sentiment_score = 0.0

        # Initialize database
        self._init_database()

        # Performance tracking
        self.trades = []
        self.positions = {}
        self.portfolio_value = 100.0  # Starting value
        self.benchmark_returns = []

        # Risk management
        self.max_drawdown_threshold = 0.15  # 15%
        self.var_threshold = 0.05  # 5%
        self.position_size_limit = 0.1  # 10% per position

        logger.info("🔬 Advanced Analytics Engine initialized")

    def _init_database(self) -> None:
        """Initialize SQLite database for analytics storage."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    timestamp TEXT PRIMARY KEY,
                    total_return REAL,
                    sharpe_ratio REAL,
                    sortino_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    profit_factor REAL,
                    total_trades INTEGER,
                    portfolio_value REAL
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_metrics (
                    timestamp TEXT PRIMARY KEY,
                    var_95 REAL,
                    var_99 REAL,
                    volatility REAL,
                    max_drawdown REAL,
                    current_exposure REAL,
                    risk_score REAL
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_performance (
                    timestamp TEXT,
                    model_name TEXT,
                    accuracy REAL,
                    precision REAL,
                    recall REAL,
                    signal_count INTEGER,
                    profitable_signals INTEGER,
                    avg_return REAL,
                    confidence_score REAL,
                    PRIMARY KEY (timestamp, model_name)
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    symbol TEXT,
                    side TEXT,
                    quantity REAL,
                    price REAL,
                    pnl REAL,
                    strategy TEXT,
                    model_source TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_regimes (
                    timestamp TEXT PRIMARY KEY,
                    regime TEXT,
                    confidence REAL,
                    volatility REAL,
                    trend_strength REAL,
                    sentiment_score REAL
                )
            ''')

            conn.commit()
            conn.close()

            logger.info("📊 Analytics database initialized")

        except Exception as e:
            logger.error(f"Failed to initialize analytics database: {e}")

    async def update_performance_metrics(self, trades_data: List[Dict]) -> PerformanceMetrics:
        """Update and calculate performance metrics."""
        try:
            if not trades_data:
                return PerformanceMetrics(
                    total_return=0.0, sharpe_ratio=0.0, sortino_ratio=0.0,
                    max_drawdown=0.0, win_rate=0.0, profit_factor=0.0,
                    avg_win=0.0, avg_loss=0.0, total_trades=0,
                    winning_trades=0, losing_trades=0, current_streak=0,
                    max_winning_streak=0, max_losing_streak=0
                )

            # Convert to DataFrame for easier analysis
            df = pd.DataFrame(trades_data)

            # Calculate returns
            returns = df['pnl'].values
            cumulative_returns = np.cumsum(returns)

            # Performance metrics
            total_return = cumulative_returns[-1] if len(cumulative_returns) > 0 else 0.0

            # Risk-adjusted metrics
            if len(returns) > 1:
                sharpe_ratio = self._calculate_sharpe_ratio(returns)
                sortino_ratio = self._calculate_sortino_ratio(returns)
                max_drawdown = self._calculate_max_drawdown(cumulative_returns)
            else:
                sharpe_ratio = sortino_ratio = max_drawdown = 0.0

            # Win/Loss metrics
            winning_trades = len([r for r in returns if r > 0])
            losing_trades = len([r for r in returns if r < 0])
            total_trades = len(returns)

            win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

            avg_win = np.mean([r for r in returns if r > 0]) if winning_trades > 0 else 0.0
            avg_loss = np.mean([r for r in returns if r < 0]) if losing_trades > 0 else 0.0

            profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0.0

            # Streak analysis
            current_streak, max_winning_streak, max_losing_streak = self._calculate_streaks(returns)

            metrics = PerformanceMetrics(
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                avg_win=avg_win,
                avg_loss=avg_loss,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                current_streak=current_streak,
                max_winning_streak=max_winning_streak,
                max_losing_streak=max_losing_streak
            )

            # Store in database
            await self._store_performance_metrics(metrics)

            logger.info(f"📈 Performance metrics updated: Return={total_return:.2f}, Sharpe={sharpe_ratio:.2f}")
            return metrics

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

    async def update_risk_metrics(self, portfolio_data: Dict, market_data: Dict) -> RiskMetrics:
        """Update and calculate risk metrics."""
        try:
            # Calculate VaR
            returns = self._get_recent_returns()
            var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0.0
            var_99 = np.percentile(returns, 1) if len(returns) > 0 else 0.0

            # Expected Shortfall (Conditional VaR)
            expected_shortfall = np.mean([r for r in returns if r <= var_95]) if len(returns) > 0 else 0.0

            # Volatility
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0.0  # Annualized

            # Beta (vs BTC if available)
            beta = self._calculate_beta(returns)

            # Correlation with BTC
            correlation_btc = self._calculate_correlation_btc(returns)

            # Position sizing
            current_exposure = sum(abs(pos.get('value', 0)) for pos in portfolio_data.get('positions', {}).values())
            max_position_size = max([abs(pos.get('value', 0)) for pos in portfolio_data.get('positions', {}).values()], default=0.0)

            # Risk score (0-100, higher = riskier)
            risk_score = self._calculate_risk_score(volatility, max_drawdown=abs(var_95), exposure=current_exposure)

            metrics = RiskMetrics(
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                volatility=volatility,
                beta=beta,
                correlation_btc=correlation_btc,
                max_position_size=max_position_size,
                current_exposure=current_exposure,
                risk_score=risk_score
            )

            # Store in database
            await self._store_risk_metrics(metrics)

            logger.info(f"⚠️ Risk metrics updated: VaR95={var_95:.3f}, Vol={volatility:.3f}, Risk Score={risk_score:.1f}")
            return metrics

        except Exception as e:
            logger.error(f"Error updating risk metrics: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)

    async def update_model_performance(self, model_name: str, signal_data: Dict, outcome: Optional[float] = None) -> ModelPerformance:
        """Update model performance metrics."""
        try:
            # Get or create model performance record
            if model_name not in self.model_performances:
                self.model_performances[model_name] = {
                    'signals': [],
                    'outcomes': [],
                    'accuracy_history': [],
                    'last_update': datetime.now()
                }

            model_data = self.model_performances[model_name]

            # Add new signal
            model_data['signals'].append(signal_data)
            if outcome is not None:
                model_data['outcomes'].append(outcome)

            # Calculate metrics
            signals = model_data['signals']
            outcomes = model_data['outcomes']

            signal_count = len(signals)
            profitable_signals = len([o for o in outcomes if o > 0]) if outcomes else 0

            accuracy = profitable_signals / len(outcomes) if outcomes else 0.0
            precision = accuracy  # Simplified for now
            recall = accuracy     # Simplified for now
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            avg_return_per_signal = np.mean(outcomes) if outcomes else 0.0
            confidence_score = signal_data.get('confidence', 0.0)
            contribution_to_pnl = sum(outcomes) if outcomes else 0.0

            performance = ModelPerformance(
                model_name=model_name,
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1_score,
                signal_count=signal_count,
                profitable_signals=profitable_signals,
                avg_return_per_signal=avg_return_per_signal,
                last_signal_time=datetime.now(),
                confidence_score=confidence_score,
                contribution_to_pnl=contribution_to_pnl
            )

            # Store in database
            await self._store_model_performance(performance)

            logger.info(f"🤖 Model {model_name} performance updated: Accuracy={accuracy:.2f}, Signals={signal_count}")
            return performance

        except Exception as e:
            logger.error(f"Error updating model performance for {model_name}: {e}")
            return ModelPerformance(model_name, 0, 0, 0, 0, 0, 0, 0, datetime.now(), 0, 0)

    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio."""
        if len(returns) < 2:
            return 0.0

        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0.0

    def _calculate_sortino_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio."""
        if len(returns) < 2:
            return 0.0

        excess_returns = returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]

        if len(downside_returns) == 0:
            return float('inf')

        downside_deviation = np.std(downside_returns)
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0.0

    def _calculate_max_drawdown(self, cumulative_returns: np.ndarray) -> float:
        """Calculate maximum drawdown."""
        if len(cumulative_returns) == 0:
            return 0.0

        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        return np.min(drawdown)

    def _calculate_streaks(self, returns: np.ndarray) -> Tuple[int, int, int]:
        """Calculate current streak and max winning/losing streaks."""
        if len(returns) == 0:
            return 0, 0, 0

        # Current streak
        current_streak = 0
        for i in range(len(returns) - 1, -1, -1):
            if returns[i] > 0:
                if current_streak >= 0:
                    current_streak += 1
                else:
                    break
            elif returns[i] < 0:
                if current_streak <= 0:
                    current_streak -= 1
                else:
                    break
            else:
                break

        # Max streaks
        max_winning_streak = 0
        max_losing_streak = 0
        current_win_streak = 0
        current_loss_streak = 0

        for ret in returns:
            if ret > 0:
                current_win_streak += 1
                current_loss_streak = 0
                max_winning_streak = max(max_winning_streak, current_win_streak)
            elif ret < 0:
                current_loss_streak += 1
                current_win_streak = 0
                max_losing_streak = max(max_losing_streak, current_loss_streak)
            else:
                current_win_streak = 0
                current_loss_streak = 0

        return current_streak, max_winning_streak, max_losing_streak

    def _get_recent_returns(self, days: int = 30) -> np.ndarray:
        """Get recent returns for risk calculations."""
        # This would typically fetch from database
        # For now, return dummy data
        return np.random.normal(0.001, 0.02, days)

    def _calculate_beta(self, returns: np.ndarray) -> float:
        """Calculate beta vs market (simplified)."""
        # Simplified beta calculation
        market_returns = np.random.normal(0.001, 0.015, len(returns))  # Dummy market data

        if len(returns) < 2 or len(market_returns) < 2:
            return 1.0

        covariance = np.cov(returns, market_returns)[0, 1]
        market_variance = np.var(market_returns)

        return covariance / market_variance if market_variance > 0 else 1.0

    def _calculate_correlation_btc(self, returns: np.ndarray) -> float:
        """Calculate correlation with BTC."""
        # Simplified correlation calculation
        btc_returns = np.random.normal(0.002, 0.03, len(returns))  # Dummy BTC data

        if len(returns) < 2:
            return 0.0

        return np.corrcoef(returns, btc_returns)[0, 1] if len(returns) == len(btc_returns) else 0.0

    def _calculate_risk_score(self, volatility: float, max_drawdown: float, exposure: float) -> float:
        """Calculate overall risk score (0-100)."""
        # Normalize components
        vol_score = min(volatility * 100, 50)  # Cap at 50
        dd_score = min(abs(max_drawdown) * 100, 30)  # Cap at 30
        exp_score = min(exposure / 1000 * 20, 20)  # Cap at 20

        return vol_score + dd_score + exp_score

    async def _store_performance_metrics(self, metrics: PerformanceMetrics) -> None:
        """Store performance metrics in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO performance_metrics
                (timestamp, total_return, sharpe_ratio, sortino_ratio, max_drawdown,
                 win_rate, profit_factor, total_trades, portfolio_value)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                metrics.total_return,
                metrics.sharpe_ratio,
                metrics.sortino_ratio,
                metrics.max_drawdown,
                metrics.win_rate,
                metrics.profit_factor,
                metrics.total_trades,
                self.portfolio_value
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing performance metrics: {e}")

    async def _store_risk_metrics(self, metrics: RiskMetrics) -> None:
        """Store risk metrics in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO risk_metrics
                (timestamp, var_95, var_99, volatility, max_drawdown, current_exposure, risk_score)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                metrics.var_95,
                metrics.var_99,
                metrics.volatility,
                metrics.max_drawdown,
                metrics.current_exposure,
                metrics.risk_score
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing risk metrics: {e}")

    async def _store_model_performance(self, performance: ModelPerformance) -> None:
        """Store model performance in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO model_performance
                (timestamp, model_name, accuracy, precision, recall, signal_count,
                 profitable_signals, avg_return, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                performance.model_name,
                performance.accuracy,
                performance.precision,
                performance.recall,
                performance.signal_count,
                performance.profitable_signals,
                performance.avg_return_per_signal,
                performance.confidence_score
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing model performance: {e}")

    async def get_analytics_summary(self) -> Dict[str, Any]:
        """Get comprehensive analytics summary."""
        try:
            # Get recent performance data
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Performance metrics
            cursor.execute('''
                SELECT * FROM performance_metrics
                ORDER BY timestamp DESC LIMIT 1
            ''')
            perf_row = cursor.fetchone()

            # Risk metrics
            cursor.execute('''
                SELECT * FROM risk_metrics
                ORDER BY timestamp DESC LIMIT 1
            ''')
            risk_row = cursor.fetchone()

            # Model performance
            cursor.execute('''
                SELECT model_name, accuracy, signal_count, avg_return, confidence_score
                FROM model_performance
                WHERE timestamp >= datetime('now', '-1 day')
                ORDER BY accuracy DESC
            ''')
            model_rows = cursor.fetchall()

            conn.close()

            # Format response
            summary = {
                "timestamp": datetime.now().isoformat(),
                "performance": {
                    "total_return": perf_row[1] if perf_row else 0.0,
                    "sharpe_ratio": perf_row[2] if perf_row else 0.0,
                    "max_drawdown": perf_row[4] if perf_row else 0.0,
                    "win_rate": perf_row[5] if perf_row else 0.0,
                    "total_trades": perf_row[7] if perf_row else 0
                },
                "risk": {
                    "var_95": risk_row[1] if risk_row else 0.0,
                    "volatility": risk_row[3] if risk_row else 0.0,
                    "risk_score": risk_row[6] if risk_row else 0.0,
                    "current_exposure": risk_row[5] if risk_row else 0.0
                },
                "models": [
                    {
                        "name": row[0],
                        "accuracy": row[1],
                        "signal_count": row[2],
                        "avg_return": row[3],
                        "confidence": row[4]
                    }
                    for row in model_rows
                ],
                "market_regime": self.market_regime,
                "sentiment_score": self.sentiment_score
            }

            return summary

        except Exception as e:
            logger.error(f"Error getting analytics summary: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "performance": {},
                "risk": {},
                "models": [],
                "market_regime": "UNKNOWN",
                "sentiment_score": 0.0
            }

    async def detect_market_regime(self, market_data: Dict) -> str:
        """Detect current market regime using AI/ML."""
        try:
            # Get recent price data
            prices = market_data.get('prices', [])
            volumes = market_data.get('volumes', [])

            if len(prices) < 20:
                return "INSUFFICIENT_DATA"

            # Calculate features for regime detection
            returns = np.diff(np.log(prices))
            volatility = np.std(returns) * np.sqrt(252)

            # Trend strength
            sma_20 = np.mean(prices[-20:])
            sma_50 = np.mean(prices[-50:]) if len(prices) >= 50 else sma_20
            trend_strength = (prices[-1] - sma_50) / sma_50 if sma_50 > 0 else 0

            # Volume analysis
            avg_volume = np.mean(volumes[-20:]) if volumes else 0
            volume_ratio = volumes[-1] / avg_volume if avg_volume > 0 and volumes else 1

            # Simple regime classification
            if volatility > 0.4:  # High volatility
                regime = "HIGH_VOLATILITY"
            elif abs(trend_strength) > 0.1:  # Strong trend
                if trend_strength > 0:
                    regime = "BULL_TREND"
                else:
                    regime = "BEAR_TREND"
            elif volatility < 0.15:  # Low volatility
                regime = "LOW_VOLATILITY"
            else:
                regime = "SIDEWAYS"

            # Store regime
            self.market_regime = regime

            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO market_regimes
                (timestamp, regime, confidence, volatility, trend_strength, sentiment_score)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                regime,
                0.8,  # Simplified confidence
                volatility,
                trend_strength,
                self.sentiment_score
            ))

            conn.commit()
            conn.close()

            logger.info(f"🔍 Market regime detected: {regime} (Vol: {volatility:.3f}, Trend: {trend_strength:.3f})")
            return regime

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return "ERROR"

    async def optimize_model_weights(self, model_performances: Dict[str, ModelPerformance]) -> Dict[str, float]:
        """Optimize ensemble model weights based on performance."""
        try:
            if not model_performances:
                return {}

            # Calculate weights based on performance metrics
            weights = {}
            total_score = 0

            for model_name, perf in model_performances.items():
                # Composite score: accuracy + sharpe-like ratio
                accuracy_score = perf.accuracy
                return_score = perf.avg_return_per_signal / (abs(perf.avg_return_per_signal) + 0.01)  # Normalized
                confidence_score = perf.confidence_score

                # Combine scores
                composite_score = (accuracy_score * 0.4 +
                                 return_score * 0.4 +
                                 confidence_score * 0.2)

                weights[model_name] = max(composite_score, 0.01)  # Minimum weight
                total_score += weights[model_name]

            # Normalize weights
            if total_score > 0:
                weights = {name: weight / total_score for name, weight in weights.items()}

            logger.info(f"🎯 Model weights optimized: {weights}")
            return weights

        except Exception as e:
            logger.error(f"Error optimizing model weights: {e}")
            return {}

    async def calculate_optimal_position_size(self, signal_strength: float, risk_metrics: RiskMetrics,
                                            account_balance: float) -> float:
        """Calculate optimal position size using Kelly Criterion and risk management."""
        try:
            # Kelly Criterion: f = (bp - q) / b
            # where f = fraction to bet, b = odds, p = win probability, q = loss probability

            # Estimate win probability from signal strength
            win_prob = 0.5 + (signal_strength * 0.3)  # 0.2 to 0.8 range
            loss_prob = 1 - win_prob

            # Estimate odds from historical data (simplified)
            avg_win = 0.02  # 2% average win
            avg_loss = 0.015  # 1.5% average loss

            # Kelly fraction
            kelly_fraction = (avg_win * win_prob - avg_loss * loss_prob) / avg_win
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%

            # Risk-adjusted position size
            risk_adjustment = 1.0 - (risk_metrics.risk_score / 100)  # Reduce size if high risk
            volatility_adjustment = 1.0 / (1.0 + risk_metrics.volatility)  # Reduce size if high vol

            # Final position size
            position_fraction = kelly_fraction * risk_adjustment * volatility_adjustment
            position_size = account_balance * position_fraction

            # Apply maximum position size limit
            max_position = account_balance * self.position_size_limit
            position_size = min(position_size, max_position)

            logger.info(f"💰 Optimal position size calculated: ${position_size:.2f} ({position_fraction:.1%} of balance)")
            return position_size

        except Exception as e:
            logger.error(f"Error calculating optimal position size: {e}")
            return account_balance * 0.01  # Default to 1%


# Global analytics instance
analytics_engine = AdvancedAnalytics()
