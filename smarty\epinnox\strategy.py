"""
Epinnox Smart Strategy - Enhanced Smart Trading Strategy with CCXT Integration

This module contains the main smart trading strategy, enhanced for multi-exchange
support using CCXT. Based on the smart-trader system's smart_model_integrated_strategy.
"""

import logging
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from .core.events import Signal, Side
from .core.feature_store import feature_store
from .core.utils import timer, calculate_rsi, calculate_bollinger_bands
from .models.rsi import RSIModel
from .models.vwap_deviation import VWAPDeviationModel
from .models.funding_momentum import FundingMomentumModel
from .models.open_interest_momentum import OpenInterestMomentumModel

logger = logging.getLogger(__name__)


class EpinnoxSmartStrategy:
    """
    Epinnox Smart Strategy - Enhanced multi-model trading strategy.

    This strategy integrates:
    - Technical indicators (SMA, RSI, Bollinger Bands)
    - VWAP deviation analysis
    - Funding rate momentum (for futures)
    - Open interest analysis (for futures)
    - RSI model predictions

    Enhanced for CCXT multi-exchange support.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Epinnox Smart Strategy.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}

        # Component weights (can be optimized)
        self.weights = {
            "technical": 0.3,
            "vwap": 0.2,
            "rsi_model": 0.15,
            "funding": 0.1,
            "open_interest": 0.1,
            "volatility": 0.1,
            "ensemble": 0.05
        }

        # Initialize models
        self.rsi_model = RSIModel(
            period=self.config.get("rsi_period", 14),
            overbought_threshold=self.config.get("rsi_overbought", 70),
            oversold_threshold=self.config.get("rsi_oversold", 30)
        )

        self.vwap_model = VWAPDeviationModel(
            config=self.config.get("vwap_config", {})
        )

        self.funding_model = FundingMomentumModel(
            config=self.config.get("funding_config", {})
        )

        self.oi_model = OpenInterestMomentumModel(
            config=self.config.get("oi_config", {})
        )

        # Strategy parameters
        self.min_data_points = self.config.get("min_data_points", 30)
        self.base_buy_threshold = self.config.get("base_buy_threshold", 0.3)
        self.base_sell_threshold = self.config.get("base_sell_threshold", -0.3)

    @timer
    async def generate_signals(
        self,
        timestamp: datetime,
        symbols: List[str],
        exchange: str = "default",
        signal_source: str = "epinnox_smart"
    ) -> List[Signal]:
        """
        Generate trading signals for the given symbols.

        Args:
            timestamp: Current timestamp
            symbols: List of symbols to generate signals for
            exchange: Exchange identifier
            signal_source: Source identifier for the signal

        Returns:
            List of signals
        """
        signals = []

        for symbol in symbols:
            try:
                signal = await self._generate_signal_for_symbol(
                    symbol, timestamp, exchange, signal_source
                )
                if signal:
                    signals.append(signal)
            except Exception as e:
                logger.error(f"Error generating signal for {symbol} on {exchange}: {e}")

        return signals

    async def _generate_signal_for_symbol(
        self,
        symbol: str,
        timestamp: datetime,
        exchange: str,
        signal_source: str
    ) -> Optional[Signal]:
        """
        Generate a signal for a single symbol.

        Args:
            symbol: Trading symbol
            timestamp: Current timestamp
            exchange: Exchange identifier
            signal_source: Source identifier

        Returns:
            Signal object or None
        """
        # Get current price
        current_price = await feature_store.get(symbol, "close", exchange=exchange)
        if not current_price:
            logger.warning(f"No current price available for {symbol} on {exchange}")
            return None

        # Initialize signal components
        signal_components = {}
        total_weight = 0.0

        # 1. Technical Analysis Component
        tech_signal = await self._get_technical_signal(symbol, timestamp, exchange)
        if tech_signal is not None:
            signal_components["technical"] = {
                "signal": tech_signal,
                "weight": self.weights["technical"],
                "confidence": abs(tech_signal)
            }
            total_weight += self.weights["technical"]

        # 2. VWAP Deviation Component
        vwap_signal = await self._get_vwap_signal(symbol, exchange)
        if vwap_signal is not None:
            signal_components["vwap"] = {
                "signal": vwap_signal,
                "weight": self.weights["vwap"],
                "confidence": abs(vwap_signal)
            }
            total_weight += self.weights["vwap"]

        # 3. RSI Model Component
        rsi_signal = await self._get_rsi_model_signal(symbol, exchange)
        if rsi_signal is not None:
            signal_components["rsi_model"] = {
                "signal": rsi_signal,
                "weight": self.weights["rsi_model"],
                "confidence": abs(rsi_signal)
            }
            total_weight += self.weights["rsi_model"]

        # 4. Funding Momentum Component (for futures)
        funding_signal = await self._get_funding_signal(symbol, exchange)
        if funding_signal is not None:
            signal_components["funding"] = {
                "signal": funding_signal,
                "weight": self.weights["funding"],
                "confidence": abs(funding_signal)
            }
            total_weight += self.weights["funding"]

        # 5. Open Interest Component (for futures)
        oi_signal = await self._get_open_interest_signal(symbol, exchange)
        if oi_signal is not None:
            signal_components["open_interest"] = {
                "signal": oi_signal,
                "weight": self.weights["open_interest"],
                "confidence": abs(oi_signal)
            }
            total_weight += self.weights["open_interest"]

        # Calculate weighted ensemble score
        if total_weight > 0:
            weighted_sum = sum(
                comp["signal"] * comp["weight"]
                for comp in signal_components.values()
            )
            ensemble_score = weighted_sum / total_weight

            # Apply confidence weighting
            confidence_weighted_score = ensemble_score * self._calculate_confidence_multiplier(signal_components)

            # Determine action based on ensemble score with dynamic thresholds
            buy_threshold, sell_threshold = self._get_dynamic_thresholds(signal_components)

            if confidence_weighted_score > buy_threshold:
                action = Side.BUY
                confidence = min(0.95, 0.5 + abs(confidence_weighted_score) * 0.4)
            elif confidence_weighted_score < sell_threshold:
                action = Side.SELL
                confidence = min(0.95, 0.5 + abs(confidence_weighted_score) * 0.4)
            else:
                return None  # No clear signal

            # Create detailed rationale
            active_components = [
                f"{name}({comp['signal']:.2f})"
                for name, comp in signal_components.items()
                if abs(comp['signal']) > 0.1
            ]
            rationale = f"Epinnox-Smart {action.value}: {', '.join(active_components)} → {confidence_weighted_score:.2f}"

            signal = Signal(
                symbol=symbol,
                timestamp=timestamp,
                action=action,
                score=confidence,
                source=signal_source,
                rationale=rationale,
                metadata={
                    "price": current_price,
                    "ensemble_score": ensemble_score,
                    "confidence_weighted_score": confidence_weighted_score,
                    "components": {name: comp["signal"] for name, comp in signal_components.items()},
                    "total_weight": total_weight,
                    "buy_threshold": buy_threshold,
                    "sell_threshold": sell_threshold,
                    "exchange": exchange
                }
            )

            logger.info(f"Generated {action.value} signal for {symbol} on {exchange} (score: {confidence:.2f})")
            return signal

        return None

    async def _get_technical_signal(self, symbol: str, timestamp: datetime, exchange: str) -> Optional[float]:
        """Get technical analysis signal combining SMA, RSI, and Bollinger Bands."""
        try:
            # Get price data
            prices_data = await feature_store.get_time_series(symbol, "close_prices", 50, exchange=exchange)
            if not prices_data or len(prices_data) < 30:
                return None

            # Extract price values
            prices = []
            for price_data in prices_data:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    prices.append(float(price_data))

            prices_array = np.array(prices)
            current_price = prices_array[-1]

            # SMA signals
            fast_ma = np.mean(prices_array[-10:])
            slow_ma = np.mean(prices_array[-30:])
            sma_signal = 1.0 if fast_ma > slow_ma else -1.0

            # RSI signal
            rsi_values = calculate_rsi(prices_array, 14)
            rsi = rsi_values[-1]

            if rsi < 30:
                rsi_signal = 1.0  # Oversold -> Buy
            elif rsi > 70:
                rsi_signal = -1.0  # Overbought -> Sell
            else:
                rsi_signal = 0.0

            # Bollinger Bands signal
            upper_band, middle_band, lower_band = calculate_bollinger_bands(prices_array, 20, 2.0)

            if current_price <= lower_band:
                bb_signal = 1.0  # Below lower band -> Buy
            elif current_price >= upper_band:
                bb_signal = -1.0  # Above upper band -> Sell
            else:
                bb_signal = 0.0

            # Combine technical signals
            tech_signal = (sma_signal * 0.5) + (rsi_signal * 0.3) + (bb_signal * 0.2)
            return tech_signal

        except Exception as e:
            logger.error(f"Error calculating technical signal for {symbol}: {e}")
            return None

    async def _get_vwap_signal(self, symbol: str, exchange: str) -> Optional[float]:
        """Get VWAP deviation signal."""
        try:
            # Get current price and volume
            current_price = await feature_store.get(symbol, "close", exchange=exchange)
            current_volume = await feature_store.get(symbol, "volume", exchange=exchange)

            if not current_price or not current_volume:
                return None

            # Use VWAP model
            features = {
                'symbol': symbol,
                'close_price': current_price,
                'volume': current_volume,
                'timestamp': datetime.now(),
                'exchange': exchange
            }

            prediction = await self.vwap_model.predict(features)

            # Convert VWAP signal to numeric
            signal_enum = prediction.get("signal", "AT_VWAP")
            z_score = prediction.get("primary_z_score", 0.0)

            if signal_enum == "BELOW_VWAP":
                return min(1.0, abs(z_score) / 2.0)  # Buy signal
            elif signal_enum == "ABOVE_VWAP":
                return max(-1.0, -abs(z_score) / 2.0)  # Sell signal
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Error getting VWAP signal for {symbol}: {e}")
            return None

    async def _get_rsi_model_signal(self, symbol: str, exchange: str) -> Optional[float]:
        """Get RSI model signal."""
        try:
            # Get price data
            prices_data = await feature_store.get_time_series(symbol, "close_prices", 50, exchange=exchange)
            if not prices_data or len(prices_data) < 15:
                return None

            # Extract price values
            prices = []
            for price_data in prices_data:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    prices.append(float(price_data))

            # Use RSI model
            features = {
                'symbol': symbol,
                'close_prices': prices,
                'timestamp': datetime.now(),
                'exchange': exchange
            }

            prediction = await self.rsi_model.predict(features)
            return prediction.get("signal_strength", 0.0)

        except Exception as e:
            logger.error(f"Error getting RSI model signal for {symbol}: {e}")
            return None

    async def _get_funding_signal(self, symbol: str, exchange: str) -> Optional[float]:
        """Get funding momentum signal."""
        try:
            # Use funding momentum model
            features = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'exchange': exchange
            }

            prediction = await self.funding_model.predict(features)

            action = prediction.get("action", "HOLD")
            confidence = prediction.get("confidence", 0.0)

            if action == "BUY":
                return confidence
            elif action == "SELL":
                return -confidence
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Error getting funding signal for {symbol}: {e}")
            return None

    async def _get_open_interest_signal(self, symbol: str, exchange: str) -> Optional[float]:
        """Get open interest momentum signal."""
        try:
            # Use open interest momentum model
            features = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'exchange': exchange
            }

            prediction = await self.oi_model.predict(features)

            action = prediction.get("action", "HOLD")
            confidence = prediction.get("confidence", 0.0)

            if action == "BUY":
                return confidence
            elif action == "SELL":
                return -confidence
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Error getting open interest signal for {symbol}: {e}")
            return None

    def _calculate_confidence_multiplier(self, signal_components: Dict[str, Dict[str, Any]]) -> float:
        """Calculate confidence multiplier based on signal agreement."""
        if not signal_components:
            return 1.0

        # Count positive and negative signals
        positive_signals = sum(1 for comp in signal_components.values() if comp["signal"] > 0.1)
        negative_signals = sum(1 for comp in signal_components.values() if comp["signal"] < -0.1)
        total_signals = len(signal_components)

        # Calculate agreement ratio
        max_agreement = max(positive_signals, negative_signals)
        agreement_ratio = max_agreement / total_signals if total_signals > 0 else 0.0

        # Boost confidence when signals agree
        if agreement_ratio > 0.7:
            return 1.3  # High agreement
        elif agreement_ratio > 0.5:
            return 1.1  # Moderate agreement
        else:
            return 0.8  # Low agreement

    def _get_dynamic_thresholds(self, signal_components: Dict[str, Dict[str, Any]]) -> Tuple[float, float]:
        """Get dynamic buy/sell thresholds based on signal strength and market conditions."""
        # Base thresholds
        buy_threshold = self.base_buy_threshold
        sell_threshold = self.base_sell_threshold

        # Adjust based on signal strength
        if signal_components:
            avg_confidence = np.mean([comp["confidence"] for comp in signal_components.values()])

            # Lower thresholds when confidence is high
            if avg_confidence > 0.7:
                buy_threshold *= 0.8
                sell_threshold *= 0.8
            elif avg_confidence > 0.5:
                buy_threshold *= 0.9
                sell_threshold *= 0.9
            else:
                buy_threshold *= 1.1
                sell_threshold *= 1.1

        return buy_threshold, sell_threshold

    def update_weights(self, new_weights: Dict[str, float]) -> None:
        """
        Update component weights.

        Args:
            new_weights: Dictionary of new weights
        """
        for component, weight in new_weights.items():
            if component in self.weights:
                self.weights[component] = weight
                logger.info(f"Updated {component} weight to {weight}")

    def get_model_status(self) -> Dict[str, Any]:
        """
        Get status of all models.

        Returns:
            Dictionary with model status information
        """
        return {
            "weights": self.weights,
            "models": {
                "rsi": {
                    "period": self.rsi_model.period,
                    "overbought": self.rsi_model.overbought_threshold,
                    "oversold": self.rsi_model.oversold_threshold
                },
                "vwap": {
                    "lookback_periods": self.vwap_model.lookback_periods,
                    "significant_deviation": self.vwap_model.significant_deviation,
                    "vwap_types": self.vwap_model.vwap_types
                },
                "funding": {
                    "short_window": self.funding_model.short_window,
                    "long_window": self.funding_model.long_window,
                    "contrarian": self.funding_model.contrarian
                },
                "open_interest": {
                    "delta_window": self.oi_model.delta_window,
                    "z_score_window": self.oi_model.z_score_window,
                    "mode": self.oi_model.mode
                }
            },
            "thresholds": {
                "base_buy_threshold": self.base_buy_threshold,
                "base_sell_threshold": self.base_sell_threshold
            }
        }
