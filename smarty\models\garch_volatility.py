"""
GARCH-style Volatility Forecaster for the smart-trader system.

This model fits a GARCH(1,1) model to recent price data to forecast
short-term volatility, which can be used for position sizing, stop placement,
and regime classification.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Deque
from datetime import datetime, timedelta
from enum import Enum
from collections import deque

from core.utils import timer

logger = logging.getLogger(__name__)

# Try to import arch package, fall back to statsmodels if not available
try:
    import arch
    from arch import arch_model
    ARCH_AVAILABLE = True
    logger.info("Using arch package for GARCH modeling")
except ImportError:
    ARCH_AVAILABLE = False
    logger.warning("arch package not available, falling back to statsmodels")
    try:
        import statsmodels.api as sm
        import statsmodels.tsa.api as tsa
        STATSMODELS_AVAILABLE = True
        logger.info("Using statsmodels for GARCH modeling")
    except ImportError:
        STATSMODELS_AVAILABLE = False
        logger.warning("Neither arch nor statsmodels available, using simple volatility estimation")


class VolatilityLevel(str, Enum):
    """Volatility level enum."""
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"


class GARCHVolatilityModel:
    """
    GARCH-style Volatility Forecaster.

    This model fits a GARCH(1,1) model to recent price data to forecast
    short-term volatility, which can be used for position sizing, stop placement,
    and regime classification.

    It provides:
    - 1-step ahead conditional volatility forecast
    - Normalized volatility z-score relative to longer-term average
    - Volatility regime classification
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        lookback_periods: int = 100,
        update_interval: int = 300,  # 5 minutes in seconds
        long_term_window: int = 500,
        garch_p: int = 1,
        garch_q: int = 1,
        model_type: str = "GARCH"  # "GARCH", "EGARCH", or "GJR-GARCH"
    ):
        """
        Initialize the GARCH Volatility model.

        Args:
            config: Configuration dictionary
            lookback_periods: Number of periods to use for GARCH fitting
            update_interval: How often to update the GARCH parameters (seconds)
            long_term_window: Window size for long-term volatility normalization
            garch_p: GARCH p parameter (lag order of GARCH terms)
            garch_q: GARCH q parameter (lag order of ARCH terms)
            model_type: Type of GARCH model to use
        """
        self.config = config or {}
        self.lookback_periods = self.config.get("lookback_periods", lookback_periods)
        self.update_interval = self.config.get("update_interval", update_interval)
        self.long_term_window = self.config.get("long_term_window", long_term_window)
        self.garch_p = self.config.get("garch_p", garch_p)
        self.garch_q = self.config.get("garch_q", garch_q)
        self.model_type = self.config.get("model_type", model_type)

        # Thresholds for volatility level classification
        self.vol_thresholds = self.config.get("vol_thresholds", {
            "very_low": -1.5,
            "low": -0.5,
            "high": 0.5,
            "very_high": 1.5
        })

        # Cache for returns and volatility
        self._returns_cache: Dict[str, Deque[float]] = {}
        self._volatility_cache: Dict[str, Deque[float]] = {}
        self._forecast_cache: Dict[str, float] = {}
        self._model_cache: Dict[str, Any] = {}
        self._last_update: Dict[str, datetime] = {}
        self._long_term_mean: Dict[str, float] = {}
        self._long_term_std: Dict[str, float] = {}

    @timer("garch_volatility_predict")
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'close_prices' or 'mid_price': Price data
                - 'timestamp': Current timestamp

        Returns:
            Dictionary of prediction results including:
                - 'volatility_forecast': 1-step ahead volatility forecast
                - 'volatility_z': Normalized volatility z-score
                - 'volatility_level': Volatility level classification
                - 'position_size_multiplier': Suggested position size multiplier
                - 'stop_width_multiplier': Suggested stop width multiplier
        """
        symbol = features.get('symbol', '')
        timestamp = features.get('timestamp', datetime.now())

        # Get price data
        close_prices = features.get('close_prices', [])
        if not close_prices:
            # Try to get from mid_price
            mid_price = features.get('mid_price')
            if mid_price is not None:
                close_prices = [mid_price]

        if not close_prices:
            logger.warning(f"No price data available for {symbol}")
            return self._default_prediction()

        # Initialize caches for this symbol if needed
        if symbol not in self._returns_cache:
            self._returns_cache[symbol] = deque(maxlen=max(self.lookback_periods, self.long_term_window) + 1)
            self._volatility_cache[symbol] = deque(maxlen=self.long_term_window)
            self._last_update[symbol] = datetime.min

        # Update returns cache with latest price
        current_price = close_prices[-1]
        if len(self._returns_cache[symbol]) > 0:
            # Calculate log return
            prev_price = self._returns_cache[symbol][-1]
            if prev_price > 0 and current_price > 0:
                log_return = np.log(current_price / prev_price)

                # Store the return
                self._returns_cache[symbol].append(log_return)
            else:
                # If we have invalid prices, just append 0
                self._returns_cache[symbol].append(0.0)
        else:
            # First price, no return yet
            self._returns_cache[symbol].append(current_price)

        # Check if we need to update the GARCH model
        time_since_update = (timestamp - self._last_update.get(symbol, datetime.min)).total_seconds()
        need_update = (
            time_since_update >= self.update_interval or
            symbol not in self._forecast_cache or
            symbol not in self._model_cache
        )

        # Update the model if needed
        if need_update and len(self._returns_cache[symbol]) > self.lookback_periods:
            # Get returns for model fitting
            returns = list(self._returns_cache[symbol])[-self.lookback_periods:]

            # Fit GARCH model and get forecast
            try:
                forecast, model = self._fit_garch_model(returns)
                self._forecast_cache[symbol] = forecast
                self._model_cache[symbol] = model
                self._last_update[symbol] = timestamp

                # Store volatility in cache
                self._volatility_cache[symbol].append(forecast)

                # Update long-term statistics
                if len(self._volatility_cache[symbol]) >= 30:  # Need at least 30 points
                    self._long_term_mean[symbol] = np.mean(self._volatility_cache[symbol])
                    self._long_term_std[symbol] = np.std(self._volatility_cache[symbol])
            except Exception as e:
                logger.error(f"Error fitting GARCH model: {e}")
                # If we have a previous forecast, keep using it
                if symbol not in self._forecast_cache:
                    self._forecast_cache[symbol] = self._estimate_simple_volatility(returns)

        # Get the current forecast
        volatility_forecast = self._forecast_cache.get(symbol, 0.0)

        # Calculate z-score if we have long-term statistics
        if symbol in self._long_term_mean and symbol in self._long_term_std and self._long_term_std[symbol] > 0:
            volatility_z = (volatility_forecast - self._long_term_mean[symbol]) / self._long_term_std[symbol]
        else:
            volatility_z = 0.0

        # Determine volatility level
        volatility_level = self._classify_volatility_level(volatility_z)

        # Calculate position size multiplier
        position_size_multiplier = self._calculate_position_size_multiplier(volatility_z)

        # Calculate stop width multiplier
        stop_width_multiplier = self._calculate_stop_width_multiplier(volatility_z)

        return {
            'volatility_forecast': float(volatility_forecast),
            'volatility_z': float(volatility_z),
            'volatility_level': volatility_level.value,
            'position_size_multiplier': float(position_size_multiplier),
            'stop_width_multiplier': float(stop_width_multiplier),
            'action': self._get_action_from_volatility(volatility_z),
            'confidence': min(1.0, abs(volatility_z) / 3.0)  # Scale confidence by z-score
        }

    def _fit_garch_model(self, returns: List[float]) -> Tuple[float, Any]:
        """
        Fit a GARCH model to returns data and forecast volatility.

        Args:
            returns: List of log returns

        Returns:
            Tuple of (volatility_forecast, fitted_model)
        """
        # Convert to numpy array
        returns_array = np.array(returns)

        # Remove any NaN or inf values
        returns_array = returns_array[np.isfinite(returns_array)]

        # Check if we have enough data
        if len(returns_array) < 10:
            logger.warning(f"Not enough valid returns for GARCH fitting: {len(returns_array)} < 10")
            return self._estimate_simple_volatility(returns), None

        # Try to fit GARCH model
        try:
            if ARCH_AVAILABLE:
                # Use arch package
                # Scale returns to avoid warnings
                scale_factor = 10.0 if np.std(returns_array) < 0.1 else 0.1 if np.std(returns_array) > 10.0 else 1.0
                scaled_returns = returns_array * scale_factor

                if self.model_type == "EGARCH":
                    model = arch_model(
                        scaled_returns,
                        vol="EGARCH",
                        p=self.garch_p,
                        q=self.garch_q,
                        rescale=False
                    )
                elif self.model_type == "GJR-GARCH":
                    model = arch_model(
                        scaled_returns,
                        vol="GARCH",
                        p=self.garch_p,
                        o=1,  # Include asymmetry term
                        q=self.garch_q,
                        rescale=False
                    )
                else:  # Default to GARCH
                    model = arch_model(
                        scaled_returns,
                        vol="GARCH",
                        p=self.garch_p,
                        q=self.garch_q,
                        rescale=False
                    )

                # Fit the model with robust starting values
                result = model.fit(disp='off', show_warning=False)

                # Forecast volatility
                forecast = result.forecast(horizon=1)
                # Extract the volatility forecast and scale back
                volatility = np.sqrt(forecast.variance.iloc[-1, 0]) / scale_factor

                return volatility, model

            elif STATSMODELS_AVAILABLE:
                # Use statsmodels
                # Scale returns to avoid warnings
                scale_factor = 10.0 if np.std(returns_array) < 0.1 else 0.1 if np.std(returns_array) > 10.0 else 1.0
                scaled_returns = returns_array * scale_factor

                # For simplicity, we'll use GARCH(1,1) regardless of model_type
                model = tsa.GARCH(scaled_returns, vol='Garch', p=self.garch_p, q=self.garch_q)
                result = model.fit(disp='off')

                # Forecast volatility
                forecast = result.forecast(horizon=1)
                # Extract the volatility forecast and scale back
                volatility = np.sqrt(forecast.mean.iloc[-1, 0]) / scale_factor

                return volatility, model

            else:
                # Fall back to simple volatility estimation
                return self._estimate_simple_volatility(returns), None

        except Exception as e:
            logger.error(f"Error in GARCH fitting: {e}")
            return self._estimate_simple_volatility(returns), None

    def _estimate_simple_volatility(self, returns: List[float]) -> float:
        """
        Estimate volatility using simple standard deviation.

        Args:
            returns: List of log returns

        Returns:
            Volatility estimate
        """
        # Convert to numpy array
        returns_array = np.array(returns)

        # Remove any NaN or inf values
        returns_array = returns_array[np.isfinite(returns_array)]

        # Calculate standard deviation
        if len(returns_array) > 0:
            # Scale to annualized volatility assuming returns are per minute
            return float(np.std(returns_array) * np.sqrt(60 * 24 * 365))
        else:
            return 0.0

    def _classify_volatility_level(self, volatility_z: float) -> VolatilityLevel:
        """
        Classify volatility level based on z-score.

        Args:
            volatility_z: Volatility z-score

        Returns:
            Volatility level classification
        """
        if volatility_z < self.vol_thresholds["very_low"]:
            return VolatilityLevel.VERY_LOW
        elif volatility_z < self.vol_thresholds["low"]:
            return VolatilityLevel.LOW
        elif volatility_z > self.vol_thresholds["very_high"]:
            return VolatilityLevel.VERY_HIGH
        elif volatility_z > self.vol_thresholds["high"]:
            return VolatilityLevel.HIGH
        else:
            return VolatilityLevel.NORMAL

    def _calculate_position_size_multiplier(self, volatility_z: float) -> float:
        """
        Calculate position size multiplier based on volatility z-score.

        Args:
            volatility_z: Volatility z-score

        Returns:
            Position size multiplier (0.0-1.5)
        """
        # Base multiplier is 1.0
        # Reduce size in high volatility, increase in low volatility
        if volatility_z > 1.5:  # Very high volatility
            return 0.5
        elif volatility_z > 0.5:  # High volatility
            return 0.75
        elif volatility_z < -1.5:  # Very low volatility
            return 1.5
        elif volatility_z < -0.5:  # Low volatility
            return 1.25
        else:  # Normal volatility
            return 1.0

    def _calculate_stop_width_multiplier(self, volatility_z: float) -> float:
        """
        Calculate stop width multiplier based on volatility z-score.

        Args:
            volatility_z: Volatility z-score

        Returns:
            Stop width multiplier (0.5-2.0)
        """
        # Base multiplier is 1.0
        # Widen stops in high volatility, tighten in low volatility
        if volatility_z > 1.5:  # Very high volatility
            return 2.0
        elif volatility_z > 0.5:  # High volatility
            return 1.5
        elif volatility_z < -1.5:  # Very low volatility
            return 0.5
        elif volatility_z < -0.5:  # Low volatility
            return 0.75
        else:  # Normal volatility
            return 1.0

    def _get_action_from_volatility(self, volatility_z: float) -> str:
        """
        Get trading action recommendation based on volatility.

        Args:
            volatility_z: Volatility z-score

        Returns:
            Trading action (BUY, SELL, HOLD)
        """
        # This is a simplified approach - in a real system, you'd want to
        # combine this with other signals and market direction
        if volatility_z > 1.5:
            return "HOLD"  # Very high volatility - be cautious
        elif volatility_z < -1.5:
            return "HOLD"  # Very low volatility - wait for breakout
        else:
            return "HOLD"  # Normal volatility - rely on other signals

    def _default_prediction(self) -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Returns:
            Default prediction dictionary
        """
        return {
            'volatility_forecast': 0.0,
            'volatility_z': 0.0,
            'volatility_level': VolatilityLevel.NORMAL.value,
            'position_size_multiplier': 1.0,
            'stop_width_multiplier': 1.0,
            'action': "HOLD",
            'confidence': 0.0
        }
