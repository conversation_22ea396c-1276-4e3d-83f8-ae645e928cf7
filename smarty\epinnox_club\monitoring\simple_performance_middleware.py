#!/usr/bin/env python3
"""
Simple Performance Middleware for Money Circle
A working implementation that doesn't have parameter issues
"""

import time
import logging
from datetime import datetime
from aiohttp import web

logger = logging.getLogger(__name__)

@web.middleware
async def simple_performance_middleware(request: web.Request, handler):
    """Simple performance monitoring middleware that actually works."""
    start_time = time.time()
    
    try:
        # Call the next handler
        response = await handler(request)
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Add performance headers
        response.headers['X-Response-Time'] = f"{response_time:.3f}s"
        response.headers['X-Server-Time'] = datetime.now().isoformat()
        
        # Log performance metrics
        endpoint = f"{request.method} {request.path}"
        logger.info(f"[PERF] {endpoint} - {response_time:.3f}s - {response.status}")
        
        return response
        
    except Exception as e:
        # Handle errors and still add timing
        response_time = time.time() - start_time
        logger.error(f"[PERF] Error in {request.method} {request.path} - {response_time:.3f}s - {e}")
        raise

def create_performance_middleware():
    """Factory function to create performance middleware."""
    return simple_performance_middleware
