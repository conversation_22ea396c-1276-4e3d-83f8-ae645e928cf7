#!/bin/bash
# SSL Certificate Setup for Money Circle Production Deployment
# Critical security configuration for HTTPS

set -e

echo "🔒 Setting up SSL certificates for Money Circle production deployment"

# Create SSL directory
mkdir -p ssl
cd ssl

# Option 1: Self-signed certificates (for testing/internal use)
setup_self_signed() {
    echo "📝 Generating self-signed SSL certificates..."
    
    # Generate private key
    openssl genrsa -out money_circle.key 2048
    
    # Generate certificate signing request
    openssl req -new -key money_circle.key -out money_circle.csr -subj "/C=US/ST=State/L=City/O=Epinnox/OU=IT/CN=money-circle.local"
    
    # Generate self-signed certificate
    openssl x509 -req -days 365 -in money_circle.csr -signkey money_circle.key -out money_circle.crt
    
    # Set proper permissions
    chmod 600 money_circle.key
    chmod 644 money_circle.crt
    
    echo "✅ Self-signed certificates generated"
    echo "   Certificate: ssl/money_circle.crt"
    echo "   Private Key: ssl/money_circle.key"
}

# Option 2: Let's Encrypt certificates (for public deployment)
setup_letsencrypt() {
    echo "🌐 Setting up Let's Encrypt certificates..."
    
    # Install certbot if not available
    if ! command -v certbot &> /dev/null; then
        echo "Installing certbot..."
        sudo apt-get update
        sudo apt-get install -y certbot
    fi
    
    # Get domain from user
    read -p "Enter your domain name (e.g., money-circle.yourdomain.com): " DOMAIN
    
    # Generate Let's Encrypt certificate
    sudo certbot certonly --standalone -d $DOMAIN
    
    # Copy certificates to our SSL directory
    sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem money_circle.crt
    sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem money_circle.key
    
    # Set proper permissions
    sudo chown $USER:$USER money_circle.*
    chmod 600 money_circle.key
    chmod 644 money_circle.crt
    
    echo "✅ Let's Encrypt certificates configured"
    echo "   Certificate: ssl/money_circle.crt"
    echo "   Private Key: ssl/money_circle.key"
}

# Option 3: Custom certificate (for enterprise deployment)
setup_custom() {
    echo "🏢 Setting up custom SSL certificates..."
    echo "Please place your SSL certificate files in the ssl/ directory:"
    echo "   - Certificate: ssl/money_circle.crt"
    echo "   - Private Key: ssl/money_circle.key"
    echo "   - Certificate Chain (if applicable): ssl/money_circle_chain.crt"
    
    read -p "Press Enter when certificates are in place..."
    
    # Verify certificates exist
    if [[ -f "money_circle.crt" && -f "money_circle.key" ]]; then
        # Set proper permissions
        chmod 600 money_circle.key
        chmod 644 money_circle.crt
        
        # Verify certificate
        openssl x509 -in money_circle.crt -text -noout > /dev/null
        echo "✅ Custom certificates configured and verified"
    else
        echo "❌ Certificate files not found. Please ensure files are in ssl/ directory"
        exit 1
    fi
}

# Main menu
echo "Select SSL certificate setup option:"
echo "1) Self-signed certificates (testing/internal use)"
echo "2) Let's Encrypt certificates (public deployment)"
echo "3) Custom certificates (enterprise deployment)"
read -p "Enter choice (1-3): " choice

case $choice in
    1) setup_self_signed ;;
    2) setup_letsencrypt ;;
    3) setup_custom ;;
    *) echo "Invalid choice"; exit 1 ;;
esac

# Update production configuration
cd ..
echo "🔧 Updating production configuration for SSL..."

# Create SSL-enabled production config
cat > production_ssl_config.json << EOF
{
    "environment": "production",
    "version": "1.0.0",
    "server": {
        "host": "0.0.0.0",
        "port": 8086,
        "ssl_enabled": true,
        "ssl_cert_path": "ssl/money_circle.crt",
        "ssl_key_path": "ssl/money_circle.key",
        "debug": false,
        "workers": 4
    },
    "security": {
        "https_only": true,
        "secure_cookies": true,
        "csrf_protection": true,
        "rate_limit_requests": 1000,
        "session_timeout": 7200,
        "max_login_attempts": 5,
        "lockout_duration": 900
    }
}
EOF

echo "✅ SSL configuration complete!"
echo ""
echo "🔒 SECURITY CHECKLIST:"
echo "   ✅ SSL certificates configured"
echo "   ✅ HTTPS-only mode enabled"
echo "   ✅ Secure cookies configured"
echo "   ✅ CSRF protection active"
echo "   ✅ Rate limiting configured"
echo ""
echo "🚀 Next steps:"
echo "   1. Update DNS records to point to your server"
echo "   2. Configure firewall to allow HTTPS (port 443)"
echo "   3. Test SSL configuration with: openssl s_client -connect yourdomain.com:443"
echo "   4. Run production deployment with SSL config"
