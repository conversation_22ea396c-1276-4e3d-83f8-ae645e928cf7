#!/usr/bin/env python3
"""
Debug Dashboard - Simplified version to identify startup issues
"""

import asyncio
import logging
import sys
from aiohttp import web
import aiohttp_cors

# Set up basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DebugDashboard:
    """Simplified dashboard for debugging."""
    
    def __init__(self):
        logger.info("🔍 Initializing debug dashboard...")
        self.app = web.Application()
        
    async def serve_debug_page(self, request):
        """Serve a simple debug page."""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Debug Dashboard</title>
</head>
<body>
    <h1>🔍 Debug Dashboard</h1>
    <p>If you can see this page, the basic web server is working!</p>
    <p>Timestamp: <span id="timestamp"></span></p>
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def start_server(self, host: str = "localhost", port: int = 8082):
        """Start the debug server."""
        logger.info(f"🚀 Starting debug server on {host}:{port}")
        
        # Setup routes
        self.app.router.add_get('/', self.serve_debug_page)
        self.app.router.add_get('/debug', self.serve_debug_page)
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
        
        # Start server
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        logger.info(f"✅ Debug server running at http://{host}:{port}")
        
        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Debug server shutting down...")
        finally:
            await runner.cleanup()

async def main():
    """Main entry point."""
    logger.info("🎯 Starting Debug Dashboard")
    
    dashboard = DebugDashboard()
    await dashboard.start_server()

if __name__ == "__main__":
    asyncio.run(main())
