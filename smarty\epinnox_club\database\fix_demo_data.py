#!/usr/bin/env python3
"""
Fix demo data by adding member profiles and mock exchange accounts.
"""

import sqlite3
import random
import json
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoDataFixer:
    def __init__(self, db_path='data/money_circle.db'):
        self.db_path = db_path
        self.conn = None
        
        # Demo user profiles data
        self.user_profiles = {
            'trader_alex': {
                'display_name': '<PERSON>',
                'bio': 'Quantitative trader with 8 years experience in algorithmic trading. Specializes in momentum strategies and risk management.',
                'trading_style': 'algorithmic',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'crypto,stocks',
                'balance': 25000.0
            },
            'crypto_sarah': {
                'display_name': '<PERSON>',
                'bio': 'Cryptocurrency enthusiast and DeFi researcher. Focus on altcoin analysis and yield farming strategies.',
                'trading_style': 'swing',
                'risk_tolerance': 'aggressive',
                'preferred_assets': 'crypto,defi',
                'balance': 18500.0
            },
            'quant_mike': {
                'display_name': '<PERSON>',
                'bio': 'Former hedge fund analyst turned independent trader. Expert in statistical arbitrage and pairs trading.',
                'trading_style': 'quantitative',
                'risk_tolerance': 'conservative',
                'preferred_assets': 'stocks,bonds',
                'balance': 45000.0
            },
            'forex_emma': {
                'display_name': 'Emma Johnson',
                'bio': 'Professional forex trader with focus on major currency pairs. Specializes in technical analysis and scalping.',
                'trading_style': 'scalping',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'forex,commodities',
                'balance': 32000.0
            },
            'options_david': {
                'display_name': 'David Kim',
                'bio': 'Options trading specialist with expertise in volatility strategies and covered calls.',
                'trading_style': 'options',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'options,stocks',
                'balance': 28000.0
            },
            'swing_lisa': {
                'display_name': 'Lisa Wang',
                'bio': 'Swing trader focusing on growth stocks and sector rotation strategies. Part-time trader with engineering background.',
                'trading_style': 'swing',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'stocks,etfs',
                'balance': 15000.0
            },
            'momentum_james': {
                'display_name': 'James Wilson',
                'bio': 'Momentum trader specializing in breakout strategies and high-volume stocks.',
                'trading_style': 'momentum',
                'risk_tolerance': 'aggressive',
                'preferred_assets': 'stocks,crypto',
                'balance': 22000.0
            },
            'value_maria': {
                'display_name': 'Maria Garcia',
                'bio': 'Value investor with focus on undervalued dividend stocks and long-term wealth building.',
                'trading_style': 'value',
                'risk_tolerance': 'conservative',
                'preferred_assets': 'stocks,reits',
                'balance': 35000.0
            },
            'algo_robert': {
                'display_name': 'Robert Chen',
                'bio': 'Algorithmic trading developer and platform administrator. Expert in Python and trading system architecture.',
                'trading_style': 'algorithmic',
                'risk_tolerance': 'moderate',
                'preferred_assets': 'crypto,stocks',
                'balance': 50000.0
            },
            'scalp_jenny': {
                'display_name': 'Jennifer Brown',
                'bio': 'Professional scalper trading futures and forex. High-frequency trading specialist.',
                'trading_style': 'scalping',
                'risk_tolerance': 'aggressive',
                'preferred_assets': 'futures,forex',
                'balance': 38000.0
            }
        }

    def connect_db(self):
        """Connect to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            logger.info(f"Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False

    def create_member_profiles(self):
        """Create member profiles for demo users."""
        try:
            # Get all demo users
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()
            
            for user in users:
                user_id = user[0]
                username = user[1]
                
                if username in self.user_profiles:
                    profile = self.user_profiles[username]
                    
                    # Insert or update member profile
                    self.conn.execute("""
                        INSERT OR REPLACE INTO member_profiles 
                        (user_id, display_name, bio, trading_style, risk_tolerance, preferred_assets, 
                         public_stats, joined_strategies, total_votes, reputation_score)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        profile['display_name'],
                        profile['bio'],
                        profile['trading_style'],
                        profile['risk_tolerance'],
                        profile['preferred_assets'],
                        True,  # public_stats
                        random.randint(2, 8),  # joined_strategies
                        random.randint(5, 25),  # total_votes
                        random.uniform(3.5, 4.8)  # reputation_score
                    ))
                    
                    logger.info(f"Created profile for {username}")
            
            self.conn.commit()
            logger.info("✅ Member profiles created")
            
        except Exception as e:
            logger.error(f"❌ Error creating member profiles: {e}")

    def create_mock_exchange_accounts(self):
        """Create mock exchange accounts for demo users."""
        try:
            # Get all demo users
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()
            
            for user in users:
                user_id = user[0]
                username = user[1]
                
                if username in self.user_profiles:
                    profile = self.user_profiles[username]
                    balance = profile['balance']
                    
                    # Create HTX exchange account
                    self.conn.execute("""
                        INSERT OR REPLACE INTO user_exchanges 
                        (user_id, exchange_name, api_key, api_secret, passphrase, 
                         is_testnet, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        'HTX',
                        f'demo_key_{username}',
                        f'demo_secret_{username}',
                        '',
                        True,  # is_testnet
                        True,  # is_active
                        datetime.now().isoformat()
                    ))
                    
                    # Create mock balance entries
                    usdt_balance = balance * random.uniform(0.3, 0.7)  # 30-70% in USDT
                    btc_balance = (balance - usdt_balance) / 50000  # Rest in BTC (assuming $50k BTC)
                    
                    # Insert mock balances
                    balances = [
                        ('USDT', usdt_balance, usdt_balance * 0.9, usdt_balance * 0.1),
                        ('BTC', btc_balance, btc_balance * 0.8, btc_balance * 0.2),
                    ]
                    
                    for symbol, total, free, used in balances:
                        self.conn.execute("""
                            INSERT OR REPLACE INTO user_balances 
                            (user_id, exchange_name, symbol, total_balance, free_balance, 
                             used_balance, usd_value, last_updated)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            user_id,
                            'HTX',
                            symbol,
                            total,
                            free,
                            used,
                            total * (1 if symbol == 'USDT' else 50000),  # USD value
                            datetime.now().isoformat()
                        ))
                    
                    logger.info(f"Created exchange account for {username} with ${balance:,.2f}")
            
            self.conn.commit()
            logger.info("✅ Mock exchange accounts created")
            
        except Exception as e:
            logger.error(f"❌ Error creating exchange accounts: {e}")

    def create_portfolio_snapshots(self):
        """Create portfolio snapshots for demo users."""
        try:
            # Get all demo users
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()
            
            for user in users:
                user_id = user[0]
                username = user[1]
                
                if username in self.user_profiles:
                    profile = self.user_profiles[username]
                    balance = profile['balance']
                    
                    # Create portfolio snapshots for the last 30 days
                    for days_ago in range(30):
                        snapshot_date = datetime.now() - timedelta(days=days_ago)
                        
                        # Simulate portfolio growth/decline
                        daily_change = random.uniform(-0.03, 0.05)  # -3% to +5% daily
                        portfolio_value = balance * (1 + daily_change * days_ago / 30)
                        
                        daily_pnl = portfolio_value * random.uniform(-0.02, 0.03)  # Daily P&L
                        
                        self.conn.execute("""
                            INSERT OR REPLACE INTO portfolio_snapshots 
                            (user_id, snapshot_date, total_value, daily_pnl, daily_pnl_percent, 
                             positions_count, cash_balance, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            user_id,
                            snapshot_date.date().isoformat(),
                            portfolio_value,
                            daily_pnl,
                            (daily_pnl / portfolio_value) * 100,
                            random.randint(3, 12),  # positions_count
                            portfolio_value * random.uniform(0.1, 0.4),  # cash_balance
                            datetime.now().isoformat()
                        ))
                    
                    logger.info(f"Created portfolio snapshots for {username}")
            
            self.conn.commit()
            logger.info("✅ Portfolio snapshots created")
            
        except Exception as e:
            logger.error(f"❌ Error creating portfolio snapshots: {e}")

    def update_user_stats(self):
        """Update user statistics for better display."""
        try:
            # Get all demo users
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()
            
            for user in users:
                user_id = user[0]
                username = user[1]
                
                if username in self.user_profiles:
                    # Update user with additional stats
                    self.conn.execute("""
                        UPDATE users 
                        SET last_login = ?, is_active = 1
                        WHERE id = ?
                    """, (
                        (datetime.now() - timedelta(days=random.randint(0, 7))).isoformat(),
                        user_id
                    ))
            
            self.conn.commit()
            logger.info("✅ User stats updated")
            
        except Exception as e:
            logger.error(f"❌ Error updating user stats: {e}")

    def fix_all_demo_data(self):
        """Fix all demo data issues."""
        logger.info("🔧 Starting demo data fixes...")
        
        if not self.connect_db():
            return False
        
        try:
            # Step 1: Create member profiles
            logger.info("Step 1: Creating member profiles...")
            self.create_member_profiles()
            
            # Step 2: Create mock exchange accounts
            logger.info("Step 2: Creating mock exchange accounts...")
            self.create_mock_exchange_accounts()
            
            # Step 3: Create portfolio snapshots
            logger.info("Step 3: Creating portfolio snapshots...")
            self.create_portfolio_snapshots()
            
            # Step 4: Update user stats
            logger.info("Step 4: Updating user stats...")
            self.update_user_stats()
            
            logger.info("✅ All demo data fixes completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Demo data fix failed: {e}")
            return False
        
        finally:
            if self.conn:
                self.conn.close()

def main():
    """Main function to fix demo data."""
    fixer = DemoDataFixer()
    success = fixer.fix_all_demo_data()
    
    if success:
        print("\nDEMO DATA FIXES COMPLETED!")
        print("=" * 50)
        print("✅ Member profiles created for all demo users")
        print("✅ Mock exchange accounts with realistic balances")
        print("✅ Portfolio snapshots for performance tracking")
        print("✅ User statistics updated")
        print("\n🎯 Demo users should now appear in:")
        print("   - Member Directory with profiles and stats")
        print("   - Personal dashboards with account balances")
        print("   - Portfolio performance charts")
        print("\n🔐 Login with any demo account:")
        print("   Username: trader_alex, crypto_sarah, etc.")
        print("   Password: securepass123")
        return 0
    else:
        print("\nDEMO DATA FIX FAILED - Check logs for details")
        return 1

if __name__ == "__main__":
    exit(main())
