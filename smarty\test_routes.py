#!/usr/bin/env python3
"""
Test script to verify dashboard routes are properly registered
"""

import requests
import time

def test_route(route, description):
    """Test a single route."""
    try:
        print(f"Testing {description}: {route}")
        response = requests.get(f'http://localhost:8082{route}', timeout=5)
        print(f"  Status: {response.status_code}")
        if response.status_code == 200:
            print(f"  ✅ SUCCESS")
            return True
        else:
            print(f"  ❌ FAILED - {response.text}")
            return False
    except Exception as e:
        print(f"  💥 ERROR - {e}")
        return False

def main():
    """Test all routes."""
    print("🔍 Testing Dashboard Routes")
    print("=" * 50)
    
    # Wait for dashboard to be ready
    print("⏳ Waiting for dashboard to be ready...")
    time.sleep(5)
    
    routes = [
        ('/', 'Main Dashboard'),
        ('/api/market', 'Market Data'),
        ('/api/signals', 'Trading Signals'),
        ('/api/trades', 'Active Trades'),
        ('/api/stats', 'System Stats'),
        ('/api/ai-analysis', 'AI Analysis'),
        ('/api/market-sentiment', 'Market Sentiment'),
        ('/api/orderbook', 'Order Book'),
        ('/api/recent-trades', 'Recent Trades'),
    ]
    
    results = []
    for route, description in routes:
        success = test_route(route, description)
        results.append((route, description, success))
        time.sleep(0.5)
    
    print("\n" + "=" * 50)
    print("📊 RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, _, success in results if success)
    total = len(results)
    
    for route, description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {description}")
    
    print(f"\n🎯 Results: {passed}/{total} routes working")

if __name__ == "__main__":
    main()
