{"start_time": "2025-05-23T19:34:44.983924", "tests": {"config_loading": {"status": "FAIL", "message": "Configuration loading failed: Missing required configuration key: symbols", "details": {}}, "monitor_init": {"status": "PASS", "message": "Model monitor initialized and functioning", "details": {"total_predictions": 1, "avg_latency_ms": 100.0, "avg_confidence": 0.8}}, "feature_store": {"status": "PASS", "message": "Feature store integration working", "details": {"stored_data": {"price": 50000.0, "volume": 1000.0, "timestamp": "2025-05-23T19:34:44.994446"}, "retrieved_data": {"price": 50000.0, "volume": 1000.0, "timestamp": "2025-05-23T19:34:44.994446"}}}, "model_execution": {"status": "PASS", "message": "Model execution successful", "details": {"execution_time_ms": 0.0, "prediction": {"rsi": 50.0, "is_overbought": "False", "is_oversold": "False", "bullish_divergence": "False", "bearish_divergence": "False", "signal_strength": 0.0, "prob_overbought": 0.25}, "rsi_value": 50.0}}, "signal_generation": {"status": "PASS", "message": "Generated 0 signals", "details": {"signal_count": 0, "signals": []}}, "performance_tracking": {"status": "PASS", "message": "Performance tracking working", "details": {"total_predictions": 5, "total_signals": 3, "active_models": 1}}, "dashboard_api": {"status": "PASS", "message": "Dashboard API data generation working", "details": {"summary_keys": ["timestamp", "uptime_hours", "models", "signals", "system_health", "active_alerts"], "health_uptime": 0.0}}, "alert_system": {"status": "PASS", "message": "Alert system functioning", "details": {"active_alerts": ["High latency in slow_model: 200.0ms"], "alert_count": 1}}}, "overall_status": "PARTIAL", "end_time": "2025-05-23T19:34:45.575811", "duration_seconds": 0.591887, "passed_tests": 7, "total_tests": 8}