/* Money Circle Unified Footer Component */

.unified-footer {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
    color: #e2e8f0;
}

/* Main Footer Content */
.footer-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
}

/* Company Info Section */
.footer-company {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.footer-logo .logo-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    font-size: 1.1rem;
}

.footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: #94a3b8;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.footer-tagline {
    color: #8b5cf6;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Social Links */
.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.social-link:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: #8b5cf6;
    color: #8b5cf6;
    transform: translateY(-2px);
}

/* Footer Sections */
.footer-section h4 {
    color: #f1f5f9;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 1px;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-link {
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    padding: 0.25rem 0;
}

.footer-link:hover {
    color: #8b5cf6;
    padding-left: 0.5rem;
}

/* Contact Info */
.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #94a3b8;
    font-size: 0.9rem;
}

.contact-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8b5cf6;
}

/* Newsletter Signup */
.newsletter-signup {
    margin-top: 1rem;
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.newsletter-input {
    flex: 1;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.newsletter-input::placeholder {
    color: #64748b;
}

.newsletter-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border: none;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.newsletter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 2rem;
    background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright {
    color: #64748b;
    font-size: 0.875rem;
}

.footer-legal {
    display: flex;
    gap: 2rem;
}

.footer-legal a {
    color: #64748b;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: #8b5cf6;
}

/* Security Badge */
.security-badges {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 0.5rem;
    color: #22c55e;
    font-size: 0.8rem;
    font-weight: 500;
}

.security-icon {
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-main {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .footer-company {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .footer-main {
        grid-template-columns: 1fr;
        padding: 2rem 1rem 1.5rem;
        gap: 2rem;
    }
    
    .footer-bottom {
        padding: 1rem;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .footer-legal {
        gap: 1rem;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .security-badges {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .footer-main {
        padding: 1.5rem 1rem 1rem;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    .footer-legal {
        flex-direction: column;
        gap: 0.5rem;
    }
}
