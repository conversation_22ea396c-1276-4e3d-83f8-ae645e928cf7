<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Controls Test - Money Circle</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-section h2 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .test-button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .test-button.primary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }
        .test-button.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        .test-button.danger {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        .test-result {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            border-left: 4px solid #FFD700;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.pass {
            background: #4CAF50;
            color: white;
        }
        .status.fail {
            background: #f44336;
            color: white;
        }
        .status.pending {
            background: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Money Circle Trading Controls Test Suite</h1>
        
        <!-- API Connectivity Tests -->
        <div class="test-section">
            <h2>🔌 API Connectivity Tests</h2>
            <div class="test-grid">
                <div>
                    <h3>Market Data API</h3>
                    <button class="test-button primary" onclick="testMarketData()">Test Market Data</button>
                    <div id="market-data-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Portfolio API</h3>
                    <button class="test-button primary" onclick="testPortfolioAPI()">Test Portfolio</button>
                    <div id="portfolio-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Trading API</h3>
                    <button class="test-button primary" onclick="testTradingAPI()">Test Trading API</button>
                    <div id="trading-api-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Trading Controls Tests -->
        <div class="test-section">
            <h2>📈 Trading Controls Tests</h2>
            <div class="test-grid">
                <div>
                    <h3>Market Orders</h3>
                    <button class="test-button success" onclick="testMarketOrder('buy')">Test Buy Order</button>
                    <button class="test-button danger" onclick="testMarketOrder('sell')">Test Sell Order</button>
                    <div id="market-order-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Limit Orders</h3>
                    <button class="test-button success" onclick="testLimitOrder('buy')">Test Buy Limit</button>
                    <button class="test-button danger" onclick="testLimitOrder('sell')">Test Sell Limit</button>
                    <div id="limit-order-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Quick Trade</h3>
                    <button class="test-button primary" onclick="testQuickTrade()">Test Quick Trade</button>
                    <div id="quick-trade-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Risk Management Tests -->
        <div class="test-section">
            <h2>🛡️ Risk Management Tests</h2>
            <div class="test-grid">
                <div>
                    <h3>Global Stop Loss</h3>
                    <button class="test-button danger" onclick="testGlobalStopLoss()">Test Stop Loss</button>
                    <div id="stop-loss-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Emergency Close</h3>
                    <button class="test-button danger" onclick="testEmergencyClose()">Test Emergency Close</button>
                    <div id="emergency-close-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Position Management</h3>
                    <button class="test-button primary" onclick="testPositionManagement()">Test Position Controls</button>
                    <div id="position-mgmt-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- UI/UX Tests -->
        <div class="test-section">
            <h2>🎨 UI/UX Tests</h2>
            <div class="test-grid">
                <div>
                    <h3>Tab Switching</h3>
                    <button class="test-button primary" onclick="testTabSwitching()">Test Tabs</button>
                    <div id="tab-switching-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Modal Dialogs</h3>
                    <button class="test-button primary" onclick="testModals()">Test Modals</button>
                    <div id="modals-result" class="test-result" style="display: none;"></div>
                </div>
                <div>
                    <h3>Notifications</h3>
                    <button class="test-button primary" onclick="testNotifications()">Test Notifications</button>
                    <div id="notifications-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Overall Test Results -->
        <div class="test-section">
            <h2>📊 Test Summary</h2>
            <div id="test-summary">
                <p>Click "Run All Tests" to execute the complete test suite.</p>
                <button class="test-button primary" onclick="runAllTests()" style="font-size: 16px; padding: 15px 30px;">
                    🚀 Run All Tests
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test results tracking
        let testResults = {};

        // Utility functions
        function showResult(elementId, message, status) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `${message} <span class="status ${status}">${status.toUpperCase()}</span>`;
            testResults[elementId] = status;
        }

        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // API Tests
        async function testMarketData() {
            try {
                const response = await fetch('/api/market/data/BTCUSDT');
                const data = await response.json();
                if (response.ok && data.price) {
                    showResult('market-data-result', `Market data API working. BTC price: $${data.price}`, 'pass');
                } else {
                    showResult('market-data-result', 'Market data API failed', 'fail');
                }
            } catch (error) {
                showResult('market-data-result', `Market data API error: ${error.message}`, 'fail');
            }
        }

        async function testPortfolioAPI() {
            try {
                const response = await fetch('/api/portfolio');
                const data = await response.json();
                if (response.ok) {
                    showResult('portfolio-result', 'Portfolio API working', 'pass');
                } else {
                    showResult('portfolio-result', 'Portfolio API failed', 'fail');
                }
            } catch (error) {
                showResult('portfolio-result', `Portfolio API error: ${error.message}`, 'fail');
            }
        }

        async function testTradingAPI() {
            // Test if trading API endpoints are accessible
            try {
                const response = await fetch('/api/trading/market_order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({}) // Empty body to test endpoint availability
                });
                
                if (response.status === 400) {
                    // 400 means endpoint exists but missing required fields
                    showResult('trading-api-result', 'Trading API endpoints accessible', 'pass');
                } else {
                    showResult('trading-api-result', 'Trading API response unexpected', 'fail');
                }
            } catch (error) {
                showResult('trading-api-result', `Trading API error: ${error.message}`, 'fail');
            }
        }

        // Trading Tests
        async function testMarketOrder(side) {
            try {
                const response = await fetch('/api/trading/market_order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: 'BTCUSDT',
                        side: side,
                        amount: 0.001,
                        exchange: 'HTX'
                    })
                });
                
                const data = await response.json();
                if (response.ok && data.success) {
                    showResult('market-order-result', `${side} market order test passed`, 'pass');
                } else {
                    showResult('market-order-result', `Market order test failed: ${data.error || 'Unknown error'}`, 'fail');
                }
            } catch (error) {
                showResult('market-order-result', `Market order error: ${error.message}`, 'fail');
            }
        }

        async function testLimitOrder(side) {
            try {
                const response = await fetch('/api/trading/limit_order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: 'BTCUSDT',
                        side: side,
                        amount: 0.001,
                        price: side === 'buy' ? 45000 : 55000,
                        exchange: 'HTX'
                    })
                });
                
                const data = await response.json();
                if (response.ok && data.success) {
                    showResult('limit-order-result', `${side} limit order test passed`, 'pass');
                } else {
                    showResult('limit-order-result', `Limit order test failed: ${data.error || 'Unknown error'}`, 'fail');
                }
            } catch (error) {
                showResult('limit-order-result', `Limit order error: ${error.message}`, 'fail');
            }
        }

        async function testQuickTrade() {
            // Test if quick trade functions exist
            if (typeof quickTrade === 'function') {
                showResult('quick-trade-result', 'Quick trade functions available', 'pass');
            } else {
                showResult('quick-trade-result', 'Quick trade functions missing', 'fail');
            }
        }

        // Risk Management Tests
        async function testGlobalStopLoss() {
            try {
                const response = await fetch('/api/trading/global_stop_loss', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ stop_loss_percent: 5 })
                });
                
                const data = await response.json();
                if (response.ok && data.success) {
                    showResult('stop-loss-result', 'Global stop loss test passed', 'pass');
                } else {
                    showResult('stop-loss-result', `Stop loss test failed: ${data.error || 'Unknown error'}`, 'fail');
                }
            } catch (error) {
                showResult('stop-loss-result', `Stop loss error: ${error.message}`, 'fail');
            }
        }

        async function testEmergencyClose() {
            try {
                const response = await fetch('/api/trading/emergency_close_all', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                if (response.ok && data.success !== undefined) {
                    showResult('emergency-close-result', 'Emergency close test passed', 'pass');
                } else {
                    showResult('emergency-close-result', `Emergency close test failed: ${data.error || 'Unknown error'}`, 'fail');
                }
            } catch (error) {
                showResult('emergency-close-result', `Emergency close error: ${error.message}`, 'fail');
            }
        }

        async function testPositionManagement() {
            // Test if position management functions exist
            if (typeof closePosition === 'function' && typeof modifyPosition === 'function') {
                showResult('position-mgmt-result', 'Position management functions available', 'pass');
            } else {
                showResult('position-mgmt-result', 'Position management functions missing', 'fail');
            }
        }

        // UI/UX Tests
        async function testTabSwitching() {
            if (typeof switchTab === 'function') {
                showResult('tab-switching-result', 'Tab switching function available', 'pass');
            } else {
                showResult('tab-switching-result', 'Tab switching function missing', 'fail');
            }
        }

        async function testModals() {
            if (typeof showQuickTradeConfirmation === 'function' && typeof showConfirmation === 'function') {
                showResult('modals-result', 'Modal functions available', 'pass');
            } else {
                showResult('modals-result', 'Modal functions missing', 'fail');
            }
        }

        async function testNotifications() {
            if (typeof showNotification === 'function') {
                showResult('notifications-result', 'Notification function available', 'pass');
                // Test notification
                showNotification('Test notification working!', 'success');
            } else {
                showResult('notifications-result', 'Notification function missing', 'fail');
            }
        }

        // Run all tests
        async function runAllTests() {
            const summaryElement = document.getElementById('test-summary');
            summaryElement.innerHTML = '<p>Running tests... Please wait.</p>';
            
            // Clear previous results
            testResults = {};
            
            // Run all tests with delays
            await testMarketData();
            await delay(500);
            await testPortfolioAPI();
            await delay(500);
            await testTradingAPI();
            await delay(500);
            await testMarketOrder('buy');
            await delay(500);
            await testLimitOrder('buy');
            await delay(500);
            await testQuickTrade();
            await delay(500);
            await testGlobalStopLoss();
            await delay(500);
            await testEmergencyClose();
            await delay(500);
            await testPositionManagement();
            await delay(500);
            await testTabSwitching();
            await delay(500);
            await testModals();
            await delay(500);
            await testNotifications();
            
            // Calculate results
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === 'pass').length;
            const failedTests = totalTests - passedTests;
            
            const successRate = ((passedTests / totalTests) * 100).toFixed(1);
            
            summaryElement.innerHTML = `
                <h3>Test Results Summary</h3>
                <p><strong>Total Tests:</strong> ${totalTests}</p>
                <p><strong>Passed:</strong> <span style="color: #4CAF50;">${passedTests}</span></p>
                <p><strong>Failed:</strong> <span style="color: #f44336;">${failedTests}</span></p>
                <p><strong>Success Rate:</strong> <span style="color: #FFD700;">${successRate}%</span></p>
                <button class="test-button primary" onclick="runAllTests()" style="font-size: 16px; padding: 15px 30px;">
                    🔄 Run Tests Again
                </button>
            `;
        }
    </script>
</body>
</html>
