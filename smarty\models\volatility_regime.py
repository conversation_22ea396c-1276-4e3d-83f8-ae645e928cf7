"""
Volatility Regime Classifier for the smart-trader system.

This model classifies the current market into different volatility regimes:
- LOW_VOL: Low volatility, typically range-bound or slow trending markets
- NORMAL: Normal volatility, typical market conditions
- HIGH_VOL: High volatility, fast-moving or choppy markets
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Literal
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class VolatilityRegime(str, Enum):
    """Volatility regime enum."""
    LOW_VOL = "LOW_VOL"
    NORMAL = "NORMAL"
    HIGH_VOL = "HIGH_VOL"


class VolatilityRegimeModel:
    """
    Volatility Regime Classifier.

    This model uses multiple metrics to classify the current market regime:
    1. Price volatility (ATR, standard deviation)
    2. Volume profile
    3. Funding rate volatility (if available)

    The regime classification can be used to adjust:
    - Position sizing
    - Stop loss distances
    - Take profit targets
    - Trading frequency
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        lookback_window: int = 30,
        update_interval: int = 5  # minutes
    ):
        """
        Initialize the Volatility Regime model.

        Args:
            config: Configuration dictionary
            lookback_window: Number of periods to look back for regime calculation
            update_interval: How often to update the regime classification (minutes)
        """
        self.config = config or {}
        self.lookback_window = self.config.get("lookback_window", lookback_window)
        self.update_interval = self.config.get("update_interval", update_interval)

        # Get regime thresholds from config or use defaults
        regime_thresholds = self.config.get("regime_thresholds", {})
        self.low_vol_threshold = regime_thresholds.get("low_vol", 0.5)  # percentile
        self.high_vol_threshold = regime_thresholds.get("high_vol", 2.0)  # percentile

        # Cache for regime classification
        self._regime_cache: Dict[str, Tuple[VolatilityRegime, float, datetime]] = {}

        # Historical volatility data
        self._historical_vol: Dict[str, List[float]] = {}
        self._historical_funding: Dict[str, List[float]] = {}

    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'close_prices': Array of close prices
                - 'timestamp': Current timestamp
                - 'funding_rate': Current funding rate (optional)

        Returns:
            Dictionary of prediction results including:
                - 'regime': Current volatility regime (LOW_VOL, NORMAL, HIGH_VOL)
                - 'confidence': Confidence in the regime classification (0-1)
                - 'volatility': Current volatility measure
                - 'percentile': Volatility percentile relative to history
                - 'action': Trading action recommendation based on regime
                - 'position_size_multiplier': Suggested position size multiplier
        """
        symbol = features.get('symbol', '')
        close_prices = features.get('close_prices', [])
        funding_rate = features.get('funding_rate', None)
        timestamp = features.get('timestamp', datetime.now())

        if not close_prices or len(close_prices) < self.lookback_window + 1:
            logger.warning(f"Not enough price data for volatility calculation: {len(close_prices)} < {self.lookback_window + 1}")
            return self._default_prediction()

        # Check if we need to update the regime classification
        if symbol in self._regime_cache:
            regime, confidence, last_update = self._regime_cache[symbol]
            time_since_update = (timestamp - last_update).total_seconds() / 60

            if time_since_update < self.update_interval:
                # Return cached regime if it's recent enough
                return {
                    'regime': regime.value,
                    'confidence': confidence,
                    'volatility': self._get_current_volatility(close_prices),
                    'percentile': self._get_volatility_percentile(symbol, close_prices),
                    'action': self._get_action_from_regime(regime),
                    'position_size_multiplier': self._get_position_size_multiplier(regime)
                }

        # Calculate current volatility
        current_vol = self._get_current_volatility(close_prices)

        # Update historical volatility
        if symbol not in self._historical_vol:
            self._historical_vol[symbol] = []

        self._historical_vol[symbol].append(current_vol)

        # Keep only the last 100 volatility values
        if len(self._historical_vol[symbol]) > 100:
            self._historical_vol[symbol] = self._historical_vol[symbol][-100:]

        # Update funding rate history if available
        if funding_rate is not None:
            if symbol not in self._historical_funding:
                self._historical_funding[symbol] = []

            self._historical_funding[symbol].append(funding_rate)

            # Keep only the last 100 funding rate values
            if len(self._historical_funding[symbol]) > 100:
                self._historical_funding[symbol] = self._historical_funding[symbol][-100:]

        # Determine regime
        regime, confidence = self._classify_regime(symbol, current_vol, funding_rate)

        # Cache the result
        self._regime_cache[symbol] = (regime, confidence, timestamp)

        return {
            'regime': regime.value,
            'confidence': confidence,
            'volatility': current_vol,
            'percentile': self._get_volatility_percentile(symbol, close_prices),
            'action': self._get_action_from_regime(regime),
            'position_size_multiplier': self._get_position_size_multiplier(regime)
        }

    def _get_current_volatility(self, prices: List[float]) -> float:
        """
        Calculate current volatility using ATR-like measure.

        Args:
            prices: List of close prices

        Returns:
            Volatility measure
        """
        # Use only the lookback window
        prices = prices[-self.lookback_window-1:]

        # Calculate returns
        returns = np.diff(prices) / prices[:-1]

        # Calculate volatility (annualized standard deviation of returns)
        volatility = np.std(returns) * np.sqrt(365)  # Assuming daily data

        return float(volatility)

    def _get_volatility_percentile(self, symbol: str, prices: List[float]) -> float:
        """
        Calculate the percentile of current volatility relative to history.

        Args:
            symbol: Trading symbol
            prices: List of close prices

        Returns:
            Volatility percentile (0-100)
        """
        if symbol not in self._historical_vol or not self._historical_vol[symbol]:
            return 50.0  # Default to median if no history

        current_vol = self._get_current_volatility(prices)
        historical_vol = self._historical_vol[symbol]

        # Calculate percentile
        percentile = sum(1 for vol in historical_vol if vol < current_vol) / len(historical_vol) * 100

        return percentile

    def _classify_regime(
        self,
        symbol: str,
        volatility: float,
        funding_rate: Optional[float] = None
    ) -> Tuple[VolatilityRegime, float]:
        """
        Classify the current market regime.

        Args:
            symbol: Trading symbol
            volatility: Current volatility measure
            funding_rate: Current funding rate (optional)

        Returns:
            Tuple of (regime, confidence)
        """
        if symbol not in self._historical_vol or len(self._historical_vol[symbol]) < 10:
            # Not enough history to classify confidently
            return VolatilityRegime.NORMAL, 0.5

        historical_vol = self._historical_vol[symbol]

        # Calculate percentiles
        vol_percentile = sum(1 for vol in historical_vol if vol < volatility) / len(historical_vol)

        # Classify regime based on percentiles
        if vol_percentile < self.low_vol_threshold:
            regime = VolatilityRegime.LOW_VOL
            # Confidence increases as we move away from the threshold
            confidence = 1.0 - (vol_percentile / self.low_vol_threshold)
        elif vol_percentile > self.high_vol_threshold:
            regime = VolatilityRegime.HIGH_VOL
            # Confidence increases as we move away from the threshold
            confidence = (vol_percentile - self.high_vol_threshold) / (1.0 - self.high_vol_threshold)
        else:
            regime = VolatilityRegime.NORMAL
            # Confidence is highest in the middle of the normal range
            mid_point = (self.low_vol_threshold + self.high_vol_threshold) / 2
            distance_from_mid = abs(vol_percentile - mid_point)
            range_half_width = (self.high_vol_threshold - self.low_vol_threshold) / 2
            confidence = 1.0 - (distance_from_mid / range_half_width)

        # Adjust confidence based on funding rate if available
        if funding_rate is not None and symbol in self._historical_funding and len(self._historical_funding[symbol]) >= 10:
            funding_history = self._historical_funding[symbol]
            funding_volatility = np.std(funding_history)

            # If funding volatility is high, it might indicate a regime change
            if funding_volatility > np.mean(funding_history) + 2 * np.std(funding_history):
                confidence *= 0.8  # Reduce confidence if funding is volatile

        return regime, min(1.0, max(0.0, confidence))

    def _get_action_from_regime(self, regime: VolatilityRegime) -> str:
        """
        Get trading action recommendation based on regime.

        Args:
            regime: Volatility regime

        Returns:
            Trading action (BUY, SELL, HOLD)
        """
        # This is a simplified approach - in a real system, you'd want to
        # combine this with other signals and market direction
        if regime == VolatilityRegime.LOW_VOL:
            return "HOLD"  # Low volatility often means range-bound markets
        elif regime == VolatilityRegime.NORMAL:
            return "HOLD"  # Normal regime - rely on other signals
        else:  # HIGH_VOL
            return "HOLD"  # High volatility - be cautious

    def _get_position_size_multiplier(self, regime: VolatilityRegime) -> float:
        """
        Get position size multiplier based on regime.

        Args:
            regime: Volatility regime

        Returns:
            Position size multiplier (0.0-1.5)
        """
        if regime == VolatilityRegime.LOW_VOL:
            return 1.2  # Increase position size in low volatility
        elif regime == VolatilityRegime.NORMAL:
            return 1.0  # Normal position size
        else:  # HIGH_VOL
            return 0.5  # Reduce position size in high volatility

    def _default_prediction(self) -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Returns:
            Default prediction dictionary
        """
        return {
            'regime': VolatilityRegime.NORMAL.value,
            'confidence': 0.5,
            'volatility': 0.0,
            'percentile': 50.0,
            'action': "HOLD",
            'position_size_multiplier': 1.0
        }
