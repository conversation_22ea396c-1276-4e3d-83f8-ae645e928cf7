"""
🔥 Production-Grade Circuit Breaker Implementation

Implements fail-fast patterns for exchange APIs, strategy processes, and database connections
to ensure system resilience and automatic recovery in production environments.
"""

import asyncio
import time
import logging
from enum import Enum
from typing import Dict, Any, Callable, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing fast
    HALF_OPEN = "half_open"  # Testing recovery

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior."""
    failure_threshold: int = 5          # Failures before opening
    recovery_timeout: float = 60.0      # Seconds before trying half-open
    success_threshold: int = 3          # Successes needed to close from half-open
    timeout: float = 30.0               # Operation timeout
    exponential_backoff: bool = True    # Use exponential backoff
    max_backoff: float = 300.0          # Maximum backoff time

class CircuitBreaker:
    """
    Production-grade circuit breaker for protecting external dependencies.
    
    Features:
    - Automatic failure detection and recovery
    - Exponential backoff
    - Health monitoring
    - Metrics collection
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        
        # State management
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_success_time = None
        
        # Metrics
        self.total_calls = 0
        self.total_failures = 0
        self.total_successes = 0
        self.total_timeouts = 0
        self.total_circuit_opens = 0
        
        # Backoff tracking
        self.current_backoff = 1.0
        
        logger.info(f"🔧 Circuit breaker '{name}' initialized")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args, **kwargs: Arguments for the function
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenError: When circuit is open
            TimeoutError: When operation times out
        """
        self.total_calls += 1
        
        # Check if circuit should remain open
        if self.state == CircuitState.OPEN:
            if not self._should_attempt_reset():
                raise CircuitBreakerOpenError(
                    f"Circuit breaker '{self.name}' is OPEN. "
                    f"Next attempt in {self._time_until_retry():.1f}s"
                )
            else:
                self.state = CircuitState.HALF_OPEN
                logger.info(f"🔄 Circuit breaker '{self.name}' entering HALF_OPEN state")
        
        try:
            # Execute with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            # Record success
            await self._record_success()
            return result
            
        except asyncio.TimeoutError:
            self.total_timeouts += 1
            await self._record_failure("timeout")
            raise TimeoutError(f"Operation timed out after {self.config.timeout}s")
            
        except Exception as e:
            await self._record_failure(str(e))
            raise
    
    async def _record_success(self):
        """Record a successful operation."""
        self.total_successes += 1
        self.last_success_time = time.time()
        self.success_count += 1
        
        # Reset backoff on success
        self.current_backoff = 1.0
        
        if self.state == CircuitState.HALF_OPEN:
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.success_count = 0
                logger.info(f"✅ Circuit breaker '{self.name}' CLOSED - service recovered")
        elif self.state == CircuitState.CLOSED:
            # Reset failure count on success in closed state
            self.failure_count = 0
    
    async def _record_failure(self, error: str):
        """Record a failed operation."""
        self.total_failures += 1
        self.last_failure_time = time.time()
        self.failure_count += 1
        self.success_count = 0  # Reset success count
        
        logger.warning(f"⚠️ Circuit breaker '{self.name}' recorded failure: {error}")
        
        # Check if we should open the circuit
        if (self.state in [CircuitState.CLOSED, CircuitState.HALF_OPEN] and 
            self.failure_count >= self.config.failure_threshold):
            
            self.state = CircuitState.OPEN
            self.total_circuit_opens += 1
            
            # Apply exponential backoff
            if self.config.exponential_backoff:
                self.current_backoff = min(
                    self.current_backoff * 2,
                    self.config.max_backoff
                )
            
            logger.error(
                f"🚨 Circuit breaker '{self.name}' OPENED after {self.failure_count} failures. "
                f"Will retry in {self.current_backoff:.1f}s"
            )
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if not self.last_failure_time:
            return True
        
        time_since_failure = time.time() - self.last_failure_time
        return time_since_failure >= self.current_backoff
    
    def _time_until_retry(self) -> float:
        """Calculate time until next retry attempt."""
        if not self.last_failure_time:
            return 0.0
        
        time_since_failure = time.time() - self.last_failure_time
        return max(0.0, self.current_backoff - time_since_failure)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get circuit breaker metrics."""
        uptime = time.time() - (self.last_success_time or time.time())
        failure_rate = self.total_failures / max(self.total_calls, 1)
        
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "total_calls": self.total_calls,
            "total_failures": self.total_failures,
            "total_successes": self.total_successes,
            "total_timeouts": self.total_timeouts,
            "total_circuit_opens": self.total_circuit_opens,
            "failure_rate": failure_rate,
            "last_failure_time": self.last_failure_time,
            "last_success_time": self.last_success_time,
            "current_backoff": self.current_backoff,
            "time_until_retry": self._time_until_retry(),
            "uptime": uptime
        }
    
    def reset(self):
        """Manually reset the circuit breaker."""
        logger.info(f"🔄 Manually resetting circuit breaker '{self.name}'")
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.current_backoff = 1.0

class CircuitBreakerOpenError(Exception):
    """Raised when circuit breaker is open."""
    pass

class CircuitBreakerManager:
    """Manages multiple circuit breakers for different services."""
    
    def __init__(self):
        self.breakers: Dict[str, CircuitBreaker] = {}
        logger.info("🔧 Circuit breaker manager initialized")
    
    def get_breaker(self, name: str, config: CircuitBreakerConfig = None) -> CircuitBreaker:
        """Get or create a circuit breaker."""
        if name not in self.breakers:
            self.breakers[name] = CircuitBreaker(name, config)
        return self.breakers[name]
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all circuit breakers."""
        return {name: breaker.get_metrics() for name, breaker in self.breakers.items()}
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health based on circuit breaker states."""
        total_breakers = len(self.breakers)
        if total_breakers == 0:
            return {
                "status": "healthy",
                "message": "No circuit breakers configured",
                "total_breakers": 0
            }
        
        open_breakers = sum(1 for b in self.breakers.values() if b.state == CircuitState.OPEN)
        half_open_breakers = sum(1 for b in self.breakers.values() if b.state == CircuitState.HALF_OPEN)
        
        if open_breakers == 0:
            if half_open_breakers == 0:
                status = "healthy"
                message = "All circuit breakers closed"
            else:
                status = "warning"
                message = f"{half_open_breakers} circuit breakers recovering"
        elif open_breakers < total_breakers:
            status = "degraded"
            message = f"{open_breakers}/{total_breakers} circuit breakers open"
        else:
            status = "critical"
            message = "All circuit breakers open"
        
        return {
            "status": status,
            "message": message,
            "total_breakers": total_breakers,
            "open_breakers": open_breakers,
            "half_open_breakers": half_open_breakers,
            "closed_breakers": total_breakers - open_breakers - half_open_breakers,
            "breaker_details": {name: b.state.value for name, b in self.breakers.items()}
        }
    
    def reset_all(self):
        """Reset all circuit breakers."""
        logger.info("🔄 Resetting all circuit breakers")
        for breaker in self.breakers.values():
            breaker.reset()

# Global circuit breaker manager instance
circuit_manager = CircuitBreakerManager()

# Convenience function for creating circuit breakers with common configurations
def create_exchange_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker optimized for exchange API calls."""
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=30.0,
        success_threshold=2,
        timeout=10.0,
        exponential_backoff=True,
        max_backoff=120.0
    )
    return circuit_manager.get_breaker(f"exchange_{name}", config)

def create_database_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker optimized for database operations."""
    config = CircuitBreakerConfig(
        failure_threshold=5,
        recovery_timeout=15.0,
        success_threshold=3,
        timeout=5.0,
        exponential_backoff=True,
        max_backoff=60.0
    )
    return circuit_manager.get_breaker(f"database_{name}", config)

def create_strategy_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker optimized for strategy processes."""
    config = CircuitBreakerConfig(
        failure_threshold=2,
        recovery_timeout=60.0,
        success_threshold=1,
        timeout=30.0,
        exponential_backoff=True,
        max_backoff=300.0
    )
    return circuit_manager.get_breaker(f"strategy_{name}", config)
