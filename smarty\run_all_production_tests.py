#!/usr/bin/env python3
"""
🚀 Complete Production Readiness Test Runner

Runs all production readiness tests in sequence to validate the entire
Epinnox Smart Trading Dashboard system for production deployment.

Usage:
    python run_all_production_tests.py
"""

import subprocess
import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionTestRunner:
    """Runs all production readiness tests in sequence."""
    
    def __init__(self):
        self.test_results = {
            "timestamp": time.strftime("%Y%m%d_%H%M%S"),
            "total_test_suites": 0,
            "passed_suites": 0,
            "failed_suites": 0,
            "suite_results": {}
        }
        
        # Define test suites in execution order
        self.test_suites = [
            {
                "name": "Strategy Implementation Validation",
                "script": "validate_strategy_implementations.py",
                "description": "Validates all strategy implementations are working",
                "critical": True
            },
            {
                "name": "Strategy Startup Test",
                "script": "test_strategy_startup.py", 
                "description": "Tests individual strategy startup functionality",
                "critical": True
            },
            {
                "name": "Dashboard Startup Verification",
                "script": "verify_dashboard_startup.py",
                "description": "Verifies dashboard can start with new configuration",
                "critical": True
            },
            {
                "name": "Circuit Breaker Test",
                "script": "test_circuit_breaker.py",
                "description": "Tests production-grade circuit breaker implementation",
                "critical": False
            },
            {
                "name": "Dashboard Integration Test",
                "script": "test_dashboard_strategy_integration.py",
                "description": "Tests end-to-end dashboard and strategy integration",
                "critical": False,
                "requires_dashboard": True
            }
        ]
    
    def run_test_suite(self, suite: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test suite."""
        logger.info(f"🧪 Running: {suite['name']}")
        logger.info(f"   Description: {suite['description']}")
        logger.info(f"   Script: {suite['script']}")
        
        self.test_results["total_test_suites"] += 1
        
        # Check if script exists
        script_path = Path(suite["script"])
        if not script_path.exists():
            result = {
                "status": "failed",
                "message": f"Test script not found: {suite['script']}",
                "return_code": -1,
                "output": "",
                "error": f"File not found: {script_path}"
            }
            logger.error(f"   ❌ {suite['name']}: Script not found")
            return result
        
        try:
            # Run the test script
            start_time = time.time()
            process = subprocess.run(
                [sys.executable, suite["script"]],
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout per test
            )
            execution_time = time.time() - start_time
            
            # Determine result
            if process.returncode == 0:
                result = {
                    "status": "passed",
                    "message": f"Test suite completed successfully",
                    "return_code": process.returncode,
                    "output": process.stdout,
                    "error": process.stderr,
                    "execution_time": execution_time
                }
                logger.info(f"   ✅ {suite['name']}: PASSED ({execution_time:.1f}s)")
                self.test_results["passed_suites"] += 1
            else:
                result = {
                    "status": "failed",
                    "message": f"Test suite failed with return code {process.returncode}",
                    "return_code": process.returncode,
                    "output": process.stdout,
                    "error": process.stderr,
                    "execution_time": execution_time
                }
                logger.error(f"   ❌ {suite['name']}: FAILED ({execution_time:.1f}s)")
                logger.error(f"      Return code: {process.returncode}")
                if process.stderr:
                    logger.error(f"      Error: {process.stderr[:200]}...")
                self.test_results["failed_suites"] += 1
            
            return result
            
        except subprocess.TimeoutExpired:
            result = {
                "status": "failed",
                "message": "Test suite timed out after 120 seconds",
                "return_code": -1,
                "output": "",
                "error": "Timeout",
                "execution_time": 120.0
            }
            logger.error(f"   ❌ {suite['name']}: TIMEOUT")
            self.test_results["failed_suites"] += 1
            return result
            
        except Exception as e:
            result = {
                "status": "failed",
                "message": f"Test suite execution failed: {e}",
                "return_code": -1,
                "output": "",
                "error": str(e),
                "execution_time": 0.0
            }
            logger.error(f"   ❌ {suite['name']}: EXCEPTION - {e}")
            self.test_results["failed_suites"] += 1
            return result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all production readiness tests."""
        logger.info("🚀 Complete Production Readiness Test Suite")
        logger.info("=" * 60)
        logger.info(f"Running {len(self.test_suites)} test suites...")
        logger.info("")
        
        # Run each test suite
        for i, suite in enumerate(self.test_suites, 1):
            logger.info(f"📋 Test Suite {i}/{len(self.test_suites)}")
            
            # Skip tests that require dashboard if we're not running it
            if suite.get("requires_dashboard", False):
                logger.info(f"   ⚠️ Skipping {suite['name']} (requires manual dashboard startup)")
                continue
            
            result = self.run_test_suite(suite)
            self.test_results["suite_results"][suite["name"]] = result
            
            # Check if critical test failed
            if suite.get("critical", False) and result["status"] == "failed":
                logger.error(f"   🚨 CRITICAL TEST FAILED: {suite['name']}")
                logger.error("   Stopping test execution due to critical failure")
                break
            
            logger.info("")  # Add spacing between tests
        
        return self.test_results
    
    def print_summary(self):
        """Print comprehensive test results summary."""
        logger.info("📊 Production Readiness Test Summary")
        logger.info("=" * 60)
        
        # Overall statistics
        total = self.test_results["total_test_suites"]
        passed = self.test_results["passed_suites"]
        failed = self.test_results["failed_suites"]
        
        logger.info(f"Total Test Suites: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        
        if total > 0:
            success_rate = passed / total
            logger.info(f"Success Rate: {success_rate:.1%}")
        
        # Detailed results
        logger.info("\n📋 Detailed Results:")
        for suite_name, result in self.test_results["suite_results"].items():
            status_icon = "✅" if result["status"] == "passed" else "❌"
            execution_time = result.get("execution_time", 0)
            logger.info(f"   {status_icon} {suite_name}: {result['status'].upper()} ({execution_time:.1f}s)")
            if result["status"] == "failed":
                logger.info(f"      Error: {result['message']}")
        
        # Overall assessment
        logger.info("\n🎯 Production Readiness Assessment:")
        
        critical_failures = 0
        for suite in self.test_suites:
            if suite.get("critical", False):
                result = self.test_results["suite_results"].get(suite["name"])
                if result and result["status"] == "failed":
                    critical_failures += 1
        
        if critical_failures == 0 and failed == 0:
            logger.info("🎉 ALL TESTS PASSED - SYSTEM IS PRODUCTION READY!")
            logger.info("✅ The Epinnox Smart Trading Dashboard is ready for live deployment")
        elif critical_failures == 0:
            logger.info("✅ CRITICAL TESTS PASSED - SYSTEM IS PRODUCTION READY")
            logger.info("⚠️ Some non-critical tests failed - review recommended")
        else:
            logger.error("🚨 CRITICAL TESTS FAILED - SYSTEM NOT READY FOR PRODUCTION")
            logger.error("❌ Fix critical issues before deployment")
        
        # Save results to file
        import json
        results_file = f"production_test_results_{self.test_results['timestamp']}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        logger.info(f"\n📄 Detailed results saved to: {results_file}")
    
    def get_exit_code(self) -> int:
        """Get appropriate exit code based on test results."""
        # Check for critical failures
        critical_failures = 0
        for suite in self.test_suites:
            if suite.get("critical", False):
                result = self.test_results["suite_results"].get(suite["name"])
                if result and result["status"] == "failed":
                    critical_failures += 1
        
        if critical_failures > 0:
            return 1  # Critical failure
        elif self.test_results["failed_suites"] > 0:
            return 2  # Non-critical failures
        else:
            return 0  # All tests passed

def main():
    """Main test runner."""
    logger.info("🚀 Starting Complete Production Readiness Test Suite")
    logger.info("⚠️ This will run all production validation tests")
    logger.info("")
    
    runner = ProductionTestRunner()
    results = runner.run_all_tests()
    runner.print_summary()
    
    return runner.get_exit_code()

if __name__ == "__main__":
    sys.exit(main())
