/* Money Circle Club Dashboard Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #FFD700;
    --primary-hover: #FFA000;
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --card-bg: rgba(0, 0, 0, 0.3);
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --border-color: rgba(255, 255, 255, 0.1);
    --input-bg: rgba(255, 255, 255, 0.05);
    --success-color: #4CAF50;
    --error-color: #f44336;
    --warning-color: #FF9800;
}

/* Club-specific styles - inherits global body from design_system.css */

/* Club Dashboard Layout - Consistent with main dashboard */
.club-dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    color: #ffffff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Club Dashboard Grid Layout - Responsive */
.club-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-gap, 20px);
    margin-bottom: 30px;
}

/* Responsive grid layout */
@media (min-width: 768px) {
    .club-dashboard-grid {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

@media (min-width: 1200px) {
    .club-dashboard-grid {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 30px;
    }
}

/* Club Header */
.club-header {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.club-branding h1 {
    color: #FFD700;
    margin: 0;
    font-size: 2em;
    font-weight: 700;
}

.club-branding p {
    color: #e2e8f0;
    margin: 5px 0 0 0;
    font-size: 0.9em;
    opacity: 0.8;
}

.club-nav {
    display: flex;
    align-items: center;
    gap: 30px;
}

.club-nav nav {
    display: flex;
    gap: 20px;
}

.nav-link {
    color: #e2e8f0;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: rgba(255, 215, 0, 0.1);
    color: #FFD700;
}

.nav-link.active {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #e2e8f0;
}

.logout-btn {
    color: #ff6b6b;
    text-decoration: none;
    padding: 6px 12px;
    border: 1px solid #ff6b6b;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #ff6b6b;
    color: white;
}

/* Club Overview Stats - Consistent with dashboard styling */
.club-overview {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.club-overview h2 {
    color: var(--warning-400, #FFD700);
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.overview-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 215, 0, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.overview-card h3 {
    color: #e2e8f0;
    margin: 0 0 10px 0;
    font-size: 0.9em;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.overview-card .value {
    color: #FFD700;
    font-size: 2em;
    font-weight: 700;
    margin-bottom: 5px;
}

.overview-card .value.positive {
    color: #4CAF50;
}

.overview-card .value.negative {
    color: #f44336;
}

.stat-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 215, 0, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    color: #e2e8f0;
    margin: 0 0 8px 0;
    font-size: 0.9em;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: #FFD700;
    font-size: 2.2em;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-change {
    color: #4CAF50;
    font-size: 0.8em;
    opacity: 0.8;
}

/* Main Content Layout */
.club-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr 350px;
    gap: 30px;
    padding: 0 30px 30px;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    color: #FFD700;
    margin: 0;
    font-size: 1.4em;
}

.section-header h3 {
    color: #e2e8f0;
    margin: 0 0 15px 0;
    font-size: 1.1em;
}

/* Section Cards - Consistent with dashboard styling */
.strategy-governance,
.recent-activity,
.performance-charts,
.top-performers,
.notifications,
.strategy-proposals,
.activity-feed,
.member-leaderboard,
.quick-actions {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.strategy-governance h2,
.recent-activity h2,
.performance-charts h2,
.top-performers h2,
.notifications h2 {
    color: var(--warning-400, #FFD700);
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
}

.pending-strategy-card,
.voting-strategy-card,
.approved-strategy-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pending-strategy-card:hover,
.voting-strategy-card:hover,
.approved-strategy-card:hover {
    border-color: rgba(255, 215, 0, 0.3);
    transform: translateY(-1px);
}

.strategy-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.strategy-header h4 {
    color: #ffffff;
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
}

.strategy-type {
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
}

.strategy-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.strategy-meta span {
    font-size: 0.85em;
    color: #cbd5e0;
}

.risk-level {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.risk-low { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.risk-medium { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
.risk-high { background: rgba(244, 67, 54, 0.2); color: #f44336; }

.strategy-description {
    color: #e2e8f0;
    font-size: 0.9em;
    line-height: 1.4;
    margin-bottom: 15px;
}

/* Action Buttons */
.admin-actions,
.voting-actions,
.strategy-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-approve,
.btn-reject,
.btn-changes,
.vote-btn,
.btn-follow,
.btn-view,
.btn-primary,
.btn-secondary {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-approve,
.vote-btn.approve {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.btn-reject,
.vote-btn.reject {
    background: linear-gradient(135deg, #f44336, #da190b);
    color: white;
}

.btn-changes,
.vote-btn.abstain {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.btn-follow,
.btn-primary {
    background: linear-gradient(135deg, #FFD700, #FFA000);
    color: #000;
}

.btn-view,
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-approve:hover,
.btn-reject:hover,
.btn-changes:hover,
.vote-btn:hover,
.btn-follow:hover,
.btn-view:hover,
.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Activity Feed */
.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FFD700, #FFA000);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #000;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: #e2e8f0;
    font-size: 0.9em;
    line-height: 1.4;
    margin-bottom: 5px;
}

.activity-time {
    color: #94a3b8;
    font-size: 0.8em;
}

.activity-type-icon {
    font-size: 1.2em;
    opacity: 0.6;
}

/* Feed Filters */
.feed-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: #e2e8f0;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
    color: #FFD700;
}

/* Leaderboard */
.leaderboard-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.leaderboard-item.gold .rank {
    color: #FFD700;
    font-weight: 700;
}

.leaderboard-item.silver .rank {
    color: #C0C0C0;
    font-weight: 700;
}

.leaderboard-item.bronze .rank {
    color: #CD7F32;
    font-weight: 700;
}

.rank {
    font-weight: 600;
    color: #e2e8f0;
    min-width: 30px;
}

.member-info {
    flex: 1;
}

.member-name {
    color: #ffffff;
    font-weight: 500;
    margin-bottom: 3px;
}

.member-stats {
    display: flex;
    gap: 10px;
    font-size: 0.8em;
    color: #94a3b8;
}

.member-score {
    color: #FFD700;
    font-weight: 600;
}

/* Quick Actions */
.action-buttons {
    display: grid;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
    color: #FFD700;
    transform: translateY(-1px);
}

.action-icon {
    font-size: 1.2em;
}

/* Utilities */
.empty-state {
    text-align: center;
    color: #94a3b8;
    font-style: italic;
    padding: 20px;
}

.view-all-link {
    color: #FFD700;
    text-decoration: none;
    font-size: 0.9em;
    font-weight: 500;
    display: inline-block;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.view-all-link:hover {
    color: #FFA000;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .club-main-content {
        grid-template-columns: 1fr 1fr;
    }

    .right-column {
        grid-column: 1 / -1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .club-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .club-nav {
        flex-direction: column;
        gap: 15px;
    }

    .club-nav nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .club-main-content {
        grid-template-columns: 1fr;
        padding: 0 15px 15px;
    }

    .right-column {
        grid-template-columns: 1fr;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 20px;
    }
}
