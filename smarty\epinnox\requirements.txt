# Epinnox Trading System Requirements

# Core dependencies
numpy>=1.21.0
pandas>=1.3.0
pyyaml>=6.0

# CCXT for exchange integration
ccxt>=4.0.0

# Optional dependencies for enhanced features
redis>=4.0.0  # For Redis caching
aioredis>=2.0.0  # Async Redis client

# Data analysis and visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine learning (optional)
scikit-learn>=1.0.0
scipy>=1.7.0

# Web framework (optional, for API)
fastapi>=0.70.0
uvicorn>=0.15.0

# Database support (optional)
sqlalchemy>=1.4.0
aiosqlite>=0.17.0

# Testing
pytest>=6.0.0
pytest-asyncio>=0.18.0

# Development tools
black>=22.0.0
flake8>=4.0.0
mypy>=0.910
