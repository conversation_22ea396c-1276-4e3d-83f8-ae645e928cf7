#!/bin/bash
# Production Environment Setup for Money Circle
# Critical environment configuration for production deployment

set -e

echo "🌐 Setting up Money Circle production environment"

# Create production environment file
create_production_env() {
    echo "📝 Creating production environment configuration..."
    
    # Generate secure JWT secret
    JWT_SECRET=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    
    # Create .env.production file
    cat > .env.production << EOF
# Money Circle Production Environment Configuration
# Generated on $(date)

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8086
WORKERS=4
MAX_REQUEST_SIZE=16777216
REQUEST_TIMEOUT=60

# Security Configuration
JWT_SECRET=${JWT_SECRET}
SESSION_TIMEOUT=7200
HTTPS_ONLY=true
SECURE_COOKIES=true
CSRF_PROTECTION=true

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# Database Configuration
DATABASE_PATH=data/money_circle.db
BUS_DATABASE_PATH=data/bus.db
DATABASE_TIMEOUT=30
DATABASE_MAX_CONNECTIONS=100
DATABASE_WAL_MODE=true

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=ssl/money_circle.crt
SSL_KEY_PATH=ssl/money_circle.key

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=1800
BACKUP_DIR=backups
MAX_BACKUPS=30
BACKUP_COMPRESSION=true

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_INTERVAL=60
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true

# Trading Configuration
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
MAX_POSITION_SIZE=1000.0
RISK_LIMIT_PERCENT=2.0

# Exchange Configuration
BINANCE_ENABLED=true
HTX_ENABLED=true
BYBIT_ENABLED=true
EXCHANGE_TIMEOUT=30
EXCHANGE_RETRY_ATTEMPTS=3

# Logging Configuration
LOG_FILE=logs/money_circle_production.log
MAX_LOG_SIZE=104857600
LOG_BACKUP_COUNT=5
AUDIT_LOG_ENABLED=true

# External Services (configure as needed)
# SMTP_HOST=smtp.yourdomain.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_smtp_password
# SMTP_TLS=true

# Monitoring Integration (configure as needed)
# PROMETHEUS_ENABLED=false
# PROMETHEUS_PORT=9090
# GRAFANA_ENABLED=false
# ELASTICSEARCH_URL=http://localhost:9200

# Cloud Storage (configure as needed)
# AWS_ACCESS_KEY_ID=your_aws_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret
# AWS_S3_BUCKET=money-circle-backups
# AWS_REGION=us-east-1
EOF

    echo "✅ Production environment file created: .env.production"
}

# Create systemd service file
create_systemd_service() {
    echo "🔧 Creating systemd service for Money Circle..."
    
    CURRENT_USER=$(whoami)
    CURRENT_DIR=$(pwd)
    
    sudo tee /etc/systemd/system/money-circle.service > /dev/null << EOF
[Unit]
Description=Money Circle Investment Club Platform
After=network.target
Wants=network.target

[Service]
Type=simple
User=${CURRENT_USER}
Group=${CURRENT_USER}
WorkingDirectory=${CURRENT_DIR}
Environment=PATH=${CURRENT_DIR}/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=${CURRENT_DIR}/.env.production
ExecStart=${CURRENT_DIR}/venv/bin/python app.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=money-circle

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=${CURRENT_DIR}

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    echo "✅ Systemd service created: /etc/systemd/system/money-circle.service"
}

# Create nginx configuration
create_nginx_config() {
    echo "🌐 Creating nginx reverse proxy configuration..."
    
    read -p "Enter your domain name (e.g., money-circle.yourdomain.com): " DOMAIN
    
    sudo tee /etc/nginx/sites-available/money-circle << EOF
# Money Circle Investment Club Platform
# Nginx reverse proxy configuration

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name ${DOMAIN};
    return 301 https://\$server_name\$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name ${DOMAIN};

    # SSL Configuration
    ssl_certificate ${PWD}/ssl/money_circle.crt;
    ssl_certificate_key ${PWD}/ssl/money_circle.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;

    # Static files
    location /static/ {
        alias ${PWD}/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://127.0.0.1:8086;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Login rate limiting
    location /login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://127.0.0.1:8086;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://127.0.0.1:8086;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Health check (no rate limiting)
    location /health {
        proxy_pass http://127.0.0.1:8086;
        proxy_set_header Host \$host;
        access_log off;
    }

    # Main application
    location / {
        proxy_pass http://127.0.0.1:8086;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
EOF

    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/money-circle /etc/nginx/sites-enabled/
    
    echo "✅ Nginx configuration created and enabled"
}

# Create firewall rules
setup_firewall() {
    echo "🔥 Setting up firewall rules..."
    
    # Enable UFW if not already enabled
    sudo ufw --force enable
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow application port (if direct access needed)
    sudo ufw allow 8086/tcp
    
    # Deny all other incoming traffic
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    echo "✅ Firewall rules configured"
    sudo ufw status
}

# Create production directories
create_directories() {
    echo "📁 Creating production directories..."
    
    mkdir -p data
    mkdir -p logs
    mkdir -p backups
    mkdir -p ssl
    mkdir -p static
    mkdir -p uploads
    
    # Set proper permissions
    chmod 755 data logs backups static uploads
    chmod 700 ssl
    
    echo "✅ Production directories created"
}

# Install system dependencies
install_dependencies() {
    echo "📦 Installing system dependencies..."
    
    # Update package list
    sudo apt-get update
    
    # Install required packages
    sudo apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        nginx \
        ufw \
        openssl \
        curl \
        htop \
        supervisor \
        logrotate
    
    # Install Python dependencies in virtual environment
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    
    echo "✅ System dependencies installed"
}

# Main execution
main() {
    echo "🚀 Starting Money Circle production environment setup..."
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        echo "❌ Do not run this script as root"
        exit 1
    fi
    
    # Create directories first
    create_directories
    
    # Install dependencies
    install_dependencies
    
    # Create environment configuration
    create_production_env
    
    # Create systemd service
    create_systemd_service
    
    # Setup firewall
    setup_firewall
    
    # Create nginx configuration
    create_nginx_config
    
    echo ""
    echo "✅ Production environment setup complete!"
    echo ""
    echo "🔧 NEXT STEPS:"
    echo "   1. Run SSL setup: ./deployment/ssl_setup.sh"
    echo "   2. Test nginx config: sudo nginx -t"
    echo "   3. Reload nginx: sudo systemctl reload nginx"
    echo "   4. Enable Money Circle service: sudo systemctl enable money-circle"
    echo "   5. Start Money Circle service: sudo systemctl start money-circle"
    echo "   6. Check service status: sudo systemctl status money-circle"
    echo ""
    echo "📊 MONITORING:"
    echo "   - Service logs: sudo journalctl -u money-circle -f"
    echo "   - Application logs: tail -f logs/money_circle_production.log"
    echo "   - Nginx logs: sudo tail -f /var/log/nginx/access.log"
    echo ""
    echo "🔒 SECURITY:"
    echo "   - Firewall status: sudo ufw status"
    echo "   - SSL test: openssl s_client -connect yourdomain.com:443"
    echo "   - Security headers: curl -I https://yourdomain.com"
}

# Run main function
main
