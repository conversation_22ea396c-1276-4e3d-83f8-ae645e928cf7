#!/usr/bin/env python3
"""
Live Trading Dashboard Startup Script

Comprehensive startup script that:
1. Starts the enhanced dashboard with strategy management
2. Ensures data producer is running
3. Provides live monitoring and trading capabilities
4. Integrates the 3 working strategies with live data
"""

import asyncio
import logging
import subprocess
import time
import signal
import sys
from pathlib import Path
from typing import Dict, List, Optional
import psutil

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveTradingDashboardManager:
    """Manages the complete live trading dashboard system."""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
        # Core components
        self.components = {
            "data_producer": {
                "command": "python feeds/htx_data_producer.py",
                "description": "HTX market data producer",
                "required": True,
                "startup_delay": 2
            },
            "dashboard": {
                "command": "python live_dashboard.py",
                "description": "Enhanced live trading dashboard",
                "required": True,
                "startup_delay": 5
            }
        }
        
        # Available strategies (managed through dashboard)
        self.available_strategies = {
            "smart_model_integrated": {
                "name": "Smart Model Integrated",
                "command": "python orchestrator.py --debug",
                "description": "Full orchestrator with LLM integration"
            },
            "smart_strategy_only": {
                "name": "Smart Strategy Only", 
                "command": "python run_smart_strategy_live.py",
                "description": "Lightweight smart strategy"
            },
            "order_flow": {
                "name": "Order Flow Analysis",
                "command": "python live_dataframe_strategy_runner.py",
                "description": "DataFrame-based order flow analysis"
            }
        }
    
    def check_prerequisites(self) -> bool:
        """Check if all required files and dependencies exist."""
        logger.info("🔍 Checking prerequisites...")
        
        required_files = [
            "live_dashboard.py",
            "feeds/htx_data_producer.py",
            "orchestrator.py",
            "run_smart_strategy_live.py",
            "live_dataframe_strategy_runner.py",
            "trading_config_live.yaml",
            "config.yaml"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.error(f"❌ Missing required files: {missing_files}")
            return False
        
        # Check data directory
        data_dir = Path("data")
        if not data_dir.exists():
            logger.info("📁 Creating data directory...")
            data_dir.mkdir(exist_ok=True)
        
        # Check logs directory
        logs_dir = Path("logs")
        if not logs_dir.exists():
            logger.info("📁 Creating logs directory...")
            logs_dir.mkdir(exist_ok=True)
        
        logger.info("✅ Prerequisites check passed")
        return True
    
    def start_component(self, component_name: str) -> bool:
        """Start a specific component."""
        if component_name not in self.components:
            logger.error(f"❌ Unknown component: {component_name}")
            return False
        
        component = self.components[component_name]
        
        try:
            logger.info(f"🚀 Starting {component['description']}...")
            
            process = subprocess.Popen(
                component["command"].split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for startup
            time.sleep(component.get("startup_delay", 2))
            
            if process.poll() is None:
                self.processes[component_name] = process
                logger.info(f"✅ {component['description']} started (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {component['description']} failed to start")
                if stderr:
                    logger.error(f"   Error: {stderr[:200]}...")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to start {component['description']}: {e}")
            return False
    
    def stop_component(self, component_name: str) -> bool:
        """Stop a specific component."""
        if component_name not in self.processes:
            logger.warning(f"⚠️ Component {component_name} not running")
            return True
        
        try:
            process = self.processes[component_name]
            component = self.components[component_name]
            
            logger.info(f"🛑 Stopping {component['description']}...")
            process.terminate()
            
            try:
                process.wait(timeout=10)
                logger.info(f"✅ {component['description']} stopped gracefully")
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️ Force killing {component['description']}...")
                process.kill()
                process.wait()
                logger.info(f"✅ {component['description']} force stopped")
            
            del self.processes[component_name]
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to stop {component_name}: {e}")
            return False
    
    def check_component_health(self) -> Dict[str, bool]:
        """Check health of all running components."""
        health = {}
        
        for component_name, process in self.processes.items():
            if process.poll() is None:
                health[component_name] = True
            else:
                health[component_name] = False
                logger.warning(f"⚠️ Component {component_name} has stopped unexpectedly")
        
        return health
    
    def display_status(self):
        """Display current system status."""
        logger.info("\n" + "="*60)
        logger.info("🎯 LIVE TRADING DASHBOARD STATUS")
        logger.info("="*60)
        
        # Component status
        logger.info("\n📊 Core Components:")
        for component_name, component in self.components.items():
            if component_name in self.processes:
                process = self.processes[component_name]
                if process.poll() is None:
                    status = f"✅ RUNNING (PID: {process.pid})"
                else:
                    status = "❌ STOPPED"
            else:
                status = "⚪ NOT STARTED"
            
            logger.info(f"  {component['description']}: {status}")
        
        # Available strategies
        logger.info("\n🎯 Available Strategies (managed through dashboard):")
        for strategy_id, strategy in self.available_strategies.items():
            logger.info(f"  {strategy['name']}: {strategy['description']}")
        
        # Access information
        logger.info("\n🌐 Access Information:")
        logger.info("  Dashboard URL: http://localhost:8082")
        logger.info("  Login: epinnox / securepass123")
        
        logger.info("\n💡 Usage Instructions:")
        logger.info("  1. Open http://localhost:8082 in your browser")
        logger.info("  2. Login with Epinnox credentials")
        logger.info("  3. Select and start strategies from the dashboard")
        logger.info("  4. Monitor live data and trading signals")
        logger.info("  5. Configure trading parameters as needed")
        
        logger.info("="*60)
    
    async def start_system(self):
        """Start the complete live trading system."""
        logger.info("🎯 STARTING LIVE TRADING DASHBOARD SYSTEM")
        logger.info("="*60)
        
        # Check prerequisites
        if not self.check_prerequisites():
            logger.error("❌ Prerequisites check failed. Aborting startup.")
            return False
        
        # Start core components in order
        startup_order = ["data_producer", "dashboard"]
        
        for component_name in startup_order:
            if not self.start_component(component_name):
                logger.error(f"❌ Failed to start {component_name}. Aborting startup.")
                await self.stop_system()
                return False
        
        self.running = True
        
        # Display status
        self.display_status()
        
        # Monitor system health
        await self.monitor_system()
        
        return True
    
    async def monitor_system(self):
        """Monitor system health and restart components if needed."""
        logger.info("📡 Starting system health monitoring...")
        
        try:
            while self.running:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                health = self.check_component_health()
                
                # Restart failed components
                for component_name, is_healthy in health.items():
                    if not is_healthy and self.components[component_name].get("required", False):
                        logger.warning(f"🔄 Restarting failed component: {component_name}")
                        self.start_component(component_name)
                
        except asyncio.CancelledError:
            logger.info("📡 Health monitoring stopped")
    
    async def stop_system(self):
        """Stop the complete system."""
        logger.info("🛑 Stopping live trading dashboard system...")
        self.running = False
        
        # Stop all components
        for component_name in list(self.processes.keys()):
            self.stop_component(component_name)
        
        logger.info("✅ System stopped successfully")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}. Initiating graceful shutdown...")
        self.running = False


async def main():
    """Main entry point."""
    manager = LiveTradingDashboardManager()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        success = await manager.start_system()
        if not success:
            logger.error("❌ Failed to start system")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("🛑 Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        await manager.stop_system()


if __name__ == "__main__":
    print("🎯 Live Trading Dashboard Manager")
    print("=" * 50)
    print("Starting comprehensive live trading system...")
    print("This will start:")
    print("  • HTX market data producer")
    print("  • Enhanced live dashboard")
    print("  • Strategy management interface")
    print("  • Live monitoring and alerts")
    print("=" * 50)
    
    asyncio.run(main())
