#!/usr/bin/env python3
"""
CSRF Token Extractor for Money Circle
Shows how to get CSRF tokens programmatically for API testing
"""

import asyncio
import aiohttp
import re
import logging
import sys
import platform

# Fix for Windows event loop issue
if platform.system() == 'Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def get_csrf_token_from_login():
    """Get CSRF token from login page."""
    base_url = "http://localhost:8086"

    try:
        async with aiohttp.ClientSession() as session:
            # Get login page
            async with session.get(f"{base_url}/login") as resp:
                if resp.status == 200:
                    content = await resp.text()

                    # Extract CSRF token using regex
                    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', content)
                    if csrf_match:
                        csrf_token = csrf_match.group(1)
                        return csrf_token
                    else:
                        logger.error("CSRF token not found in login page")
                        return None
                else:
                    logger.error(f"Failed to get login page: {resp.status}")
                    return None
    except Exception as e:
        logger.error(f"Error getting CSRF token: {e}")
        return None

async def test_login_with_csrf():
    """Test login using extracted CSRF token."""
    logger.info("🔐 Getting CSRF Token from Money Circle")
    logger.info("=" * 40)

    # Get CSRF token
    csrf_token = await get_csrf_token_from_login()
    if not csrf_token:
        logger.error("❌ Could not get CSRF token")
        return False

    logger.info(f"✅ CSRF Token: {csrf_token}")
    logger.info(f"   Length: {len(csrf_token)} characters")
    logger.info(f"   First 16 chars: {csrf_token[:16]}...")

    # Test login with the token
    logger.info("\n🧪 Testing login with CSRF token...")

    base_url = "http://localhost:8086"

    try:
        async with aiohttp.ClientSession() as session:
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123',
                'csrf_token': csrf_token
            }

            async with session.post(f"{base_url}/login", data=login_data, allow_redirects=False) as resp:
                logger.info(f"Login response status: {resp.status}")

                if resp.status == 302:
                    location = resp.headers.get('Location', '')
                    logger.info(f"Redirected to: {location}")

                    if '/dashboard' in location:
                        logger.info("✅ Login successful!")
                        return True
                    else:
                        logger.warning("⚠️ Login redirected but not to dashboard")
                        return False
                else:
                    content = await resp.text()
                    logger.error(f"❌ Login failed: {content}")
                    return False

    except Exception as e:
        logger.error(f"❌ Login test error: {e}")
        return False

async def show_csrf_usage_examples():
    """Show different ways to use CSRF tokens."""
    logger.info("\n📚 CSRF TOKEN USAGE EXAMPLES")
    logger.info("=" * 40)

    csrf_token = await get_csrf_token_from_login()
    if not csrf_token:
        logger.error("❌ Could not get CSRF token for examples")
        return

    logger.info("🌐 1. WEB BROWSER (Automatic):")
    logger.info("   - Just visit http://localhost:8086/login")
    logger.info("   - Token is automatically included in the form")
    logger.info("   - No manual action needed!")

    logger.info("\n🐍 2. PYTHON REQUESTS:")
    logger.info("   ```python")
    logger.info("   import requests")
    logger.info("   ")
    logger.info("   # Get CSRF token")
    logger.info("   resp = requests.get('http://localhost:8086/login')")
    logger.info("   # Extract token from HTML...")
    logger.info("   ")
    logger.info("   # Use token in login")
    logger.info("   data = {")
    logger.info("       'username': 'epinnox',")
    logger.info("       'password': 'securepass123',")
    logger.info(f"       'csrf_token': '{csrf_token[:16]}...'")
    logger.info("   }")
    logger.info("   requests.post('http://localhost:8086/login', data=data)")
    logger.info("   ```")

    logger.info("\n🌐 3. CURL COMMAND:")
    logger.info("   ```bash")
    logger.info("   # First get the token from login page")
    logger.info("   curl http://localhost:8086/login | grep csrf_token")
    logger.info("   ")
    logger.info("   # Then use it in login")
    logger.info("   curl -X POST http://localhost:8086/login \\")
    logger.info("        -d 'username=epinnox' \\")
    logger.info("        -d 'password=securepass123' \\")
    logger.info(f"        -d 'csrf_token={csrf_token[:16]}...'")
    logger.info("   ```")

    logger.info("\n📱 4. JAVASCRIPT/FETCH:")
    logger.info("   ```javascript")
    logger.info("   // Get token from page")
    logger.info("   const token = document.querySelector('[name=csrf_token]').value;")
    logger.info("   ")
    logger.info("   // Use in fetch request")
    logger.info("   fetch('/login', {")
    logger.info("       method: 'POST',")
    logger.info("       headers: {'Content-Type': 'application/x-www-form-urlencoded'},")
    logger.info(f"       body: 'username=epinnox&password=securepass123&csrf_token=' + token")
    logger.info("   });")
    logger.info("   ```")

async def main():
    """Main function."""
    print("🔐 Money Circle CSRF Token Guide")
    print("How to get and use CSRF tokens")
    print()

    try:
        # Test getting and using CSRF token
        success = await test_login_with_csrf()

        # Show usage examples
        await show_csrf_usage_examples()

        print("\n" + "=" * 50)
        print("📋 SUMMARY:")
        print("✅ CSRF tokens are automatically generated")
        print("✅ Web browsers handle them automatically")
        print("✅ API clients need to extract them first")
        print("✅ Tokens are required for all POST requests")

        if success:
            print("\n🎉 CSRF TOKEN SYSTEM WORKING PERFECTLY!")
        else:
            print("\n⚠️ CSRF token system needs attention")

        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n🛑 Interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == '__main__':
    exit(asyncio.run(main()))
