#!/usr/bin/env python3
"""
Legacy LLM Cleanup Script.

This script helps clean up the old LLM files after successful migration
to the Enhanced LLM system. Run this ONLY after confirming the enhanced
system is working correctly.

IMPORTANT: This script will move files to a backup directory, not delete them.
"""

import os
import shutil
from datetime import datetime
from pathlib import Path


def create_backup_directory():
    """Create a backup directory for legacy files."""
    backup_dir = Path("legacy_llm_backup")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = backup_dir / f"backup_{timestamp}"
    backup_path.mkdir(parents=True, exist_ok=True)
    return backup_path


def backup_file(file_path: str, backup_dir: Path):
    """Backup a file to the backup directory."""
    if os.path.exists(file_path):
        file_name = os.path.basename(file_path)
        backup_file_path = backup_dir / file_name
        shutil.copy2(file_path, backup_file_path)
        print(f"✅ Backed up: {file_path} -> {backup_file_path}")
        return True
    else:
        print(f"⚠️  File not found: {file_path}")
        return False


def main():
    """Main cleanup function."""
    print("🧹 Legacy LLM Cleanup Script")
    print("=" * 40)
    
    # Confirm with user
    print("\n⚠️  WARNING: This script will backup and remove legacy LLM files.")
    print("Make sure the Enhanced LLM system is working correctly before proceeding.")
    
    response = input("\nDo you want to continue? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Cleanup cancelled.")
        return
    
    # Create backup directory
    backup_dir = create_backup_directory()
    print(f"\n📁 Created backup directory: {backup_dir}")
    
    # List of legacy files to backup and remove
    legacy_files = [
        "llm_consumer.py",
        "phi_llm_consumer.py",
        # Add other legacy LLM files here if needed
    ]
    
    print(f"\n🔄 Backing up {len(legacy_files)} legacy files...")
    
    backed_up_files = []
    for file_path in legacy_files:
        if backup_file(file_path, backup_dir):
            backed_up_files.append(file_path)
    
    print(f"\n✅ Successfully backed up {len(backed_up_files)} files")
    
    # Ask if user wants to remove the original files
    if backed_up_files:
        print("\n🗑️  Do you want to remove the original legacy files?")
        print("(They are safely backed up in the backup directory)")
        
        remove_response = input("Remove original files? (yes/no): ").lower().strip()
        if remove_response in ['yes', 'y']:
            print("\n🗑️  Removing original legacy files...")
            for file_path in backed_up_files:
                try:
                    os.remove(file_path)
                    print(f"✅ Removed: {file_path}")
                except Exception as e:
                    print(f"❌ Error removing {file_path}: {e}")
        else:
            print("⚠️  Original files kept. You can remove them manually later.")
    
    # Summary
    print(f"\n📊 CLEANUP SUMMARY")
    print("=" * 30)
    print(f"✅ Files backed up: {len(backed_up_files)}")
    print(f"📁 Backup location: {backup_dir}")
    print(f"🎯 Enhanced LLM system is now the only LLM system")
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Test the enhanced LLM system thoroughly")
    print("2. If everything works, you can delete the backup directory")
    print("3. Update any documentation to reference the new system")
    
    print(f"\n✅ Legacy LLM cleanup completed!")


if __name__ == "__main__":
    main()
