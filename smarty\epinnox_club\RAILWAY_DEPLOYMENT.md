# Railway Deployment Guide
## Money Circle Investment Club Platform

### **🚀 Why Railway?**
Railway is the optimal deployment platform for Money Circle because:
- ✅ **Full Python Support**: Native aiohttp application support
- ✅ **Database Integration**: Built-in PostgreSQL with automatic backups
- ✅ **WebSocket Support**: Real-time trading data connections
- ✅ **GitHub Integration**: Automatic deployments from repository
- ✅ **Custom Domains**: Easy SSL and domain configuration
- ✅ **Environment Variables**: Secure configuration management
- ✅ **Affordable**: $5/month starter plan, scales automatically

---

## **📋 STEP-BY-STEP DEPLOYMENT**

### **Step 1: Push Code to GitHub**
First, ensure your code is on GitHub (follow GITHUB_SETUP.md):
```bash
git add .
git commit -m "Prepare for Railway deployment"
git push origin main
```

### **Step 2: Create Railway Account**
1. Go to [railway.app](https://railway.app)
2. Click "Start a New Project"
3. Sign up with GitHub (recommended for easy integration)

### **Step 3: Deploy from GitHub**
1. **Click "Deploy from GitHub repo"**
2. **Select your repository**: `yourusername/money-circle`
3. **Railway will automatically detect**: Python application
4. **Click "Deploy Now"**

### **Step 4: Add PostgreSQL Database**
1. **In your Railway project dashboard**
2. **Click "New Service"**
3. **Select "Database" → "PostgreSQL"**
4. **Railway will provision a database automatically**

### **Step 5: Configure Environment Variables**
In Railway dashboard, go to your app service → Variables tab:

```bash
# Core Configuration
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=$PORT

# Database (Railway will auto-populate DATABASE_URL)
DATABASE_URL=${{Postgres.DATABASE_URL}}

# Security (generate secure values)
JWT_SECRET=your-secure-32-character-jwt-secret-here
SESSION_TIMEOUT=7200

# Trading Configuration
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
MAX_POSITION_SIZE=1000.0
RISK_LIMIT_PERCENT=2.0

# Exchange Configuration
BINANCE_ENABLED=true
HTX_ENABLED=true
BYBIT_ENABLED=true

# Monitoring
MONITORING_ENABLED=true
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600

# Python Configuration
PYTHONUNBUFFERED=1
```

### **Step 6: Configure Custom Domain (Optional)**
1. **In Railway dashboard**: Go to Settings → Domains
2. **Add custom domain**: `money-circle.yourdomain.com`
3. **Update DNS records** in your domain provider:
   ```
   Type: CNAME
   Name: money-circle
   Value: your-app.railway.app
   ```
4. **Railway automatically provisions SSL certificate**

### **Step 7: Database Migration**
Railway will automatically run the build command from `railway.json`:
```bash
pip install -r requirements.txt && python deployment/database_production_setup.py
```

This will:
- Install all Python dependencies
- Set up production database schema
- Create indexes for performance
- Seed initial data

---

## **🔧 CONFIGURATION DETAILS**

### **Environment Variables Explained**

#### **Core Settings**
- `ENVIRONMENT=production` - Enables production mode
- `DEBUG=false` - Disables debug mode for security
- `HOST=0.0.0.0` - Allows external connections
- `PORT=$PORT` - Uses Railway's assigned port

#### **Database**
- `DATABASE_URL` - Automatically set by Railway PostgreSQL
- Railway handles connection pooling and backups

#### **Security**
- `JWT_SECRET` - **CRITICAL**: Generate a secure 32+ character secret
- `SESSION_TIMEOUT` - Session expiry in seconds (7200 = 2 hours)

#### **Trading**
- `LIVE_TRADING_ENABLED=true` - Enables real trading (set false for demo)
- `TESTNET_MODE=false` - Uses live exchange APIs
- `MAX_POSITION_SIZE` - Maximum position size limit
- `RISK_LIMIT_PERCENT` - Risk management percentage

### **Database Migration from SQLite**
Railway uses PostgreSQL instead of SQLite. The migration happens automatically:

1. **Schema Creation**: Tables created with PostgreSQL syntax
2. **Index Creation**: Optimized indexes for performance
3. **Data Seeding**: Initial admin user and system settings
4. **Connection Pooling**: Automatic connection management

### **SSL/HTTPS Configuration**
Railway automatically provides:
- ✅ **Free SSL certificates** for all domains
- ✅ **Automatic HTTPS redirect** from HTTP
- ✅ **TLS 1.3 support** for maximum security
- ✅ **Certificate auto-renewal** (no maintenance required)

---

## **📊 MONITORING & HEALTH CHECKS**

### **Built-in Monitoring**
Railway provides:
- **Application logs**: Real-time log streaming
- **Performance metrics**: CPU, memory, network usage
- **Health checks**: Automatic monitoring of `/health` endpoint
- **Uptime monitoring**: 99.9% uptime SLA

### **Health Check Endpoint**
Money Circle includes a health check at `/health`:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "environment": "production",
  "database": "healthy",
  "market_data": "connected"
}
```

### **Log Access**
View logs in Railway dashboard or via CLI:
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and view logs
railway login
railway logs
```

---

## **🔒 SECURITY FEATURES**

### **Automatic Security**
Railway provides:
- ✅ **DDoS protection** at infrastructure level
- ✅ **Firewall protection** with automatic threat detection
- ✅ **Encrypted connections** (TLS 1.3)
- ✅ **Secure environment variables** (encrypted at rest)

### **Application Security**
Money Circle includes:
- ✅ **Rate limiting** (1000 requests/hour per IP)
- ✅ **CSRF protection** for all forms
- ✅ **Security headers** (XSS, clickjacking protection)
- ✅ **JWT authentication** with secure sessions
- ✅ **Input sanitization** to prevent injection attacks

---

## **💰 PRICING**

### **Railway Pricing (as of 2024)**
- **Hobby Plan**: $5/month
  - 512MB RAM, 1 vCPU
  - 1GB storage
  - Perfect for Money Circle
- **Pro Plan**: $20/month
  - 8GB RAM, 8 vCPU
  - 100GB storage
  - For high-traffic usage

### **Database Costs**
- **PostgreSQL**: Included in plan
- **Automatic backups**: Included
- **Connection pooling**: Included

---

## **🚀 DEPLOYMENT VERIFICATION**

### **Step 1: Check Deployment Status**
1. **Railway dashboard**: Ensure deployment shows "Success"
2. **Logs**: Check for any error messages
3. **Health check**: Visit `https://your-app.railway.app/health`

### **Step 2: Test Core Functionality**
1. **Homepage**: Visit your Railway URL
2. **Login**: Test with `epinnox` / `securepass123`
3. **Dashboard**: Verify all pages load correctly
4. **Market Data**: Check real-time data is flowing

### **Step 3: Performance Testing**
```bash
# Test response time
curl -w "@curl-format.txt" -o /dev/null -s https://your-app.railway.app/health

# Load testing (optional)
ab -n 100 -c 10 https://your-app.railway.app/
```

---

## **🆘 TROUBLESHOOTING**

### **Common Issues**

#### **Deployment Fails**
- **Check logs** in Railway dashboard
- **Verify requirements.txt** has all dependencies
- **Check Python version** (Railway uses Python 3.11)

#### **Database Connection Errors**
- **Verify DATABASE_URL** is set correctly
- **Check PostgreSQL service** is running
- **Review database migration logs**

#### **Environment Variable Issues**
- **Verify all required variables** are set
- **Check for typos** in variable names
- **Ensure JWT_SECRET** is properly generated

#### **Performance Issues**
- **Check resource usage** in Railway dashboard
- **Consider upgrading plan** if needed
- **Review application logs** for bottlenecks

### **Getting Help**
- **Railway Documentation**: [docs.railway.app](https://docs.railway.app)
- **Railway Discord**: Community support
- **GitHub Issues**: For Money Circle specific issues

---

## **🎯 SUCCESS CRITERIA**

Deployment is successful when:
- ✅ **Application accessible** via Railway URL
- ✅ **Health check returns 200** status
- ✅ **Login functionality works** (epinnox/securepass123)
- ✅ **All dashboards load** without errors
- ✅ **Real-time data flowing** from exchanges
- ✅ **Database operations working** (user creation, trading data)
- ✅ **SSL certificate valid** (green lock in browser)

---

## **🔄 CONTINUOUS DEPLOYMENT**

Railway automatically redeploys when you push to GitHub:
```bash
# Make changes locally
git add .
git commit -m "Update feature"
git push origin main

# Railway automatically detects and deploys changes
```

**Deployment typically takes 2-3 minutes.**

---

## **📞 SUPPORT**

- **Railway Support**: [railway.app/help](https://railway.app/help)
- **Money Circle Issues**: GitHub repository issues
- **Emergency Contact**: [Your contact information]

**Your Money Circle platform will be live at:**
`https://your-app.railway.app`

**Ready for Epinnox investment club members!** 🎉
