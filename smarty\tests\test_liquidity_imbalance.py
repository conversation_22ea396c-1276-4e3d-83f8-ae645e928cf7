"""
Tests for the Liquidity Imbalance model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.liquidity_imbalance import LiquidityImbalanceModel, ImbalanceSignal
from core.events import OrderbookDelta, OrderbookLevel


class TestLiquidityImbalanceModel(unittest.TestCase):
    """Test the Liquidity Imbalance model."""

    def setUp(self):
        """Set up test environment."""
        self.model = LiquidityImbalanceModel(
            window_size=10,
            depth_levels=5,
            significant_imbalance=0.3,
            decay_factor=0.9
        )

    def test_imbalance_calculation(self):
        """Test imbalance calculation."""
        # Create test orderbook levels
        bids = [
            OrderbookLevel(price=100.0, quantity=10.0),
            OrderbookLevel(price=99.0, quantity=20.0),
            OrderbookLevel(price=98.0, quantity=30.0)
        ]

        asks = [
            OrderbookLevel(price=101.0, quantity=5.0),
            OrderbookLevel(price=102.0, quantity=10.0),
            OrderbookLevel(price=103.0, quantity=15.0)
        ]

        # Calculate imbalance
        imbalance_ratio, bid_volume, ask_volume = self.model._calculate_imbalance(bids, asks)

        # Check that bid volume is greater than ask volume
        self.assertGreater(bid_volume, ask_volume)

        # Check that imbalance ratio is positive (bid dominated)
        self.assertGreater(imbalance_ratio, 0)

        # Check that imbalance ratio is between -1 and 1
        self.assertGreaterEqual(imbalance_ratio, -1.0)
        self.assertLessEqual(imbalance_ratio, 1.0)

    def test_mid_price_calculation(self):
        """Test mid price calculation."""
        # Create test orderbook levels
        bids = [
            OrderbookLevel(price=100.0, quantity=10.0),
            OrderbookLevel(price=99.0, quantity=20.0)
        ]

        asks = [
            OrderbookLevel(price=101.0, quantity=5.0),
            OrderbookLevel(price=102.0, quantity=10.0)
        ]

        # Calculate mid price
        mid_price = self.model._calculate_mid_price(bids, asks)

        # Check result
        self.assertEqual(mid_price, 100.5)

    def test_predict_with_orderbook_delta(self):
        """Test predict method with OrderbookDelta."""
        async def test_predict_async():
            # Create test orderbook
            orderbook = OrderbookDelta(
                symbol="BTC-USDT",
                timestamp=datetime.now(),
                bids=[
                    OrderbookLevel(price=100.0, quantity=10.0),
                    OrderbookLevel(price=99.0, quantity=20.0),
                    OrderbookLevel(price=98.0, quantity=30.0)
                ],
                asks=[
                    OrderbookLevel(price=101.0, quantity=5.0),
                    OrderbookLevel(price=102.0, quantity=10.0),
                    OrderbookLevel(price=103.0, quantity=15.0)
                ],
                is_snapshot=True
            )

            # Create features
            features = {
                "symbol": "BTC-USDT",
                "orderbook": orderbook,
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Verify prediction structure
            self.assertIn("imbalance_ratio", prediction)
            self.assertIn("rolling_imbalance", prediction)
            self.assertIn("signal", prediction)
            self.assertIn("bid_volume", prediction)
            self.assertIn("ask_volume", prediction)
            self.assertIn("liquidity_ratio", prediction)
            self.assertIn("thinness", prediction)
            self.assertIn("action", prediction)
            self.assertIn("confidence", prediction)

            # Verify signal is one of the valid signals
            self.assertIn(prediction["signal"], [s.value for s in ImbalanceSignal])

            # Verify action is one of the valid actions
            self.assertIn(prediction["action"], ["BUY", "SELL", "HOLD"])

            # Verify confidence is between 0 and 1
            self.assertGreaterEqual(prediction["confidence"], 0.0)
            self.assertLessEqual(prediction["confidence"], 1.0)

            # In this case, bids dominate asks, so we expect a BID_DOMINATED signal
            self.assertEqual(prediction["signal"], ImbalanceSignal.BID_DOMINATED.value)
            self.assertEqual(prediction["action"], "BUY")

        # Run async test
        asyncio.run(test_predict_async())

    def test_predict_with_raw_data(self):
        """Test predict method with raw bid/ask data."""
        async def test_predict_async():
            # Create features with raw bid/ask data
            features = {
                "symbol": "BTC-USDT",
                "bids": [
                    [100.0, 10.0],  # price, quantity
                    [99.0, 20.0],
                    [98.0, 30.0]
                ],
                "asks": [
                    [101.0, 5.0],
                    [102.0, 10.0],
                    [103.0, 15.0]
                ],
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Verify prediction structure
            self.assertIn("imbalance_ratio", prediction)
            self.assertIn("rolling_imbalance", prediction)
            self.assertIn("signal", prediction)

            # In this case, bids dominate asks, so we expect a BID_DOMINATED signal
            self.assertEqual(prediction["signal"], ImbalanceSignal.BID_DOMINATED.value)

        # Run async test
        asyncio.run(test_predict_async())

    def test_rolling_imbalance(self):
        """Test rolling imbalance calculation."""
        async def test_rolling_async():
            symbol = "BTC-USDT"

            # Create a series of orderbooks with varying imbalance
            for i in range(15):
                # Alternate between bid-dominated and ask-dominated
                if i % 2 == 0:
                    # Bid-dominated
                    bids = [
                        OrderbookLevel(price=100.0, quantity=10.0 + i),
                        OrderbookLevel(price=99.0, quantity=20.0)
                    ]
                    asks = [
                        OrderbookLevel(price=101.0, quantity=5.0),
                        OrderbookLevel(price=102.0, quantity=10.0)
                    ]
                else:
                    # Ask-dominated
                    bids = [
                        OrderbookLevel(price=100.0, quantity=5.0),
                        OrderbookLevel(price=99.0, quantity=10.0)
                    ]
                    asks = [
                        OrderbookLevel(price=101.0, quantity=10.0 + i),
                        OrderbookLevel(price=102.0, quantity=20.0)
                    ]

                orderbook = OrderbookDelta(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    bids=bids,
                    asks=asks,
                    is_snapshot=True
                )

                features = {
                    "symbol": symbol,
                    "orderbook": orderbook,
                    "timestamp": datetime.now()
                }

                prediction = await self.model.predict(features)

            # Check that we have the expected number of entries in the history
            self.assertEqual(len(self.model._imbalance_history[symbol]), 10)  # window_size=10

            # Check that rolling imbalance is calculated
            self.assertIn("rolling_imbalance", prediction)

        # Run async test
        asyncio.run(test_rolling_async())

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Create features with no orderbook
            features = {
                "symbol": "BTC-USDT",
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["signal"], ImbalanceSignal.BALANCED.value)
            self.assertEqual(prediction["action"], "HOLD")
            self.assertEqual(prediction["confidence"], 0.0)
            self.assertEqual(prediction["imbalance_ratio"], 0.0)

        # Run async test
        asyncio.run(test_insufficient_data_async())


if __name__ == "__main__":
    unittest.main()
