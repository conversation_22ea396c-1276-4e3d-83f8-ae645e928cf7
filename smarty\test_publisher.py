#!/usr/bin/env python3
"""
Test script to verify the publisher is working correctly.
"""

import asyncio
import sys
import sqlite3
from pipeline.databus import SQLiteBus
from feeds.htx_futures import HTXFuturesClient
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_publisher():
    """Test if the publisher is working."""
    
    # Fix for Windows
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # Create SQLite bus
    bus = SQLiteBus(path="data/test_publisher.db", poll_interval=0.5)
    
    # Test direct publishing
    logger.info("🧪 Testing direct publishing to SQLite bus...")
    bus.publish("test.stream", 1234567890, {"test": "data", "value": 123})
    
    # Check if message was stored
    conn = sqlite3.connect("data/test_publisher.db")
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM messages WHERE stream = 'test.stream'")
    count = cursor.fetchone()[0]
    conn.close()
    
    logger.info(f"📊 Direct publish test: {count} messages stored")
    
    if count == 0:
        logger.error("❌ Direct publishing failed!")
        return False
    
    # Test HTX client with publisher
    logger.info("🧪 Testing HTX client with publisher...")
    
    htx_client = HTXFuturesClient(testnet=False)
    htx_client.simulation_mode = True
    
    # Set the publisher
    htx_client.set_publisher(bus.publish)
    
    # Check if publisher was set
    if htx_client._publish:
        logger.info("✅ Publisher set successfully")
    else:
        logger.error("❌ Publisher not set!")
        return False
    
    # Test manual publishing through HTX client
    logger.info("🧪 Testing manual publish through HTX client...")
    
    # Simulate a manual publish call
    try:
        htx_client._publish("test.htx.manual", 1234567890, {"manual": "test"})
        logger.info("✅ Manual publish through HTX client succeeded")
    except Exception as e:
        logger.error(f"❌ Manual publish through HTX client failed: {e}")
        return False
    
    # Check database again
    conn = sqlite3.connect("data/test_publisher.db")
    cursor = conn.cursor()
    cursor.execute("SELECT stream, payload FROM messages ORDER BY ts")
    messages = cursor.fetchall()
    conn.close()
    
    logger.info("📊 All messages in test database:")
    for stream, payload in messages:
        logger.info(f"   {stream}: {payload}")
    
    bus.close()
    return True

if __name__ == "__main__":
    result = asyncio.run(test_publisher())
    if result:
        logger.info("✅ Publisher test PASSED")
    else:
        logger.error("❌ Publisher test FAILED")
