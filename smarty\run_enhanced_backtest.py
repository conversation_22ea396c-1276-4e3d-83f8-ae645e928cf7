#!/usr/bin/env python3
"""
Quick script to run enhanced backtesting and analysis on the smart strategy.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def run_enhanced_analysis():
    """Run comprehensive enhanced backtesting and analysis."""
    
    print("🚀 Starting Enhanced Smart Strategy Analysis...")
    print("=" * 60)
    
    # Import our enhanced tools
    try:
        from enhanced_backtest_runner import EnhancedBacktestRunner
        from strategy_analyzer import StrategyAnalyzer
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the smarty directory")
        return
    
    # Configuration
    symbols = ['BTC-USDT']
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    output_dir = 'results/enhanced_analysis'
    
    print(f"📊 Testing Strategy: Smart Model Integrated")
    print(f"📈 Symbols: {', '.join(symbols)}")
    print(f"📅 Period: {start_date} to {end_date}")
    print(f"📁 Output: {output_dir}")
    print()
    
    # Step 1: Run strategy analysis to identify issues
    print("🔍 Step 1: Analyzing Current Strategy Performance...")
    try:
        analyzer = StrategyAnalyzer()
        analysis_results = await analyzer.analyze_strategy_performance(
            strategy_name='smart',
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            output_dir=os.path.join(output_dir, 'analysis')
        )
        
        print("✅ Strategy analysis completed!")
        
        # Print key findings
        if 'improvement_suggestions' in analysis_results:
            suggestions = analysis_results['improvement_suggestions']
            high_priority = [s for s in suggestions if s.get('priority') == 'High']
            
            print(f"\n🚨 Found {len(high_priority)} high-priority issues:")
            for i, suggestion in enumerate(high_priority[:3], 1):
                print(f"  {i}. {suggestion.get('issue', 'Unknown')}")
            
            print(f"\n💡 Total improvement suggestions: {len(suggestions)}")
        
    except Exception as e:
        print(f"❌ Strategy analysis failed: {e}")
        logger.error(f"Strategy analysis error: {e}")
    
    print("\n" + "-" * 60)
    
    # Step 2: Run enhanced backtesting with multiple strategies
    print("🏃 Step 2: Running Enhanced Backtesting...")
    try:
        runner = EnhancedBacktestRunner()
        
        # Test multiple strategies for comparison
        strategies_to_test = ['smart', 'ensemble', 'multi']
        
        backtest_results = await runner.run_comprehensive_backtest(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            strategies=strategies_to_test,
            optimize=False,  # Skip optimization for now (can be slow)
            walk_forward=False,  # Skip walk-forward for now
            output_dir=os.path.join(output_dir, 'backtest')
        )
        
        print("✅ Enhanced backtesting completed!")
        
        # Print comparison results
        if 'comparison' in backtest_results and 'ranking' in backtest_results['comparison']:
            print("\n🏆 Strategy Rankings:")
            
            rankings = backtest_results['comparison']['ranking']
            
            if 'sharpe_ratio' in rankings:
                print("  By Sharpe Ratio:")
                for i, strategy in enumerate(rankings['sharpe_ratio'][:3], 1):
                    print(f"    {i}. {strategy}")
            
            if 'total_return' in rankings:
                print("  By Total Return:")
                for i, strategy in enumerate(rankings['total_return'][:3], 1):
                    print(f"    {i}. {strategy}")
        
        # Print performance summary
        if 'strategy_results' in backtest_results:
            print("\n📊 Performance Summary:")
            for strategy_name, results in backtest_results['strategy_results'].items():
                metrics = results.get('metrics', {})
                if isinstance(metrics, dict):
                    total_return = metrics.get('total_return', 0)
                    sharpe_ratio = metrics.get('sharpe_ratio', 0)
                else:
                    total_return = getattr(metrics, 'total_return', 0)
                    sharpe_ratio = getattr(metrics, 'sharpe_ratio', 0)
                
                print(f"  {strategy_name}: Return={total_return:.2%}, Sharpe={sharpe_ratio:.3f}")
        
    except Exception as e:
        print(f"❌ Enhanced backtesting failed: {e}")
        logger.error(f"Enhanced backtesting error: {e}")
    
    print("\n" + "-" * 60)
    
    # Step 3: Quick optimization test for smart strategy
    print("🔧 Step 3: Quick Parameter Optimization...")
    try:
        # Run a quick optimization on the smart strategy
        optimization_results = await runner.run_comprehensive_backtest(
            symbols=symbols,
            start_date='2023-06-01',  # Shorter period for faster optimization
            end_date='2023-12-31',
            strategies=['smart'],
            optimize=True,
            walk_forward=False,
            output_dir=os.path.join(output_dir, 'optimization')
        )
        
        print("✅ Parameter optimization completed!")
        
        # Print optimization results
        if 'optimization' in optimization_results and 'smart' in optimization_results['optimization']:
            opt_data = optimization_results['optimization']['smart']
            best_params = opt_data.get('best_params', {})
            best_metrics = opt_data.get('best_metrics', {})
            
            print(f"\n🎯 Best Parameters Found:")
            for param, value in best_params.items():
                print(f"  {param}: {value}")
            
            if best_metrics:
                best_sharpe = best_metrics.get('sharpe_ratio', 0)
                best_return = best_metrics.get('total_return', 0)
                print(f"\n📈 Optimized Performance:")
                print(f"  Sharpe Ratio: {best_sharpe:.3f}")
                print(f"  Total Return: {best_return:.2%}")
        
    except Exception as e:
        print(f"❌ Parameter optimization failed: {e}")
        logger.error(f"Parameter optimization error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced Analysis Complete!")
    print(f"📁 All results saved to: {output_dir}")
    print("\n📋 Next Steps:")
    print("1. Review the analysis report for specific improvement suggestions")
    print("2. Check the strategy comparison to see which performs best")
    print("3. Use the optimized parameters if they show improvement")
    print("4. Consider implementing the suggested fixes")
    
    # Generate summary report
    await generate_summary_report(output_dir)


async def generate_summary_report(output_dir: str):
    """Generate a summary report of all analysis."""
    try:
        summary_file = os.path.join(output_dir, 'SUMMARY_REPORT.txt')
        
        with open(summary_file, 'w') as f:
            f.write("SMART STRATEGY ENHANCED ANALYSIS SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("ANALYSIS COMPONENTS:\n")
            f.write("- Strategy Performance Analysis\n")
            f.write("- Multi-Strategy Comparison\n")
            f.write("- Parameter Optimization\n")
            f.write("- Improvement Suggestions\n\n")
            
            f.write("DIRECTORIES:\n")
            f.write(f"- Analysis Results: {os.path.join(output_dir, 'analysis')}\n")
            f.write(f"- Backtest Results: {os.path.join(output_dir, 'backtest')}\n")
            f.write(f"- Optimization Results: {os.path.join(output_dir, 'optimization')}\n\n")
            
            f.write("KEY FILES TO REVIEW:\n")
            f.write("- analysis/analysis_report_*.txt (Detailed analysis)\n")
            f.write("- analysis/improvement_suggestions_*.json (Specific fixes)\n")
            f.write("- backtest/comprehensive_report_*.txt (Strategy comparison)\n")
            f.write("- optimization/best_params_*.json (Optimized parameters)\n\n")
            
            f.write("RECOMMENDED ACTIONS:\n")
            f.write("1. Read the analysis report for identified issues\n")
            f.write("2. Implement high-priority improvement suggestions\n")
            f.write("3. Test optimized parameters in live environment\n")
            f.write("4. Monitor model availability and data quality\n")
            f.write("5. Consider risk management improvements\n")
        
        print(f"📄 Summary report saved: {summary_file}")
        
    except Exception as e:
        logger.error(f"Error generating summary report: {e}")


async def quick_test():
    """Quick test to check if everything is working."""
    print("🧪 Running Quick Test...")
    
    try:
        # Test imports
        from enhanced_backtest_runner import EnhancedBacktestRunner
        from strategy_analyzer import StrategyAnalyzer
        print("✅ Imports successful")
        
        # Test basic functionality
        analyzer = StrategyAnalyzer()
        print("✅ Strategy analyzer created")
        
        runner = EnhancedBacktestRunner()
        print("✅ Enhanced backtest runner created")
        
        print("🎉 Quick test passed! Ready to run full analysis.")
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False


async def main():
    """Main function."""
    print("🔬 Smart Strategy Enhanced Analysis Tool")
    print("=" * 50)
    
    # Run quick test first
    if not await quick_test():
        print("\n❌ Quick test failed. Please check your setup.")
        return
    
    print("\n" + "=" * 50)
    
    # Ask user if they want to proceed
    try:
        response = input("\n🤔 Run full enhanced analysis? This may take several minutes. (y/n): ")
        if response.lower() not in ['y', 'yes']:
            print("👋 Analysis cancelled.")
            return
    except KeyboardInterrupt:
        print("\n👋 Analysis cancelled.")
        return
    
    # Run full analysis
    await run_enhanced_analysis()


if __name__ == "__main__":
    asyncio.run(main())
