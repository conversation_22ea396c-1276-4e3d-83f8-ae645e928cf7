/* Money Circle Member Directory Styles */

.directory-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    color: #e2e8f0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.directory-header {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.directory-branding h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.directory-branding p {
    margin: 0.25rem 0 0 0;
    color: #94a3b8;
    font-size: 0.9rem;
}

.directory-nav {
    display: flex;
    gap: 1.5rem;
}

.directory-nav .nav-link {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.directory-nav .nav-link:hover {
    color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

.directory-nav .nav-link.active {
    color: #22c55e;
    background: rgba(34, 197, 94, 0.2);
}

/* Member Stats Overview */
.member-stats-overview {
    background: rgba(30, 41, 59, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
}

.stats-grid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 0.75rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f1f5f9;
    margin: 0;
}

.stat-label {
    font-size: 0.875rem;
    color: #94a3b8;
    margin: 0;
}

/* Directory Controls */
.directory-controls {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.search-bar {
    display: flex;
    gap: 0.5rem;
    flex: 1;
    max-width: 500px;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 1rem;
}

.search-bar input::placeholder {
    color: #64748b;
}

.search-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border: none;
    border-radius: 0.5rem;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.view-toggles {
    display: flex;
    gap: 0.5rem;
}

.view-toggle {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.view-toggle.active,
.view-toggle:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: #22c55e;
    color: #22c55e;
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #94a3b8;
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-group select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 0.875rem;
}

/* Leaderboards */
.leaderboards-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem 2rem 2rem;
}

.leaderboards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.leaderboard-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
}

.leaderboard-card h3 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.leaderboard-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.rank-badge {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
}

.rank-1 .rank-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1f2937;
}

.rank-2 .rank-badge {
    background: linear-gradient(135deg, #94a3b8, #64748b);
    color: #1f2937;
}

.rank-3 .rank-badge {
    background: linear-gradient(135deg, #cd7c2f, #a16207);
    color: #1f2937;
}

.rank-other .rank-badge {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    color: #f1f5f9;
    margin: 0 0 0.25rem 0;
}

.member-value {
    font-size: 0.875rem;
    color: #22c55e;
    font-weight: 500;
}

.view-profile-btn {
    padding: 0.5rem 1rem;
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 0.5rem;
    color: #22c55e;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.view-profile-btn:hover {
    background: rgba(34, 197, 94, 0.3);
    transform: translateY(-1px);
}

/* Member Grid */
.member-grid-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem 2rem 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: #f1f5f9;
}

.results-info {
    color: #94a3b8;
    font-size: 0.875rem;
}

.member-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.member-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.member-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(34, 197, 94, 0.3);
}

.member-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.member-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    position: relative;
}

.verified-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 1rem;
    height: 1rem;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.625rem;
    color: white;
    border: 2px solid #0f1419;
}

.member-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.action-btn:hover {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.action-btn.connected {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.member-info {
    margin-bottom: 1rem;
}

.member-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #f1f5f9;
    line-height: 1.3;
}

.member-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 0.5rem;
}

.experience-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.exp-beginner {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.exp-intermediate {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.exp-advanced {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.exp-expert {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.specialization-badge {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
}

.member-location {
    color: #64748b;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.member-stats {
    margin: 1rem 0;
}

.stat-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.stat {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #f1f5f9;
}

.stat-value.positive {
    color: #22c55e;
}

.stat-value.negative {
    color: #ef4444;
}

.member-bio {
    color: #94a3b8;
    line-height: 1.5;
    margin: 1rem 0;
    font-size: 0.875rem;
}

.member-achievements {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.achievement-badge {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: rgba(251, 191, 36, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.achievement-badge:hover {
    transform: scale(1.1);
    background: rgba(251, 191, 36, 0.3);
}

.no-achievements {
    color: #64748b;
    font-size: 0.75rem;
    font-style: italic;
}

.member-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.75rem;
}

.activity-label {
    color: #64748b;
}

.activity-time {
    color: #94a3b8;
}

.member-strategies {
    color: #22c55e;
    font-weight: 500;
}

.member-card-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
}

.btn-view,
.btn-connect {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 0.875rem;
}

.btn-view {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-view:hover {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border-color: rgba(34, 197, 94, 0.3);
}

.btn-connect {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.btn-connect.active {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.3);
}

.btn-connect:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-connect.active:hover {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #64748b;
    font-size: 1.125rem;
}

.empty-leaderboard {
    text-align: center;
    padding: 2rem;
    color: #64748b;
    font-style: italic;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.large .modal-content {
    max-width: 800px;
}

.modal-content {
    background: linear-gradient(135deg, #1e293b, #334155);
    margin: 5% auto;
    padding: 0;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #f1f5f9;
    font-size: 1.5rem;
    font-weight: 700;
}

.close {
    color: #94a3b8;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ef4444;
}

.modal-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #94a3b8;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 1rem;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.form-actions button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-actions button[type="button"] {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
}

.form-actions button[type="submit"] {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
}

.form-actions button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .directory-nav {
        order: -1;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .search-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .filter-controls {
        grid-template-columns: 1fr;
    }
    
    .leaderboards-grid {
        grid-template-columns: 1fr;
    }
    
    .member-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
}
