{
  "start_time": "2025-05-23T19:33:57.129517",
  "tests": {
    "config_loading": {
      "status": "FAIL",
      "message": "Configuration loading failed: Missing required configuration key: symbols",
      "details": {}
    },
    "monitor_init": {
      "status": "PASS",
      "message": "Model monitor initialized and functioning",
      "details": {
        "total_predictions": 1,
        "avg_latency_ms": 100.0,
        "avg_confidence": 0.8
      }
    },
    "feature_store": {
      "status": "PASS",
      "message": "Feature store integration working",
      "details": {
        "stored_data": {
          "price": 50000.0,
          "volume": 1000.0,
          "timestamp": "2025-05-23T19:33:57.138039"
        },
        "retrieved_data": {
          "price": 50000.0,
          "volume": 1000.0,
          "timestamp": "2025-05-23T19:33:57.138039"
        }
      }
    },
    "model_execution": {
      "status": "PASS",
      "message": "Model execution successful",
      "details": {
        "execution_time_ms": 0.0,
        "prediction": {
          "rsi": 50.0,
          "is_overbought": 