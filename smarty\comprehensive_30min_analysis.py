#!/usr/bin/env python3
"""
📊 Comprehensive 30-Minute Live Analysis

Monitors the Epinnox Smart Trading Dashboard for exactly 30 minutes,
collecting performance metrics, logs, and system behavior data.
"""

import asyncio
import time
import json
import logging
import requests
import subprocess
import psutil
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
from dataclasses import dataclass, asdict

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SystemSnapshot:
    """System performance snapshot."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_connections: int
    dashboard_responsive: bool
    response_time_ms: float
    active_processes: int

@dataclass
class DashboardMetrics:
    """Dashboard-specific metrics."""
    timestamp: float
    authentication_working: bool
    api_endpoints_responding: int
    websocket_connected: bool
    strategy_count: int
    market_data_age: float
    error_count: int

class ComprehensiveAnalyzer:
    """Comprehensive 30-minute system analysis."""
    
    def __init__(self):
        self.start_time = time.time()
        self.analysis_duration = 30 * 60  # 30 minutes
        self.snapshots = []
        self.dashboard_metrics = []
        self.errors = []
        self.performance_issues = []
        
        # Analysis configuration
        self.dashboard_url = "http://localhost:8082"
        self.monitor_url = "ws://localhost:8083"
        self.snapshot_interval = 30  # Every 30 seconds
        
    async def run_analysis(self):
        """Run the complete 30-minute analysis."""
        logger.info("📊 Starting 30-Minute Comprehensive Live Analysis")
        logger.info("=" * 60)
        logger.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Duration: 30 minutes")
        logger.info(f"Snapshot Interval: {self.snapshot_interval} seconds")
        logger.info("")
        
        end_time = self.start_time + self.analysis_duration
        snapshot_count = 0
        
        try:
            while time.time() < end_time:
                snapshot_count += 1
                elapsed = time.time() - self.start_time
                remaining = end_time - time.time()
                
                logger.info(f"📸 Snapshot {snapshot_count} - Elapsed: {elapsed/60:.1f}min, Remaining: {remaining/60:.1f}min")
                
                # Collect system snapshot
                system_snapshot = await self._collect_system_snapshot()
                self.snapshots.append(system_snapshot)
                
                # Collect dashboard metrics
                dashboard_metrics = await self._collect_dashboard_metrics()
                self.dashboard_metrics.append(dashboard_metrics)
                
                # Check for issues
                await self._check_for_issues(system_snapshot, dashboard_metrics)
                
                # Log current status
                self._log_current_status(system_snapshot, dashboard_metrics)
                
                # Wait for next snapshot
                await asyncio.sleep(self.snapshot_interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 Analysis interrupted by user")
        except Exception as e:
            logger.error(f"❌ Analysis error: {e}")
            self.errors.append(f"Analysis error: {e}")
        
        # Generate final report
        await self._generate_final_report()
    
    async def _collect_system_snapshot(self) -> SystemSnapshot:
        """Collect system performance snapshot."""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            # Network connections
            connections = len(psutil.net_connections())
            
            # Process count
            processes = len(psutil.pids())
            
            # Dashboard responsiveness test
            dashboard_responsive = False
            response_time_ms = 0
            
            try:
                start = time.time()
                response = requests.get(f"{self.dashboard_url}/login", timeout=5)
                response_time_ms = (time.time() - start) * 1000
                dashboard_responsive = response.status_code in [200, 302]
            except Exception as e:
                self.errors.append(f"Dashboard connectivity error: {e}")
            
            return SystemSnapshot(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage=disk.percent,
                network_connections=connections,
                dashboard_responsive=dashboard_responsive,
                response_time_ms=response_time_ms,
                active_processes=processes
            )
            
        except Exception as e:
            logger.error(f"❌ System snapshot error: {e}")
            self.errors.append(f"System snapshot error: {e}")
            return SystemSnapshot(0, 0, 0, 0, 0, False, 0, 0)
    
    async def _collect_dashboard_metrics(self) -> DashboardMetrics:
        """Collect dashboard-specific metrics."""
        try:
            # Test authentication
            auth_working = False
            try:
                session = requests.Session()
                login_data = {"username": "epinnox", "password": "securepass123"}
                response = session.post(f"{self.dashboard_url}/login", data=login_data, timeout=10)
                auth_working = response.status_code in [200, 302]
            except Exception as e:
                self.errors.append(f"Authentication test error: {e}")
            
            # Test API endpoints
            api_endpoints = [
                "/api/strategy/status",
                "/api/market-data",
                "/api/orderbook",
                "/api/recent-trades"
            ]
            
            responding_endpoints = 0
            strategy_count = 0
            market_data_age = 0
            
            if auth_working:
                for endpoint in api_endpoints:
                    try:
                        response = session.get(f"{self.dashboard_url}{endpoint}", timeout=5)
                        if response.status_code == 200:
                            responding_endpoints += 1
                            
                            # Extract specific data
                            if endpoint == "/api/strategy/status":
                                data = response.json()
                                strategy_count = len(data.get("available_strategies", []))
                            elif endpoint == "/api/market-data":
                                data = response.json()
                                timestamp = data.get("timestamp", 0)
                                if timestamp:
                                    market_data_age = time.time() - timestamp
                    except Exception as e:
                        self.errors.append(f"API endpoint {endpoint} error: {e}")
            
            return DashboardMetrics(
                timestamp=time.time(),
                authentication_working=auth_working,
                api_endpoints_responding=responding_endpoints,
                websocket_connected=True,  # Assume connected if dashboard is responsive
                strategy_count=strategy_count,
                market_data_age=market_data_age,
                error_count=len(self.errors)
            )
            
        except Exception as e:
            logger.error(f"❌ Dashboard metrics error: {e}")
            self.errors.append(f"Dashboard metrics error: {e}")
            return DashboardMetrics(0, False, 0, False, 0, 0, len(self.errors))
    
    async def _check_for_issues(self, system: SystemSnapshot, dashboard: DashboardMetrics):
        """Check for performance issues."""
        issues = []
        
        # System performance issues
        if system.cpu_percent > 80:
            issues.append(f"High CPU usage: {system.cpu_percent:.1f}%")
        
        if system.memory_percent > 85:
            issues.append(f"High memory usage: {system.memory_percent:.1f}%")
        
        if system.response_time_ms > 2000:
            issues.append(f"Slow response time: {system.response_time_ms:.0f}ms")
        
        if not system.dashboard_responsive:
            issues.append("Dashboard not responsive")
        
        # Dashboard issues
        if not dashboard.authentication_working:
            issues.append("Authentication not working")
        
        if dashboard.api_endpoints_responding < 3:
            issues.append(f"Only {dashboard.api_endpoints_responding}/4 API endpoints responding")
        
        if dashboard.market_data_age > 300:  # 5 minutes
            issues.append(f"Market data stale: {dashboard.market_data_age:.0f}s old")
        
        # Log issues
        for issue in issues:
            logger.warning(f"⚠️ Issue detected: {issue}")
            self.performance_issues.append({
                "timestamp": time.time(),
                "issue": issue
            })
    
    def _log_current_status(self, system: SystemSnapshot, dashboard: DashboardMetrics):
        """Log current system status."""
        logger.info(f"   💻 CPU: {system.cpu_percent:.1f}%, RAM: {system.memory_percent:.1f}%")
        logger.info(f"   🌐 Dashboard: {'✅' if system.dashboard_responsive else '❌'} ({system.response_time_ms:.0f}ms)")
        logger.info(f"   🔐 Auth: {'✅' if dashboard.authentication_working else '❌'}, APIs: {dashboard.api_endpoints_responding}/4")
        logger.info(f"   📊 Strategies: {dashboard.strategy_count}, Data Age: {dashboard.market_data_age:.0f}s")
        logger.info("")
    
    async def _generate_final_report(self):
        """Generate comprehensive final report."""
        logger.info("📋 Generating Final Analysis Report")
        logger.info("=" * 60)
        
        # Calculate analysis duration
        actual_duration = time.time() - self.start_time
        
        # System performance statistics
        if self.snapshots:
            avg_cpu = sum(s.cpu_percent for s in self.snapshots) / len(self.snapshots)
            max_cpu = max(s.cpu_percent for s in self.snapshots)
            avg_memory = sum(s.memory_percent for s in self.snapshots) / len(self.snapshots)
            max_memory = max(s.memory_percent for s in self.snapshots)
            avg_response = sum(s.response_time_ms for s in self.snapshots) / len(self.snapshots)
            max_response = max(s.response_time_ms for s in self.snapshots)
            uptime_percent = sum(1 for s in self.snapshots if s.dashboard_responsive) / len(self.snapshots) * 100
        else:
            avg_cpu = max_cpu = avg_memory = max_memory = avg_response = max_response = uptime_percent = 0
        
        # Dashboard statistics
        if self.dashboard_metrics:
            auth_success_rate = sum(1 for d in self.dashboard_metrics if d.authentication_working) / len(self.dashboard_metrics) * 100
            avg_api_response = sum(d.api_endpoints_responding for d in self.dashboard_metrics) / len(self.dashboard_metrics)
        else:
            auth_success_rate = avg_api_response = 0
        
        # Create comprehensive report
        report = {
            "analysis_summary": {
                "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
                "end_time": datetime.now().isoformat(),
                "planned_duration_minutes": 30,
                "actual_duration_minutes": actual_duration / 60,
                "total_snapshots": len(self.snapshots),
                "total_errors": len(self.errors),
                "total_issues": len(self.performance_issues)
            },
            
            "system_performance": {
                "cpu_usage": {
                    "average_percent": round(avg_cpu, 2),
                    "maximum_percent": round(max_cpu, 2),
                    "status": "Good" if avg_cpu < 50 else "High" if avg_cpu < 80 else "Critical"
                },
                "memory_usage": {
                    "average_percent": round(avg_memory, 2),
                    "maximum_percent": round(max_memory, 2),
                    "status": "Good" if avg_memory < 70 else "High" if avg_memory < 85 else "Critical"
                },
                "response_time": {
                    "average_ms": round(avg_response, 2),
                    "maximum_ms": round(max_response, 2),
                    "status": "Excellent" if avg_response < 500 else "Good" if avg_response < 1000 else "Slow"
                },
                "uptime_percent": round(uptime_percent, 2)
            },
            
            "dashboard_performance": {
                "authentication_success_rate": round(auth_success_rate, 2),
                "average_api_endpoints_responding": round(avg_api_response, 2),
                "overall_status": "Excellent" if auth_success_rate > 95 and avg_api_response > 3.5 else "Good" if auth_success_rate > 90 else "Poor"
            },
            
            "issues_detected": self.performance_issues,
            "errors_encountered": self.errors,
            
            "recommendations": self._generate_recommendations(avg_cpu, avg_memory, avg_response, uptime_percent, auth_success_rate)
        }
        
        # Save report
        report_filename = f"30min_analysis_report_{int(self.start_time)}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        logger.info("📊 ANALYSIS SUMMARY")
        logger.info(f"Duration: {actual_duration/60:.1f} minutes")
        logger.info(f"Snapshots: {len(self.snapshots)}")
        logger.info(f"Uptime: {uptime_percent:.1f}%")
        logger.info(f"Avg CPU: {avg_cpu:.1f}%")
        logger.info(f"Avg Memory: {avg_memory:.1f}%")
        logger.info(f"Avg Response: {avg_response:.0f}ms")
        logger.info(f"Auth Success: {auth_success_rate:.1f}%")
        logger.info(f"Issues: {len(self.performance_issues)}")
        logger.info(f"Errors: {len(self.errors)}")
        logger.info("")
        logger.info(f"📄 Full report saved to: {report_filename}")
        
        return report
    
    def _generate_recommendations(self, avg_cpu, avg_memory, avg_response, uptime_percent, auth_success_rate) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        if avg_cpu > 70:
            recommendations.append("Consider optimizing CPU-intensive operations")
        
        if avg_memory > 80:
            recommendations.append("Monitor memory usage and implement cleanup routines")
        
        if avg_response > 1000:
            recommendations.append("Optimize response times with caching or async operations")
        
        if uptime_percent < 95:
            recommendations.append("Investigate dashboard stability issues")
        
        if auth_success_rate < 95:
            recommendations.append("Review authentication system reliability")
        
        if not recommendations:
            recommendations.append("System performing excellently - no immediate optimizations needed")
        
        return recommendations

async def main():
    """Main analysis runner."""
    analyzer = ComprehensiveAnalyzer()
    await analyzer.run_analysis()

if __name__ == "__main__":
    asyncio.run(main())
