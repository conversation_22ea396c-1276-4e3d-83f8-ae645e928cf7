"""
Enhanced analytics module for backtesting performance analysis.

This module provides comprehensive performance metrics, risk analysis,
and visualization capabilities for trading strategy evaluation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for a trading strategy."""

    # Basic metrics
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float

    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float

    # Risk metrics
    var_95: float  # Value at Risk (95%)
    cvar_95: float  # Conditional Value at Risk (95%)
    beta: float
    alpha: float

    # Advanced metrics
    information_ratio: float
    treynor_ratio: float
    omega_ratio: float
    tail_ratio: float

    # Timing metrics
    avg_trade_duration: float
    max_trade_duration: float
    min_trade_duration: float

    # Balance metrics
    initial_balance: float
    final_balance: float
    peak_balance: float

    # Additional statistics
    skewness: float
    kurtosis: float
    stability: float  # Consistency of returns


class EnhancedAnalytics:
    """Enhanced analytics engine for comprehensive performance analysis."""

    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize the analytics engine.

        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate

    def calculate_comprehensive_metrics(
        self,
        balance_history: List[float],
        trade_history: List[Dict[str, Any]],
        timestamps: List[datetime],
        benchmark_returns: Optional[List[float]] = None
    ) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics.

        Args:
            balance_history: List of balance values over time
            trade_history: List of trade dictionaries
            timestamps: List of timestamps corresponding to balance history
            benchmark_returns: Optional benchmark returns for beta/alpha calculation

        Returns:
            PerformanceMetrics object with all calculated metrics
        """
        if len(balance_history) < 2:
            logger.warning("Insufficient data for comprehensive metrics calculation")
            return self._create_empty_metrics()

        # Convert to numpy arrays for easier calculation
        balances = np.array(balance_history)
        returns = np.diff(balances) / balances[:-1]

        # Basic metrics
        initial_balance = balances[0]
        final_balance = balances[-1]
        peak_balance = np.max(balances)
        total_return = (final_balance - initial_balance) / initial_balance

        # Time-based calculations
        total_days = (timestamps[-1] - timestamps[0]).days
        years = total_days / 365.25

        # Annualized return and volatility
        if years > 0:
            annualized_return = (final_balance / initial_balance) ** (1 / years) - 1
            volatility = np.std(returns) * np.sqrt(252)  # Assuming daily data
        else:
            annualized_return = 0.0
            volatility = 0.0

        # Risk-adjusted metrics
        excess_returns = returns - (self.risk_free_rate / 252)
        sharpe_ratio = np.mean(excess_returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0.0

        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.0
        sortino_ratio = np.mean(excess_returns) / downside_std * np.sqrt(252) if downside_std > 0 else 0.0

        # Drawdown analysis
        peak_values = np.maximum.accumulate(balances)
        drawdowns = (balances - peak_values) / peak_values
        max_drawdown = np.min(drawdowns)

        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0.0

        # Trade statistics
        trade_stats = self._calculate_trade_statistics(trade_history)

        # Risk metrics
        var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0.0
        cvar_95 = np.mean(returns[returns <= var_95]) if len(returns[returns <= var_95]) > 0 else 0.0

        # Beta and Alpha (if benchmark provided)
        beta, alpha = self._calculate_beta_alpha(returns, benchmark_returns) if benchmark_returns else (0.0, 0.0)

        # Advanced metrics
        information_ratio = self._calculate_information_ratio(returns, benchmark_returns)
        treynor_ratio = (annualized_return - self.risk_free_rate) / beta if beta != 0 else 0.0
        omega_ratio = self._calculate_omega_ratio(returns)
        tail_ratio = self._calculate_tail_ratio(returns)

        # Timing metrics
        timing_stats = self._calculate_timing_statistics(trade_history, timestamps)

        # Distribution metrics
        skewness = self._calculate_skewness(returns)
        kurtosis = self._calculate_kurtosis(returns)
        stability = self._calculate_stability(returns)

        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            total_trades=trade_stats['total_trades'],
            winning_trades=trade_stats['winning_trades'],
            losing_trades=trade_stats['losing_trades'],
            win_rate=trade_stats['win_rate'],
            avg_win=trade_stats['avg_win'],
            avg_loss=trade_stats['avg_loss'],
            profit_factor=trade_stats['profit_factor'],
            var_95=var_95,
            cvar_95=cvar_95,
            beta=beta,
            alpha=alpha,
            information_ratio=information_ratio,
            treynor_ratio=treynor_ratio,
            omega_ratio=omega_ratio,
            tail_ratio=tail_ratio,
            avg_trade_duration=timing_stats['avg_duration'],
            max_trade_duration=timing_stats['max_duration'],
            min_trade_duration=timing_stats['min_duration'],
            initial_balance=initial_balance,
            final_balance=final_balance,
            peak_balance=peak_balance,
            skewness=skewness,
            kurtosis=kurtosis,
            stability=stability
        )

    def _create_empty_metrics(self) -> PerformanceMetrics:
        """Create empty metrics for insufficient data cases."""
        return PerformanceMetrics(
            total_return=0.0, annualized_return=0.0, volatility=0.0,
            sharpe_ratio=0.0, sortino_ratio=0.0, max_drawdown=0.0,
            calmar_ratio=0.0, total_trades=0, winning_trades=0,
            losing_trades=0, win_rate=0.0, avg_win=0.0, avg_loss=0.0,
            profit_factor=0.0, var_95=0.0, cvar_95=0.0, beta=0.0,
            alpha=0.0, information_ratio=0.0, treynor_ratio=0.0,
            omega_ratio=0.0, tail_ratio=0.0, avg_trade_duration=0.0,
            max_trade_duration=0.0, min_trade_duration=0.0,
            initial_balance=0.0, final_balance=0.0, peak_balance=0.0,
            skewness=0.0, kurtosis=0.0, stability=0.0
        )

    def _calculate_trade_statistics(self, trade_history: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate trade-level statistics."""
        if not trade_history:
            return {
                'total_trades': 0, 'winning_trades': 0, 'losing_trades': 0,
                'win_rate': 0.0, 'avg_win': 0.0, 'avg_loss': 0.0, 'profit_factor': 0.0
            }

        total_trades = len(trade_history)
        winning_trades = sum(1 for trade in trade_history if trade.get('pnl', 0) > 0)
        losing_trades = sum(1 for trade in trade_history if trade.get('pnl', 0) < 0)

        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

        wins = [trade['pnl'] for trade in trade_history if trade.get('pnl', 0) > 0]
        losses = [trade['pnl'] for trade in trade_history if trade.get('pnl', 0) < 0]

        avg_win = np.mean(wins) if wins else 0.0
        avg_loss = np.mean(losses) if losses else 0.0

        total_wins = sum(wins) if wins else 0.0
        total_losses = abs(sum(losses)) if losses else 0.0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0.0

        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor
        }

    def _calculate_beta_alpha(self, returns: np.ndarray, benchmark_returns: Optional[List[float]]) -> Tuple[float, float]:
        """Calculate beta and alpha relative to benchmark."""
        if benchmark_returns is None or len(benchmark_returns) != len(returns):
            return 0.0, 0.0

        benchmark_array = np.array(benchmark_returns)

        # Calculate beta using linear regression
        covariance = np.cov(returns, benchmark_array)[0, 1]
        benchmark_variance = np.var(benchmark_array)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0.0

        # Calculate alpha
        portfolio_return = np.mean(returns) * 252  # Annualized
        benchmark_return = np.mean(benchmark_array) * 252  # Annualized
        alpha = portfolio_return - (self.risk_free_rate + beta * (benchmark_return - self.risk_free_rate))

        return beta, alpha

    def _calculate_information_ratio(self, returns: np.ndarray, benchmark_returns: Optional[List[float]]) -> float:
        """Calculate information ratio (excess return / tracking error)."""
        if benchmark_returns is None or len(benchmark_returns) != len(returns):
            return 0.0

        benchmark_array = np.array(benchmark_returns)
        excess_returns = returns - benchmark_array
        tracking_error = np.std(excess_returns)

        return np.mean(excess_returns) / tracking_error * np.sqrt(252) if tracking_error > 0 else 0.0

    def _calculate_omega_ratio(self, returns: np.ndarray, threshold: float = 0.0) -> float:
        """Calculate Omega ratio (probability-weighted ratio of gains to losses)."""
        if len(returns) == 0:
            return 0.0

        gains = returns[returns > threshold] - threshold
        losses = threshold - returns[returns <= threshold]

        total_gains = np.sum(gains) if len(gains) > 0 else 0.0
        total_losses = np.sum(losses) if len(losses) > 0 else 0.0

        return total_gains / total_losses if total_losses > 0 else 0.0

    def _calculate_tail_ratio(self, returns: np.ndarray) -> float:
        """Calculate tail ratio (95th percentile / 5th percentile)."""
        if len(returns) == 0:
            return 0.0

        p95 = np.percentile(returns, 95)
        p5 = np.percentile(returns, 5)

        return abs(p95 / p5) if p5 != 0 else 0.0

    def _calculate_timing_statistics(self, trade_history: List[Dict[str, Any]], timestamps: List[datetime]) -> Dict[str, float]:
        """Calculate timing-related statistics."""
        if not trade_history:
            return {'avg_duration': 0.0, 'max_duration': 0.0, 'min_duration': 0.0}

        durations = []
        for trade in trade_history:
            if 'entry_time' in trade and 'exit_time' in trade:
                entry_time = trade['entry_time']
                exit_time = trade['exit_time']
                if isinstance(entry_time, str):
                    entry_time = datetime.fromisoformat(entry_time)
                if isinstance(exit_time, str):
                    exit_time = datetime.fromisoformat(exit_time)

                duration = (exit_time - entry_time).total_seconds() / 3600  # Hours
                durations.append(duration)

        if not durations:
            return {'avg_duration': 0.0, 'max_duration': 0.0, 'min_duration': 0.0}

        return {
            'avg_duration': np.mean(durations),
            'max_duration': np.max(durations),
            'min_duration': np.min(durations)
        }

    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """Calculate skewness of returns."""
        if len(returns) < 3:
            return 0.0

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        skewness = np.mean(((returns - mean_return) / std_return) ** 3)
        return skewness

    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate kurtosis of returns."""
        if len(returns) < 4:
            return 0.0

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        kurtosis = np.mean(((returns - mean_return) / std_return) ** 4) - 3
        return kurtosis

    def _calculate_stability(self, returns: np.ndarray) -> float:
        """Calculate stability of returns (R-squared of linear regression against time)."""
        if len(returns) < 2:
            return 0.0

        cumulative_returns = np.cumprod(1 + returns)
        time_index = np.arange(len(cumulative_returns))

        # Linear regression
        correlation_matrix = np.corrcoef(time_index, cumulative_returns)
        correlation = correlation_matrix[0, 1]

        return correlation ** 2 if not np.isnan(correlation) else 0.0

    def generate_performance_report(self, metrics: PerformanceMetrics) -> str:
        """Generate a comprehensive performance report."""
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE PERFORMANCE REPORT")
        report.append("=" * 80)

        # Basic Performance
        report.append("\n📊 BASIC PERFORMANCE METRICS")
        report.append("-" * 40)
        report.append(f"Initial Balance:      ${metrics.initial_balance:,.2f}")
        report.append(f"Final Balance:        ${metrics.final_balance:,.2f}")
        report.append(f"Peak Balance:         ${metrics.peak_balance:,.2f}")
        report.append(f"Total Return:         {metrics.total_return:.2%}")
        report.append(f"Annualized Return:    {metrics.annualized_return:.2%}")
        report.append(f"Volatility:           {metrics.volatility:.2%}")

        # Risk-Adjusted Metrics
        report.append("\n⚖️ RISK-ADJUSTED METRICS")
        report.append("-" * 40)
        report.append(f"Sharpe Ratio:         {metrics.sharpe_ratio:.3f}")
        report.append(f"Sortino Ratio:        {metrics.sortino_ratio:.3f}")
        report.append(f"Calmar Ratio:         {metrics.calmar_ratio:.3f}")
        report.append(f"Information Ratio:    {metrics.information_ratio:.3f}")
        report.append(f"Treynor Ratio:        {metrics.treynor_ratio:.3f}")
        report.append(f"Omega Ratio:          {metrics.omega_ratio:.3f}")

        # Risk Metrics
        report.append("\n🚨 RISK METRICS")
        report.append("-" * 40)
        report.append(f"Maximum Drawdown:     {metrics.max_drawdown:.2%}")
        report.append(f"Value at Risk (95%):  {metrics.var_95:.2%}")
        report.append(f"CVaR (95%):           {metrics.cvar_95:.2%}")
        report.append(f"Beta:                 {metrics.beta:.3f}")
        report.append(f"Alpha:                {metrics.alpha:.2%}")
        report.append(f"Tail Ratio:           {metrics.tail_ratio:.3f}")

        # Trade Statistics
        report.append("\n📈 TRADE STATISTICS")
        report.append("-" * 40)
        report.append(f"Total Trades:         {metrics.total_trades}")
        report.append(f"Winning Trades:       {metrics.winning_trades}")
        report.append(f"Losing Trades:        {metrics.losing_trades}")
        report.append(f"Win Rate:             {metrics.win_rate:.2%}")
        report.append(f"Average Win:          ${metrics.avg_win:.2f}")
        report.append(f"Average Loss:         ${metrics.avg_loss:.2f}")
        report.append(f"Profit Factor:        {metrics.profit_factor:.3f}")

        # Timing Statistics
        report.append("\n⏱️ TIMING STATISTICS")
        report.append("-" * 40)
        report.append(f"Avg Trade Duration:   {metrics.avg_trade_duration:.1f} hours")
        report.append(f"Max Trade Duration:   {metrics.max_trade_duration:.1f} hours")
        report.append(f"Min Trade Duration:   {metrics.min_trade_duration:.1f} hours")

        # Distribution Statistics
        report.append("\n📊 DISTRIBUTION STATISTICS")
        report.append("-" * 40)
        report.append(f"Skewness:             {metrics.skewness:.3f}")
        report.append(f"Kurtosis:             {metrics.kurtosis:.3f}")
        report.append(f"Stability:            {metrics.stability:.3f}")

        # Performance Rating
        report.append("\n🏆 PERFORMANCE RATING")
        report.append("-" * 40)
        rating = self._calculate_performance_rating(metrics)
        report.append(f"Overall Rating:       {rating['score']:.1f}/10 ({rating['grade']})")
        report.append(f"Rating Breakdown:")
        for component, score in rating['breakdown'].items():
            report.append(f"  {component:15}: {score:.1f}/10")

        report.append("\n" + "=" * 80)

        return "\n".join(report)

    def _calculate_performance_rating(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Calculate an overall performance rating."""
        scores = {}

        # Return score (0-10)
        if metrics.annualized_return > 0.20:
            scores['Returns'] = 10
        elif metrics.annualized_return > 0.15:
            scores['Returns'] = 8
        elif metrics.annualized_return > 0.10:
            scores['Returns'] = 6
        elif metrics.annualized_return > 0.05:
            scores['Returns'] = 4
        elif metrics.annualized_return > 0:
            scores['Returns'] = 2
        else:
            scores['Returns'] = 0

        # Risk score (0-10, higher is better)
        if metrics.sharpe_ratio > 2.0:
            scores['Risk-Adjusted'] = 10
        elif metrics.sharpe_ratio > 1.5:
            scores['Risk-Adjusted'] = 8
        elif metrics.sharpe_ratio > 1.0:
            scores['Risk-Adjusted'] = 6
        elif metrics.sharpe_ratio > 0.5:
            scores['Risk-Adjusted'] = 4
        elif metrics.sharpe_ratio > 0:
            scores['Risk-Adjusted'] = 2
        else:
            scores['Risk-Adjusted'] = 0

        # Drawdown score (0-10, lower drawdown is better)
        if abs(metrics.max_drawdown) < 0.05:
            scores['Drawdown'] = 10
        elif abs(metrics.max_drawdown) < 0.10:
            scores['Drawdown'] = 8
        elif abs(metrics.max_drawdown) < 0.20:
            scores['Drawdown'] = 6
        elif abs(metrics.max_drawdown) < 0.30:
            scores['Drawdown'] = 4
        elif abs(metrics.max_drawdown) < 0.50:
            scores['Drawdown'] = 2
        else:
            scores['Drawdown'] = 0

        # Consistency score (0-10)
        if metrics.win_rate > 0.70:
            scores['Consistency'] = 10
        elif metrics.win_rate > 0.60:
            scores['Consistency'] = 8
        elif metrics.win_rate > 0.50:
            scores['Consistency'] = 6
        elif metrics.win_rate > 0.40:
            scores['Consistency'] = 4
        elif metrics.win_rate > 0.30:
            scores['Consistency'] = 2
        else:
            scores['Consistency'] = 0

        # Overall score
        overall_score = np.mean(list(scores.values()))

        # Grade
        if overall_score >= 8:
            grade = "A"
        elif overall_score >= 6:
            grade = "B"
        elif overall_score >= 4:
            grade = "C"
        elif overall_score >= 2:
            grade = "D"
        else:
            grade = "F"

        return {
            'score': overall_score,
            'grade': grade,
            'breakdown': scores
        }
