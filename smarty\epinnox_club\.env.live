# Money Circle Live Trading Environment Configuration
# Copy this to .env to enable live trading mode

# Live Trading Mode
LIVE_TRADING_MODE=true

# HTX Futures API Credentials (EPX Account)
HTX_API_KEY=nbtycf4rw2-72d300ec-fb900970-27ef8
HTX_SECRET=b4d92e15-523563a0-72a16ad9-9a275
HTX_PASSPHRASE=

# Server Configuration
HOST=localhost
PORT=8086
DEBUG=false

# Database Configuration
DATABASE_PATH=data/money_circle.db
SQLITE_BUS_PATH=data/bus.db

# Security Configuration
SECRET_KEY=your-secret-key-here
SESSION_TIMEOUT=86400

# Trading Configuration
MAX_POSITION_SIZE=1000.0
RISK_LIMIT_PERCENT=2.0
AUTO_TRADER_ENABLED=true
SIGNALS_ENABLED=true

# Exchange Configuration
BINANCE_ENABLED=true
HTX_ENABLED=true
BYBIT_ENABLED=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/money_circle_live.log

# Safety Configuration
EMERGENCY_STOP_ENABLED=true
MAX_DAILY_LOSS=100.0
POSITION_SIZE_LIMIT=500.0

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true
