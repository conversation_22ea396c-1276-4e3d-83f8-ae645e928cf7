"""
Enhanced LLM Consumer for the smart-trader system.

This consumer integrates with the Enhanced LLM Manager to provide:
- Robust signal processing and decision making
- Account state integration for context-aware decisions
- Performance monitoring and health checks
- Adaptive behavior based on market conditions
"""

import asyncio
import logging
import yaml
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional

from .enhanced_llm_manager import EnhancedLLMManager, LLMResponse, LLMAction

logger = logging.getLogger(__name__)


class EnhancedLLMConsumer:
    """
    Enhanced LLM Consumer with robust error handling and monitoring.
    """

    def __init__(self, bus, feature_store, config: Dict[str, Any]):
        """
        Initialize the Enhanced LLM Consumer.

        Args:
            bus: Message bus instance
            feature_store: Feature store instance
            config: Configuration dictionary
        """
        self.bus = bus
        self.feature_store = feature_store
        self.config = config

        # LLM configuration
        self.llm_config = self._extract_llm_config(config)

        # Load prompt template
        self.prompt_template = self._load_prompt_template()

        # Initialize LLM manager
        self.llm_manager = EnhancedLLMManager(
            model_path=self.llm_config["model_path"],
            prompt_template=self.prompt_template,
            config=self.llm_config,
            dummy_mode=self.llm_config.get("dummy_mode", False)
        )

        # Consumer state
        self.running = False
        self.last_signal_time = {}
        self.last_account_update = None
        self.cached_account_state = {}

        # Performance tracking
        self.signals_processed = 0
        self.decisions_made = 0
        self.last_health_check = datetime.now()

        logger.info("Enhanced LLM Consumer initialized")

    def _extract_llm_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract LLM-specific configuration."""
        return {
            "model_path": config.get("model_path", config.get("llm_model_path", "")),
            "prompt_path": config.get("prompt_path", config.get("llm_prompt_path", "")),
            "n_ctx": config.get("n_ctx", 2048),
            "n_threads": config.get("n_threads", 4),
            "n_gpu_layers": config.get("n_gpu_layers", 0),
            "max_tokens": config.get("max_tokens", 128),
            "temperature": config.get("temperature", 0.0),
            "call_interval_s": config.get("call_interval_s", 30),
            "dummy_mode": config.get("dummy_mode", config.get("dummy_llm", False)),
            "adaptive_throttle": config.get("adaptive_throttle", True),
            "min_throttle_interval": config.get("min_throttle_interval", 10),
            "max_throttle_interval": config.get("max_throttle_interval", 120),
            "max_memory_size": config.get("max_memory_size", 10),
            "verbose": config.get("debug", {}).get("enabled", False)
        }

    def _load_prompt_template(self) -> Dict[str, Any]:
        """Load the prompt template from YAML file."""
        try:
            prompt_path = self.llm_config.get("prompt_path", "llm/prompts/trading_prompt_phi.yaml")

            with open(prompt_path, 'r', encoding='utf-8') as f:
                template = yaml.safe_load(f)

            logger.info(f"Loaded prompt template from {prompt_path}")
            return template

        except Exception as e:
            logger.error(f"Failed to load prompt template: {e}")
            # Return fallback template
            return self._get_fallback_template()

    def _get_fallback_template(self) -> Dict[str, Any]:
        """Get fallback prompt template."""
        return {
            "name": "fallback_template",
            "template": """
            <|system|>
            You are a trading assistant. Analyze the market data and respond with JSON only.
            <|user|>
            Symbol: {symbol}
            Account Balance: {account_balance} USDT
            Positions: {positions_summary}
            Market Conditions: {market_conditions}
            Signals: {signals}

            Respond with JSON in this exact format:
            {{"action": "HOLD", "confidence": 0.5, "rationale": "Your reasoning"}}
            <|assistant|>
            """
        }

    async def start(self):
        """Start the LLM consumer."""
        if self.running:
            logger.warning("LLM Consumer already running")
            return

        self.running = True
        logger.info("Starting Enhanced LLM Consumer...")

        # Subscribe to relevant channels
        await self._subscribe_to_channels()

        # Start background tasks
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._account_update_loop())

        logger.info("Enhanced LLM Consumer started successfully")

    async def stop(self):
        """Stop the LLM consumer."""
        self.running = False
        logger.info("Enhanced LLM Consumer stopped")

    async def _subscribe_to_channels(self):
        """Subscribe to message bus channels."""
        try:
            # Subscribe to fused signals (bus.subscribe is not async)
            self.bus.subscribe("signals.fused", self._handle_fused_signal_sync)

            # Subscribe to account updates (bus.subscribe is not async)
            self.bus.subscribe("account.update", self._handle_account_update_sync)

            logger.info("Subscribed to message bus channels")

        except Exception as e:
            logger.error(f"Failed to subscribe to channels: {e}")

    def _handle_fused_signal_sync(self, timestamp: float, data: Dict[str, Any]):
        """Synchronous wrapper for fused signal handler."""
        try:
            # Create a task to handle the signal asynchronously
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self._handle_fused_signal_async(timestamp, data))
            else:
                asyncio.run(self._handle_fused_signal_async(timestamp, data))
        except Exception as e:
            logger.error(f"Error in sync fused signal handler: {e}")

    def _handle_account_update_sync(self, timestamp: float, data: Dict[str, Any]):
        """Synchronous wrapper for account update handler."""
        try:
            # Create a task to handle the update asynchronously
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self._handle_account_update_async(timestamp, data))
            else:
                asyncio.run(self._handle_account_update_async(timestamp, data))
        except Exception as e:
            logger.error(f"Error in sync account update handler: {e}")

    async def _handle_fused_signal_async(self, timestamp: float, data: Dict[str, Any]):
        """Handle incoming fused signals."""
        try:
            self.signals_processed += 1
            symbol = data.get("symbol", "BTC-USDT")

            logger.debug(f"Processing fused signal for {symbol}")

            # Check if we should process this signal
            if not self._should_process_signal(symbol, timestamp):
                return

            # Update last signal time
            self.last_signal_time[symbol] = timestamp

            # Prepare context for LLM
            context = await self._prepare_context(symbol, data)

            # Generate LLM decision
            response = await self.llm_manager.generate_decision(context)

            # Publish LLM decision
            await self._publish_decision(symbol, response, data)

            self.decisions_made += 1

        except Exception as e:
            logger.error(f"Error handling fused signal: {e}")

    async def _handle_account_update_async(self, timestamp: float, data: Dict[str, Any]):
        """Handle account state updates."""
        try:
            self.cached_account_state = data
            self.last_account_update = datetime.now()
            logger.debug("Account state updated")

        except Exception as e:
            logger.error(f"Error handling account update: {e}")

    def _should_process_signal(self, symbol: str, timestamp: float) -> bool:
        """Check if we should process this signal based on throttling."""
        last_time = self.last_signal_time.get(symbol, 0)
        min_interval = self.llm_config["call_interval_s"]

        return (timestamp - last_time) >= min_interval

    async def _prepare_context(self, symbol: str, fused_signal: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare context for LLM decision making."""
        try:
            # Get account information
            account_balance = self.cached_account_state.get("total_balance", 0.0)

            # Get current positions
            positions = await self._get_positions(symbol)
            positions_summary = self._format_positions(positions)

            # Get market conditions
            market_conditions = await self._get_market_conditions(symbol)

            # Format signals
            signals = self._format_signals(fused_signal)

            # Get ensemble data if available
            ensemble_data = await self._get_ensemble_data(symbol)

            context = {
                "symbol": symbol,
                "account_balance": account_balance,
                "positions_summary": positions_summary,
                "market_conditions": market_conditions,
                "signals": signals,
                "ensemble_action": ensemble_data.get("action", "HOLD"),
                "ensemble_score": ensemble_data.get("score", 0.0),
                "ensemble_confidence_lower": ensemble_data.get("confidence_lower", 0.0),
                "ensemble_confidence_upper": ensemble_data.get("confidence_upper", 1.0),
                "timestamp": datetime.now().isoformat()
            }

            return context

        except Exception as e:
            logger.error(f"Error preparing context: {e}")
            # Return minimal context
            return {
                "symbol": symbol,
                "account_balance": 0.0,
                "positions_summary": "No positions",
                "market_conditions": "Unknown",
                "signals": str(fused_signal),
                "ensemble_action": "HOLD",
                "ensemble_score": 0.0,
                "ensemble_confidence_lower": 0.0,
                "ensemble_confidence_upper": 1.0
            }

    async def _get_positions(self, symbol: str) -> List[Dict[str, Any]]:
        """Get current positions for the symbol."""
        try:
            # Try to get from feature store
            positions_data = await self.feature_store.get(symbol, "account.positions")
            if positions_data:
                return positions_data if isinstance(positions_data, list) else [positions_data]

            # Fallback to cached account state
            positions = self.cached_account_state.get("positions", [])
            return [p for p in positions if p.get("symbol") == symbol]

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []

    def _format_positions(self, positions: List[Dict[str, Any]]) -> str:
        """Format positions for LLM context."""
        if not positions:
            return "No open positions"

        formatted = []
        for pos in positions:
            side = pos.get("side", "unknown")
            size = pos.get("size", 0)
            entry_price = pos.get("entry_price", 0)
            pnl = pos.get("unrealized_pnl", 0)

            formatted.append(f"{side} {size} @ {entry_price} (PnL: {pnl:.2f})")

        return "; ".join(formatted)

    async def _get_market_conditions(self, symbol: str) -> str:
        """Get current market conditions."""
        try:
            conditions = []

            # Get volatility
            volatility = await self.feature_store.get(symbol, "volatility.current")
            if volatility:
                conditions.append(f"Volatility: {volatility:.4f}")

            # Get funding rate
            funding = await self.feature_store.get(symbol, "funding.rate")
            if funding:
                conditions.append(f"Funding: {funding:.6f}")

            # Get price trend
            price_trend = await self.feature_store.get(symbol, "price.trend")
            if price_trend:
                conditions.append(f"Trend: {price_trend}")

            return "; ".join(conditions) if conditions else "Market conditions unknown"

        except Exception as e:
            logger.error(f"Error getting market conditions: {e}")
            return "Market conditions unknown"

    def _format_signals(self, fused_signal: Dict[str, Any]) -> str:
        """Format signals for LLM context."""
        try:
            signals = []

            # Extract key signal information
            action = fused_signal.get("action", "HOLD")
            confidence = fused_signal.get("confidence", 0.0)
            signals.append(f"Fused: {action} (confidence: {confidence:.2f})")

            # Add individual model signals if available
            models = fused_signal.get("models", {})
            for model_name, model_data in models.items():
                if isinstance(model_data, dict):
                    model_action = model_data.get("action", "HOLD")
                    model_score = model_data.get("score", 0.0)
                    signals.append(f"{model_name}: {model_action} ({model_score:.2f})")

            return "; ".join(signals)

        except Exception as e:
            logger.error(f"Error formatting signals: {e}")
            return str(fused_signal)

    async def _get_ensemble_data(self, symbol: str) -> Dict[str, Any]:
        """Get ensemble model data."""
        try:
            ensemble_data = await self.feature_store.get(symbol, "ensemble.prediction")
            if ensemble_data:
                return ensemble_data

            # Fallback
            return {
                "action": "HOLD",
                "score": 0.0,
                "confidence_lower": 0.0,
                "confidence_upper": 1.0
            }

        except Exception as e:
            logger.error(f"Error getting ensemble data: {e}")
            return {
                "action": "HOLD",
                "score": 0.0,
                "confidence_lower": 0.0,
                "confidence_upper": 1.0
            }

    async def _publish_decision(self, symbol: str, response: LLMResponse, original_signal: Dict[str, Any]):
        """Publish LLM decision to message bus."""
        try:
            # Create decision message
            decision = {
                "symbol": symbol,
                "action": response.action.value,
                "confidence": response.confidence,
                "rationale": response.rationale,
                "timestamp": response.timestamp.isoformat(),
                "processing_time": response.processing_time,
                "source": "enhanced_llm",
                "original_signal": original_signal
            }

            # Publish to message bus (bus.publish is not async)
            self.bus.publish("signals.llm", response.timestamp.timestamp(), decision)

            # Store in feature store
            await self.feature_store.set(symbol, "llm.latest_decision", decision)

            logger.info(f"Published LLM decision: {response.action.value} for {symbol}")

        except Exception as e:
            logger.error(f"Error publishing decision: {e}")

    async def _health_check_loop(self):
        """Periodic health check loop."""
        while self.running:
            try:
                await asyncio.sleep(60)  # Check every minute

                # Get health status
                health = self.llm_manager.get_health_status()

                # Log health status
                if not health["is_healthy"]:
                    logger.warning(f"LLM health check failed: {health}")
                else:
                    logger.debug("LLM health check passed")

                # Store health metrics
                await self.feature_store.set("system", "llm.health", health)

                self.last_health_check = datetime.now()

            except Exception as e:
                logger.error(f"Error in health check loop: {e}")

    async def _account_update_loop(self):
        """Periodic account update loop."""
        while self.running:
            try:
                await asyncio.sleep(30)  # Update every 30 seconds

                # Check if account data is stale
                if (self.last_account_update and
                    datetime.now() - self.last_account_update > timedelta(minutes=5)):
                    logger.warning("Account data is stale")

            except Exception as e:
                logger.error(f"Error in account update loop: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get consumer status and metrics."""
        return {
            "running": self.running,
            "signals_processed": self.signals_processed,
            "decisions_made": self.decisions_made,
            "last_health_check": self.last_health_check.isoformat(),
            "last_account_update": self.last_account_update.isoformat() if self.last_account_update else None,
            "llm_health": self.llm_manager.get_health_status(),
            "symbols_tracked": list(self.last_signal_time.keys())
        }
