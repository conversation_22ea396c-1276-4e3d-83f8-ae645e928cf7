#!/usr/bin/env python3
"""
Enhanced LLM Integration Runner for Smart-Trader System.

This script demonstrates the enhanced LLM integration with:
- Robust JSON parsing and validation
- Performance monitoring and health checks
- Adaptive throttling and error handling
- Context memory and decision tracking
"""

import asyncio
import logging
import yaml
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.utils.logging_utils import setup_logging
from core.feature_store import FeatureStore
from llm.enhanced_llm_consumer import EnhancedLLMConsumer


class MockMessageBus:
    """Mock message bus for testing the enhanced LLM integration."""
    
    def __init__(self):
        self.subscribers = {}
        self.published_messages = []
    
    async def subscribe(self, channel: str, callback):
        """Subscribe to a channel."""
        if channel not in self.subscribers:
            self.subscribers[channel] = []
        self.subscribers[channel].append(callback)
        print(f"Subscribed to channel: {channel}")
    
    async def publish(self, channel: str, timestamp: float, data: Dict[str, Any]):
        """Publish a message to a channel."""
        message = {
            "channel": channel,
            "timestamp": timestamp,
            "data": data
        }
        self.published_messages.append(message)
        print(f"Published to {channel}: {data}")
        
        # Notify subscribers
        if channel in self.subscribers:
            for callback in self.subscribers[channel]:
                try:
                    await callback(channel, timestamp, data)
                except Exception as e:
                    print(f"Error in subscriber callback: {e}")


async def create_test_signals():
    """Create test signals for LLM processing."""
    return [
        {
            "symbol": "BTC-USDT",
            "action": "BUY",
            "confidence": 0.75,
            "models": {
                "rsi": {"action": "BUY", "score": 0.8},
                "vwap": {"action": "BUY", "score": 0.7},
                "volatility": {"action": "HOLD", "score": 0.5},
                "funding": {"action": "BUY", "score": 0.9}
            },
            "timestamp": datetime.now().isoformat()
        },
        {
            "symbol": "BTC-USDT",
            "action": "SELL",
            "confidence": 0.65,
            "models": {
                "rsi": {"action": "SELL", "score": -0.6},
                "vwap": {"action": "SELL", "score": -0.7},
                "volatility": {"action": "HOLD", "score": 0.3},
                "funding": {"action": "SELL", "score": -0.8}
            },
            "timestamp": datetime.now().isoformat()
        },
        {
            "symbol": "BTC-USDT",
            "action": "HOLD",
            "confidence": 0.45,
            "models": {
                "rsi": {"action": "HOLD", "score": 0.1},
                "vwap": {"action": "BUY", "score": 0.3},
                "volatility": {"action": "SELL", "score": -0.2},
                "funding": {"action": "HOLD", "score": 0.0}
            },
            "timestamp": datetime.now().isoformat()
        }
    ]


async def setup_test_environment():
    """Set up the test environment with mock data."""
    # Initialize feature store
    feature_store = FeatureStore()
    
    # Add test data to feature store
    symbol = "BTC-USDT"
    
    # Market conditions
    await feature_store.set(symbol, "volatility.current", 0.025)
    await feature_store.set(symbol, "funding.rate", 0.0001)
    await feature_store.set(symbol, "price.trend", "bullish")
    
    # Account data
    await feature_store.set(symbol, "account.positions", [])
    
    # Ensemble data
    await feature_store.set(symbol, "ensemble.prediction", {
        "action": "BUY",
        "score": 0.7,
        "confidence_lower": 0.6,
        "confidence_upper": 0.8
    })
    
    return feature_store


async def test_enhanced_llm():
    """Test the enhanced LLM integration."""
    print("🧠 Testing Enhanced LLM Integration")
    print("=" * 50)
    
    # Setup logging
    logger = setup_logging(level=logging.INFO)
    
    # Load configuration
    try:
        with open("config.yaml", "r") as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        # Use fallback config
        config = {
            "llm": {
                "model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf",
                "prompt_path": "llm/prompts/trading_prompt_phi.yaml",
                "dummy_mode": True,  # Use dummy mode for testing
                "call_interval_s": 5,
                "adaptive_throttle": True
            }
        }
    
    # Set dummy mode for testing
    config["llm"]["dummy_mode"] = True
    config["dummy_mode"] = True
    
    # Setup test environment
    print("Setting up test environment...")
    feature_store = await setup_test_environment()
    message_bus = MockMessageBus()
    
    # Initialize enhanced LLM consumer
    print("Initializing Enhanced LLM Consumer...")
    llm_consumer = EnhancedLLMConsumer(message_bus, feature_store, config)
    
    # Start the consumer
    print("Starting LLM Consumer...")
    await llm_consumer.start()
    
    # Create test signals
    test_signals = await create_test_signals()
    
    print(f"\nProcessing {len(test_signals)} test signals...")
    print("-" * 30)
    
    # Process each test signal
    for i, signal in enumerate(test_signals, 1):
        print(f"\n📊 Test Signal {i}:")
        print(f"   Action: {signal['action']}")
        print(f"   Confidence: {signal['confidence']}")
        print(f"   Models: {len(signal['models'])} signals")
        
        # Simulate account update
        account_data = {
            "total_balance": 100.0,
            "available_balance": 90.0,
            "positions": []
        }
        await message_bus.publish("account.update", datetime.now().timestamp(), account_data)
        
        # Send fused signal
        await message_bus.publish("signals.fused", datetime.now().timestamp(), signal)
        
        # Wait a bit for processing
        await asyncio.sleep(2)
        
        # Check for LLM response
        llm_messages = [msg for msg in message_bus.published_messages if msg["channel"] == "signals.llm"]
        if llm_messages:
            latest_llm = llm_messages[-1]["data"]
            print(f"   🧠 LLM Decision: {latest_llm['action']} (confidence: {latest_llm['confidence']:.2f})")
            print(f"   📝 Rationale: {latest_llm['rationale']}")
            print(f"   ⏱️  Processing Time: {latest_llm['processing_time']:.3f}s")
        else:
            print("   ❌ No LLM response received")
    
    # Get final status
    print(f"\n📈 Final Status:")
    print("-" * 20)
    status = llm_consumer.get_status()
    print(f"   Signals Processed: {status['signals_processed']}")
    print(f"   Decisions Made: {status['decisions_made']}")
    print(f"   LLM Health: {'✅ Healthy' if status['llm_health']['is_healthy'] else '❌ Unhealthy'}")
    print(f"   Dummy Mode: {status['llm_health']['dummy_mode']}")
    
    # Show LLM metrics
    metrics = status['llm_health']['metrics']
    print(f"\n📊 LLM Metrics:")
    print(f"   Total Calls: {metrics['total_calls']}")
    print(f"   Success Rate: {(metrics['successful_calls'] / max(1, metrics['total_calls']) * 100):.1f}%")
    print(f"   Avg Latency: {metrics['avg_latency']:.3f}s")
    print(f"   Avg Confidence: {metrics['avg_confidence']:.2f}")
    
    # Show throttling info
    throttling = status['llm_health']['throttling']
    print(f"\n⏱️  Throttling:")
    print(f"   Current Interval: {throttling['current_throttle']:.1f}s")
    print(f"   Range: {throttling['min_throttle']:.1f}s - {throttling['max_throttle']:.1f}s")
    
    # Show memory usage
    memory = status['llm_health']['memory']
    print(f"\n🧠 Memory:")
    print(f"   Entries: {memory['entries']}/{memory['max_size']}")
    
    # Stop the consumer
    await llm_consumer.stop()
    
    print(f"\n✅ Enhanced LLM Integration Test Completed!")
    print(f"   Published {len(message_bus.published_messages)} messages")
    print(f"   LLM responses: {len([m for m in message_bus.published_messages if m['channel'] == 'signals.llm'])}")


async def test_llm_manager_directly():
    """Test the LLM manager directly."""
    print("\n🔧 Testing LLM Manager Directly")
    print("=" * 40)
    
    from llm.enhanced_llm_manager import EnhancedLLMManager
    
    # Load prompt template
    try:
        with open("llm/prompts/trading_prompt_phi.yaml", "r") as f:
            prompt_template = yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading prompt template: {e}")
        return
    
    # Create LLM manager in dummy mode
    config = {
        "dummy_mode": True,
        "call_interval_s": 1,
        "adaptive_throttle": True,
        "min_throttle_interval": 1,
        "max_throttle_interval": 10
    }
    
    llm_manager = EnhancedLLMManager(
        model_path="dummy_path",
        prompt_template=prompt_template,
        config=config,
        dummy_mode=True
    )
    
    # Test context
    test_context = {
        "symbol": "BTC-USDT",
        "account_balance": 100.0,
        "positions_summary": "No positions",
        "market_conditions": "Bullish trend, low volatility",
        "signals": "RSI: BUY (0.8), VWAP: BUY (0.7), Volatility: HOLD (0.5)",
        "ensemble_action": "BUY",
        "ensemble_score": 0.75,
        "ensemble_confidence_lower": 0.6,
        "ensemble_confidence_upper": 0.9,
        "timestamp": datetime.now().isoformat()
    }
    
    print("Generating LLM decisions...")
    
    # Generate multiple decisions to test adaptive throttling
    for i in range(3):
        print(f"\nDecision {i+1}:")
        response = await llm_manager.generate_decision(test_context)
        
        print(f"   Action: {response.action.value}")
        print(f"   Confidence: {response.confidence:.2f}")
        print(f"   Rationale: {response.rationale}")
        print(f"   Processing Time: {response.processing_time:.3f}s")
        
        # Modify context for next iteration
        test_context["signals"] = f"Modified signals iteration {i+2}"
        
        await asyncio.sleep(0.5)
    
    # Show health status
    health = llm_manager.get_health_status()
    print(f"\nHealth Status: {'✅ Healthy' if health['is_healthy'] else '❌ Unhealthy'}")
    print(f"Total Calls: {health['metrics']['total_calls']}")
    print(f"Success Rate: {(health['metrics']['successful_calls'] / max(1, health['metrics']['total_calls']) * 100):.1f}%")


async def main():
    """Main test function."""
    print("🚀 Enhanced LLM Integration Test Suite")
    print("=" * 60)
    
    try:
        # Test the full integration
        await test_enhanced_llm()
        
        # Test the LLM manager directly
        await test_llm_manager_directly()
        
        print(f"\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
