/**
 * Money Circle Strategy Marketplace JavaScript
 * Handles filtering, searching, following, and interactive features
 */

class StrategyMarketplace {
    constructor() {
        this.strategies = [];
        this.filteredStrategies = [];
        this.userFollowed = [];
        this.categories = [];
        this.currentFilters = {
            search: '',
            category: '',
            risk: '',
            performance: '',
            sort: 'performance',
            quickFilter: 'all'
        };
        this.comparisonList = [];
        this.currentPage = 1;
        this.strategiesPerPage = 12;
        
        this.init();
    }
    
    init() {
        this.loadData();
        this.bindEvents();
        this.applyFilters();
    }
    
    loadData() {
        if (window.marketplaceData) {
            this.strategies = window.marketplaceData.strategies || [];
            this.userFollowed = window.marketplaceData.userFollowed || [];
            this.categories = window.marketplaceData.categories || [];
            this.filteredStrategies = [...this.strategies];
        }
    }
    
    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('strategySearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentFilters.search = e.target.value.toLowerCase();
                this.applyFilters();
            });
        }
        
        // Filter controls
        const filterSelects = ['categoryFilter', 'riskFilter', 'performanceFilter', 'sortFilter'];
        filterSelects.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
        
        // Quick filter chips
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilters.quickFilter = e.target.dataset.filter;
                this.applyFilters();
            });
        });
        
        // View toggle
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.toggleView(e.target.dataset.view);
            });
        });
    }
    
    applyFilters() {
        // Update filter values
        this.currentFilters.category = document.getElementById('categoryFilter')?.value || '';
        this.currentFilters.risk = document.getElementById('riskFilter')?.value || '';
        this.currentFilters.performance = document.getElementById('performanceFilter')?.value || '';
        this.currentFilters.sort = document.getElementById('sortFilter')?.value || 'performance';
        
        // Filter strategies
        this.filteredStrategies = this.strategies.filter(strategy => {
            // Search filter
            if (this.currentFilters.search) {
                const searchTerm = this.currentFilters.search;
                const searchableText = `${strategy.title} ${strategy.description} ${strategy.creator_display_name} ${strategy.strategy_type}`.toLowerCase();
                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }
            
            // Category filter
            if (this.currentFilters.category && strategy.strategy_type !== this.currentFilters.category) {
                return false;
            }
            
            // Risk filter
            if (this.currentFilters.risk && strategy.risk_level !== this.currentFilters.risk) {
                return false;
            }
            
            // Performance filter
            if (this.currentFilters.performance) {
                const performance = strategy.avg_return;
                switch (this.currentFilters.performance) {
                    case 'top':
                        if (performance < 15) return false;
                        break;
                    case 'above_avg':
                        if (performance < 5) return false;
                        break;
                    case 'positive':
                        if (performance <= 0) return false;
                        break;
                }
            }
            
            // Quick filter
            switch (this.currentFilters.quickFilter) {
                case 'featured':
                    return strategy.is_featured;
                case 'trending':
                    return strategy.followers_count > 20;
                case 'new':
                    const createdDate = new Date(strategy.created_at);
                    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                    return createdDate > weekAgo;
                case 'followed':
                    return this.userFollowed.includes(strategy.id);
                default:
                    return true;
            }
        });
        
        // Sort strategies
        this.sortStrategies();
        
        // Update display
        this.updateStrategyGrid();
        this.updateResultsCount();
    }
    
    sortStrategies() {
        this.filteredStrategies.sort((a, b) => {
            switch (this.currentFilters.sort) {
                case 'performance':
                    return b.avg_return - a.avg_return;
                case 'followers':
                    return b.followers_count - a.followers_count;
                case 'recent':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'alphabetical':
                    return a.title.localeCompare(b.title);
                default:
                    return b.avg_return - a.avg_return;
            }
        });
    }
    
    updateStrategyGrid() {
        const grid = document.getElementById('strategyGrid');
        if (!grid) return;
        
        const startIndex = (this.currentPage - 1) * this.strategiesPerPage;
        const endIndex = startIndex + this.strategiesPerPage;
        const pageStrategies = this.filteredStrategies.slice(startIndex, endIndex);
        
        if (pageStrategies.length === 0) {
            grid.innerHTML = '<div class="empty-state">No strategies match your filters</div>';
            return;
        }
        
        grid.innerHTML = pageStrategies.map(strategy => this.renderStrategyCard(strategy)).join('');
    }
    
    renderStrategyCard(strategy) {
        const isFollowed = this.userFollowed.includes(strategy.id);
        const riskClass = `risk-${strategy.risk_level}`;
        const performanceClass = strategy.avg_return > 0 ? 'positive' : 'negative';
        const followClass = isFollowed ? 'followed' : '';
        
        return `
            <div class="strategy-card ${followClass}" data-strategy-id="${strategy.id}">
                <div class="strategy-card-header">
                    <div class="strategy-title-section">
                        <h4 class="strategy-title">${strategy.title}</h4>
                        <div class="strategy-badges">
                            <span class="type-badge">${strategy.strategy_type.replace('_', ' ')}</span>
                            <span class="risk-badge ${riskClass}">${strategy.risk_level}</span>
                            ${strategy.is_featured ? '<span class="featured-badge">⭐</span>' : ''}
                        </div>
                    </div>
                    <div class="strategy-actions-mini">
                        <button class="action-btn" onclick="marketplace.addToComparison(${strategy.id})" title="Add to comparison">📊</button>
                        <button class="action-btn ${isFollowed ? 'followed' : ''}" onclick="marketplace.toggleFollow(${strategy.id})" title="${isFollowed ? 'Unfollow' : 'Follow'} strategy">${isFollowed ? '❤️' : '🤍'}</button>
                    </div>
                </div>
                
                <div class="strategy-metrics">
                    <div class="metric-row">
                        <div class="metric">
                            <span class="metric-label">Performance</span>
                            <span class="metric-value ${performanceClass}">${strategy.avg_return >= 0 ? '+' : ''}${strategy.avg_return.toFixed(2)}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Win Rate</span>
                            <span class="metric-value">${strategy.avg_win_rate.toFixed(1)}%</span>
                        </div>
                    </div>
                    <div class="metric-row">
                        <div class="metric">
                            <span class="metric-label">Sharpe Ratio</span>
                            <span class="metric-value">${strategy.avg_sharpe.toFixed(2)}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Followers</span>
                            <span class="metric-value">${strategy.followers_count}</span>
                        </div>
                    </div>
                </div>
                
                <div class="strategy-description">
                    ${strategy.description.length > 100 ? strategy.description.substring(0, 100) + '...' : strategy.description}
                </div>
                
                <div class="strategy-footer">
                    <div class="creator-info">
                        <span class="creator-avatar">${strategy.creator_display_name.charAt(0).toUpperCase()}</span>
                        <span class="creator-name">${strategy.creator_display_name}</span>
                    </div>
                    <div class="strategy-age">${this.formatTimeAgo(strategy.created_at)}</div>
                </div>
                
                <div class="strategy-card-actions">
                    <button class="btn-view" onclick="marketplace.viewStrategyDetails(${strategy.id})">View Details</button>
                    <button class="btn-follow ${isFollowed ? 'active' : ''}" onclick="marketplace.toggleFollow(${strategy.id})">
                        ${isFollowed ? 'Unfollow' : 'Follow'}
                    </button>
                </div>
            </div>
        `;
    }
    
    updateResultsCount() {
        const countElement = document.getElementById('resultsCount');
        if (countElement) {
            countElement.textContent = `${this.filteredStrategies.length} strategies found`;
        }
    }
    
    toggleView(viewType) {
        const grid = document.querySelector('.strategy-grid');
        if (!grid) return;
        
        if (viewType === 'list') {
            grid.classList.add('list-view');
        } else {
            grid.classList.remove('list-view');
        }
    }
    
    async toggleFollow(strategyId) {
        try {
            const isCurrentlyFollowed = this.userFollowed.includes(strategyId);
            const action = isCurrentlyFollowed ? 'unfollow' : 'follow';
            
            const response = await fetch(`/api/strategies/${strategyId}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                if (isCurrentlyFollowed) {
                    this.userFollowed = this.userFollowed.filter(id => id !== strategyId);
                } else {
                    this.userFollowed.push(strategyId);
                }
                
                // Update strategy follower count
                const strategy = this.strategies.find(s => s.id === strategyId);
                if (strategy) {
                    strategy.followers_count += isCurrentlyFollowed ? -1 : 1;
                }
                
                this.updateStrategyGrid();
                this.showNotification(`Strategy ${action}ed successfully!`, 'success');
            } else {
                throw new Error(`Failed to ${action} strategy`);
            }
        } catch (error) {
            console.error('Error toggling follow:', error);
            this.showNotification(`Failed to ${action} strategy`, 'error');
        }
    }
    
    addToComparison(strategyId) {
        if (this.comparisonList.includes(strategyId)) {
            this.showNotification('Strategy already in comparison', 'warning');
            return;
        }
        
        if (this.comparisonList.length >= 5) {
            this.showNotification('Maximum 5 strategies can be compared', 'warning');
            return;
        }
        
        this.comparisonList.push(strategyId);
        this.showNotification('Strategy added to comparison', 'success');
        this.updateComparisonButton();
    }
    
    updateComparisonButton() {
        // Update comparison button if it exists
        const comparisonBtn = document.getElementById('comparisonButton');
        if (comparisonBtn) {
            comparisonBtn.textContent = `Compare (${this.comparisonList.length})`;
            comparisonBtn.style.display = this.comparisonList.length > 1 ? 'block' : 'none';
        }
    }
    
    async viewStrategyDetails(strategyId) {
        try {
            const response = await fetch(`/api/strategies/${strategyId}/details`);
            if (response.ok) {
                const data = await response.json();
                this.showStrategyModal(data);
            } else {
                throw new Error('Failed to load strategy details');
            }
        } catch (error) {
            console.error('Error loading strategy details:', error);
            this.showNotification('Failed to load strategy details', 'error');
        }
    }
    
    showStrategyModal(strategyData) {
        const modal = document.getElementById('strategyDetailModal');
        const content = document.getElementById('strategyDetailContent');
        const title = document.getElementById('modalStrategyTitle');
        
        if (modal && content && title) {
            title.textContent = strategyData.title;
            content.innerHTML = this.renderStrategyDetails(strategyData);
            modal.style.display = 'block';
        }
    }
    
    renderStrategyDetails(strategy) {
        // This would render detailed strategy information
        return `
            <div class="strategy-detail-content">
                <div class="strategy-overview">
                    <h3>Strategy Overview</h3>
                    <p>${strategy.description}</p>
                </div>
                <div class="strategy-performance-chart">
                    <h3>Performance Chart</h3>
                    <canvas id="performanceChart"></canvas>
                </div>
                <div class="strategy-metrics-detailed">
                    <h3>Detailed Metrics</h3>
                    <!-- Detailed metrics would go here -->
                </div>
            </div>
        `;
    }
    
    formatTimeAgo(timestamp) {
        const now = new Date();
        const date = new Date(timestamp);
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        const months = Math.floor(diff / 2592000000);
        
        if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }
    
    showNotification(message, type = 'info') {
        // Create and show notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global functions for onclick handlers
function searchStrategies() {
    if (window.marketplace) {
        window.marketplace.applyFilters();
    }
}

function applyFilters() {
    if (window.marketplace) {
        window.marketplace.applyFilters();
    }
}

function loadMoreStrategies() {
    if (window.marketplace) {
        window.marketplace.currentPage++;
        window.marketplace.updateStrategyGrid();
    }
}

function viewStrategyDetails(strategyId) {
    if (window.marketplace) {
        window.marketplace.viewStrategyDetails(strategyId);
    }
}

function followStrategy(strategyId) {
    if (window.marketplace) {
        window.marketplace.toggleFollow(strategyId);
    }
}

function addToComparison(strategyId) {
    if (window.marketplace) {
        window.marketplace.addToComparison(strategyId);
    }
}

function closeStrategyModal() {
    const modal = document.getElementById('strategyDetailModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function closeComparisonModal() {
    const modal = document.getElementById('strategyComparisonModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Initialize marketplace when DOM is loaded
function initializeMarketplace() {
    window.marketplace = new StrategyMarketplace();
}
