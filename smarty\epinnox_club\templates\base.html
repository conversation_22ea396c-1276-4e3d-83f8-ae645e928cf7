<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token if csrf_token else '' }}">
    <title>{% block title %}Money Circle - Investment Club{% endblock %}</title>

    <!-- Critical CSS Inline for Performance -->
    <style>
        /* Critical above-the-fold CSS inlined for fastest rendering */
        :root{--primary-600:#8b5cf6;--warning-400:#fbbf24;--bg-primary:#0f1419;--bg-secondary:#1a1f2e;--bg-card:rgba(255,255,255,.05);--text-primary:#f1f5f9;--text-secondary:#e2e8f0;--border-primary:rgba(255,255,255,.1);--space-4:1rem;--space-6:1.5rem;--radius-lg:.5rem;--transition-normal:300ms ease;--touch-target-min:44px;--mobile-gap:.75rem}*{box-sizing:border-box}html{scroll-behavior:smooth}body{font-family:'Inter','Segoe UI','Roboto','Helvetica Neue',sans-serif;background:linear-gradient(135deg,var(--bg-primary) 0%,var(--bg-secondary) 100%);color:var(--text-secondary);line-height:1.5;margin:0;padding:0;min-height:100vh;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:flex;flex-direction:column}.main-content{flex:1;padding:var(--space-6) 0;min-height:calc(100vh - 200px)}.container{width:100%;max-width:1400px;margin:0 auto;padding:0 var(--space-4)}.dashboard-grid{display:grid;grid-template-columns:1fr;gap:var(--mobile-gap);margin-bottom:30px}.portfolio-overview{grid-column:1/-1;background:rgba(0,0,0,.3);border-radius:16px;padding:25px;border:1px solid rgba(255,255,255,.1);backdrop-filter:blur(10px)}.portfolio-overview h2{color:var(--warning-400);margin-bottom:20px;font-size:1.5em;font-weight:600}.portfolio-cards{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.portfolio-card{background:rgba(255,255,255,.05);border-radius:12px;padding:20px;border:1px solid rgba(255,255,255,.1);transition:all .3s ease;min-height:var(--touch-target-min);touch-action:manipulation}.btn{display:inline-flex;align-items:center;justify-content:center;gap:.5rem;padding:.75rem 1.5rem;border:none;border-radius:var(--radius-lg);font-weight:500;font-size:.875rem;text-decoration:none;cursor:pointer;transition:var(--transition-normal);white-space:nowrap;min-height:var(--touch-target-min);touch-action:manipulation;-webkit-tap-highlight-color:transparent}.btn-primary{background:linear-gradient(135deg,var(--primary-600),#7c3aed);color:white}@media (min-width:768px){.dashboard-grid{grid-template-columns:1fr 1fr;gap:30px}}@media (hover:hover){.portfolio-card:hover{transform:translateY(-2px);box-shadow:0 8px 20px rgba(255,215,0,.1);border-color:rgba(255,215,0,.3)}.btn-primary:hover{background:linear-gradient(135deg,#7c3aed,#6d28d9);transform:translateY(-1px);box-shadow:0 0 20px rgba(139,92,246,.3)}}@media (hover:none){.portfolio-card:focus,.portfolio-card:active{background:rgba(255,255,255,.08);border-color:rgba(255,215,0,.3);transform:scale(.98)}.btn:focus,.btn:active{background:rgba(255,255,255,.08);transform:scale(.98)}}@media (max-width:474px){.container{padding:0 1rem}.main-content{padding:1rem 0}.portfolio-cards{grid-template-columns:1fr;gap:.75rem}}
    </style>

    <!-- Preload Critical Resources -->
    <link rel="preload" href="/static/css/design_system.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/static/css/design_system.min.css"></noscript>

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="preload" href="/static/css/unified_header.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/static/css/unified_footer.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/static/css/browser_fallbacks.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for browsers without JavaScript -->
    <noscript>
        <link rel="stylesheet" href="/static/css/unified_header.css">
        <link rel="stylesheet" href="/static/css/unified_footer.css">
        <link rel="stylesheet" href="/static/css/browser_fallbacks.css">
    </noscript>

    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="{% block description %}Money Circle - Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta name="keywords" content="investment club, trading, cryptocurrency, portfolio management, fintech">
    <meta name="author" content="Epinnox">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{% block og_url %}https://moneycircle.epinnox.com/{% endblock %}">
    <meta property="og:title" content="{% block og_title %}Money Circle - Investment Club{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta property="og:image" content="/static/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{% block twitter_url %}https://moneycircle.epinnox.com/{% endblock %}">
    <meta property="twitter:title" content="{% block twitter_title %}Money Circle - Investment Club{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta property="twitter:image" content="/static/images/twitter-image.png">
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Unified Header -->
    {% include 'components/header.html' %}

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Unified Footer -->
    {% include 'components/footer.html' %}

    <!-- Core JavaScript -->
    <script src="/static/js/common.js"></script>

    <!-- CSRF Protection (must load early) -->
    <script src="/static/js/csrf_fix.js"></script>

    <!-- Header Navigation JavaScript (required for all pages) -->
    <script src="/static/js/header_navigation.js"></script>

    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Global JavaScript for all pages -->
    <script>
        // Global configuration
        window.MoneyCircle = {
            user: {
                id: {% if user %}{{ user.user_id }}{% else %}null{% endif %},
                username: {% if user %}'{{ user.username }}'{% else %}null{% endif %},
                role: {% if user %}'{{ user.role }}'{% else %}null{% endif %}
            },
            config: {
                apiBaseUrl: '/api',
                wsBaseUrl: 'ws://localhost:8087/ws'
            }
        };

        // Make user data available for header navigation
        window.currentUser = window.MoneyCircle.user;

        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });

        // Initialize common functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips, modals, etc.
            initializeCommonComponents();
        });

        function initializeCommonComponents() {
            // Add any global component initialization here
            console.log('Money Circle platform initialized');
        }
    </script>
</body>
</html>
