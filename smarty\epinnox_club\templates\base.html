<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Money Circle - Investment Club{% endblock %}</title>
    
    <!-- Core CSS -->
    <link rel="stylesheet" href="/static/css/design_system.css">
    <link rel="stylesheet" href="/static/css/unified_header.css">
    <link rel="stylesheet" href="/static/css/unified_footer.css">
    
    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    
    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="{% block description %}Money Circle - Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta name="keywords" content="investment club, trading, cryptocurrency, portfolio management, fintech">
    <meta name="author" content="Epinnox">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{% block og_url %}https://moneycircle.epinnox.com/{% endblock %}">
    <meta property="og:title" content="{% block og_title %}Money Circle - Investment Club{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta property="og:image" content="/static/images/og-image.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{% block twitter_url %}https://moneycircle.epinnox.com/{% endblock %}">
    <meta property="twitter:title" content="{% block twitter_title %}Money Circle - Investment Club{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}Professional Investment Club Platform for Collaborative Trading{% endblock %}">
    <meta property="twitter:image" content="/static/images/twitter-image.png">
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Unified Header -->
    {% include 'components/header.html' %}
    
    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Unified Footer -->
    {% include 'components/footer.html' %}
    
    <!-- Core JavaScript -->
    <script src="/static/js/common.js"></script>
    
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- Global JavaScript for all pages -->
    <script>
        // Global configuration
        window.MoneyCircle = {
            user: {
                id: {% if user %}{{ user.user_id }}{% else %}null{% endif %},
                username: {% if user %}'{{ user.username }}'{% else %}null{% endif %},
                role: {% if user %}'{{ user.role }}'{% else %}null{% endif %}
            },
            config: {
                apiBaseUrl: '/api',
                wsBaseUrl: '{{ "wss" if request.scheme == "https" else "ws" }}://{{ request.host }}/ws'
            }
        };
        
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });
        
        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
        });
        
        // Initialize common functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips, modals, etc.
            initializeCommonComponents();
        });
        
        function initializeCommonComponents() {
            // Add any global component initialization here
            console.log('Money Circle platform initialized');
        }
    </script>
</body>
</html>
