2025-05-29 22:05:08,917 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250529_22_smart_model_integrated.log
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250529_22_smart_model_integrated_events.json
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-29 22:05:08
2025-05-29 22:05:08,921 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-29 22:05:08,921 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.13.2 | packaged by <PERSON><PERSON>da, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]
2025-05-29 22:05:08,922 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-29 22:05:08,941 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-29 22:05:08,951 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-29 22:05:08,954 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-29 22:05:08,960 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-29 22:05:08,960 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-29 22:05:12,508 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-29 22:05:12,510 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-29 22:05:12,511 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-29 22:08:55,222 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-29 22:08:55,222 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250529_22_smart_model_integrated.log
2025-05-29 22:08:55,223 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250529_22_smart_model_integrated_events.json
2025-05-29 22:08:55,223 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-29 22:08:55,223 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-29 22:08:55
2025-05-29 22:08:55,223 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-29 22:08:55,224 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-29 22:08:55,224 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-29 22:08:55,225 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-29 22:08:55,226 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-29 22:08:55,226 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-29 22:08:55,226 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-29 22:08:55,226 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-29 22:08:55,247 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-29 22:08:55,248 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-29 22:08:55,248 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-29 22:08:55,249 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-29 22:08:55,249 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-29 22:08:56,545 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-29 22:08:56,547 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-29 22:08:56,552 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-29 22:08:56,553 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-29 22:08:58,414 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-29 22:08:58,650 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-29 22:08:58,651 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-29 22:10:23,771 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-29 22:10:23,772 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-29 22:10:23,773 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-29 22:10:23,774 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-29 22:10:23,775 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-29 22:10:23,776 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-29 22:10:23,777 - strategy.smart_model_integrated - ERROR - [error:97] - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
2025-05-29 22:10:23,777 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-29 22:10:23,778 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-29 22:10:23,778 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-29 22:10:23,779 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-29 22:10:23,779 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-29 22:10:23,783 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-29 22:10:23,786 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
