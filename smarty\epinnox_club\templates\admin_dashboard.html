<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Money Circle</title>
    <link rel="stylesheet" href="/static/css/design_system.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/club_analytics.css">
    <style>
        .admin-dashboard {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .admin-header h1 {
            color: #FFD700;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .admin-header p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-size: 1.1rem;
        }
        
        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .admin-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .admin-card h3 {
            color: #FFD700;
            margin: 0 0 20px 0;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }
        
        .kpi-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .kpi-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .user-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            color: white;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .user-details {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }
        
        .user-role {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-admin {
            background: #f44336;
            color: white;
        }
        
        .role-member {
            background: #2196F3;
            color: white;
        }
        
        .role-viewer {
            background: #ff9800;
            color: white;
        }
        
        .strategy-item {
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .strategy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .strategy-title {
            color: white;
            font-weight: 600;
            margin: 0;
        }
        
        .strategy-risk {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .risk-low {
            background: #4CAF50;
            color: white;
        }
        
        .risk-medium {
            background: #ff9800;
            color: white;
        }
        
        .risk-high {
            background: #f44336;
            color: white;
        }
        
        .strategy-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .strategy-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .admin-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .admin-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-approve {
            background: #4CAF50;
            color: white;
        }
        
        .btn-reject {
            background: #f44336;
            color: white;
        }
        
        .btn-view {
            background: #2196F3;
            color: white;
        }
        
        .admin-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .system-health {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .health-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .health-healthy {
            background: #4CAF50;
        }
        
        .health-warning {
            background: #ff9800;
        }
        
        .health-error {
            background: #f44336;
        }
        
        .activity-log {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-item {
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 3px solid #2196F3;
        }
        
        .log-timestamp {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
            margin-bottom: 4px;
        }
        
        .log-details {
            color: white;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .admin-grid {
                grid-template-columns: 1fr;
            }
            
            .kpi-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .admin-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>
    
    <div class="admin-dashboard">
        <!-- Admin Header -->
        <div class="admin-header">
            <h1>🛡️ Admin Dashboard</h1>
            <p>Comprehensive platform management and oversight</p>
        </div>
        
        <!-- Platform KPIs -->
        <div class="admin-card">
            <h3>📊 Platform Overview</h3>
            <div class="kpi-grid">
                <div class="kpi-item">
                    <div class="kpi-value" id="total-users">0</div>
                    <div class="kpi-label">Total Users</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="active-users">0</div>
                    <div class="kpi-label">Active Users</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="total-strategies">0</div>
                    <div class="kpi-label">Strategies</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="total-trades">0</div>
                    <div class="kpi-label">Trades (30d)</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="total-volume">$0</div>
                    <div class="kpi-label">Volume (30d)</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="trading-users">0</div>
                    <div class="kpi-label">Trading Users</div>
                </div>
            </div>
        </div>
        
        <!-- Main Admin Grid -->
        <div class="admin-grid">
            <!-- User Management -->
            <div class="admin-card">
                <h3>👥 User Management</h3>
                <div class="user-list" id="recent-users">
                    <!-- Users will be populated by JavaScript -->
                </div>
                <div class="admin-actions">
                    <button class="admin-btn btn-view" onclick="viewAllUsers()">View All Users</button>
                    <button class="admin-btn btn-approve" onclick="bulkUserActions()">Bulk Actions</button>
                </div>
            </div>
            
            <!-- Strategy Oversight -->
            <div class="admin-card">
                <h3>🎯 Strategy Oversight</h3>
                <div id="pending-strategies">
                    <!-- Pending strategies will be populated by JavaScript -->
                </div>
                <div class="admin-actions">
                    <button class="admin-btn btn-view" onclick="viewAllStrategies()">View All Strategies</button>
                </div>
            </div>
            
            <!-- System Health -->
            <div class="admin-card">
                <h3>🔧 System Health</h3>
                <div class="system-health">
                    <div class="health-indicator health-healthy" id="health-indicator"></div>
                    <span id="health-status">System Healthy</span>
                </div>
                <div style="margin-top: 15px;">
                    <div style="color: rgba(255, 255, 255, 0.7); margin-bottom: 10px;">
                        <strong>Database Tables:</strong> <span id="db-tables">0</span>
                    </div>
                    <div style="color: rgba(255, 255, 255, 0.7); margin-bottom: 10px;">
                        <strong>Active Sessions:</strong> <span id="active-sessions">0</span>
                    </div>
                    <div style="color: rgba(255, 255, 255, 0.7);">
                        <strong>Recent Errors:</strong> <span id="recent-errors">0</span>
                    </div>
                </div>
                <div class="admin-actions">
                    <button class="admin-btn btn-view" onclick="viewSystemLogs()">View Logs</button>
                    <button class="admin-btn btn-approve" onclick="systemMaintenance()">Maintenance</button>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="admin-card">
                <h3>📋 Recent Activity</h3>
                <div class="activity-log" id="activity-log">
                    <!-- Activity logs will be populated by JavaScript -->
                </div>
                <div class="admin-actions">
                    <button class="admin-btn btn-view" onclick="viewAuditLogs()">Full Audit Log</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include footer -->
    <div id="footer-placeholder"></div>
    
    <script src="/static/js/common.js"></script>
    <script>
        // Admin dashboard data (will be populated from server)
        let adminData = {{admin_data}};
        
        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadHeaderFooter();
            populateAdminDashboard();
        });
        
        function populateAdminDashboard() {
            if (!adminData || !adminData.kpis) {
                console.error('Admin data not available');
                return;
            }
            
            // Populate KPIs
            const kpis = adminData.kpis;
            document.getElementById('total-users').textContent = kpis.total_users || 0;
            document.getElementById('active-users').textContent = kpis.active_users || 0;
            document.getElementById('total-strategies').textContent = kpis.total_strategies || 0;
            document.getElementById('total-trades').textContent = kpis.total_trades_30d || 0;
            document.getElementById('total-volume').textContent = `$${(kpis.total_volume_30d || 0).toLocaleString()}`;
            document.getElementById('trading-users').textContent = kpis.trading_users_30d || 0;
            
            // Populate recent users
            populateRecentUsers();
            
            // Populate pending strategies
            populatePendingStrategies();
            
            // Populate system health
            populateSystemHealth();
            
            // Populate activity log
            populateActivityLog();
        }
        
        function populateRecentUsers() {
            const container = document.getElementById('recent-users');
            const users = adminData.recent_users || [];
            
            container.innerHTML = '';
            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.innerHTML = `
                    <div class="user-info">
                        <div class="user-name">${user.username}</div>
                        <div class="user-details">${user.email} • Joined ${new Date(user.date_joined).toLocaleDateString()}</div>
                    </div>
                    <div class="user-role role-${user.role}">${user.role}</div>
                `;
                container.appendChild(userItem);
            });
        }
        
        function populatePendingStrategies() {
            const container = document.getElementById('pending-strategies');
            const strategies = adminData.pending_strategies || [];
            
            container.innerHTML = '';
            if (strategies.length === 0) {
                container.innerHTML = '<p style="color: rgba(255, 255, 255, 0.6); text-align: center;">No pending strategies</p>';
                return;
            }
            
            strategies.forEach(strategy => {
                const strategyItem = document.createElement('div');
                strategyItem.className = 'strategy-item';
                strategyItem.innerHTML = `
                    <div class="strategy-header">
                        <h4 class="strategy-title">${strategy.title}</h4>
                        <div class="strategy-risk risk-${strategy.risk_level}">${strategy.risk_level}</div>
                    </div>
                    <div class="strategy-description">${strategy.description}</div>
                    <div class="strategy-meta">
                        <span>By ${strategy.proposer}</span>
                        <span>Expected Return: ${strategy.expected_return}%</span>
                    </div>
                    <div class="admin-actions">
                        <button class="admin-btn btn-approve" onclick="approveStrategy(${strategy.id})">Approve</button>
                        <button class="admin-btn btn-reject" onclick="rejectStrategy(${strategy.id})">Reject</button>
                        <button class="admin-btn btn-view" onclick="viewStrategy(${strategy.id})">Details</button>
                    </div>
                `;
                container.appendChild(strategyItem);
            });
        }
        
        function populateSystemHealth() {
            const health = adminData.system_health || {};
            const indicator = document.getElementById('health-indicator');
            const status = document.getElementById('health-status');
            
            // Update health indicator
            indicator.className = `health-indicator health-${health.status || 'warning'}`;
            status.textContent = health.status === 'healthy' ? 'System Healthy' : 
                                health.status === 'warning' ? 'System Warning' : 'System Error';
            
            // Update metrics
            document.getElementById('db-tables').textContent = health.database_tables || 0;
            document.getElementById('active-sessions').textContent = health.active_sessions || 0;
            document.getElementById('recent-errors').textContent = health.recent_errors || 0;
        }
        
        function populateActivityLog() {
            const container = document.getElementById('activity-log');
            const logs = adminData.recent_logs || [];
            
            container.innerHTML = '';
            if (logs.length === 0) {
                container.innerHTML = '<p style="color: rgba(255, 255, 255, 0.6); text-align: center;">No recent activity</p>';
                return;
            }
            
            logs.forEach(log => {
                const logItem = document.createElement('div');
                logItem.className = 'log-item';
                logItem.innerHTML = `
                    <div class="log-timestamp">${new Date(log.timestamp).toLocaleString()}</div>
                    <div class="log-details">${log.username}: ${log.details}</div>
                `;
                container.appendChild(logItem);
            });
        }
        
        // Admin action functions
        function approveStrategy(strategyId) {
            if (confirm('Approve this strategy?')) {
                // TODO: Implement strategy approval
                console.log('Approving strategy:', strategyId);
            }
        }
        
        function rejectStrategy(strategyId) {
            if (confirm('Reject this strategy?')) {
                // TODO: Implement strategy rejection
                console.log('Rejecting strategy:', strategyId);
            }
        }
        
        function viewStrategy(strategyId) {
            // TODO: Implement strategy details view
            console.log('Viewing strategy:', strategyId);
        }
        
        function viewAllUsers() {
            // TODO: Navigate to user management page
            console.log('Viewing all users');
        }
        
        function bulkUserActions() {
            // TODO: Implement bulk user actions
            console.log('Bulk user actions');
        }
        
        function viewAllStrategies() {
            // TODO: Navigate to strategy management page
            console.log('Viewing all strategies');
        }
        
        function viewSystemLogs() {
            // TODO: Navigate to system logs page
            console.log('Viewing system logs');
        }
        
        function systemMaintenance() {
            // TODO: Implement system maintenance
            console.log('System maintenance');
        }
        
        function viewAuditLogs() {
            // TODO: Navigate to audit logs page
            console.log('Viewing audit logs');
        }
    </script>
</body>
</html>
