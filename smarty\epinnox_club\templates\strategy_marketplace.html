{% extends "base.html" %}

{% block title %}Money Circle - Strategy Marketplace{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/club.css">
<link rel="stylesheet" href="/static/css/strategy_marketplace.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="strategy-marketplace-container">
    <!-- Marketplace Header -->
    <section class="marketplace-header">
        <div class="header-content">
            <h1>🎯 Strategy Marketplace</h1>
            <p>Discover, analyze, and follow proven trading strategies</p>
        </div>
        
        <!-- Marketplace Stats -->
        <div class="marketplace-stats">
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-content">
                    <div class="stat-value">{{ marketplace_stats.total_strategies }}</div>
                    <div class="stat-label">Total Strategies</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⭐</div>
                <div class="stat-content">
                    <div class="stat-value">{{ marketplace_stats.avg_performance|round(1) }}%</div>
                    <div class="stat-label">Avg Performance</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <div class="stat-value">{{ marketplace_stats.total_followers }}</div>
                    <div class="stat-label">Total Followers</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🚀</div>
                <div class="stat-content">
                    <div class="stat-value">{{ marketplace_stats.active_strategies }}</div>
                    <div class="stat-label">Active Strategies</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="marketplace-filters">
        <div class="search-bar">
            <input type="text" id="strategy-search" placeholder="Search strategies..." onkeyup="filterStrategies()">
            <button onclick="filterStrategies()">🔍</button>
        </div>
        
        <div class="filter-controls">
            <select id="category-filter" onchange="filterStrategies()">
                <option value="">All Categories</option>
                <option value="momentum">Momentum</option>
                <option value="mean_reversion">Mean Reversion</option>
                <option value="arbitrage">Arbitrage</option>
                <option value="market_making">Market Making</option>
                <option value="trend_following">Trend Following</option>
            </select>
            
            <select id="risk-filter" onchange="filterStrategies()">
                <option value="">All Risk Levels</option>
                <option value="low">Low Risk</option>
                <option value="medium">Medium Risk</option>
                <option value="high">High Risk</option>
            </select>
            
            <select id="performance-filter" onchange="filterStrategies()">
                <option value="">All Performance</option>
                <option value="positive">Positive Only</option>
                <option value="top_10">Top 10%</option>
                <option value="top_25">Top 25%</option>
            </select>
            
            <select id="sort-filter" onchange="filterStrategies()">
                <option value="performance">Sort by Performance</option>
                <option value="followers">Sort by Followers</option>
                <option value="recent">Sort by Recent</option>
                <option value="alphabetical">Sort Alphabetically</option>
            </select>
        </div>
    </section>

    <!-- Featured Strategies -->
    <section class="featured-strategies">
        <h2>⭐ Featured Strategies</h2>
        <div class="featured-grid">
            {% for strategy in featured_strategies %}
            <div class="featured-strategy-card">
                <div class="strategy-header">
                    <h3>{{ strategy.name }}</h3>
                    <div class="strategy-badges">
                        <span class="badge category-badge">{{ strategy.category }}</span>
                        <span class="badge risk-badge risk-{{ strategy.risk_level }}">{{ strategy.risk_level|title }} Risk</span>
                    </div>
                </div>
                
                <div class="strategy-metrics">
                    <div class="metric">
                        <span class="metric-label">Performance</span>
                        <span class="metric-value {{ 'positive' if strategy.performance >= 0 else 'negative' }}">
                            {{ '+' if strategy.performance >= 0 else '' }}{{ strategy.performance|round(2) }}%
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Followers</span>
                        <span class="metric-value">{{ strategy.followers_count }}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Win Rate</span>
                        <span class="metric-value">{{ strategy.win_rate|round(1) }}%</span>
                    </div>
                </div>
                
                <div class="strategy-description">
                    <p>{{ strategy.description[:100] }}{% if strategy.description|length > 100 %}...{% endif %}</p>
                </div>
                
                <div class="strategy-actions">
                    <button onclick="viewStrategyDetails({{ strategy.id }})" class="btn-secondary">
                        📊 View Details
                    </button>
                    {% if strategy.id not in user_following %}
                    <button onclick="followStrategy({{ strategy.id }})" class="btn-primary">
                        ➕ Follow
                    </button>
                    {% else %}
                    <button onclick="unfollowStrategy({{ strategy.id }})" class="btn-danger">
                        ➖ Unfollow
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- All Strategies -->
    <section class="all-strategies">
        <h2>📈 All Strategies</h2>
        <div class="strategies-grid" id="strategies-grid">
            {% for strategy in all_strategies %}
            <div class="strategy-card" 
                 data-category="{{ strategy.category }}" 
                 data-risk="{{ strategy.risk_level }}" 
                 data-performance="{{ strategy.performance }}"
                 data-name="{{ strategy.name|lower }}">
                
                <div class="strategy-header">
                    <h4>{{ strategy.name }}</h4>
                    <div class="strategy-author">by {{ strategy.creator_name }}</div>
                </div>
                
                <div class="strategy-badges">
                    <span class="badge category-badge">{{ strategy.category }}</span>
                    <span class="badge risk-badge risk-{{ strategy.risk_level }}">{{ strategy.risk_level|title }}</span>
                    {% if strategy.is_active %}
                    <span class="badge status-badge active">Active</span>
                    {% else %}
                    <span class="badge status-badge inactive">Inactive</span>
                    {% endif %}
                </div>
                
                <div class="strategy-metrics-compact">
                    <div class="metric-item">
                        <span class="metric-label">Performance:</span>
                        <span class="metric-value {{ 'positive' if strategy.performance >= 0 else 'negative' }}">
                            {{ '+' if strategy.performance >= 0 else '' }}{{ strategy.performance|round(2) }}%
                        </span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Followers:</span>
                        <span class="metric-value">{{ strategy.followers_count }}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Win Rate:</span>
                        <span class="metric-value">{{ strategy.win_rate|round(1) }}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Max Drawdown:</span>
                        <span class="metric-value">{{ strategy.max_drawdown|round(1) }}%</span>
                    </div>
                </div>
                
                <div class="strategy-description-compact">
                    <p>{{ strategy.description[:80] }}{% if strategy.description|length > 80 %}...{% endif %}</p>
                </div>
                
                <div class="strategy-actions-compact">
                    <button onclick="viewStrategyDetails({{ strategy.id }})" class="btn-sm btn-secondary">
                        Details
                    </button>
                    {% if strategy.id not in user_following %}
                    <button onclick="followStrategy({{ strategy.id }})" class="btn-sm btn-primary">
                        Follow
                    </button>
                    {% else %}
                    <button onclick="unfollowStrategy({{ strategy.id }})" class="btn-sm btn-danger">
                        Unfollow
                    </button>
                    {% endif %}
                </div>
                
                <div class="strategy-footer">
                    <span class="created-date">Created: {{ strategy.created_at.strftime('%Y-%m-%d') }}</span>
                    {% if strategy.last_trade_at %}
                    <span class="last-trade">Last trade: {{ strategy.last_trade_at.strftime('%Y-%m-%d') }}</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- My Following -->
    <section class="my-following">
        <h2>👥 Strategies I'm Following</h2>
        {% if following_strategies %}
        <div class="following-grid">
            {% for strategy in following_strategies %}
            <div class="following-strategy-card">
                <h4>{{ strategy.name }}</h4>
                <div class="following-metrics">
                    <span class="performance {{ 'positive' if strategy.performance >= 0 else 'negative' }}">
                        {{ '+' if strategy.performance >= 0 else '' }}{{ strategy.performance|round(2) }}%
                    </span>
                    <span class="followers">{{ strategy.followers_count }} followers</span>
                </div>
                <div class="following-actions">
                    <button onclick="viewStrategyDetails({{ strategy.id }})" class="btn-sm btn-secondary">
                        Details
                    </button>
                    <button onclick="unfollowStrategy({{ strategy.id }})" class="btn-sm btn-danger">
                        Unfollow
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <p>You're not following any strategies yet. Explore the marketplace above to find strategies that match your investment goals.</p>
        </div>
        {% endif %}
    </section>
</div>

<!-- Strategy Details Modal -->
<div id="strategy-details-modal" class="modal">
    <div class="modal-content large">
        <span class="close" onclick="closeModal('strategy-details-modal')">&times;</span>
        <div id="strategy-details-content">
            <!-- Strategy details will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/strategy_marketplace.js"></script>
<script>
    // Initialize marketplace data
    window.marketplaceData = {
        user: {{ user|tojson }},
        marketplaceStats: {{ marketplace_stats|tojson }},
        featuredStrategies: {{ featured_strategies|tojson }},
        allStrategies: {{ all_strategies|tojson }},
        followingStrategies: {{ following_strategies|tojson }},
        userFollowing: {{ user_following|tojson }}
    };

    // Initialize strategy marketplace
    document.addEventListener('DOMContentLoaded', function() {
        initializeStrategyMarketplace();
    });
</script>
{% endblock %}
