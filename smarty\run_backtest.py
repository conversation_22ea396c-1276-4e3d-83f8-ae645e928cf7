"""
Run a backtest with the smart-trader system.
"""

import os
import json
import logging
import asyncio
import argparse
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional

from backtester.backtester import Backtester
from backtester.visualizer import BacktestVisualizer
from backtester.strategies import (
    simple_moving_average_crossover,
    bollinger_bands_strategy,
    rsi_strategy,
    enhanced_multi_signal_strategy,
    model_ensemble_strategy,
    smart_model_integrated_strategy
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def run_backtest(args):
    """
    Run a backtest with the specified parameters.

    Args:
        args: Command-line arguments
    """
    # Load configuration
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)

    # Create backtester
    backtester = Backtester(
        config=config,
        data_dir=args.data_dir,
        output_dir=args.output_dir
    )

    # Get date range from data if not specified
    if not args.start_date or not args.end_date:
        # Use the dates from the generated data
        import pandas as pd

        # Load the first symbol's data to get date range
        symbol_file = os.path.join(args.data_dir, f"{args.symbols[0].replace('-', '_')}.csv")
        if os.path.exists(symbol_file):
            df = pd.read_csv(symbol_file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                min_date = df['timestamp'].min().strftime("%Y-%m-%d")
                max_date = df['timestamp'].max().strftime("%Y-%m-%d")

                if not args.start_date:
                    args.start_date = min_date
                    logger.info(f"Using start date from data: {args.start_date}")

                if not args.end_date:
                    args.end_date = max_date
                    logger.info(f"Using end date from data: {args.end_date}")

    # Load historical data
    await backtester.load_data(
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date
    )

    # Select strategy
    strategy_map = {
        'sma': simple_moving_average_crossover,
        'bollinger': bollinger_bands_strategy,
        'rsi': rsi_strategy,
        'multi': enhanced_multi_signal_strategy,
        'ensemble': model_ensemble_strategy,
        'smart': smart_model_integrated_strategy
    }

    strategy = strategy_map.get(args.strategy)
    if not strategy:
        logger.error(f"Unknown strategy: {args.strategy}")
        return

    # Set strategy parameters
    strategy_params = {}
    if args.strategy == 'sma':
        strategy_params = {
            'fast_period': args.fast_period,
            'slow_period': args.slow_period
        }
    elif args.strategy == 'bollinger':
        strategy_params = {
            'period': args.period,
            'std_dev': args.std_dev
        }
    elif args.strategy == 'rsi':
        strategy_params = {
            'period': args.period,
            'oversold': args.oversold,
            'overbought': args.overbought
        }
    elif args.strategy == 'multi':
        strategy_params = {
            'sma_fast': args.fast_period,
            'sma_slow': args.slow_period,
            'rsi_period': args.rsi_period,
            'rsi_oversold': args.oversold,
            'rsi_overbought': args.overbought,
            'bb_period': args.period,
            'bb_std': args.std_dev
        }
    elif args.strategy == 'ensemble':
        strategy_params = {}  # Ensemble strategy doesn't need parameters
    elif args.strategy == 'smart':
        strategy_params = {}  # Smart integrated strategy doesn't need parameters

    # Create strategy function with parameters
    async def signal_generator(timestamp, symbols):
        return await strategy(timestamp, symbols, **strategy_params)

    # Run backtest
    success = await backtester.run_backtest(signal_generator=signal_generator)

    if not success:
        logger.error("Backtest failed")
        return

    # Visualize results
    visualizer = BacktestVisualizer(
        results_dir=args.output_dir,
        output_dir=os.path.join(args.output_dir, 'plots')
    )

    # Get result file paths
    result_files = backtester._save_results()

    # Load results
    visualizer.load_results(
        equity_file=result_files['equity_curve'],
        trades_file=result_files['trades'],
        signals_file=result_files['signals'],
        metrics_file=result_files['metrics']
    )

    # Generate report
    report_dir = visualizer.generate_report()

    logger.info(f"Backtest completed. Results saved to {report_dir}")

    # Print summary
    logger.info("Backtest Summary:")
    logger.info(f"Strategy: {args.strategy}")
    logger.info(f"Symbols: {', '.join(args.symbols)}")
    logger.info(f"Period: {args.start_date} to {args.end_date}")

    # Print metrics
    metrics = backtester.metrics
    logger.info(f"Total Return: {metrics['total_return']:.2%}")
    logger.info(f"Annual Return: {metrics['annual_return']:.2%}")
    logger.info(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
    logger.info(f"Max Drawdown: {metrics['max_drawdown']:.2%}")
    logger.info(f"Win Rate: {metrics['win_rate']:.2%}")
    logger.info(f"Total Trades: {metrics['total_trades']}")
    logger.info(f"Initial Balance: ${metrics['initial_balance']:.2f}")
    logger.info(f"Final Balance: ${metrics['final_balance']:.2f}")


def main():
    """Parse command-line arguments and run backtest."""
    parser = argparse.ArgumentParser(description="Run a backtest with the smart-trader system")

    # Basic parameters
    parser.add_argument("--config", "-c", default="config_testnet.yaml", help="Path to configuration file")
    parser.add_argument("--symbols", "-s", nargs="+", required=True, help="Symbols to backtest")
    parser.add_argument("--start-date", help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", help="End date in YYYY-MM-DD format")
    parser.add_argument("--data-dir", default="data/historical", help="Directory containing historical data")
    parser.add_argument("--output-dir", default="results/backtest", help="Directory for storing backtest results")

    # Strategy selection
    parser.add_argument("--strategy", "-S", choices=['sma', 'bollinger', 'rsi', 'multi', 'ensemble', 'smart'], default='sma',
                       help="Trading strategy to use")

    # Strategy parameters
    parser.add_argument("--fast-period", type=int, default=10, help="Fast period for SMA strategy")
    parser.add_argument("--slow-period", type=int, default=30, help="Slow period for SMA strategy")
    parser.add_argument("--period", type=int, default=20, help="Period for Bollinger Bands or RSI strategy")
    parser.add_argument("--rsi-period", type=int, default=14, help="RSI period for multi-signal strategy")
    parser.add_argument("--std-dev", type=float, default=2.0, help="Standard deviation for Bollinger Bands")
    parser.add_argument("--oversold", type=float, default=30.0, help="Oversold threshold for RSI strategy")
    parser.add_argument("--overbought", type=float, default=70.0, help="Overbought threshold for RSI strategy")

    args = parser.parse_args()

    # Run backtest
    asyncio.run(run_backtest(args))


if __name__ == "__main__":
    main()
