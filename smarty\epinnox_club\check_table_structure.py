#!/usr/bin/env python3
"""
Check Database Table Structure
Quick script to check the structure of strategy_proposals table
"""

import sqlite3
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_table_structure():
    """Check the structure of strategy_proposals table."""
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # Check strategy_proposals table structure
        cursor = conn.execute("PRAGMA table_info(strategy_proposals)")
        columns = cursor.fetchall()
        
        logger.info("Strategy Proposals Table Structure:")
        for col in columns:
            logger.info(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check if there are any existing records
        cursor = conn.execute("SELECT COUNT(*) as count FROM strategy_proposals")
        count = cursor.fetchone()['count']
        logger.info(f"\nExisting records: {count}")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    check_table_structure()
