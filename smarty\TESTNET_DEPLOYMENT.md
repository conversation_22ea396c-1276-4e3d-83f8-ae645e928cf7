# Smart-Trader Testnet Deployment Guide

This guide will help you deploy the smart-trader system to testnet for testing with real market data and account updates.

## Prerequisites

1. HTX Futures testnet account with API keys
2. Python 3.9+ with required packages
3. LLM model file (llama-2-7b-chat.Q4_K_M.gguf)

## Setup Steps

### 1. Install Required Packages

```bash
pip install -r requirements.txt
```

### 2. Configure API Keys

Edit `config_testnet.yaml` and add your HTX testnet API keys:

```yaml
exchange:
  name: "htx"
  testnet: true
  api_key: "YOUR_TESTNET_API_KEY"
  api_secret: "YOUR_TESTNET_API_SECRET"
  base_url: "https://api.htx.com"
  ws_url: "wss://api.htx.com/ws"
```

### 3. Configure Trading Settings

Edit the trading settings in `config_testnet.yaml`:

```yaml
trading:
  enabled: true
  simulation_mode: false  # Set to false for real testnet trading
  symbols: ["BTC-USDT"]
  max_positions: 1
  position_size_usd: 10.0  # Small position size for testnet
  leverage: 2
  order_type: "MARKET"
  stop_loss_pct: 2.0
  take_profit_pct: 4.0
  max_slippage_bps: 10
```

### 4. Download LLM Model

Download the LLM model file and place it in the `models` directory:

```bash
mkdir -p models
# Download the model file
# Example: wget -O models/llama-2-7b-chat.Q4_K_M.gguf https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf
```

### 5. Create Required Directories

The system will create these directories automatically, but you can create them manually if needed:

```bash
mkdir -p data logs models llm/prompts
```

## Running the System

### 1. Start the System

```bash
python run_testnet.py
```

This will start the system with the default configuration file (`config_testnet.yaml`). You can specify a different configuration file with the `--config` option:

```bash
python run_testnet.py --config my_config.yaml
```

### 2. Monitor the System

Use the monitoring script to check the system's performance:

```bash
python monitor.py
```

This will show:
- LLM metrics (call count, error rate, latency)
- Recent signals (fused and LLM)
- Recent trades
- Account state (balance, positions, orders)

You can specify a different symbol to monitor:

```bash
python monitor.py --symbol ETH-USDT
```

## Troubleshooting

### 1. Check Logs

Check the log file for errors:

```bash
tail -f logs/testnet.log
```

### 2. Check Database

You can inspect the SQLite databases directly:

```bash
sqlite3 data/testnet_bus.db
sqlite3 data/testnet_features.db
```

### 3. Common Issues

#### API Key Issues

If you see errors related to API keys, make sure:
- You've added the correct API keys to the configuration file
- The API keys have the necessary permissions
- The testnet is available and operational

#### LLM Issues

If you see errors related to the LLM:
- Make sure the model file exists in the `models` directory
- Check that the model path in the configuration file is correct
- Try reducing the number of threads or GPU layers if you're running out of memory

#### Database Issues

If you see database errors:
- Make sure the `data` directory exists and is writable
- Check disk space
- Try deleting the database files and restarting the system

## Monitoring Performance

### 1. LLM Performance

Monitor the LLM's performance using the monitoring script. Look for:
- High error rates (> 5%)
- High latency (> 5 seconds)
- Low confidence scores (< 0.6)

### 2. Trading Performance

Monitor the trading performance by checking:
- P&L of executed trades
- Number of trades
- Win/loss ratio

### 3. System Health

Monitor the system's health by checking:
- CPU and memory usage
- Disk space
- Network connectivity

## Next Steps

After testing on testnet, you can:
1. Analyze the LLM's decisions and refine the prompt
2. Adjust position sizing and risk management parameters
3. Add more features or models
4. Deploy to mainnet (with extreme caution and small position sizes)

## Support

If you encounter any issues, please check the logs and troubleshooting steps above. If you need further assistance, please open an issue on the repository.

Happy trading!
