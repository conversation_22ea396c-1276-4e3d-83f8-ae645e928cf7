"""
Models package for the Epinnox trading system.
Contains AI models for market analysis, enhanced for CCXT integration.
"""

from typing import Protocol, Dict, Any


class BaseModel(Protocol):
    """Protocol for all models."""
    
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.
        
        Args:
            features: Dictionary of input features
            
        Returns:
            Dictionary of prediction results
        """
        ...


# Import all models
from .rsi import RSIModel
from .vwap_deviation import VWAPDeviationModel
from .funding_momentum import FundingMomentumModel
from .open_interest_momentum import OpenInterestMomentumModel

__all__ = [
    'BaseModel',
    'RSIModel',
    'VWAPDeviationModel',
    'FundingMomentumModel',
    'OpenInterestMomentumModel'
]
