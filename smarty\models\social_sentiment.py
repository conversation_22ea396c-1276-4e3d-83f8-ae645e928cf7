"""
Social Sentiment model based on SignalStar API data.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from enum import Enum

from core.utils import timer
from core.feature_store import feature_store
from clients.signalstar_client import SignalStarClient

logger = logging.getLogger(__name__)


class SentimentSignal(Enum):
    """Sentiment signal types."""
    POSITIVE = "POSITIVE"
    NEGATIVE = "NEGATIVE"
    NEUTRAL = "NEUTRAL"


class SocialSentimentModel:
    """
    Social Sentiment model based on SignalStar API data.

    This model analyzes social sentiment data to generate trading signals
    based on changes in sentiment and their statistical significance.
    """

    def __init__(
        self,
        client: SignalStarClient,
        delta_window: int = 5,
        z_window: int = 60,
        threshold: float = 1.2,
        contrarian: bool = True,
        symbol: str = "BTC",
        config: Dict[str, Any] = None
    ):
        """
        Initialize the Social Sentiment model.

        Args:
            client: SignalStar API client
            delta_window: Window size for calculating sentiment delta (minutes)
            z_window: Window size for calculating z-score (minutes)
            threshold: Threshold for generating signals
            contrarian: Whether to use contrarian logic (True) or trend-following (False)
            symbol: Trading symbol
            config: Additional configuration parameters
        """
        self.name = "social_sentiment"
        self.client = client
        self.config = config or {}
        self.delta_window = self.config.get("delta_window", delta_window)
        self.z_window = self.config.get("z_window", z_window)
        self.threshold = self.config.get("threshold", threshold)
        self.contrarian = self.config.get("contrarian", contrarian)
        self.symbol = self.config.get("symbol", symbol)

        # Current state
        self.current_sentiment: Optional[float] = None
        self.sentiment_delta: Optional[float] = None
        self.sentiment_z: Optional[float] = None
        self.signal: Optional[str] = None
        self.confidence: float = 0.0

        # Historical data
        self._historical_data: List[float] = []
        self._last_update: Optional[datetime] = None

    @timer()
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features

        Returns:
            Dictionary of prediction results
        """
        symbol = features.get("symbol", self.symbol)

        # Get latest sentiment
        self.current_sentiment = await self.client.get_latest_sentiment(symbol)

        # Store in feature store
        await feature_store.set(symbol, "social.raw", self.current_sentiment)

        # Add to time series
        await feature_store.add_time_series(symbol, "social.raw", self.current_sentiment)

        # Get historical data
        historical_data = await feature_store.get_time_series(symbol, "social.raw")

        # If we don't have enough data, return neutral signal
        if len(historical_data) < self.delta_window:
            logger.warning(f"Not enough historical data for sentiment analysis: {len(historical_data)} < {self.delta_window}")
            return self._default_prediction()

        # Extract sentiment values from time series
        sentiment_values = [value for _, value in historical_data]
        self._historical_data = sentiment_values

        # Calculate delta (change in sentiment)
        if len(sentiment_values) >= self.delta_window:
            self.sentiment_delta = self.current_sentiment - sentiment_values[-self.delta_window]
        else:
            # Not enough data for delta, use simple difference from first value
            self.sentiment_delta = self.current_sentiment - sentiment_values[0]

        # Calculate z-score
        if len(sentiment_values) >= self.z_window:
            mean = np.mean(sentiment_values)
            std = np.std(sentiment_values) or 1.0  # Avoid division by zero
            self.sentiment_z = (self.sentiment_delta - mean) / std
        else:
            # Not enough data for z-score, use simple normalization
            max_delta = max(abs(self.sentiment_delta), 1.0)
            self.sentiment_z = self.sentiment_delta / max_delta

        # Generate signal based on z-score and contrarian flag
        signal, confidence = self._generate_signal(self.sentiment_z)

        # Store derived features in feature store
        await feature_store.set(symbol, "social.delta", self.sentiment_delta)
        await feature_store.set(symbol, "social.z", self.sentiment_z)

        # Update state
        self.signal = signal
        self.confidence = confidence
        self._last_update = datetime.now()

        return {
            "signal": signal,
            "confidence": confidence,
            "sentiment": self.current_sentiment,
            "delta": self.sentiment_delta,
            "z_score": self.sentiment_z,
            "contrarian": self.contrarian
        }

    def _generate_signal(self, z_score: float) -> Tuple[str, float]:
        """
        Generate a trading signal based on the z-score.

        Args:
            z_score: Z-score of sentiment delta

        Returns:
            Tuple of (signal, confidence)
        """
        # Determine signal direction
        if abs(z_score) < self.threshold:
            return "HOLD", 0.0

        # For sentiment delta (change in sentiment):
        # Contrarian logic: positive delta (rising sentiment) -> SELL, negative delta (falling sentiment) -> BUY
        # Trend-following logic: positive delta (rising sentiment) -> BUY, negative delta (falling sentiment) -> SELL
        if z_score > self.threshold:
            # Positive z-score means sentiment is rising
            signal = "SELL" if self.contrarian else "BUY"
        elif z_score < -self.threshold:
            # Negative z-score means sentiment is falling
            signal = "BUY" if self.contrarian else "SELL"
        else:
            # Z-score within threshold range
            signal = "HOLD"

        # Confidence is proportional to the absolute z-score
        confidence = min(1.0, abs(z_score) / (2 * self.threshold))

        return signal, confidence

    def _default_prediction(self) -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Returns:
            Default prediction dictionary
        """
        return {
            "signal": "HOLD",
            "confidence": 0.0,
            "sentiment": 50.0,  # Neutral sentiment
            "delta": 0.0,
            "z_score": 0.0,
            "contrarian": self.contrarian
        }

    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the model.

        Returns:
            Dictionary of model state
        """
        return {
            "name": self.name,
            "sentiment": self.current_sentiment,
            "delta": self.sentiment_delta,
            "z_score": self.sentiment_z,
            "signal": self.signal,
            "confidence": self.confidence,
            "contrarian": self.contrarian,
            "threshold": self.threshold,
            "delta_window": self.delta_window,
            "z_window": self.z_window
        }
