"""
Production Configuration Manager
Enterprise-grade configuration management for Money Circle production deployment
"""

import os
import json
import logging
import secrets
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    main_db_path: str = "data/money_circle.db"
    bus_db_path: str = "data/bus.db"
    connection_timeout: int = 30
    backup_enabled: bool = True
    backup_interval: int = 3600  # 1 hour
    max_connections: int = 100
    wal_mode: bool = True
    synchronous: str = "NORMAL"
    cache_size: int = -64000  # 64MB

@dataclass
class SecurityConfig:
    """Security configuration settings."""
    jwt_secret: str = ""
    session_timeout: int = 3600  # 1 hour
    max_login_attempts: int = 5
    lockout_duration: int = 900  # 15 minutes
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # 1 hour
    csrf_protection: bool = True
    secure_cookies: bool = True
    https_only: bool = False
    password_min_length: int = 8
    password_require_special: bool = True

@dataclass
class ServerConfig:
    """Server configuration settings."""
    host: str = "0.0.0.0"
    port: int = 8086
    debug: bool = False
    workers: int = 1
    max_request_size: int = 16 * 1024 * 1024  # 16MB
    request_timeout: int = 60
    keepalive_timeout: int = 30
    client_timeout: int = 60
    ssl_cert_path: str = ""
    ssl_key_path: str = ""

@dataclass
class MonitoringConfig:
    """Monitoring configuration settings."""
    enabled: bool = True
    metrics_interval: int = 60
    performance_monitoring: bool = True
    error_tracking: bool = True
    log_level: str = "INFO"
    log_file: str = "logs/money_circle.log"
    max_log_size: int = 100 * 1024 * 1024  # 100MB
    log_backup_count: int = 5
    health_check_interval: int = 30

@dataclass
class BackupConfig:
    """Backup configuration settings."""
    enabled: bool = True
    backup_dir: str = "backups"
    backup_interval: int = 3600  # 1 hour
    max_backups: int = 30
    compression_enabled: bool = True
    remote_backup_enabled: bool = False
    remote_backup_url: str = ""
    encryption_enabled: bool = False

@dataclass
class TradingConfig:
    """Trading configuration settings."""
    live_trading_enabled: bool = False
    testnet_mode: bool = True
    max_position_size: float = 1000.0
    risk_limit_percent: float = 2.0
    auto_trader_enabled: bool = True
    signals_enabled: bool = True
    portfolio_analytics_enabled: bool = True
    social_trading_enabled: bool = True

@dataclass
class ExchangeConfig:
    """Exchange configuration settings."""
    binance_enabled: bool = True
    htx_enabled: bool = True
    bybit_enabled: bool = True
    connection_timeout: int = 30
    retry_attempts: int = 3
    rate_limit_enabled: bool = True
    websocket_reconnect: bool = True

class ProductionConfigManager:
    """Manages production configuration for Money Circle platform."""
    
    def __init__(self, config_file: str = "production_config.json"):
        self.config_file = config_file
        self.config = self._load_or_create_config()
        
        # Validate configuration
        self._validate_config()
        
        logger.info("[CONFIG] Production configuration loaded successfully")

    def _load_or_create_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                logger.info(f"[CONFIG] Loaded configuration from {self.config_file}")
                return config_data
            except Exception as e:
                logger.warning(f"[CONFIG] Failed to load config file: {e}")
                return self._create_default_config()
        else:
            logger.info("[CONFIG] Creating default production configuration")
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """Create default production configuration."""
        config = {
            'environment': 'production',
            'version': '1.0.0',
            'created_at': '2025-06-01T08:00:00Z',
            'database': asdict(DatabaseConfig()),
            'security': asdict(SecurityConfig()),
            'server': asdict(ServerConfig()),
            'monitoring': asdict(MonitoringConfig()),
            'backup': asdict(BackupConfig()),
            'trading': asdict(TradingConfig()),
            'exchange': asdict(ExchangeConfig())
        }
        
        # Generate secure JWT secret
        config['security']['jwt_secret'] = secrets.token_urlsafe(32)
        
        # Set production defaults
        config['server']['debug'] = False
        config['server']['host'] = '0.0.0.0'
        config['security']['https_only'] = True
        config['security']['secure_cookies'] = True
        config['trading']['testnet_mode'] = False
        config['trading']['live_trading_enabled'] = True
        
        # Save default configuration
        self.save_config(config)
        
        return config

    def _validate_config(self):
        """Validate configuration settings."""
        errors = []
        
        # Validate required fields
        required_sections = ['database', 'security', 'server', 'monitoring', 'backup', 'trading', 'exchange']
        for section in required_sections:
            if section not in self.config:
                errors.append(f"Missing required section: {section}")
        
        # Validate security settings
        if not self.config.get('security', {}).get('jwt_secret'):
            errors.append("JWT secret is required for production")
        
        # Validate server settings
        server_config = self.config.get('server', {})
        if server_config.get('debug', False):
            logger.warning("[CONFIG] Debug mode is enabled in production")
        
        # Validate SSL settings for production
        if self.config.get('environment') == 'production':
            if not server_config.get('ssl_cert_path') or not server_config.get('ssl_key_path'):
                logger.warning("[CONFIG] SSL certificates not configured for production")
        
        # Validate trading settings
        trading_config = self.config.get('trading', {})
        if trading_config.get('live_trading_enabled') and trading_config.get('testnet_mode'):
            errors.append("Cannot enable both live trading and testnet mode")
        
        if errors:
            for error in errors:
                logger.error(f"[CONFIG] Validation error: {error}")
            raise ValueError(f"Configuration validation failed: {errors}")
        
        logger.info("[CONFIG] Configuration validation passed")

    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file."""
        if config is None:
            config = self.config
        
        try:
            # Create config directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else '.', exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"[CONFIG] Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"[CONFIG] Failed to save configuration: {e}")
            raise

    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration."""
        db_config = self.config.get('database', {})
        return DatabaseConfig(**db_config)

    def get_security_config(self) -> SecurityConfig:
        """Get security configuration."""
        security_config = self.config.get('security', {})
        return SecurityConfig(**security_config)

    def get_server_config(self) -> ServerConfig:
        """Get server configuration."""
        server_config = self.config.get('server', {})
        return ServerConfig(**server_config)

    def get_monitoring_config(self) -> MonitoringConfig:
        """Get monitoring configuration."""
        monitoring_config = self.config.get('monitoring', {})
        return MonitoringConfig(**monitoring_config)

    def get_backup_config(self) -> BackupConfig:
        """Get backup configuration."""
        backup_config = self.config.get('backup', {})
        return BackupConfig(**backup_config)

    def get_trading_config(self) -> TradingConfig:
        """Get trading configuration."""
        trading_config = self.config.get('trading', {})
        return TradingConfig(**trading_config)

    def get_exchange_config(self) -> ExchangeConfig:
        """Get exchange configuration."""
        exchange_config = self.config.get('exchange', {})
        return ExchangeConfig(**exchange_config)

    def update_config_section(self, section: str, updates: Dict[str, Any]):
        """Update a specific configuration section."""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section].update(updates)
        self.save_config()
        
        logger.info(f"[CONFIG] Updated configuration section: {section}")

    def enable_production_mode(self):
        """Configure settings for production deployment."""
        production_updates = {
            'environment': 'production',
            'server': {
                'debug': False,
                'host': '0.0.0.0',
                'workers': 4
            },
            'security': {
                'https_only': True,
                'secure_cookies': True,
                'csrf_protection': True,
                'rate_limit_requests': 1000,
                'session_timeout': 7200  # 2 hours
            },
            'trading': {
                'testnet_mode': False,
                'live_trading_enabled': True
            },
            'monitoring': {
                'enabled': True,
                'log_level': 'INFO',
                'performance_monitoring': True,
                'error_tracking': True
            },
            'backup': {
                'enabled': True,
                'backup_interval': 1800,  # 30 minutes
                'compression_enabled': True
            }
        }
        
        for section, updates in production_updates.items():
            self.update_config_section(section, updates)
        
        logger.info("[CONFIG] Production mode enabled")

    def enable_development_mode(self):
        """Configure settings for development."""
        development_updates = {
            'environment': 'development',
            'server': {
                'debug': True,
                'host': 'localhost',
                'workers': 1
            },
            'security': {
                'https_only': False,
                'secure_cookies': False,
                'rate_limit_requests': 100
            },
            'trading': {
                'testnet_mode': True,
                'live_trading_enabled': False
            },
            'monitoring': {
                'log_level': 'DEBUG'
            }
        }
        
        for section, updates in development_updates.items():
            self.update_config_section(section, updates)
        
        logger.info("[CONFIG] Development mode enabled")

    def get_environment_variables(self) -> Dict[str, str]:
        """Get environment variables for deployment."""
        env_vars = {}
        
        # Server configuration
        server_config = self.get_server_config()
        env_vars['HOST'] = server_config.host
        env_vars['PORT'] = str(server_config.port)
        env_vars['DEBUG'] = str(server_config.debug).lower()
        
        # Security configuration
        security_config = self.get_security_config()
        env_vars['JWT_SECRET'] = security_config.jwt_secret
        env_vars['SESSION_TIMEOUT'] = str(security_config.session_timeout)
        
        # Database configuration
        db_config = self.get_database_config()
        env_vars['DATABASE_PATH'] = db_config.main_db_path
        env_vars['BUS_DATABASE_PATH'] = db_config.bus_db_path
        
        # Trading configuration
        trading_config = self.get_trading_config()
        env_vars['TESTNET_MODE'] = str(trading_config.testnet_mode).lower()
        env_vars['LIVE_TRADING_ENABLED'] = str(trading_config.live_trading_enabled).lower()
        
        return env_vars

    def generate_deployment_script(self) -> str:
        """Generate deployment script for production."""
        script = f"""#!/bin/bash
# Money Circle Production Deployment Script
# Generated on {self.config.get('created_at', 'unknown')}

set -e

echo "🚀 Starting Money Circle Production Deployment"

# Environment variables
export ENVIRONMENT=production
export HOST={self.get_server_config().host}
export PORT={self.get_server_config().port}
export DEBUG=false

# Create necessary directories
mkdir -p data
mkdir -p logs
mkdir -p backups
mkdir -p static

# Set proper permissions
chmod 755 data
chmod 755 logs
chmod 755 backups

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Database initialization
echo "🗄️ Initializing databases..."
python -c "from database.models import DatabaseManager; DatabaseManager().initialize_database()"

# Security setup
echo "🔒 Setting up security..."
python -c "from security.security_manager import SecurityManager; print('Security manager initialized')"

# Start services
echo "🌟 Starting Money Circle platform..."
python app.py

echo "✅ Money Circle deployment completed successfully!"
echo "🌐 Platform available at: http://{self.get_server_config().host}:{self.get_server_config().port}"
"""
        return script

    def generate_docker_config(self) -> Dict[str, str]:
        """Generate Docker configuration files."""
        dockerfile = f"""FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p data logs backups static

# Set environment variables
ENV ENVIRONMENT=production
ENV HOST=0.0.0.0
ENV PORT={self.get_server_config().port}
ENV DEBUG=false

# Expose port
EXPOSE {self.get_server_config().port}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:{self.get_server_config().port}/health || exit 1

# Start application
CMD ["python", "app.py"]
"""

        docker_compose = f"""version: '3.8'

services:
  money-circle:
    build: .
    ports:
      - "{self.get_server_config().port}:{self.get_server_config().port}"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
    environment:
      - ENVIRONMENT=production
      - HOST=0.0.0.0
      - PORT={self.get_server_config().port}
      - DEBUG=false
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{self.get_server_config().port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - money-circle
    restart: unless-stopped
"""

        nginx_config = f"""events {{
    worker_connections 1024;
}}

http {{
    upstream money_circle {{
        server money-circle:{self.get_server_config().port};
    }}

    server {{
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }}

    server {{
        listen 443 ssl http2;
        server_name _;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {{
            proxy_pass http://money_circle;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }}
    }}
}}
"""

        return {
            'Dockerfile': dockerfile,
            'docker-compose.yml': docker_compose,
            'nginx.conf': nginx_config
        }

    def export_config(self, format: str = 'json') -> str:
        """Export configuration in specified format."""
        if format.lower() == 'json':
            return json.dumps(self.config, indent=2)
        elif format.lower() == 'env':
            env_vars = self.get_environment_variables()
            return '\n'.join([f"{key}={value}" for key, value in env_vars.items()])
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for monitoring."""
        return {
            'environment': self.config.get('environment', 'unknown'),
            'version': self.config.get('version', 'unknown'),
            'server_host': self.get_server_config().host,
            'server_port': self.get_server_config().port,
            'debug_mode': self.get_server_config().debug,
            'security_enabled': self.get_security_config().csrf_protection,
            'backup_enabled': self.get_backup_config().enabled,
            'monitoring_enabled': self.get_monitoring_config().enabled,
            'live_trading': self.get_trading_config().live_trading_enabled,
            'testnet_mode': self.get_trading_config().testnet_mode
        }
