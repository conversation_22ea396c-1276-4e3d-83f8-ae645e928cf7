"""
Real-time model performance monitoring for the smart-trader system.

This module provides comprehensive monitoring of model performance,
signal quality, and prediction accuracy in real-time.
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

import numpy as np

from core.feature_store import feature_store

logger = logging.getLogger(__name__)


@dataclass
class ModelMetrics:
    """Metrics for a single model."""
    model_name: str
    total_predictions: int = 0
    correct_predictions: int = 0
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    avg_confidence: float = 0.0
    avg_latency_ms: float = 0.0
    last_prediction_time: Optional[datetime] = None
    signal_strength_avg: float = 0.0
    signal_strength_std: float = 0.0
    error_rate: float = 0.0
    uptime_percentage: float = 100.0


@dataclass
class SignalMetrics:
    """Metrics for trading signals."""
    total_signals: int = 0
    buy_signals: int = 0
    sell_signals: int = 0
    avg_signal_strength: float = 0.0
    signal_frequency_per_hour: float = 0.0
    last_signal_time: Optional[datetime] = None
    signal_accuracy: float = 0.0
    profitable_signals: int = 0
    unprofitable_signals: int = 0


@dataclass
class SystemHealth:
    """Overall system health metrics."""
    uptime_seconds: float = 0.0
    total_memory_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    active_models: int = 0
    failed_models: int = 0
    message_queue_size: int = 0
    last_health_check: Optional[datetime] = None
    alerts: List[str] = None

    def __post_init__(self):
        if self.alerts is None:
            self.alerts = []


class ModelPerformanceMonitor:
    """
    Real-time model performance monitoring system.

    Tracks model accuracy, latency, signal quality, and system health.
    Provides alerts for performance degradation and system issues.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the model performance monitor.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.monitoring_enabled = config.get("monitoring", {}).get("enabled", True)
        self.alert_thresholds = config.get("monitoring", {}).get("alert_thresholds", {})

        # Performance tracking
        self.model_metrics: Dict[str, ModelMetrics] = {}
        self.signal_metrics: Dict[str, SignalMetrics] = {}
        self.system_health = SystemHealth()

        # Historical data (last 24 hours)
        self.prediction_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1440))  # 1 per minute
        self.signal_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1440))
        self.performance_history: deque = deque(maxlen=1440)

        # Timing
        self.start_time = datetime.now()
        self.last_update = datetime.now()
        self.update_interval = timedelta(seconds=config.get("monitoring", {}).get("update_interval", 60))

        # Alerts
        self.active_alerts: List[str] = []
        self.alert_cooldown: Dict[str, datetime] = {}
        self.alert_cooldown_minutes = 15

    async def start(self) -> None:
        """Start the monitoring system."""
        if not self.monitoring_enabled:
            logger.info("Model monitoring disabled")
            return

        logger.info("Starting model performance monitor")

        # Start monitoring tasks
        asyncio.create_task(self._monitor_loop())
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._cleanup_loop())

    async def record_prediction(
        self,
        model_name: str,
        prediction: Dict[str, Any],
        latency_ms: float,
        features: Dict[str, Any]
    ) -> None:
        """
        Record a model prediction for performance tracking.

        Args:
            model_name: Name of the model
            prediction: Model prediction result
            latency_ms: Prediction latency in milliseconds
            features: Input features used for prediction
        """
        if not self.monitoring_enabled:
            return

        # Initialize model metrics if not exists
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = ModelMetrics(model_name=model_name)

        metrics = self.model_metrics[model_name]

        # Update basic metrics
        metrics.total_predictions += 1
        metrics.last_prediction_time = datetime.now()

        # Update latency (exponential moving average)
        if metrics.avg_latency_ms == 0:
            metrics.avg_latency_ms = latency_ms
        else:
            metrics.avg_latency_ms = 0.9 * metrics.avg_latency_ms + 0.1 * latency_ms

        # Extract confidence if available
        confidence = prediction.get("confidence", 0.0)
        if confidence > 0:
            if metrics.avg_confidence == 0:
                metrics.avg_confidence = confidence
            else:
                metrics.avg_confidence = 0.9 * metrics.avg_confidence + 0.1 * confidence

        # Extract signal strength if available
        signal_strength = prediction.get("signal_strength", 0.0)
        if signal_strength != 0:
            # Store for statistics calculation
            self.prediction_history[model_name].append({
                "timestamp": datetime.now(),
                "signal_strength": signal_strength,
                "confidence": confidence,
                "latency_ms": latency_ms
            })

        # Store prediction in feature store for analysis
        await feature_store.set(
            "monitoring",
            f"model_prediction_{model_name}",
            {
                "prediction": prediction,
                "latency_ms": latency_ms,
                "timestamp": datetime.now().isoformat()
            }
        )

    async def record_signal(
        self,
        signal_source: str,
        signal: Dict[str, Any]
    ) -> None:
        """
        Record a trading signal for performance tracking.

        Args:
            signal_source: Source of the signal
            signal: Signal data
        """
        if not self.monitoring_enabled:
            return

        # Initialize signal metrics if not exists
        if signal_source not in self.signal_metrics:
            self.signal_metrics[signal_source] = SignalMetrics()

        metrics = self.signal_metrics[signal_source]

        # Update signal counts
        metrics.total_signals += 1
        metrics.last_signal_time = datetime.now()

        # Count signal types
        action = signal.get("action", "").upper()
        if action == "BUY":
            metrics.buy_signals += 1
        elif action == "SELL":
            metrics.sell_signals += 1

        # Update signal strength
        signal_strength = signal.get("score", 0.0)
        if signal_strength > 0:
            if metrics.avg_signal_strength == 0:
                metrics.avg_signal_strength = signal_strength
            else:
                metrics.avg_signal_strength = 0.9 * metrics.avg_signal_strength + 0.1 * signal_strength

        # Store signal history
        self.signal_history[signal_source].append({
            "timestamp": datetime.now(),
            "action": action,
            "signal_strength": signal_strength,
            "symbol": signal.get("symbol", ""),
            "rationale": signal.get("rationale", "")
        })

    async def record_trade_outcome(
        self,
        signal_source: str,
        profitable: bool,
        pnl: float
    ) -> None:
        """
        Record the outcome of a trade based on a signal.

        Args:
            signal_source: Source of the original signal
            profitable: Whether the trade was profitable
            pnl: Profit/loss amount
        """
        if not self.monitoring_enabled:
            return

        if signal_source in self.signal_metrics:
            metrics = self.signal_metrics[signal_source]

            if profitable:
                metrics.profitable_signals += 1
            else:
                metrics.unprofitable_signals += 1

            # Calculate signal accuracy
            total_completed = metrics.profitable_signals + metrics.unprofitable_signals
            if total_completed > 0:
                metrics.signal_accuracy = metrics.profitable_signals / total_completed

    async def get_model_performance(self, model_name: str) -> Optional[ModelMetrics]:
        """Get performance metrics for a specific model."""
        return self.model_metrics.get(model_name)

    async def get_all_model_performance(self) -> Dict[str, ModelMetrics]:
        """Get performance metrics for all models."""
        return self.model_metrics.copy()

    async def get_signal_performance(self, signal_source: str) -> Optional[SignalMetrics]:
        """Get performance metrics for a specific signal source."""
        return self.signal_metrics.get(signal_source)

    async def get_system_health(self) -> SystemHealth:
        """Get overall system health metrics."""
        return self.system_health

    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        # Calculate overall statistics
        total_predictions = sum(m.total_predictions for m in self.model_metrics.values())

        # Handle empty arrays safely
        accuracy_values = [m.accuracy for m in self.model_metrics.values() if m.accuracy > 0]
        avg_accuracy = np.mean(accuracy_values) if accuracy_values else 0.0

        latency_values = [m.avg_latency_ms for m in self.model_metrics.values() if m.avg_latency_ms > 0]
        avg_latency = np.mean(latency_values) if latency_values else 0.0

        total_signals = sum(m.total_signals for m in self.signal_metrics.values())

        signal_accuracy_values = [m.signal_accuracy for m in self.signal_metrics.values() if m.signal_accuracy > 0]
        avg_signal_accuracy = np.mean(signal_accuracy_values) if signal_accuracy_values else 0.0

        return {
            "timestamp": datetime.now().isoformat(),
            "uptime_hours": (datetime.now() - self.start_time).total_seconds() / 3600,
            "models": {
                "total_models": len(self.model_metrics),
                "active_models": len([m for m in self.model_metrics.values() if m.last_prediction_time and
                                    (datetime.now() - m.last_prediction_time).total_seconds() < 300]),
                "total_predictions": total_predictions,
                "avg_accuracy": float(avg_accuracy) if not np.isnan(avg_accuracy) else 0.0,
                "avg_latency_ms": float(avg_latency) if not np.isnan(avg_latency) else 0.0
            },
            "signals": {
                "total_signal_sources": len(self.signal_metrics),
                "total_signals": total_signals,
                "avg_signal_accuracy": float(avg_signal_accuracy) if not np.isnan(avg_signal_accuracy) else 0.0
            },
            "system_health": asdict(self.system_health),
            "active_alerts": self.active_alerts.copy()
        }

    async def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while True:
            try:
                await self._update_metrics()
                await self._check_alerts()
                await asyncio.sleep(self.update_interval.total_seconds())
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)

    async def _health_check_loop(self) -> None:
        """Health check loop."""
        while True:
            try:
                await self._update_system_health()
                await asyncio.sleep(30)  # Health check every 30 seconds
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(60)

    async def _cleanup_loop(self) -> None:
        """Cleanup old data loop."""
        while True:
            try:
                await self._cleanup_old_data()
                await asyncio.sleep(3600)  # Cleanup every hour
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(3600)

    async def _update_metrics(self) -> None:
        """Update calculated metrics."""
        # Update model metrics
        for model_name, metrics in self.model_metrics.items():
            history = self.prediction_history[model_name]
            if len(history) > 1:
                signal_strengths = [h["signal_strength"] for h in history if h["signal_strength"] != 0]
                if signal_strengths:
                    metrics.signal_strength_avg = np.mean(signal_strengths)
                    metrics.signal_strength_std = np.std(signal_strengths)

        # Update signal metrics
        for signal_source, metrics in self.signal_metrics.items():
            history = self.signal_history[signal_source]
            if len(history) > 0:
                # Calculate signals per hour
                now = datetime.now()
                recent_signals = [h for h in history if (now - h["timestamp"]).total_seconds() < 3600]
                metrics.signal_frequency_per_hour = len(recent_signals)

    async def _update_system_health(self) -> None:
        """Update system health metrics."""
        self.system_health.uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        self.system_health.active_models = len([m for m in self.model_metrics.values()
                                              if m.last_prediction_time and
                                              (datetime.now() - m.last_prediction_time).total_seconds() < 300])
        self.system_health.failed_models = len(self.model_metrics) - self.system_health.active_models
        self.system_health.last_health_check = datetime.now()

    async def _check_alerts(self) -> None:
        """Check for alert conditions."""
        current_alerts = []

        # Check model performance alerts
        for model_name, metrics in self.model_metrics.items():
            # High latency alert
            latency_threshold = self.alert_thresholds.get("max_latency_ms", 1000)
            if metrics.avg_latency_ms > latency_threshold:
                alert = f"High latency in {model_name}: {metrics.avg_latency_ms:.1f}ms"
                current_alerts.append(alert)

            # Low accuracy alert
            accuracy_threshold = self.alert_thresholds.get("min_accuracy", 0.6)
            if metrics.accuracy > 0 and metrics.accuracy < accuracy_threshold:
                alert = f"Low accuracy in {model_name}: {metrics.accuracy:.2f}"
                current_alerts.append(alert)

            # Model not responding alert
            if metrics.last_prediction_time:
                time_since_last = (datetime.now() - metrics.last_prediction_time).total_seconds()
                if time_since_last > 600:  # 10 minutes
                    alert = f"Model {model_name} not responding for {time_since_last/60:.1f} minutes"
                    current_alerts.append(alert)

        # Update active alerts
        self.active_alerts = current_alerts

        # Log new alerts
        for alert in current_alerts:
            if alert not in self.alert_cooldown or \
               (datetime.now() - self.alert_cooldown[alert]).total_seconds() > self.alert_cooldown_minutes * 60:
                logger.warning(f"ALERT: {alert}")
                self.alert_cooldown[alert] = datetime.now()

    async def _cleanup_old_data(self) -> None:
        """Clean up old monitoring data."""
        cutoff_time = datetime.now() - timedelta(hours=24)

        # Clean prediction history
        for model_name in self.prediction_history:
            history = self.prediction_history[model_name]
            while history and history[0]["timestamp"] < cutoff_time:
                history.popleft()

        # Clean signal history
        for signal_source in self.signal_history:
            history = self.signal_history[signal_source]
            while history and history[0]["timestamp"] < cutoff_time:
                history.popleft()
