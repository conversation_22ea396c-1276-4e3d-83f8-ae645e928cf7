"""
Utility functions for position management.
"""

import os
import json
import logging
import async<PERSON>
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, cast

from position_manager import PositionManager
from executors.htx_executor import HTXExecutor
from feeds.htx_futures import HTXFuturesClient

logger = logging.getLogger(__name__)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


async def save_positions(position_manager: PositionManager, file_path: str) -> bool:
    """
    Save position data to a file.

    Args:
        position_manager: Position manager instance
        file_path: Path to save the file

    Returns:
        True if save was successful
    """
    try:
        # Export position data
        export_data = position_manager.export_positions()

        # Save to file
        with open(file_path, 'w') as f:
            # Custom JSON encoder to handle datetime objects
            class DateTimeEncoder(json.JSONEncoder):
                def default(self, obj):
                    if isinstance(obj, datetime):
                        return obj.isoformat()
                    return super().default(obj)

            json.dump(export_data, f, indent=2, cls=DateTimeEncoder)

        logger.info(f"Saved position data to {file_path}")
        return True

    except Exception as e:
        logger.error(f"Error saving position data: {e}")
        return False


async def load_positions(position_manager: PositionManager, file_path: str) -> bool:
    """
    Load position data from a file.

    Args:
        position_manager: Position manager instance
        file_path: Path to load the file from

    Returns:
        True if load was successful
    """
    try:
        # Load from file
        with open(file_path, 'r') as f:
            import_data = json.load(f)

        # Import position data
        success = await position_manager.import_positions(import_data)

        if success:
            logger.info(f"Loaded position data from {file_path}")

        return success

    except Exception as e:
        logger.error(f"Error loading position data: {e}")
        return False


async def get_position_manager(config_file: str) -> Optional[PositionManager]:
    """
    Get a position manager instance.

    Args:
        config_file: Path to configuration file

    Returns:
        Position manager instance or None if failed
    """
    try:
        # Load configuration
        with open(config_file, 'r') as f:
            config = json.load(f)

        # Try to get executor instance
        executor = HTXExecutor.get_instance()

        if not executor:
            # Create HTX client
            htx_client = HTXFuturesClient(
                api_key=config.get("exchange", {}).get("api_key", ""),
                api_secret=config.get("exchange", {}).get("api_secret", ""),
                testnet=config.get("exchange", {}).get("testnet", True)
            )

            # Create executor
            executor = HTXExecutor(
                client=htx_client,
                config=config,
                simulation_mode=True
            )

            # Create position manager
            position_manager = PositionManager(
                executor=executor,
                config=config
            )

            # Set position manager on executor
            executor.position_manager = position_manager
        else:
            # Get position manager from executor
            position_manager = executor.position_manager

        return position_manager

    except Exception as e:
        logger.error(f"Error getting position manager: {e}")
        return None


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Position management utilities")
    parser.add_argument("--config", "-c", default="config_testnet.yaml", help="Path to configuration file")
    parser.add_argument("--save", "-s", help="Save positions to file")
    parser.add_argument("--load", "-l", help="Load positions from file")
    parser.add_argument("--dashboard", "-d", action="store_true", help="Display position dashboard")
    args = parser.parse_args()

    # Get position manager
    position_manager = await get_position_manager(args.config)

    if not position_manager:
        logger.error("Failed to get position manager")
        return

    # Save positions
    if args.save:
        await save_positions(position_manager, args.save)

    # Load positions
    if args.load:
        await load_positions(position_manager, args.load)

    # Display dashboard
    if args.dashboard:
        dashboard = position_manager.get_position_dashboard()
        print("\n" + dashboard + "\n")


if __name__ == "__main__":
    asyncio.run(main())
