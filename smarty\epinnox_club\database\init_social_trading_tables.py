#!/usr/bin/env python3
"""
Initialize Social Trading Database Tables
Creates all missing social trading tables for Money Circle platform
"""

import sqlite3
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SocialTradingTableInitializer:
    """Initialize social trading database tables."""

    def __init__(self, db_path: str = 'data/money_circle.db'):
        """Initialize the table creator."""
        self.db_path = db_path
        self.conn = None
        logger.info("Social Trading Table Initializer created")

    def connect(self):
        """Connect to database."""
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row

            # Enable foreign keys
            self.conn.execute("PRAGMA foreign_keys=ON")

            logger.info(f"✅ Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection error: {e}")
            return False

    def create_all_social_tables(self):
        """Create all social trading tables."""
        try:
            logger.info("🔧 Creating social trading database tables...")

            # Create tables in dependency order
            self.create_strategy_proposals_table()
            self.create_strategy_following_table()
            self.create_strategy_discussions_table()
            self.create_member_activities_table()
            self.create_strategy_performance_table()
            self.create_social_metrics_table()
            self.create_strategy_votes_table()

            # Commit all changes
            self.conn.commit()
            logger.info("✅ All social trading tables created successfully")

            return True

        except Exception as e:
            logger.error(f"❌ Error creating social trading tables: {e}")
            if self.conn:
                self.conn.rollback()
            return False

    def create_strategy_proposals_table(self):
        """Create strategy proposals table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_proposals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    creator_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    strategy_type TEXT DEFAULT 'general',
                    risk_level TEXT CHECK(risk_level IN ('low', 'medium', 'high')) DEFAULT 'medium',
                    expected_return REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    time_horizon TEXT DEFAULT 'medium_term',
                    min_investment REAL DEFAULT 1000.0,
                    performance_fee REAL DEFAULT 20.0,
                    management_fee REAL DEFAULT 2.0,
                    status TEXT CHECK(status IN ('proposed', 'active', 'paused', 'closed')) DEFAULT 'proposed',
                    votes_for INTEGER DEFAULT 0,
                    votes_against INTEGER DEFAULT 0,
                    total_followers INTEGER DEFAULT 0,
                    total_invested REAL DEFAULT 0.0,
                    current_return REAL DEFAULT 0.0,
                    is_public BOOLEAN DEFAULT TRUE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            logger.info("✅ Created strategy_proposals table")
        except Exception as e:
            logger.error(f"❌ Error creating strategy_proposals table: {e}")
            raise

    def create_strategy_following_table(self):
        """Create strategy following table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_following (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    follower_id INTEGER NOT NULL,
                    strategy_id INTEGER NOT NULL,
                    strategy_creator_id INTEGER NOT NULL,
                    investment_amount REAL DEFAULT 0.0,
                    allocation_percentage REAL DEFAULT 0.0,
                    auto_copy BOOLEAN DEFAULT FALSE,
                    max_risk_per_trade REAL DEFAULT 2.0,
                    stop_loss_percentage REAL DEFAULT 5.0,
                    is_active BOOLEAN DEFAULT TRUE,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    paused_at TIMESTAMP,
                    total_return REAL DEFAULT 0.0,
                    total_trades INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (follower_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    FOREIGN KEY (strategy_creator_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(follower_id, strategy_id)
                )
            """)
            logger.info("✅ Created strategy_following table")
        except Exception as e:
            logger.error(f"❌ Error creating strategy_following table: {e}")
            raise

    def create_strategy_discussions_table(self):
        """Create strategy discussions table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_discussions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    parent_id INTEGER,
                    content TEXT NOT NULL,
                    discussion_type TEXT DEFAULT 'comment',
                    likes_count INTEGER DEFAULT 0,
                    is_pinned BOOLEAN DEFAULT FALSE,
                    is_edited BOOLEAN DEFAULT FALSE,
                    edit_timestamp TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (parent_id) REFERENCES strategy_discussions (id) ON DELETE CASCADE
                )
            """)
            logger.info("✅ Created strategy_discussions table")
        except Exception as e:
            logger.error(f"❌ Error creating strategy_discussions table: {e}")
            raise

    def create_member_activities_table(self):
        """Create member activities table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS member_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    activity_type TEXT NOT NULL,
                    activity_data TEXT NOT NULL,
                    related_user_id INTEGER,
                    related_strategy_id INTEGER,
                    activity_value REAL,
                    is_public BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (related_user_id) REFERENCES users (id) ON DELETE SET NULL,
                    FOREIGN KEY (related_strategy_id) REFERENCES strategy_proposals (id) ON DELETE SET NULL
                )
            """)
            logger.info("✅ Created member_activities table")
        except Exception as e:
            logger.error(f"❌ Error creating member_activities table: {e}")
            raise

    def create_strategy_performance_table(self):
        """Create strategy performance tracking table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    total_value REAL DEFAULT 0.0,
                    daily_return REAL DEFAULT 0.0,
                    cumulative_return REAL DEFAULT 0.0,
                    drawdown REAL DEFAULT 0.0,
                    trades_count INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    sharpe_ratio REAL DEFAULT 0.0,
                    volatility REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    UNIQUE(strategy_id, date)
                )
            """)
            logger.info("✅ Created strategy_performance table")
        except Exception as e:
            logger.error(f"❌ Error creating strategy_performance table: {e}")
            raise

    def create_social_metrics_table(self):
        """Create social metrics table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS social_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    followers_count INTEGER DEFAULT 0,
                    following_count INTEGER DEFAULT 0,
                    strategies_shared INTEGER DEFAULT 0,
                    total_followers_gained INTEGER DEFAULT 0,
                    reputation_score REAL DEFAULT 0.0,
                    influence_score REAL DEFAULT 0.0,
                    engagement_rate REAL DEFAULT 0.0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(user_id)
                )
            """)
            logger.info("✅ Created social_metrics table")
        except Exception as e:
            logger.error(f"❌ Error creating social_metrics table: {e}")
            raise

    def create_strategy_votes_table(self):
        """Create strategy votes table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_votes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    vote TEXT CHECK(vote IN ('approve', 'reject', 'abstain')) NOT NULL,
                    reasoning TEXT,
                    comment TEXT,
                    vote_weight REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(strategy_id, user_id)
                )
            """)
            logger.info("✅ Created strategy_votes table")
        except Exception as e:
            logger.error(f"❌ Error creating strategy_votes table: {e}")
            raise

    def create_demo_data(self):
        """Create demo data for social trading features."""
        try:
            logger.info("🎭 Creating demo social trading data...")

            # Get epinnox user ID
            cursor = self.conn.execute("SELECT id FROM users WHERE username = 'epinnox'")
            epinnox_user = cursor.fetchone()
            if not epinnox_user:
                logger.warning("⚠️ Epinnox user not found, skipping demo data")
                return

            epinnox_id = epinnox_user['id']

            # Create demo strategy proposals
            demo_strategies = [
                {
                    'title': 'DOGE Momentum Scalping',
                    'description': 'High-frequency scalping strategy for DOGE/USDT with 1-minute timeframes',
                    'strategy_type': 'scalping',
                    'risk_level': 'high',
                    'expected_return': 15.5,
                    'max_drawdown': 8.2
                },
                {
                    'title': 'BTC DCA Strategy',
                    'description': 'Dollar-cost averaging strategy for Bitcoin with weekly purchases',
                    'strategy_type': 'dca',
                    'risk_level': 'low',
                    'expected_return': 12.0,
                    'max_drawdown': 15.0
                },
                {
                    'title': 'Multi-Asset Trend Following',
                    'description': 'Diversified trend following across major cryptocurrencies',
                    'strategy_type': 'trend_following',
                    'risk_level': 'medium',
                    'expected_return': 18.7,
                    'max_drawdown': 12.5
                }
            ]

            strategy_ids = []
            for strategy in demo_strategies:
                # Check what columns exist in the table
                cursor = self.conn.execute("PRAGMA table_info(strategy_proposals)")
                columns = [row[1] for row in cursor.fetchall()]

                # Determine the correct user column name
                if 'creator_id' in columns:
                    user_column = 'creator_id'
                elif 'user_id' in columns:
                    user_column = 'user_id'
                elif 'proposed_by' in columns:
                    user_column = 'proposed_by'
                else:
                    logger.error("No user column found in strategy_proposals table")
                    continue

                # Build insert query based on available columns
                insert_columns = [user_column, 'title', 'description']
                insert_values = [epinnox_id, strategy['title'], strategy['description']]

                # Add optional columns if they exist
                if 'name' in columns:
                    insert_columns.append('name')
                    insert_values.append(strategy['title'])  # Use title as name
                if 'strategy_type' in columns:
                    insert_columns.append('strategy_type')
                    insert_values.append(strategy['strategy_type'])
                if 'risk_level' in columns:
                    insert_columns.append('risk_level')
                    insert_values.append(strategy['risk_level'])
                if 'status' in columns:
                    insert_columns.append('status')
                    insert_values.append('approved')  # Use 'approved' instead of 'active'

                placeholders = ', '.join(['?' for _ in insert_columns])
                columns_str = ', '.join(insert_columns)

                cursor = self.conn.execute(f"""
                    INSERT INTO strategy_proposals ({columns_str})
                    VALUES ({placeholders})
                """, insert_values)
                strategy_ids.append(cursor.lastrowid)

            # Create social metrics for epinnox user
            self.conn.execute("""
                INSERT OR REPLACE INTO social_metrics (
                    user_id, followers_count, following_count, strategies_shared,
                    reputation_score, influence_score
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (epinnox_id, 12, 5, len(demo_strategies), 85.5, 92.3))

            # Create demo activities
            activities = [
                ('strategy_created', f'Created new strategy: {demo_strategies[0]["title"]}'),
                ('strategy_shared', f'Shared strategy: {demo_strategies[1]["title"]}'),
                ('trade_executed', 'Executed profitable DOGE/USDT trade (+2.3%)'),
                ('milestone_reached', 'Reached 10+ followers milestone'),
            ]

            for activity_type, activity_data in activities:
                # Check what columns exist in member_activities table
                cursor = self.conn.execute("PRAGMA table_info(member_activities)")
                activity_columns = [row[1] for row in cursor.fetchall()]

                # Build insert for member_activities
                activity_insert_columns = ['user_id', 'activity_type', 'activity_data']
                activity_insert_values = [epinnox_id, activity_type, activity_data]

                # Add timestamp column if it exists (could be created_at, timestamp, etc.)
                timestamp_value = (datetime.now() - timedelta(hours=len(activities))).isoformat()
                if 'created_at' in activity_columns:
                    activity_insert_columns.append('created_at')
                    activity_insert_values.append(timestamp_value)
                elif 'timestamp' in activity_columns:
                    activity_insert_columns.append('timestamp')
                    activity_insert_values.append(timestamp_value)

                activity_placeholders = ', '.join(['?' for _ in activity_insert_columns])
                activity_columns_str = ', '.join(activity_insert_columns)

                self.conn.execute(f"""
                    INSERT INTO member_activities ({activity_columns_str})
                    VALUES ({activity_placeholders})
                """, activity_insert_values)

            self.conn.commit()
            logger.info("✅ Demo social trading data created")

        except Exception as e:
            logger.error(f"❌ Error creating demo data: {e}")
            if self.conn:
                self.conn.rollback()

    def verify_tables(self):
        """Verify all tables were created successfully."""
        try:
            logger.info("🔍 Verifying social trading tables...")

            expected_tables = [
                'strategy_proposals',
                'strategy_following',
                'strategy_discussions',
                'member_activities',
                'strategy_performance',
                'social_metrics',
                'strategy_votes'
            ]

            cursor = self.conn.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name IN ({})
            """.format(','.join('?' * len(expected_tables))), expected_tables)

            existing_tables = [row['name'] for row in cursor.fetchall()]

            logger.info(f"📊 Found {len(existing_tables)}/{len(expected_tables)} social trading tables")

            for table in expected_tables:
                if table in existing_tables:
                    logger.info(f"✅ {table}")
                else:
                    logger.error(f"❌ {table} - MISSING")

            return len(existing_tables) == len(expected_tables)

        except Exception as e:
            logger.error(f"❌ Error verifying tables: {e}")
            return False

    def close(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")

def main():
    """Main function."""
    logger.info("=" * 70)
    logger.info("MONEY CIRCLE SOCIAL TRADING TABLES INITIALIZATION")
    logger.info("=" * 70)

    initializer = SocialTradingTableInitializer()

    try:
        # Connect to database
        if not initializer.connect():
            return 1

        # Create all social trading tables
        if not initializer.create_all_social_tables():
            return 1

        # Create demo data
        initializer.create_demo_data()

        # Verify tables
        if not initializer.verify_tables():
            logger.warning("⚠️ Some tables may be missing")

        logger.info("\n🎉 Social trading tables initialization completed!")
        logger.info("The Money Circle platform now has full social trading database support.")

        return 0

    except Exception as e:
        logger.error(f"❌ Initialization failed: {e}")
        return 1
    finally:
        initializer.close()

if __name__ == "__main__":
    sys.exit(main())
