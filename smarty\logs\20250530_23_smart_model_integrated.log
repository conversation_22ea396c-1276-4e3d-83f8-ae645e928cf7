2025-05-30 23:23:59,740 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-30 23:23:59,740 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250530_23_smart_model_integrated.log
2025-05-30 23:23:59,741 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250530_23_smart_model_integrated_events.json
2025-05-30 23:23:59,741 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-30 23:23:59,741 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-30 23:23:59
2025-05-30 23:23:59,742 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-30 23:23:59,742 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit (AMD64)]
2025-05-30 23:23:59,743 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-30 23:23:59,744 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-30 23:23:59,744 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-30 23:23:59,745 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-30 23:23:59,745 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-30 23:23:59,745 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-30 23:23:59,748 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-30 23:23:59,749 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-30 23:23:59,749 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-30 23:23:59,750 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-30 23:23:59,750 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-30 23:24:00,976 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-30 23:24:00,979 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-30 23:24:00,984 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-30 23:24:00,985 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-30 23:24:02,496 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-30 23:24:02,498 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-30 23:24:02,500 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
2025-05-30 23:24:03,715 - strategy.smart_model_integrated - INFO - [info:89] - Loaded 60 historical funding rates for BTC-USDT
2025-05-30 23:24:03,715 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer started successfully
2025-05-30 23:24:03,718 - strategy.smart_model_integrated - INFO - [info:89] - 🧠 LLM Model: Unknown
2025-05-30 23:24:03,718 - strategy.smart_model_integrated - INFO - [info:89] - Position manager started
2025-05-30 23:24:03,718 - strategy.smart_model_integrated - INFO - [info:89] - Starting event loop
2025-05-30 23:24:03,719 - strategy.smart_model_integrated - INFO - [info:89] - Started bus maintenance task
2025-05-30 23:24:03,759 - strategy.smart_model_integrated - INFO - [info:89] - Starting account information update task
2025-05-30 23:24:03,760 - strategy.smart_model_integrated - ERROR - [error:97] - Error updating account information: REST client not initialized
2025-05-30 23:24:03,761 - strategy.smart_model_integrated - INFO - [info:89] - Starting health check task
2025-05-30 23:24:03,761 - strategy.smart_model_integrated - INFO - [info:89] - Starting position monitoring task
2025-05-30 23:24:03,762 - strategy.smart_model_integrated - INFO - [info:89] - Starting funding rate fetching task
2025-05-30 23:24:03,766 - strategy.smart_model_integrated - INFO - [info:89] - Starting open interest fetching task
2025-05-30 23:24:03,769 - strategy.smart_model_integrated - INFO - [info:89] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
