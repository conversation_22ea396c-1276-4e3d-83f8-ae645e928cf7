# SMART-TRADER MASTER CONFIGURATION
# ONE CONFIG TO RULE THEM ALL!
#
# This is the ONLY config file you need for the entire Smart-Trader system.
# All modes (testnet, live, backtesting) use this single configuration.

# SYSTEM MODE
# Options: "testnet", "live", "backtest", "demo"
mode: "testnet"  # Change this to switch between modes

# DEMO MODE SETTINGS
demo_mode: false  # Set to true to enable demo/simulated data for testing

# EXCHANGE SETTINGS
exchange:
  name: "htx"
  testnet: true  # Automatically set based on mode
  api_key: "nbtycf4rw2-72d300ec-fb900970-27ef8"  # EPX account - Positions Working ✅
  api_secret: "b4d92e15-523563a0-72a16ad9-9a275"  # EPX account - Positions Working ✅
  base_url: "https://api.htx.com"
  ws_url: "wss://api.htx.com/ws"

# TRADING SETTINGS
trading:
  enabled: true
  simulation_mode: false  # Set to false for live trading (DANGER!)
  symbols: ["BTC-USDT"]
  max_positions: 1
  position_size_usd: 10.0  # Position size in USD
  leverage: 2
  order_type: "MARKET"
  stop_loss_pct: 3
  take_profit_pct: 1
  max_slippage_bps: 10
  sim_balance: 100.0  # Your $100 trading balance

  # Position manager settings
  position_manager:
    enabled: true
    # Stop-loss and take-profit settings
    trailing_stop: true
    trailing_stop_activation: 1.0  # Activate trailing stop when profit reaches 1%
    trailing_stop_distance: 0.5    # Trailing stop follows price at 0.5% distance

    # Partial take-profit settings
    use_partial_take_profits: true
    partial_tp_levels:
      - { percent: 25, price_pct: 2.0 }  # Take 25% profit at 2% gain
      - { percent: 25, price_pct: 3.0 }  # Take 25% profit at 3% gain
      - { percent: 50, price_pct: 4.0 }  # Take 50% profit at 4% gain

    # Risk management settings
    max_risk_per_trade_pct: 1.0    # Risk 1% of account per trade
    max_open_positions: 3          # Maximum number of open positions
    max_risk_per_symbol_pct: 2.0   # Maximum risk per symbol
    max_daily_drawdown_pct: 5.0    # Maximum daily drawdown

    # Risk overlay settings
    use_risk_overlay: true
    market_risk_threshold: 80.0    # Market risk threshold (0-100)
    volatility_risk_threshold: 2.0 # Volatility multiplier threshold

    # Position sizing settings
    position_sizing_method: "volatility"  # Options: fixed, volatility, kelly
    volatility_lookback: 20        # Lookback period for volatility calculation
    volatility_risk_factor: 10.0   # Risk factor for volatility-based sizing (increased for larger positions)

  # Position monitoring settings
  position_monitor_interval: 1.0  # Check positions every 1 second

# DATABASE SETTINGS
database:
  path: "data/smart_trader.db"  # Single database for all modes
  backup_interval: 3600  # Backup every hour
  cleanup_interval: 86400  # Clean up every day
  max_age_days: 7  # Keep data for 7 days

# MESSAGE BUS SETTINGS
message_bus:
  type: "sqlite"
  path: "data/bus.db"  # Single bus for all modes
  poll_interval: 0.1
  batch_size: 100
  batch_timeout: 1.0
  wal_mode: true
  sync_mode: "NORMAL"
  cache_size: 10000
  mmap_size: 268435456  # 256 MB

# ENHANCED AI/LLM SETTINGS
llm:
  # Model configuration
  model_path: "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
  prompt_path: "llm/prompts/trading_prompt_phi.yaml"

  # Model parameters
  n_ctx: 2048
  n_threads: 4
  n_gpu_layers: 0
  max_tokens: 128
  temperature: 0.0
  verbose: false

  # Operational settings
  call_interval_s: 30
  dummy_mode: false  # Set to true to disable LLM for testing

  # Enhanced features
  adaptive_throttle: true
  min_throttle_interval: 10
  max_throttle_interval: 120
  throttle_volatility_factor: 2.0
  max_memory_size: 10

  # Performance thresholds
  error_threshold: 0.1  # Alert if error rate exceeds 10%
  latency_threshold: 10.0  # Alert if average latency exceeds 10 seconds
  confidence_threshold: 0.6  # Minimum confidence for LLM signals

# Legacy settings for backward compatibility
llm_model_path: "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
llm_prompt_path: "llm/prompts/trading_prompt_phi.yaml"
llm_threads: 4
llm_gpu_layers: 0
llm_throttle_seconds: 5
dummy_llm: false

model_path: "C:\\Users\\<USER>\\.lmstudio\\models\\lmstudio-community\\Phi-3.1-mini-128k-instruct-GGUF\\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
prompt_path: "llm/prompts/trading_prompt_phi.yaml"
n_ctx: 2048
max_tokens: 128
temperature: 0.0
call_interval_s: 30
n_threads: 4
n_gpu_layers: 0
dummy_mode: false

# LLM Health Check settings
health_check_interval: 60  # Check health every 60 seconds
llm_error_threshold: 0.05  # Alert if error rate exceeds 5%
llm_latency_threshold: 5.0  # Alert if average latency exceeds 5 seconds
llm_confidence_threshold: 0.6  # Minimum confidence for LLM signals

# Adaptive throttling settings
adaptive_throttle: true  # Enable adaptive throttling
min_throttle_interval: 10  # Minimum throttle interval in seconds
max_throttle_interval: 120  # Maximum throttle interval in seconds
throttle_volatility_factor: 2.0  # Increase throttle in high volatility

# Account update settings
account_update_interval: 30  # Update account info every 30 seconds

# LOGGING SETTINGS
logging:
  level: "INFO"
  file: "logs/smart_trader.log"  # Single log file for all modes
  max_size_mb: 10
  backup_count: 5
  console: true

# Debug settings
debug:
  enabled: false  # Disable debug signals to use smart strategy
  generate_test_signals: false
  test_signal_interval: 60  # Generate a test signal every 60 seconds
  test_signal_symbols: ["BTC-USDT"]
  test_signal_sources: ["rsi", "orderflow", "volatility", "vwap", "funding"]

# Strategy settings
strategy:
  type: "smart_integrated"  # Use our best-performing strategy
  signal_generation_interval: 10  # Generate signals every 10 seconds
  use_smart_strategy: true  # Enable smart model-integrated strategy

# FEATURE STORE SETTINGS
feature_store:
  type: "sqlite"
  path: "data/smart_trader_features.db"  # Single feature store for all modes
  ttl: 86400  # 1 day TTL for features

# Model settings
models:
  vwap:
    enabled: true
    window: 24  # 24 hours
    update_interval: 5  # Update every 5 seconds

  volatility:
    enabled: true
    window: 24  # 24 hours
    update_interval: 30  # Update every 30 seconds
    garch_p: 1
    garch_q: 1

  funding:
    enabled: true
    window: 24  # 24 hours
    update_interval: 60  # Update every minute

  open_interest:
    enabled: true
    window: 24  # 24 hours
    update_interval: 60  # Update every minute

  sentiment:
    enabled: true
    update_interval: 120  # Update every 2 minutes
    api_key: ""  # Add your sentiment API key if available

# AI MODEL SETTINGS
# Enable/disable specific AI models
enable_rsi_model: true
enable_orderflow_model: true
enable_volatility_regime_model: true
enable_vwap_deviation_model: true
enable_liquidity_imbalance_model: true
enable_garch_volatility_model: true
enable_funding_momentum_model: true
enable_open_interest_momentum_model: true
enable_social_sentiment_model: true
enable_ensemble_model: true

# Model-specific configurations
vwap_deviation_config:
  window: 24
  threshold: 2.0
  update_interval: 60

liquidity_imbalance_config:
  window: 12
  threshold: 1.5
  update_interval: 60

garch_volatility_config:
  window: 48
  p: 1
  q: 1
  update_interval: 300

funding_momentum_config:
  window: 24
  threshold: 1.5
  update_interval: 300

open_interest_momentum_config:
  window: 24
  threshold: 2.0
  update_interval: 300

social_sentiment_config:
  delta_window: 5
  z_window: 60
  threshold: 1.2
  contrarian: true
  update_interval: 300

ensemble_model_config:
  performance_window: 24
  min_weight: 0.1
  max_weight: 3.0
  learning_rate: 0.05
  model_weights:
    rsi: 1.0
    orderflow: 1.5
    volatility_regime: 1.2
    vwap_deviation: 1.0
    liquidity_imbalance: 1.0
    garch_volatility: 1.3
    funding_momentum: 1.2
    open_interest_momentum: 1.1
    social_sentiment: 0.8

# Monitoring configuration
monitoring:
  enabled: true
  update_interval: 60  # seconds
  alert_thresholds:
    max_latency_ms: 1000
    min_accuracy: 0.6
    max_error_rate: 0.1

# DASHBOARD CONFIGURATION
dashboard:
  enabled: true
  host: "localhost"
  port: 8081  # Your unified dashboard port
  update_interval: 5  # seconds

# Live trading configuration
live_trading:
  enabled: true
  risk_management:
    max_daily_loss: 50.0  # USD
    max_position_size: 0.1  # BTC
    stop_loss_percent: 2.0
    take_profit_percent: 4.0

  alerts:
    email_enabled: false
    webhook_enabled: false
    log_level: "WARNING"
