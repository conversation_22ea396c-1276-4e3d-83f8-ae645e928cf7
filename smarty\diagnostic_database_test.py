#!/usr/bin/env python3
"""
Comprehensive database diagnostic test for Epinnox Investment Club dashboard.
Tests all data retrieval functions to identify loading issues.
"""

import sqlite3
import json
import time
from datetime import datetime
from live_dashboard import LiveDataReader

def test_database_connection():
    """Test basic database connectivity."""
    print("🔍 Testing Database Connection...")
    try:
        conn = sqlite3.connect('data/bus.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Test basic connectivity
        cursor.execute("SELECT COUNT(*) as count FROM messages")
        total_messages = cursor.fetchone()['count']
        print(f"✅ Database connected: {total_messages} total messages")

        # Test recent data
        cursor.execute("SELECT MAX(ts) as last_ts FROM messages")
        last_ts = cursor.fetchone()['last_ts']
        if last_ts:
            last_time = datetime.fromtimestamp(last_ts)
            age_seconds = (datetime.now() - last_time).total_seconds()
            print(f"✅ Latest data: {last_time.strftime('%H:%M:%S')} ({age_seconds:.1f}s ago)")

        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_stream_analysis():
    """Analyze available data streams."""
    print("\n🔍 Analyzing Data Streams...")
    try:
        conn = sqlite3.connect('data/bus.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get all unique streams
        cursor.execute("SELECT DISTINCT stream, COUNT(*) as count FROM messages GROUP BY stream ORDER BY count DESC")
        streams = cursor.fetchall()

        print("📊 Available streams:")
        for stream in streams[:15]:  # Top 15 streams
            print(f"   {stream['stream']}: {stream['count']} messages")

        # Test specific stream patterns
        patterns = [
            ("%depth%", "Order Book"),
            ("%trade%", "Trades"),
            ("%kline%", "Price Data"),
            ("signals.%", "Trading Signals")
        ]

        print("\n📋 Stream Pattern Analysis:")
        for pattern, description in patterns:
            cursor.execute("SELECT COUNT(*) as count FROM messages WHERE stream LIKE ?", (pattern,))
            count = cursor.fetchone()['count']
            print(f"   {description}: {count} messages")

        conn.close()
        return True
    except Exception as e:
        print(f"❌ Stream analysis failed: {e}")
        return False

def test_order_book_data():
    """Test order book data retrieval."""
    print("\n🔍 Testing Order Book Data...")
    try:
        reader = LiveDataReader()
        reader.connect()

        # Test order book for BTC-USDT
        orderbook = reader.get_order_book("BTC-USDT")
        print(f"📋 Order Book Result: {len(orderbook.get('bids', []))} bids, {len(orderbook.get('asks', []))} asks")

        if orderbook.get('bids'):
            print(f"   Top bid: {orderbook['bids'][0]}")
        if orderbook.get('asks'):
            print(f"   Top ask: {orderbook['asks'][0]}")

        # Test raw database query
        conn = sqlite3.connect('data/bus.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT stream, payload FROM messages
            WHERE (stream LIKE '%depth%' OR stream LIKE '%orderbook%')
            ORDER BY ts DESC LIMIT 1
        """)
        row = cursor.fetchone()
        if row:
            data = json.loads(row['payload'])
            print(f"   Raw data stream: {row['stream']}")
            print(f"   Raw data keys: {list(data.keys())}")
            if 'tick' in data:
                tick = data['tick']
                print(f"   Tick keys: {list(tick.keys())}")
                if 'bids' in tick and 'asks' in tick:
                    print(f"   Raw bids/asks: {len(tick['bids'])}/{len(tick['asks'])}")

        conn.close()
        return len(orderbook.get('bids', [])) > 0
    except Exception as e:
        print(f"❌ Order book test failed: {e}")
        return False

def test_recent_trades_data():
    """Test recent trades data retrieval."""
    print("\n🔍 Testing Recent Trades Data...")
    try:
        reader = LiveDataReader()
        reader.connect()

        # Test recent trades for BTC-USDT
        trades = reader.get_recent_trades("BTC-USDT")
        print(f"💹 Recent Trades Result: {len(trades)} trades")

        if trades:
            print(f"   Latest trade: {trades[0]}")

        # Test raw database query
        conn = sqlite3.connect('data/bus.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT stream, payload FROM messages
            WHERE stream LIKE '%trade%'
            ORDER BY ts DESC LIMIT 1
        """)
        row = cursor.fetchone()
        if row:
            data = json.loads(row['payload'])
            print(f"   Raw trade stream: {row['stream']}")
            print(f"   Raw trade keys: {list(data.keys())}")
            if 'tick' in data:
                tick = data['tick']
                print(f"   Tick keys: {list(tick.keys())}")
                if 'data' in tick:
                    trade_data = tick['data']
                    print(f"   Trade data count: {len(trade_data) if isinstance(trade_data, list) else 1}")

        conn.close()
        return len(trades) > 0
    except Exception as e:
        print(f"❌ Recent trades test failed: {e}")
        return False

def test_debug_info():
    """Test debug information retrieval."""
    print("\n🔍 Testing Debug Information...")
    try:
        reader = LiveDataReader()
        reader.connect()

        debug_info = reader.get_debug_info()
        print(f"🔧 Debug Info Keys: {list(debug_info.keys())}")

        for key, value in debug_info.items():
            print(f"   {key}: {value}")

        return 'system_status' in debug_info
    except Exception as e:
        print(f"❌ Debug info test failed: {e}")
        return False

def main():
    """Run comprehensive database diagnostic."""
    print("🎯 EPINNOX DASHBOARD DATABASE DIAGNOSTIC")
    print("=" * 50)

    results = {
        "Database Connection": test_database_connection(),
        "Stream Analysis": test_stream_analysis(),
        "Order Book Data": test_order_book_data(),
        "Recent Trades Data": test_recent_trades_data(),
        "Debug Information": test_debug_info()
    }

    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 30)
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")

    overall_status = "✅ HEALTHY" if all(results.values()) else "❌ ISSUES DETECTED"
    print(f"\nOverall Database Status: {overall_status}")

if __name__ == "__main__":
    main()
