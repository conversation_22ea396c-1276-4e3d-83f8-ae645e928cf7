#!/usr/bin/env python3
"""
Database Production Setup for Money Circle
Critical database optimization and preparation for production deployment
"""

import sqlite3
import os
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseProductionSetup:
    def __init__(self):
        self.main_db_path = 'data/money_circle.db'
        self.bus_db_path = 'data/bus.db'
        self.backup_dir = 'backups/pre_production'
        
    def run_production_setup(self):
        """Run complete database production setup."""
        logger.info("🗄️ Starting database production setup...")
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 1. Backup existing databases
        self.backup_existing_databases()
        
        # 2. Optimize database settings
        self.optimize_database_settings()
        
        # 3. Create production indexes
        self.create_production_indexes()
        
        # 4. Vacuum and analyze databases
        self.vacuum_and_analyze()
        
        # 5. Set production pragmas
        self.set_production_pragmas()
        
        # 6. Verify database integrity
        self.verify_database_integrity()
        
        # 7. Create production admin user
        self.create_production_admin()
        
        # 8. Seed production data
        self.seed_production_data()
        
        logger.info("✅ Database production setup complete!")
        
    def backup_existing_databases(self):
        """Backup existing databases before production setup."""
        logger.info("💾 Backing up existing databases...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        databases = [self.main_db_path, self.bus_db_path]
        
        for db_path in databases:
            if os.path.exists(db_path):
                db_name = os.path.basename(db_path)
                backup_path = os.path.join(self.backup_dir, f"{db_name}.backup_{timestamp}")
                shutil.copy2(db_path, backup_path)
                logger.info(f"   ✅ Backed up {db_path} to {backup_path}")
            else:
                logger.info(f"   ⚠️ Database {db_path} not found, will be created")
    
    def optimize_database_settings(self):
        """Optimize database settings for production."""
        logger.info("⚡ Optimizing database settings for production...")
        
        for db_path in [self.main_db_path, self.bus_db_path]:
            if not os.path.exists(db_path):
                # Create database if it doesn't exist
                conn = sqlite3.connect(db_path)
                conn.close()
                logger.info(f"   📝 Created new database: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # Enable WAL mode for better concurrency
                cursor.execute("PRAGMA journal_mode = WAL")
                
                # Set synchronous to NORMAL for better performance
                cursor.execute("PRAGMA synchronous = NORMAL")
                
                # Increase cache size (64MB)
                cursor.execute("PRAGMA cache_size = -64000")
                
                # Set temp store to memory
                cursor.execute("PRAGMA temp_store = MEMORY")
                
                # Enable foreign keys
                cursor.execute("PRAGMA foreign_keys = ON")
                
                # Set page size to 4096 for better performance
                cursor.execute("PRAGMA page_size = 4096")
                
                # Auto vacuum for maintenance
                cursor.execute("PRAGMA auto_vacuum = INCREMENTAL")
                
                conn.commit()
                logger.info(f"   ✅ Optimized settings for {db_path}")
                
            except Exception as e:
                logger.error(f"   ❌ Error optimizing {db_path}: {e}")
            finally:
                conn.close()
    
    def create_production_indexes(self):
        """Create indexes for production performance."""
        logger.info("📊 Creating production indexes...")
        
        # Main database indexes
        main_indexes = [
            # User-related indexes
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
            
            # Trading-related indexes
            "CREATE INDEX IF NOT EXISTS idx_user_trades_user_id ON user_trades(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_trades_timestamp ON user_trades(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_user_trades_symbol ON user_trades(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_user_trades_strategy ON user_trades(strategy_name)",
            
            # Position-related indexes
            "CREATE INDEX IF NOT EXISTS idx_user_positions_user_id ON user_positions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_positions_symbol ON user_positions(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_user_positions_status ON user_positions(status)",
            
            # Strategy-related indexes
            "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_creator_id ON strategy_proposals(creator_id)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_status ON strategy_proposals(status)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_proposals_created_at ON strategy_proposals(created_at)",
            
            # Following-related indexes
            "CREATE INDEX IF NOT EXISTS idx_strategy_following_follower_id ON strategy_following(follower_id)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_following_strategy_id ON strategy_following(strategy_id)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_following_active ON strategy_following(is_active)",
            
            # Notification indexes
            "CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)",
            
            # Session indexes
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at)"
        ]
        
        # Bus database indexes
        bus_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_market_data_source ON market_data(source)",
            
            "CREATE INDEX IF NOT EXISTS idx_trading_signals_timestamp ON trading_signals(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_trading_signals_strategy ON trading_signals(strategy_name)"
        ]
        
        # Create indexes for main database
        self._create_indexes(self.main_db_path, main_indexes, "main database")
        
        # Create indexes for bus database
        self._create_indexes(self.bus_db_path, bus_indexes, "bus database")
    
    def _create_indexes(self, db_path: str, indexes: List[str], db_name: str):
        """Create indexes for a specific database."""
        if not os.path.exists(db_path):
            logger.warning(f"   ⚠️ Database {db_path} not found, skipping indexes")
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        created_count = 0
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                created_count += 1
            except Exception as e:
                logger.warning(f"   ⚠️ Index creation warning: {e}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"   ✅ Created {created_count} indexes for {db_name}")
    
    def vacuum_and_analyze(self):
        """Vacuum and analyze databases for optimal performance."""
        logger.info("🧹 Vacuuming and analyzing databases...")
        
        for db_path in [self.main_db_path, self.bus_db_path]:
            if not os.path.exists(db_path):
                continue
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # Vacuum to reclaim space and defragment
                cursor.execute("VACUUM")
                
                # Analyze to update statistics
                cursor.execute("ANALYZE")
                
                logger.info(f"   ✅ Vacuumed and analyzed {db_path}")
                
            except Exception as e:
                logger.error(f"   ❌ Error vacuuming {db_path}: {e}")
            finally:
                conn.close()
    
    def set_production_pragmas(self):
        """Set production-specific pragma settings."""
        logger.info("⚙️ Setting production pragma settings...")
        
        production_pragmas = [
            "PRAGMA journal_mode = WAL",
            "PRAGMA synchronous = NORMAL", 
            "PRAGMA cache_size = -64000",
            "PRAGMA temp_store = MEMORY",
            "PRAGMA foreign_keys = ON",
            "PRAGMA auto_vacuum = INCREMENTAL",
            "PRAGMA wal_autocheckpoint = 1000",
            "PRAGMA busy_timeout = 30000"
        ]
        
        for db_path in [self.main_db_path, self.bus_db_path]:
            if not os.path.exists(db_path):
                continue
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                for pragma in production_pragmas:
                    cursor.execute(pragma)
                
                conn.commit()
                logger.info(f"   ✅ Set production pragmas for {db_path}")
                
            except Exception as e:
                logger.error(f"   ❌ Error setting pragmas for {db_path}: {e}")
            finally:
                conn.close()
    
    def verify_database_integrity(self):
        """Verify database integrity before production."""
        logger.info("🔍 Verifying database integrity...")
        
        for db_path in [self.main_db_path, self.bus_db_path]:
            if not os.path.exists(db_path):
                continue
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # Check integrity
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                if result[0] == 'ok':
                    logger.info(f"   ✅ Database integrity OK: {db_path}")
                else:
                    logger.error(f"   ❌ Database integrity issues: {db_path} - {result[0]}")
                
                # Check foreign key constraints
                cursor.execute("PRAGMA foreign_key_check")
                fk_violations = cursor.fetchall()
                
                if not fk_violations:
                    logger.info(f"   ✅ Foreign key constraints OK: {db_path}")
                else:
                    logger.warning(f"   ⚠️ Foreign key violations in {db_path}: {len(fk_violations)}")
                
            except Exception as e:
                logger.error(f"   ❌ Error checking integrity for {db_path}: {e}")
            finally:
                conn.close()
    
    def create_production_admin(self):
        """Create production admin user."""
        logger.info("👤 Setting up production admin user...")
        
        if not os.path.exists(self.main_db_path):
            logger.warning("   ⚠️ Main database not found, skipping admin user creation")
            return
        
        conn = sqlite3.connect(self.main_db_path)
        cursor = conn.cursor()
        
        try:
            # Check if admin user exists
            cursor.execute("SELECT id FROM users WHERE username = 'epinnox' AND role = 'admin'")
            admin_exists = cursor.fetchone()
            
            if admin_exists:
                logger.info("   ✅ Production admin user already exists")
            else:
                # Create admin user (password will be hashed by the application)
                cursor.execute("""
                    INSERT OR REPLACE INTO users 
                    (username, email, password_hash, role, created_at, is_active)
                    VALUES (?, ?, ?, ?, datetime('now'), 1)
                """, ('epinnox', '<EMAIL>', 'temp_hash', 'admin'))
                
                conn.commit()
                logger.info("   ✅ Production admin user created")
                logger.warning("   ⚠️ Remember to update admin password on first login")
            
        except Exception as e:
            logger.error(f"   ❌ Error creating admin user: {e}")
        finally:
            conn.close()
    
    def seed_production_data(self):
        """Seed essential production data."""
        logger.info("🌱 Seeding production data...")
        
        if not os.path.exists(self.main_db_path):
            logger.warning("   ⚠️ Main database not found, skipping data seeding")
            return
        
        conn = sqlite3.connect(self.main_db_path)
        cursor = conn.cursor()
        
        try:
            # Seed default system settings
            system_settings = [
                ('platform_name', 'Money Circle Investment Club'),
                ('platform_version', '1.0.0'),
                ('maintenance_mode', 'false'),
                ('registration_enabled', 'true'),
                ('max_users', '1000'),
                ('session_timeout', '7200'),
                ('backup_enabled', 'true'),
                ('monitoring_enabled', 'true')
            ]
            
            for setting_key, setting_value in system_settings:
                cursor.execute("""
                    INSERT OR IGNORE INTO system_settings (key, value, created_at)
                    VALUES (?, ?, datetime('now'))
                """, (setting_key, setting_value))
            
            # Seed default notification templates
            notification_templates = [
                ('welcome', 'Welcome to Money Circle', 'Welcome to the Money Circle investment club platform!'),
                ('trade_executed', 'Trade Executed', 'Your trade has been executed successfully.'),
                ('strategy_followed', 'Strategy Followed', 'You are now following a new trading strategy.'),
                ('backup_completed', 'Backup Completed', 'System backup has been completed successfully.')
            ]
            
            for template_type, title, content in notification_templates:
                cursor.execute("""
                    INSERT OR IGNORE INTO notification_templates (type, title, content, created_at)
                    VALUES (?, ?, ?, datetime('now'))
                """, (template_type, title, content))
            
            conn.commit()
            logger.info("   ✅ Production data seeded successfully")
            
        except Exception as e:
            logger.error(f"   ❌ Error seeding production data: {e}")
        finally:
            conn.close()
    
    def generate_database_report(self):
        """Generate database status report."""
        logger.info("📊 Generating database status report...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'databases': {}
        }
        
        for db_path in [self.main_db_path, self.bus_db_path]:
            if not os.path.exists(db_path):
                report['databases'][db_path] = {'status': 'not_found'}
                continue
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # Get database info
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA journal_mode")
                journal_mode = cursor.fetchone()[0]
                
                # Get table count
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                
                # Get index count
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
                index_count = cursor.fetchone()[0]
                
                report['databases'][db_path] = {
                    'status': 'ready',
                    'size_mb': round((page_count * page_size) / (1024 * 1024), 2),
                    'journal_mode': journal_mode,
                    'table_count': table_count,
                    'index_count': index_count
                }
                
            except Exception as e:
                report['databases'][db_path] = {'status': 'error', 'error': str(e)}
            finally:
                conn.close()
        
        # Save report
        report_path = os.path.join(self.backup_dir, 'database_production_report.json')
        with open(report_path, 'w') as f:
            import json
            json.dump(report, f, indent=2)
        
        logger.info(f"   ✅ Database report saved: {report_path}")
        return report

def main():
    """Main execution function."""
    print("🗄️ Money Circle Database Production Setup")
    print("=" * 50)
    
    setup = DatabaseProductionSetup()
    
    try:
        setup.run_production_setup()
        report = setup.generate_database_report()
        
        print("\n✅ DATABASE PRODUCTION SETUP COMPLETE!")
        print("\n📊 Database Status:")
        for db_path, info in report['databases'].items():
            if info['status'] == 'ready':
                print(f"   ✅ {db_path}: {info['size_mb']}MB, {info['table_count']} tables, {info['index_count']} indexes")
            else:
                print(f"   ❌ {db_path}: {info['status']}")
        
        print("\n🔧 NEXT STEPS:")
        print("   1. Test database connections")
        print("   2. Run application with production database")
        print("   3. Verify all features work correctly")
        print("   4. Set up automated database backups")
        
    except Exception as e:
        logger.error(f"❌ Database production setup failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
