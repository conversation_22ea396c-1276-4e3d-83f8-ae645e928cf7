# Smart-Trader System Dependencies

# Core Web Framework
aiohttp>=3.8.0
aiohttp-cors>=0.7.0

# Data Processing
numpy>=1.20.0
pandas>=1.5.0

# Configuration
pyyaml>=6.0

# Database
aiosqlite>=0.17.0

# HTTP Requests
aiohttp>=3.8.0
requests>=2.28.0
httpx>=0.24.0

# WebSocket Support
websockets>=10.0

# Machine Learning (Optional - for AI models)
torch>=2.0.0; platform_system != "Windows" or platform_machine != "ARM64"
scikit-learn>=1.1.0

# LLM Support (Optional)
# llama-cpp-python>=0.2.0; platform_system != "Windows" or platform_machine != "ARM64"

# Crypto/Trading
ccxt>=4.0.0

# Async Support
asyncio-mqtt>=0.11.0

# Logging and Monitoring
psutil>=5.9.0

# Date/Time
python-dateutil>=2.8.0

# JSON/Data
orjson>=3.8.0

# Development/Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
