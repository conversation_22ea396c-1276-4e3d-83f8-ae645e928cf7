#!/usr/bin/env python3
"""
Test script to verify template system is working
"""

import sys
from pathlib import Path
import jinja2

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_templates():
    """Test if templates can be loaded and rendered."""
    try:
        # Setup Jinja2 environment
        template_path = current_dir / 'templates'
        print(f"Template path: {template_path}")
        print(f"Template path exists: {template_path.exists()}")
        
        if template_path.exists():
            print("Templates found:")
            for template_file in template_path.glob('*.html'):
                print(f"  - {template_file.name}")
        
        env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(template_path)),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        
        # Test loading personal dashboard template
        try:
            template = env.get_template('personal_dashboard.html')
            print("✅ Personal dashboard template loaded successfully")
            
            # Test rendering with sample data
            context = {
                'user': {'username': 'test_user', 'role': 'member'},
                'portfolio': {
                    'total_value': 1000.0,
                    'available_balance': 500.0,
                    'open_positions': 2,
                    'daily_pnl': 25.50,
                    'daily_change': 2.55,
                    'win_rate': 65.0,
                    'avg_trade': 12.50,
                    'max_drawdown': 5.0,
                    'sharpe_ratio': 1.2
                },
                'exchanges': [
                    {
                        'exchange_name': 'HTX',
                        'connected': True,
                        'balance': {'USDT': 500.0},
                        'id': 1
                    }
                ]
            }
            
            rendered = template.render(context)
            print("✅ Personal dashboard template rendered successfully")
            print(f"Rendered HTML length: {len(rendered)} characters")
            
        except Exception as e:
            print(f"❌ Error loading personal dashboard template: {e}")
        
        # Test loading club dashboard template
        try:
            template = env.get_template('club_dashboard.html')
            print("✅ Club dashboard template loaded successfully")
            
            # Test rendering with sample data
            context = {
                'user': {'username': 'test_user', 'role': 'admin'},
                'club_stats': {
                    'total_members': 15,
                    'active_strategies': 5,
                    'performance': 8.5,
                    'total_volume': 50000.0
                },
                'pending_strategies': [],
                'active_votes': [],
                'recent_activities': [],
                'top_strategies': [],
                'top_members': [],
                'notifications': []
            }
            
            rendered = template.render(context)
            print("✅ Club dashboard template rendered successfully")
            print(f"Rendered HTML length: {len(rendered)} characters")
            
        except Exception as e:
            print(f"❌ Error loading club dashboard template: {e}")
        
        # Test base template
        try:
            template = env.get_template('base.html')
            print("✅ Base template loaded successfully")
        except Exception as e:
            print(f"❌ Error loading base template: {e}")
            
        print("\n🎉 Template system test completed!")
        
    except Exception as e:
        print(f"❌ Template system error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_templates()
