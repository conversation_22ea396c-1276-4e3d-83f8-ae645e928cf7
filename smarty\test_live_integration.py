"""
Test script for live model integration and monitoring system.

This script tests the enhanced live trading system with real-time
model execution, monitoring, and dashboard functionality.
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any

from live_trader import LiveTradingSystem
from monitoring.model_monitor import ModelPerformanceMonitor
from core.feature_store import feature_store
from core.utils import setup_logging

logger = logging.getLogger(__name__)


class LiveIntegrationTester:
    """
    Test harness for live trading system integration.

    Tests:
    - Model execution and monitoring
    - Signal generation and tracking
    - Dashboard functionality
    - Performance metrics
    - Alert system
    """

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the integration tester."""
        self.config_path = config_path
        self.test_results = {}
        self.start_time = datetime.now()

    async def run_integration_tests(self) -> Dict[str, Any]:
        """Run comprehensive integration tests."""
        logger.info("🧪 Starting Live Integration Tests")

        test_results = {
            "start_time": self.start_time.isoformat(),
            "tests": {},
            "overall_status": "PENDING"
        }

        try:
            # Test 1: Configuration Loading
            test_results["tests"]["config_loading"] = await self._test_config_loading()

            # Test 2: Model Monitor Initialization
            test_results["tests"]["monitor_init"] = await self._test_monitor_initialization()

            # Test 3: Feature Store Integration
            test_results["tests"]["feature_store"] = await self._test_feature_store_integration()

            # Test 4: Model Execution Simulation
            test_results["tests"]["model_execution"] = await self._test_model_execution()

            # Test 5: Signal Generation
            test_results["tests"]["signal_generation"] = await self._test_signal_generation()

            # Test 6: Performance Tracking
            test_results["tests"]["performance_tracking"] = await self._test_performance_tracking()

            # Test 7: Dashboard API
            test_results["tests"]["dashboard_api"] = await self._test_dashboard_api()

            # Test 8: Alert System
            test_results["tests"]["alert_system"] = await self._test_alert_system()

            # Calculate overall status
            passed_tests = sum(1 for result in test_results["tests"].values() if result["status"] == "PASS")
            total_tests = len(test_results["tests"])

            if passed_tests == total_tests:
                test_results["overall_status"] = "PASS"
            elif passed_tests > total_tests * 0.7:
                test_results["overall_status"] = "PARTIAL"
            else:
                test_results["overall_status"] = "FAIL"

            test_results["end_time"] = datetime.now().isoformat()
            test_results["duration_seconds"] = (datetime.now() - self.start_time).total_seconds()
            test_results["passed_tests"] = passed_tests
            test_results["total_tests"] = total_tests

            # Log summary
            self._log_test_summary(test_results)

            return test_results

        except Exception as e:
            logger.error(f"Integration test failed: {e}")
            test_results["overall_status"] = "ERROR"
            test_results["error"] = str(e)
            return test_results

    async def _test_config_loading(self) -> Dict[str, Any]:
        """Test configuration loading."""
        logger.info("Testing configuration loading...")

        try:
            live_trader = LiveTradingSystem(self.config_path)

            # Verify required configuration sections
            required_sections = ["trading", "monitoring", "dashboard"]
            missing_sections = [section for section in required_sections
                              if section not in live_trader.config]

            if missing_sections:
                return {
                    "status": "FAIL",
                    "message": f"Missing configuration sections: {missing_sections}",
                    "details": {}
                }

            return {
                "status": "PASS",
                "message": "Configuration loaded successfully",
                "details": {
                    "symbols": live_trader.config.get("symbols", []),
                    "trading_enabled": bool(live_trader.config.get("trading", {}).get("enabled", False)),
                    "monitoring_enabled": bool(live_trader.config.get("monitoring", {}).get("enabled", False))
                }
            }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Configuration loading failed: {e}",
                "details": {}
            }

    async def _test_monitor_initialization(self) -> Dict[str, Any]:
        """Test model monitor initialization."""
        logger.info("Testing model monitor initialization...")

        try:
            # Create a test configuration
            test_config = {
                "monitoring": {
                    "enabled": True,
                    "update_interval": 60,
                    "alert_thresholds": {
                        "max_latency_ms": 1000,
                        "min_accuracy": 0.6
                    }
                }
            }

            monitor = ModelPerformanceMonitor(test_config)
            await monitor.start()

            # Test basic functionality
            await monitor.record_prediction(
                "test_model",
                {"confidence": 0.8, "signal_strength": 0.5},
                100.0,
                {"test": "data"}
            )

            # Get performance metrics
            metrics = await monitor.get_model_performance("test_model")

            if metrics and metrics.total_predictions > 0:
                return {
                    "status": "PASS",
                    "message": "Model monitor initialized and functioning",
                    "details": {
                        "total_predictions": metrics.total_predictions,
                        "avg_latency_ms": metrics.avg_latency_ms,
                        "avg_confidence": metrics.avg_confidence
                    }
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "Model monitor not recording predictions",
                    "details": {}
                }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Monitor initialization failed: {e}",
                "details": {}
            }

    async def _test_feature_store_integration(self) -> Dict[str, Any]:
        """Test feature store integration."""
        logger.info("Testing feature store integration...")

        try:
            # Test basic feature store operations
            test_symbol = "BTC-USDT"
            test_data = {
                "price": 50000.0,
                "volume": 1000.0,
                "timestamp": datetime.now().isoformat()
            }

            # Store test data
            await feature_store.set(test_symbol, "test_data", test_data)

            # Retrieve test data
            retrieved_data = await feature_store.get(test_symbol, "test_data")

            if retrieved_data == test_data:
                return {
                    "status": "PASS",
                    "message": "Feature store integration working",
                    "details": {
                        "stored_data": test_data,
                        "retrieved_data": retrieved_data
                    }
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "Feature store data mismatch",
                    "details": {
                        "stored_data": test_data,
                        "retrieved_data": retrieved_data
                    }
                }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Feature store integration failed: {e}",
                "details": {}
            }

    async def _test_model_execution(self) -> Dict[str, Any]:
        """Test model execution simulation."""
        logger.info("Testing model execution...")

        try:
            # Import and test RSI model
            from models.rsi import RSIModel

            rsi_model = RSIModel(period=14)

            # Create test price data
            test_prices = [50000 + i * 100 for i in range(20)]
            test_features = {
                "symbol": "BTC-USDT",
                "close_prices": test_prices
            }

            # Execute model prediction
            start_time = time.time()
            prediction = await rsi_model.predict(test_features)
            execution_time = (time.time() - start_time) * 1000  # ms

            # Validate prediction
            required_keys = ["rsi", "signal_strength"]
            missing_keys = [key for key in required_keys if key not in prediction]

            if missing_keys:
                return {
                    "status": "FAIL",
                    "message": f"Missing prediction keys: {missing_keys}",
                    "details": {"prediction": prediction}
                }

            return {
                "status": "PASS",
                "message": "Model execution successful",
                "details": {
                    "execution_time_ms": execution_time,
                    "prediction": prediction,
                    "rsi_value": prediction.get("rsi", 0)
                }
            }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Model execution failed: {e}",
                "details": {}
            }

    async def _test_signal_generation(self) -> Dict[str, Any]:
        """Test signal generation."""
        logger.info("Testing signal generation...")

        try:
            # Import strategy function
            from backtester.strategies import smart_model_integrated_strategy

            # Generate test signals
            test_symbols = ["BTC-USDT"]
            test_timestamp = datetime.now()

            # Populate feature store with test data
            for symbol in test_symbols:
                await feature_store.set(symbol, "close", 50000.0)

                # Add price history
                price_history = [(test_timestamp - timedelta(minutes=i), 50000 + i * 10)
                               for i in range(50)]
                for timestamp, price in price_history:
                    await feature_store.add_time_series(symbol, "close_prices", price, timestamp)

            # Generate signals
            signals = await smart_model_integrated_strategy(test_timestamp, test_symbols)

            return {
                "status": "PASS",
                "message": f"Generated {len(signals)} signals",
                "details": {
                    "signal_count": len(signals),
                    "signals": [
                        {
                            "symbol": signal.symbol,
                            "action": signal.action.value,
                            "score": signal.score,
                            "source": signal.source
                        } for signal in signals
                    ]
                }
            }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Signal generation failed: {e}",
                "details": {}
            }

    async def _test_performance_tracking(self) -> Dict[str, Any]:
        """Test performance tracking."""
        logger.info("Testing performance tracking...")

        try:
            test_config = {"monitoring": {"enabled": True}}
            monitor = ModelPerformanceMonitor(test_config)

            # Record multiple predictions
            for i in range(5):
                await monitor.record_prediction(
                    "test_model",
                    {"confidence": 0.7 + i * 0.05, "signal_strength": 0.3 + i * 0.1},
                    50.0 + i * 10,
                    {"test": f"data_{i}"}
                )

            # Record signals
            for i in range(3):
                await monitor.record_signal(
                    "test_strategy",
                    {"action": "BUY" if i % 2 == 0 else "SELL", "score": 0.6 + i * 0.1}
                )

            # Get performance summary
            summary = await monitor.get_performance_summary()

            return {
                "status": "PASS",
                "message": "Performance tracking working",
                "details": {
                    "total_predictions": summary.get("models", {}).get("total_predictions", 0),
                    "total_signals": summary.get("signals", {}).get("total_signals", 0),
                    "active_models": summary.get("models", {}).get("active_models", 0)
                }
            }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Performance tracking failed: {e}",
                "details": {}
            }

    async def _test_dashboard_api(self) -> Dict[str, Any]:
        """Test dashboard API endpoints."""
        logger.info("Testing dashboard API...")

        try:
            # This is a simplified test - in a real scenario you'd make HTTP requests
            test_config = {
                "dashboard": {"enabled": True, "host": "localhost", "port": 8080},
                "monitoring": {"enabled": True}
            }

            monitor = ModelPerformanceMonitor(test_config["monitoring"])

            # Test API data generation
            summary = await monitor.get_performance_summary()
            health = await monitor.get_system_health()

            if summary and health:
                return {
                    "status": "PASS",
                    "message": "Dashboard API data generation working",
                    "details": {
                        "summary_keys": list(summary.keys()),
                        "health_uptime": health.uptime_seconds
                    }
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "Dashboard API data generation failed",
                    "details": {}
                }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Dashboard API test failed: {e}",
                "details": {}
            }

    async def _test_alert_system(self) -> Dict[str, Any]:
        """Test alert system."""
        logger.info("Testing alert system...")

        try:
            test_config = {
                "monitoring": {
                    "enabled": True,
                    "alert_thresholds": {
                        "max_latency_ms": 100,  # Low threshold to trigger alert
                        "min_accuracy": 0.9     # High threshold to trigger alert
                    }
                }
            }

            monitor = ModelPerformanceMonitor(test_config)

            # Record a prediction with high latency to trigger alert
            await monitor.record_prediction(
                "slow_model",
                {"confidence": 0.5, "signal_strength": 0.3},
                200.0,  # High latency
                {"test": "data"}
            )

            # Manually trigger alert check
            await monitor._check_alerts()

            # Check if alerts were generated
            if monitor.active_alerts:
                return {
                    "status": "PASS",
                    "message": "Alert system functioning",
                    "details": {
                        "active_alerts": monitor.active_alerts,
                        "alert_count": len(monitor.active_alerts)
                    }
                }
            else:
                return {
                    "status": "PARTIAL",
                    "message": "Alert system initialized but no alerts triggered",
                    "details": {}
                }

        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Alert system test failed: {e}",
                "details": {}
            }

    def _log_test_summary(self, results: Dict[str, Any]) -> None:
        """Log test summary."""
        logger.info("🧪 Integration Test Summary:")
        logger.info(f"   Overall Status: {results['overall_status']}")
        logger.info(f"   Passed Tests: {results['passed_tests']}/{results['total_tests']}")
        logger.info(f"   Duration: {results['duration_seconds']:.1f} seconds")

        for test_name, test_result in results["tests"].items():
            status_emoji = "✅" if test_result["status"] == "PASS" else "❌" if test_result["status"] == "FAIL" else "⚠️"
            logger.info(f"   {status_emoji} {test_name}: {test_result['message']}")


async def main():
    """Run integration tests."""
    setup_logging(level="INFO")

    tester = LiveIntegrationTester()
    results = await tester.run_integration_tests()

    # Save results
    results_file = f"test_results/integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    import os
    os.makedirs("test_results", exist_ok=True)

    # Convert any non-JSON serializable values
    def make_json_serializable(obj):
        if isinstance(obj, dict):
            return {k: make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [make_json_serializable(v) for v in obj]
        elif isinstance(obj, (bool, int, float, str, type(None))):
            return obj
        else:
            return str(obj)

    serializable_results = make_json_serializable(results)

    with open(results_file, 'w') as f:
        json.dump(serializable_results, f, indent=2)

    logger.info(f"📄 Test results saved to {results_file}")

    # Exit with appropriate code
    if results["overall_status"] == "PASS":
        exit(0)
    elif results["overall_status"] == "PARTIAL":
        exit(1)
    else:
        exit(2)


if __name__ == "__main__":
    asyncio.run(main())
