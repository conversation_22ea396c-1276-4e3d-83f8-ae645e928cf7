#!/usr/bin/env python3
"""
Money Circle Exchange Account Manager
Manages user exchange accounts and API credentials securely.
"""

import logging
import ccxt
from typing import Optional, Dict, List, Any
from database.models import DatabaseManager, UserExchange
from exchanges.encryption_utils import encrypt_api_credentials, decrypt_api_credentials

logger = logging.getLogger(__name__)

class ExchangeAccountManager:
    """Manages user exchange accounts and trading operations."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

        # Supported exchanges configuration
        self.supported_exchanges = {
            'HTX': {
                'class': ccxt.huobi,
                'name': 'HTX (Huobi)',
                'requires_passphrase': False,
                'testnet_available': False  # HTX/Huobi doesn't support sandbox
            },
            'huobi': {  # Support both HTX and huobi names
                'class': ccxt.huobi,
                'name': 'HTX (Huobi)',
                'requires_passphrase': False,
                'testnet_available': False
            },
            'Binance': {
                'class': ccxt.binance,
                'name': 'Binance',
                'requires_passphrase': False,
                'testnet_available': True
            },
            'Bybit': {
                'class': ccxt.bybit,
                'name': 'Bybit',
                'requires_passphrase': False,
                'testnet_available': True
            }
        }

    def add_exchange_account(self, user_id: int, exchange_name: str,
                           api_key: str, secret_key: str,
                           passphrase: Optional[str] = None) -> bool:
        """Add exchange account for user."""
        try:
            # Validate exchange
            if exchange_name not in self.supported_exchanges:
                logger.error(f"Unsupported exchange: {exchange_name}")
                return False

            # Test credentials before storing
            if not self._test_credentials(exchange_name, api_key, secret_key, passphrase):
                logger.error(f"Invalid credentials for {exchange_name}")
                return False

            # Encrypt credentials
            encrypted_creds = encrypt_api_credentials(api_key, secret_key, passphrase)

            # Store in database
            self.db.conn.execute("""
                INSERT INTO user_exchanges
                (user_id, exchange_name, api_key_encrypted, secret_key_encrypted, passphrase_encrypted)
                VALUES (?, ?, ?, ?, ?)
            """, (
                user_id,
                exchange_name,
                encrypted_creds['api_key'],
                encrypted_creds['secret_key'],
                encrypted_creds.get('passphrase')
            ))

            self.db.conn.commit()
            logger.info(f"✅ Exchange account added: {exchange_name} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding exchange account: {e}")
            return False

    def _test_credentials(self, exchange_name: str, api_key: str,
                         secret_key: str, passphrase: Optional[str] = None) -> bool:
        """Test exchange credentials."""
        try:
            exchange_config = self.supported_exchanges[exchange_name]
            exchange_class = exchange_config['class']

            # Create exchange instance
            config = {
                'apiKey': api_key,
                'secret': secret_key,
                'enableRateLimit': True
            }

            # Only use sandbox if the exchange supports it
            if exchange_config['testnet_available']:
                config['sandbox'] = True
                logger.info(f"Using sandbox mode for {exchange_name} validation")
            else:
                config['sandbox'] = False
                logger.info(f"Using production mode for {exchange_name} validation (sandbox not available)")

            if passphrase and exchange_config['requires_passphrase']:
                config['password'] = passphrase

            exchange = exchange_class(config)

            # Test with a simple API call
            try:
                balance = exchange.fetch_balance()
                logger.info(f"✅ Credentials validated for {exchange_name}")
                return True
            except Exception as api_error:
                # For production mode, some API errors might be expected (like insufficient permissions)
                # Check if it's a credential error vs other API error
                error_msg = str(api_error).lower()
                if any(keyword in error_msg for keyword in ['invalid', 'unauthorized', 'forbidden', 'signature']):
                    logger.error(f"❌ Invalid credentials for {exchange_name}: {api_error}")
                    return False
                else:
                    # Other API errors might be acceptable (e.g., rate limits, permissions)
                    logger.warning(f"⚠️ API call failed but credentials appear valid for {exchange_name}: {api_error}")
                    return True

        except Exception as e:
            logger.error(f"Credential validation failed for {exchange_name}: {e}")
            return False

    def get_user_exchanges(self, user_id: int) -> List[UserExchange]:
        """Get all exchange accounts for user."""
        try:
            cursor = self.db.conn.execute("""
                SELECT id, user_id, exchange_name, api_key_encrypted,
                       secret_key_encrypted, passphrase_encrypted, is_active, created_at
                FROM user_exchanges
                WHERE user_id = ? AND is_active = 1
                ORDER BY created_at DESC
            """, (user_id,))

            exchanges = []
            for row in cursor.fetchall():
                exchanges.append(UserExchange(
                    id=row[0],
                    user_id=row[1],
                    exchange_name=row[2],
                    api_key_encrypted=row[3],
                    secret_key_encrypted=row[4],
                    passphrase_encrypted=row[5],
                    is_active=bool(row[6]),
                    created_at=row[7]
                ))

            return exchanges

        except Exception as e:
            logger.error(f"Error getting user exchanges: {e}")
            return []

    def get_exchange_client(self, user_id: int, exchange_name: str,
                          testnet: bool = False) -> Optional[ccxt.Exchange]:
        """Get authenticated exchange client for user."""
        try:
            # Ensure database connection
            if not self.db.ensure_connection():
                logger.error(f"Database connection failed for user {user_id}")
                return None

            # Get user's exchange account
            cursor = self.db.conn.execute("""
                SELECT api_key_encrypted, secret_key_encrypted, passphrase_encrypted
                FROM user_exchanges
                WHERE user_id = ? AND exchange_name = ? AND is_active = 1
            """, (user_id, exchange_name))

            row = cursor.fetchone()
            if not row:
                logger.error(f"No {exchange_name} account found for user {user_id}")
                return None

            # Check if we have actual encrypted data
            if not row[0] or not row[1]:
                logger.error(f"Empty API credentials found for {exchange_name} user {user_id}")
                return None

            # Decrypt credentials
            encrypted_creds = {
                'api_key': row[0],
                'secret_key': row[1],
                'passphrase': row[2] if row[2] else None
            }

            creds = decrypt_api_credentials(encrypted_creds)

            # Validate decrypted credentials
            if not creds.get('api_key') or not creds.get('secret_key'):
                logger.error(f"Failed to decrypt valid credentials for {exchange_name} user {user_id}")
                return None

            # Create exchange client
            exchange_config = self.supported_exchanges[exchange_name]
            exchange_class = exchange_config['class']

            config = {
                'apiKey': creds['api_key'],
                'secret': creds['secret_key'],
                'enableRateLimit': True
            }

            # Only use sandbox if the exchange supports it and testnet is requested
            if testnet and exchange_config['testnet_available']:
                config['sandbox'] = True
                logger.debug(f"Using sandbox mode for {exchange_name}")
            else:
                config['sandbox'] = False
                if testnet and not exchange_config['testnet_available']:
                    logger.warning(f"Testnet requested but not available for {exchange_name}, using production")

            if creds.get('passphrase') and exchange_config['requires_passphrase']:
                config['password'] = creds['passphrase']

            exchange = exchange_class(config)
            logger.debug(f"✅ Exchange client created: {exchange_name} for user {user_id}")
            return exchange

        except Exception as e:
            logger.error(f"Error creating exchange client: {e}")
            return None

    def get_user_balance(self, user_id: int, exchange_name: str) -> Optional[Dict[str, Any]]:
        """Get user's balance from exchange."""
        try:
            exchange = self.get_exchange_client(user_id, exchange_name)
            if not exchange:
                return None

            balance = exchange.fetch_balance()

            # Format balance data
            formatted_balance = {
                'total': balance.get('total', {}),
                'free': balance.get('free', {}),
                'used': balance.get('used', {}),
                'timestamp': balance.get('timestamp'),
                'exchange': exchange_name
            }

            logger.debug(f"✅ Balance retrieved for user {user_id} on {exchange_name}")
            return formatted_balance

        except Exception as e:
            logger.error(f"Error getting user balance: {e}")
            return None

    def get_user_positions(self, user_id: int, exchange_name: str) -> List[Dict[str, Any]]:
        """Get user's open positions from exchange."""
        try:
            exchange = self.get_exchange_client(user_id, exchange_name)
            if not exchange:
                return []

            # Check if exchange supports positions
            if not hasattr(exchange, 'fetch_positions'):
                logger.warning(f"Exchange {exchange_name} doesn't support positions")
                return []

            positions = exchange.fetch_positions()

            # Filter only open positions
            open_positions = [pos for pos in positions if pos.get('size', 0) != 0]

            logger.debug(f"✅ Positions retrieved for user {user_id} on {exchange_name}: {len(open_positions)}")
            return open_positions

        except Exception as e:
            logger.error(f"Error getting user positions: {e}")
            return []

    def place_order(self, user_id: int, exchange_name: str, symbol: str,
                   order_type: str, side: str, amount: float,
                   price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """Place order for user."""
        try:
            exchange = self.get_exchange_client(user_id, exchange_name)
            if not exchange:
                return None

            # Place order
            if order_type == 'market':
                order = exchange.create_market_order(symbol, side, amount)
            elif order_type == 'limit':
                if price is None:
                    raise ValueError("Price required for limit orders")
                order = exchange.create_limit_order(symbol, side, amount, price)
            else:
                raise ValueError(f"Unsupported order type: {order_type}")

            logger.info(f"✅ Order placed for user {user_id}: {side} {amount} {symbol} on {exchange_name}")
            return order

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None

    def remove_exchange_account(self, user_id: int, exchange_id: int) -> bool:
        """Remove exchange account for user."""
        try:
            self.db.conn.execute("""
                UPDATE user_exchanges
                SET is_active = 0
                WHERE id = ? AND user_id = ?
            """, (exchange_id, user_id))

            self.db.conn.commit()
            logger.info(f"✅ Exchange account removed: ID {exchange_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error removing exchange account: {e}")
            return False

    def get_supported_exchanges(self) -> Dict[str, Dict[str, Any]]:
        """Get list of supported exchanges."""
        return {
            name: {
                'name': config['name'],
                'requires_passphrase': config['requires_passphrase'],
                'testnet_available': config['testnet_available']
            }
            for name, config in self.supported_exchanges.items()
        }
