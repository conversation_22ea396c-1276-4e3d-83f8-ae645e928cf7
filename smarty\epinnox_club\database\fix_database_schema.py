#!/usr/bin/env python3
"""
Database Schema Migration Script
Fixes missing columns and adds demo data for Money Circle platform.
"""

import sqlite3
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path

logger = logging.getLogger(__name__)

class DatabaseSchemaMigration:
    """Database schema migration and data seeding."""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None

    def connect(self):
        """Connect to database."""
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row
            return True
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False

    def run_migration(self):
        """Run complete database migration."""
        if not self.connect():
            raise Exception("Failed to connect to database")

        try:
            logger.info("Starting database schema migration...")

            # Add missing columns to existing tables
            self._add_missing_columns()

            # Create missing tables
            self._create_missing_tables()

            # Seed demo data
            self._seed_demo_data()

            self.conn.commit()
            logger.info("Database migration completed successfully")

        except Exception as e:
            logger.error(f"Migration error: {e}")
            self.conn.rollback()
            raise
        finally:
            if self.conn:
                self.conn.close()

    def _add_missing_columns(self):
        """Add missing columns to existing tables."""
        try:
            # Check strategy_proposals table
            cursor = self.conn.execute("PRAGMA table_info(strategy_proposals)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'title' not in columns:
                logger.info("Adding title column to strategy_proposals")
                self.conn.execute("ALTER TABLE strategy_proposals ADD COLUMN title TEXT")
                # Copy name to title for existing records
                self.conn.execute("UPDATE strategy_proposals SET title = name WHERE title IS NULL")

            # Check trading_performance table exists
            try:
                cursor = self.conn.execute("PRAGMA table_info(trading_performance)")
                tp_columns = [row[1] for row in cursor.fetchall()]
            except:
                # Table doesn't exist, will be created later
                tp_columns = []

            logger.info("Missing columns check completed")

        except Exception as e:
            logger.error(f"Error adding missing columns: {e}")

    def _create_missing_tables(self):
        """Create any missing tables."""
        try:
            # Check if trading_performance table exists and add missing columns
            try:
                cursor = self.conn.execute("PRAGMA table_info(trading_performance)")
                tp_columns = [row[1] for row in cursor.fetchall()]

                # Add missing columns to existing table
                missing_columns = [
                    ('trade_size', 'REAL DEFAULT 0.0'),
                    ('volatility', 'REAL DEFAULT 0.0')
                ]

                for col_name, col_def in missing_columns:
                    if col_name not in tp_columns:
                        logger.info(f"Adding {col_name} column to trading_performance")
                        self.conn.execute(f"ALTER TABLE trading_performance ADD COLUMN {col_name} {col_def}")

            except sqlite3.OperationalError:
                # Table doesn't exist, create it
                logger.info("Creating trading_performance table")
                self.conn.execute("""
                    CREATE TABLE trading_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        date DATE NOT NULL,
                        portfolio_value REAL DEFAULT 0.0,
                        total_return REAL DEFAULT 0.0,
                        daily_return REAL DEFAULT 0.0,
                        win_rate REAL DEFAULT 0.0,
                        trade_size REAL DEFAULT 0.0,
                        trades_count INTEGER DEFAULT 0,
                        max_drawdown REAL DEFAULT 0.0,
                        sharpe_ratio REAL DEFAULT 0.0,
                        volatility REAL DEFAULT 0.0,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                        UNIQUE(user_id, date)
                    )
                """)

            # Ensure strategy_performance table has all required columns
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    total_return REAL DEFAULT 0.0,
                    daily_return REAL DEFAULT 0.0,
                    trades_count INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    sharpe_ratio REAL DEFAULT 0.0,
                    followers_count INTEGER DEFAULT 0,
                    aum REAL DEFAULT 0.0,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    UNIQUE(strategy_id, date)
                )
            """)

            logger.info("Missing tables created successfully")

        except Exception as e:
            logger.error(f"Error creating missing tables: {e}")

    def _seed_demo_data(self):
        """Seed demo data for testing."""
        try:
            # Check if demo data already exists
            cursor = self.conn.execute("SELECT COUNT(*) FROM users WHERE username LIKE 'demo_%'")
            if cursor.fetchone()[0] > 0:
                logger.info("Demo data already exists, skipping seeding")
                return

            logger.info("Seeding demo data...")

            # Create demo users
            demo_users = [
                ('demo_trader1', '<EMAIL>', 'Demo Trader 1', 'aggressive'),
                ('demo_trader2', '<EMAIL>', 'Demo Trader 2', 'moderate'),
                ('demo_trader3', '<EMAIL>', 'Demo Trader 3', 'conservative'),
                ('demo_trader4', '<EMAIL>', 'Demo Trader 4', 'moderate'),
                ('demo_trader5', '<EMAIL>', 'Demo Trader 5', 'aggressive')
            ]

            for username, email, display_name, risk_tolerance in demo_users:
                # Insert user
                cursor = self.conn.execute("""
                    INSERT OR IGNORE INTO users (username, email, hashed_password, role, agreement_accepted)
                    VALUES (?, ?, 'demo_hash', 'member', 1)
                """, (username, email))

                user_id = cursor.lastrowid or self.conn.execute(
                    "SELECT id FROM users WHERE username = ?", (username,)
                ).fetchone()[0]

                # Create member profile
                self.conn.execute("""
                    INSERT OR IGNORE INTO member_profiles
                    (user_id, display_name, risk_tolerance, reputation_score, joined_strategies, total_votes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (user_id, display_name, risk_tolerance,
                     50 + (user_id * 10), 2 + user_id, 5 + user_id))

                # Add trading performance data
                for i in range(30):  # 30 days of data
                    date = (datetime.now() - timedelta(days=i)).date()
                    self.conn.execute("""
                        INSERT OR IGNORE INTO trading_performance
                        (user_id, date, portfolio_value, total_return, daily_return,
                         win_rate, trade_size, trades_count, max_drawdown, sharpe_ratio, volatility)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (user_id, date, 10000 + (user_id * 1000),
                         5.0 + (user_id * 2), 0.2, 65.0 + user_id,
                         500.0, 2 + (i % 3), 5.0, 1.2, 15.0))

            # Create demo strategies
            demo_strategies = [
                ('Momentum Trading Strategy', 'momentum', 'aggressive', 15.0, 8.0),
                ('Mean Reversion Strategy', 'mean_reversion', 'moderate', 12.0, 5.0),
                ('Arbitrage Strategy', 'arbitrage', 'conservative', 8.0, 3.0),
                ('Trend Following Strategy', 'trend_following', 'moderate', 18.0, 12.0)
            ]

            for title, strategy_type, risk_level, expected_return, max_drawdown in demo_strategies:
                cursor = self.conn.execute("""
                    INSERT OR IGNORE INTO strategy_proposals
                    (title, name, description, user_id, proposed_by, strategy_type,
                     risk_level, expected_return, max_drawdown, status, is_active)
                    VALUES (?, ?, ?, 1, 1, ?, ?, ?, ?, 'approved', 1)
                """, (title, title, f"Demo {title} for testing",
                     strategy_type, risk_level, expected_return, max_drawdown))

                strategy_id = cursor.lastrowid or self.conn.execute(
                    "SELECT id FROM strategy_proposals WHERE title = ?", (title,)
                ).fetchone()[0]

                # Add strategy performance data
                for i in range(30):
                    date = (datetime.now() - timedelta(days=i)).date()
                    self.conn.execute("""
                        INSERT OR IGNORE INTO strategy_performance
                        (strategy_id, date, total_return, daily_return, trades_count,
                         win_rate, max_drawdown, sharpe_ratio, followers_count, aum)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (strategy_id, date, expected_return + (i * 0.1),
                         0.5, 3 + (i % 2), 70.0, max_drawdown, 1.5,
                         10 + strategy_id, 50000.0))

            logger.info("Demo data seeded successfully")

        except Exception as e:
            logger.error(f"Error seeding demo data: {e}")

def main():
    """Run database migration."""
    db_path = "data/money_circle.db"
    migration = DatabaseSchemaMigration(db_path)
    migration.run_migration()
    print("Database migration completed successfully!")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
