#!/usr/bin/env python3
"""
Test Database and Template Fixes
Comprehensive test for database connectivity and social trading template issues
"""

import asyncio
import logging
import sys
import json
import aiohttp
import sqlite3
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8086"

class DatabaseAndTemplateTester:
    """Test database and template fixes."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = None
        self.session_cookie = None
        self.test_results = {}
        logger.info("Database and Template Tester initialized")
    
    async def run_all_tests(self):
        """Run all database and template tests."""
        try:
            logger.info("=" * 70)
            logger.info("DATABASE AND TEMPLATE FIXES TEST SUITE")
            logger.info("=" * 70)
            
            # Create session
            self.session = aiohttp.ClientSession()
            
            # Test 1: Database connection persistence
            await self._test_database_connection_persistence()
            
            # Test 2: Social trading tables
            await self._test_social_trading_tables()
            
            # Test 3: Login and get session
            await self._test_login_and_session()
            
            # Test 4: Social trading dashboard template
            await self._test_social_trading_dashboard()
            
            # Test 5: SQLite bus integration
            await self._test_sqlite_bus_integration()
            
            # Test 6: Concurrent database access
            await self._test_concurrent_database_access()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        finally:
            if self.session:
                await self.session.close()
        
        return True
    
    async def _test_database_connection_persistence(self):
        """Test database connection persistence."""
        logger.info("[TEST 1] Testing Database Connection Persistence...")
        
        try:
            from database.models import DatabaseManager
            
            # Test multiple database operations
            db_manager = DatabaseManager('data/money_circle.db')
            
            # Test 1: Initial connection
            if db_manager.ensure_connection():
                logger.info("✅ Initial database connection successful")
            else:
                logger.error("❌ Initial database connection failed")
                self.test_results['database_persistence'] = 'FAIL'
                return
            
            # Test 2: Multiple ensure_connection calls
            for i in range(10):
                if not db_manager.ensure_connection():
                    logger.error(f"❌ Database connection ensure failed on attempt {i+1}")
                    self.test_results['database_persistence'] = 'FAIL'
                    return
            
            # Test 3: Connection after simulated close
            if db_manager.conn:
                try:
                    db_manager.conn.close()
                    db_manager.conn = None
                except:
                    pass
            
            if db_manager.ensure_connection():
                logger.info("✅ Database reconnection after close successful")
                self.test_results['database_persistence'] = 'PASS'
            else:
                logger.error("❌ Database reconnection failed")
                self.test_results['database_persistence'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Database persistence test failed: {e}")
            self.test_results['database_persistence'] = 'FAIL'
    
    async def _test_social_trading_tables(self):
        """Test social trading database tables."""
        logger.info("[TEST 2] Testing Social Trading Database Tables...")
        
        try:
            conn = sqlite3.connect('data/money_circle.db')
            conn.row_factory = sqlite3.Row
            
            # Expected social trading tables
            expected_tables = [
                'strategy_proposals',
                'strategy_following', 
                'strategy_discussions',
                'member_activities',
                'strategy_performance',
                'social_metrics',
                'strategy_votes'
            ]
            
            # Check if tables exist
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ({})
            """.format(','.join('?' * len(expected_tables))), expected_tables)
            
            existing_tables = [row['name'] for row in cursor.fetchall()]
            
            if len(existing_tables) == len(expected_tables):
                logger.info(f"✅ All {len(expected_tables)} social trading tables exist")
                
                # Test data insertion/retrieval
                cursor = conn.execute("SELECT COUNT(*) as count FROM strategy_proposals")
                proposals_count = cursor.fetchone()['count']
                
                cursor = conn.execute("SELECT COUNT(*) as count FROM social_metrics")
                metrics_count = cursor.fetchone()['count']
                
                if proposals_count > 0 and metrics_count > 0:
                    logger.info(f"✅ Demo data exists: {proposals_count} proposals, {metrics_count} metrics")
                    self.test_results['social_tables'] = 'PASS'
                else:
                    logger.warning("⚠️ Tables exist but no demo data found")
                    self.test_results['social_tables'] = 'PARTIAL'
            else:
                logger.error(f"❌ Missing tables: {set(expected_tables) - set(existing_tables)}")
                self.test_results['social_tables'] = 'FAIL'
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Social trading tables test failed: {e}")
            self.test_results['social_tables'] = 'FAIL'
    
    async def _test_login_and_session(self):
        """Test login and session management."""
        logger.info("[TEST 3] Testing Login and Session Management...")
        
        try:
            # Login to get session
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            async with self.session.post(f"{BASE_URL}/login", data=login_data) as response:
                if response.status == 302:  # Redirect after successful login
                    # Get session cookie
                    cookies = response.cookies
                    if 'session_id' in cookies:
                        self.session_cookie = cookies['session_id'].value
                        logger.info(f"✅ Login successful, session: {self.session_cookie[:16]}...")
                        self.test_results['login_session'] = 'PASS'
                    else:
                        logger.error("❌ No session cookie received")
                        self.test_results['login_session'] = 'FAIL'
                else:
                    logger.error(f"❌ Login failed: {response.status}")
                    self.test_results['login_session'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Login test failed: {e}")
            self.test_results['login_session'] = 'FAIL'
    
    async def _test_social_trading_dashboard(self):
        """Test social trading dashboard template."""
        logger.info("[TEST 4] Testing Social Trading Dashboard Template...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping dashboard test")
                self.test_results['social_dashboard'] = 'SKIP'
                return
            
            # Set session cookie for authenticated requests
            self.session.cookie_jar.update_cookies({'session_id': self.session_cookie})
            
            # Test social trading dashboard
            async with self.session.get(f"{BASE_URL}/social-trading") as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Check if template rendered properly
                    if 'Social Trading Hub' in content and 'social-trading-container' in content:
                        logger.info("✅ Social trading dashboard template rendered successfully")
                        
                        # Check for JavaScript initialization
                        if 'initializeSocialDashboard' in content:
                            logger.info("✅ Social trading dashboard JavaScript included")
                            self.test_results['social_dashboard'] = 'PASS'
                        else:
                            logger.warning("⚠️ Social trading dashboard missing JavaScript")
                            self.test_results['social_dashboard'] = 'PARTIAL'
                    else:
                        logger.error("❌ Social trading dashboard template content missing")
                        self.test_results['social_dashboard'] = 'FAIL'
                elif response.status == 500:
                    logger.error("❌ Social trading dashboard returned 500 error")
                    self.test_results['social_dashboard'] = 'FAIL'
                else:
                    logger.error(f"❌ Social trading dashboard unexpected status: {response.status}")
                    self.test_results['social_dashboard'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"Social trading dashboard test failed: {e}")
            self.test_results['social_dashboard'] = 'FAIL'
    
    async def _test_sqlite_bus_integration(self):
        """Test SQLite bus integration."""
        logger.info("[TEST 5] Testing SQLite Bus Integration...")
        
        try:
            # Check if bus database exists
            bus_db_path = Path('data/bus.db')
            
            if bus_db_path.exists():
                conn = sqlite3.connect('data/bus.db')
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                if len(tables) > 0:
                    logger.info(f"✅ SQLite bus has {len(tables)} tables")
                    self.test_results['sqlite_bus'] = 'PASS'
                else:
                    logger.warning("⚠️ SQLite bus database exists but has no tables")
                    self.test_results['sqlite_bus'] = 'PARTIAL'
                
                conn.close()
            else:
                logger.warning("⚠️ SQLite bus database not found")
                self.test_results['sqlite_bus'] = 'PARTIAL'
                
        except Exception as e:
            logger.error(f"SQLite bus integration test failed: {e}")
            self.test_results['sqlite_bus'] = 'FAIL'
    
    async def _test_concurrent_database_access(self):
        """Test concurrent database access."""
        logger.info("[TEST 6] Testing Concurrent Database Access...")
        
        try:
            if not self.session_cookie:
                logger.warning("⚠️ No session cookie, skipping concurrent access test")
                self.test_results['concurrent_access'] = 'SKIP'
                return
            
            # Create multiple concurrent requests
            tasks = []
            for i in range(5):
                tasks.append(self.session.get(f"{BASE_URL}/api/exchanges/list"))
            
            # Execute all requests concurrently
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = 0
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    logger.warning(f"⚠️ Concurrent request {i+1} failed: {response}")
                else:
                    if response.status == 200:
                        success_count += 1
                        logger.info(f"✅ Concurrent request {i+1} successful")
                    else:
                        logger.warning(f"⚠️ Concurrent request {i+1} status: {response.status}")
                    await response.release()
            
            if success_count >= 4:  # Allow 1 failure
                logger.info("✅ Concurrent database access tests passed")
                self.test_results['concurrent_access'] = 'PASS'
            elif success_count >= 2:
                logger.info("⚠️ Concurrent database access partially working")
                self.test_results['concurrent_access'] = 'PARTIAL'
            else:
                logger.error("❌ Concurrent database access tests failed")
                self.test_results['concurrent_access'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"Concurrent database access test failed: {e}")
            self.test_results['concurrent_access'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("DATABASE AND TEMPLATE FIXES TEST RESULTS")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] Database and template fixes working!")
            logger.info("\nThe Money Circle platform should now have:")
            logger.info("- Stable database connectivity without 'closed database' errors")
            logger.info("- Working social trading dashboard template")
            logger.info("- Complete social trading database schema")
            logger.info("- Robust concurrent database access handling")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical issues still need attention")
        
        logger.info("=" * 70)

async def main():
    """Main test function."""
    tester = DatabaseAndTemplateTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 Database and template fixes test suite completed!")
    else:
        print("\n❌ Database and template fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
