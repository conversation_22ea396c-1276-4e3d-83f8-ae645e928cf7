#!/usr/bin/env python3
"""
Money Circle Enhanced Strategy Marketplace
Professional strategy discovery interface with advanced filtering, performance analytics, and social features.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
import aiohttp_jinja2
from auth.decorators import get_current_user
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from club.strategy_governance import StrategyGovernance
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

logger = logging.getLogger(__name__)

class EnhancedStrategyMarketplace:
    """Professional strategy marketplace with advanced features."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
        self.strategy_governance = StrategyGovernance(db_manager)
        self.social_trading = SocialTrading(db_manager)
        self.analytics = ClubAnalytics(db_manager)

    async def serve_strategy_marketplace(self, request: web.Request) -> web.Response:
        """Serve the enhanced strategy marketplace."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get marketplace data
        strategies = self._get_marketplace_strategies()
        categories = self._get_strategy_categories()
        featured_strategies = self._get_featured_strategies()
        user_followed = self._get_user_followed_strategies(user['user_id'])
        marketplace_stats = self._get_marketplace_stats()

        # Prepare template context
        context = {
            'user': user,
            'marketplace_stats': marketplace_stats,
            'featured_strategies': featured_strategies,
            'all_strategies': strategies,
            'following_strategies': [s for s in strategies if s['id'] in user_followed],
            'user_following': user_followed,
            'request': request  # Add request object for template access
        }

        # Render using template
        return aiohttp_jinja2.render_template('strategy_marketplace.html', request, context)

    def _get_marketplace_strategies(self) -> List[Dict[str, Any]]:
        """Get all marketplace strategies with performance data."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type, sp.risk_level,
                    sp.expected_return, sp.max_drawdown, sp.time_horizon, sp.created_at,
                    sp.user_id, u.username, mp.display_name,
                    COALESCE(AVG(spr.total_return), 0) as avg_return,
                    COALESCE(AVG(spr.win_rate), 0) as avg_win_rate,
                    COALESCE(AVG(spr.sharpe_ratio), 0) as avg_sharpe,
                    COALESCE(MAX(spr.followers_count), 0) as followers_count,
                    COUNT(DISTINCT spr.date) as performance_days
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved' AND sp.is_active = 1
                GROUP BY sp.id, sp.title, sp.description, sp.strategy_type, sp.risk_level,
                         sp.expected_return, sp.max_drawdown, sp.time_horizon, sp.created_at,
                         sp.user_id, u.username, mp.display_name
                ORDER BY avg_return DESC, followers_count DESC
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'strategy_type': row[3],
                    'risk_level': row[4],
                    'expected_return': row[5],
                    'max_drawdown': row[6],
                    'time_horizon': row[7],
                    'created_at': row[8],
                    'creator_id': row[9],
                    'creator_username': row[10],
                    'creator_display_name': row[11] or row[10],
                    'avg_return': row[12],
                    'avg_win_rate': row[13],
                    'avg_sharpe': row[14],
                    'followers_count': row[15],
                    'performance_days': row[16],
                    'is_featured': row[12] > 10.0 or row[15] > 50  # Featured criteria
                })

            return strategies

        except Exception as e:
            logger.error(f"Error getting marketplace strategies: {e}")
            return []

    def _get_strategy_categories(self) -> List[Dict[str, Any]]:
        """Get strategy categories with counts."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    strategy_type,
                    COUNT(*) as count,
                    AVG(COALESCE((SELECT AVG(total_return) FROM strategy_performance WHERE strategy_id = sp.id), 0)) as avg_performance
                FROM strategy_proposals sp
                WHERE status = 'approved' AND is_active = 1
                GROUP BY strategy_type
                ORDER BY count DESC
            """)

            categories = []
            for row in cursor.fetchall():
                categories.append({
                    'type': row[0],
                    'count': row[1],
                    'avg_performance': row[2] or 0.0
                })

            return categories

        except Exception as e:
            logger.error(f"Error getting strategy categories: {e}")
            return []

    def _get_featured_strategies(self) -> List[Dict[str, Any]]:
        """Get featured strategies."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.strategy_type, sp.risk_level,
                    sp.expected_return, sp.user_id, u.username, mp.display_name,
                    COALESCE(AVG(spr.total_return), 0) as avg_return,
                    COALESCE(MAX(spr.followers_count), 0) as followers_count
                FROM strategy_proposals sp
                JOIN users u ON sp.user_id = u.id
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved' AND sp.is_active = 1
                GROUP BY sp.id, sp.title, sp.description, sp.strategy_type, sp.risk_level,
                         sp.expected_return, sp.user_id, u.username, mp.display_name
                HAVING avg_return > 5.0 OR followers_count > 20
                ORDER BY avg_return DESC, followers_count DESC
                LIMIT 6
            """)

            featured = []
            for row in cursor.fetchall():
                featured.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'strategy_type': row[3],
                    'risk_level': row[4],
                    'expected_return': row[5],
                    'creator_id': row[6],
                    'creator_username': row[7],
                    'creator_display_name': row[8] or row[7],
                    'avg_return': row[9],
                    'followers_count': row[10]
                })

            return featured

        except Exception as e:
            logger.error(f"Error getting featured strategies: {e}")
            return []

    def _get_user_followed_strategies(self, user_id: int) -> List[int]:
        """Get strategies followed by user."""
        try:
            cursor = self.db.conn.execute("""
                SELECT strategy_id FROM strategy_following
                WHERE user_id = ? AND is_active = 1
            """, (user_id,))

            return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Error getting user followed strategies: {e}")
            return []

    def _get_marketplace_stats(self) -> Dict[str, Any]:
        """Get marketplace statistics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    COUNT(*) as total_strategies,
                    COUNT(CASE WHEN sp.is_active = 1 THEN 1 END) as active_strategies,
                    COALESCE(AVG(spr.total_return), 0) as avg_performance,
                    COALESCE(SUM(spr.followers_count), 0) as total_followers
                FROM strategy_proposals sp
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved'
            """)

            row = cursor.fetchone()
            return {
                'total_strategies': row[0] or 0,
                'active_strategies': row[1] or 0,
                'avg_performance': row[2] or 0.0,
                'total_followers': row[3] or 0
            }

        except Exception as e:
            logger.error(f"Error getting marketplace stats: {e}")
            return {
                'total_strategies': 0,
                'active_strategies': 0,
                'avg_performance': 0.0,
                'total_followers': 0
            }

    def _render_category_options(self, categories: List[Dict[str, Any]]) -> str:
        """Render category filter options."""
        options_html = ""
        for category in categories:
            options_html += f"""
            <option value="{category['type']}">{category['type'].replace('_', ' ').title()} ({category['count']})</option>
            """
        return options_html

    def _render_featured_strategies(self, featured_strategies: List[Dict[str, Any]]) -> str:
        """Render featured strategies grid."""
        if not featured_strategies:
            return '<div class="empty-state">No featured strategies available</div>'

        featured_html = ""
        for strategy in featured_strategies:
            risk_class = f"risk-{strategy['risk_level']}"
            performance_class = "positive" if strategy['avg_return'] > 0 else "negative"

            featured_html += f"""
            <div class="featured-strategy-card" data-strategy-id="{strategy['id']}">
                <div class="strategy-header">
                    <div class="strategy-title">
                        <h3>{strategy['title']}</h3>
                        <span class="featured-badge">⭐ Featured</span>
                    </div>
                    <div class="strategy-meta">
                        <span class="strategy-type">{strategy['strategy_type'].replace('_', ' ').title()}</span>
                        <span class="risk-badge {risk_class}">{strategy['risk_level'].title()}</span>
                    </div>
                </div>

                <div class="strategy-performance">
                    <div class="performance-metric">
                        <span class="metric-label">Performance</span>
                        <span class="metric-value {performance_class}">{strategy['avg_return']:+.2f}%</span>
                    </div>
                    <div class="performance-metric">
                        <span class="metric-label">Followers</span>
                        <span class="metric-value">{strategy['followers_count']}</span>
                    </div>
                </div>

                <div class="strategy-description">
                    {strategy['description'][:120]}{'...' if len(strategy['description']) > 120 else ''}
                </div>

                <div class="strategy-creator">
                    <span class="creator-label">Created by:</span>
                    <span class="creator-name">{strategy['creator_display_name']}</span>
                </div>

                <div class="strategy-actions">
                    <button class="btn-primary" onclick="viewStrategyDetails({strategy['id']})">View Details</button>
                    <button class="btn-secondary" onclick="followStrategy({strategy['id']})">Follow</button>
                    <button class="btn-tertiary" onclick="addToComparison({strategy['id']})">Compare</button>
                </div>
            </div>
            """

        return featured_html

    def _render_strategy_grid(self, strategies: List[Dict[str, Any]], user_followed: List[int]) -> str:
        """Render main strategy grid."""
        if not strategies:
            return '<div class="empty-state">No strategies found</div>'

        grid_html = ""
        for strategy in strategies:
            is_followed = strategy['id'] in user_followed
            risk_class = f"risk-{strategy['risk_level']}"
            performance_class = "positive" if strategy['avg_return'] > 0 else "negative"
            follow_class = "followed" if is_followed else ""

            grid_html += f"""
            <div class="strategy-card {follow_class}" data-strategy-id="{strategy['id']}"
                 data-category="{strategy['strategy_type']}" data-risk="{strategy['risk_level']}"
                 data-performance="{strategy['avg_return']}" data-followers="{strategy['followers_count']}">

                <div class="strategy-card-header">
                    <div class="strategy-title-section">
                        <h4 class="strategy-title">{strategy['title']}</h4>
                        <div class="strategy-badges">
                            <span class="type-badge">{strategy['strategy_type'].replace('_', ' ').title()}</span>
                            <span class="risk-badge {risk_class}">{strategy['risk_level'].title()}</span>
                            {('<span class="featured-badge">⭐</span>' if strategy.get('is_featured') else '')}
                        </div>
                    </div>
                    <div class="strategy-actions-mini">
                        <button class="action-btn" onclick="addToComparison({strategy['id']})" title="Add to comparison">
                            📊
                        </button>
                        <button class="action-btn {'followed' if is_followed else ''}"
                                onclick="toggleFollow({strategy['id']})"
                                title="{'Unfollow' if is_followed else 'Follow'} strategy">
                            {'❤️' if is_followed else '🤍'}
                        </button>
                    </div>
                </div>

                <div class="strategy-metrics">
                    <div class="metric-row">
                        <div class="metric">
                            <span class="metric-label">Performance</span>
                            <span class="metric-value {performance_class}">{strategy['avg_return']:+.2f}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Win Rate</span>
                            <span class="metric-value">{strategy['avg_win_rate']:.1f}%</span>
                        </div>
                    </div>
                    <div class="metric-row">
                        <div class="metric">
                            <span class="metric-label">Sharpe Ratio</span>
                            <span class="metric-value">{strategy['avg_sharpe']:.2f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Followers</span>
                            <span class="metric-value">{strategy['followers_count']}</span>
                        </div>
                    </div>
                </div>

                <div class="strategy-description">
                    {strategy['description'][:100]}{'...' if len(strategy['description']) > 100 else ''}
                </div>

                <div class="strategy-footer">
                    <div class="creator-info">
                        <span class="creator-avatar">{strategy['creator_display_name'][0].upper()}</span>
                        <span class="creator-name">{strategy['creator_display_name']}</span>
                    </div>
                    <div class="strategy-age">
                        {self._format_time_ago(strategy['created_at'])}
                    </div>
                </div>

                <div class="strategy-card-actions">
                    <button class="btn-view" onclick="viewStrategyDetails({strategy['id']})">View Details</button>
                    <button class="btn-follow {'active' if is_followed else ''}"
                            onclick="toggleFollow({strategy['id']})">
                        {'Unfollow' if is_followed else 'Follow'}
                    </button>
                </div>
            </div>
            """

        return grid_html

    def _format_time_ago(self, timestamp: str) -> str:
        """Format timestamp as time ago."""
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = timestamp

            now = datetime.now()
            diff = now - dt

            if diff.days > 30:
                months = diff.days // 30
                return f"{months} month{'s' if months != 1 else ''} ago"
            elif diff.days > 0:
                return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            else:
                return "Just now"
        except:
            return "Unknown"
