"""
Event dataclasses for the smart-trader system.
These classes represent the core data structures used throughout the system.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union


class Side(str, Enum):
    """Trading side enum."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class OrderType(str, Enum):
    """Order type enum."""
    LIMIT = "LIMIT"
    MARKET = "MARKET"
    STOP_MARKET = "STOP_MARKET"
    STOP_LIMIT = "STOP_LIMIT"
    TAKE_PROFIT = "TAKE_PROFIT"
    TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT"


@dataclass
class Kline:
    """Candlestick data."""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    interval: str  # e.g., "1m", "5m", "15m"
    trades: int = 0
    closed: bool = False  # Whether this candle is closed or still updating


@dataclass
class Trade:
    """Individual trade data."""
    symbol: str
    timestamp: datetime
    price: float
    quantity: float
    side: Side
    trade_id: str = ""
    is_buyer_maker: bool = False


@dataclass
class OrderbookLevel:
    """Single level in the orderbook."""
    price: float
    quantity: float


@dataclass
class OrderbookDelta:
    """Change in the orderbook."""
    symbol: str
    timestamp: datetime
    bids: List[OrderbookLevel] = field(default_factory=list)
    asks: List[OrderbookLevel] = field(default_factory=list)
    is_snapshot: bool = False  # True if this is a full snapshot, False if delta


@dataclass
class Position:
    """Current position information."""
    symbol: str
    side: Side
    size: float
    entry_price: float
    leverage: float
    liquidation_price: Optional[float] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    margin: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class Signal:
    """Trading signal generated by models or LLM."""
    symbol: str
    timestamp: datetime
    action: Side
    score: float  # Confidence score between -1 and 1
    source: str  # e.g., "llm", "rsi", "orderflow"
    rationale: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Order:
    """Order to be sent to the exchange."""
    symbol: str
    side: Side
    quantity: float
    order_type: OrderType
    price: Optional[float] = None  # Required for LIMIT orders
    reduce_only: bool = False
    post_only: bool = False
    client_order_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class OrderResponse:
    """Response from the exchange after placing an order."""
    symbol: str
    order_id: str
    client_order_id: str
    status: str
    timestamp: datetime
    error: Optional[str] = None


@dataclass
class Fill:
    """Order fill information."""
    symbol: str
    order_id: str
    trade_id: str
    side: Side
    price: float
    quantity: float
    commission: float
    commission_asset: str
    timestamp: datetime


@dataclass
class FeatureSnapshot:
    """Snapshot of features at a point in time."""
    symbol: str
    timestamp: datetime
    features: Dict[str, Union[float, str, bool]] = field(default_factory=dict)


@dataclass
class LLMRequest:
    """Request to the LLM."""
    prompt: str
    timestamp: datetime
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LLMResponse:
    """Response from the LLM."""
    action: Side
    score: float
    rationale: str
    timestamp: datetime
    raw_response: str = ""
    processing_time: float = 0.0  # in seconds
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FundingRateEvent:
    """Funding rate data from exchange."""
    symbol: str
    timestamp: datetime
    rate: float  # Current funding rate (in % per 8h)
    next_funding_time: str = ""  # Timestamp of next funding
    estimated_rate: float = 0.0  # Estimated next funding rate


@dataclass
class OpenInterestEvent:
    """Open interest data from exchange."""
    symbol: str
    timestamp: datetime
    open_interest: float  # Current open interest (in contracts)
    amount: float = 0.0  # Open interest in quote currency (e.g., USDT)
