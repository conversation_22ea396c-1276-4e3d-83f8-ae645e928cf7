"""
Meta-Ensemble model for the smart-trader system.

This model combines all model outputs using Bayesian averaging,
updates weights based on recent performance, and provides a unified
signal with confidence intervals.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Deque
from datetime import datetime, timedelta
from enum import Enum
from collections import deque

from core.utils import timer
from core.feature_store import feature_store
from core.events import Side

logger = logging.getLogger(__name__)


class MetaEnsembleModel:
    """
    Meta-Ensemble model.

    This model combines outputs from all registered models using Bayesian averaging,
    updates weights based on recent performance, and provides a unified signal with
    confidence intervals.

    Features:
    - Combines signals from all models
    - Weights models based on historical performance
    - Provides confidence intervals for predictions
    - Adapts to changing market conditions
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        performance_window: int = 24,  # 24 hours
        min_weight: float = 0.1,       # Minimum weight for any model
        max_weight: float = 3.0,       # Maximum weight for any model
        learning_rate: float = 0.05,   # Rate at which weights are updated
        base_weights: Optional[Dict[str, float]] = None  # Initial weights for models
    ):
        """
        Initialize the Meta-Ensemble model.

        Args:
            config: Configuration dictionary
            performance_window: Window for tracking model performance (hours)
            min_weight: Minimum weight for any model
            max_weight: Maximum weight for any model
            learning_rate: Rate at which weights are updated
            base_weights: Initial weights for models
        """
        self.config = config or {}
        self.performance_window = self.config.get("performance_window", performance_window)
        self.min_weight = self.config.get("min_weight", min_weight)
        self.max_weight = self.config.get("max_weight", max_weight)
        self.learning_rate = self.config.get("learning_rate", learning_rate)
        
        # Initialize model weights
        self.base_weights = base_weights or {
            "rsi": 1.0,
            "orderflow": 1.5,
            "volatility_regime": 1.2,
            "vwap_deviation": 1.0,
            "liquidity_imbalance": 1.0,
            "garch_volatility": 1.3,
            "funding_momentum": 1.2,
            "open_interest_momentum": 1.1,
            "social_sentiment": 0.8
        }
        
        # Override with config weights if provided
        config_weights = self.config.get("model_weights", {})
        for model, weight in config_weights.items():
            self.base_weights[model] = weight
        
        # Current weights (will be updated based on performance)
        self.current_weights = self.base_weights.copy()
        
        # Performance tracking
        self.model_performance: Dict[str, Deque[Tuple[datetime, float]]] = {}
        for model in self.base_weights:
            self.model_performance[model] = deque(maxlen=100)  # Track last 100 predictions
        
        # Last price for each symbol (for performance evaluation)
        self.last_prices: Dict[str, float] = {}
        
        # Last prediction for each model and symbol
        self.last_predictions: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"Meta-Ensemble model initialized with {len(self.base_weights)} base models")

    @timer("meta_ensemble_predict")
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'timestamp': Current timestamp

        Returns:
            Dictionary of prediction results including:
                - 'ensemble_action': Recommended trading action
                - 'ensemble_score': Confidence score (-1 to 1)
                - 'model_weights': Current weights for each model
                - 'confidence_interval': Confidence interval for the prediction
                - 'contributing_models': List of models contributing to the decision
        """
        symbol = features.get('symbol', '')
        timestamp = features.get('timestamp', datetime.now())
        
        # Get current price
        current_price = features.get('mid_price', 0.0)
        if current_price > 0:
            self.last_prices[symbol] = current_price
        
        # Update model performance based on previous predictions
        await self._update_model_performance(symbol, current_price)
        
        # Get predictions from all registered models
        model_predictions = {}
        for model_name in self.base_weights:
            prediction = await feature_store.get(symbol, f"{model_name}_prediction")
            if prediction:
                model_predictions[model_name] = prediction
                # Store for future performance evaluation
                self.last_predictions[model_name] = {
                    "symbol": symbol,
                    "timestamp": timestamp,
                    "prediction": prediction
                }
        
        if not model_predictions:
            logger.warning(f"No model predictions available for {symbol}")
            return self._default_prediction()
        
        # Combine model predictions
        ensemble_result = self._combine_predictions(model_predictions, symbol)
        
        # Store the ensemble prediction in the feature store
        await feature_store.set(symbol, "ensemble_prediction", ensemble_result)
        
        return ensemble_result

    def _combine_predictions(self, model_predictions: Dict[str, Dict[str, Any]], symbol: str) -> Dict[str, Any]:
        """
        Combine predictions from multiple models using weighted averaging.

        Args:
            model_predictions: Dictionary of model predictions
            symbol: Trading symbol

        Returns:
            Combined prediction
        """
        # Initialize scores for each action
        action_scores = {
            Side.BUY.value: 0.0,
            Side.SELL.value: 0.0,
            Side.HOLD.value: 0.0
        }
        
        total_weight = 0.0
        contributing_models = []
        
        # Calculate weighted scores
        for model_name, prediction in model_predictions.items():
            # Skip if no prediction or no action
            if not prediction or "action" not in prediction:
                continue
                
            # Get model weight
            weight = self.current_weights.get(model_name, 1.0)
            
            # Get action and confidence
            action = prediction.get("action", "HOLD")
            confidence = prediction.get("confidence", 0.5)
            
            # Normalize action to BUY, SELL, HOLD
            if action not in action_scores:
                if isinstance(action, str) and action.upper() in ["BUY", "SELL", "HOLD"]:
                    action = action.upper()
                else:
                    action = "HOLD"
            
            # Add weighted score
            action_scores[action] += confidence * weight
            total_weight += weight
            
            # Add to contributing models
            contributing_models.append({
                "model": model_name,
                "action": action,
                "confidence": confidence,
                "weight": weight
            })
        
        # Normalize scores
        if total_weight > 0:
            for action in action_scores:
                action_scores[action] /= total_weight
        
        # Determine ensemble action and score
        if action_scores[Side.BUY.value] > action_scores[Side.SELL.value] and action_scores[Side.BUY.value] > action_scores[Side.HOLD.value]:
            ensemble_action = Side.BUY.value
            ensemble_score = action_scores[Side.BUY.value]
        elif action_scores[Side.SELL.value] > action_scores[Side.BUY.value] and action_scores[Side.SELL.value] > action_scores[Side.HOLD.value]:
            ensemble_action = Side.SELL.value
            ensemble_score = -action_scores[Side.SELL.value]  # Negative for SELL
        else:
            ensemble_action = Side.HOLD.value
            ensemble_score = 0.0
        
        # Calculate confidence interval
        confidence_interval = self._calculate_confidence_interval(model_predictions, ensemble_action)
        
        return {
            'ensemble_action': ensemble_action,
            'ensemble_score': ensemble_score,
            'action_scores': action_scores,
            'model_weights': self.current_weights,
            'confidence_interval': confidence_interval,
            'contributing_models': contributing_models,
            'total_models': len(model_predictions)
        }

    def _calculate_confidence_interval(self, model_predictions: Dict[str, Dict[str, Any]], ensemble_action: str) -> Dict[str, float]:
        """
        Calculate confidence interval for the ensemble prediction.

        Args:
            model_predictions: Dictionary of model predictions
            ensemble_action: Ensemble action

        Returns:
            Dictionary with lower and upper bounds
        """
        # Extract confidence values for the ensemble action
        confidences = []
        for model_name, prediction in model_predictions.items():
            if prediction.get("action") == ensemble_action:
                confidences.append(prediction.get("confidence", 0.5))
        
        if not confidences:
            return {"lower": 0.0, "upper": 0.0}
        
        # Calculate mean and standard deviation
        mean_confidence = np.mean(confidences)
        std_confidence = np.std(confidences) if len(confidences) > 1 else 0.1
        
        # Calculate 95% confidence interval
        lower = max(0.0, mean_confidence - 1.96 * std_confidence / np.sqrt(len(confidences)))
        upper = min(1.0, mean_confidence + 1.96 * std_confidence / np.sqrt(len(confidences)))
        
        return {"lower": lower, "upper": upper}

    async def _update_model_performance(self, symbol: str, current_price: float) -> None:
        """
        Update model performance based on previous predictions.

        Args:
            symbol: Trading symbol
            current_price: Current price
        """
        # Skip if no current price
        if current_price <= 0:
            return
        
        # Get last price
        last_price = self.last_prices.get(symbol, 0.0)
        if last_price <= 0:
            return
        
        # Calculate price change
        price_change = (current_price - last_price) / last_price
        
        # Update performance for each model
        for model_name, last_pred_info in self.last_predictions.items():
            # Skip if not for this symbol or too old
            if last_pred_info.get("symbol") != symbol:
                continue
                
            last_timestamp = last_pred_info.get("timestamp")
            if not last_timestamp or (datetime.now() - last_timestamp).total_seconds() > 3600:
                continue
                
            prediction = last_pred_info.get("prediction", {})
            action = prediction.get("action", "HOLD")
            
            # Calculate performance score
            performance = 0.0
            if action == "BUY" and price_change > 0:
                performance = price_change  # Positive for correct BUY
            elif action == "SELL" and price_change < 0:
                performance = -price_change  # Positive for correct SELL
            elif action == "HOLD":
                performance = 0.1 if abs(price_change) < 0.001 else -0.1  # Reward HOLD in stable markets
            else:
                performance = -abs(price_change)  # Negative for incorrect predictions
            
            # Add to performance history
            self.model_performance[model_name].append((datetime.now(), performance))
            
            # Update model weight
            self._update_model_weight(model_name, performance)
            
            # Store performance in feature store
            await feature_store.set(symbol, f"metrics.{model_name}.performance", performance)
            
    def _update_model_weight(self, model_name: str, performance: float) -> None:
        """
        Update weight for a model based on its performance.

        Args:
            model_name: Model name
            performance: Performance score
        """
        if model_name not in self.current_weights:
            return
            
        # Get current weight
        current_weight = self.current_weights[model_name]
        
        # Update weight based on performance
        if performance > 0:
            # Increase weight for good performance
            new_weight = current_weight * (1.0 + self.learning_rate * performance)
        else:
            # Decrease weight for poor performance
            new_weight = current_weight * (1.0 + self.learning_rate * performance)
        
        # Ensure weight is within bounds
        new_weight = max(self.min_weight, min(self.max_weight, new_weight))
        
        # Update weight
        self.current_weights[model_name] = new_weight

    def _default_prediction(self) -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Returns:
            Default prediction dictionary
        """
        return {
            'ensemble_action': "HOLD",
            'ensemble_score': 0.0,
            'action_scores': {
                "BUY": 0.0,
                "SELL": 0.0,
                "HOLD": 1.0
            },
            'model_weights': self.current_weights,
            'confidence_interval': {"lower": 0.0, "upper": 0.0},
            'contributing_models': [],
            'total_models': 0
        }
