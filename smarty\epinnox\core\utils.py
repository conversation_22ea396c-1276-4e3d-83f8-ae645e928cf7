"""
Utility functions for the Epinnox trading system.

Essential utilities extracted from the smart-trader system,
enhanced for CCXT integration.
"""

import asyncio
import hashlib
import hmac
import logging
import time
import numpy as np
from datetime import datetime, timezone
from typing import Any, Callable, Dict, List, Optional, Union
from functools import wraps


def setup_logging(level: str = "INFO", format_string: Optional[str] = None) -> None:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Custom format string for log messages
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_string,
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def utc_timestamp() -> int:
    """Get current UTC timestamp in milliseconds."""
    return int(datetime.now(timezone.utc).timestamp() * 1000)


def iso_timestamp() -> str:
    """Get current UTC timestamp in ISO format."""
    return datetime.now(timezone.utc).isoformat()


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """
    Convert timestamp to datetime object.
    
    Args:
        timestamp: Unix timestamp (seconds or milliseconds)
        
    Returns:
        datetime object in UTC
    """
    # Handle both seconds and milliseconds
    if timestamp > 1e10:  # Likely milliseconds
        timestamp = timestamp / 1000
    
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    Convert datetime to timestamp in milliseconds.
    
    Args:
        dt: datetime object
        
    Returns:
        Unix timestamp in milliseconds
    """
    return int(dt.timestamp() * 1000)


def generate_signature(secret: str, message: str, algorithm: str = "sha256") -> str:
    """
    Generate HMAC signature for API authentication.
    
    Args:
        secret: Secret key
        message: Message to sign
        algorithm: Hash algorithm (sha256, sha512, etc.)
        
    Returns:
        Hex-encoded signature
    """
    hash_func = getattr(hashlib, algorithm)
    return hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hash_func
    ).hexdigest()


def calculate_rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
    """
    Calculate Relative Strength Index (RSI).
    
    Args:
        prices: Array of prices
        period: RSI period
        
    Returns:
        Array of RSI values
    """
    if len(prices) < period + 1:
        return np.array([50.0] * len(prices))
    
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # Calculate initial averages
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    rsi_values = []
    
    for i in range(period, len(prices)):
        if i == period:
            # First RSI calculation
            rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
        else:
            # Smoothed averages
            avg_gain = (avg_gain * (period - 1) + gains[i - 1]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i - 1]) / period
            rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
        
        rsi = 100 - (100 / (1 + rs))
        rsi_values.append(rsi)
    
    # Pad with initial values
    result = np.array([50.0] * period + rsi_values)
    return result


def calculate_vwap(prices: np.ndarray, volumes: np.ndarray) -> float:
    """
    Calculate Volume Weighted Average Price (VWAP).
    
    Args:
        prices: Array of prices
        volumes: Array of volumes
        
    Returns:
        VWAP value
    """
    if len(prices) != len(volumes) or len(prices) == 0:
        return 0.0
    
    total_volume = np.sum(volumes)
    if total_volume == 0:
        return np.mean(prices)
    
    return np.sum(prices * volumes) / total_volume


def calculate_bollinger_bands(prices: np.ndarray, period: int = 20, std_dev: float = 2.0) -> tuple:
    """
    Calculate Bollinger Bands.
    
    Args:
        prices: Array of prices
        period: Moving average period
        std_dev: Standard deviation multiplier
        
    Returns:
        Tuple of (upper_band, middle_band, lower_band)
    """
    if len(prices) < period:
        mean_price = np.mean(prices)
        return mean_price, mean_price, mean_price
    
    middle_band = np.mean(prices[-period:])
    std = np.std(prices[-period:])
    upper_band = middle_band + (std_dev * std)
    lower_band = middle_band - (std_dev * std)
    
    return upper_band, middle_band, lower_band


def timer(func: Callable) -> Callable:
    """
    Decorator to time function execution.
    
    Args:
        func: Function to time
        
    Returns:
        Wrapped function
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            logging.getLogger(func.__module__).debug(
                f"{func.__name__} executed in {execution_time:.4f} seconds"
            )
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            logging.getLogger(func.__module__).debug(
                f"{func.__name__} executed in {execution_time:.4f} seconds"
            )
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for async function retry logic.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff: Backoff multiplier for delay
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logging.getLogger(func.__module__).warning(
                            f"{func.__name__} failed (attempt {attempt + 1}/{max_retries + 1}): {e}. "
                            f"Retrying in {current_delay:.2f} seconds..."
                        )
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logging.getLogger(func.__module__).error(
                            f"{func.__name__} failed after {max_retries + 1} attempts: {e}"
                        )
            
            raise last_exception
        
        return wrapper
    return decorator


def normalize_symbol(symbol: str, exchange: str = "binance") -> str:
    """
    Normalize symbol format for different exchanges.
    
    Args:
        symbol: Trading symbol
        exchange: Exchange identifier
        
    Returns:
        Normalized symbol
    """
    # Remove common separators and convert to uppercase
    symbol = symbol.replace("-", "").replace("_", "").replace("/", "").upper()
    
    # Exchange-specific formatting
    if exchange.lower() in ["binance", "bybit"]:
        # Most exchanges use BTCUSDT format
        return symbol
    elif exchange.lower() == "okx":
        # OKX uses BTC-USDT format for spot, BTC-USDT-SWAP for futures
        if "USDT" in symbol and not symbol.endswith("-SWAP"):
            base = symbol.replace("USDT", "")
            return f"{base}-USDT"
    
    return symbol


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.2f}h"
