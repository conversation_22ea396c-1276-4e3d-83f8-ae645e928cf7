#!/usr/bin/env python3
"""
Money Circle Social Trading Features
Member activity feeds, strategy following, leaderboards, and social interactions.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager

logger = logging.getLogger(__name__)

class SocialTrading:
    """Social trading and member interaction system."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
    
    def follow_strategy(self, user_id: int, strategy_id: int, 
                       auto_execute: bool = False, allocation_percentage: float = 0.0) -> bool:
        """Follow a strategy with optional auto-execution."""
        try:
            # Check if strategy is approved and active
            cursor = self.db.conn.execute("""
                SELECT status, is_active FROM strategy_proposals WHERE id = ?
            """, (strategy_id,))
            
            row = cursor.fetchone()
            if not row or row[0] != 'approved' or not row[1]:
                return False
            
            # Insert or update following record
            self.db.conn.execute("""
                INSERT OR REPLACE INTO strategy_following 
                (user_id, strategy_id, auto_execute, allocation_percentage)
                VALUES (?, ?, ?, ?)
            """, (user_id, strategy_id, auto_execute, allocation_percentage))
            
            # Update member profile
            self.db.conn.execute("""
                UPDATE member_profiles 
                SET joined_strategies = (
                    SELECT COUNT(*) FROM strategy_following 
                    WHERE user_id = ? AND is_active = TRUE
                )
                WHERE user_id = ?
            """, (user_id, user_id))
            
            # Log activity
            self._log_member_activity(user_id, 'strategy_follow', {
                'strategy_id': strategy_id,
                'auto_execute': auto_execute,
                'allocation_percentage': allocation_percentage
            })
            
            # Update strategy follower count
            self._update_strategy_followers(strategy_id)
            
            self.db.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error following strategy: {e}")
            return False
    
    def unfollow_strategy(self, user_id: int, strategy_id: int) -> bool:
        """Unfollow a strategy."""
        try:
            self.db.conn.execute("""
                UPDATE strategy_following 
                SET is_active = FALSE
                WHERE user_id = ? AND strategy_id = ?
            """, (user_id, strategy_id))
            
            # Update member profile
            self.db.conn.execute("""
                UPDATE member_profiles 
                SET joined_strategies = (
                    SELECT COUNT(*) FROM strategy_following 
                    WHERE user_id = ? AND is_active = TRUE
                )
                WHERE user_id = ?
            """, (user_id, user_id))
            
            # Log activity
            self._log_member_activity(user_id, 'strategy_unfollow', {
                'strategy_id': strategy_id
            })
            
            # Update strategy follower count
            self._update_strategy_followers(strategy_id)
            
            self.db.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error unfollowing strategy: {e}")
            return False
    
    def get_member_activity_feed(self, user_id: Optional[int] = None, 
                               limit: int = 50) -> List[Dict[str, Any]]:
        """Get member activity feed."""
        try:
            if user_id:
                # Get specific user's activity
                cursor = self.db.conn.execute("""
                    SELECT 
                        ma.id, ma.user_id, ma.activity_type, ma.activity_data,
                        ma.timestamp, u.username, mp.display_name
                    FROM member_activities ma
                    JOIN users u ON ma.user_id = u.id
                    LEFT JOIN member_profiles mp ON ma.user_id = mp.user_id
                    WHERE ma.user_id = ? AND ma.is_public = TRUE
                    ORDER BY ma.timestamp DESC
                    LIMIT ?
                """, (user_id, limit))
            else:
                # Get all public activity
                cursor = self.db.conn.execute("""
                    SELECT 
                        ma.id, ma.user_id, ma.activity_type, ma.activity_data,
                        ma.timestamp, u.username, mp.display_name
                    FROM member_activities ma
                    JOIN users u ON ma.user_id = u.id
                    LEFT JOIN member_profiles mp ON ma.user_id = mp.user_id
                    WHERE ma.is_public = TRUE
                    ORDER BY ma.timestamp DESC
                    LIMIT ?
                """, (limit,))
            
            activities = []
            for row in cursor.fetchall():
                activity_data = json.loads(row[3]) if row[3] else {}
                activities.append({
                    'id': row[0],
                    'user_id': row[1],
                    'activity_type': row[2],
                    'activity_data': activity_data,
                    'timestamp': row[4],
                    'username': row[5],
                    'display_name': row[6] or row[5],
                    'formatted_message': self._format_activity_message(row[2], activity_data)
                })
            
            return activities
            
        except Exception as e:
            logger.error(f"Error getting activity feed: {e}")
            return []
    
    def get_member_leaderboard(self, metric: str = 'total_return', 
                             period: str = 'all_time', limit: int = 20) -> List[Dict[str, Any]]:
        """Get member leaderboard by various metrics."""
        try:
            # Base query for member stats
            base_query = """
                SELECT 
                    u.id, u.username, mp.display_name, mp.reputation_score,
                    mp.joined_strategies, mp.total_votes,
                    COUNT(DISTINCT ut.id) as total_trades,
                    COALESCE(SUM(CASE WHEN ut.side = 'buy' THEN ut.size * ut.price ELSE -ut.size * ut.price END), 0) as total_volume,
                    COALESCE(AVG(ut.price), 0) as avg_trade_price
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN user_trades ut ON u.id = ut.user_id
            """
            
            # Add time filter if needed
            if period != 'all_time':
                if period == 'monthly':
                    base_query += " AND ut.timestamp >= DATE('now', '-1 month')"
                elif period == 'weekly':
                    base_query += " AND ut.timestamp >= DATE('now', '-1 week')"
                elif period == 'daily':
                    base_query += " AND ut.timestamp >= DATE('now', '-1 day')"
            
            base_query += """
                WHERE u.role IN ('admin', 'member') AND mp.public_stats = TRUE
                GROUP BY u.id, u.username, mp.display_name, mp.reputation_score, mp.joined_strategies, mp.total_votes
            """
            
            # Add ordering based on metric
            if metric == 'reputation':
                base_query += " ORDER BY mp.reputation_score DESC"
            elif metric == 'trades':
                base_query += " ORDER BY total_trades DESC"
            elif metric == 'volume':
                base_query += " ORDER BY total_volume DESC"
            elif metric == 'strategies':
                base_query += " ORDER BY mp.joined_strategies DESC"
            elif metric == 'votes':
                base_query += " ORDER BY mp.total_votes DESC"
            else:
                base_query += " ORDER BY mp.reputation_score DESC"
            
            base_query += f" LIMIT {limit}"
            
            cursor = self.db.conn.execute(base_query)
            
            leaderboard = []
            rank = 1
            for row in cursor.fetchall():
                leaderboard.append({
                    'rank': rank,
                    'user_id': row[0],
                    'username': row[1],
                    'display_name': row[2] or row[1],
                    'reputation_score': row[3] or 0.0,
                    'joined_strategies': row[4] or 0,
                    'total_votes': row[5] or 0,
                    'total_trades': row[6] or 0,
                    'total_volume': row[7] or 0.0,
                    'avg_trade_price': row[8] or 0.0
                })
                rank += 1
            
            return leaderboard
            
        except Exception as e:
            logger.error(f"Error getting leaderboard: {e}")
            return []
    
    def get_member_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get comprehensive member profile."""
        try:
            cursor = self.db.conn.execute("""
                SELECT 
                    u.id, u.username, u.email, u.role, u.created_at,
                    mp.display_name, mp.bio, mp.trading_style, mp.risk_tolerance,
                    mp.preferred_assets, mp.public_stats, mp.joined_strategies,
                    mp.total_votes, mp.reputation_score
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                WHERE u.id = ?
            """, (user_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            profile = {
                'user_id': row[0],
                'username': row[1],
                'email': row[2],
                'role': row[3],
                'created_at': row[4],
                'display_name': row[5] or row[1],
                'bio': row[6] or '',
                'trading_style': row[7] or '',
                'risk_tolerance': row[8] or '',
                'preferred_assets': json.loads(row[9]) if row[9] else [],
                'public_stats': row[10] if row[10] is not None else True,
                'joined_strategies': row[11] or 0,
                'total_votes': row[12] or 0,
                'reputation_score': row[13] or 0.0
            }
            
            # Get trading statistics
            cursor = self.db.conn.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN side = 'buy' THEN size * price ELSE -size * price END) as total_volume,
                    AVG(price) as avg_price,
                    MIN(timestamp) as first_trade,
                    MAX(timestamp) as last_trade
                FROM user_trades
                WHERE user_id = ?
            """, (user_id,))
            
            trade_stats = cursor.fetchone()
            if trade_stats:
                profile.update({
                    'total_trades': trade_stats[0] or 0,
                    'total_volume': trade_stats[1] or 0.0,
                    'avg_trade_price': trade_stats[2] or 0.0,
                    'first_trade': trade_stats[3],
                    'last_trade': trade_stats[4]
                })
            
            # Get followed strategies
            cursor = self.db.conn.execute("""
                SELECT 
                    sp.id, sp.title, sp.strategy_type, sf.started_at,
                    sf.auto_execute, sf.allocation_percentage
                FROM strategy_following sf
                JOIN strategy_proposals sp ON sf.strategy_id = sp.id
                WHERE sf.user_id = ? AND sf.is_active = TRUE
                ORDER BY sf.started_at DESC
            """, (user_id,))
            
            followed_strategies = []
            for row in cursor.fetchall():
                followed_strategies.append({
                    'strategy_id': row[0],
                    'title': row[1],
                    'strategy_type': row[2],
                    'started_at': row[3],
                    'auto_execute': row[4],
                    'allocation_percentage': row[5]
                })
            
            profile['followed_strategies'] = followed_strategies
            
            return profile
            
        except Exception as e:
            logger.error(f"Error getting member profile: {e}")
            return None
    
    def update_member_profile(self, user_id: int, profile_data: Dict[str, Any]) -> bool:
        """Update member profile."""
        try:
            # Ensure profile exists
            self.club_db.create_member_profile(user_id)
            
            # Update profile fields
            update_fields = []
            update_values = []
            
            allowed_fields = ['display_name', 'bio', 'trading_style', 'risk_tolerance', 
                            'preferred_assets', 'public_stats']
            
            for field in allowed_fields:
                if field in profile_data:
                    update_fields.append(f"{field} = ?")
                    if field == 'preferred_assets':
                        update_values.append(json.dumps(profile_data[field]))
                    else:
                        update_values.append(profile_data[field])
            
            if update_fields:
                update_values.append(user_id)
                query = f"""
                    UPDATE member_profiles 
                    SET {', '.join(update_fields)}
                    WHERE user_id = ?
                """
                
                self.db.conn.execute(query, update_values)
                self.db.conn.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating member profile: {e}")
            return False
    
    def get_strategy_performance_comparison(self, strategy_ids: List[int], 
                                          days: int = 30) -> Dict[str, Any]:
        """Compare performance of multiple strategies."""
        try:
            if not strategy_ids:
                return {}
            
            placeholders = ','.join(['?' for _ in strategy_ids])
            params = strategy_ids + [days]
            
            cursor = self.db.conn.execute(f"""
                SELECT 
                    sp.id, sp.title, sp.strategy_type,
                    AVG(spr.total_return) as avg_return,
                    AVG(spr.daily_return) as avg_daily_return,
                    AVG(spr.win_rate) as avg_win_rate,
                    AVG(spr.max_drawdown) as avg_drawdown,
                    AVG(spr.sharpe_ratio) as avg_sharpe,
                    COUNT(spr.id) as data_points,
                    MAX(spr.followers_count) as max_followers
                FROM strategy_proposals sp
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                    AND spr.date >= DATE('now', '-{days} days')
                WHERE sp.id IN ({placeholders})
                GROUP BY sp.id, sp.title, sp.strategy_type
            """, params)
            
            comparison = {}
            for row in cursor.fetchall():
                comparison[row[0]] = {
                    'strategy_id': row[0],
                    'title': row[1],
                    'strategy_type': row[2],
                    'avg_return': row[3] or 0.0,
                    'avg_daily_return': row[4] or 0.0,
                    'avg_win_rate': row[5] or 0.0,
                    'avg_drawdown': row[6] or 0.0,
                    'avg_sharpe': row[7] or 0.0,
                    'data_points': row[8] or 0,
                    'max_followers': row[9] or 0
                }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error getting strategy comparison: {e}")
            return {}
    
    def _log_member_activity(self, user_id: int, activity_type: str, data: Dict[str, Any]):
        """Log member activity."""
        try:
            self.db.conn.execute("""
                INSERT INTO member_activities 
                (user_id, activity_type, activity_data)
                VALUES (?, ?, ?)
            """, (user_id, activity_type, json.dumps(data)))
            
        except Exception as e:
            logger.error(f"Error logging member activity: {e}")
    
    def _update_strategy_followers(self, strategy_id: int):
        """Update strategy follower count."""
        try:
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM strategy_following 
                WHERE strategy_id = ? AND is_active = TRUE
            """, (strategy_id,))
            
            follower_count = cursor.fetchone()[0]
            
            # Update today's performance record
            self.db.conn.execute("""
                INSERT OR REPLACE INTO strategy_performance 
                (strategy_id, date, followers_count)
                VALUES (?, DATE('now'), ?)
            """, (strategy_id, follower_count))
            
        except Exception as e:
            logger.error(f"Error updating strategy followers: {e}")
    
    def _format_activity_message(self, activity_type: str, data: Dict[str, Any]) -> str:
        """Format activity message for display."""
        try:
            if activity_type == 'strategy_proposal':
                return f"proposed new strategy: {data.get('title', 'Unknown')}"
            elif activity_type == 'vote':
                return f"voted {data.get('vote', 'unknown')} on a strategy"
            elif activity_type == 'strategy_follow':
                return f"started following a strategy"
            elif activity_type == 'strategy_unfollow':
                return f"stopped following a strategy"
            elif activity_type == 'trade':
                side = data.get('side', 'unknown')
                amount = data.get('amount', 0)
                symbol = data.get('symbol', 'unknown')
                return f"placed {side} order for {amount} {symbol}"
            elif activity_type == 'comment':
                return f"commented on a strategy discussion"
            else:
                return f"performed {activity_type}"
                
        except Exception as e:
            logger.error(f"Error formatting activity message: {e}")
            return "performed an activity"
