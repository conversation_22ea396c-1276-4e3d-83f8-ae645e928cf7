{"strategy_name": "Order Flow", "generated_at": "2025-05-30T23:00:53.722752", "performance_metrics": {"strategy_name": "Order Flow", "timestamp": "2025-05-30T23:00:53.722752", "total_trades": 0, "winning_trades": 0, "losing_trades": 0, "win_rate": 0.0, "total_pnl": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "avg_trade_duration": 0.0, "avg_win": 0.0, "avg_loss": 0.0, "profit_factor": 0.0, "confidence_score": 0.0}, "optimization_suggestions": [{"parameter": "signal_threshold", "current_value": "0.3", "suggested_value": "0.4", "expected_improvement": 0.15, "confidence": 0.8, "reasoning": "Low win rate suggests signals are too aggressive. Increase threshold for higher quality signals."}, {"parameter": "stop_loss_percent", "current_value": "1.5", "suggested_value": "1.0", "expected_improvement": 0.2, "confidence": 0.7, "reasoning": "Low Sharpe ratio suggests poor risk-adjusted returns. Tighter stop loss may improve efficiency."}, {"parameter": "signal_generation_interval", "current_value": "30", "suggested_value": "15", "expected_improvement": 0.3, "confidence": 0.6, "reasoning": "Low trade frequency. Increase signal generation frequency for more opportunities."}], "risk_assessment": {"risk_level": "LOW", "max_drawdown": 0.0, "volatility": 0.0, "consistency": 0.0}, "market_conditions": {"trend": "BULLISH", "volatility": "MEDIUM", "volume": "HIGH", "sentiment": "POSITIVE"}, "recommendations": ["Consider reducing trade frequency and increasing signal quality thresholds", "Increase market exposure with more frequent signal generation", "Apply 3 optimization suggestions for improved performance"]}