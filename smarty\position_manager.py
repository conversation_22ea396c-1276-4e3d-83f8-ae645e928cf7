"""
Position manager for the smart-trader system.
Handles position tracking, stop-loss, and take-profit orders.
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Set

from core.events import Order, OrderResponse, Fill, Signal, Side, OrderType, Position
from core.utils import retry_async
from core.feature_store import feature_store
from executors.htx_executor import HTXExecutor
import numpy as np

logger = logging.getLogger(__name__)


class PositionManager:
    """
    Position manager for the smart-trader system.

    This class handles:
    - Position tracking
    - Stop-loss orders
    - Take-profit orders
    - Position monitoring
    """

    def __init__(self, executor: HTXExecutor, config: Dict[str, Any]):
        """
        Initialize the position manager.

        Args:
            executor: Order executor
            config: Configuration dictionary
        """
        self.executor = executor
        self.config = config

        # Extract configuration
        trading_config = config.get("trading", {})
        self.stop_loss_pct = trading_config.get("stop_loss_pct", 2.0)
        self.take_profit_pct = trading_config.get("take_profit_pct", 4.0)

        # Get position manager config
        position_manager_config = trading_config.get("position_manager", {})
        self.trailing_stop = position_manager_config.get("trailing_stop", False)
        self.trailing_stop_activation = position_manager_config.get("trailing_stop_activation", 1.0)
        self.trailing_stop_distance = position_manager_config.get("trailing_stop_distance", 0.5)

        # Risk management settings
        self.max_risk_per_trade_pct = position_manager_config.get("max_risk_per_trade_pct", 1.0)
        self.max_open_positions = position_manager_config.get("max_open_positions", 3)
        self.max_risk_per_symbol_pct = position_manager_config.get("max_risk_per_symbol_pct", 2.0)
        self.max_daily_drawdown_pct = position_manager_config.get("max_daily_drawdown_pct", 5.0)

        # Position sizing settings
        self.position_sizing_method = position_manager_config.get("position_sizing_method", "fixed")  # fixed, volatility, kelly
        self.volatility_lookback = position_manager_config.get("volatility_lookback", 20)
        self.volatility_risk_factor = position_manager_config.get("volatility_risk_factor", 1.0)

        # Partial take-profit settings
        self.use_partial_take_profits = position_manager_config.get("use_partial_take_profits", False)
        self.partial_tp_levels = position_manager_config.get("partial_tp_levels", [])

        # If no partial TP levels are specified but feature is enabled, create default levels
        if self.use_partial_take_profits and not self.partial_tp_levels:
            # Default: Take 25% at 2% profit, 25% at 3% profit, and the rest at 4% profit
            self.partial_tp_levels = [
                {"percent": 25, "price_pct": 2.0},
                {"percent": 25, "price_pct": 3.0},
                {"percent": 50, "price_pct": 4.0}
            ]

        # Risk overlay settings
        self.use_risk_overlay = position_manager_config.get("use_risk_overlay", False)
        self.market_risk_threshold = position_manager_config.get("market_risk_threshold", 80.0)  # Market risk threshold (0-100)
        self.volatility_risk_threshold = position_manager_config.get("volatility_risk_threshold", 2.0)  # Volatility multiplier threshold

        # Position tracking
        self.positions: Dict[str, Dict[str, Any]] = {}
        self.stop_orders: Dict[str, Dict[str, Any]] = {}
        self.take_profit_orders: Dict[str, Dict[str, Any]] = {}
        self.trailing_stops: Dict[str, Dict[str, Any]] = {}

        # Performance tracking
        self.position_history: List[Dict[str, Any]] = []
        self.daily_pnl: Dict[str, float] = {}  # Date -> PnL
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        self.peak_balance = self.executor.sim_balance if self.executor.simulation_mode else 0.0

        # Risk metrics
        self.current_risk_exposure = 0.0  # Current risk as percentage of account
        self.risk_per_symbol: Dict[str, float] = {}  # Symbol -> risk percentage

        # Active monitoring
        self._monitoring = False
        self._monitor_task = None
        self._stop_event = asyncio.Event()

        logger.info(f"Position manager initialized with stop-loss: {self.stop_loss_pct}%, "
                   f"take-profit: {self.take_profit_pct}%, "
                   f"trailing-stop: {self.trailing_stop}")

    async def start(self):
        """Start position monitoring."""
        if self._monitoring:
            logger.warning("Position monitoring already started")
            return

        self._monitoring = True
        self._stop_event.clear()
        self._monitor_task = asyncio.create_task(self._monitor_positions())
        logger.info("Position monitoring started")

    async def stop(self):
        """Stop position monitoring."""
        if not self._monitoring:
            logger.warning("Position monitoring not running")
            return

        self._monitoring = False
        self._stop_event.set()
        if self._monitor_task:
            await self._monitor_task
        logger.info("Position monitoring stopped")

    async def on_position_opened(self, symbol: str, position: Position, price: float):
        """
        Handle a new position being opened.

        Args:
            symbol: Trading symbol
            position: Position object
            price: Entry price
        """
        logger.info(f"New position opened: {position.side.value} {position.size} {symbol} @ {price}")

        # Store position
        self.positions[symbol] = {
            "position": position,
            "entry_price": price,
            "entry_time": datetime.now(),
            "high_price": price,
            "low_price": price,
            "stop_loss_price": self._calculate_stop_loss(position.side, price),
            "take_profit_price": self._calculate_take_profit(position.side, price),
            "trailing_stop_price": None,
            "trailing_stop_activated": False,
            "partial_tp_orders": [],  # Track partial take-profit orders
            "partial_tp_executed": []  # Track executed partial take-profits
        }

        # Place stop-loss order
        await self._place_stop_loss(symbol, position, price)

        # Place take-profit orders (either partial or full)
        if self.use_partial_take_profits:
            await self._place_partial_take_profits(symbol, position, price)

            # Log partial take-profit levels
            tp_levels = ", ".join([f"{level['percent']}% @ {self._calculate_partial_take_profit(position.side, price, level['price_pct'])}"
                                 for level in self.partial_tp_levels])
            logger.info(f"Position management set up for {symbol}: "
                       f"Stop-loss @ {self.positions[symbol]['stop_loss_price']}, "
                       f"Partial take-profits: {tp_levels}")
        else:
            # Place regular take-profit order
            await self._place_take_profit(symbol, position, price)

            logger.info(f"Position management set up for {symbol}: "
                       f"Stop-loss @ {self.positions[symbol]['stop_loss_price']}, "
                       f"Take-profit @ {self.positions[symbol]['take_profit_price']}")

    async def on_partial_close(self, symbol: str, quantity: float, realized_pnl: float = 0.0):
        """
        Handle a partial position close.

        Args:
            symbol: Trading symbol
            quantity: Quantity closed
            realized_pnl: Realized profit/loss from the partial close
        """
        logger.info(f"Partial position close for {symbol}: {quantity} units, PnL: {realized_pnl:.2f}")

        # Update performance metrics
        self.total_pnl += realized_pnl

        # Update daily PnL
        today = datetime.now().strftime("%Y-%m-%d")
        if today not in self.daily_pnl:
            self.daily_pnl[today] = 0.0
        self.daily_pnl[today] += realized_pnl

        # We don't count partial closes as trades in the statistics

        # Update position data if available
        if symbol in self.positions:
            position_data = self.positions[symbol]
            position = position_data["position"]

            # Update position size
            position.size -= quantity

            # If trailing stop is active, we might want to adjust it based on the new position size
            if position_data["trailing_stop_activated"]:
                # Recalculate trailing stop price (no change in the price, just updating for the new position size)
                current_price = await self._get_current_price(symbol)
                if current_price:
                    position_data["trailing_stop_price"] = self._calculate_trailing_stop(position.side, current_price)

    async def on_position_closed(self, symbol: str, realized_pnl: float = 0.0):
        """
        Handle a position being closed.

        Args:
            symbol: Trading symbol
            realized_pnl: Realized profit/loss from the position
        """
        logger.info(f"Position closed for {symbol}")

        # Cancel any open orders
        await self._cancel_position_orders(symbol)

        # Update performance metrics if we have position data
        if symbol in self.positions:
            position_data = self.positions[symbol]
            position = position_data["position"]

            # Create position history record
            position_record = {
                "symbol": symbol,
                "side": position.side.value,
                "size": position.size,
                "entry_price": position_data["entry_price"],
                "exit_price": await self._get_current_price(symbol) or position_data["entry_price"],
                "entry_time": position_data["entry_time"],
                "exit_time": datetime.now(),
                "duration_seconds": (datetime.now() - position_data["entry_time"]).total_seconds(),
                "pnl": realized_pnl,
                "stop_loss_hit": False,
                "take_profit_hit": False,
                "trailing_stop_hit": False
            }

            # Add to position history
            self.position_history.append(position_record)

            # Update trade statistics
            self.total_trades += 1
            if realized_pnl > 0:
                self.winning_trades += 1
            elif realized_pnl < 0:
                self.losing_trades += 1

            self.total_pnl += realized_pnl

            # Update daily PnL
            today = datetime.now().strftime("%Y-%m-%d")
            if today not in self.daily_pnl:
                self.daily_pnl[today] = 0.0
            self.daily_pnl[today] += realized_pnl

            # Update drawdown metrics
            current_balance = self.executor.sim_balance if self.executor.simulation_mode else await self._get_account_balance()
            if current_balance > self.peak_balance:
                self.peak_balance = current_balance
            else:
                current_drawdown_pct = (self.peak_balance - current_balance) / self.peak_balance * 100
                self.current_drawdown = current_drawdown_pct
                if current_drawdown_pct > self.max_drawdown:
                    self.max_drawdown = current_drawdown_pct

            # Update risk metrics
            if symbol in self.risk_per_symbol:
                self.risk_per_symbol.pop(symbol)
                # Recalculate total risk exposure
                self.current_risk_exposure = sum(self.risk_per_symbol.values())

        # Remove position tracking
        self.positions.pop(symbol, None)
        self.stop_orders.pop(symbol, None)
        self.take_profit_orders.pop(symbol, None)
        self.trailing_stops.pop(symbol, None)

    async def on_price_update(self, symbol: str, price: float):
        """
        Handle a price update for a symbol.

        Args:
            symbol: Trading symbol
            price: Current price
        """
        # Check if we have a position for this symbol
        if symbol not in self.positions:
            return

        position_data = self.positions[symbol]
        position = position_data["position"]

        # Update high/low prices
        if price > position_data["high_price"]:
            position_data["high_price"] = price
        if price < position_data["low_price"]:
            position_data["low_price"] = price

        # Check for trailing stop activation
        if self.trailing_stop and not position_data["trailing_stop_activated"]:
            # Calculate profit percentage
            entry_price = position_data["entry_price"]
            profit_pct = ((price - entry_price) / entry_price) * 100
            if position.side == Side.SELL:
                profit_pct = -profit_pct

            # Activate trailing stop if profit exceeds activation threshold
            if profit_pct >= self.trailing_stop_activation:
                position_data["trailing_stop_activated"] = True
                position_data["trailing_stop_price"] = self._calculate_trailing_stop(position.side, price)
                logger.info(f"Trailing stop activated for {symbol} @ {position_data['trailing_stop_price']}")

        # Update trailing stop if activated
        if position_data["trailing_stop_activated"]:
            new_stop = self._calculate_trailing_stop(position.side, price)
            current_stop = position_data["trailing_stop_price"]

            # Update stop price if it would move in favor of the position
            if position.side == Side.BUY and new_stop > current_stop:
                position_data["trailing_stop_price"] = new_stop
                logger.info(f"Trailing stop updated for {symbol}: {current_stop} -> {new_stop}")
            elif position.side == Side.SELL and new_stop < current_stop:
                position_data["trailing_stop_price"] = new_stop
                logger.info(f"Trailing stop updated for {symbol}: {current_stop} -> {new_stop}")

    async def _monitor_positions(self):
        """Monitor positions for stop-loss and take-profit conditions."""
        logger.info("Starting position monitor loop")

        while self._monitoring and not self._stop_event.is_set():
            try:
                # Check each position
                for symbol in list(self.positions.keys()):
                    await self._check_position(symbol)

                # Sleep to avoid busy waiting
                await asyncio.sleep(1.0)

            except Exception as e:
                logger.error(f"Error in position monitor: {e}")
                await asyncio.sleep(5.0)

    async def _check_position(self, symbol: str):
        """
        Check a position for stop-loss and take-profit conditions.

        Args:
            symbol: Trading symbol
        """
        # Skip if position doesn't exist
        if symbol not in self.positions:
            return

        # Get current price
        price = await self._get_current_price(symbol)
        if not price:
            return

        position_data = self.positions[symbol]
        position = position_data["position"]

        # Update price tracking
        await self.on_price_update(symbol, price)

        # Check stop-loss
        stop_price = position_data["stop_loss_price"]
        if position.side == Side.BUY and price <= stop_price:
            logger.info(f"Stop-loss triggered for {symbol} @ {price} (stop: {stop_price})")
            await self._execute_stop_loss(symbol, price)
        elif position.side == Side.SELL and price >= stop_price:
            logger.info(f"Stop-loss triggered for {symbol} @ {price} (stop: {stop_price})")
            await self._execute_stop_loss(symbol, price)

        # Check take-profit
        take_profit_price = position_data["take_profit_price"]
        if position.side == Side.BUY and price >= take_profit_price:
            logger.info(f"Take-profit triggered for {symbol} @ {price} (target: {take_profit_price})")
            await self._execute_take_profit(symbol, price)
        elif position.side == Side.SELL and price <= take_profit_price:
            logger.info(f"Take-profit triggered for {symbol} @ {price} (target: {take_profit_price})")
            await self._execute_take_profit(symbol, price)

        # Check trailing stop
        if position_data["trailing_stop_activated"]:
            trailing_stop = position_data["trailing_stop_price"]
            if position.side == Side.BUY and price <= trailing_stop:
                logger.info(f"Trailing stop triggered for {symbol} @ {price} (stop: {trailing_stop})")
                await self._execute_trailing_stop(symbol, price)
            elif position.side == Side.SELL and price >= trailing_stop:
                logger.info(f"Trailing stop triggered for {symbol} @ {price} (stop: {trailing_stop})")
                await self._execute_trailing_stop(symbol, price)

    def _calculate_stop_loss(self, side: Side, price: float) -> float:
        """
        Calculate stop-loss price.

        Args:
            side: Position side
            price: Entry price

        Returns:
            Stop-loss price
        """
        if side == Side.BUY:
            return price * (1 - self.stop_loss_pct / 100)
        else:
            return price * (1 + self.stop_loss_pct / 100)

    def _calculate_take_profit(self, side: Side, price: float) -> float:
        """
        Calculate take-profit price.

        Args:
            side: Position side
            price: Entry price

        Returns:
            Take-profit price
        """
        if side == Side.BUY:
            return price * (1 + self.take_profit_pct / 100)
        else:
            return price * (1 - self.take_profit_pct / 100)

    def _calculate_partial_take_profit(self, side: Side, price: float, price_pct: float) -> float:
        """
        Calculate partial take-profit price.

        Args:
            side: Position side
            price: Entry price
            price_pct: Price percentage for partial take-profit

        Returns:
            Partial take-profit price
        """
        if side == Side.BUY:
            return price * (1 + price_pct / 100)
        else:
            return price * (1 - price_pct / 100)

    def _calculate_trailing_stop(self, side: Side, price: float) -> float:
        """
        Calculate trailing stop price.

        Args:
            side: Position side
            price: Current price

        Returns:
            Trailing stop price
        """
        if side == Side.BUY:
            return price * (1 - self.trailing_stop_distance / 100)
        else:
            return price * (1 + self.trailing_stop_distance / 100)

    async def _place_stop_loss(self, symbol: str, position: Position, price: float):
        """
        Place a stop-loss order.

        Args:
            symbol: Trading symbol
            position: Position object
            price: Entry price
        """
        # Calculate stop price
        stop_price = self._calculate_stop_loss(position.side, price)

        # Create order
        side = Side.SELL if position.side == Side.BUY else Side.BUY
        order = Order(
            symbol=symbol,
            side=side,
            quantity=position.size,
            order_type=OrderType.STOP_MARKET,
            price=stop_price,
            reduce_only=True,
            client_order_id=f"sl_{int(time.time())}",
            timestamp=datetime.now()
        )

        # Place order if not in simulation mode
        if not self.executor.simulation_mode:
            try:
                response = await self.executor._place_order(order)
                if response:
                    self.stop_orders[symbol] = {
                        "order": order,
                        "order_id": response.order_id,
                        "price": stop_price
                    }
                    logger.info(f"Placed stop-loss order for {symbol} @ {stop_price}")
            except Exception as e:
                logger.error(f"Error placing stop-loss order: {e}")
        else:
            # In simulation mode, just track the stop price
            self.stop_orders[symbol] = {
                "order": order,
                "order_id": f"sim_sl_{int(time.time())}",
                "price": stop_price
            }
            logger.info(f"Simulated stop-loss for {symbol} @ {stop_price}")

    async def _place_take_profit(self, symbol: str, position: Position, price: float):
        """
        Place a take-profit order.

        Args:
            symbol: Trading symbol
            position: Position object
            price: Entry price
        """
        # Calculate take-profit price
        take_profit_price = self._calculate_take_profit(position.side, price)

        # Create order
        side = Side.SELL if position.side == Side.BUY else Side.BUY
        order = Order(
            symbol=symbol,
            side=side,
            quantity=position.size,
            order_type=OrderType.LIMIT,
            price=take_profit_price,
            reduce_only=True,
            client_order_id=f"tp_{int(time.time())}",
            timestamp=datetime.now()
        )

        # Place order if not in simulation mode
        if not self.executor.simulation_mode:
            try:
                response = await self.executor._place_order(order)
                if response:
                    self.take_profit_orders[symbol] = {
                        "order": order,
                        "order_id": response.order_id,
                        "price": take_profit_price
                    }
                    logger.info(f"Placed take-profit order for {symbol} @ {take_profit_price}")
            except Exception as e:
                logger.error(f"Error placing take-profit order: {e}")
        else:
            # In simulation mode, just track the take-profit price
            self.take_profit_orders[symbol] = {
                "order": order,
                "order_id": f"sim_tp_{int(time.time())}",
                "price": take_profit_price
            }
            logger.info(f"Simulated take-profit for {symbol} @ {take_profit_price}")

    async def _place_partial_take_profits(self, symbol: str, position: Position, price: float):
        """
        Place partial take-profit orders.

        Args:
            symbol: Trading symbol
            position: Position object
            price: Entry price
        """
        # Get position data
        position_data = self.positions[symbol]

        # Clear any existing partial take-profit orders
        position_data["partial_tp_orders"] = []

        # Calculate total percentage (should sum to 100)
        total_percent = sum(level["percent"] for level in self.partial_tp_levels)
        if total_percent != 100:
            logger.warning(f"Partial take-profit levels do not sum to 100% (got {total_percent}%). Adjusting...")
            # Normalize percentages to sum to 100%
            for level in self.partial_tp_levels:
                level["percent"] = level["percent"] * 100 / total_percent

        # Place partial take-profit orders
        remaining_size = position.size
        for i, level in enumerate(self.partial_tp_levels):
            # Calculate quantity for this level
            level_percent = level["percent"] / 100
            level_quantity = position.size * level_percent

            # Ensure we don't exceed remaining size (due to rounding)
            if i == len(self.partial_tp_levels) - 1:
                # Last level - use all remaining size
                level_quantity = remaining_size
            else:
                # Update remaining size
                remaining_size -= level_quantity

            # Calculate take-profit price for this level
            tp_price = self._calculate_partial_take_profit(position.side, price, level["price_pct"])

            # Create order
            side = Side.SELL if position.side == Side.BUY else Side.BUY
            order = Order(
                symbol=symbol,
                side=side,
                quantity=level_quantity,
                order_type=OrderType.LIMIT,
                price=tp_price,
                reduce_only=True,
                client_order_id=f"ptp_{i}_{int(time.time())}",
                timestamp=datetime.now()
            )

            # Place order if not in simulation mode
            if not self.executor.simulation_mode:
                try:
                    response = await self.executor._place_order(order)
                    if response:
                        # Add to partial take-profit orders
                        position_data["partial_tp_orders"].append({
                            "order": order,
                            "order_id": response.order_id,
                            "price": tp_price,
                            "quantity": level_quantity,
                            "percent": level["percent"],
                            "level": i
                        })
                        logger.info(f"Placed partial take-profit order {i+1}/{len(self.partial_tp_levels)} "
                                   f"for {symbol}: {level_quantity} @ {tp_price} ({level['percent']}%)")
                except Exception as e:
                    logger.error(f"Error placing partial take-profit order: {e}")
            else:
                # In simulation mode, just track the take-profit orders
                position_data["partial_tp_orders"].append({
                    "order": order,
                    "order_id": f"sim_ptp_{i}_{int(time.time())}",
                    "price": tp_price,
                    "quantity": level_quantity,
                    "percent": level["percent"],
                    "level": i
                })
                logger.info(f"Simulated partial take-profit order {i+1}/{len(self.partial_tp_levels)} "
                           f"for {symbol}: {level_quantity} @ {tp_price} ({level['percent']}%)")

    async def _execute_stop_loss(self, symbol: str, price: float):
        """
        Execute a stop-loss order.

        Args:
            symbol: Trading symbol
            price: Current price
        """
        # Get position
        position_data = self.positions.get(symbol)
        if not position_data:
            return

        position = position_data["position"]

        # Create market order to close position
        side = Side.SELL if position.side == Side.BUY else Side.BUY
        order = Order(
            symbol=symbol,
            side=side,
            quantity=position.size,
            order_type=OrderType.MARKET,
            reduce_only=True,
            client_order_id=f"sl_exec_{int(time.time())}",
            timestamp=datetime.now()
        )

        # Execute order
        response = await self.executor._place_order(order) if not self.executor.simulation_mode else await self.executor._simulate_order(order)

        if response:
            logger.info(f"Executed stop-loss for {symbol} @ {price}")
            await self.on_position_closed(symbol)

    async def _execute_take_profit(self, symbol: str, price: float):
        """
        Execute a take-profit order.

        Args:
            symbol: Trading symbol
            price: Current price
        """
        # Get position
        position_data = self.positions.get(symbol)
        if not position_data:
            return

        position = position_data["position"]

        # Create market order to close position
        side = Side.SELL if position.side == Side.BUY else Side.BUY
        order = Order(
            symbol=symbol,
            side=side,
            quantity=position.size,
            order_type=OrderType.MARKET,
            reduce_only=True,
            client_order_id=f"tp_exec_{int(time.time())}",
            timestamp=datetime.now()
        )

        # Execute order
        response = await self.executor._place_order(order) if not self.executor.simulation_mode else await self.executor._simulate_order(order)

        if response:
            logger.info(f"Executed take-profit for {symbol} @ {price}")
            await self.on_position_closed(symbol)

    async def _execute_trailing_stop(self, symbol: str, price: float):
        """
        Execute a trailing stop order.

        Args:
            symbol: Trading symbol
            price: Current price
        """
        # Get position
        position_data = self.positions.get(symbol)
        if not position_data:
            return

        position = position_data["position"]

        # Create market order to close position
        side = Side.SELL if position.side == Side.BUY else Side.BUY
        order = Order(
            symbol=symbol,
            side=side,
            quantity=position.size,
            order_type=OrderType.MARKET,
            reduce_only=True,
            client_order_id=f"ts_exec_{int(time.time())}",
            timestamp=datetime.now()
        )

        # Execute order
        response = await self.executor._place_order(order) if not self.executor.simulation_mode else await self.executor._simulate_order(order)

        if response:
            logger.info(f"Executed trailing stop for {symbol} @ {price}")
            await self.on_position_closed(symbol)

    async def _cancel_position_orders(self, symbol: str):
        """
        Cancel all orders for a position.

        Args:
            symbol: Trading symbol
        """
        # Cancel stop-loss order
        if symbol in self.stop_orders:
            order_id = self.stop_orders[symbol]["order_id"]
            if not self.executor.simulation_mode:
                await self.executor.cancel_order(symbol, order_id)
            logger.info(f"Cancelled stop-loss order for {symbol}")

        # Cancel take-profit order
        if symbol in self.take_profit_orders:
            order_id = self.take_profit_orders[symbol]["order_id"]
            if not self.executor.simulation_mode:
                await self.executor.cancel_order(symbol, order_id)
            logger.info(f"Cancelled take-profit order for {symbol}")

        # Cancel partial take-profit orders
        if symbol in self.positions and "partial_tp_orders" in self.positions[symbol]:
            for tp_order in self.positions[symbol]["partial_tp_orders"]:
                order_id = tp_order["order_id"]
                if not self.executor.simulation_mode:
                    await self.executor.cancel_order(symbol, order_id)

            if self.positions[symbol]["partial_tp_orders"]:
                logger.info(f"Cancelled {len(self.positions[symbol]['partial_tp_orders'])} partial take-profit orders for {symbol}")

    async def calculate_position_size(self, symbol: str, price: float, signal_score: float = 0.5) -> float:
        """
        Calculate position size based on the selected sizing method.

        Args:
            symbol: Trading symbol
            price: Current price
            signal_score: Signal confidence score (0.0-1.0)

        Returns:
            Position size in base currency units
        """
        # Get account balance
        balance = self.executor.sim_balance if self.executor.simulation_mode else await self._get_account_balance()
        logger.info(f"Position sizing calculation: balance=${balance:.2f}, price=${price:.2f}, signal_score={signal_score:.2f}")

        # Calculate risk amount (how much we're willing to risk on this trade)
        risk_amount = balance * (self.max_risk_per_trade_pct / 100)
        logger.info(f"Risk amount: ${risk_amount:.2f} ({self.max_risk_per_trade_pct}% of balance)")

        # Adjust risk based on signal confidence
        adjusted_risk = risk_amount * signal_score
        logger.info(f"Adjusted risk (signal confidence): ${adjusted_risk:.2f}")

        # Apply risk overlay if enabled
        if self.use_risk_overlay:
            risk_multiplier = await self._calculate_risk_overlay(symbol)
            adjusted_risk = adjusted_risk * risk_multiplier
            logger.info(f"Applied risk overlay with multiplier {risk_multiplier:.2f} for {symbol}")

        logger.info(f"Final adjusted risk: ${adjusted_risk:.2f}")

        # Check if we've reached the maximum number of open positions
        if len(self.positions) >= self.max_open_positions:
            logger.warning(f"Maximum number of open positions reached ({self.max_open_positions}). Reducing position size.")
            adjusted_risk = adjusted_risk * 0.5  # Reduce position size by 50%

        # Check if we've reached the maximum risk per symbol
        if symbol in self.risk_per_symbol:
            current_symbol_risk = self.risk_per_symbol[symbol]
            if current_symbol_risk >= self.max_risk_per_symbol_pct:
                logger.warning(f"Maximum risk per symbol reached for {symbol} ({current_symbol_risk:.2f}%). Reducing position size.")
                adjusted_risk = adjusted_risk * 0.5  # Reduce position size by 50%

        # Check if we've reached the maximum daily drawdown
        today = datetime.now().strftime("%Y-%m-%d")
        if today in self.daily_pnl and self.daily_pnl[today] < 0:
            daily_drawdown_pct = abs(self.daily_pnl[today]) / balance * 100
            if daily_drawdown_pct >= self.max_daily_drawdown_pct:
                logger.warning(f"Maximum daily drawdown reached ({daily_drawdown_pct:.2f}%). Reducing position size.")
                adjusted_risk = adjusted_risk * 0.25  # Reduce position size by 75%

        # Calculate position size based on selected method
        logger.info(f"Position sizing method: {self.position_sizing_method}")
        if self.position_sizing_method == "volatility":
            position_size = await self._calculate_volatility_based_size(symbol, price, adjusted_risk)
        elif self.position_sizing_method == "kelly":
            position_size = await self._calculate_kelly_based_size(symbol, price, adjusted_risk, signal_score)
        else:
            # Default to fixed sizing
            position_size = await self._calculate_fixed_size(symbol, price, adjusted_risk)

        logger.info(f"Final calculated position size: {position_size}")
        return position_size

    async def _calculate_risk_overlay(self, symbol: str) -> float:
        """
        Calculate risk overlay multiplier based on market conditions.

        Args:
            symbol: Trading symbol

        Returns:
            Risk multiplier (0.0-1.0)
        """
        # Default risk multiplier (full size)
        risk_multiplier = 1.0

        try:
            # Check market volatility
            volatility = await self._get_market_volatility(symbol)
            if volatility:
                # If volatility is higher than threshold, reduce position size
                if volatility > self.volatility_risk_threshold:
                    vol_ratio = self.volatility_risk_threshold / volatility
                    risk_multiplier = min(risk_multiplier, vol_ratio)
                    logger.info(f"Reducing position size due to high volatility: {volatility:.2f}x threshold")

            # Check market sentiment
            sentiment = await self._get_market_sentiment(symbol)
            if sentiment is not None:
                # If sentiment is extremely bullish/bearish, reduce position size
                if sentiment > self.market_risk_threshold:
                    sent_ratio = self.market_risk_threshold / sentiment
                    risk_multiplier = min(risk_multiplier, sent_ratio)
                    logger.info(f"Reducing position size due to extreme market sentiment: {sentiment:.2f}")

            # Ensure risk multiplier is not too small
            risk_multiplier = max(0.1, risk_multiplier)

            return risk_multiplier

        except Exception as e:
            logger.error(f"Error calculating risk overlay: {e}")
            return 1.0  # Default to full size on error

    async def _calculate_fixed_size(self, symbol: str, price: float, risk_amount: float) -> float:
        """
        Calculate position size using fixed risk percentage.

        Args:
            symbol: Trading symbol
            price: Current price
            risk_amount: Amount to risk in quote currency

        Returns:
            Position size in base currency units
        """
        # Calculate stop loss distance
        stop_loss_distance = self.stop_loss_pct / 100
        logger.info(f"Fixed sizing: stop_loss_distance={stop_loss_distance:.4f} ({self.stop_loss_pct}%)")

        # Calculate position size based on risk and stop loss
        position_value = risk_amount / stop_loss_distance
        logger.info(f"Fixed sizing: position_value=${position_value:.2f}")

        position_size = position_value / price
        logger.info(f"Fixed sizing: position_size={position_size:.6f} (before leverage)")

        # Apply leverage if available
        leverage = self.executor.default_leverage
        position_size = position_size * leverage
        logger.info(f"Fixed sizing: position_size={position_size:.6f} (after {leverage}x leverage)")

        # Round to appropriate precision for the symbol
        # For BTC, typically 3 decimal places (0.001 BTC)
        final_size = round(position_size, 3)
        logger.info(f"Fixed sizing: final_size={final_size:.3f} (after rounding)")
        return final_size

    async def _calculate_volatility_based_size(self, symbol: str, price: float, risk_amount: float) -> float:
        """
        Calculate position size based on recent volatility.

        Args:
            symbol: Trading symbol
            price: Current price
            risk_amount: Amount to risk in quote currency

        Returns:
            Position size in base currency units
        """
        logger.info(f"Volatility sizing: Starting calculation for {symbol}")

        # Get historical prices from feature store
        try:
            close_prices = await feature_store.get_time_series(
                symbol, "close_prices", self.volatility_lookback
            )

            logger.info(f"Volatility sizing: Retrieved {len(close_prices) if close_prices else 0} price points")

            if not close_prices or len(close_prices) < self.volatility_lookback / 2:
                # Not enough data, fall back to fixed sizing
                logger.warning(f"Not enough price data for volatility calculation for {symbol}, using fixed sizing")
                return await self._calculate_fixed_size(symbol, price, risk_amount)

            # Extract just the price values from the time series data
            # Time series data might be returned as (timestamp, value) pairs
            prices = []
            for price_data in close_prices:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    # If it's a tuple/list of (timestamp, value)
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    # If it's just the value
                    prices.append(float(price_data))

            if len(prices) < 2:
                # Not enough valid price data
                logger.warning(f"Not enough valid price data for volatility calculation for {symbol}, using fixed sizing")
                return await self._calculate_fixed_size(symbol, price, risk_amount)

            # Calculate daily volatility (standard deviation of returns)
            prices_array = np.array(prices)
            logger.info(f"Volatility sizing: prices_array shape: {prices_array.shape}, first few prices: {prices_array[:5]}")

            returns = np.diff(np.log(prices_array)) * 100  # Log returns in percentage
            logger.info(f"Volatility sizing: returns shape: {returns.shape}, first few returns: {returns[:5]}")

            volatility = np.std(returns)
            logger.info(f"Volatility sizing: calculated volatility: {volatility:.6f}")

            # Adjust position size inversely to volatility
            # Higher volatility = smaller position
            volatility_factor = self.volatility_risk_factor / max(0.1, volatility)
            logger.info(f"Volatility sizing: volatility_risk_factor: {self.volatility_risk_factor}, volatility_factor: {volatility_factor:.6f}")

            # Calculate position size
            position_value = risk_amount * volatility_factor
            logger.info(f"Volatility sizing: position_value: ${position_value:.6f}")

            position_size = position_value / price
            logger.info(f"Volatility sizing: position_size before leverage: {position_size:.6f}")

            # Apply leverage if available
            leverage = self.executor.default_leverage
            position_size = position_size * leverage
            logger.info(f"Volatility sizing: position_size after {leverage}x leverage: {position_size:.6f}")

            # Round to appropriate precision
            final_size = round(position_size, 3)
            logger.info(f"Volatility sizing: final_size after rounding: {final_size:.3f}")

            # Check if position size is below minimum (0.001 BTC for most exchanges)
            min_position_size = 0.001
            if final_size < min_position_size:
                logger.warning(f"Volatility sizing produced position size {final_size:.6f} below minimum {min_position_size}, falling back to fixed sizing")
                return await self._calculate_fixed_size(symbol, price, risk_amount)

            return final_size

        except Exception as e:
            logger.error(f"Error calculating volatility-based position size: {e}")
            logger.info(f"Volatility sizing: Exception occurred, falling back to fixed sizing")
            # Fall back to fixed sizing
            return await self._calculate_fixed_size(symbol, price, risk_amount)

    async def _calculate_kelly_based_size(self, symbol: str, price: float, risk_amount: float, signal_score: float) -> float:
        """
        Calculate position size using Kelly Criterion.

        Args:
            symbol: Trading symbol
            price: Current price
            risk_amount: Amount to risk in quote currency
            signal_score: Signal confidence score (0.0-1.0)

        Returns:
            Position size in base currency units
        """
        # For Kelly, we need win rate and win/loss ratio
        # We'll use historical performance if available, otherwise estimate from signal score

        # Estimate win probability from signal score (0.5-1.0)
        win_prob = max(0.1, signal_score)

        # Estimate win/loss ratio (using take-profit and stop-loss)
        win_amount = self.take_profit_pct
        loss_amount = self.stop_loss_pct
        win_loss_ratio = win_amount / loss_amount

        # Calculate Kelly percentage
        kelly_pct = win_prob - ((1 - win_prob) / win_loss_ratio)

        # Apply a fraction of Kelly (full Kelly is very aggressive)
        kelly_pct = kelly_pct * 0.5  # Half-Kelly

        # Ensure Kelly is positive and not too large
        kelly_pct = max(0.01, min(kelly_pct, 0.2))

        # Calculate position value
        position_value = risk_amount * kelly_pct * 10  # Scale up since Kelly tends to be conservative
        position_size = position_value / price

        # Apply leverage if available
        leverage = self.executor.default_leverage
        position_size = position_size * leverage

        # Round to appropriate precision
        return round(position_size, 3)

    async def _get_market_volatility(self, symbol: str) -> Optional[float]:
        """
        Get market volatility for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Volatility as a multiple of normal volatility, or None if not available
        """
        try:
            # Try to get volatility from feature store
            volatility = await feature_store.get(symbol, "volatility")
            if volatility:
                return float(volatility)

            # Calculate volatility from price history
            close_prices = await feature_store.get_time_series(
                symbol, "close_prices", self.volatility_lookback
            )

            if close_prices and len(close_prices) >= self.volatility_lookback / 2:
                # Extract just the price values from the time series data
                # Time series data might be returned as (timestamp, value) pairs
                prices = []
                for price_data in close_prices:
                    if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                        # If it's a tuple/list of (timestamp, value)
                        prices.append(float(price_data[1]))
                    elif isinstance(price_data, (int, float, str)):
                        # If it's just the value
                        prices.append(float(price_data))

                if len(prices) >= 2:
                    # Calculate daily volatility (standard deviation of returns)
                    prices_array = np.array(prices)
                    returns = np.diff(np.log(prices_array)) * 100  # Log returns in percentage
                    current_volatility = np.std(returns)
                else:
                    # Not enough valid price data
                    return None

                # Get historical volatility if available
                historical_volatility = await feature_store.get(symbol, "historical_volatility")
                if historical_volatility:
                    # Return as multiple of historical volatility
                    return current_volatility / float(historical_volatility)
                else:
                    # Just return current volatility
                    return current_volatility

            return None

        except Exception as e:
            logger.error(f"Error getting market volatility: {e}")
            return None

    async def _get_market_sentiment(self, symbol: str) -> Optional[float]:
        """
        Get market sentiment for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Sentiment score (0-100) or None if not available
        """
        try:
            # Try to get sentiment from feature store
            sentiment = await feature_store.get(symbol, "market_sentiment")
            if sentiment:
                return float(sentiment)

            # Try to get sentiment from social model if available
            social_sentiment = await feature_store.get(symbol, "social_sentiment")
            if social_sentiment:
                return float(social_sentiment) * 100  # Scale to 0-100

            return None

        except Exception as e:
            logger.error(f"Error getting market sentiment: {e}")
            return None

    async def _get_account_balance(self) -> float:
        """
        Get the current account balance.

        Returns:
            Account balance in quote currency
        """
        if self.executor.simulation_mode:
            return self.executor.sim_balance

        try:
            # Try to get balance from executor's client
            if hasattr(self.executor.client, 'get_account_info'):
                account_info = await self.executor.client.get_account_info()
                if account_info and 'available_balance' in account_info:
                    return float(account_info['available_balance'])

            # Fallback
            return 100.0  # Default fallback value

        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return 100.0  # Default fallback value

    def get_position_statistics(self) -> Dict[str, Any]:
        """
        Get position and performance statistics.

        Returns:
            Dictionary of statistics
        """
        # Calculate win rate
        win_rate = 0.0
        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100

        # Calculate average trade
        avg_trade = 0.0
        if self.total_trades > 0:
            avg_trade = self.total_pnl / self.total_trades

        # Get current open positions
        open_positions = []
        for symbol, position_data in self.positions.items():
            position = position_data["position"]
            open_positions.append({
                "symbol": symbol,
                "side": position.side.value,
                "size": position.size,
                "entry_price": position_data["entry_price"],
                "current_price": position_data.get("last_price", position_data["entry_price"]),
                "unrealized_pnl": position.unrealized_pnl,
                "stop_loss": position_data["stop_loss_price"],
                "take_profit": position_data["take_profit_price"],
                "trailing_stop": position_data.get("trailing_stop_price", None),
                "trailing_active": position_data.get("trailing_stop_activated", False),
                "entry_time": position_data["entry_time"].isoformat()
            })

        # Get daily PnL
        daily_pnl_list = [{"date": date, "pnl": pnl} for date, pnl in self.daily_pnl.items()]

        # Return statistics
        return {
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": win_rate,
            "total_pnl": self.total_pnl,
            "average_trade": avg_trade,
            "max_drawdown": self.max_drawdown,
            "current_drawdown": self.current_drawdown,
            "open_positions": open_positions,
            "daily_pnl": daily_pnl_list,
            "current_risk_exposure": self.current_risk_exposure,
            "risk_per_symbol": self.risk_per_symbol
        }

    def get_position_dashboard(self) -> str:
        """
        Get a formatted dashboard of position statistics.

        Returns:
            Formatted string with position statistics
        """
        stats = self.get_position_statistics()

        # Format dashboard
        dashboard = [
            "=== POSITION MANAGER DASHBOARD ===",
            f"Total Trades: {stats['total_trades']}",
            f"Win Rate: {stats['win_rate']:.2f}%",
            f"Total PnL: {stats['total_pnl']:.2f} USDT",
            f"Average Trade: {stats['average_trade']:.2f} USDT",
            f"Max Drawdown: {stats['max_drawdown']:.2f}%",
            f"Current Drawdown: {stats['current_drawdown']:.2f}%",
            f"Current Risk Exposure: {stats['current_risk_exposure']:.2f}%",
            "",
            "--- OPEN POSITIONS ---"
        ]

        for pos in stats['open_positions']:
            dashboard.append(f"{pos['symbol']}: {pos['side']} {pos['size']} @ {pos['entry_price']}")
            dashboard.append(f"  Current Price: {pos['current_price']}")
            dashboard.append(f"  Stop-Loss: {pos['stop_loss']}")
            dashboard.append(f"  Take-Profit: {pos['take_profit']}")
            if pos['trailing_active']:
                dashboard.append(f"  Trailing-Stop: {pos['trailing_stop']} (ACTIVE)")
            dashboard.append("")

        if not stats['open_positions']:
            dashboard.append("No open positions")
            dashboard.append("")

        dashboard.append("--- DAILY PNL ---")
        for day in stats['daily_pnl']:
            dashboard.append(f"{day['date']}: {day['pnl']:.2f} USDT")

        return "\n".join(dashboard)

    def export_positions(self) -> Dict[str, Any]:
        """
        Export position data for saving to file.

        Returns:
            Dictionary with position data
        """
        export_data = {
            "positions": {},
            "position_history": self.position_history,
            "daily_pnl": self.daily_pnl,
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "total_pnl": self.total_pnl,
            "max_drawdown": self.max_drawdown,
            "timestamp": datetime.now().isoformat()
        }

        # Export position data (excluding Position objects which can't be serialized)
        for symbol, position_data in self.positions.items():
            # Create a serializable copy of position data
            position = position_data["position"]
            export_data["positions"][symbol] = {
                "symbol": symbol,
                "side": position.side.value,
                "size": position.size,
                "entry_price": position_data["entry_price"],
                "entry_time": position_data["entry_time"].isoformat(),
                "high_price": position_data["high_price"],
                "low_price": position_data["low_price"],
                "stop_loss_price": position_data["stop_loss_price"],
                "take_profit_price": position_data["take_profit_price"],
                "trailing_stop_price": position_data.get("trailing_stop_price"),
                "trailing_stop_activated": position_data.get("trailing_stop_activated", False)
            }

        return export_data

    async def import_positions(self, import_data: Dict[str, Any]) -> bool:
        """
        Import position data from file.

        Args:
            import_data: Dictionary with position data

        Returns:
            True if import was successful
        """
        try:
            # Import position history and statistics
            self.position_history = import_data.get("position_history", [])
            self.daily_pnl = import_data.get("daily_pnl", {})
            self.total_trades = import_data.get("total_trades", 0)
            self.winning_trades = import_data.get("winning_trades", 0)
            self.losing_trades = import_data.get("losing_trades", 0)
            self.total_pnl = import_data.get("total_pnl", 0.0)
            self.max_drawdown = import_data.get("max_drawdown", 0.0)

            # Import positions
            for symbol, position_data in import_data.get("positions", {}).items():
                # Create Position object
                side = Side.BUY if position_data["side"] == "BUY" else Side.SELL
                position = Position(
                    symbol=symbol,
                    side=side,
                    size=position_data["size"],
                    entry_price=position_data["entry_price"],
                    leverage=self.executor.default_leverage,
                    liquidation_price=0.0,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    margin=position_data["entry_price"] * position_data["size"] / self.executor.default_leverage,
                    timestamp=datetime.now()
                )

                # Create position data
                self.positions[symbol] = {
                    "position": position,
                    "entry_price": position_data["entry_price"],
                    "entry_time": datetime.fromisoformat(position_data["entry_time"]),
                    "high_price": position_data["high_price"],
                    "low_price": position_data["low_price"],
                    "stop_loss_price": position_data["stop_loss_price"],
                    "take_profit_price": position_data["take_profit_price"],
                    "trailing_stop_price": position_data.get("trailing_stop_price"),
                    "trailing_stop_activated": position_data.get("trailing_stop_activated", False),
                    "partial_tp_orders": [],
                    "partial_tp_executed": []
                }

                # Place stop-loss and take-profit orders
                await self._place_stop_loss(symbol, position, position_data["entry_price"])

                if self.use_partial_take_profits:
                    await self._place_partial_take_profits(symbol, position, position_data["entry_price"])
                else:
                    await self._place_take_profit(symbol, position, position_data["entry_price"])

                logger.info(f"Imported position: {side.value} {position_data['size']} {symbol} @ {position_data['entry_price']}")

            logger.info(f"Successfully imported {len(import_data.get('positions', {}))} positions and trade history")
            return True

        except Exception as e:
            logger.error(f"Error importing positions: {e}")
            return False

    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """
        Get the current price for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Current price or None if not available
        """
        # First try to get from feature store
        try:
            price = await feature_store.get(symbol, "last_price")
            if price:
                return float(price)
        except Exception:
            pass

        # Then try executor's client
        try:
            # Try to get price from executor's client
            if hasattr(self.executor.client, 'get_ticker'):
                ticker = await self.executor.client.get_ticker(symbol)
                if ticker and 'last' in ticker:
                    return float(ticker['last'])

            # Fallback to simulation price
            if symbol in self.executor.sim_positions:
                position = self.executor.sim_positions[symbol]
                # Use a slightly different price than entry to simulate market movement
                return position.entry_price * (1 + (time.time() % 10 - 5) / 1000)

            return None
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None
