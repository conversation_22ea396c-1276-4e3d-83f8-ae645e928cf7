#!/usr/bin/env python3
"""
Simple CSRF Token Test for Money Circle
Uses requests library (no async issues on Windows)
"""

import requests
import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_csrf_token():
    """Get CSRF token from login page using requests."""
    base_url = "http://localhost:8086"
    
    try:
        # Get login page
        response = requests.get(f"{base_url}/login", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Extract CSRF token using regex
            csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', content)
            if csrf_match:
                csrf_token = csrf_match.group(1)
                return csrf_token
            else:
                logger.error("CSRF token not found in login page")
                return None
        else:
            logger.error(f"Failed to get login page: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Error getting CSRF token: {e}")
        return None

def test_login_with_csrf(csrf_token):
    """Test login using CSRF token."""
    base_url = "http://localhost:8086"
    
    try:
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123',
            'csrf_token': csrf_token
        }
        
        response = requests.post(f"{base_url}/login", data=login_data, allow_redirects=False, timeout=10)
        
        logger.info(f"Login response status: {response.status_code}")
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            logger.info(f"Redirected to: {location}")
            
            if '/dashboard' in location:
                logger.info("✅ Login successful!")
                return True
            else:
                logger.warning("⚠️ Login redirected but not to dashboard")
                return False
        else:
            logger.error(f"❌ Login failed: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Login test error: {e}")
        return False

def test_login_without_csrf():
    """Test login without CSRF token (should fail)."""
    base_url = "http://localhost:8086"
    
    try:
        login_data = {
            'username': 'epinnox',
            'password': 'securepass123'
            # No CSRF token
        }
        
        response = requests.post(f"{base_url}/login", data=login_data, timeout=10)
        
        if response.status_code == 403:
            if 'CSRF' in response.text:
                logger.info("✅ CSRF protection working - request blocked without token")
                return True
            else:
                logger.warning("⚠️ Request blocked but not for CSRF reasons")
                return False
        else:
            logger.warning(f"⚠️ Request not blocked: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ CSRF protection test error: {e}")
        return False

def show_manual_methods():
    """Show manual methods to get CSRF tokens."""
    logger.info("\n📚 MANUAL METHODS TO GET CSRF TOKENS")
    logger.info("=" * 50)
    
    logger.info("🌐 1. WEB BROWSER (Easiest):")
    logger.info("   - Visit: http://localhost:8086/login")
    logger.info("   - Right-click → View Page Source")
    logger.info("   - Search for: csrf_token")
    logger.info("   - Copy the value from: value=\"abc123...\"")
    
    logger.info("\n🔧 2. CURL COMMAND:")
    logger.info("   curl http://localhost:8086/login | grep csrf_token")
    
    logger.info("\n🐍 3. PYTHON REQUESTS:")
    logger.info("   import requests, re")
    logger.info("   resp = requests.get('http://localhost:8086/login')")
    logger.info("   token = re.search(r'csrf_token\" value=\"([^\"]+)', resp.text).group(1)")
    
    logger.info("\n📱 4. BROWSER DEVELOPER TOOLS:")
    logger.info("   - Visit: http://localhost:8086/login")
    logger.info("   - Press F12 (Developer Tools)")
    logger.info("   - Elements tab → Search for 'csrf_token'")
    logger.info("   - Copy the value attribute")

def main():
    """Main function."""
    print("🔐 Money Circle CSRF Token Test (Windows Compatible)")
    print("=" * 55)
    
    try:
        # Test 1: Get CSRF token
        logger.info("📋 Test 1: Getting CSRF token from login page...")
        csrf_token = get_csrf_token()
        
        if csrf_token:
            logger.info(f"✅ CSRF Token found: {csrf_token}")
            logger.info(f"   Length: {len(csrf_token)} characters")
            logger.info(f"   First 16 chars: {csrf_token[:16]}...")
        else:
            logger.error("❌ Could not get CSRF token")
            print("\n❌ CSRF TOKEN EXTRACTION FAILED")
            print("🔧 Make sure Money Circle is running at http://localhost:8086")
            return 1
        
        # Test 2: Login with CSRF token
        logger.info("\n📋 Test 2: Testing login with CSRF token...")
        login_success = test_login_with_csrf(csrf_token)
        
        # Test 3: Login without CSRF token
        logger.info("\n📋 Test 3: Testing login without CSRF token (should fail)...")
        csrf_protection_working = test_login_without_csrf()
        
        # Show manual methods
        show_manual_methods()
        
        # Summary
        print("\n" + "=" * 55)
        print("📋 TEST RESULTS:")
        print(f"✅ CSRF token extraction: {'SUCCESS' if csrf_token else 'FAILED'}")
        print(f"✅ Login with CSRF token: {'SUCCESS' if login_success else 'FAILED'}")
        print(f"✅ CSRF protection active: {'YES' if csrf_protection_working else 'NO'}")
        
        if csrf_token and login_success:
            print("\n🎉 CSRF TOKEN SYSTEM WORKING PERFECTLY!")
            print("✅ Tokens are being generated correctly")
            print("✅ Login works with valid tokens")
            print("✅ Protection blocks requests without tokens")
            print("\n🌐 You can now login at: http://localhost:8086")
            print("🔐 Credentials: epinnox / securepass123")
            return 0
        else:
            print("\n⚠️ CSRF TOKEN SYSTEM NEEDS ATTENTION")
            print("🔧 Check that Money Circle is running properly")
            return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
