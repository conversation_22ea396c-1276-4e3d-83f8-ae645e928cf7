/* Money Circle Live Trading Interface Styles */

/* Real-time status indicators */
.real-time-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.active {
    background: #00ff88;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.status-indicator.inactive {
    background: #ff4444;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Risk Dashboard */
.risk-dashboard {
    background: linear-gradient(135deg, rgba(255, 68, 68, 0.1), rgba(255, 165, 0, 0.1));
    border: 1px solid rgba(255, 68, 68, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.risk-dashboard h2 {
    color: #ff4444;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.risk-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.risk-metric {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.risk-metric.low { border-left-color: #00ff88; }
.risk-metric.medium { border-left-color: #ffa500; }
.risk-metric.high { border-left-color: #ff6b35; }
.risk-metric.critical { border-left-color: #ff4444; }

.risk-score {
    font-size: 2rem;
    font-weight: bold;
    margin: 5px 0;
}

.risk-score.risk-low { color: #00ff88; }
.risk-score.risk-medium { color: #ffa500; }
.risk-score.risk-high { color: #ff6b35; }
.risk-score.risk-critical { color: #ff4444; }

.risk-warnings {
    background: rgba(255, 68, 68, 0.1);
    border: 1px solid rgba(255, 68, 68, 0.3);
    border-radius: 8px;
    padding: 15px;
}

.risk-warning {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #ff6b35;
}

/* Active Orders Section */
.active-orders-section {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.active-orders-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.order-item {
    display: grid;
    grid-template-columns: 1fr 80px 120px 100px 80px;
    gap: 15px;
    align-items: center;
    padding: 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border-left: 4px solid;
}

.order-item.buy { border-left-color: #00ff88; }
.order-item.sell { border-left-color: #ff4444; }

.order-symbol {
    font-weight: bold;
    color: #ffffff;
}

.order-side {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.order-side.buy {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
}

.order-side.sell {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
}

.order-amount {
    color: #cccccc;
    text-align: right;
}

.order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-align: center;
}

.order-status.pending {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.order-status.filled {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
}

.order-status.cancelled {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
}

/* Trading Interface */
.trading-interface {
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 191, 255, 0.1));
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.trading-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #cccccc;
    font-size: 0.9rem;
    font-weight: 500;
}

.form-group input,
.form-group select {
    padding: 12px;
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.trading-buttons {
    display: flex;
    gap: 15px;
    grid-column: 1 / -1;
    justify-content: center;
    margin-top: 10px;
}

.btn-buy,
.btn-sell {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-buy {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #000000;
}

.btn-buy:hover {
    background: linear-gradient(135deg, #00cc6a, #00aa55);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
}

.btn-sell {
    background: linear-gradient(135deg, #ff4444, #cc3333);
    color: #ffffff;
}

.btn-sell:hover {
    background: linear-gradient(135deg, #cc3333, #aa2222);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
}

/* Enhanced Metric Cards */
.metric-card {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.2);
}

.metric-card label {
    display: block;
    color: #cccccc;
    font-size: 0.9rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 5px;
}

.metric-change {
    font-size: 0.9rem;
    font-weight: 500;
}

.metric-change.positive {
    color: #00ff88;
}

.metric-change.negative {
    color: #ff4444;
}

/* Real-time Updates Animation */
.updating {
    animation: updatePulse 1s ease-in-out;
}

@keyframes updatePulse {
    0% { background-color: rgba(255, 215, 0, 0.1); }
    50% { background-color: rgba(255, 215, 0, 0.3); }
    100% { background-color: rgba(255, 215, 0, 0.1); }
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8));
    border: 1px solid rgba(255, 215, 0, 0.5);
    border-radius: 12px;
    padding: 15px 20px;
    color: #ffffff;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    max-width: 350px;
}

.notification-toast.success {
    border-color: rgba(0, 255, 136, 0.5);
}

.notification-toast.error {
    border-color: rgba(255, 68, 68, 0.5);
}

.notification-toast.warning {
    border-color: rgba(255, 165, 0, 0.5);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #FFD700;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .trading-form {
        grid-template-columns: 1fr;
    }
    
    .trading-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .risk-metrics {
        grid-template-columns: 1fr;
    }
    
    .order-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: center;
    }
    
    .notification-toast {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Dark theme enhancements */
.dashboard-container {
    background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
    min-height: 100vh;
}

/* Scrollbar styling for consistency */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}
