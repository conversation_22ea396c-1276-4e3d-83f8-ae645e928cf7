#!/usr/bin/env python3
"""
Money Circle Club Database Models
Extended database schema for club collaborative features.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from database.models import DatabaseManager

logger = logging.getLogger(__name__)

@dataclass
class StrategyDiscussion:
    """Strategy discussion thread model."""
    id: Optional[int] = None
    strategy_id: int = 0
    user_id: int = 0
    parent_id: Optional[int] = None  # For replies
    content: str = ""
    timestamp: Optional[datetime] = None
    is_edited: bool = False
    edit_timestamp: Optional[datetime] = None

@dataclass
class MemberActivity:
    """Member activity feed model."""
    id: Optional[int] = None
    user_id: int = 0
    activity_type: str = ""  # trade, strategy_proposal, vote, comment
    activity_data: str = ""  # JSON data
    timestamp: Optional[datetime] = None
    is_public: bool = True

@dataclass
class StrategyFollowing:
    """Strategy following model."""
    id: Optional[int] = None
    user_id: int = 0
    strategy_id: int = 0
    auto_execute: bool = False
    allocation_percentage: float = 0.0
    started_at: Optional[datetime] = None
    is_active: bool = True

@dataclass
class ClubNotification:
    """Club notification model."""
    id: Optional[int] = None
    user_id: int = 0
    notification_type: str = ""  # proposal, vote_deadline, result, mention
    title: str = ""
    content: str = ""
    related_id: Optional[int] = None  # strategy_id, discussion_id, etc.
    is_read: bool = False
    timestamp: Optional[datetime] = None

@dataclass
class MemberProfile:
    """Extended member profile model."""
    id: Optional[int] = None
    user_id: int = 0
    display_name: str = ""
    bio: str = ""
    trading_style: str = ""
    risk_tolerance: str = ""  # conservative, moderate, aggressive
    preferred_assets: str = ""  # JSON array
    public_stats: bool = True
    joined_strategies: int = 0
    total_votes: int = 0
    reputation_score: float = 0.0

class ClubDatabaseManager:
    """Extended database manager for club features."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.init_club_tables()

    def init_club_tables(self):
        """Initialize club-specific database tables."""
        try:
            # Strategy discussions table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_discussions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    parent_id INTEGER,
                    content TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_edited BOOLEAN DEFAULT FALSE,
                    edit_timestamp TIMESTAMP,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (parent_id) REFERENCES strategy_discussions (id) ON DELETE CASCADE
                )
            """)

            # Member activity feed table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS member_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    activity_type TEXT NOT NULL,
                    activity_data TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_public BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Strategy following table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_following (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    strategy_id INTEGER NOT NULL,
                    auto_execute BOOLEAN DEFAULT FALSE,
                    allocation_percentage REAL DEFAULT 0.0,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    UNIQUE(user_id, strategy_id)
                )
            """)

            # Club notifications table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS club_notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    notification_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    related_id INTEGER,
                    is_read BOOLEAN DEFAULT FALSE,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Member profiles table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS member_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER UNIQUE NOT NULL,
                    display_name TEXT,
                    bio TEXT,
                    trading_style TEXT,
                    risk_tolerance TEXT CHECK(risk_tolerance IN ('conservative', 'moderate', 'aggressive')),
                    preferred_assets TEXT,
                    public_stats BOOLEAN DEFAULT TRUE,
                    joined_strategies INTEGER DEFAULT 0,
                    total_votes INTEGER DEFAULT 0,
                    reputation_score REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)

            # Strategy performance tracking table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    total_return REAL DEFAULT 0.0,
                    daily_return REAL DEFAULT 0.0,
                    trades_count INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    sharpe_ratio REAL DEFAULT 0.0,
                    followers_count INTEGER DEFAULT 0,
                    aum REAL DEFAULT 0.0,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id) ON DELETE CASCADE,
                    UNIQUE(strategy_id, date)
                )
            """)

            # Trading performance table for individual member performance tracking
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS trading_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    portfolio_value REAL DEFAULT 0.0,
                    total_return REAL DEFAULT 0.0,
                    daily_return REAL DEFAULT 0.0,
                    win_rate REAL DEFAULT 0.0,
                    trade_size REAL DEFAULT 0.0,
                    trades_count INTEGER DEFAULT 0,
                    max_drawdown REAL DEFAULT 0.0,
                    sharpe_ratio REAL DEFAULT 0.0,
                    volatility REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(user_id, date)
                )
            """)

            # Club analytics table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS club_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    total_members INTEGER DEFAULT 0,
                    active_members INTEGER DEFAULT 0,
                    total_aum REAL DEFAULT 0.0,
                    total_strategies INTEGER DEFAULT 0,
                    active_strategies INTEGER DEFAULT 0,
                    club_return REAL DEFAULT 0.0,
                    avg_member_return REAL DEFAULT 0.0,
                    total_trades INTEGER DEFAULT 0,
                    UNIQUE(date)
                )
            """)

            # Voting periods configuration table
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS voting_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Insert default voting configuration
            self.db.conn.execute("""
                INSERT OR IGNORE INTO voting_config (config_key, config_value, description)
                VALUES
                ('voting_period_days', '7', 'Default voting period in days'),
                ('min_votes_required', '3', 'Minimum votes required for strategy approval'),
                ('approval_threshold', '0.6', 'Percentage of votes needed for approval'),
                ('discussion_period_days', '3', 'Discussion period before voting starts')
            """)

            # Create indexes for better performance
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy_discussions_strategy_id ON strategy_discussions(strategy_id)")
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_member_activities_user_id ON member_activities(user_id)")
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_member_activities_timestamp ON member_activities(timestamp)")
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy_following_user_id ON strategy_following(user_id)")
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_club_notifications_user_id ON club_notifications(user_id)")
            self.db.conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_id ON strategy_performance(strategy_id)")

            self.db.conn.commit()
            logger.info("[OK] Club database tables initialized successfully")

        except Exception as e:
            logger.error(f"Club database initialization error: {e}")
            raise

    def get_voting_config(self, key: str) -> Optional[str]:
        """Get voting configuration value."""
        try:
            cursor = self.db.conn.execute("""
                SELECT config_value FROM voting_config WHERE config_key = ?
            """, (key,))

            row = cursor.fetchone()
            return row[0] if row else None

        except Exception as e:
            logger.error(f"Error getting voting config: {e}")
            return None

    def update_voting_config(self, key: str, value: str) -> bool:
        """Update voting configuration."""
        try:
            self.db.conn.execute("""
                UPDATE voting_config
                SET config_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE config_key = ?
            """, (value, key))

            self.db.conn.commit()
            return True

        except Exception as e:
            logger.error(f"Error updating voting config: {e}")
            return False

    def create_member_profile(self, user_id: int, display_name: str = "") -> bool:
        """Create default member profile."""
        try:
            self.db.conn.execute("""
                INSERT OR IGNORE INTO member_profiles (user_id, display_name)
                VALUES (?, ?)
            """, (user_id, display_name))

            self.db.conn.commit()
            return True

        except Exception as e:
            logger.error(f"Error creating member profile: {e}")
            return False
