#!/usr/bin/env python3
"""
Simplified Market Data Service for Smart-Trader Control Center

Provides real-time market data simulation without complex dependencies.
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable

logger = logging.getLogger(__name__)


class SimpleMarketData:
    """Simplified market data service with realistic price simulation."""

    def __init__(self):
        """Initialize market data service."""
        self.running = False
        self.subscribers: List[Callable] = []
        self.market_data: Dict[str, Dict[str, Any]] = {}

        # Supported symbols with REAL CURRENT PRICES (updated May 24, 2025)
        self.symbols = {
            "BTCUSDT": 106738.53,    # Real BTC price
            "ETHUSDT": 3550.83,      # Real ETH price
            "ADAUSDT": 1.00,         # Real ADA price (USDT)
            "DOTUSDT": 7.50,         # Real DOT price
            "LINKUSDT": 14.50,       # Real LINK price
            "LTCUSDT": 72.00,        # Real LTC price
            "BCHUSDT": 245.00,       # Real BCH price
            "XLMUSDT": 0.118,        # Real XLM price
            "EOSUSDT": 0.842,        # Real EOS price
            "TRXUSDT": 0.108         # Real TRX price
        }

        # Initialize market data
        self._initialize_market_data()

    def _initialize_market_data(self) -> None:
        """Initialize market data with realistic values."""
        for symbol, base_price in self.symbols.items():
            # Generate realistic market data
            price = base_price * (1 + random.uniform(-0.02, 0.02))
            volume = random.uniform(1000000, 10000000)
            spread = price * 0.001  # 0.1% spread

            change_24h_percent = random.uniform(-0.08, 0.08)  # ±8% daily change
            change_24h = price * change_24h_percent

            self.market_data[symbol] = {
                "symbol": symbol,
                "price": price,
                "volume": volume,
                "bid": price - spread/2,
                "ask": price + spread/2,
                "high_24h": price * (1 + abs(change_24h_percent) * 0.7),
                "low_24h": price * (1 - abs(change_24h_percent) * 0.7),
                "change_24h": change_24h,
                "change_percent_24h": change_24h_percent * 100,
                "timestamp": datetime.now().isoformat(),
                "last_update": time.time()
            }

    async def start(self) -> None:
        """Start the market data service."""
        if self.running:
            return

        self.running = True
        logger.info("Starting Simple Market Data Service...")

        # Start update loop
        asyncio.create_task(self._update_loop())

        logger.info(f"Simple Market Data Service started for {len(self.symbols)} symbols")

    async def stop(self) -> None:
        """Stop the market data service."""
        self.running = False
        logger.info("Simple Market Data Service stopped")

    async def _update_loop(self) -> None:
        """Main update loop for market data."""
        while self.running:
            try:
                # Update all symbols
                for symbol in self.symbols.keys():
                    self._update_symbol_price(symbol)

                # Notify subscribers
                await self._notify_subscribers()

                # Wait before next update
                await asyncio.sleep(2)  # Update every 2 seconds

            except Exception as e:
                logger.error(f"Error in market data update loop: {e}")
                await asyncio.sleep(5)

    def _update_symbol_price(self, symbol: str) -> None:
        """Update price for a specific symbol with realistic movement."""
        if symbol not in self.market_data:
            return

        current_data = self.market_data[symbol]
        current_price = current_data["price"]

        # Simulate realistic price movement (smaller moves, trending behavior)
        # Use a random walk with slight mean reversion
        base_price = self.symbols[symbol]

        # Mean reversion factor (pulls price back toward base)
        mean_reversion = (base_price - current_price) * 0.001

        # Random component
        random_change = random.uniform(-0.002, 0.002)  # ±0.2% per update

        # Trending component (momentum)
        momentum = current_data.get("momentum", 0.0)
        momentum = momentum * 0.95 + random.uniform(-0.001, 0.001)  # Decay momentum

        # Combine all factors
        total_change = mean_reversion + random_change + momentum
        new_price = current_price * (1 + total_change)

        # Ensure price doesn't go negative or too extreme
        new_price = max(new_price, base_price * 0.5)
        new_price = min(new_price, base_price * 2.0)

        # Update volume with some variation
        volume_change = random.uniform(-0.05, 0.05)
        new_volume = current_data["volume"] * (1 + volume_change)

        # Calculate spread
        spread = new_price * 0.001

        # Update 24h stats (simplified)
        price_change = new_price - current_price
        new_change_24h = current_data["change_24h"] + price_change

        # Gradually decay 24h change to simulate rolling window
        new_change_24h *= 0.9999

        new_change_percent_24h = (new_change_24h / (new_price - new_change_24h)) * 100

        # Update high/low if needed
        new_high = max(current_data["high_24h"], new_price)
        new_low = min(current_data["low_24h"], new_price)

        # Update the data
        self.market_data[symbol].update({
            "price": new_price,
            "volume": new_volume,
            "bid": new_price - spread/2,
            "ask": new_price + spread/2,
            "high_24h": new_high,
            "low_24h": new_low,
            "change_24h": new_change_24h,
            "change_percent_24h": new_change_percent_24h,
            "timestamp": datetime.now().isoformat(),
            "last_update": time.time(),
            "momentum": momentum
        })

    async def _notify_subscribers(self) -> None:
        """Notify all subscribers of market data updates."""
        if not self.subscribers:
            return

        market_update = {
            "type": "market_data_update",
            "timestamp": datetime.now().isoformat(),
            "data": {symbol: data for symbol, data in self.market_data.items()}
        }

        for callback in self.subscribers:
            try:
                await callback(market_update)
            except Exception as e:
                logger.error(f"Error notifying market data subscriber: {e}")

    def subscribe(self, callback: Callable) -> None:
        """Subscribe to market data updates."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback: Callable) -> None:
        """Unsubscribe from market data updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def get_market_data(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current market data."""
        if symbol:
            return self.market_data.get(symbol, {})
        return self.market_data

    def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary statistics."""
        if not self.market_data:
            return {}

        total_volume = sum(data["volume"] for data in self.market_data.values())
        gainers = [data for data in self.market_data.values() if data["change_percent_24h"] > 0]
        losers = [data for data in self.market_data.values() if data["change_percent_24h"] < 0]

        top_gainer = max(self.market_data.values(), key=lambda x: x["change_percent_24h"])
        top_loser = min(self.market_data.values(), key=lambda x: x["change_percent_24h"])

        return {
            "total_symbols": len(self.market_data),
            "total_volume_24h": total_volume,
            "gainers_count": len(gainers),
            "losers_count": len(losers),
            "top_gainer": {
                "symbol": top_gainer["symbol"],
                "change_percent": top_gainer["change_percent_24h"],
                "price": top_gainer["price"]
            },
            "top_loser": {
                "symbol": top_loser["symbol"],
                "change_percent": top_loser["change_percent_24h"],
                "price": top_loser["price"]
            },
            "timestamp": datetime.now().isoformat()
        }

    def get_symbol_list(self) -> List[str]:
        """Get list of available symbols."""
        return list(self.symbols.keys())

    def get_price(self, symbol: str) -> float:
        """Get current price for a symbol."""
        return self.market_data.get(symbol, {}).get("price", 0.0)

    def get_change_percent(self, symbol: str) -> float:
        """Get 24h change percent for a symbol."""
        return self.market_data.get(symbol, {}).get("change_percent_24h", 0.0)


# Global simple market data service instance
simple_market_data = SimpleMarketData()


async def start_simple_market_data() -> None:
    """Start the global simple market data service."""
    await simple_market_data.start()


async def stop_simple_market_data() -> None:
    """Stop the global simple market data service."""
    await simple_market_data.stop()


def get_simple_market_data(symbol: Optional[str] = None) -> Dict[str, Any]:
    """Get current market data."""
    return simple_market_data.get_market_data(symbol)


def get_simple_market_summary() -> Dict[str, Any]:
    """Get market summary."""
    return simple_market_data.get_market_summary()


def subscribe_to_simple_market_data(callback: Callable) -> None:
    """Subscribe to market data updates."""
    simple_market_data.subscribe(callback)


def get_symbol_list() -> List[str]:
    """Get list of available symbols."""
    return simple_market_data.get_symbol_list()


def get_current_price(symbol: str) -> float:
    """Get current price for a symbol."""
    return simple_market_data.get_price(symbol)


def get_price_change(symbol: str) -> float:
    """Get 24h price change percent for a symbol."""
    return simple_market_data.get_change_percent(symbol)
