"""
HTX Futures order executor for the smart-trader system.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

from core.events import Order, OrderResponse, Fill, Signal, Side, OrderType, Position
from core.utils import retry_async
from feeds.htx_futures import HTXFuturesClient

logger = logging.getLogger(__name__)


class HTXExecutor:
    """
    HTX Futures order executor.

    This class handles order execution, position management,
    and order tracking.
    """
    # Singleton instance
    _instance = None

    @classmethod
    def get_instance(cls):
        """
        Get the singleton instance of the executor.

        Returns:
            HTXExecutor instance or None
        """
        return cls._instance

    def __init__(
        self,
        client: HTXFuturesClient,
        config: Dict[str, Any],
        simulation_mode: bool = True
    ):
        """
        Initialize the HTX executor.

        Args:
            client: HTX Futures client
            config: Configuration dictionary
            simulation_mode: Whether to run in simulation mode
        """
        # Set as singleton instance
        HTXExecutor._instance = self

        self.client = client
        self.config = config
        self.simulation_mode = simulation_mode

        # Set simulation mode on client
        if hasattr(self.client, 'simulation_mode'):
            self.client.simulation_mode = simulation_mode

        # Position manager
        self.position_manager = None

        # Default leverage
        self.default_leverage = config.get("default_leverage", 10)

        # Order tracking
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}

        # Simulation state
        self.sim_fills: List[Fill] = []
        # Get simulation balance from trading config if available
        trading_config = config.get("trading", {})
        self.sim_balance = trading_config.get("sim_balance", 100.0)
        logger.info(f"Initialized simulation balance: ${self.sim_balance:.2f} USDT")
        self.sim_positions: Dict[str, Position] = {}

        # Execution lock to prevent concurrent order placement
        self.execution_lock = asyncio.Lock()

    async def execute(self, signal: Signal) -> Optional[OrderResponse]:
        """
        Execute a trading signal.

        Args:
            signal: Trading signal

        Returns:
            Order response or None if no order was placed
        """
        # Convert LLM action to Side enum if needed
        if isinstance(signal.action, str):
            if signal.action.upper() == "BUY":
                action = Side.BUY
            elif signal.action.upper() == "SELL":
                action = Side.SELL
            elif signal.action.upper() == "HOLD":
                action = Side.HOLD
            else:
                logger.warning(f"Unknown action string: {signal.action}, defaulting to HOLD")
                action = Side.HOLD
        else:
            action = signal.action

        if action == Side.HOLD:
            logger.info(f"Signal action is HOLD, no order to execute")
            return None

        async with self.execution_lock:
            # Get current position
            position = await self._get_position(signal.symbol)

            # Create a modified signal with the correct action type
            modified_signal = Signal(
                symbol=signal.symbol,
                action=action,
                score=signal.score if hasattr(signal, 'score') else 0.5,
                timestamp=signal.timestamp if hasattr(signal, 'timestamp') else datetime.now(),
                source=signal.source if hasattr(signal, 'source') else "unknown",
                rationale=signal.rationale if hasattr(signal, 'rationale') else "",
                metadata=signal.metadata if hasattr(signal, 'metadata') else {}
            )

            # Determine order parameters
            order_side, order_size, reduce_only = await self._determine_order_params(modified_signal, position)

            # Check if we should place an order
            if order_size <= 0:
                logger.info(f"Order size is zero or negative, no order to execute")
                return None

            # Get price from signal metadata
            signal_price = signal.metadata.get("price", 0.0)

            # Create order
            order = Order(
                symbol=signal.symbol,
                side=order_side,
                quantity=order_size,
                order_type=OrderType.MARKET,
                price=signal_price,  # Include price for simulation
                reduce_only=reduce_only,
                client_order_id=f"smarty_{int(time.time())}_{uuid.uuid4().hex[:8]}",
                timestamp=datetime.now()
            )

            # Log the source of the signal
            source = signal.source if hasattr(signal, 'source') else "unknown"
            rationale = signal.rationale if hasattr(signal, 'rationale') else ""
            logger.info(f"Executing {order_side.value} signal from {source} with score {modified_signal.score:.2f}")
            if rationale:
                logger.info(f"Signal rationale: {rationale}")

            # Place order
            if self.simulation_mode:
                response = await self._simulate_order(order)
            else:
                response = await self._place_order(order)

            # Track order
            if response:
                self.orders[response.order_id] = order

            return response

    async def _get_position(self, symbol: str) -> Optional[Position]:
        """
        Get current position for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Position or None if no position
        """
        if self.simulation_mode:
            return self.sim_positions.get(symbol)

        try:
            # Get position from exchange
            position_info = await self.client.get_position_info(symbol)

            # Parse position data
            positions = position_info.get("data", [])
            if not positions:
                return None

            position_data = positions[0]

            # Determine position side
            position_size = float(position_data.get("volume", 0))
            if position_size == 0:
                return None

            position_side = Side.BUY if position_data.get("direction") == "buy" else Side.SELL

            # Create position object
            position = Position(
                symbol=symbol,
                side=position_side,
                size=position_size,
                entry_price=float(position_data.get("cost_open", 0)),
                leverage=float(position_data.get("lever_rate", self.default_leverage)),
                liquidation_price=float(position_data.get("liquidation_price", 0)),
                unrealized_pnl=float(position_data.get("unrealized_profit", 0)),
                realized_pnl=float(position_data.get("profit", 0)),
                margin=float(position_data.get("position_margin", 0)),
                timestamp=datetime.now()
            )

            # Update position cache
            self.positions[symbol] = position

            return position

        except Exception as e:
            logger.error(f"Error getting position: {e}")
            return self.positions.get(symbol)

    async def _determine_order_params(
        self,
        signal: Signal,
        position: Optional[Position]
    ) -> Tuple[Side, float, bool]:
        """
        Determine order parameters based on signal and current position.

        Args:
            signal: Trading signal
            position: Current position

        Returns:
            Tuple of (order_side, order_size, reduce_only)
        """
        order_side = signal.action
        reduce_only = False

        # Get price from signal metadata
        price = signal.metadata.get("price", 0.0)
        if price <= 0:
            logger.warning(f"Invalid price in signal metadata: {price}")
            return order_side, 0.0, reduce_only

        # Get balance
        balance = self.sim_balance if self.simulation_mode else signal.metadata.get("balance", 0.0)
        if balance <= 0:
            logger.warning(f"Invalid balance: {balance}")
            return order_side, 0.0, reduce_only

        # Check if order_size is specified in metadata
        if "order_size" in signal.metadata:
            position_size = float(signal.metadata["order_size"])
            logger.info(f"Using order size from metadata: {position_size}")
        elif self.position_manager:
            # Use position manager's position sizing
            try:
                position_size = await self.position_manager.calculate_position_size(
                    symbol=signal.symbol,
                    price=price,
                    signal_score=signal.score
                )
                logger.info(f"Using position manager sizing: {position_size}")
            except Exception as e:
                logger.error(f"Error using position manager sizing: {e}")
                # Fall back to default sizing
                risk_multiplier = abs(signal.score)
                risk_per_trade = self.config.get("risk_per_trade", 0.01)  # 1% of balance
                position_value = balance * risk_per_trade * risk_multiplier
                position_size = position_value / price * self.default_leverage
        else:
            # Get risk multiplier from signal score
            risk_multiplier = abs(signal.score)

            # Calculate base position size
            risk_per_trade = self.config.get("risk_per_trade", 0.01)  # 1% of balance
            position_value = balance * risk_per_trade * risk_multiplier
            position_size = position_value / price * self.default_leverage

        # Round to contract size
        contract_size = self.config.get("contract_size", 0.001)  # Default to 0.001 BTC
        if position_size > 0:
            # Round to nearest contract size, but ensure minimum is one contract
            rounded_contracts = max(1, round(position_size / contract_size))
            position_size = rounded_contracts * contract_size
        logger.info(f"Final position size after rounding: {position_size} (contract size: {contract_size})")

        # Adjust for existing position
        if position:
            if position.side == order_side:
                # Adding to position - check max size
                max_size = self.config.get("max_position_size", 0.0)
                if max_size > 0 and position.size + position_size > max_size:
                    position_size = max(0.0, max_size - position.size)
            else:
                # Closing or reversing position
                if position_size <= position.size:
                    # Partial or full close
                    position_size = min(position_size, position.size)
                    reduce_only = True
                else:
                    # Reversing position
                    # First close existing position
                    reduce_only = True
                    position_size = position.size

        return order_side, position_size, reduce_only

    async def _place_order(self, order: Order) -> Optional[OrderResponse]:
        """
        Place an order on the exchange.

        Args:
            order: Order to place

        Returns:
            Order response or None if failed
        """
        try:
            # Set leverage if needed
            await self.client.set_leverage(order.symbol, self.default_leverage)

            # Place order
            response = await retry_async(
                lambda: self.client.place_order(order),
                retries=3,
                delay=1.0
            )

            logger.info(f"Placed order: {order.side.value} {order.quantity} {order.symbol} @ {order.price or 'MARKET'}")

            return response

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None

    async def _simulate_order(self, order: Order) -> OrderResponse:
        """
        Simulate order execution.

        Args:
            order: Order to simulate

        Returns:
            Simulated order response
        """
        # Generate order ID
        order_id = f"sim_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        # Create response
        response = OrderResponse(
            symbol=order.symbol,
            order_id=order_id,
            client_order_id=order.client_order_id,
            status="FILLED",  # Assume immediate fill in simulation
            timestamp=datetime.now()
        )

        # Simulate fill
        # Get current price from feature store or order price
        price = order.price
        if not price:
            # Try to get current market price from feature store
            try:
                from core.feature_store import feature_store
                market_price = await feature_store.get(order.symbol, "close")
                if market_price:
                    price = float(market_price)
                else:
                    price = 100.0  # Fallback price
            except Exception as e:
                logger.warning(f"Could not get market price for {order.symbol}: {e}")
                price = 100.0  # Fallback price

        # Calculate required margin
        required_margin = price * order.quantity / self.default_leverage
        logger.info(f"Order requires margin: {required_margin:.2f} USDT (available: {self.sim_balance:.2f} USDT)")

        # Check if we have enough balance
        if order.side == Side.BUY and required_margin > self.sim_balance:
            logger.error(f"Insufficient balance for order: {required_margin:.2f} USDT > {self.sim_balance:.2f} USDT")
            return None

        # Deduct margin from balance for new positions
        if order.side == Side.BUY:
            # Check if we're adding to an existing position
            position = self.sim_positions.get(order.symbol)
            if not position or position.side != order.side:
                # New position or reversing, deduct margin
                self.sim_balance -= required_margin
                logger.info(f"Deducted {required_margin:.2f} USDT margin from balance, new balance: {self.sim_balance:.2f} USDT")

        fill = Fill(
            symbol=order.symbol,
            order_id=order_id,
            trade_id=f"sim_trade_{uuid.uuid4().hex[:8]}",
            side=order.side,
            price=price,
            quantity=order.quantity,
            commission=price * order.quantity * 0.0004,  # Simulate 0.04% fee
            commission_asset="USDT",
            timestamp=datetime.now()
        )

        # Add to simulation fills
        self.sim_fills.append(fill)

        # Update simulation position
        await self._update_sim_position(order, fill)

        logger.info(f"Simulated order: {order.side.value} {order.quantity} {order.symbol} @ {price}")

        return response

    async def _update_sim_position(self, order: Order, fill: Fill) -> None:
        """
        Update simulated position after a fill.

        Args:
            order: Executed order
            fill: Order fill
        """
        symbol = order.symbol

        # Get current position
        position = self.sim_positions.get(symbol)
        position_opened = False
        position_closed = False

        if not position:
            # New position
            position = Position(
                symbol=symbol,
                side=order.side,
                size=fill.quantity,
                entry_price=fill.price,
                leverage=self.default_leverage,
                liquidation_price=0.0,  # Not simulating liquidation
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                margin=fill.price * fill.quantity / self.default_leverage,
                timestamp=datetime.now()
            )
            self.sim_positions[symbol] = position
            position_opened = True

            # Commission is already deducted from balance
            self.sim_balance -= fill.commission
            logger.info(f"Deducted {fill.commission:.2f} USDT commission, new balance: {self.sim_balance:.2f} USDT")

        else:
            # Existing position
            if position.side == order.side:
                # Adding to position
                # Calculate new average entry price
                total_value = position.entry_price * position.size + fill.price * fill.quantity
                total_size = position.size + fill.quantity
                new_entry_price = total_value / total_size

                position.entry_price = new_entry_price
                position.size += fill.quantity

                # Deduct commission
                self.sim_balance -= fill.commission
                logger.info(f"Deducted {fill.commission:.2f} USDT commission, new balance: {self.sim_balance:.2f} USDT")

            else:
                # Reducing or closing position
                if fill.quantity < position.size:
                    # Partial close
                    realized_pnl = (fill.price - position.entry_price) * fill.quantity
                    if position.side == Side.SELL:
                        realized_pnl = -realized_pnl

                    position.size -= fill.quantity
                    position.realized_pnl += realized_pnl

                    # Add realized PNL to balance and deduct commission
                    self.sim_balance += realized_pnl - fill.commission
                    logger.info(f"Realized PNL: {realized_pnl:.2f} USDT, commission: {fill.commission:.2f} USDT")
                    logger.info(f"New balance: {self.sim_balance:.2f} USDT")

                    # Notify position manager of partial close with realized PnL
                    if self.position_manager:
                        # Create a partial close record
                        await self.position_manager.on_partial_close(symbol, fill.quantity, realized_pnl)

                else:
                    # Full close or reverse
                    realized_pnl = (fill.price - position.entry_price) * position.size
                    if position.side == Side.SELL:
                        realized_pnl = -realized_pnl

                    # Update balance - add realized PNL and deduct commission
                    self.sim_balance += realized_pnl - fill.commission
                    logger.info(f"Realized PNL: {realized_pnl:.2f} USDT, commission: {fill.commission:.2f} USDT")
                    logger.info(f"New balance: {self.sim_balance:.2f} USDT")

                    if fill.quantity > position.size:
                        # Reverse position
                        remaining_qty = fill.quantity - position.size

                        # Close old position
                        self.sim_positions.pop(symbol, None)
                        position_closed = True

                        # Create new position in opposite direction
                        position = Position(
                            symbol=symbol,
                            side=order.side,
                            size=remaining_qty,
                            entry_price=fill.price,
                            leverage=self.default_leverage,
                            liquidation_price=0.0,
                            unrealized_pnl=0.0,
                            realized_pnl=0.0,
                            margin=fill.price * remaining_qty / self.default_leverage,
                            timestamp=datetime.now()
                        )
                        self.sim_positions[symbol] = position
                        position_opened = True
                    else:
                        # Full close
                        self.sim_positions.pop(symbol, None)
                        position_closed = True

                    # Notify position manager with realized PnL if position was closed
                    if position_closed and self.position_manager:
                        await self.position_manager.on_position_closed(symbol, realized_pnl)

        # Log position update
        if symbol in self.sim_positions:
            logger.info(f"Updated sim position: {position.side.value} {position.size} {symbol} @ {position.entry_price}")
        else:
            logger.info(f"Closed sim position: {symbol}")

        # Notify position manager if available
        if self.position_manager:
            if position_opened:
                await self.position_manager.on_position_opened(symbol, position, fill.price)
            elif position_closed:
                await self.position_manager.on_position_closed(symbol)

    async def _get_symbol_price(self, symbol: str) -> float:
        """
        Get the current price for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Current price or 0.0 if not available
        """
        try:
            # Try to get from feature store if available
            from core.feature_store import feature_store
            price = await feature_store.get(symbol, "last_price")
            if price:
                return float(price)

            # Try to get from client
            if hasattr(self.client, 'get_ticker'):
                ticker = await self.client.get_ticker(symbol)
                if ticker and 'last' in ticker:
                    return float(ticker['last'])

            # Fallback to simulation price
            if symbol in self.sim_positions:
                position = self.sim_positions[symbol]
                return position.entry_price

            # Default fallback
            return 100.0  # Default price for testing

        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return 0.0

    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """
        Cancel an order.

        Args:
            symbol: Symbol of the order
            order_id: Order ID to cancel

        Returns:
            True if cancellation was successful
        """
        if self.simulation_mode:
            # Remove from tracked orders
            if order_id in self.orders:
                del self.orders[order_id]
            return True

        try:
            # Cancel order on exchange
            success = await retry_async(
                self.client.cancel_order,
                symbol,
                order_id,
                retries=3,
                delay=1.0
            )

            if success:
                logger.info(f"Cancelled order: {order_id}")

                # Remove from tracked orders
                if order_id in self.orders:
                    del self.orders[order_id]

            return success

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False

    async def get_open_orders(self, symbol: str = None) -> List[OrderResponse]:
        """
        Get open orders.

        Args:
            symbol: Symbol to get open orders for (None for all)

        Returns:
            List of open orders
        """
        if self.simulation_mode:
            # No open orders in simulation mode (all are filled immediately)
            return []

        try:
            # Get open orders from exchange
            response = await self.client.get_open_orders(symbol)

            # Parse response
            orders_data = response.get("data", {}).get("orders", [])

            # Convert to OrderResponse objects
            orders = []
            for order_data in orders_data:
                symbol = order_data.get("contract_code", "")
                order_id = str(order_data.get("order_id", ""))
                client_order_id = str(order_data.get("client_order_id", ""))
                status = order_data.get("status", "")

                order = OrderResponse(
                    symbol=symbol,
                    order_id=order_id,
                    client_order_id=client_order_id,
                    status=status,
                    timestamp=datetime.fromtimestamp(int(order_data.get("created_at", 0)) / 1000)
                )

                orders.append(order)

            return orders

        except Exception as e:
            logger.error(f"Error getting open orders: {e}")
            return []



