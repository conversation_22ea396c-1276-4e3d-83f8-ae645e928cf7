#!/usr/bin/env python3
"""
Data Producer Manager for Smart-Trader Dashboard

Manages HTX data producers and provides API endpoints for the web interface.
"""

import asyncio
import logging
import subprocess
import sys
import os
import signal
import time
from datetime import datetime
from typing import Dict, Optional, List
import psutil

logger = logging.getLogger(__name__)

class DataProducerManager:
    """Manages data producer processes for the Smart-Trader system."""

    def __init__(self):
        self.producers: Dict[str, Dict] = {}
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def get_status(self) -> Dict:
        """Get status of all data producers."""
        status = {
            "producers": {},
            "total_active": 0,
            "last_updated": datetime.now().isoformat()
        }

        for name, producer in self.producers.items():
            producer_status = self._check_producer_status(name, producer)
            status["producers"][name] = producer_status
            if producer_status["status"] == "running":
                status["total_active"] += 1

        return status

    def _check_producer_status(self, name: str, producer: Dict) -> Dict:
        """Check the status of a specific producer."""
        try:
            process = producer.get("process")
            if process and process.poll() is None:
                # Process is running
                return {
                    "status": "running",
                    "pid": process.pid,
                    "started_at": producer.get("started_at"),
                    "uptime_seconds": int(time.time() - producer.get("start_time", time.time())),
                    "command": producer.get("command", "")
                }
            else:
                # Process is not running
                return {
                    "status": "stopped",
                    "pid": None,
                    "started_at": None,
                    "uptime_seconds": 0,
                    "command": producer.get("command", "")
                }
        except Exception as e:
            logger.error(f"Error checking producer {name}: {e}")
            return {
                "status": "error",
                "pid": None,
                "started_at": None,
                "uptime_seconds": 0,
                "error": str(e),
                "command": producer.get("command", "")
            }

    def start_htx_producer(self) -> Dict:
        """Start the HTX data producer with Binance fallback."""
        try:
            if "htx_producer" in self.producers:
                status = self._check_producer_status("htx_producer", self.producers["htx_producer"])
                if status["status"] == "running":
                    return {"success": False, "message": "HTX producer is already running", "status": status}

            # Path to the HTX data producer script (with Binance fallback)
            script_path = os.path.join(self.base_dir, "feeds", "htx_data_producer.py")

            if not os.path.exists(script_path):
                return {"success": False, "message": f"HTX producer script not found: {script_path}"}

            # Start the process
            process = subprocess.Popen(
                [sys.executable, script_path],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Store producer info
            self.producers["htx_producer"] = {
                "process": process,
                "started_at": datetime.now().isoformat(),
                "start_time": time.time(),
                "command": f"python {script_path}"
            }

            logger.info(f"Started HTX data producer (with Binance fallback) with PID {process.pid}")
            return {
                "success": True,
                "message": "HTX data producer started successfully (with Binance fallback)",
                "pid": process.pid
            }

        except Exception as e:
            logger.error(f"Failed to start HTX producer: {e}")
            return {"success": False, "message": f"Failed to start HTX producer: {e}"}

    def stop_htx_producer(self) -> Dict:
        """Stop the HTX data producer."""
        try:
            if "htx_producer" not in self.producers:
                return {"success": False, "message": "HTX producer is not registered"}

            producer = self.producers["htx_producer"]
            process = producer.get("process")

            if not process or process.poll() is not None:
                # Process is already stopped
                if "htx_producer" in self.producers:
                    del self.producers["htx_producer"]
                return {"success": True, "message": "HTX producer was already stopped"}

            # Try graceful shutdown first
            process.terminate()

            # Wait up to 5 seconds for graceful shutdown
            try:
                process.wait(timeout=5)
                logger.info("HTX producer stopped gracefully")
            except subprocess.TimeoutExpired:
                # Force kill if graceful shutdown fails
                process.kill()
                process.wait()
                logger.info("HTX producer force killed")

            # Remove from producers
            del self.producers["htx_producer"]

            return {"success": True, "message": "HTX producer stopped successfully"}

        except Exception as e:
            logger.error(f"Failed to stop HTX producer: {e}")
            return {"success": False, "message": f"Failed to stop HTX producer: {e}"}

    def restart_htx_producer(self) -> Dict:
        """Restart the HTX data producer."""
        stop_result = self.stop_htx_producer()
        if not stop_result["success"]:
            return stop_result

        # Wait a moment before restarting
        time.sleep(1)

        start_result = self.start_htx_producer()
        return start_result

    def get_producer_logs(self, name: str, lines: int = 50) -> Dict:
        """Get recent logs from a producer."""
        try:
            if name not in self.producers:
                return {"success": False, "message": f"Producer {name} not found"}

            producer = self.producers[name]
            process = producer.get("process")

            if not process:
                return {"success": False, "message": f"No process found for producer {name}"}

            # For now, return basic info since we're using subprocess.PIPE
            # In a production system, you'd want to log to files and read from there
            return {
                "success": True,
                "logs": [
                    f"Producer {name} started at {producer.get('started_at')}",
                    f"PID: {process.pid if process.poll() is None else 'Not running'}",
                    f"Command: {producer.get('command', 'Unknown')}"
                ]
            }

        except Exception as e:
            logger.error(f"Failed to get logs for producer {name}: {e}")
            return {"success": False, "message": f"Failed to get logs: {e}"}

    def cleanup(self):
        """Clean up all running producers."""
        logger.info("Cleaning up data producers...")
        for name in list(self.producers.keys()):
            try:
                self.stop_htx_producer() if name == "htx_producer" else None
            except Exception as e:
                logger.error(f"Error stopping producer {name}: {e}")

# Global manager instance
manager = DataProducerManager()

def get_manager() -> DataProducerManager:
    """Get the global data producer manager instance."""
    return manager
