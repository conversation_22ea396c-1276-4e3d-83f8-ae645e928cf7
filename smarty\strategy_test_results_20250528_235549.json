{"test_start_time": "2025-05-28T23:48:21.413793", "strategies_tested": 10, "strategies_passed": 7, "strategies_failed": 3, "test_duration": 447.844604, "detailed_results": {"Smart Model Integrated": {"strategy_name": "Smart Model Integrated", "test_script": "test_strategy_smart_model_integrated.py", "description": "Full orchestrator.py system with LLM integration", "success": false, "return_code": 1, "test_duration": 71.216079, "test_start": "2025-05-28T23:48:21.414955", "test_end": "2025-05-28T23:49:32.631034", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING SMART MODEL INTEGRATED\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 468660 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Smart Model Integrated...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 30852)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 60 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 4: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 5: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 6: Recent=0, Signals=0, Market=0, LLM=0\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Smart Model Integrated...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Smart Model Integrated\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u274c\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  orchestrator: \\u2705\r\nINFO:__main__:  llm_consumer: \\u274c\r\nINFO:__main__:  feature_store: \\u274c\r\nINFO:__main__:  model_predictions: \\u274c\r\nINFO:__main__:  signal_generation: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u274c FAIL\r\n", "detailed_results": {"strategy_name": "Smart Model Integrated", "test_start_time": "2025-05-28T23:48:21.576728", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 0, "signal_messages": 0, "market_messages": 0, "llm_messages": 0, "data_flowing": false}, "components": {"orchestrator": true, "llm_consumer": false, "feature_store": false, "model_predictions": false, "signal_generation": false}, "strategy_shutdown": true, "overall_success": false, "test_duration": 60, "test_end_time": "2025-05-28T23:49:32.615835"}}, "Smart Strategy Only": {"strategy_name": "Smart Strategy Only", "test_script": "test_strategy_smart_strategy_only.py", "description": "Technical analysis only (run_smart_strategy_live.py)", "success": false, "return_code": 1, "test_duration": 50.11501, "test_start": "2025-05-28T23:49:32.640582", "test_end": "2025-05-28T23:50:22.755592", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING SMART STRATEGY ONLY\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 468660 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Smart Strategy Only...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 7524)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 45 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=0, Signals=0, Market=0, Indicators=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=0, Signals=0, Market=0, Indicators=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=0, Signals=0, Market=0, Indicators=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 4: Recent=0, Signals=0, Market=0, Indicators=0\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Smart Strategy Only...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Smart Strategy Only\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u274c\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  smart_strategy: \\u2705\r\nINFO:__main__:  technical_analysis: \\u274c\r\nINFO:__main__:  signal_generation: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u274c FAIL\r\n", "detailed_results": {"strategy_name": "Smart Strategy Only", "test_start_time": "2025-05-28T23:49:32.799086", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 0, "signal_messages": 0, "market_messages": 0, "indicator_messages": 0, "data_flowing": false}, "components": {"smart_strategy": true, "technical_analysis": false, "signal_generation": false, "market_data_feed": false}, "strategy_shutdown": true, "overall_success": false, "test_duration": 45, "test_end_time": "2025-05-28T23:50:22.741223"}}, "Order Flow": {"strategy_name": "Order Flow", "test_script": "test_strategy_order_flow.py", "description": "Dataframe-based order flow analysis", "success": false, "return_code": 1, "test_duration": 51.068848, "test_start": "2025-05-28T23:50:22.763370", "test_end": "2025-05-28T23:51:13.832218", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING ORDER FLOW\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 468660 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Order Flow...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 35828)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 45 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=0, OrderFlow=0, DataFrame=0, Signals=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=0, OrderFlow=0, DataFrame=0, Signals=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=0, OrderFlow=0, DataFrame=0, Signals=0\r\nINFO:__main__:\\U0001f4c8 Data flow check 4: Recent=0, OrderFlow=0, DataFrame=0, Signals=0\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Order Flow...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Order Flow\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u274c\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  dataframe_runner: \\u2705\r\nINFO:__main__:  order_flow_analysis: \\u274c\r\nINFO:__main__:  signal_generation: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u274c FAIL\r\n", "detailed_results": {"strategy_name": "Order Flow", "test_start_time": "2025-05-28T23:50:22.924416", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 0, "orderflow_messages": 0, "dataframe_messages": 0, "market_messages": 0, "signal_messages": 0, "data_flowing": false}, "components": {"dataframe_runner": true, "order_flow_analysis": false, "signal_generation": false, "market_data_feed": false}, "strategy_shutdown": true, "overall_success": false, "test_duration": 45, "test_end_time": "2025-05-28T23:51:13.816992"}}, "RSI Strategy": {"strategy_name": "RSI Strategy", "test_script": "test_strategy_data_producer.py", "description": "RSI-based trading signals", "success": true, "return_code": 0, "test_duration": 38.567799, "test_start": "2025-05-28T23:51:13.841769", "test_end": "2025-05-28T23:51:52.409568", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING RSI STRATEGY\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 468660 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting RSI Strategy...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 2084)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=1410, Market=705, HTX=705, Depth=108\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=2246, Market=1123, HTX=1123, Depth=158\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=2686, Market=1343, HTX=1343, Depth=202\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping RSI Strategy...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: RSI Strategy\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "RSI Strategy", "test_start_time": "2025-05-28T23:51:13.995164", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 2686, "market_messages": 1343, "htx_messages": 1343, "depth_messages": 202, "trade_messages": 2242, "kline_messages": 40, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:51:52.394675"}}, "Bollinger Bands": {"strategy_name": "Bollinger Bands", "test_script": "test_strategy_data_producer.py", "description": "Bollinger Bands trading strategy", "success": true, "return_code": 0, "test_duration": 39.367661, "test_start": "2025-05-28T23:51:52.417235", "test_end": "2025-05-28T23:52:31.784896", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING BOLLINGER BANDS\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 472008 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Bollinger Bands...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 13544)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=1988, Market=994, HTX=994, Depth=210\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=1958, Market=979, HTX=979, Depth=226\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=3056, Market=1528, HTX=1528, Depth=220\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Bollinger Bands...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Bollinger Bands\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "Bollinger Bands", "test_start_time": "2025-05-28T23:51:52.577424", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 3056, "market_messages": 1528, "htx_messages": 1528, "depth_messages": 220, "trade_messages": 2572, "kline_messages": 44, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:52:31.768397"}}, "Multi-Signal": {"strategy_name": "Multi-Signal", "test_script": "test_strategy_data_producer.py", "description": "Multiple signal combination strategy", "success": true, "return_code": 0, "test_duration": 39.35539, "test_start": "2025-05-28T23:52:31.794492", "test_end": "2025-05-28T23:53:11.149882", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING MULTI-SIGNAL\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 475300 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Multi-Signal...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 27304)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=2918, Market=1459, HTX=1459, Depth=223\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=2334, Market=1167, HTX=1167, Depth=378\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=2380, Market=1190, HTX=1190, Depth=435\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Multi-Signal...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Multi-Signal\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n"}, "Ensemble Model": {"strategy_name": "Ensemble Model", "test_script": "test_strategy_data_producer.py", "description": "Ensemble model predictions", "success": true, "return_code": 0, "test_duration": 39.435871, "test_start": "2025-05-28T23:53:11.150559", "test_end": "2025-05-28T23:53:50.586430", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING ENSEMBLE MODEL\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 478132 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Ensemble Model...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 34708)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=7366, Market=3683, HTX=3683, Depth=420\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=9834, Market=4917, HTX=4917, Depth=522\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=10173, Market=5086, HTX=5087, Depth=614\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Ensemble Model...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Ensemble Model\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "Ensemble Model", "test_start_time": "2025-05-28T23:53:11.301945", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 10173, "market_messages": 5086, "htx_messages": 5087, "depth_messages": 614, "trade_messages": 8821, "kline_messages": 124, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:53:50.572606"}}, "SMA Crossover": {"strategy_name": "SMA Crossover", "test_script": "test_strategy_data_producer.py", "description": "Simple Moving Average crossover", "success": true, "return_code": 0, "test_duration": 39.507774, "test_start": "2025-05-28T23:53:50.593515", "test_end": "2025-05-28T23:54:30.101289", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING SMA CROSSOVER\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 491596 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting SMA Crossover...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 33824)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=5354, Market=2677, HTX=2677, Depth=391\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=2864, Market=1432, HTX=1432, Depth=270\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=2645, Market=1323, HTX=1323, Depth=214\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping SMA Crossover...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: SMA Crossover\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "SMA Crossover", "test_start_time": "2025-05-28T23:53:50.748356", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 2645, "market_messages": 1323, "htx_messages": 1323, "depth_messages": 214, "trade_messages": 2177, "kline_messages": 44, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:54:30.087478"}}, "VWAP Strategy": {"strategy_name": "VWAP Strategy", "test_script": "test_strategy_data_producer.py", "description": "Volume Weighted Average Price strategy", "success": true, "return_code": 0, "test_duration": 39.55002, "test_start": "2025-05-28T23:54:30.109369", "test_end": "2025-05-28T23:55:09.659389", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING VWAP STRATEGY\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 494638 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting VWAP Strategy...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 30836)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=2704, Market=1352, HTX=1352, Depth=57\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=2368, Market=1184, HTX=1184, Depth=38\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=2698, Market=1349, HTX=1349, Depth=98\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping VWAP Strategy...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: VWAP Strategy\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "VWAP Strategy", "test_start_time": "2025-05-28T23:54:30.263645", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 2698, "market_messages": 1349, "htx_messages": 1349, "depth_messages": 98, "trade_messages": 2482, "kline_messages": 20, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:55:09.643227"}}, "Scalper Strategy": {"strategy_name": "Scalper Strategy", "test_script": "test_strategy_data_producer.py", "description": "High-frequency scalping strategy", "success": true, "return_code": 0, "test_duration": 39.5838, "test_start": "2025-05-28T23:55:09.666942", "test_end": "2025-05-28T23:55:49.250742", "stdout": "", "stderr": "INFO:__main__:\\U0001f3af TESTING SCALPER STRATEGY\r\nINFO:__main__:============================================================\r\nINFO:__main__:\\U0001f4ca Testing database connection...\r\nINFO:__main__:\\u2705 Database connected: 497828 messages\r\nINFO:__main__:\\U0001f680 Testing strategy startup...\r\nINFO:__main__:\\U0001f680 Starting Scalper Strategy...\r\nINFO:__main__:\\u2705 Strategy started successfully (PID: 30768)\r\nINFO:__main__:\\U0001f50d Checking process status...\r\nINFO:__main__:\\U0001f4e1 Monitoring data flow for 30 seconds...\r\nINFO:__main__:\\U0001f4c8 Data flow check 1: Recent=2148, Market=1074, HTX=1074, Depth=228\r\nINFO:__main__:\\U0001f4c8 Data flow check 2: Recent=2190, Market=1095, HTX=1095, Depth=284\r\nINFO:__main__:\\U0001f4c8 Data flow check 3: Recent=2746, Market=1373, HTX=1373, Depth=366\r\nINFO:__main__:\\U0001f527 Checking strategy components...\r\nERROR:__main__:\\u274c Error checking components: tuple indices must be integers or slices, not str\r\nINFO:__main__:\\U0001f6d1 Testing strategy shutdown...\r\nINFO:__main__:\\U0001f6d1 Stopping Scalper Strategy...\r\nINFO:__main__:\\u2705 Strategy stopped gracefully\r\nINFO:__main__:\r\n\\U0001f4ca TEST RESULTS SUMMARY\r\nINFO:__main__:========================================\r\nINFO:__main__:Strategy: Scalper Strategy\r\nINFO:__main__:Database Connection: \\u2705\r\nINFO:__main__:Strategy Startup: \\u2705\r\nINFO:__main__:Process Running: \\u2705\r\nINFO:__main__:Data Flowing: \\u2705\r\nINFO:__main__:Strategy Shutdown: \\u2705\r\nINFO:__main__:\r\nComponent Status:\r\nINFO:__main__:  data_producer: \\u2705\r\nINFO:__main__:  websocket_connection: \\u274c\r\nINFO:__main__:  market_data_feed: \\u274c\r\nINFO:__main__:  database_writes: \\u274c\r\nINFO:__main__:\r\nOverall Success: \\u2705 PASS\r\n", "detailed_results": {"strategy_name": "Scalper Strategy", "test_start_time": "2025-05-28T23:55:09.841935", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 2746, "market_messages": 1373, "htx_messages": 1373, "depth_messages": 366, "trade_messages": 1938, "kline_messages": 76, "data_flowing": true}, "components": {"data_producer": true, "websocket_connection": false, "market_data_feed": false, "database_writes": false}, "strategy_shutdown": true, "overall_success": true, "test_duration": 30, "test_end_time": "2025-05-28T23:55:49.234664"}}}, "test_end_time": "2025-05-28T23:55:49.258418", "success_rate": 70.0}