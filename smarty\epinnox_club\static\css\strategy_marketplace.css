/* Money Circle Strategy Marketplace Styles */

.marketplace-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    color: #e2e8f0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.marketplace-header {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.marketplace-branding h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.marketplace-branding p {
    margin: 0.25rem 0 0 0;
    color: #94a3b8;
    font-size: 0.9rem;
}

.marketplace-nav {
    display: flex;
    gap: 1.5rem;
}

.marketplace-nav .nav-link {
    color: #94a3b8;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.marketplace-nav .nav-link:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.marketplace-nav .nav-link.active {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.2);
}

/* Stats Bar */
.marketplace-stats {
    background: rgba(30, 41, 59, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
}

.stats-grid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 0.75rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f1f5f9;
    margin: 0;
}

.stat-label {
    font-size: 0.875rem;
    color: #94a3b8;
    margin: 0;
}

/* Controls Section */
.marketplace-controls {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.search-section {
    margin-bottom: 1.5rem;
}

.search-bar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 1rem;
}

.search-bar input::placeholder {
    color: #64748b;
}

.search-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    border-radius: 0.5rem;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.quick-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-chip {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.filter-chip:hover,
.filter-chip.active {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.advanced-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #94a3b8;
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-group select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #e2e8f0;
    font-size: 0.875rem;
}

/* Featured Strategies */
.featured-strategies {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem 2rem 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: #f1f5f9;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    color: #3b82f6;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.featured-strategy-card {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.featured-strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
}

.strategy-header {
    margin-bottom: 1rem;
}

.strategy-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.strategy-title h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #f1f5f9;
}

.featured-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1f2937;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.strategy-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.strategy-type {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
}

.risk-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.risk-badge.risk-low {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.risk-badge.risk-medium {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.risk-badge.risk-high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.strategy-performance {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.performance-metric {
    flex: 1;
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 0.75rem;
    color: #94a3b8;
    margin-bottom: 0.25rem;
}

.metric-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
}

.metric-value.positive {
    color: #22c55e;
}

.metric-value.negative {
    color: #ef4444;
}

.strategy-description {
    color: #94a3b8;
    line-height: 1.5;
    margin: 1rem 0;
}

.strategy-creator {
    margin: 1rem 0;
    font-size: 0.875rem;
}

.creator-label {
    color: #64748b;
}

.creator-name {
    color: #3b82f6;
    font-weight: 500;
}

.strategy-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-primary,
.btn-secondary,
.btn-tertiary {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-secondary {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.btn-tertiary {
    background: rgba(255, 255, 255, 0.05);
    color: #94a3b8;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover,
.btn-secondary:hover,
.btn-tertiary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Strategy Grid */
.strategy-grid-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem 2rem 2rem;
}

.results-info {
    color: #94a3b8;
    font-size: 0.875rem;
}

.strategy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.strategy-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(59, 130, 246, 0.3);
}

.strategy-card.followed {
    border-color: rgba(34, 197, 94, 0.5);
    background: rgba(34, 197, 94, 0.05);
}

.strategy-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.strategy-title-section {
    flex: 1;
}

.strategy-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #f1f5f9;
    line-height: 1.3;
}

.strategy-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.type-badge {
    background: rgba(139, 92, 246, 0.2);
    color: #a78bfa;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.featured-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1f2937;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.strategy-actions-mini {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.action-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.action-btn.followed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.strategy-metrics {
    margin: 1rem 0;
}

.metric-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.metric {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.metric-label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.metric-value {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #f1f5f9;
}

.strategy-description {
    color: #94a3b8;
    line-height: 1.5;
    margin: 1rem 0;
    font-size: 0.875rem;
}

.strategy-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.creator-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.creator-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.creator-name {
    color: #3b82f6;
    font-weight: 500;
    font-size: 0.875rem;
}

.strategy-age {
    color: #64748b;
    font-size: 0.75rem;
}

.strategy-card-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
}

.btn-view,
.btn-follow {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 0.875rem;
}

.btn-view {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-view:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border-color: rgba(59, 130, 246, 0.3);
}

.btn-follow {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.btn-follow.active {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.3);
}

.btn-follow:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-follow.active:hover {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    border-radius: 0.75rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.large .modal-content {
    max-width: 800px;
}

.modal.extra-large .modal-content {
    max-width: 1200px;
}

.modal-content {
    background: linear-gradient(135deg, #1e293b, #334155);
    margin: 5% auto;
    padding: 0;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #f1f5f9;
    font-size: 1.5rem;
    font-weight: 700;
}

.close {
    color: #94a3b8;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ef4444;
}

.modal-body {
    padding: 1.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #64748b;
    font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .marketplace-nav {
        order: -1;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .advanced-filters {
        grid-template-columns: 1fr;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }

    .strategy-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
}
