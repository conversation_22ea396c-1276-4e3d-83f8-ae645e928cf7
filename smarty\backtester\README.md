# Smart-Trader Backtesting Framework

This module provides a comprehensive backtesting framework for the smart-trader system, allowing you to test trading strategies with historical data.

## Features

- Historical data loading and preprocessing
- Simulation engine that replays historical data
- Integration with position manager for realistic position management
- Performance metrics calculation (Sharpe ratio, drawdown, win rate, etc.)
- Visualization tools for backtest results
- Support for multiple trading strategies

## Components

- **Backtester**: Core backtesting engine that simulates market conditions and executes strategies
- **Visualizer**: Tools for visualizing backtest results (equity curves, drawdowns, etc.)
- **Strategies**: Collection of trading strategies (SMA crossover, Bollinger Bands, RSI, etc.)
- **Data Downloader**: Utility for downloading historical data from exchanges

## Usage

### 1. Download or Generate Historical Data

You can either download real historical data from exchanges or generate synthetic data for testing:

```bash
# Download real data from HTX
python download_sample_data.py --symbols BTC-USDT ETH-USDT --start-date 2023-01-01 --end-date 2023-03-31

# Generate synthetic data
python generate_sample_data.py --symbols BTC-USDT ETH-USDT --days 90
```

### 2. Run a Backtest

Run a backtest with a specific strategy and parameters:

```bash
python run_backtest.py --symbols BTC-USDT --start-date 2023-01-01 --end-date 2023-03-31 --strategy sma --fast-period 10 --slow-period 30
```

Available strategies:
- `sma`: Simple Moving Average Crossover
- `bollinger`: Bollinger Bands
- `rsi`: Relative Strength Index

### 3. View Results

The backtest results will be saved to the specified output directory (default: `results/backtest`). The results include:

- Equity curve CSV file
- Trades JSON file
- Signals JSON file
- Performance metrics JSON file
- Visualization plots (equity curve, drawdown, trade distribution, monthly returns)

## Creating Custom Strategies

You can create custom strategies by implementing a function that generates signals based on market data. The function should have the following signature:

```python
async def my_custom_strategy(
    timestamp: datetime,
    symbols: List[str],
    **params
) -> List[Signal]:
    # Strategy logic here
    # ...
    return signals
```

Then, you can use your custom strategy with the backtester:

```python
from backtester import Backtester

# Create backtester
backtester = Backtester(config=config)

# Load data
await backtester.load_data(symbols=symbols, start_date=start_date, end_date=end_date)

# Run backtest with custom strategy
await backtester.run_backtest(signal_generator=my_custom_strategy)
```

## Performance Metrics

The backtester calculates the following performance metrics:

- **Total Return**: Overall return of the strategy
- **Annual Return**: Annualized return
- **Annual Volatility**: Annualized standard deviation of returns
- **Sharpe Ratio**: Risk-adjusted return (assuming risk-free rate of 0)
- **Max Drawdown**: Maximum peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Total Trades**: Number of trades executed

## Visualization

The visualizer generates the following plots:

- **Equity Curve**: Shows the growth of the account over time
- **Drawdown**: Shows the drawdowns over time
- **Trade Distribution**: Shows the distribution of trades by symbol and side
- **Monthly Returns**: Shows the returns by month

## Limitations

- The backtester uses historical data and may not accurately reflect future market conditions
- Transaction costs and slippage may not be fully accounted for
- The backtester assumes perfect execution of orders
- Historical data may contain gaps or inaccuracies

## Future Enhancements

- Support for more sophisticated order types (OCO, trailing stops, etc.)
- Monte Carlo simulation for robustness testing
- Parameter optimization using grid search or genetic algorithms
- Walk-forward testing for more realistic performance estimation
- Integration with machine learning models for strategy development
