"""
HTX Futures API client package.

This package provides a modular, robust HTX Futures API client with:
- Separated concerns (WebSocket, REST, parsing)
- Enhanced error handling and logging
- Comprehensive type safety
- Built-in retry mechanisms
- Health monitoring
- Performance metrics
"""

from .constants import __version__
from .types import (
    HTXConfig, ConnectionState, MessageType, QueuePolicy,
    ConnectionMetrics, HealthStatus, APIResponse,
    HTXError, ConnectionError, AuthenticationError, APIError,
    ValidationError, RateLimitError,
    validate_symbol, validate_interval, normalize_symbol
)
from .client import HTXFuturesClient
from .websocket import WebSocketManager
from .rest import HTXRestClient
from .parser import MessageParser

# Backward compatibility - export the main client as the original name
HTXFuturesClient = HTXFuturesClient

__all__ = [
    # Version
    "__version__",
    
    # Main client
    "HTXFuturesClient",
    
    # Components
    "WebSocketManager",
    "HTXRestClient", 
    "MessageParser",
    
    # Configuration and types
    "HTXConfig",
    "ConnectionState",
    "MessageType",
    "QueuePolicy",
    "ConnectionMetrics",
    "HealthStatus",
    "APIResponse",
    
    # Exceptions
    "HTXError",
    "ConnectionError",
    "AuthenticationError", 
    "APIError",
    "ValidationError",
    "RateLimitError",
    
    # Utilities
    "validate_symbol",
    "validate_interval",
    "normalize_symbol",
]

# Package metadata
__author__ = "Smart Trader Team"
__email__ = "<EMAIL>"
__description__ = "Enhanced HTX Futures API client with modular architecture"
__url__ = "https://github.com/smarttrader/htx-client"
