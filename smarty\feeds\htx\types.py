"""
HTX Futures API type definitions and data models.
"""

import re
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable

from .constants import SYMBOL_PATTERN, INTERVAL_PATTERN


class ConnectionState(Enum):
    """WebSocket connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class MessageType(Enum):
    """WebSocket message types."""
    PING = "ping"
    PONG = "pong"
    SUBSCRIBE = "sub"
    UNSUBSCRIBE = "unsub"
    AUTH = "auth"
    NOTIFY = "notify"
    DATA = "data"


class QueuePolicy(Enum):
    """Queue overflow policies."""
    DROP_NEW = "drop_new"
    DROP_OLD = "drop_old"
    BLOCK = "block"


@dataclass
class HTXConfig:
    """HTX client configuration."""
    api_key: str = ""
    api_secret: str = ""
    testnet: bool = False
    ping_interval: int = 20
    queue_size: int = 1000
    max_reconnect_delay: int = 60
    reconnect_backoff_factor: float = 2.0
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    debug: bool = False
    verbosity: str = "INFO"
    queue_policy: QueuePolicy = QueuePolicy.DROP_NEW
    enable_metrics: bool = True
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.ping_interval < 5:
            raise ValueError("ping_interval must be at least 5 seconds")
        if self.queue_size < 10:
            raise ValueError("queue_size must be at least 10")
        if self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")


@dataclass
class ConnectionMetrics:
    """Connection performance metrics."""
    connected_at: Optional[datetime] = None
    last_ping_sent: Optional[datetime] = None
    last_pong_received: Optional[datetime] = None
    reconnect_count: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    messages_dropped: int = 0
    avg_latency: float = 0.0
    last_error: Optional[str] = None
    error_count: int = 0


@dataclass
class SubscriptionInfo:
    """WebSocket subscription information."""
    channel: str
    symbol: str
    subscribed_at: datetime
    message_count: int = 0
    last_message_at: Optional[datetime] = None
    is_private: bool = False


@dataclass
class APIResponse:
    """Standardized API response wrapper."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    latency: Optional[float] = None


@dataclass
class HealthStatus:
    """System health status."""
    is_healthy: bool
    connection_state: ConnectionState
    last_check: datetime
    issues: List[str] = field(default_factory=list)
    metrics: Optional[ConnectionMetrics] = None


# Type aliases for better readability
PublisherFunction = Callable[[str, float, dict], None]
AsyncPublisherFunction = Callable[[str, float, dict], Awaitable[None]]
MessageHandler = Callable[[Dict[str, Any]], Awaitable[None]]
ErrorHandler = Callable[[Exception], Awaitable[None]]


def validate_symbol(symbol: str) -> bool:
    """
    Validate trading symbol format.
    
    Args:
        symbol: Trading symbol to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(SYMBOL_PATTERN, symbol))


def validate_interval(interval: str) -> bool:
    """
    Validate kline interval format.
    
    Args:
        interval: Interval to validate
        
    Returns:
        True if valid, False otherwise
    """
    return bool(re.match(INTERVAL_PATTERN, interval))


def normalize_symbol(symbol: str) -> str:
    """
    Normalize symbol to HTX format.
    
    Args:
        symbol: Symbol to normalize
        
    Returns:
        Normalized symbol
    """
    # Convert common formats to HTX format
    symbol = symbol.upper().replace("/", "-").replace("_", "-")
    
    # Ensure USDT suffix for futures
    if not symbol.endswith("-USDT"):
        if symbol.endswith("USDT"):
            symbol = symbol[:-4] + "-USDT"
        else:
            symbol = symbol + "-USDT"
    
    return symbol


class HTXError(Exception):
    """Base exception for HTX client errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.error_code = error_code


class ConnectionError(HTXError):
    """Connection-related errors."""
    pass


class AuthenticationError(HTXError):
    """Authentication-related errors."""
    pass


class APIError(HTXError):
    """API-related errors."""
    pass


class ValidationError(HTXError):
    """Validation-related errors."""
    pass


class RateLimitError(HTXError):
    """Rate limiting errors."""
    pass
