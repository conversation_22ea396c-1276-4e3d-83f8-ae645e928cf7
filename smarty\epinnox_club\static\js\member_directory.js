/**
 * Money Circle Member Directory JavaScript
 * Handles member filtering, searching, connections, and interactive features
 */

class MemberDirectory {
    constructor() {
        this.members = [];
        this.filteredMembers = [];
        this.userConnections = [];
        this.achievements = {};
        this.leaderboards = {};
        this.currentFilters = {
            search: '',
            experience: '',
            specialization: '',
            performance: '',
            sort: 'performance'
        };
        this.currentView = 'grid';
        
        this.init();
    }
    
    init() {
        this.loadData();
        this.bindEvents();
        this.applyFilters();
    }
    
    loadData() {
        if (window.directoryData) {
            this.members = window.directoryData.members || [];
            this.userConnections = window.directoryData.userConnections || [];
            this.achievements = window.directoryData.achievements || {};
            this.leaderboards = window.directoryData.leaderboards || {};
            this.filteredMembers = [...this.members];
        }
    }
    
    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('memberSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentFilters.search = e.target.value.toLowerCase();
                this.applyFilters();
            });
        }
        
        // Filter controls
        const filterSelects = ['experienceFilter', 'specializationFilter', 'performanceFilter', 'sortFilter'];
        filterSelects.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
        
        // View toggles
        document.querySelectorAll('.view-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                document.querySelectorAll('.view-toggle').forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                this.switchView(e.target.dataset.view);
            });
        });
        
        // Message form
        const messageForm = document.getElementById('messageForm');
        if (messageForm) {
            messageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendMessage();
            });
        }
    }
    
    applyFilters() {
        // Update filter values
        this.currentFilters.experience = document.getElementById('experienceFilter')?.value || '';
        this.currentFilters.specialization = document.getElementById('specializationFilter')?.value || '';
        this.currentFilters.performance = document.getElementById('performanceFilter')?.value || '';
        this.currentFilters.sort = document.getElementById('sortFilter')?.value || 'performance';
        
        // Filter members
        this.filteredMembers = this.members.filter(member => {
            // Search filter
            if (this.currentFilters.search) {
                const searchTerm = this.currentFilters.search;
                const searchableText = `${member.display_name} ${member.bio} ${member.location} ${member.specialization} ${member.experience_level}`.toLowerCase();
                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }
            
            // Experience filter
            if (this.currentFilters.experience && member.experience_level !== this.currentFilters.experience) {
                return false;
            }
            
            // Specialization filter
            if (this.currentFilters.specialization && member.specialization !== this.currentFilters.specialization) {
                return false;
            }
            
            // Performance filter
            if (this.currentFilters.performance) {
                const performance = member.avg_return;
                const allPerformances = this.members.map(m => m.avg_return).sort((a, b) => b - a);
                const top10Threshold = allPerformances[Math.floor(allPerformances.length * 0.1)];
                const top25Threshold = allPerformances[Math.floor(allPerformances.length * 0.25)];
                
                switch (this.currentFilters.performance) {
                    case 'top_10':
                        if (performance < top10Threshold) return false;
                        break;
                    case 'top_25':
                        if (performance < top25Threshold) return false;
                        break;
                    case 'positive':
                        if (performance <= 0) return false;
                        break;
                }
            }
            
            return true;
        });
        
        // Sort members
        this.sortMembers();
        
        // Update display
        if (this.currentView === 'grid') {
            this.updateMemberGrid();
        }
        this.updateResultsCount();
    }
    
    sortMembers() {
        this.filteredMembers.sort((a, b) => {
            switch (this.currentFilters.sort) {
                case 'performance':
                    return b.avg_return - a.avg_return;
                case 'experience':
                    const expOrder = { 'expert': 4, 'advanced': 3, 'intermediate': 2, 'beginner': 1 };
                    return (expOrder[b.experience_level] || 0) - (expOrder[a.experience_level] || 0);
                case 'connections':
                    return b.followers_count - a.followers_count;
                case 'recent':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'alphabetical':
                    return a.display_name.localeCompare(b.display_name);
                default:
                    return b.avg_return - a.avg_return;
            }
        });
    }
    
    switchView(viewType) {
        this.currentView = viewType;
        
        const memberGridSection = document.getElementById('memberGridSection');
        const leaderboardsSection = document.getElementById('leaderboardsSection');
        
        if (viewType === 'leaderboard') {
            memberGridSection.style.display = 'none';
            leaderboardsSection.style.display = 'block';
        } else {
            memberGridSection.style.display = 'block';
            leaderboardsSection.style.display = 'none';
            
            const grid = document.querySelector('.member-grid');
            if (grid) {
                if (viewType === 'list') {
                    grid.classList.add('list-view');
                } else {
                    grid.classList.remove('list-view');
                }
            }
        }
    }
    
    updateMemberGrid() {
        const grid = document.getElementById('memberGrid');
        if (!grid) return;
        
        if (this.filteredMembers.length === 0) {
            grid.innerHTML = '<div class="empty-state">No members match your filters</div>';
            return;
        }
        
        grid.innerHTML = this.filteredMembers.map(member => this.renderMemberCard(member)).join('');
    }
    
    renderMemberCard(member) {
        const isConnected = this.userConnections.includes(member.id);
        const memberAchievements = this.achievements[member.id] || [];
        const performanceClass = member.avg_return > 0 ? 'positive' : 'negative';
        const expClass = `exp-${member.experience_level}`;
        
        return `
            <div class="member-card" data-member-id="${member.id}">
                <div class="member-card-header">
                    <div class="member-avatar">
                        ${member.display_name.charAt(0).toUpperCase()}
                        ${member.verified ? '<div class="verified-badge">✓</div>' : ''}
                    </div>
                    <div class="member-actions">
                        <button class="action-btn" onclick="directory.openMessageModal(${member.id})" title="Send message">💬</button>
                        <button class="action-btn ${isConnected ? 'connected' : ''}" onclick="directory.toggleConnection(${member.id})" title="${isConnected ? 'Disconnect' : 'Connect'}">${isConnected ? '🤝' : '👋'}</button>
                    </div>
                </div>
                
                <div class="member-info">
                    <h4 class="member-name">${member.display_name}</h4>
                    <div class="member-meta">
                        <span class="experience-badge ${expClass}">${member.experience_level.charAt(0).toUpperCase() + member.experience_level.slice(1)}</span>
                        <span class="specialization-badge">${member.specialization.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                    </div>
                    ${member.location ? `<div class="member-location">📍 ${member.location}</div>` : ''}
                </div>
                
                <div class="member-stats">
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Performance</span>
                            <span class="stat-value ${performanceClass}">${member.avg_return >= 0 ? '+' : ''}${member.avg_return.toFixed(2)}%</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Win Rate</span>
                            <span class="stat-value">${member.avg_win_rate.toFixed(1)}%</span>
                        </div>
                    </div>
                    <div class="stat-row">
                        <div class="stat">
                            <span class="stat-label">Reputation</span>
                            <span class="stat-value">${member.reputation_score}/100</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Followers</span>
                            <span class="stat-value">${member.followers_count}</span>
                        </div>
                    </div>
                </div>
                
                ${member.bio ? `<div class="member-bio">${member.bio.length > 80 ? member.bio.substring(0, 80) + '...' : member.bio}</div>` : ''}
                
                <div class="member-achievements">
                    ${this.renderMemberAchievements(memberAchievements.slice(0, 3))}
                </div>
                
                <div class="member-footer">
                    <div class="member-activity">
                        <span class="activity-label">Last active:</span>
                        <span class="activity-time">${this.formatTimeAgo(member.last_activity)}</span>
                    </div>
                    <div class="member-strategies">${member.strategies_count} strategies</div>
                </div>
                
                <div class="member-card-actions">
                    <button class="btn-view" onclick="directory.viewMemberProfile(${member.id})">View Profile</button>
                    <button class="btn-connect ${isConnected ? 'active' : ''}" onclick="directory.toggleConnection(${member.id})">
                        ${isConnected ? 'Disconnect' : 'Connect'}
                    </button>
                </div>
            </div>
        `;
    }
    
    renderMemberAchievements(achievements) {
        if (!achievements || achievements.length === 0) {
            return '<div class="no-achievements">No achievements yet</div>';
        }
        
        return achievements.map(achievement => 
            `<div class="achievement-badge" title="${achievement.title}">${achievement.icon}</div>`
        ).join('');
    }
    
    updateResultsCount() {
        const countElement = document.getElementById('memberResultsCount');
        if (countElement) {
            countElement.textContent = `${this.filteredMembers.length} members found`;
        }
    }
    
    async toggleConnection(memberId) {
        try {
            const isCurrentlyConnected = this.userConnections.includes(memberId);
            const action = isCurrentlyConnected ? 'disconnect' : 'connect';
            
            const response = await fetch(`/api/members/${memberId}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                if (isCurrentlyConnected) {
                    this.userConnections = this.userConnections.filter(id => id !== memberId);
                } else {
                    this.userConnections.push(memberId);
                }
                
                // Update member follower count
                const member = this.members.find(m => m.id === memberId);
                if (member) {
                    member.followers_count += isCurrentlyConnected ? -1 : 1;
                }
                
                this.updateMemberGrid();
                this.showNotification(`${action.charAt(0).toUpperCase() + action.slice(1)}ed successfully!`, 'success');
            } else {
                throw new Error(`Failed to ${action}`);
            }
        } catch (error) {
            console.error('Error toggling connection:', error);
            this.showNotification(`Failed to ${action}`, 'error');
        }
    }
    
    async viewMemberProfile(memberId) {
        try {
            const response = await fetch(`/api/members/${memberId}/profile`);
            if (response.ok) {
                const data = await response.json();
                this.showMemberProfileModal(data);
            } else {
                throw new Error('Failed to load member profile');
            }
        } catch (error) {
            console.error('Error loading member profile:', error);
            this.showNotification('Failed to load member profile', 'error');
        }
    }
    
    showMemberProfileModal(memberData) {
        const modal = document.getElementById('memberProfileModal');
        const content = document.getElementById('memberProfileContent');
        const title = document.getElementById('modalMemberName');
        
        if (modal && content && title) {
            title.textContent = memberData.display_name;
            content.innerHTML = this.renderMemberProfileDetails(memberData);
            modal.style.display = 'block';
        }
    }
    
    renderMemberProfileDetails(member) {
        return `
            <div class="member-profile-details">
                <div class="profile-header">
                    <div class="profile-avatar">
                        ${member.display_name.charAt(0).toUpperCase()}
                        ${member.verified ? '<div class="verified-badge">✓</div>' : ''}
                    </div>
                    <div class="profile-info">
                        <h3>${member.display_name}</h3>
                        <p class="profile-bio">${member.bio || 'No bio available'}</p>
                        <div class="profile-meta">
                            <span>📍 ${member.location || 'Location not specified'}</span>
                            <span>🎯 ${member.specialization.replace('_', ' ')}</span>
                            <span>⭐ ${member.experience_level} level</span>
                        </div>
                    </div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-grid">
                        <div class="stat-item">
                            <span class="stat-value">${member.avg_return.toFixed(2)}%</span>
                            <span class="stat-label">Avg Return</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${member.avg_win_rate.toFixed(1)}%</span>
                            <span class="stat-label">Win Rate</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${member.reputation_score}/100</span>
                            <span class="stat-label">Reputation</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${member.followers_count}</span>
                            <span class="stat-label">Followers</span>
                        </div>
                    </div>
                </div>
                
                <div class="profile-achievements">
                    <h4>Achievements</h4>
                    <div class="achievements-grid">
                        ${this.renderAllAchievements(this.achievements[member.id] || [])}
                    </div>
                </div>
                
                <div class="profile-actions">
                    <button class="btn-primary" onclick="directory.openMessageModal(${member.id})">Send Message</button>
                    <button class="btn-secondary" onclick="directory.toggleConnection(${member.id})">
                        ${this.userConnections.includes(member.id) ? 'Disconnect' : 'Connect'}
                    </button>
                </div>
            </div>
        `;
    }
    
    renderAllAchievements(achievements) {
        if (!achievements || achievements.length === 0) {
            return '<div class="no-achievements">No achievements earned yet</div>';
        }
        
        return achievements.map(achievement => `
            <div class="achievement-item">
                <div class="achievement-icon">${achievement.icon}</div>
                <div class="achievement-title">${achievement.title}</div>
            </div>
        `).join('');
    }
    
    openMessageModal(memberId) {
        const member = this.members.find(m => m.id === memberId);
        if (!member) return;
        
        const modal = document.getElementById('messageModal');
        if (modal) {
            // Store the target member ID
            modal.dataset.targetMemberId = memberId;
            
            // Clear form
            document.getElementById('messageSubject').value = '';
            document.getElementById('messageContent').value = '';
            
            modal.style.display = 'block';
        }
    }
    
    async sendMessage() {
        const modal = document.getElementById('messageModal');
        const memberId = modal.dataset.targetMemberId;
        const subject = document.getElementById('messageSubject').value;
        const content = document.getElementById('messageContent').value;
        
        if (!subject || !content) {
            this.showNotification('Please fill in all fields', 'warning');
            return;
        }
        
        try {
            const response = await fetch(`/api/members/${memberId}/message`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    subject: subject,
                    content: content
                })
            });
            
            if (response.ok) {
                this.showNotification('Message sent successfully!', 'success');
                this.closeMessageModal();
            } else {
                throw new Error('Failed to send message');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.showNotification('Failed to send message', 'error');
        }
    }
    
    closeMessageModal() {
        const modal = document.getElementById('messageModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    formatTimeAgo(timestamp) {
        if (timestamp === '1970-01-01') return 'Never';
        
        const now = new Date();
        const date = new Date(timestamp);
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        const months = Math.floor(diff / 2592000000);
        
        if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global functions for onclick handlers
function searchMembers() {
    if (window.directory) {
        window.directory.applyFilters();
    }
}

function applyMemberFilters() {
    if (window.directory) {
        window.directory.applyFilters();
    }
}

function viewMemberProfile(memberId) {
    if (window.directory) {
        window.directory.viewMemberProfile(memberId);
    }
}

function sendMessage(memberId) {
    if (window.directory) {
        window.directory.openMessageModal(memberId);
    }
}

function toggleConnection(memberId) {
    if (window.directory) {
        window.directory.toggleConnection(memberId);
    }
}

function closeMemberModal() {
    const modal = document.getElementById('memberProfileModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function closeMessageModal() {
    if (window.directory) {
        window.directory.closeMessageModal();
    }
}

// Initialize directory when DOM is loaded
function initializeMemberDirectory() {
    window.directory = new MemberDirectory();
}
