2025-05-29 13:56:28,413 - strategy.smart_model_integrated - INFO - [info:88] - Initialized message bus: SQLiteBus
2025-05-29 13:56:28,413 - strategy.smart_model_integrated - INFO - [info:88] - Set HTX client simulation mode: True
2025-05-29 13:56:28,414 - strategy.smart_model_integrated - INFO - [info:88] - Set publisher for HTX client
2025-05-29 13:56:28,416 - strategy.smart_model_integrated - INFO - [info:88] - Set publisher for Multi-Exchange client
2025-05-29 13:56:28,416 - strategy.smart_model_integrated - INFO - [info:88] - Set publisher for Binance fallback client
2025-05-29 13:56:33,908 - strategy.smart_model_integrated - WARNING - [warning:92] - SignalStar client not initialized, social sentiment model disabled
2025-05-29 13:56:33,929 - strategy.smart_model_integrated - INFO - [info:88] - Starting orchestrator...
2025-05-29 13:56:36,740 - strategy.smart_model_integrated - INFO - [info:88] - Loaded 60 historical funding rates for BTC-USDT
2025-05-29 13:56:36,743 - strategy.smart_model_integrated - INFO - [info:88] - Position manager started
2025-05-29 13:56:36,743 - strategy.smart_model_integrated - INFO - [info:88] - Starting event loop
2025-05-29 13:56:36,744 - strategy.smart_model_integrated - INFO - [info:88] - Started bus maintenance task
2025-05-29 13:56:36,744 - strategy.smart_model_integrated - INFO - [info:88] - Starting account information update task
2025-05-29 13:56:36,744 - strategy.smart_model_integrated - ERROR - [error:96] - Error updating account information: REST client not initialized
2025-05-29 13:56:36,745 - strategy.smart_model_integrated - INFO - [info:88] - Starting health check task
2025-05-29 13:56:36,745 - strategy.smart_model_integrated - INFO - [info:88] - Starting position monitoring task
2025-05-29 13:56:36,745 - strategy.smart_model_integrated - INFO - [info:88] - Starting funding rate fetching task
2025-05-29 13:56:36,748 - strategy.smart_model_integrated - INFO - [info:88] - Starting open interest fetching task
2025-05-29 13:56:36,754 - strategy.smart_model_integrated - INFO - [info:88] - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
