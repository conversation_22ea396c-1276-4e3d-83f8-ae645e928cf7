#!/usr/bin/env python3
"""
Smart Trader Main - Complete Trading System Orchestrator

This is the main entry point for the Smart Trader system.
It orchestrates all components: data feeds, strategy, dashboard, and trading.
"""

import asyncio
import logging
import signal
import sys
import os
import subprocess
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import yaml
import json

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dataframe_smart_strategy import DataFrameSmartStrategy
from pipeline.databus import SQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SmartTraderMain:
    """Main orchestrator for the complete Smart Trader system."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the Smart Trader main system."""
        self.config_path = config_path
        self.config = self._load_config()
        self.processes = {}
        self.bus = None
        self.strategy = None
        self.running = False

        # System components
        self.data_producer_process = None
        self.dashboard_process = None
        self.strategy_active = False

        # Trading settings
        self.trading_enabled = self.config.get("trading", {}).get("enabled", False)
        self.simulation_mode = self.config.get("trading", {}).get("simulation_mode", True)
        self.symbols = self.config.get("trading", {}).get("symbols", ["BTC-USDT"])

        # Signal tracking
        self.signal_count = 0
        self.last_signal_time = None
        self.system_start_time = None

        logger.info("🎯 Smart Trader Main System Initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"❌ Failed to load config: {e}")
            return {}

    async def start_system(self):
        """Start the complete Smart Trader system."""
        logger.info("🚀 Starting Complete Smart Trader System...")
        logger.info("=" * 70)
        logger.info("🎯 SMART TRADER - AI-POWERED LIVE TRADING SYSTEM")
        logger.info("=" * 70)

        self.system_start_time = datetime.now()

        try:
            # Set up signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            # Step 1: Start Data Producer
            await self._start_data_producer()

            # Step 2: Start Dashboard
            await self._start_dashboard()

            # Step 3: Initialize Strategy System
            await self._initialize_strategy()

            # Step 4: Start Main Loop
            self.running = True
            await self._main_loop()

        except Exception as e:
            logger.error(f"❌ Failed to start system: {e}")
            await self.shutdown()
            raise

    async def _start_data_producer(self):
        """Start the market data producer or use existing one."""
        logger.info("📊 Checking Market Data Producer...")

        try:
            # Check if data is already flowing (indicating producer is running)
            bus_path = self.config.get("message_bus", {}).get("path", "data/bus.db")
            test_bus = SQLiteBus(path=bus_path, poll_interval=0.1)

            # Check for recent data
            cursor = test_bus.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (time.time() - 60,))
            recent_count = cursor.fetchone()[0]
            test_bus.close()

            if recent_count > 10:
                logger.info("✅ Market Data Producer already running (data flowing)")
                return

            # Start data producer as subprocess
            logger.info("📊 Starting Market Data Producer...")
            self.data_producer_process = subprocess.Popen(
                [sys.executable, "feeds/htx_data_producer.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a moment for it to start
            await asyncio.sleep(3)

            # Check if it's running
            if self.data_producer_process.poll() is None:
                logger.info("✅ Market Data Producer started successfully")
                self.processes['data_producer'] = self.data_producer_process
            else:
                raise Exception("Data producer failed to start")

        except Exception as e:
            logger.error(f"❌ Failed to start data producer: {e}")
            # Don't raise - continue without starting new producer
            logger.warning("⚠️  Continuing with existing data flow")

    async def _start_dashboard(self):
        """Start the web dashboard or use existing one."""
        logger.info("🌐 Checking Web Dashboard...")

        try:
            # Check if dashboard is already running
            import aiohttp
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get('http://localhost:8082/api/market-data', timeout=3) as response:
                        if response.status == 200:
                            logger.info("✅ Web Dashboard already running at http://localhost:8082")
                            return
            except:
                pass  # Dashboard not running, start it

            # Start dashboard as subprocess
            logger.info("🌐 Starting Live Dashboard...")
            self.dashboard_process = subprocess.Popen(
                [sys.executable, "live_dashboard.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a moment for it to start
            await asyncio.sleep(5)

            # Check if it's running
            if self.dashboard_process.poll() is None:
                logger.info("✅ Web Dashboard started at http://localhost:8082")
                self.processes['dashboard'] = self.dashboard_process
            else:
                raise Exception("Dashboard failed to start")

        except Exception as e:
            logger.error(f"❌ Failed to start dashboard: {e}")
            # Don't raise - continue without dashboard
            logger.warning("⚠️  Continuing without dashboard startup")

    async def _initialize_strategy(self):
        """Initialize the smart trading strategy."""
        logger.info("🧠 Initializing Smart Trading Strategy...")

        try:
            # Connect to SQLite bus
            bus_path = self.config.get("message_bus", {}).get("path", "data/bus.db")
            self.bus = SQLiteBus(path=bus_path, poll_interval=0.5)
            logger.info(f"✅ Connected to SQLite bus at {bus_path}")

            # Initialize DataFrame strategy
            strategy_config = self.config.get("strategy", {})
            self.strategy = DataFrameSmartStrategy(strategy_config)
            logger.info("✅ Smart Strategy initialized")

            self.strategy_active = True

        except Exception as e:
            logger.error(f"❌ Failed to initialize strategy: {e}")
            raise

    async def _main_loop(self):
        """Main system loop - coordinates all components."""
        logger.info("🔄 Starting Main System Loop...")
        logger.info(f"📊 Monitoring symbols: {self.symbols}")
        logger.info(f"🔧 Trading enabled: {self.trading_enabled}")
        logger.info(f"⚡ Simulation mode: {self.simulation_mode}")
        logger.info("=" * 70)

        signal_interval = 30  # Generate signals every 30 seconds
        status_interval = 300  # Status update every 5 minutes
        last_status_time = time.time()

        while self.running:
            try:
                current_time = time.time()

                # Generate trading signals
                if self.strategy_active:
                    for symbol in self.symbols:
                        signal = await self._generate_signal(symbol)
                        if signal:
                            await self._process_signal(signal)

                # Periodic status update
                if current_time - last_status_time >= status_interval:
                    await self._log_system_status()
                    last_status_time = current_time

                # Wait before next iteration
                await asyncio.sleep(signal_interval)

            except Exception as e:
                logger.error(f"❌ Error in main loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _generate_signal(self, symbol: str):
        """Generate a trading signal for the given symbol."""
        try:
            if not self.strategy:
                return None

            signal = self.strategy.generate_signal(symbol)

            if signal:
                self.signal_count += 1
                self.last_signal_time = datetime.now()

                confidence = signal.metadata.get("confidence", signal.score)
                price = signal.metadata.get("price", 0)

                logger.info(f"🎯 Signal #{self.signal_count}: {signal.action} {symbol}")
                logger.info(f"   💰 Price: ${price:,.2f}")
                logger.info(f"   📊 Score: {signal.score:.3f}, Confidence: {confidence:.3f}")
                logger.info(f"   📝 Rationale: {signal.rationale}")

                return signal
            else:
                logger.debug(f"No signal generated for {symbol}")
                return None

        except Exception as e:
            logger.error(f"❌ Error generating signal for {symbol}: {e}")
            return None

    async def _process_signal(self, signal):
        """Process and execute a trading signal."""
        try:
            confidence = signal.metadata.get("confidence", signal.score)
            price = signal.metadata.get("price", 0)

            # Create signal data for dashboard
            signal_data = {
                "symbol": signal.symbol,
                "action": signal.action.value if hasattr(signal.action, 'value') else str(signal.action),
                "score": signal.score,
                "confidence": confidence,
                "price": price,
                "rationale": signal.rationale,
                "timestamp": datetime.now().isoformat(),
                "source": "smart_trader_main",
                "signal_id": self.signal_count
            }

            # Publish signal to dashboard
            if self.bus:
                self.bus.publish("signals.trading", datetime.now().timestamp(), signal_data)
                logger.info("📡 Signal published to dashboard")

            # Handle trading execution
            if self.trading_enabled:
                if self.simulation_mode:
                    logger.info(f"📊 SIMULATION: Would execute {signal.action} {signal.symbol}")
                    logger.info(f"   💰 Simulation mode - no real trades executed")
                else:
                    logger.warning(f"🚨 REAL TRADING SIGNAL: {signal.action} {signal.symbol}")
                    logger.warning(f"⚠️  Real trading execution would happen here!")
                    # TODO: Implement real trading execution
            else:
                logger.info(f"📊 Trading disabled - signal logged only")

        except Exception as e:
            logger.error(f"❌ Error processing signal: {e}")

    async def _log_system_status(self):
        """Log comprehensive system status."""
        uptime = datetime.now() - self.system_start_time if self.system_start_time else None

        logger.info("=" * 70)
        logger.info("📊 SMART TRADER SYSTEM STATUS")
        logger.info("=" * 70)
        logger.info(f"⏱️  System Uptime: {uptime}")
        logger.info(f"🎯 Total Signals Generated: {self.signal_count}")
        logger.info(f"📡 Last Signal: {self.last_signal_time}")
        logger.info(f"🔧 Trading Enabled: {self.trading_enabled}")
        logger.info(f"⚡ Simulation Mode: {self.simulation_mode}")

        # Check process status
        for name, process in self.processes.items():
            if process and process.poll() is None:
                logger.info(f"✅ {name.title()}: Running")
            else:
                logger.warning(f"⚠️  {name.title()}: Not running")

        logger.info("🌐 Dashboard: http://localhost:8082")
        logger.info("=" * 70)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.running = False

    async def shutdown(self):
        """Shutdown the complete system."""
        logger.info("🛑 Shutting down Smart Trader System...")

        self.running = False

        # Close strategy
        if self.strategy:
            self.strategy.close()
            logger.info("✅ Smart strategy closed")

        # Close bus connection
        if self.bus:
            self.bus.close()
            logger.info("✅ SQLite bus connection closed")

        # Terminate subprocesses
        for name, process in self.processes.items():
            if process and process.poll() is None:
                logger.info(f"🛑 Terminating {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                    logger.info(f"✅ {name} terminated")
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️  Force killing {name}...")
                    process.kill()

        # Final statistics
        uptime = datetime.now() - self.system_start_time if self.system_start_time else None
        logger.info("=" * 70)
        logger.info("📊 FINAL SYSTEM STATISTICS")
        logger.info("=" * 70)
        logger.info(f"⏱️  Total Uptime: {uptime}")
        logger.info(f"🎯 Total Signals Generated: {self.signal_count}")
        logger.info(f"📡 Last Signal: {self.last_signal_time}")
        logger.info("🏁 Smart Trader System shutdown complete")


async def main():
    """Main entry point."""
    print("🎯 Smart Trader - AI-Powered Live Trading System")
    print("=" * 70)
    print("🚀 Starting complete trading system...")
    print("🌐 Dashboard will be available at: http://localhost:8082")
    print("📊 Live data feeds will start automatically")
    print("🧠 AI strategy will begin analyzing market data")
    print("=" * 70)

    # Create and start the main system
    smart_trader = SmartTraderMain()

    try:
        await smart_trader.start_system()
    except KeyboardInterrupt:
        logger.info("📡 Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ System error: {e}")
    finally:
        await smart_trader.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
