# SQLiteBus Migration Guide

This guide provides step-by-step instructions for migrating the smart-trader system to use the SQLiteBus as the central message pipeline.

## Overview

The SQLiteBus is a durable, SQLite-backed message bus that provides a publish-subscribe pattern for communication between different components of the system. It replaces the direct client-to-orchestrator communication pattern with a more flexible and resilient messaging architecture.

## Migration Steps

### 1. Update Configuration

Add SQLiteBus configuration to your `config.yaml` file:

```yaml
pipeline:
  bus: "sqlite"  # Options: sqlite, in_memory
  sqlite:
    path: "data/bus.db"  # Path to the SQLite database file
    poll_interval: 0.5   # How often to check for new messages (seconds)
  cleanup_interval_hours: 24  # How often to run cleanup (hours)
  message_retention_days: 7   # How long to keep messages (days)
```

### 2. Update Exchange Clients

#### 2.1. HTXFuturesClient

Add a `set_publisher` method to the HTXFuturesClient:

```python
def set_publisher(self, publish_fn: Callable[[str, float, dict], None]) -> None:
    """
    Set the message bus publisher function.
    
    Args:
        publish_fn: Function to publish messages to the bus
                    Takes (stream_name, timestamp, payload)
    """
    self._publish = publish_fn
    logger.info("Message bus publisher set for HTX Futures client")
```

Update the message handlers to publish to the bus:

```python
def _on_kline(self, data):
    # Process the kline data
    # ...
    
    # Publish to the bus if publisher is set
    if self._publish:
        ts = data["timestamp"] / 1000.0
        self._publish("htx.kline", ts, {
            "symbol": data["symbol"],
            "interval": data["interval"],
            "open": data["open"],
            "high": data["high"],
            "low": data["low"],
            "close": data["close"],
            "volume": data["volume"]
        })
```

#### 2.2. SignalStarClient

Add a `set_publisher` method to the SignalStarClient:

```python
def set_publisher(self, publish_fn: Callable[[str, float, dict], None]) -> None:
    """
    Set the message bus publisher function.
    
    Args:
        publish_fn: Function to publish messages to the bus
                    Takes (stream_name, timestamp, payload)
    """
    self._publish = publish_fn
    logger.info("Message bus publisher set for SignalStar client")
```

Update the `get_latest_sentiment` method to publish to the bus:

```python
async def get_latest_sentiment(self, symbol: str) -> Optional[float]:
    # ...
    
    # Publish to the bus if publisher is set
    if self._publish:
        self._publish("signalstar.sentiment", time.time(), {
            "symbol": symbol,
            "sentiment": sentiment_value,
            "timestamp": datetime.now().isoformat()
        })
    
    return sentiment_value
```

### 3. Update Models

Add a `set_publisher` method to each model:

```python
def set_publisher(self, publish_fn: Callable[[str, float, dict], None]) -> None:
    """
    Set the message bus publisher function.
    
    Args:
        publish_fn: Function to publish messages to the bus
                    Takes (stream_name, timestamp, payload)
    """
    self._publish = publish_fn
    logger.info(f"Message bus publisher set for {self.__class__.__name__}")
```

Update the `predict` method to publish to the bus:

```python
async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
    # Generate prediction
    # ...
    
    # Publish to the bus if publisher is set
    if self._publish:
        self._publish(f"model.{self.__class__.__name__.lower()}", time.time(), {
            "symbol": features["symbol"],
            "prediction": prediction,
            "timestamp": datetime.now().isoformat()
        })
    
    return prediction
```

### 4. Update Orchestrator

Update the orchestrator to use the SQLiteBus:

```python
def __init__(self, config: Dict[str, Any]):
    # ...
    
    # Initialize message bus
    self.bus = create_bus(config)
    logger.info(f"Initialized message bus: {type(self.bus).__name__}")
    
    # Initialize components
    self._init_components()
    
    # Set up bus subscriptions
    self._setup_bus_subscriptions()

def _setup_bus_subscriptions(self) -> None:
    """Set up subscriptions to message bus topics."""
    # Market data subscriptions
    self.bus.subscribe("htx.kline", self._on_kline_message)
    self.bus.subscribe("htx.trade", self._on_trade_message)
    self.bus.subscribe("htx.orderbook", self._on_orderbook_message)
    
    # Private data subscriptions
    self.bus.subscribe("htx.position", self._on_position_message)
    
    # External data subscriptions
    self.bus.subscribe("htx.funding", self._on_funding_message)
    self.bus.subscribe("htx.open_interest", self._on_open_interest_message)
    
    # Social sentiment subscription
    self.bus.subscribe("signalstar.sentiment", self._on_sentiment_message)
    
    # Model predictions
    self.bus.subscribe("model.rsi", self._on_rsi_prediction)
    self.bus.subscribe("model.orderflow", self._on_orderflow_prediction)
    # ... other model subscriptions
    
    logger.info("Set up message bus subscriptions")
```

Add message handlers for each subscription:

```python
def _on_kline_message(self, ts: float, payload: dict) -> None:
    """Handle a kline message from the bus."""
    symbol = payload.get("symbol")
    if not symbol:
        return
    
    # Process the kline data
    asyncio.create_task(self._handle_kline(
        Kline(
            symbol=symbol,
            interval=payload.get("interval", "1min"),
            open=payload.get("open", 0.0),
            high=payload.get("high", 0.0),
            low=payload.get("low", 0.0),
            close=payload.get("close", 0.0),
            volume=payload.get("volume", 0.0),
            timestamp=datetime.fromtimestamp(ts)
        )
    ))
```

Add a bus maintenance task:

```python
async def _bus_maintenance(self) -> None:
    """Perform periodic maintenance on the message bus."""
    # Only SQLiteBus needs maintenance
    if not isinstance(self.bus, SQLiteBus):
        return
    
    cleanup_interval = self.config.get("pipeline", {}).get("cleanup_interval_hours", 24)
    retention_days = self.config.get("pipeline", {}).get("message_retention_days", 7)
    
    while self.running:
        try:
            # Sleep first to avoid immediate cleanup on startup
            await asyncio.sleep(cleanup_interval * 3600)
            
            # Clean up old messages
            deleted = self.bus.cleanup_old_messages(max_age_days=retention_days)
            logger.info(f"Bus maintenance: deleted {deleted} old messages")
            
        except Exception as e:
            logger.error(f"Error in bus maintenance: {e}")
            await asyncio.sleep(60)  # Sleep for a minute on error
```

Update the `start` method to start the bus maintenance task:

```python
async def start(self) -> None:
    # ...
    
    # Create tasks
    feature_task = asyncio.create_task(self._update_features())
    signal_task = asyncio.create_task(self._generate_signals())
    bus_maintenance_task = asyncio.create_task(self._bus_maintenance())
    
    # Collect all tasks
    tasks = [feature_task, signal_task, bus_maintenance_task]
    
    # ...
```

Update the `stop` method to close the bus:

```python
async def stop(self) -> None:
    # ...
    
    # Close message bus
    self.bus.close()
    
    # ...
```

### 5. Update Main Entry Point

Update the main entry point to use the SQLiteBus:

```python
async def main():
    # ...
    
    # Create orchestrator with SQLiteBus
    orchestrator = Orchestrator(config)
    
    # ...
```

## Testing

After making these changes, run the tests to ensure everything is working correctly:

```bash
python -m pytest tests/test_sqlitebus.py -v
```

## Rollback Plan

If you encounter issues with the SQLiteBus implementation, you can roll back to the previous communication pattern by:

1. Removing the SQLiteBus configuration from `config.yaml`
2. Reverting the changes to the orchestrator
3. Reverting the changes to the clients and models

## Conclusion

By following this migration guide, you will have successfully integrated the SQLiteBus into your smart-trader system, providing a more flexible, durable, and resilient messaging architecture.

For more information, see the [SQLiteBus README](pipeline/README.md).
