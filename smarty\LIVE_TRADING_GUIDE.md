# 🎯 Live Trading Dashboard Guide

## 🚀 **SYSTEM OVERVIEW**

Your Smart Trader system is now fully operational with:
- ✅ **3 Working Strategies** with live data processing
- ✅ **Enhanced Dashboard** with strategy management
- ✅ **Live Market Data** from HTX/Binance
- ✅ **Trading Configuration** optimized for $100 account
- ✅ **Risk Management** with TP/SL and position sizing

---

## 🎯 **QUICK START**

### 1. **Start the System**
```bash
python start_live_trading_dashboard.py
```

### 2. **Access Dashboard**
- **URL**: http://localhost:8082
- **Login**: epinnox / securepass123

### 3. **Select & Start Strategy**
- Choose from 3 working strategies in the dropdown
- Click "Start Strategy" to begin live trading
- Monitor real-time data and signals

---

## 📊 **AVAILABLE STRATEGIES**

### 🧠 **Smart Model Integrated** (Recommended)
- **Command**: `python orchestrator.py --debug`
- **Features**: Full LLM integration, all models, comprehensive analysis
- **Allocation**: 40% of capital ($40)
- **Best For**: Conservative, high-confidence trades

### ⚡ **Smart Strategy Only** (Fast)
- **Command**: `python run_smart_strategy_live.py`
- **Features**: Technical analysis, lightweight, quick signals
- **Allocation**: 35% of capital ($35)
- **Best For**: Active trading, multiple positions

### 📈 **Order Flow Analysis** (Specialized)
- **Command**: `python live_dataframe_strategy_runner.py`
- **Features**: DataFrame analysis, order flow patterns
- **Allocation**: 25% of capital ($25)
- **Best For**: Market microstructure analysis

---

## 💰 **TRADING CONFIGURATION**

### **Account Settings**
- **Balance**: $100 USD
- **Max Position**: $20 (20% of balance)
- **Min Position**: $5 (5% of balance)
- **Max Daily Loss**: $10 (10% of balance)

### **Risk Management**
- **Take Profit**: 2.0% default
- **Stop Loss**: 1.5% default
- **Trailing Stop**: Enabled (0.5% distance)
- **Max Drawdown**: 15%

### **Position Sizing**
- **Smart Model**: Max 2 positions ($20 each)
- **Smart Strategy**: Max 3 positions ($12 each)
- **Order Flow**: Max 2 positions ($12 each)

---

## 🎮 **DASHBOARD FEATURES**

### **Strategy Control Panel**
- **Strategy Selector**: Choose active strategy
- **Start/Stop Buttons**: Manual control
- **Live Status**: Real-time strategy status
- **Metrics Toggles**: Enable/disable indicators

### **Live Data Monitoring**
- **Market Data**: Real-time price, volume, indicators
- **Order Book**: Live bid/ask levels
- **Recent Trades**: Latest market transactions
- **AI Analysis**: LLM market insights

### **Trading Interface**
- **Active Trades**: Current open positions
- **Trade History**: Past trade performance
- **Performance Stats**: P&L, win rate, metrics
- **Signal Generation**: Live trading signals

---

## 📈 **LIVE DATA SOURCES**

### **Primary**: HTX WebSocket
- Real-time 1-second klines
- Order book updates
- Trade stream
- Funding rates

### **Fallback**: Binance WebSocket
- Automatic failover if HTX blocked
- Same data format and quality
- Seamless switching

### **Symbols Supported**
- BTC-USDT (default)
- ETH-USDT
- BNB-USDT
- ADA-USDT, SOL-USDT, XRP-USDT, DOT-USDT, AVAX-USDT

---

## 🔧 **CONFIGURATION FILES**

### **trading_config_live.yaml**
- Complete trading parameters
- Risk management settings
- Strategy allocations
- Exchange configuration

### **config.yaml**
- System configuration
- Database settings
- Logging configuration
- Model parameters

---

## 🚨 **SAFETY FEATURES**

### **Circuit Breakers**
- Stop after 5 consecutive losses
- 1-hour cooling off period
- Emergency stop at $20 total loss

### **Position Limits**
- Max 80% total exposure
- Correlation limit (0.7)
- Manual override available

### **Monitoring**
- Real-time health checks
- Automatic component restart
- Performance tracking
- Alert system ready

---

## 📊 **PERFORMANCE TRACKING**

### **Real-Time Metrics**
- **P&L**: Live profit/loss tracking
- **Win Rate**: Success percentage
- **Sharpe Ratio**: Risk-adjusted returns
- **Max Drawdown**: Peak-to-trough loss

### **Strategy Analytics**
- **Signal Quality**: Confidence scores
- **Execution Speed**: Order fill times
- **Slippage**: Price impact analysis
- **Market Impact**: Trade size effects

---

## 🎯 **TRADING WORKFLOW**

### **1. System Startup**
```bash
python start_live_trading_dashboard.py
```

### **2. Dashboard Access**
- Open http://localhost:8082
- Login with Epinnox credentials

### **3. Strategy Selection**
- Choose strategy from dropdown
- Review allocation and settings
- Click "Start Strategy"

### **4. Live Monitoring**
- Watch real-time data flow
- Monitor signal generation
- Track trade execution
- Review performance metrics

### **5. Risk Management**
- Monitor position sizes
- Check daily P&L
- Adjust settings if needed
- Use manual override if required

---

## 🔍 **TROUBLESHOOTING**

### **Strategy Won't Start**
- Check if data producer is running
- Verify database connectivity
- Review log files for errors
- Restart system if needed

### **No Data Flow**
- Check internet connection
- Verify WebSocket connections
- Try fallback data source
- Restart data producer

### **Poor Performance**
- Review strategy settings
- Adjust confidence thresholds
- Check market conditions
- Consider strategy switching

---

## 📞 **SUPPORT & MONITORING**

### **Log Files**
- `logs/trading.log` - Main system log
- `logs/smart_model.log` - Smart Model strategy
- `logs/smart_strategy.log` - Smart Strategy Only
- `logs/order_flow.log` - Order Flow strategy

### **Database**
- `data/bus.db` - Live data storage
- Automatic backups every hour
- 30-day data retention

### **Health Monitoring**
- Automatic component restart
- 30-second health checks
- Performance tracking
- Alert system (webhook ready)

---

## 🎉 **SUCCESS METRICS**

Your system is considered successful when:
- ✅ All strategies start without errors
- ✅ Live data flows consistently
- ✅ Signals generate with confidence > 0.6
- ✅ Trades execute within 30 seconds
- ✅ Daily P&L stays within risk limits
- ✅ System uptime > 99%

**Current Status**: 🎯 **FULLY OPERATIONAL** - All 3 strategies tested and working with live data!

---

*Last Updated: 2025-05-29*
*System Version: Live Trading v1.0*
