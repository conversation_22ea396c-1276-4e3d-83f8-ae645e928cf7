"""
Historical data downloader for backtesting.

This module provides functionality for downloading historical data from
various sources for use in backtesting.
"""

import os
import json
import logging
import asyncio
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple

from feeds.htx_futures import HTXFuturesClient

logger = logging.getLogger(__name__)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class HistoricalDataDownloader:
    """
    Historical data downloader for backtesting.
    
    This class provides functionality for downloading historical data from
    various sources for use in backtesting.
    """
    
    def __init__(self, output_dir: str = "data/historical"):
        """
        Initialize the downloader.
        
        Args:
            output_dir: Directory for storing downloaded data
        """
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize clients
        self.htx_client = None
    
    async def initialize(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        """
        Initialize the downloader with API credentials.
        
        Args:
            api_key: API key for exchange
            api_secret: API secret for exchange
            testnet: Whether to use testnet
        """
        # Initialize HTX client
        self.htx_client = HTXFuturesClient(
            api_key=api_key,
            api_secret=api_secret,
            testnet=testnet
        )
        
        logger.info("Historical data downloader initialized")
    
    async def download_htx_klines(
        self,
        symbol: str,
        interval: str = "1h",
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        limit: int = 2000
    ):
        """
        Download historical klines (candlestick data) from HTX.
        
        Args:
            symbol: Trading symbol
            interval: Kline interval (e.g., 1m, 5m, 15m, 1h, 4h, 1d)
            start_time: Start time in YYYY-MM-DD format
            end_time: End time in YYYY-MM-DD format
            limit: Maximum number of klines to download
        """
        if not self.htx_client:
            logger.error("HTX client not initialized")
            return False
        
        # Convert start and end times to timestamps
        start_timestamp = None
        end_timestamp = None
        
        if start_time:
            start_dt = datetime.strptime(start_time, "%Y-%m-%d")
            start_timestamp = int(start_dt.timestamp() * 1000)
        
        if end_time:
            end_dt = datetime.strptime(end_time, "%Y-%m-%d")
            end_timestamp = int(end_dt.timestamp() * 1000)
        else:
            end_timestamp = int(datetime.now().timestamp() * 1000)
        
        # If no start time provided, default to 1 month before end time
        if not start_timestamp:
            start_dt = datetime.fromtimestamp(end_timestamp / 1000) - timedelta(days=30)
            start_timestamp = int(start_dt.timestamp() * 1000)
        
        logger.info(f"Downloading {symbol} {interval} klines from {start_time} to {end_time}")
        
        try:
            # Download klines
            klines = await self.htx_client.get_klines(
                symbol=symbol,
                interval=interval,
                start_time=start_timestamp,
                end_time=end_timestamp,
                limit=limit
            )
            
            if not klines:
                logger.warning(f"No klines returned for {symbol}")
                return False
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'close_time',
                'quote_volume', 'count', 'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Convert numeric columns to float
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            for col in numeric_columns:
                df[col] = df[col].astype(float)
            
            # Sort by timestamp
            df = df.sort_values('timestamp')
            
            # Save to CSV
            file_name = f"{symbol.replace('-', '_')}_{interval}.csv"
            file_path = os.path.join(self.output_dir, file_name)
            df.to_csv(file_path, index=False)
            
            logger.info(f"Downloaded {len(df)} klines for {symbol} {interval} to {file_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error downloading klines for {symbol}: {e}")
            return False
    
    async def download_multiple_symbols(
        self,
        symbols: List[str],
        interval: str = "1h",
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ):
        """
        Download historical data for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            interval: Kline interval
            start_time: Start time in YYYY-MM-DD format
            end_time: End time in YYYY-MM-DD format
        """
        results = {}
        
        for symbol in symbols:
            success = await self.download_htx_klines(
                symbol=symbol,
                interval=interval,
                start_time=start_time,
                end_time=end_time
            )
            
            results[symbol] = success
            
            # Add a small delay to avoid rate limits
            await asyncio.sleep(1)
        
        # Print summary
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"Downloaded data for {success_count}/{len(symbols)} symbols")
        
        return results


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download historical data for backtesting")
    parser.add_argument("--symbols", "-s", nargs="+", required=True, help="Symbols to download data for")
    parser.add_argument("--interval", "-i", default="1h", help="Kline interval (e.g., 1m, 5m, 15m, 1h, 4h, 1d)")
    parser.add_argument("--start", default=None, help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end", default=None, help="End date in YYYY-MM-DD format")
    parser.add_argument("--output", "-o", default="data/historical", help="Output directory")
    parser.add_argument("--api-key", default="", help="API key for exchange")
    parser.add_argument("--api-secret", default="", help="API secret for exchange")
    parser.add_argument("--testnet", action="store_true", help="Use testnet")
    
    args = parser.parse_args()
    
    # Create downloader
    downloader = HistoricalDataDownloader(output_dir=args.output)
    
    # Initialize downloader
    await downloader.initialize(
        api_key=args.api_key,
        api_secret=args.api_secret,
        testnet=args.testnet
    )
    
    # Download data
    await downloader.download_multiple_symbols(
        symbols=args.symbols,
        interval=args.interval,
        start_time=args.start,
        end_time=args.end
    )


if __name__ == "__main__":
    asyncio.run(main())
