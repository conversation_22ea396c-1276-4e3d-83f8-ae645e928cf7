#!/usr/bin/env python3
"""
Test SQLite Bus Connection

This script tests the connection between the web dashboard and the Smart-Trader SQLite bus
to verify that real data is flowing properly.
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
from bus_reader import SQLiteBusReader, get_real_market_data, get_real_market_summary

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_sqlite_bus_exists():
    """Test if the SQLite bus database exists."""
    bus_path = "data/smart_trader_bus.db"
    
    print(f"\n🔍 Testing SQLite Bus Database...")
    print(f"📁 Looking for: {bus_path}")
    
    if os.path.exists(bus_path):
        print(f"✅ SQLite bus database found!")
        
        # Get file size
        size = os.path.getsize(bus_path)
        print(f"📊 Database size: {size:,} bytes")
        
        return True
    else:
        print(f"❌ SQLite bus database NOT found!")
        print(f"🔍 Checking alternative paths...")
        
        # Check alternative paths
        alt_paths = [
            "data/bus.db",
            "smart_trader_bus.db",
            "bus.db"
        ]
        
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                print(f"✅ Found alternative database: {alt_path}")
                return True
        
        print(f"❌ No SQLite bus database found in any location!")
        return False


def test_sqlite_bus_content():
    """Test the content of the SQLite bus database."""
    bus_path = "data/smart_trader_bus.db"
    
    if not os.path.exists(bus_path):
        # Try alternative path
        bus_path = "data/bus.db"
        if not os.path.exists(bus_path):
            print(f"❌ Cannot test content - database not found!")
            return False
    
    print(f"\n📊 Testing SQLite Bus Content...")
    
    try:
        conn = sqlite3.connect(bus_path)
        cursor = conn.cursor()
        
        # Check if messages table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='messages'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print(f"❌ Messages table does not exist!")
            return False
        
        print(f"✅ Messages table exists!")
        
        # Count total messages
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_messages = cursor.fetchone()[0]
        print(f"📈 Total messages: {total_messages:,}")
        
        if total_messages == 0:
            print(f"⚠️  No messages in database - testnet may not be running!")
            return False
        
        # Get message streams
        cursor.execute("SELECT DISTINCT stream FROM messages ORDER BY stream")
        streams = [row[0] for row in cursor.fetchall()]
        print(f"📡 Message streams found: {len(streams)}")
        for stream in streams:
            cursor.execute("SELECT COUNT(*) FROM messages WHERE stream = ?", (stream,))
            count = cursor.fetchone()[0]
            print(f"   - {stream}: {count:,} messages")
        
        # Get latest messages
        cursor.execute("SELECT stream, ts, payload FROM messages ORDER BY ts DESC LIMIT 5")
        latest_messages = cursor.fetchall()
        
        print(f"\n📋 Latest 5 messages:")
        for stream, ts, payload in latest_messages:
            timestamp = datetime.fromtimestamp(ts)
            try:
                data = json.loads(payload)
                print(f"   - {timestamp.strftime('%H:%M:%S')} | {stream} | {data.get('symbol', 'N/A')}")
            except:
                print(f"   - {timestamp.strftime('%H:%M:%S')} | {stream} | [Invalid JSON]")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing database content: {e}")
        return False


def test_bus_reader():
    """Test the SQLite bus reader."""
    print(f"\n🔧 Testing SQLite Bus Reader...")
    
    try:
        # Create bus reader
        reader = SQLiteBusReader()
        
        if not reader.is_connected():
            print(f"❌ Bus reader failed to connect!")
            return False
        
        print(f"✅ Bus reader connected successfully!")
        
        # Test market data
        print(f"\n📊 Testing market data retrieval...")
        market_data = reader.get_latest_market_data("BTC-USDT")
        
        if market_data:
            print(f"✅ Market data retrieved:")
            print(f"   - Symbol: {market_data.symbol}")
            print(f"   - Price: ${market_data.price:,.2f}")
            print(f"   - Volume: {market_data.volume:,.0f}")
            print(f"   - Timestamp: {market_data.timestamp}")
        else:
            print(f"⚠️  No market data found - may be normal if testnet not running")
        
        # Test signals
        print(f"\n🎯 Testing signals retrieval...")
        signals = reader.get_latest_signals(5)
        
        if signals:
            print(f"✅ Found {len(signals)} signals:")
            for signal in signals[:3]:  # Show first 3
                print(f"   - {signal.timestamp.strftime('%H:%M:%S')} | {signal.action} | {signal.source}")
        else:
            print(f"⚠️  No signals found - may be normal if testnet not running")
        
        # Test trades
        print(f"\n💼 Testing trades retrieval...")
        trades = reader.get_latest_trades(5)
        
        if trades:
            print(f"✅ Found {len(trades)} trades:")
            for trade in trades[:3]:  # Show first 3
                print(f"   - {trade.timestamp.strftime('%H:%M:%S')} | {trade.side} | ${trade.price:,.2f}")
        else:
            print(f"⚠️  No trades found - may be normal if testnet not running")
        
        # Test model status
        print(f"\n🤖 Testing model status...")
        models = reader.get_model_status()
        
        if models:
            print(f"✅ Found {len(models)} models:")
            for model_name, status in models.items():
                print(f"   - {model_name}: {status.get('status', 'unknown')}")
        else:
            print(f"⚠️  No model status found - may be normal if testnet not running")
        
        reader.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing bus reader: {e}")
        return False


def test_convenience_functions():
    """Test the convenience functions for web dashboard."""
    print(f"\n🌐 Testing convenience functions for web dashboard...")
    
    try:
        # Test get_real_market_data
        print(f"📊 Testing get_real_market_data()...")
        market_data = get_real_market_data("BTC-USDT")
        
        if market_data:
            print(f"✅ Market data function works:")
            print(f"   - Symbol: {market_data['symbol']}")
            print(f"   - Price: ${market_data['price']:,.2f}")
            print(f"   - Timestamp: {market_data['timestamp']}")
        else:
            print(f"⚠️  No market data returned - may be normal if testnet not running")
        
        # Test get_real_market_summary
        print(f"\n📈 Testing get_real_market_summary()...")
        summary = get_real_market_summary()
        
        if summary:
            print(f"✅ Market summary function works:")
            print(f"   - Total symbols: {summary['total_symbols']}")
            print(f"   - Total volume: {summary['total_volume_24h']:,.0f}")
            print(f"   - Timestamp: {summary['timestamp']}")
        else:
            print(f"❌ No market summary returned")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing convenience functions: {e}")
        return False


def main():
    """Main test function."""
    print(f"🔍 COMPREHENSIVE SQLITE BUS CONNECTION TEST")
    print(f"=" * 50)
    
    # Test results
    results = {
        "database_exists": False,
        "database_content": False,
        "bus_reader": False,
        "convenience_functions": False
    }
    
    # Run tests
    results["database_exists"] = test_sqlite_bus_exists()
    
    if results["database_exists"]:
        results["database_content"] = test_sqlite_bus_content()
        results["bus_reader"] = test_bus_reader()
        results["convenience_functions"] = test_convenience_functions()
    
    # Summary
    print(f"\n🎯 TEST RESULTS SUMMARY")
    print(f"=" * 30)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\n📊 Overall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print(f"🎉 ALL TESTS PASSED! SQLite bus connection is working!")
    elif results["database_exists"]:
        print(f"⚠️  PARTIAL SUCCESS - Database exists but may need testnet running")
    else:
        print(f"❌ TESTS FAILED - SQLite bus not available")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if not results["database_exists"]:
        print(f"   1. Start testnet to create SQLite bus database")
        print(f"   2. Check if testnet is writing to correct database path")
    elif not results["database_content"]:
        print(f"   1. Start testnet trading to generate messages")
        print(f"   2. Check if orchestrator is publishing to bus")
    elif not results["bus_reader"]:
        print(f"   1. Check bus_reader.py for connection issues")
        print(f"   2. Verify database permissions")
    else:
        print(f"   1. Web dashboard should now show real data!")
        print(f"   2. Start testnet to see live updates")


if __name__ == "__main__":
    main()
