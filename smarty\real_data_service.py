#!/usr/bin/env python3
"""
Real Data Integration Service for Smart-Trader Control Center

Integrates with the existing smart-trader orchestrator and models to provide
real market data, model predictions, and trading signals to the web interface.
"""

import asyncio
import json
import logging
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict

# Import smart-trader components
from orchestrator import Orchestrator
from core.feature_store import feature_store
from core.events import Signal, Position
from core.utils import setup_logging

logger = logging.getLogger(__name__)


@dataclass
class ModelPrediction:
    """Model prediction data structure."""
    model_name: str
    symbol: str
    prediction: Dict[str, Any]
    confidence: float
    timestamp: str
    signal_strength: float = 0.0
    signal_direction: str = "neutral"  # "bullish", "bearish", "neutral"


@dataclass
class TradingSignal:
    """Trading signal data structure."""
    signal_id: str
    symbol: str
    action: str  # "buy", "sell", "hold"
    price: float
    quantity: float
    confidence: float
    source: str  # "model", "llm", "ensemble"
    timestamp: str
    metadata: Dict[str, Any]


class RealDataService:
    """Real data integration service."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize real data service."""
        self.config_path = config_path
        self.config = self._load_config()
        self.orchestrator: Optional[Orchestrator] = None
        self.running = False
        self.subscribers: List[Callable] = []
        
        # Data caches
        self.model_predictions: Dict[str, List[ModelPrediction]] = {}
        self.trading_signals: List[TradingSignal] = []
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.account_info: Dict[str, Any] = {}
        
        # Symbols to monitor
        self.symbols = self.config.get("symbols", ["BTC-USDT"])
        
        # Setup logging
        setup_logging(level=logging.INFO)

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
            # Return default config
            return {
                "symbols": ["BTC-USDT"],
                "testnet": True,
                "simulation_mode": True,
                "trading": {"enabled": False}
            }

    async def start(self) -> None:
        """Start the real data service."""
        if self.running:
            return
        
        logger.info("Starting Real Data Service...")
        
        try:
            # Initialize orchestrator with real config
            self.orchestrator = Orchestrator(self.config)
            
            # Start orchestrator in background
            self.orchestrator_task = asyncio.create_task(self._run_orchestrator())
            
            # Start data collection loops
            self.data_collection_task = asyncio.create_task(self._collect_data_loop())
            
            self.running = True
            logger.info("Real Data Service started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Real Data Service: {e}")
            raise

    async def stop(self) -> None:
        """Stop the real data service."""
        if not self.running:
            return
        
        logger.info("Stopping Real Data Service...")
        
        self.running = False
        
        # Stop orchestrator
        if self.orchestrator:
            await self.orchestrator.stop()
        
        # Cancel tasks
        if hasattr(self, 'orchestrator_task'):
            self.orchestrator_task.cancel()
        if hasattr(self, 'data_collection_task'):
            self.data_collection_task.cancel()
        
        logger.info("Real Data Service stopped")

    async def _run_orchestrator(self) -> None:
        """Run the orchestrator in background."""
        try:
            await self.orchestrator.start()
        except Exception as e:
            logger.error(f"Error running orchestrator: {e}")

    async def _collect_data_loop(self) -> None:
        """Main data collection loop."""
        while self.running:
            try:
                # Collect model predictions
                await self._collect_model_predictions()
                
                # Collect market data
                await self._collect_market_data()
                
                # Collect account info
                await self._collect_account_info()
                
                # Collect trading signals
                await self._collect_trading_signals()
                
                # Notify subscribers
                await self._notify_subscribers()
                
                # Wait before next collection
                await asyncio.sleep(5)  # Collect every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in data collection loop: {e}")
                await asyncio.sleep(10)

    async def _collect_model_predictions(self) -> None:
        """Collect predictions from all registered models."""
        if not self.orchestrator or not hasattr(self.orchestrator, 'registered_models'):
            return
        
        for symbol in self.symbols:
            for model_name, model in self.orchestrator.registered_models.items():
                try:
                    # Get features from feature store
                    features = await self._get_features_for_model(symbol, model_name)
                    
                    if features:
                        # Get prediction from model
                        prediction = await self._get_model_prediction(model, features)
                        
                        if prediction:
                            # Create model prediction object
                            model_pred = ModelPrediction(
                                model_name=model_name,
                                symbol=symbol,
                                prediction=prediction,
                                confidence=prediction.get('confidence', 0.5),
                                timestamp=datetime.now().isoformat(),
                                signal_strength=prediction.get('signal_strength', 0.0),
                                signal_direction=self._determine_signal_direction(prediction)
                            )
                            
                            # Store prediction
                            if symbol not in self.model_predictions:
                                self.model_predictions[symbol] = []
                            
                            self.model_predictions[symbol].append(model_pred)
                            
                            # Keep only last 50 predictions per symbol
                            if len(self.model_predictions[symbol]) > 50:
                                self.model_predictions[symbol] = self.model_predictions[symbol][-50:]
                
                except Exception as e:
                    logger.error(f"Error collecting prediction from {model_name}: {e}")

    async def _get_features_for_model(self, symbol: str, model_name: str) -> Dict[str, Any]:
        """Get features required for a specific model."""
        features = {"symbol": symbol}
        
        try:
            # Get basic market data
            features["close_price"] = await feature_store.get(symbol, "close_price", 0.0)
            features["volume"] = await feature_store.get(symbol, "volume", 0.0)
            features["timestamp"] = datetime.now()
            
            # Get model-specific features
            if model_name == "rsi":
                # Get price history for RSI calculation
                close_prices = await feature_store.get_time_series(symbol, "close_prices", limit=50)
                if close_prices:
                    features["close_prices"] = [float(price) for price in close_prices]
                else:
                    # Generate mock price data for testing
                    base_price = features["close_price"] or 43500.0
                    features["close_prices"] = [base_price * (1 + (i-25)*0.001) for i in range(50)]
            
            elif model_name == "vwap_deviation":
                # Get VWAP-related data
                features["mid_price"] = features["close_price"]
                features["volume"] = features["volume"]
            
            elif model_name == "garch_volatility":
                # Get return history for volatility calculation
                returns = await feature_store.get_time_series(symbol, "returns", limit=100)
                if returns:
                    features["returns"] = [float(ret) for ret in returns]
                else:
                    # Generate mock returns for testing
                    features["returns"] = [0.001 * (i % 10 - 5) for i in range(100)]
            
            elif model_name == "funding_momentum":
                # Get funding rate data
                funding_rate = await feature_store.get(symbol, "funding_rate", 0.0)
                features["funding_rate"] = funding_rate
                
                funding_rates = await feature_store.get_time_series(symbol, "funding_rates", limit=24)
                if funding_rates:
                    features["funding_rates"] = [float(rate) for rate in funding_rates]
            
            elif model_name == "open_interest_momentum":
                # Get open interest data
                open_interest = await feature_store.get(symbol, "open_interest", 0.0)
                features["open_interest"] = open_interest
                
                oi_history = await feature_store.get_time_series(symbol, "open_interest_history", limit=24)
                if oi_history:
                    features["open_interest_history"] = [float(oi) for oi in oi_history]
            
            return features
            
        except Exception as e:
            logger.error(f"Error getting features for {model_name}: {e}")
            return features

    async def _get_model_prediction(self, model, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get prediction from a model."""
        try:
            if hasattr(model, 'predict'):
                prediction = model.predict(features)
                if asyncio.iscoroutine(prediction):
                    prediction = await prediction
                return prediction
            else:
                logger.warning(f"Model {type(model).__name__} does not have predict method")
                return None
        except Exception as e:
            logger.error(f"Error getting prediction from model: {e}")
            return None

    def _determine_signal_direction(self, prediction: Dict[str, Any]) -> str:
        """Determine signal direction from prediction."""
        signal_strength = prediction.get('signal_strength', 0.0)
        
        if signal_strength > 0.3:
            return "bullish"
        elif signal_strength < -0.3:
            return "bearish"
        else:
            return "neutral"

    async def _collect_market_data(self) -> None:
        """Collect current market data."""
        for symbol in self.symbols:
            try:
                market_data = {
                    "symbol": symbol,
                    "price": await feature_store.get(symbol, "close_price", 0.0),
                    "volume": await feature_store.get(symbol, "volume", 0.0),
                    "bid": await feature_store.get(symbol, "bid_price", 0.0),
                    "ask": await feature_store.get(symbol, "ask_price", 0.0),
                    "high_24h": await feature_store.get(symbol, "high_24h", 0.0),
                    "low_24h": await feature_store.get(symbol, "low_24h", 0.0),
                    "change_24h": await feature_store.get(symbol, "change_24h", 0.0),
                    "timestamp": datetime.now().isoformat()
                }
                
                self.market_data[symbol] = market_data
                
            except Exception as e:
                logger.error(f"Error collecting market data for {symbol}: {e}")

    async def _collect_account_info(self) -> None:
        """Collect account information."""
        try:
            if self.orchestrator and hasattr(self.orchestrator, 'htx_client'):
                # Get account info from HTX client
                client = self.orchestrator.htx_client
                
                if hasattr(client, 'get_account_info'):
                    account_info = await client.get_account_info()
                    if account_info:
                        self.account_info = account_info
                
                # Get positions
                if hasattr(client, 'get_positions'):
                    positions = await client.get_positions()
                    if positions:
                        self.account_info["positions"] = positions
                        
        except Exception as e:
            logger.error(f"Error collecting account info: {e}")

    async def _collect_trading_signals(self) -> None:
        """Collect recent trading signals."""
        try:
            # This would collect signals from the orchestrator's signal generation
            # For now, we'll create mock signals based on model predictions
            pass
        except Exception as e:
            logger.error(f"Error collecting trading signals: {e}")

    async def _notify_subscribers(self) -> None:
        """Notify all subscribers of data updates."""
        if not self.subscribers:
            return
        
        update_data = {
            "type": "real_data_update",
            "timestamp": datetime.now().isoformat(),
            "model_predictions": self.model_predictions,
            "market_data": self.market_data,
            "account_info": self.account_info,
            "trading_signals": [asdict(signal) for signal in self.trading_signals[-10:]]  # Last 10 signals
        }
        
        for callback in self.subscribers:
            try:
                await callback(update_data)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")

    def subscribe(self, callback: Callable) -> None:
        """Subscribe to real data updates."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback: Callable) -> None:
        """Unsubscribe from real data updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def get_model_predictions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current model predictions."""
        if symbol:
            return {symbol: self.model_predictions.get(symbol, [])}
        return self.model_predictions

    def get_market_data(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current market data."""
        if symbol:
            return self.market_data.get(symbol, {})
        return self.market_data

    def get_account_info(self) -> Dict[str, Any]:
        """Get current account information."""
        return self.account_info

    def get_system_status(self) -> Dict[str, Any]:
        """Get system status."""
        return {
            "running": self.running,
            "orchestrator_running": self.orchestrator is not None and self.orchestrator.running,
            "models_count": len(self.orchestrator.registered_models) if self.orchestrator else 0,
            "symbols": self.symbols,
            "config_loaded": bool(self.config),
            "timestamp": datetime.now().isoformat()
        }


# Global real data service instance
real_data_service = RealDataService()


async def start_real_data_service() -> None:
    """Start the global real data service."""
    await real_data_service.start()


async def stop_real_data_service() -> None:
    """Stop the global real data service."""
    await real_data_service.stop()


def get_model_predictions(symbol: Optional[str] = None) -> Dict[str, Any]:
    """Get current model predictions."""
    return real_data_service.get_model_predictions(symbol)


def get_real_market_data(symbol: Optional[str] = None) -> Dict[str, Any]:
    """Get current market data."""
    return real_data_service.get_market_data(symbol)


def get_account_info() -> Dict[str, Any]:
    """Get current account information."""
    return real_data_service.get_account_info()


def subscribe_to_real_data(callback: Callable) -> None:
    """Subscribe to real data updates."""
    real_data_service.subscribe(callback)
