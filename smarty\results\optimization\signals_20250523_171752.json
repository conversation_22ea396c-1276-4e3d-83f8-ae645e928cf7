[{"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-02-24T17:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-02-25T15:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-02-28T23:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-03T00:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-06T09:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-07T11:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-07T16:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-09T21:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-11T20:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-14T23:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-16T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-18T12:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-20T13:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-21T11:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-24T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-25T07:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-25T14:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-27T01:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-03-27T20:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-03-29T21:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-01T08:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-02T15:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-07T08:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-07T13:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-09T21:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-10T23:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-11T23:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-14T10:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-15T13:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-16T06:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-17T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-19T17:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-20T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-23T23:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-25T13:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-28T12:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-29T07:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-04-29T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-04-30T02:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-05-03T03:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-05-05T19:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-05-06T17:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-05-07T18:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-05-11T15:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-05-11T21:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-05-13T04:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.7, "timestamp": "2025-05-15T07:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed below Slow MA (40)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.7, "timestamp": "2025-05-18T01:00:00", "source": "sma_crossover", "rationale": "Fast MA (10) crossed above Slow MA (40)"}]