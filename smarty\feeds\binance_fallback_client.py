#!/usr/bin/env python3
"""
Binance Fallback WebSocket Client

This provides real market data from Binance when HTX is geo-blocked.
Converts Binance data format to HTX-compatible format for seamless fallback.
"""

import asyncio
import json
import logging
import websockets
import time
from typing import Dict, Callable, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)

class BinanceFallbackClient:
    """Binance WebSocket client that provides HTX-compatible market data."""

    def __init__(self):
        self.ws = None
        self.publisher = None
        self.running = False
        self.subscriptions = set()

        # Binance WebSocket URL
        self.ws_url = "wss://stream.binance.com:9443/ws"

        # Symbol mapping: HTX -> Binance
        self.symbol_map = {
            "BTC-USDT": "btcusdt",
            "ETH-USDT": "ethusdt"
        }

        # Reverse mapping: Binance -> HTX
        self.reverse_symbol_map = {v: k for k, v in self.symbol_map.items()}

    def set_publisher(self, publisher: Callable):
        """Set the message publisher function."""
        self.publisher = publisher
        logger.info("Message bus publisher set for Binance fallback client")

    async def connect(self) -> bool:
        """Connect to Binance WebSocket."""
        try:
            logger.info("🔄 Connecting to Binance WebSocket (HTX fallback)...")

            # Build subscription message for all symbols
            streams = []
            for binance_symbol in self.symbol_map.values():
                # Add kline, trade, and depth streams
                streams.extend([
                    f"{binance_symbol}@kline_1s",
                    f"{binance_symbol}@trade",
                    f"{binance_symbol}@depth20@100ms"
                ])

            # Connect to combined stream
            stream_url = f"wss://stream.binance.com:9443/stream?streams={'/'.join(streams)}"
            self.ws = await websockets.connect(stream_url)

            logger.info("✅ Connected to Binance WebSocket")
            logger.info(f"📊 Subscribed to {len(streams)} Binance streams")

            self.running = True

            # Start message processing
            asyncio.create_task(self._process_messages())

            return True

        except Exception as e:
            logger.error(f"❌ Failed to connect to Binance WebSocket: {e}")
            return False

    async def _process_messages(self):
        """Process incoming Binance WebSocket messages."""
        try:
            while self.running and self.ws:
                try:
                    message = await asyncio.wait_for(self.ws.recv(), timeout=30.0)
                    data = json.loads(message)

                    # Process the message and convert to HTX format
                    await self._handle_binance_message(data)

                except asyncio.TimeoutError:
                    # Send ping to keep connection alive
                    if self.ws:
                        await self.ws.ping()

                except websockets.exceptions.ConnectionClosed:
                    logger.warning("Binance WebSocket connection closed")
                    break

                except Exception as e:
                    logger.error(f"Error processing Binance message: {e}")

        except Exception as e:
            logger.error(f"Error in Binance message processing: {e}")
        finally:
            self.running = False

    async def _handle_binance_message(self, data: Dict):
        """Convert Binance message to HTX format and publish."""
        try:
            if 'stream' not in data or 'data' not in data:
                return

            stream = data['stream']
            payload = data['data']

            # Extract symbol and stream type
            parts = stream.split('@')
            if len(parts) < 2:
                return

            binance_symbol = parts[0]
            stream_type = parts[1]

            # Convert to HTX symbol
            htx_symbol = self.reverse_symbol_map.get(binance_symbol)
            if not htx_symbol:
                return

            # Convert based on stream type
            if stream_type.startswith('kline'):
                await self._convert_kline(htx_symbol, payload)
            elif stream_type == 'trade':
                await self._convert_trade(htx_symbol, payload)
            elif stream_type.startswith('depth'):
                await self._convert_depth(htx_symbol, payload)

        except Exception as e:
            logger.error(f"Error handling Binance message: {e}")

    async def _convert_kline(self, htx_symbol: str, kline_data: Dict):
        """Convert Binance kline to HTX format."""
        try:
            k = kline_data['k']

            # Convert to HTX kline format
            htx_kline = {
                "ch": f"market.{htx_symbol}.kline.1s",
                "ts": int(time.time() * 1000),
                "tick": {
                    "id": int(k['t'] / 1000),  # Convert ms to seconds
                    "open": float(k['o']),
                    "close": float(k['c']),
                    "high": float(k['h']),
                    "low": float(k['l']),
                    "amount": float(k['v']),  # Base asset volume
                    "vol": float(k['q']),     # Quote asset volume
                    "count": int(k['n'])      # Number of trades
                }
            }

            if self.publisher:
                # Publish to both market.* and htx.* topics for compatibility
                self.publisher(f"market.{htx_symbol}.kline.1s", time.time(), htx_kline)
                self.publisher("htx.kline", time.time(), htx_kline)

        except Exception as e:
            logger.error(f"Error converting Binance kline: {e}")

    async def _convert_trade(self, htx_symbol: str, trade_data: Dict):
        """Convert Binance trade to HTX format."""
        try:
            # Convert to HTX trade format
            htx_trade = {
                "ch": f"market.{htx_symbol}.trade.detail",
                "ts": int(time.time() * 1000),
                "tick": {
                    "id": int(trade_data['E']),  # Event time
                    "ts": int(trade_data['E']),
                    "data": [{
                        "id": int(trade_data['t']),
                        "ts": int(trade_data['T']),
                        "tradeId": int(trade_data['t']),
                        "amount": float(trade_data['q']),
                        "price": float(trade_data['p']),
                        "direction": "buy" if trade_data['m'] else "sell"  # m=true means buyer is market maker
                    }]
                }
            }

            if self.publisher:
                # Publish to both market.* and htx.* topics for compatibility
                self.publisher(f"market.{htx_symbol}.trade.detail", time.time(), htx_trade)
                self.publisher("htx.trade", time.time(), htx_trade)

        except Exception as e:
            logger.error(f"Error converting Binance trade: {e}")

    async def _convert_depth(self, htx_symbol: str, depth_data: Dict):
        """Convert Binance depth to HTX format."""
        try:
            # Convert to HTX depth format
            htx_depth = {
                "ch": f"market.{htx_symbol}.depth.step0",
                "ts": int(time.time() * 1000),
                "tick": {
                    "bids": [[float(bid[0]), float(bid[1])] for bid in depth_data['bids']],
                    "asks": [[float(ask[0]), float(ask[1])] for ask in depth_data['asks']],
                    "version": depth_data.get('lastUpdateId', int(time.time())),
                    "ts": int(time.time() * 1000)
                }
            }

            if self.publisher:
                # Publish to both market.* and htx.* topics for compatibility
                self.publisher(f"market.{htx_symbol}.depth.step0", time.time(), htx_depth)
                self.publisher("htx.orderbook", time.time(), htx_depth)

        except Exception as e:
            logger.error(f"Error converting Binance depth: {e}")

    async def close(self):
        """Close the Binance WebSocket connection."""
        logger.info("🛑 Closing Binance fallback client...")
        self.running = False

        if self.ws:
            try:
                await self.ws.close()
                logger.info("✅ Binance WebSocket connection closed")
            except Exception as e:
                logger.error(f"Error closing Binance WebSocket: {e}")

    def get_connection_status(self) -> Dict:
        """Get connection status."""
        return {
            "connected": self.running and self.ws is not None,
            "subscriptions": len(self.subscriptions),
            "exchange": "Binance (HTX Fallback)"
        }
