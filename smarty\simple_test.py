#!/usr/bin/env python
"""
Simple test script for the smart-trader system.
"""

import os
import sys
import yaml
import logging
import time
import json
import sqlite3
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("simple-test")

# Create directories
os.makedirs("data", exist_ok=True)
os.makedirs("logs", exist_ok=True)

# Create a simple message bus
class SimpleBus:
    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        
        # Create tables if they don't exist
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stream TEXT,
            timestamp REAL,
            payload TEXT
        )
        ''')
        self.conn.commit()
    
    def publish(self, stream, timestamp, payload):
        """Publish a message to a stream."""
        payload_json = json.dumps(payload)
        self.cursor.execute(
            "INSERT INTO messages (stream, timestamp, payload) VALUES (?, ?, ?)",
            (stream, timestamp, payload_json)
        )
        self.conn.commit()
        logger.info(f"Published message to {stream}: {payload}")
    
    def get_messages(self, stream, limit=10):
        """Get recent messages from a stream."""
        self.cursor.execute(
            "SELECT timestamp, payload FROM messages WHERE stream = ? ORDER BY timestamp DESC LIMIT ?",
            (stream, limit)
        )
        
        messages = []
        for ts, payload in self.cursor.fetchall():
            try:
                payload_dict = json.loads(payload)
                messages.append((ts, payload_dict))
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse payload: {payload}")
        
        return messages
    
    def close(self):
        """Close the connection."""
        self.conn.close()

# Create a simple feature store
class SimpleFeatureStore:
    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        
        # Create tables if they don't exist
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS features (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT,
            key TEXT,
            value TEXT,
            timestamp REAL,
            UNIQUE(symbol, key)
        )
        ''')
        
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS feature_time_series (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT,
            key TEXT,
            value TEXT,
            timestamp REAL
        )
        ''')
        self.conn.commit()
    
    def set(self, symbol, key, value):
        """Set a feature value."""
        value_json = json.dumps(value)
        timestamp = time.time()
        
        self.cursor.execute(
            "INSERT OR REPLACE INTO features (symbol, key, value, timestamp) VALUES (?, ?, ?, ?)",
            (symbol, key, value_json, timestamp)
        )
        self.conn.commit()
        logger.info(f"Set feature {symbol}.{key} = {value}")
    
    def get(self, symbol, key):
        """Get a feature value."""
        self.cursor.execute(
            "SELECT value FROM features WHERE symbol = ? AND key = ? ORDER BY timestamp DESC LIMIT 1",
            (symbol, key)
        )
        
        result = self.cursor.fetchone()
        if result:
            try:
                return json.loads(result[0])
            except json.JSONDecodeError:
                return result[0]
        
        return None
    
    def add_time_series(self, symbol, key, value, timestamp=None):
        """Add a value to a time series."""
        if timestamp is None:
            timestamp = datetime.now()
        
        if isinstance(timestamp, datetime):
            timestamp = timestamp.timestamp()
        
        value_json = json.dumps(value)
        
        self.cursor.execute(
            "INSERT INTO feature_time_series (symbol, key, value, timestamp) VALUES (?, ?, ?, ?)",
            (symbol, key, value_json, timestamp)
        )
        self.conn.commit()
        logger.info(f"Added time series value {symbol}.{key} = {value}")
    
    def close(self):
        """Close the connection."""
        self.conn.close()

# Main function
def main():
    # Load configuration
    config_path = "config_testnet.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Log startup
    logger.info("=" * 50)
    logger.info("Starting simple test for smart-trader system")
    logger.info(f"Trading symbols: {config.get('trading', {}).get('symbols', [])}")
    logger.info(f"Trading enabled: {config.get('trading', {}).get('enabled', False)}")
    logger.info(f"Simulation mode: {config.get('trading', {}).get('simulation_mode', True)}")
    logger.info("=" * 50)
    
    # Create message bus
    bus_path = config.get("message_bus", {}).get("path", "data/test_bus.db")
    bus = SimpleBus(bus_path)
    
    # Create feature store
    feature_store_path = config.get("feature_store", {}).get("path", "data/test_features.db")
    feature_store = SimpleFeatureStore(feature_store_path)
    
    # Get symbols
    symbols = config.get("trading", {}).get("symbols", ["BTC-USDT"])
    
    try:
        # Simulate account state
        account_state = {
            "total": 100.0,
            "available": 90.0,
            "reserved": 10.0,
            "timestamp": datetime.now().isoformat()
        }
        bus.publish("account.state", time.time(), account_state)
        
        # Simulate positions
        positions_state = {
            "positions": [
                {
                    "symbol": symbols[0],
                    "side": "LONG",
                    "size": 0.01,
                    "entry_price": 50000.0,
                    "mark_price": 51000.0
                }
            ],
            "timestamp": datetime.now().isoformat()
        }
        bus.publish("positions.state", time.time(), positions_state)
        
        # Simulate orders
        orders_state = {
            "orders": [
                {
                    "symbol": symbols[0],
                    "side": "BUY",
                    "quantity": 0.005,
                    "price": 49500.0,
                    "status": "NEW"
                }
            ],
            "timestamp": datetime.now().isoformat()
        }
        bus.publish("orders.state", time.time(), orders_state)
        
        # Simulate features
        for symbol in symbols:
            # VWAP feature
            feature_store.set(symbol, "vwap.z_score", 0.5)
            
            # Volatility feature
            feature_store.set(symbol, "volatility.z_score", 1.2)
            feature_store.set(symbol, "volatility.volatility_level", "HIGH")
            
            # Funding feature
            feature_store.set(symbol, "funding.z_score", -0.3)
            
            # Open interest feature
            feature_store.set(symbol, "open_interest.z_score", 0.8)
            
            # Sentiment feature
            feature_store.set(symbol, "sentiment.score", 0.6)
        
        # Simulate fused signal
        fused_signal = {
            "symbol": symbols[0],
            "score": 0.8,
            "decision": "BUY",
            "confidence": 0.9,
            "timestamp": datetime.now().isoformat(),
            "source": "fused"
        }
        bus.publish("signals.fused", time.time(), fused_signal)
        
        # Simulate LLM signal
        llm_signal = {
            "symbol": symbols[0],
            "action": "BUY",
            "confidence": 0.85,
            "rationale": "Strong buy signal from multiple indicators",
            "timestamp": datetime.now().isoformat(),
            "source": "llm",
            "original_signal": fused_signal
        }
        bus.publish("signals.llm", time.time(), llm_signal)
        
        # Simulate trade execution
        trade_execution = {
            "symbol": symbols[0],
            "side": "BUY",
            "quantity": 0.01,
            "price": 50000.0,
            "order_id": "test-order-123",
            "timestamp": datetime.now().isoformat(),
            "source": "llm",
            "rationale": "Strong buy signal from multiple indicators",
            "pnl": 0.0,
            "pnl_pct": 0.0
        }
        bus.publish("trades.executed", time.time(), trade_execution)
        
        # Simulate LLM metrics
        feature_store.set(symbols[0], "metrics.llm.call_count", 10)
        feature_store.set(symbols[0], "metrics.llm.valid_calls", 9)
        feature_store.set(symbols[0], "metrics.llm.invalid_calls", 1)
        feature_store.set(symbols[0], "metrics.llm.health.error_rate", 0.1)
        feature_store.set(symbols[0], "metrics.llm.health.avg_latency", 2.5)
        
        # Add some time series data
        for i in range(5):
            feature_store.add_time_series(symbols[0], "metrics.llm.latency", 2.0 + i * 0.1)
            feature_store.add_time_series(symbols[0], "metrics.llm.confidence", 0.8 - i * 0.05)
        
        logger.info("Test data generated successfully")
        
        # Wait a moment
        time.sleep(1)
        
        # Print some stats
        logger.info("=" * 50)
        logger.info("Test Results:")
        logger.info(f"Account balance: ${account_state['total']:.2f}")
        logger.info(f"Positions: {len(positions_state['positions'])}")
        logger.info(f"Orders: {len(orders_state['orders'])}")
        logger.info(f"LLM calls: {feature_store.get(symbols[0], 'metrics.llm.call_count')}")
        logger.info(f"LLM error rate: {feature_store.get(symbols[0], 'metrics.llm.health.error_rate'):.2%}")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"Error in test: {e}", exc_info=True)
    finally:
        # Close connections
        bus.close()
        feature_store.close()
        logger.info("Test complete")

if __name__ == "__main__":
    main()
