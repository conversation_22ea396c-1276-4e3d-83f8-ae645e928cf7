/**
 * Symbol Selection Fix for Live Trading Interface
 * Fixes live data flow and symbol selection issues
 */

class SymbolSelectionFix {
    constructor() {
        this.symbols = [
            'DOGE/USDT:USDT',
            'BTC/USDT:USDT', 
            'ETH/USDT:USDT',
            'BNB/USDT:USDT',
            'ADA/USDT:USDT',
            'SOL/USDT:USDT',
            'XRP/USDT:USDT',
            'DOT/USDT:USDT',
            'AVAX/USDT:USDT'
        ];
        
        this.currentSymbol = 'DOGE/USDT:USDT';
        this.init();
    }
    
    init() {
        console.log('🔧 Initializing Symbol Selection Fix...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.fixSymbolSelection());
        } else {
            this.fixSymbolSelection();
        }
        
        // Also try after a delay in case elements are loaded dynamically
        setTimeout(() => this.fixSymbolSelection(), 2000);
    }
    
    fixSymbolSelection() {
        console.log('🔧 Fixing symbol selection...');
        
        // Try to find existing symbol selector
        let symbolSelect = document.getElementById('symbol-select');
        
        if (!symbolSelect) {
            // Create symbol selector if it doesn't exist
            this.createSymbolSelector();
            symbolSelect = document.getElementById('symbol-select');
        }
        
        if (symbolSelect) {
            this.updateSymbolOptions(symbolSelect);
            this.attachEventListeners(symbolSelect);
            console.log('✅ Symbol selection fixed');
        } else {
            console.log('⚠️ Could not find or create symbol selector');
        }
        
        // Fix market info display
        this.fixMarketInfoDisplay();
        
        // Start live data updates
        this.startLiveDataUpdates();
    }
    
    createSymbolSelector() {
        console.log('🔧 Creating symbol selector...');
        
        // Find a good place to insert the symbol selector
        const tradingPanel = document.querySelector('.trading-panel') || 
                           document.querySelector('.market-info') ||
                           document.querySelector('.trading-controls') ||
                           document.querySelector('.order-form');
        
        if (!tradingPanel) {
            console.log('⚠️ Could not find trading panel to insert symbol selector');
            return;
        }
        
        const symbolContainer = document.createElement('div');
        symbolContainer.className = 'symbol-selector-container';
        symbolContainer.style.cssText = `
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        `;
        
        symbolContainer.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <label for="symbol-select" style="color: #fff; font-weight: 500; min-width: 80px;">Symbol:</label>
                <select id="symbol-select" class="symbol-select" style="
                    flex: 1;
                    padding: 8px 12px;
                    background: rgba(0, 0, 0, 0.3);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 6px;
                    color: #fff;
                    font-size: 14px;
                ">
                    ${this.symbols.map(symbol => 
                        `<option value="${symbol}">${symbol.replace(':USDT', '')}</option>`
                    ).join('')}
                </select>
                <div id="symbol-status" style="
                    color: #4CAF50;
                    font-size: 12px;
                    min-width: 60px;
                ">●  Live</div>
            </div>
        `;
        
        tradingPanel.insertBefore(symbolContainer, tradingPanel.firstChild);
        console.log('✅ Symbol selector created');
    }
    
    updateSymbolOptions(symbolSelect) {
        console.log('🔧 Updating symbol options...');
        
        symbolSelect.innerHTML = '';
        this.symbols.forEach(symbol => {
            const option = document.createElement('option');
            option.value = symbol;
            option.textContent = symbol.replace(':USDT', '');
            if (symbol === this.currentSymbol) {
                option.selected = true;
            }
            symbolSelect.appendChild(option);
        });
        
        console.log('✅ Symbol options updated');
    }
    
    attachEventListeners(symbolSelect) {
        console.log('🔧 Attaching event listeners...');
        
        symbolSelect.addEventListener('change', (event) => {
            const newSymbol = event.target.value;
            console.log(`📊 Symbol changed to: ${newSymbol}`);
            
            this.currentSymbol = newSymbol;
            
            // Update global trading interface if available
            if (window.liveTradingInterface) {
                window.liveTradingInterface.currentSymbol = newSymbol;
                window.liveTradingInterface.updateMarketInfo();
                console.log('✅ Trading interface symbol updated');
            }
            
            // Update enhanced trading interface if available
            if (window.enhancedLiveTradingInterface) {
                window.enhancedLiveTradingInterface.currentSymbol = newSymbol;
                window.enhancedLiveTradingInterface.updateMarketInfo();
                console.log('✅ Enhanced trading interface symbol updated');
            }
            
            // Trigger market data refresh
            this.refreshMarketData(newSymbol);
        });
        
        console.log('✅ Event listeners attached');
    }
    
    fixMarketInfoDisplay() {
        console.log('🔧 Fixing market info display...');
        
        // Find market info elements and ensure they show current symbol
        const marketInfoElements = document.querySelectorAll('.market-symbol, .symbol-display, .current-symbol');
        
        marketInfoElements.forEach(element => {
            element.textContent = this.currentSymbol.replace(':USDT', '');
        });
        
        // Update price display elements
        const priceElements = document.querySelectorAll('.current-price, .market-price, .symbol-price');
        priceElements.forEach(element => {
            if (!element.textContent || element.textContent === '0' || element.textContent === '$0.00') {
                element.textContent = 'Loading...';
                element.style.color = '#FFA726';
            }
        });
        
        console.log('✅ Market info display fixed');
    }
    
    startLiveDataUpdates() {
        console.log('🔧 Starting live data updates...');
        
        // Update market data every 2 seconds
        setInterval(() => {
            this.refreshMarketData(this.currentSymbol);
        }, 2000);
        
        // Update status indicator
        this.updateStatusIndicator();
        
        console.log('✅ Live data updates started');
    }
    
    async refreshMarketData(symbol) {
        try {
            // Try to get market data from API
            const response = await fetch(`/api/market/ticker/${symbol.replace(':', '')}`);
            
            if (response.ok) {
                const data = await response.json();
                this.updateMarketDisplay(data);
                this.updateStatusIndicator(true);
            } else {
                // Use mock data if API not available
                this.updateMarketDisplay(this.getMockMarketData(symbol));
                this.updateStatusIndicator(false);
            }
        } catch (error) {
            console.log('⚠️ Market data API not available, using mock data');
            this.updateMarketDisplay(this.getMockMarketData(symbol));
            this.updateStatusIndicator(false);
        }
    }
    
    updateMarketDisplay(data) {
        // Update price displays
        const priceElements = document.querySelectorAll('.current-price, .market-price, .symbol-price');
        priceElements.forEach(element => {
            if (data.price) {
                element.textContent = `$${parseFloat(data.price).toFixed(4)}`;
                element.style.color = '#4CAF50';
            }
        });
        
        // Update change displays
        const changeElements = document.querySelectorAll('.price-change, .change-24h');
        changeElements.forEach(element => {
            if (data.change_24h) {
                const change = parseFloat(data.change_24h);
                element.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                element.style.color = change >= 0 ? '#4CAF50' : '#F44336';
            }
        });
        
        // Update volume displays
        const volumeElements = document.querySelectorAll('.volume-24h, .market-volume');
        volumeElements.forEach(element => {
            if (data.volume_24h) {
                const volume = parseFloat(data.volume_24h);
                element.textContent = volume > 1000000 ? 
                    `${(volume / 1000000).toFixed(1)}M` : 
                    `${(volume / 1000).toFixed(0)}K`;
            }
        });
    }
    
    getMockMarketData(symbol) {
        const basePrice = {
            'DOGE/USDT:USDT': 0.3850,
            'BTC/USDT:USDT': 43250.00,
            'ETH/USDT:USDT': 2650.00,
            'BNB/USDT:USDT': 315.50,
            'ADA/USDT:USDT': 0.4820,
            'SOL/USDT:USDT': 98.75,
            'XRP/USDT:USDT': 0.6150,
            'DOT/USDT:USDT': 7.25,
            'AVAX/USDT:USDT': 38.90
        }[symbol] || 1.0;
        
        // Add some random variation
        const variation = (Math.random() - 0.5) * 0.02; // ±1%
        const price = basePrice * (1 + variation);
        
        return {
            symbol: symbol,
            price: price.toFixed(4),
            change_24h: (Math.random() * 10 - 5).toFixed(2), // ±5%
            volume_24h: (Math.random() * 10000000).toFixed(0)
        };
    }
    
    updateStatusIndicator(isLive = true) {
        const statusElement = document.getElementById('symbol-status');
        if (statusElement) {
            if (isLive) {
                statusElement.innerHTML = '● Live';
                statusElement.style.color = '#4CAF50';
            } else {
                statusElement.innerHTML = '● Demo';
                statusElement.style.color = '#FFA726';
            }
        }
    }
}

// Initialize the fix
window.symbolSelectionFix = new SymbolSelectionFix();

// Export for global access
window.fixSymbolSelection = () => {
    if (window.symbolSelectionFix) {
        window.symbolSelectionFix.fixSymbolSelection();
    }
};

console.log('✅ Symbol Selection Fix loaded');
