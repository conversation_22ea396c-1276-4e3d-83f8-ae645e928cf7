#!/usr/bin/env python3
"""
Strategy Integration Manager for Money Circle
Connects existing smart trading strategies with live trading interface
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class StrategyStatus(Enum):
    """Strategy execution status."""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"

class SignalType(Enum):
    """Trading signal types."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_LONG = "close_long"
    CLOSE_SHORT = "close_short"

@dataclass
class TradingSignal:
    """Trading signal from strategy."""
    strategy_name: str
    symbol: str
    signal_type: SignalType
    confidence: float  # 0.0 to 1.0
    price: Optional[float] = None
    quantity: Optional[float] = None
    leverage: Optional[int] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class StrategyConfig:
    """Strategy configuration."""
    name: str
    enabled: bool
    module_path: str
    class_name: str
    config: Dict[str, Any]
    symbols: List[str]
    timeframes: List[str]
    risk_limits: Dict[str, float]
    auto_execute: bool = False

@dataclass
class StrategyPerformance:
    """Real-time strategy performance tracking."""
    strategy_name: str
    signals_generated: int
    signals_executed: int
    total_pnl: float
    win_rate: float
    avg_signal_confidence: float
    last_signal_time: Optional[datetime]
    execution_errors: int
    status: StrategyStatus

class StrategyIntegrationManager:
    """Manages integration between smart strategies and live trading."""
    
    def __init__(self, config_path: str = "config/strategies.json"):
        """Initialize strategy integration manager."""
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Strategy management
        self.strategies: Dict[str, StrategyConfig] = {}
        self.strategy_instances: Dict[str, Any] = {}
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        
        # Signal handling
        self.signal_queue: asyncio.Queue = asyncio.Queue()
        self.signal_callbacks: List[Callable] = []
        self.execution_callbacks: List[Callable] = []
        
        # Control
        self.running = False
        self.execution_enabled = False
        
        # Load configuration
        self._load_strategy_configs()
        
        logger.info("🔗 Strategy Integration Manager initialized")
    
    def _load_strategy_configs(self):
        """Load strategy configurations from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    configs = json.load(f)
                
                for config_data in configs:
                    strategy_config = StrategyConfig(**config_data)
                    self.strategies[strategy_config.name] = strategy_config
                
                logger.info(f"📚 Loaded {len(self.strategies)} strategy configurations")
            else:
                # Create default configuration
                self._create_default_configs()
                
        except Exception as e:
            logger.error(f"❌ Error loading strategy configs: {e}")
            self._create_default_configs()
    
    def _create_default_configs(self):
        """Create default strategy configurations."""
        default_strategies = [
            {
                "name": "Smart Model Integrated",
                "enabled": True,
                "module_path": "epinnox.strategy",
                "class_name": "EpinnoxSmartStrategy",
                "config": {
                    "weights": {
                        "technical": 0.3,
                        "vwap": 0.2,
                        "rsi_model": 0.15,
                        "funding": 0.1,
                        "open_interest": 0.1,
                        "volatility": 0.1,
                        "ensemble": 0.05
                    },
                    "base_buy_threshold": 0.3,
                    "base_sell_threshold": -0.3
                },
                "symbols": ["DOGE/USDT:USDT"],
                "timeframes": ["1m", "5m", "15m"],
                "risk_limits": {
                    "max_position_size": 1000.0,
                    "max_leverage": 10,
                    "stop_loss_pct": 2.0
                },
                "auto_execute": False
            },
            {
                "name": "RSI Strategy",
                "enabled": True,
                "module_path": "epinnox.models.rsi",
                "class_name": "RSIModel",
                "config": {
                    "period": 14,
                    "overbought_threshold": 70,
                    "oversold_threshold": 30
                },
                "symbols": ["DOGE/USDT:USDT"],
                "timeframes": ["5m", "15m"],
                "risk_limits": {
                    "max_position_size": 500.0,
                    "max_leverage": 5,
                    "stop_loss_pct": 1.5
                },
                "auto_execute": False
            },
            {
                "name": "VWAP Deviation",
                "enabled": True,
                "module_path": "epinnox.models.vwap_deviation",
                "class_name": "VWAPDeviationModel",
                "config": {
                    "lookback_periods": [20, 50, 100],
                    "significant_deviation": 0.02
                },
                "symbols": ["DOGE/USDT:USDT"],
                "timeframes": ["15m", "1h"],
                "risk_limits": {
                    "max_position_size": 750.0,
                    "max_leverage": 8,
                    "stop_loss_pct": 2.5
                },
                "auto_execute": False
            }
        ]
        
        self.strategies = {s["name"]: StrategyConfig(**s) for s in default_strategies}
        self._save_strategy_configs()
    
    def _save_strategy_configs(self):
        """Save strategy configurations to file."""
        try:
            configs = [strategy.__dict__ for strategy in self.strategies.values()]
            with open(self.config_path, 'w') as f:
                json.dump(configs, f, indent=2, default=str)
            
            logger.info("💾 Strategy configurations saved")
            
        except Exception as e:
            logger.error(f"❌ Error saving strategy configs: {e}")
    
    async def start_strategy_monitoring(self):
        """Start monitoring and executing strategies."""
        self.running = True
        logger.info("🚀 Starting strategy monitoring...")
        
        # Initialize strategy instances
        await self._initialize_strategies()
        
        # Start monitoring tasks
        tasks = [
            self._strategy_signal_generator(),
            self._signal_processor(),
            self._performance_monitor()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    def stop_strategy_monitoring(self):
        """Stop strategy monitoring."""
        self.running = False
        logger.info("🛑 Strategy monitoring stopped")
    
    async def _initialize_strategies(self):
        """Initialize strategy instances."""
        for name, config in self.strategies.items():
            if not config.enabled:
                continue
            
            try:
                # Dynamic import of strategy module
                module = __import__(config.module_path, fromlist=[config.class_name])
                strategy_class = getattr(module, config.class_name)
                
                # Initialize strategy instance
                strategy_instance = strategy_class(config.config)
                self.strategy_instances[name] = strategy_instance
                
                # Initialize performance tracking
                self.strategy_performance[name] = StrategyPerformance(
                    strategy_name=name,
                    signals_generated=0,
                    signals_executed=0,
                    total_pnl=0.0,
                    win_rate=0.0,
                    avg_signal_confidence=0.0,
                    last_signal_time=None,
                    execution_errors=0,
                    status=StrategyStatus.RUNNING
                )
                
                logger.info(f"✅ Strategy initialized: {name}")
                
            except Exception as e:
                logger.error(f"❌ Error initializing strategy {name}: {e}")
                if name in self.strategy_performance:
                    self.strategy_performance[name].status = StrategyStatus.ERROR
    
    async def _strategy_signal_generator(self):
        """Generate signals from all active strategies."""
        while self.running:
            try:
                for name, strategy in self.strategy_instances.items():
                    if name not in self.strategies or not self.strategies[name].enabled:
                        continue
                    
                    config = self.strategies[name]
                    
                    # Generate signals for each symbol
                    for symbol in config.symbols:
                        try:
                            # Get market data (would be injected from market data manager)
                            market_data = await self._get_market_data(symbol)
                            
                            if not market_data:
                                continue
                            
                            # Generate signal from strategy
                            signal = await self._generate_strategy_signal(
                                strategy, name, symbol, market_data, config
                            )
                            
                            if signal:
                                await self.signal_queue.put(signal)
                                self.strategy_performance[name].signals_generated += 1
                                self.strategy_performance[name].last_signal_time = datetime.now()
                                
                                logger.info(f"📡 Signal generated: {name} -> {signal.signal_type.value} {symbol}")
                        
                        except Exception as e:
                            logger.error(f"❌ Error generating signal for {name}/{symbol}: {e}")
                            self.strategy_performance[name].execution_errors += 1
                
                await asyncio.sleep(5)  # Generate signals every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in signal generation: {e}")
                await asyncio.sleep(10)
    
    async def _signal_processor(self):
        """Process signals from the queue."""
        while self.running:
            try:
                # Get signal from queue (with timeout)
                signal = await asyncio.wait_for(self.signal_queue.get(), timeout=1.0)
                
                # Send to signal callbacks
                for callback in self.signal_callbacks:
                    try:
                        await callback(signal)
                    except Exception as e:
                        logger.error(f"❌ Error in signal callback: {e}")
                
                # Execute signal if auto-execution is enabled
                if self.execution_enabled and self.strategies[signal.strategy_name].auto_execute:
                    await self._execute_signal(signal)
                
            except asyncio.TimeoutError:
                continue  # No signals in queue
            except Exception as e:
                logger.error(f"❌ Error processing signal: {e}")
    
    async def _performance_monitor(self):
        """Monitor strategy performance."""
        while self.running:
            try:
                # Update performance metrics for each strategy
                for name, performance in self.strategy_performance.items():
                    # Calculate win rate, average confidence, etc.
                    # This would integrate with the trading analytics system
                    pass
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in performance monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for signal generation."""
        # This would integrate with the market data manager
        # For now, return mock data
        return {
            'symbol': symbol,
            'price': 0.1,  # DOGE price
            'volume': 1000000,
            'timestamp': datetime.now()
        }
    
    async def _generate_strategy_signal(self, strategy, strategy_name: str, symbol: str,
                                      market_data: Dict[str, Any], config: StrategyConfig) -> Optional[TradingSignal]:
        """Generate signal from strategy instance."""
        try:
            # This would call the actual strategy's signal generation method
            # For now, return a mock signal occasionally
            import random
            
            if random.random() < 0.1:  # 10% chance of signal
                signal_types = [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
                signal_type = random.choice(signal_types)
                
                if signal_type == SignalType.HOLD:
                    return None
                
                return TradingSignal(
                    strategy_name=strategy_name,
                    symbol=symbol,
                    signal_type=signal_type,
                    confidence=random.uniform(0.6, 0.9),
                    price=market_data['price'],
                    quantity=100.0,  # DOGE quantity
                    leverage=config.risk_limits.get('max_leverage', 5),
                    stop_loss=market_data['price'] * (0.98 if signal_type == SignalType.BUY else 1.02),
                    take_profit=market_data['price'] * (1.04 if signal_type == SignalType.BUY else 0.96),
                    metadata={'market_data': market_data}
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error generating signal from {strategy_name}: {e}")
            return None
    
    async def _execute_signal(self, signal: TradingSignal):
        """Execute a trading signal."""
        try:
            # Send to execution callbacks
            for callback in self.execution_callbacks:
                try:
                    await callback(signal)
                    self.strategy_performance[signal.strategy_name].signals_executed += 1
                except Exception as e:
                    logger.error(f"❌ Error in execution callback: {e}")
                    self.strategy_performance[signal.strategy_name].execution_errors += 1
            
            logger.info(f"⚡ Signal executed: {signal.strategy_name} -> {signal.signal_type.value}")
            
        except Exception as e:
            logger.error(f"❌ Error executing signal: {e}")
    
    def add_signal_callback(self, callback: Callable[[TradingSignal], None]):
        """Add callback for signal notifications."""
        self.signal_callbacks.append(callback)
    
    def add_execution_callback(self, callback: Callable[[TradingSignal], None]):
        """Add callback for signal execution."""
        self.execution_callbacks.append(callback)
    
    def enable_auto_execution(self, strategy_name: str = None):
        """Enable automatic signal execution."""
        if strategy_name:
            if strategy_name in self.strategies:
                self.strategies[strategy_name].auto_execute = True
                logger.info(f"✅ Auto-execution enabled for {strategy_name}")
        else:
            self.execution_enabled = True
            logger.info("✅ Global auto-execution enabled")
    
    def disable_auto_execution(self, strategy_name: str = None):
        """Disable automatic signal execution."""
        if strategy_name:
            if strategy_name in self.strategies:
                self.strategies[strategy_name].auto_execute = False
                logger.info(f"🛑 Auto-execution disabled for {strategy_name}")
        else:
            self.execution_enabled = False
            logger.info("🛑 Global auto-execution disabled")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get comprehensive strategy status."""
        return {
            'strategies': {
                name: {
                    'config': config.__dict__,
                    'performance': self.strategy_performance.get(name, {}).__dict__ if name in self.strategy_performance else {},
                    'active': name in self.strategy_instances
                }
                for name, config in self.strategies.items()
            },
            'signal_queue_size': self.signal_queue.qsize(),
            'execution_enabled': self.execution_enabled,
            'monitoring_active': self.running
        }
    
    async def backtest_strategy(self, strategy_name: str, start_date: datetime,
                              end_date: datetime, initial_balance: float = 10000.0) -> Dict[str, Any]:
        """Run backtest for a specific strategy."""
        try:
            if strategy_name not in self.strategies:
                return {'error': f'Strategy {strategy_name} not found'}
            
            # This would integrate with the backtesting system
            # For now, return mock results
            return {
                'strategy': strategy_name,
                'period': f"{start_date.date()} to {end_date.date()}",
                'initial_balance': initial_balance,
                'final_balance': initial_balance * 1.15,  # Mock 15% return
                'total_return': 15.0,
                'max_drawdown': -5.2,
                'sharpe_ratio': 1.8,
                'total_trades': 45,
                'win_rate': 0.67,
                'profit_factor': 2.1
            }
            
        except Exception as e:
            logger.error(f"❌ Error running backtest: {e}")
            return {'error': str(e)}
