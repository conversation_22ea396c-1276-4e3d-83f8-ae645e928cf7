<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Analytics - Money Circle</title>
    <link rel="stylesheet" href="/static/css/design_system.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/club_analytics.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .portfolio-dashboard {
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .portfolio-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .portfolio-header h1 {
            color: #FFD700;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .portfolio-header p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-size: 1.1rem;
        }

        .portfolio-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .overview-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .overview-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .overview-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .positive {
            color: #4CAF50;
        }

        .negative {
            color: #f44336;
        }

        .neutral {
            color: #FFD700;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .analytics-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .analytics-card h3 {
            color: #FFD700;
            margin: 0 0 20px 0;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            text-transform: uppercase;
        }

        .allocation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .allocation-symbol {
            font-weight: 600;
            color: white;
            font-size: 1.1rem;
        }

        .allocation-details {
            text-align: right;
        }

        .allocation-value {
            font-weight: 600;
            color: #4CAF50;
        }

        .allocation-percentage {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .suggestion-item {
            padding: 20px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid;
        }

        .suggestion-high {
            border-left-color: #f44336;
        }

        .suggestion-medium {
            border-left-color: #FF9800;
        }

        .suggestion-low {
            border-left-color: #4CAF50;
        }

        .suggestion-title {
            color: white;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .suggestion-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .suggestion-action {
            color: #2196F3;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .benchmark-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .benchmark-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .benchmark-name {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .benchmark-return {
            font-size: 1.2rem;
            font-weight: 700;
        }

        .risk-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .risk-level {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .risk-low {
            background: #4CAF50;
            color: white;
        }

        .risk-medium {
            background: #FF9800;
            color: white;
        }

        .risk-high {
            background: #f44336;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .portfolio-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <div class="portfolio-dashboard">
        <!-- Portfolio Header -->
        <div class="portfolio-header">
            <h1>📊 Portfolio Analytics</h1>
            <p>Advanced portfolio analysis and performance tracking</p>
        </div>

        <!-- Portfolio Overview -->
        <div class="portfolio-overview" id="portfolio-overview">
            <!-- Overview cards will be populated by JavaScript -->
        </div>

        <!-- Main Analytics Grid -->
        <div class="analytics-grid">
            <!-- Performance Chart -->
            <div class="analytics-card">
                <h3>📈 Performance Analysis</h3>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
                <div class="metrics-grid" id="performance-metrics">
                    <!-- Performance metrics will be populated by JavaScript -->
                </div>
            </div>

            <!-- Asset Allocation -->
            <div class="analytics-card">
                <h3>🎯 Asset Allocation</h3>
                <div class="chart-container">
                    <canvas id="allocation-chart"></canvas>
                </div>
                <div id="allocation-breakdown">
                    <!-- Allocation breakdown will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="analytics-card">
            <h3>🛡️ Risk Analysis</h3>
            <div class="risk-indicator" id="risk-indicator">
                <!-- Risk indicator will be populated by JavaScript -->
            </div>
            <div class="metrics-grid" id="risk-metrics">
                <!-- Risk metrics will be populated by JavaScript -->
            </div>
        </div>

        <!-- Benchmark Comparison -->
        <div class="analytics-card">
            <h3>📊 Benchmark Comparison</h3>
            <div class="benchmark-comparison" id="benchmark-comparison">
                <!-- Benchmark comparison will be populated by JavaScript -->
            </div>
        </div>

        <!-- Optimization Suggestions -->
        <div class="analytics-card">
            <h3>💡 Optimization Suggestions</h3>
            <div id="optimization-suggestions">
                <!-- Suggestions will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script src="/static/js/common.js"></script>
    <script src="/static/js/header_navigation.js"></script>
    <script>
        // Portfolio data (will be populated from server)
        let portfolioData = {{portfolio_data}};

        // Chart instances
        let performanceChart = null;
        let allocationChart = null;

        // Initialize portfolio analytics dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadHeaderFooter();
            populatePortfolioDashboard();

            // Set up real-time updates
            setInterval(updateRealTimeData, 30000); // Update every 30 seconds
        });

        function populatePortfolioDashboard() {
            if (!portfolioData) {
                console.error('Portfolio data not available');
                return;
            }

            // Populate portfolio overview
            populatePortfolioOverview();

            // Create performance chart
            createPerformanceChart();

            // Create allocation chart
            createAllocationChart();

            // Populate risk analysis
            populateRiskAnalysis();

            // Populate benchmark comparison
            populateBenchmarkComparison();

            // Populate optimization suggestions
            populateOptimizationSuggestions();
        }

        function populatePortfolioOverview() {
            const container = document.getElementById('portfolio-overview');
            const overview = portfolioData.portfolio_overview || {};

            container.innerHTML = `
                <div class="overview-card">
                    <div class="overview-value neutral">$${(overview.total_value || 0).toLocaleString()}</div>
                    <div class="overview-label">Portfolio Value</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value ${overview.total_pnl >= 0 ? 'positive' : 'negative'}">
                        $${(overview.total_pnl || 0).toLocaleString()}
                    </div>
                    <div class="overview-label">Total P&L</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value ${overview.total_return_pct >= 0 ? 'positive' : 'negative'}">
                        ${(overview.total_return_pct || 0).toFixed(2)}%
                    </div>
                    <div class="overview-label">Total Return</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value neutral">${overview.open_positions || 0}</div>
                    <div class="overview-label">Open Positions</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value neutral">${overview.total_trades || 0}</div>
                    <div class="overview-label">Total Trades</div>
                </div>
            `;
        }

        function createPerformanceChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            const metrics = portfolioData.performance_metrics || {};

            // Sample data for demonstration
            const data = {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Portfolio Return',
                    data: [2.1, 3.5, 1.8, 5.2],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Benchmark',
                    data: [1.5, 2.1, 1.2, 3.8],
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4
                }]
            };

            performanceChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });

            // Populate performance metrics
            const metricsContainer = document.getElementById('performance-metrics');
            metricsContainer.innerHTML = `
                <div class="metric-item">
                    <div class="metric-value positive">${(metrics.sharpe_ratio || 0).toFixed(3)}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value neutral">${(metrics.volatility || 0).toFixed(2)}%</div>
                    <div class="metric-label">Volatility</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value negative">${(metrics.max_drawdown || 0).toFixed(2)}%</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value positive">${(metrics.win_rate || 0).toFixed(1)}%</div>
                    <div class="metric-label">Win Rate</div>
                </div>
            `;
        }

        function createAllocationChart() {
            const ctx = document.getElementById('allocation-chart').getContext('2d');
            const allocations = portfolioData.asset_allocation || [];

            if (allocations.length > 0) {
                const data = {
                    labels: allocations.map(a => a.symbol),
                    datasets: [{
                        data: allocations.map(a => a.percentage),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ]
                    }]
                };

                allocationChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: 'white'
                                }
                            }
                        }
                    }
                });
            }

            // Populate allocation breakdown
            const breakdownContainer = document.getElementById('allocation-breakdown');
            breakdownContainer.innerHTML = '';

            allocations.forEach(allocation => {
                const allocationElement = document.createElement('div');
                allocationElement.className = 'allocation-item';
                allocationElement.innerHTML = `
                    <div>
                        <div class="allocation-symbol">${allocation.symbol}</div>
                        <div class="allocation-percentage">${allocation.percentage}%</div>
                    </div>
                    <div class="allocation-details">
                        <div class="allocation-value">$${allocation.value.toLocaleString()}</div>
                        <div class="allocation-percentage ${allocation.pnl_pct >= 0 ? 'positive' : 'negative'}">
                            ${allocation.pnl_pct >= 0 ? '+' : ''}${allocation.pnl_pct.toFixed(2)}%
                        </div>
                    </div>
                `;
                breakdownContainer.appendChild(allocationElement);
            });
        }

        function populateRiskAnalysis() {
            const riskData = portfolioData.risk_analysis || {};

            // Risk indicator
            const riskIndicator = document.getElementById('risk-indicator');
            const riskLevel = riskData.risk_level || 'Low';
            const riskClass = `risk-${riskLevel.toLowerCase()}`;

            riskIndicator.innerHTML = `
                <span>Risk Level:</span>
                <span class="risk-level ${riskClass}">${riskLevel}</span>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${riskData.risk_score || 0}%"></div>
                </div>
            `;

            // Risk metrics
            const riskMetrics = document.getElementById('risk-metrics');
            riskMetrics.innerHTML = `
                <div class="metric-item">
                    <div class="metric-value neutral">${riskData.total_positions || 0}</div>
                    <div class="metric-label">Total Positions</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value neutral">${(riskData.largest_position_pct || 0).toFixed(1)}%</div>
                    <div class="metric-label">Largest Position</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value positive">${(riskData.diversification_score || 0).toFixed(1)}</div>
                    <div class="metric-label">Diversification</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value neutral">${riskData.portfolio_concentration || 'N/A'}</div>
                    <div class="metric-label">Concentration</div>
                </div>
            `;
        }

        function populateBenchmarkComparison() {
            const benchmarkData = portfolioData.benchmark_comparison || {};
            const container = document.getElementById('benchmark-comparison');

            container.innerHTML = `
                <div class="benchmark-item">
                    <div class="benchmark-name">Your Portfolio</div>
                    <div class="benchmark-return positive">${(benchmarkData.user_return_30d || 0).toFixed(1)}%</div>
                </div>
                <div class="benchmark-item">
                    <div class="benchmark-name">Bitcoin</div>
                    <div class="benchmark-return neutral">${(benchmarkData.btc_return_30d || 0).toFixed(1)}%</div>
                </div>
                <div class="benchmark-item">
                    <div class="benchmark-name">Ethereum</div>
                    <div class="benchmark-return neutral">${(benchmarkData.eth_return_30d || 0).toFixed(1)}%</div>
                </div>
                <div class="benchmark-item">
                    <div class="benchmark-name">S&P 500</div>
                    <div class="benchmark-return neutral">${(benchmarkData.sp500_return_30d || 0).toFixed(1)}%</div>
                </div>
            `;
        }

        function populateOptimizationSuggestions() {
            const suggestions = portfolioData.optimization_suggestions || [];
            const container = document.getElementById('optimization-suggestions');

            if (suggestions.length === 0) {
                container.innerHTML = '<p style="color: rgba(255, 255, 255, 0.6); text-align: center;">No optimization suggestions at this time</p>';
                return;
            }

            container.innerHTML = '';
            suggestions.forEach(suggestion => {
                const suggestionElement = document.createElement('div');
                suggestionElement.className = `suggestion-item suggestion-${suggestion.priority}`;
                suggestionElement.innerHTML = `
                    <div class="suggestion-title">${suggestion.title}</div>
                    <div class="suggestion-description">${suggestion.description}</div>
                    <div class="suggestion-action">💡 ${suggestion.action}</div>
                `;
                container.appendChild(suggestionElement);
            });
        }

        function updateRealTimeData() {
            // TODO: Implement real-time data updates via WebSocket or API polling
            console.log('Updating real-time portfolio data...');
        }

        // Export functions for global use
        window.updateRealTimeData = updateRealTimeData;
    </script>
</body>
</html>
