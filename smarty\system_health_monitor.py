#!/usr/bin/env python3
"""
System Health Monitor for Smart-Trader Control Center

Monitors system resources, performance metrics, and provides health alerts.
"""

import asyncio
import json
import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class HealthMetric:
    """Health metric data structure."""
    name: str
    value: float
    unit: str
    status: str  # 'healthy', 'warning', 'critical'
    threshold_warning: float
    threshold_critical: float
    timestamp: str


@dataclass
class HealthAlert:
    """Health alert data structure."""
    id: str
    metric_name: str
    level: str  # 'warning', 'critical'
    message: str
    value: float
    threshold: float
    timestamp: str
    resolved: bool = False


class SystemHealthMonitor:
    """System health monitoring service."""
    
    def __init__(self):
        """Initialize health monitor."""
        self.running = False
        self.metrics_history: Dict[str, List[HealthMetric]] = {}
        self.active_alerts: Dict[str, HealthAlert] = {}
        self.subscribers = []
        
        # Health thresholds
        self.thresholds = {
            'cpu_percent': {'warning': 70.0, 'critical': 90.0},
            'memory_percent': {'warning': 80.0, 'critical': 95.0},
            'disk_percent': {'warning': 85.0, 'critical': 95.0},
            'network_latency': {'warning': 100.0, 'critical': 500.0},
            'process_count': {'warning': 200, 'critical': 300},
            'open_files': {'warning': 1000, 'critical': 2000}
        }

    async def start(self) -> None:
        """Start health monitoring."""
        if self.running:
            return
        
        self.running = True
        logger.info("Starting system health monitor...")
        
        # Start monitoring loop
        asyncio.create_task(self._monitoring_loop())
        
        logger.info("System health monitor started")

    async def stop(self) -> None:
        """Stop health monitoring."""
        self.running = False
        logger.info("System health monitor stopped")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.running:
            try:
                # Collect metrics
                metrics = await self._collect_metrics()
                
                # Update history
                self._update_metrics_history(metrics)
                
                # Check for alerts
                await self._check_health_alerts(metrics)
                
                # Notify subscribers
                await self._notify_subscribers(metrics)
                
                # Wait before next collection
                await asyncio.sleep(10)  # Collect every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(30)

    async def _collect_metrics(self) -> List[HealthMetric]:
        """Collect system health metrics."""
        metrics = []
        timestamp = datetime.now().isoformat()
        
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = self._get_status('cpu_percent', cpu_percent)
            metrics.append(HealthMetric(
                name='cpu_percent',
                value=cpu_percent,
                unit='%',
                status=cpu_status,
                threshold_warning=self.thresholds['cpu_percent']['warning'],
                threshold_critical=self.thresholds['cpu_percent']['critical'],
                timestamp=timestamp
            ))
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_status = self._get_status('memory_percent', memory.percent)
            metrics.append(HealthMetric(
                name='memory_percent',
                value=memory.percent,
                unit='%',
                status=memory_status,
                threshold_warning=self.thresholds['memory_percent']['warning'],
                threshold_critical=self.thresholds['memory_percent']['critical'],
                timestamp=timestamp
            ))
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_status = self._get_status('disk_percent', disk_percent)
            metrics.append(HealthMetric(
                name='disk_percent',
                value=disk_percent,
                unit='%',
                status=disk_status,
                threshold_warning=self.thresholds['disk_percent']['warning'],
                threshold_critical=self.thresholds['disk_percent']['critical'],
                timestamp=timestamp
            ))
            
            # Process count
            process_count = len(psutil.pids())
            process_status = self._get_status('process_count', process_count)
            metrics.append(HealthMetric(
                name='process_count',
                value=process_count,
                unit='processes',
                status=process_status,
                threshold_warning=self.thresholds['process_count']['warning'],
                threshold_critical=self.thresholds['process_count']['critical'],
                timestamp=timestamp
            ))
            
            # Network latency (mock for now)
            network_latency = 25.0  # Mock latency in ms
            network_status = self._get_status('network_latency', network_latency)
            metrics.append(HealthMetric(
                name='network_latency',
                value=network_latency,
                unit='ms',
                status=network_status,
                threshold_warning=self.thresholds['network_latency']['warning'],
                threshold_critical=self.thresholds['network_latency']['critical'],
                timestamp=timestamp
            ))
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
        
        return metrics

    def _get_status(self, metric_name: str, value: float) -> str:
        """Determine health status based on thresholds."""
        thresholds = self.thresholds.get(metric_name, {})
        critical = thresholds.get('critical', float('inf'))
        warning = thresholds.get('warning', float('inf'))
        
        if value >= critical:
            return 'critical'
        elif value >= warning:
            return 'warning'
        else:
            return 'healthy'

    def _update_metrics_history(self, metrics: List[HealthMetric]) -> None:
        """Update metrics history."""
        for metric in metrics:
            if metric.name not in self.metrics_history:
                self.metrics_history[metric.name] = []
            
            self.metrics_history[metric.name].append(metric)
            
            # Keep only last 100 data points
            if len(self.metrics_history[metric.name]) > 100:
                self.metrics_history[metric.name] = self.metrics_history[metric.name][-100:]

    async def _check_health_alerts(self, metrics: List[HealthMetric]) -> None:
        """Check for health alerts."""
        for metric in metrics:
            alert_id = f"{metric.name}_{metric.status}"
            
            if metric.status in ['warning', 'critical']:
                # Create or update alert
                if alert_id not in self.active_alerts:
                    threshold = (metric.threshold_critical if metric.status == 'critical' 
                               else metric.threshold_warning)
                    
                    alert = HealthAlert(
                        id=alert_id,
                        metric_name=metric.name,
                        level=metric.status,
                        message=f"{metric.name.replace('_', ' ').title()} is {metric.status}: {metric.value}{metric.unit}",
                        value=metric.value,
                        threshold=threshold,
                        timestamp=metric.timestamp
                    )
                    
                    self.active_alerts[alert_id] = alert
                    logger.warning(f"Health alert: {alert.message}")
                    
                    # Notify subscribers about new alert
                    await self._notify_alert(alert)
            
            else:
                # Resolve alert if it exists
                if alert_id in self.active_alerts:
                    self.active_alerts[alert_id].resolved = True
                    logger.info(f"Health alert resolved: {metric.name}")

    async def _notify_subscribers(self, metrics: List[HealthMetric]) -> None:
        """Notify subscribers of health updates."""
        if not self.subscribers:
            return
        
        health_update = {
            "type": "health_update",
            "timestamp": datetime.now().isoformat(),
            "metrics": [asdict(metric) for metric in metrics],
            "overall_status": self._get_overall_status(metrics)
        }
        
        for callback in self.subscribers:
            try:
                await callback(health_update)
            except Exception as e:
                logger.error(f"Error notifying health subscriber: {e}")

    async def _notify_alert(self, alert: HealthAlert) -> None:
        """Notify subscribers about health alert."""
        alert_notification = {
            "type": "health_alert",
            "timestamp": datetime.now().isoformat(),
            "alert": asdict(alert)
        }
        
        for callback in self.subscribers:
            try:
                await callback(alert_notification)
            except Exception as e:
                logger.error(f"Error notifying health alert: {e}")

    def _get_overall_status(self, metrics: List[HealthMetric]) -> str:
        """Get overall system health status."""
        statuses = [metric.status for metric in metrics]
        
        if 'critical' in statuses:
            return 'critical'
        elif 'warning' in statuses:
            return 'warning'
        else:
            return 'healthy'

    def subscribe(self, callback) -> None:
        """Subscribe to health updates."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback) -> None:
        """Unsubscribe from health updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current health metrics."""
        current_metrics = {}
        for metric_name, history in self.metrics_history.items():
            if history:
                current_metrics[metric_name] = asdict(history[-1])
        
        return current_metrics

    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary."""
        current_metrics = self.get_current_metrics()
        active_alerts = [asdict(alert) for alert in self.active_alerts.values() if not alert.resolved]
        
        overall_status = 'healthy'
        if current_metrics:
            statuses = [metric['status'] for metric in current_metrics.values()]
            if 'critical' in statuses:
                overall_status = 'critical'
            elif 'warning' in statuses:
                overall_status = 'warning'
        
        return {
            "overall_status": overall_status,
            "metrics": current_metrics,
            "active_alerts": active_alerts,
            "alert_count": len(active_alerts),
            "timestamp": datetime.now().isoformat()
        }

    def get_metrics_history(self, metric_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get metrics history for a specific metric."""
        if metric_name not in self.metrics_history:
            return []
        
        history = self.metrics_history[metric_name][-limit:]
        return [asdict(metric) for metric in history]


# Global health monitor instance
health_monitor = SystemHealthMonitor()


async def start_health_monitor() -> None:
    """Start the global health monitor."""
    await health_monitor.start()


async def stop_health_monitor() -> None:
    """Stop the global health monitor."""
    await health_monitor.stop()


def get_health_summary() -> Dict[str, Any]:
    """Get current health summary."""
    return health_monitor.get_health_summary()


def subscribe_to_health_updates(callback) -> None:
    """Subscribe to health updates."""
    health_monitor.subscribe(callback)
