{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "pip install -r requirements.txt && python deployment/database_production_setup.py"}, "deploy": {"startCommand": "python app.py", "healthcheckPath": "/health", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "environments": {"production": {"variables": {"ENVIRONMENT": "production", "DEBUG": "false", "HOST": "0.0.0.0", "PORT": "$PORT", "PYTHONUNBUFFERED": "1"}}}}