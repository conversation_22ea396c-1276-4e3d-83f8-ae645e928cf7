#!/usr/bin/env python3
"""
Quick Start Money Circle Server
Minimal server startup for testing
"""

import asyncio
import logging
from aiohttp import web

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def start_server():
    """Start the server."""
    try:
        logger.info("🚀 Quick starting Money Circle...")
        
        # Import and create app
        from app import MoneyCircleApp
        app_instance = MoneyCircleApp('development')
        
        # Create web app
        app = await app_instance.create_app()
        
        # Start server
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', 8087)
        await site.start()
        
        logger.info("✅ Server running at http://localhost:8087")
        logger.info("🔐 Login: epinnox / securepass123")
        logger.info("💰 Live Trading: http://localhost:8087/live-trading")
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        raise

if __name__ == '__main__':
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("🛑 Server stopped")
