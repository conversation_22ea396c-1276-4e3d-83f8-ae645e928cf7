#!/usr/bin/env python3
"""
Test script to verify HTX WebSocket ping/pong fix.
"""

import asyncio
import json
import logging
import sys
import os
import gzip
import time
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HTXWebSocketTester:
    """Test HTX WebSocket connection with ping/pong handling."""
    
    def __init__(self):
        self.ping_count = 0
        self.pong_count = 0
        self.data_messages = 0
        self.connection_drops = 0
        self.start_time = None
        
    async def test_htx_connection(self, duration_seconds=60):
        """Test HTX WebSocket connection for specified duration."""
        print("🧪 TESTING HTX WEBSOCKET PING/PONG FIX")
        print("=" * 60)
        print(f"⏱️ Testing for {duration_seconds} seconds...")
        
        self.start_time = datetime.now()
        
        try:
            import websockets
            
            url = "wss://api-aws.huobi.pro/ws"
            print(f"🔄 Connecting to {url}...")
            
            # Use the same connection parameters as the fix
            async with websockets.connect(
                url,
                ping_interval=None,  # Disable built-in ping
                ping_timeout=None,
                close_timeout=10
            ) as websocket:
                print("✅ Connected to HTX WebSocket")
                
                # Subscribe to BTC ticker
                subscribe_msg = {
                    "sub": "market.btcusdt.detail",
                    "id": "test_btc"
                }
                await websocket.send(json.dumps(subscribe_msg))
                print("📡 Subscribed to BTCUSDT ticker")
                
                # Track connection health
                last_ping_time = 0
                test_start = time.time()
                
                async def timeout_handler():
                    """Handle test timeout."""
                    await asyncio.sleep(duration_seconds)
                    return "timeout"
                
                # Race between message handling and timeout
                timeout_task = asyncio.create_task(timeout_handler())
                
                try:
                    async for message in websocket:
                        # Check if timeout reached
                        if timeout_task.done():
                            break
                            
                        try:
                            # HTX sends gzipped data
                            try:
                                data = json.loads(gzip.decompress(message).decode('utf-8'))
                            except gzip.BadGzipFile:
                                # Try plain JSON
                                data = json.loads(message)
                            
                            # Handle ping/pong
                            if 'ping' in data:
                                self.ping_count += 1
                                ping_timestamp = data['ping']
                                last_ping_time = ping_timestamp
                                
                                # Respond with pong
                                pong_msg = {"pong": ping_timestamp}
                                await websocket.send(json.dumps(pong_msg))
                                self.pong_count += 1
                                
                                elapsed = time.time() - test_start
                                print(f"🏓 Ping/Pong #{self.ping_count} at {elapsed:.1f}s - timestamp: {ping_timestamp}")
                                continue
                            
                            # Handle subscription confirmations
                            if 'subbed' in data:
                                print(f"✅ Subscription confirmed: {data.get('subbed')}")
                                continue
                            
                            # Handle error messages
                            if 'status' in data and data['status'] == 'error':
                                print(f"❌ Subscription error: {data.get('err-msg', 'Unknown error')}")
                                continue
                            
                            # Handle market data
                            if 'ch' in data and 'tick' in data:
                                self.data_messages += 1
                                channel = data['ch']
                                tick = data['tick']
                                
                                if self.data_messages <= 3:  # Show first few messages
                                    print(f"📊 Market data #{self.data_messages}: {channel}")
                                    print(f"   Price: {tick.get('close', 'N/A')}")
                                    print(f"   Volume: {tick.get('vol', 'N/A')}")
                                elif self.data_messages % 10 == 0:  # Show every 10th message
                                    elapsed = time.time() - test_start
                                    print(f"📊 Received {self.data_messages} data messages at {elapsed:.1f}s")
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON decode error: {e}")
                        except Exception as e:
                            print(f"❌ Error processing message: {e}")
                
                finally:
                    timeout_task.cancel()
                
        except websockets.exceptions.ConnectionClosed as e:
            print(f"⚠️ Connection closed: {e}")
            self.connection_drops += 1
        except Exception as e:
            print(f"❌ Connection error: {e}")
            self.connection_drops += 1
        
        # Print test results
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "=" * 60)
        print("📊 HTX WEBSOCKET TEST RESULTS")
        print("=" * 60)
        print(f"⏱️ Test duration: {duration:.1f} seconds")
        print(f"🏓 Ping messages received: {self.ping_count}")
        print(f"🏓 Pong responses sent: {self.pong_count}")
        print(f"📊 Market data messages: {self.data_messages}")
        print(f"💔 Connection drops: {self.connection_drops}")
        
        # Calculate ping frequency
        if self.ping_count > 1 and duration > 0:
            ping_interval = duration / self.ping_count
            print(f"⏰ Average ping interval: {ping_interval:.1f} seconds")
        
        # Determine success
        success = (
            self.ping_count > 0 and  # Received pings
            self.pong_count == self.ping_count and  # Responded to all pings
            self.connection_drops == 0 and  # No connection drops
            duration >= duration_seconds * 0.9  # Ran for most of the duration
        )
        
        if success:
            print("\n🎉 HTX WEBSOCKET TEST PASSED!")
            print("✅ Ping/pong mechanism working correctly")
            print("✅ No connection drops detected")
            print("✅ Stable connection maintained")
        else:
            print("\n⚠️ HTX WEBSOCKET TEST ISSUES DETECTED")
            if self.ping_count == 0:
                print("❌ No ping messages received")
            if self.pong_count != self.ping_count:
                print("❌ Pong responses don't match ping count")
            if self.connection_drops > 0:
                print("❌ Connection drops occurred")
            if duration < duration_seconds * 0.9:
                print("❌ Test ended prematurely")
        
        return success

async def test_advanced_market_data_manager():
    """Test the fixed Advanced Market Data Manager."""
    print("\n🧪 TESTING ADVANCED MARKET DATA MANAGER")
    print("=" * 60)
    
    try:
        from market_data.advanced_market_data_manager import AdvancedMarketDataManager
        
        # Initialize with test configuration
        config = {
            'symbols': ['BTC/USDT', 'ETH/USDT'],
            'update_interval': 1.0,
            'max_trade_history': 100
        }
        
        print("🔄 Initializing Advanced Market Data Manager...")
        manager = AdvancedMarketDataManager(config)
        
        print("🚀 Starting HTX WebSocket connection...")
        
        # Start only HTX connection for testing
        htx_task = asyncio.create_task(manager._start_htx_websocket())
        
        # Let it run for 30 seconds
        await asyncio.sleep(30)
        
        # Stop the manager
        manager.running = False
        htx_task.cancel()
        
        print("✅ Advanced Market Data Manager test completed")
        return True
        
    except ImportError as e:
        print(f"❌ Could not import Advanced Market Data Manager: {e}")
        return False
    except Exception as e:
        print(f"❌ Advanced Market Data Manager test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🛠️ HTX WEBSOCKET PING/PONG FIX VERIFICATION")
    print("=" * 60)
    print("This tool verifies that the HTX 'ping check expired' issue is fixed")
    print()
    
    # Get test duration from user
    try:
        duration_input = input("⏱️ Enter test duration in seconds (default: 60): ").strip()
        duration = int(duration_input) if duration_input else 60
        
        if duration < 10:
            print("⚠️ Minimum duration is 10 seconds")
            duration = 10
        elif duration > 300:
            print("⚠️ Maximum duration is 300 seconds (5 minutes)")
            duration = 300
            
    except ValueError:
        print("⚠️ Invalid input, using default duration of 60 seconds")
        duration = 60
    
    # Test 1: Direct WebSocket connection
    print("\n1️⃣ TESTING DIRECT HTX WEBSOCKET CONNECTION")
    print("-" * 60)
    
    tester = HTXWebSocketTester()
    direct_success = await tester.test_htx_connection(duration)
    
    # Test 2: Advanced Market Data Manager
    print("\n2️⃣ TESTING ADVANCED MARKET DATA MANAGER")
    print("-" * 60)
    
    manager_success = await test_advanced_market_data_manager()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 60)
    
    if direct_success and manager_success:
        print("🎉 HTX WEBSOCKET FIX VERIFICATION PASSED!")
        print("✅ Direct WebSocket connection stable")
        print("✅ Advanced Market Data Manager working")
        print("✅ Ping/pong mechanism functioning correctly")
        print("✅ No more 'ping check expired' errors expected")
        print("\n🚀 The Smart Trader system HTX connection is now stable!")
        return 0
    else:
        print("❌ HTX WEBSOCKET FIX VERIFICATION FAILED!")
        if not direct_success:
            print("❌ Direct WebSocket connection issues")
        if not manager_success:
            print("❌ Advanced Market Data Manager issues")
        print("\n🔧 Additional debugging may be required")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
