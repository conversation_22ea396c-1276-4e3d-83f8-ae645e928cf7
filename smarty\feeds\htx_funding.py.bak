"""
HTX Funding Rate client for the smart-trader system.

This module fetches funding rate data from HTX Futures API.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List

import aiohttp

from core.events import FundingRateEvent
from core.utils import retry_async

logger = logging.getLogger(__name__)


class HTXFundingClient:
    """
    HTX Funding Rate client.

    Fetches funding rate data from HTX Futures API.
    """

    # API endpoints
    REST_BASE_URL = "https://api.hbdm.com"
    FUNDING_RATE_ENDPOINT = "/linear-swap-api/v1/swap_funding_rate"
    HISTORICAL_FUNDING_ENDPOINT = "/linear-swap-api/v1/swap_historical_funding_rate"

    def __init__(
        self,
        session: Optional[aiohttp.ClientSession] = None,
        testnet: bool = False
    ):
        """
        Initialize the HTX Funding Rate client.

        Args:
            session: aiohttp session to use for requests
            testnet: Whether to use testnet
        """
        self.session = session
        self.testnet = testnet

        # Cache for funding rates
        self._funding_cache: Dict[str, Dict[str, Any]] = {}

    async def fetch_funding_rate(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetch current funding rate for a symbol.

        Args:
            symbol: Trading symbol (e.g., "BTC-USDT")

        Returns:
            Dictionary with funding rate data or None if failed
        """
        # Check if we have cached data
        if symbol in self._funding_cache:
            cache_entry = self._funding_cache[symbol]
            cache_age = datetime.now(timezone.utc) - cache_entry["timestamp"]
            # If cache is less than 5 minutes old, use it
            if cache_age.total_seconds() < 300:
                logger.debug(f"Using cached funding rate for {symbol}")
                # Return a JSON-serializable dictionary
                return {
                    "symbol": symbol,
                    "timestamp": cache_entry["timestamp"].isoformat(),
                    "rate": cache_entry["funding_rate"],
                    "next_funding_time": cache_entry["next_funding_time"],
                    "estimated_rate": cache_entry["estimated_rate"]
                }

        try:
            # Create session if needed
            if self.session is None or self.session.closed:
                self.session = aiohttp.ClientSession()

            # Build URL
            url = f"{self.REST_BASE_URL}{self.FUNDING_RATE_ENDPOINT}"

            # Prepare parameters
            params = {"contract_code": symbol}

            # Make request
            try:
                async with self.session.get(url, params=params, timeout=10) as response:
                    if response.status != 200:
                        logger.error(f"Failed to fetch funding rate: {response.status}, message='{await response.text()}', url='{url}'")
                        # Use cached data if available
                        if symbol in self._funding_cache:
                            logger.warning(f"Using cached funding rate for {symbol} due to API error")
                            cache_entry = self._funding_cache[symbol]
                            # Return a JSON-serializable dictionary
                            return {
                                "symbol": symbol,
                                "timestamp": cache_entry["timestamp"].isoformat(),
                                "rate": cache_entry["funding_rate"],
                                "next_funding_time": cache_entry["next_funding_time"],
                                "estimated_rate": cache_entry["estimated_rate"]
                            }
                        else:
                            # Return default values as a JSON-serializable dictionary
                            logger.warning(f"Using default funding rate for {symbol}")
                            return {
                                "symbol": symbol,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "rate": 0.0,
                                "next_funding_time": "",
                                "estimated_rate": 0.0
                            }

                    # Try to parse JSON response
                    try:
                        # First try standard JSON parsing
                        try:
                            data = await response.json()
                        except aiohttp.ContentTypeError:
                            # If content type error, try to parse text as JSON
                            text = await response.text()
                            logger.debug(f"Response text: {text[:200]}...")
                            data = json.loads(text)
                    except Exception as e:
                        logger.error(f"Error parsing funding rate JSON response: {response.status}, message='{e}', url='{url}', content_type='{response.content_type}'")
                        # Use cached data if available
                        if symbol in self._funding_cache:
                            logger.warning(f"Using cached funding rate for {symbol} due to parsing error")
                            cache_entry = self._funding_cache[symbol]
                            # Return a JSON-serializable dictionary
                            return {
                                "symbol": symbol,
                                "timestamp": cache_entry["timestamp"].isoformat(),
                                "rate": cache_entry["funding_rate"],
                                "next_funding_time": cache_entry["next_funding_time"],
                                "estimated_rate": cache_entry["estimated_rate"]
                            }
                        else:
                            # Return default values as a JSON-serializable dictionary
                            logger.warning(f"Using default funding rate for {symbol}")
                            return {
                                "symbol": symbol,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "rate": 0.0,
                                "next_funding_time": "",
                                "estimated_rate": 0.0
                            }

                    # Check for errors
                    if data.get("status") != "ok":
                        error_msg = data.get('err_msg', 'Unknown error')
                        logger.error(f"API error: {error_msg}")
                        # Use cached data if available
                        if symbol in self._funding_cache:
                            logger.warning(f"Using cached funding rate for {symbol} due to API status error")
                            cache_entry = self._funding_cache[symbol]
                            # Return a JSON-serializable dictionary
                            return {
                                "symbol": symbol,
                                "timestamp": cache_entry["timestamp"].isoformat(),
                                "rate": cache_entry["funding_rate"],
                                "next_funding_time": cache_entry["next_funding_time"],
                                "estimated_rate": cache_entry["estimated_rate"]
                            }
                        else:
                            # Return default values as a JSON-serializable dictionary
                            logger.warning(f"Using default funding rate for {symbol}")
                            return {
                                "symbol": symbol,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "rate": 0.0,
                                "next_funding_time": "",
                                "estimated_rate": 0.0
                            }

                    # Extract funding rate data
                    funding_data = data.get("data", [])
                    if not funding_data:
                        logger.warning(f"No funding rate data for {symbol}")
                        # Use cached data if available
                        if symbol in self._funding_cache:
                            logger.warning(f"Using cached funding rate for {symbol} due to empty data")
                            cache_entry = self._funding_cache[symbol]
                            # Return a JSON-serializable dictionary
                            return {
                                "symbol": symbol,
                                "timestamp": cache_entry["timestamp"].isoformat(),
                                "rate": cache_entry["funding_rate"],
                                "next_funding_time": cache_entry["next_funding_time"],
                                "estimated_rate": cache_entry["estimated_rate"]
                            }
                        else:
                            # Return default values as a JSON-serializable dictionary
                            logger.warning(f"Using default funding rate for {symbol}")
                            return {
                                "symbol": symbol,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "rate": 0.0,
                                "next_funding_time": "",
                                "estimated_rate": 0.0
                            }

                    # Get the first (and usually only) entry
                    entry = funding_data[0]

                    # Extract fields
                    funding_rate = float(entry.get("funding_rate", 0.0))
                    timestamp_str = entry.get("funding_time", "")

                    # Parse timestamp
                    if timestamp_str:
                        try:
                            # Convert timestamp string to datetime
                            timestamp = datetime.fromtimestamp(int(timestamp_str) / 1000, tz=timezone.utc)
                        except (ValueError, TypeError):
                            timestamp = datetime.now(timezone.utc)
                    else:
                        timestamp = datetime.now(timezone.utc)

                    # Create a JSON-serializable dictionary
                    result = {
                        "symbol": symbol,
                        "timestamp": timestamp.isoformat(),  # Convert datetime to string
                        "rate": funding_rate,
                        "next_funding_time": entry.get("next_funding_time", ""),
                        "estimated_rate": float(entry.get("estimated_rate", 0.0))
                    }

                    # Update cache with the original datetime object
                    self._funding_cache[symbol] = {
                        "funding_rate": funding_rate,
                        "timestamp": timestamp,
                        "next_funding_time": entry.get("next_funding_time", ""),
                        "estimated_rate": float(entry.get("estimated_rate", 0.0))
                    }

                    # Log successful funding rate fetch
                    logger.info(f"Successfully fetched funding rate for {symbol}: {funding_rate}")
                    
                    return result
            except aiohttp.ClientConnectorError as e:
                logger.error(f"Connection error fetching funding rate: {e}")
                # Use cached data if available
                if symbol in self._funding_cache:
                    logger.warning(f"Using cached funding rate for {symbol} due to connection error")
                    cache_entry = self._funding_cache[symbol]
                    # Return a JSON-serializable dictionary
                    return {
                        "symbol": symbol,
                        "timestamp": cache_entry["timestamp"].isoformat(),
                        "rate": cache_entry["funding_rate"],
                        "next_funding_time": cache_entry["next_funding_time"],
                        "estimated_rate": cache_entry["estimated_rate"]
                    }
                else:
                    # Return default values as a JSON-serializable dictionary
                    logger.warning(f"Using default funding rate for {symbol}")
                    return {
                        "symbol": symbol,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "rate": 0.0,
                        "next_funding_time": "",
                        "estimated_rate": 0.0
                    }
            except asyncio.TimeoutError as e:
                logger.error(f"Timeout error fetching funding rate: {e}")
                # Use cached data if available
                if symbol in self._funding_cache:
                    logger.warning(f"Using cached funding rate for {symbol} due to timeout")
                    cache_entry = self._funding_cache[symbol]
                    # Return a JSON-serializable dictionary
                    return {
                        "symbol": symbol,
                        "timestamp": cache_entry["timestamp"].isoformat(),
                        "rate": cache_entry["funding_rate"],
                        "next_funding_time": cache_entry["next_funding_time"],
                        "estimated_rate": cache_entry["estimated_rate"]
                    }
                else:
                    # Return default values as a JSON-serializable dictionary
                    logger.warning(f"Using default funding rate for {symbol}")
                    return {
                        "symbol": symbol,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "rate": 0.0,
                        "next_funding_time": "",
                        "estimated_rate": 0.0
                    }

        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            logger.error(f"Error fetching funding rate: {error_type} - {error_msg}")
            
            # Try to make a direct API call to diagnose the issue
            try:
                import requests
                url = f"{self.REST_BASE_URL}{self.FUNDING_RATE_ENDPOINT}"
                params = {"contract_code": symbol}
                response = requests.get(url, params=params, timeout=10)
                logger.info(f"Direct API call status: {response.status_code}, response: {response.text[:200]}")
            except Exception as direct_e:
                logger.error(f"Direct API call also failed: {str(direct_e)}")
            
            # Use cached data if available
            if symbol in self._funding_cache:
                logger.warning(f"Using cached funding rate for {symbol} due to exception: {error_type} - {error_msg}")
                cache_entry = self._funding_cache[symbol]
                # Return a JSON-serializable dictionary
                return {
                    "symbol": symbol,
                    "timestamp": cache_entry["timestamp"].isoformat(),
                    "rate": cache_entry["funding_rate"],
                    "next_funding_time": cache_entry["next_funding_time"],
                    "estimated_rate": cache_entry["estimated_rate"]
                }
            else:
                # Return default values as a JSON-serializable dictionary
                logger.warning(f"Using default funding rate for {symbol}")
                return {
                    "symbol": symbol,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "rate": 0.0,
                    "next_funding_time": "",
                    "estimated_rate": 0.0
                }

    async def fetch_historical_funding_rates(
        self,
        symbol: str,
        size: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Fetch historical funding rates for a symbol.

        Args:
            symbol: Trading symbol (e.g., "BTC-USDT")
            size: Number of historical entries to fetch (max 100)

        Returns:
            List of dictionaries with funding rate data
        """
        try:
            # Create session if needed
            if self.session is None or self.session.closed:
                self.session = aiohttp.ClientSession()

            # Build URL
            url = f"{self.REST_BASE_URL}{self.HISTORICAL_FUNDING_ENDPOINT}"

            # Prepare parameters
            params = {
                "contract_code": symbol,
                "page_size": min(size, 100),  # API limit is 100
                "page_index": 1
            }

            # Make request
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"Failed to fetch historical funding rates: {response.status}, message='{await response.text()}', url='{url}'")
                    return []

                # Try to parse JSON response
                try:
                    # First try standard JSON parsing
                    try:
                        data = await response.json()
                    except aiohttp.ContentTypeError:
                        # If content type error, try to parse text as JSON
                        text = await response.text()
                        logger.debug(f"Response text: {text[:200]}...")
                        data = json.loads(text)
                except Exception as e:
                    logger.error(f"Error parsing historical funding rates JSON response: {response.status}, message='{e}', url='{url}', content_type='{response.content_type}'")
                    return []

                # Check for errors
                if data.get("status") != "ok":
                    logger.error(f"API error: {data.get('err_msg', 'Unknown error')}")
                    return []

                # Extract funding rate data
                funding_data = data.get("data", {}).get("data", [])
                if not funding_data:
                    logger.warning(f"No historical funding rate data for {symbol}")
                    return []

                # Parse data into events
                events = []
                for entry in funding_data:
                    funding_rate = float(entry.get("funding_rate", 0.0))
                    timestamp_str = entry.get("funding_time", "")

                    # Parse timestamp
                    if timestamp_str:
                        try:
                            # Convert timestamp string to datetime
                            timestamp = datetime.fromtimestamp(int(timestamp_str) / 1000, tz=timezone.utc)
                        except (ValueError, TypeError):
                            timestamp = datetime.now(timezone.utc)
                    else:
                        timestamp = datetime.now(timezone.utc)

                    # Create a JSON-serializable dictionary
                    event_data = {
                        "symbol": symbol,
                        "timestamp": timestamp.isoformat(),  # Convert datetime to string
                        "rate": funding_rate,
                        "next_funding_time": "",
                        "estimated_rate": 0.0
                    }

                    events.append(event_data)

                return events

        except Exception as e:
            logger.error(f"Error fetching historical funding rates: {e}")
            return []

    async def close(self) -> None:
        """
        Close the client session.
        """
        if self.session and not self.session.closed:
            await self.session.close()


async def fetch_funding_rate(
    symbol: str,
    session: Optional[aiohttp.ClientSession] = None,
    testnet: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Fetch current funding rate for a symbol.

    This is a convenience function that creates a client and fetches the funding rate.

    Args:
        symbol: Trading symbol (e.g., "BTC-USDT")
        session: aiohttp session to use for requests
        testnet: Whether to use testnet

    Returns:
        Dictionary with funding rate data or None if failed
    """
    client = HTXFundingClient(session=session, testnet=testnet)

    try:
        return await retry_async(
            client.fetch_funding_rate,
            symbol,
            retries=3,
            delay=1.0
        )
    finally:
        await client.close()


async def fetch_historical_funding_rates(
    symbol: str,
    size: int = 20,
    session: Optional[aiohttp.ClientSession] = None,
    testnet: bool = False
) -> List[Dict[str, Any]]:
    """
    Fetch historical funding rates for a symbol.

    This is a convenience function that creates a client and fetches historical funding rates.

    Args:
        symbol: Trading symbol (e.g., "BTC-USDT")
        size: Number of historical entries to fetch (max 100)
        session: aiohttp session to use for requests
        testnet: Whether to use testnet

    Returns:
        List of dictionaries with funding rate data
    """
    client = HTXFundingClient(session=session, testnet=testnet)

    try:
        return await retry_async(
            client.fetch_historical_funding_rates,
            symbol,
            size,
            retries=3,
            delay=1.0
        )
    finally:
        await client.close()
