#!/usr/bin/env python3
"""
Trading API Endpoints for Money Circle
Handles real-time trading operations, position management, and risk controls.
"""

from aiohttp import web
import json
import asyncio
from datetime import datetime, timedelta
import random
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class TradingAPI:
    """Enhanced trading API with comprehensive controls."""

    def __init__(self, db_manager):
        self.db_manager = db_manager

    async def market_order(self, request):
        """Execute market order."""
        try:
            data = await request.json()
            user_id = request.get('user_id', 1)  # From session middleware, default to 1 for demo

            # Validate required fields
            required_fields = ['symbol', 'side', 'amount', 'exchange']
            for field in required_fields:
                if field not in data:
                    return web.json_response({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }, status=400)

            symbol = data['symbol']
            side = data['side']
            amount = float(data['amount'])
            exchange = data['exchange']
            strategy_name = data.get('strategy_name', 'Manual_Trade')

            # Simulate market order execution
            market_price = await self._get_market_price(symbol)
            if not market_price:
                return web.json_response({
                    'success': False,
                    'error': 'Unable to fetch market price'
                }, status=500)

            # Calculate trade details
            trade_value = amount * market_price
            fee = trade_value * 0.001  # 0.1% fee

            # Execute trade (simulate for demo)
            trade_id = await self._execute_trade(
                user_id, exchange, symbol, side, amount,
                market_price, fee, 'market', strategy_name
            )

            if trade_id:
                return web.json_response({
                    'success': True,
                    'trade_id': trade_id,
                    'price': market_price,
                    'amount': amount,
                    'fee': fee,
                    'total': trade_value + fee
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Trade execution failed'
                }, status=500)

        except Exception as e:
            logger.error(f"Market order error: {e}")
            return web.json_response({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

    async def limit_order(self, request):
        """Execute limit order."""
        try:
            data = await request.json()
            user_id = request.get('user_id')

            # Validate required fields
            required_fields = ['symbol', 'side', 'amount', 'price', 'exchange']
            for field in required_fields:
                if field not in data:
                    return web.json_response({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }, status=400)

            symbol = data['symbol']
            side = data['side']
            amount = float(data['amount'])
            price = float(data['price'])
            exchange = data['exchange']
            strategy_name = data.get('strategy_name', 'Manual_Limit')

            # Calculate trade details
            trade_value = amount * price
            fee = trade_value * 0.001

            # Execute limit order (simulate for demo)
            trade_id = await self._execute_trade(
                user_id, exchange, symbol, side, amount,
                price, fee, 'limit', strategy_name
            )

            if trade_id:
                return web.json_response({
                    'success': True,
                    'trade_id': trade_id,
                    'price': price,
                    'amount': amount,
                    'fee': fee,
                    'total': trade_value + fee
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Limit order placement failed'
                }, status=500)

        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return web.json_response({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

    async def close_position(self, request):
        """Close an open position."""
        try:
            data = await request.json()
            user_id = request.get('user_id')

            position_id = data.get('position_id')
            symbol = data.get('symbol')

            if not position_id or not symbol:
                return web.json_response({
                    'success': False,
                    'error': 'Missing position_id or symbol'
                }, status=400)

            # Get position details
            position = await self._get_position(user_id, position_id)
            if not position:
                return web.json_response({
                    'success': False,
                    'error': 'Position not found'
                }, status=404)

            # Calculate closing trade
            market_price = await self._get_market_price(symbol)
            close_side = 'sell' if position['side'] == 'long' else 'buy'

            # Execute closing trade
            trade_id = await self._execute_trade(
                user_id, position['exchange_name'], symbol, close_side,
                position['size'], market_price, market_price * position['size'] * 0.001,
                'market', 'Position_Close'
            )

            if trade_id:
                # Update position status
                await self._close_position(position_id)

                return web.json_response({
                    'success': True,
                    'trade_id': trade_id,
                    'close_price': market_price
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to close position'
                }, status=500)

        except Exception as e:
            logger.error(f"Close position error: {e}")
            return web.json_response({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

    async def get_position(self, request):
        """Get position details."""
        try:
            user_id = request.get('user_id')
            position_id = request.match_info['position_id']

            position = await self._get_position(user_id, position_id)
            if position:
                return web.json_response(position)
            else:
                return web.json_response({
                    'error': 'Position not found'
                }, status=404)

        except Exception as e:
            logger.error(f"Get position error: {e}")
            return web.json_response({
                'error': 'Internal server error'
            }, status=500)

    async def market_data(self, request):
        """Get market data for symbol."""
        try:
            symbol = request.match_info['symbol']

            # Simulate market data (in production, fetch from exchange APIs)
            base_prices = {
                'BTCUSDT': 50000,
                'ETHUSDT': 3000,
                'ADAUSDT': 0.5,
                'SOLUSDT': 100,
                'DOTUSDT': 8
            }

            base_price = base_prices.get(symbol, 100)
            current_price = base_price * random.uniform(0.98, 1.02)

            return web.json_response({
                'symbol': symbol,
                'price': round(current_price, 4),
                'change_24h': random.uniform(-5, 5),
                'volume_24h': random.uniform(1000000, 10000000),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Market data error: {e}")
            return web.json_response({
                'error': 'Internal server error'
            }, status=500)

    async def global_stop_loss(self, request):
        """Set global stop loss."""
        try:
            data = await request.json()
            user_id = request.get('user_id')
            stop_loss_percent = data.get('stop_loss_percent')

            if not stop_loss_percent:
                return web.json_response({
                    'success': False,
                    'error': 'Missing stop_loss_percent'
                }, status=400)

            # Store global stop loss setting (simulate for demo)
            # In production, this would update user settings and trigger monitoring

            return web.json_response({
                'success': True,
                'message': f'Global stop loss set to {stop_loss_percent}%'
            })

        except Exception as e:
            logger.error(f"Global stop loss error: {e}")
            return web.json_response({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

    async def emergency_close_all(self, request):
        """Emergency close all positions."""
        try:
            user_id = request.get('user_id')

            # Get all open positions
            positions = await self._get_user_positions(user_id)
            closed_count = 0

            for position in positions:
                if position['status'] == 'open':
                    # Close position
                    market_price = await self._get_market_price(position['symbol'])
                    close_side = 'sell' if position['side'] == 'long' else 'buy'

                    trade_id = await self._execute_trade(
                        user_id, position['exchange_name'], position['symbol'],
                        close_side, position['size'], market_price,
                        market_price * position['size'] * 0.001,
                        'market', 'Emergency_Close'
                    )

                    if trade_id:
                        await self._close_position(position['id'])
                        closed_count += 1

            return web.json_response({
                'success': True,
                'closed_positions': closed_count
            })

        except Exception as e:
            logger.error(f"Emergency close all error: {e}")
            return web.json_response({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

    # Helper methods
    async def _get_market_price(self, symbol):
        """Get current market price for symbol."""
        base_prices = {
            'BTCUSDT': 50000,
            'ETHUSDT': 3000,
            'ADAUSDT': 0.5,
            'SOLUSDT': 100,
            'DOTUSDT': 8
        }
        base_price = base_prices.get(symbol, 100)
        return base_price * random.uniform(0.99, 1.01)

    async def _execute_trade(self, user_id, exchange, symbol, side, size, price, fee, order_type, strategy_name):
        """Execute trade and store in database."""
        try:
            trade_data = {
                'user_id': user_id,
                'exchange_name': exchange,
                'symbol': symbol,
                'side': side,
                'size': size,
                'price': price,
                'fee': fee,
                'order_type': order_type,
                'strategy_name': strategy_name,
                'timestamp': datetime.now().isoformat()
            }

            # Insert trade into database
            query = """
                INSERT INTO user_trades (
                    user_id, exchange_name, symbol, side, size, price, fee,
                    order_type, strategy_name, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            cursor = self.db_manager.execute(query, (
                user_id, exchange, symbol, side, size, price, fee,
                order_type, strategy_name, trade_data['timestamp']
            ))

            return cursor.lastrowid

        except Exception as e:
            logger.error(f"Execute trade error: {e}")
            return None

    async def _get_position(self, user_id, position_id):
        """Get position by ID."""
        try:
            query = """
                SELECT * FROM user_positions
                WHERE user_id = ? AND id = ?
            """
            cursor = self.db_manager.execute(query, (user_id, position_id))
            row = cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            return None

        except Exception as e:
            logger.error(f"Get position error: {e}")
            return None

    async def _get_user_positions(self, user_id):
        """Get all positions for user."""
        try:
            query = """
                SELECT * FROM user_positions
                WHERE user_id = ? AND status = 'open'
            """
            cursor = self.db_manager.execute(query, (user_id,))
            rows = cursor.fetchall()

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"Get user positions error: {e}")
            return []

    async def _close_position(self, position_id):
        """Mark position as closed."""
        try:
            query = """
                UPDATE user_positions
                SET status = 'closed', updated_at = ?
                WHERE id = ?
            """
            self.db_manager.execute(query, (datetime.now().isoformat(), position_id))
            return True

        except Exception as e:
            logger.error(f"Close position error: {e}")
            return False
