{% extends "base.html" %}

{% block title %}Money Circle - Club Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/club.css">
<link rel="stylesheet" href="/static/css/dashboard.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="club-dashboard-container">
    <!-- Club Dashboard Grid -->
    <div class="club-dashboard-grid">
        <!-- Club Overview -->
        <section class="club-overview">
        <h2>🏛️ Club Overview</h2>
        <div class="overview-cards">
            <div class="overview-card">
                <h3>Total Members</h3>
                <div class="value">{{ club_stats.total_members }}</div>
            </div>
            <div class="overview-card">
                <h3>Active Strategies</h3>
                <div class="value">{{ club_stats.active_strategies }}</div>
            </div>
            <div class="overview-card">
                <h3>Club Performance</h3>
                <div class="value {{ 'positive' if club_stats.performance >= 0 else 'negative' }}">
                    {{ '+' if club_stats.performance >= 0 else '' }}{{ club_stats.performance|round(2) }}%
                </div>
            </div>
            <div class="overview-card">
                <h3>Total Volume</h3>
                <div class="value">${{ club_stats.total_volume|round(2) }}</div>
            </div>
        </div>
    </section>

    <!-- Strategy Governance -->
    <section class="strategy-governance">
        <h2>⚖️ Strategy Governance</h2>

        {% if user.role == 'admin' and pending_strategies %}
        <div class="admin-review-section">
            <h3>⚖️ Admin Review Queue</h3>
            <div class="pending-strategies">
                {% for strategy in pending_strategies %}
                <div class="strategy-review-card">
                    <h4>{{ strategy.name }}</h4>
                    <p>{{ strategy.description }}</p>
                    <div class="strategy-meta">
                        <span>Proposed by: {{ strategy.proposer_name }}</span>
                        <span>{{ strategy.created_at }}</span>
                    </div>
                    <div class="review-actions">
                        <button onclick="approveStrategy({{ strategy.id }})" class="approve-btn">
                            ✅ Approve
                        </button>
                        <button onclick="rejectStrategy({{ strategy.id }})" class="reject-btn">
                            ❌ Reject
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Active Votes -->
        <div class="active-votes">
            <h3>🗳️ Active Votes</h3>
            {% if active_votes %}
            <div class="votes-list">
                {% for vote in active_votes %}
                <div class="vote-card">
                    <h4>{{ vote.strategy_name }}</h4>
                    <p>{{ vote.description }}</p>
                    <div class="vote-progress">
                        <div class="vote-bar">
                            <div class="vote-yes" style="width: {{ vote.yes_percentage }}%"></div>
                        </div>
                        <div class="vote-stats">
                            <span>{{ vote.yes_votes }} Yes</span>
                            <span>{{ vote.no_votes }} No</span>
                            <span>{{ vote.total_votes }} Total</span>
                        </div>
                    </div>
                    <div class="vote-actions">
                        <button onclick="castVote({{ vote.id }}, 'yes')" class="vote-btn yes">
                            👍 Vote Yes
                        </button>
                        <button onclick="castVote({{ vote.id }}, 'no')" class="vote-btn no">
                            👎 Vote No
                        </button>
                    </div>
                    <div class="vote-deadline">
                        Deadline: {{ vote.deadline }}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">No active votes at this time</div>
            {% endif %}
        </div>

        <!-- Propose Strategy -->
        <div class="propose-strategy">
            <h3>💡 Propose New Strategy</h3>
            <button onclick="showProposeModal()" class="propose-btn">
                ➕ Propose Strategy
            </button>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
        <h2>📈 Recent Activity</h2>
        <div class="activity-feed" id="activity-feed">
            {% for activity in recent_activities %}
            <div class="activity-item">
                <div class="activity-icon">{{ activity.icon }}</div>
                <div class="activity-content">
                    <div class="activity-text">{{ activity.text }}</div>
                    <div class="activity-time">{{ activity.timestamp }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- Performance Charts -->
    <section class="performance-charts">
        <h2>📊 Performance Charts</h2>
        <div class="charts-grid">
            <div class="chart-container">
                <h3>Club Performance</h3>
                <canvas id="performance-chart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Strategy Distribution</h3>
                <canvas id="strategy-chart"></canvas>
            </div>
        </div>
    </section>

    <!-- Top Performers -->
    <section class="top-performers">
        <h2>🏆 Top Performers</h2>
        <div class="performers-grid">
            <div class="performer-category">
                <h3>Top Strategies</h3>
                <div class="performers-list">
                    {% for strategy in top_strategies %}
                    <div class="performer-item">
                        <span class="rank">#{{ loop.index }}</span>
                        <span class="name">{{ strategy.name }}</span>
                        <span class="performance {{ 'positive' if strategy.performance >= 0 else 'negative' }}">
                            {{ '+' if strategy.performance >= 0 else '' }}{{ strategy.performance|round(2) }}%
                        </span>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="performer-category">
                <h3>Top Members</h3>
                <div class="performers-list">
                    {% for member in top_members %}
                    <div class="performer-item">
                        <span class="rank">#{{ loop.index }}</span>
                        <span class="name">{{ member.username }}</span>
                        <span class="performance {{ 'positive' if member.performance >= 0 else 'negative' }}">
                            {{ '+' if member.performance >= 0 else '' }}{{ member.performance|round(2) }}%
                        </span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>

    <!-- Notifications -->
    <section class="notifications">
        <h2>🔔 Recent Notifications</h2>
        <div class="notification-list" id="notification-list">
            {% for notification in notifications %}
            <div class="notification-item {{ 'unread' if not notification.read else '' }}">
                <div class="notification-icon">{{ notification.icon }}</div>
                <div class="notification-content">
                    <div class="notification-text">{{ notification.text }}</div>
                    <div class="notification-time">{{ notification.timestamp }}</div>
                </div>
                {% if not notification.read %}
                <button onclick="markNotificationRead({{ notification.id }})" class="mark-read-btn">
                    ✓
                </button>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </section>
    </div> <!-- End club-dashboard-grid -->
</div>

<!-- Modals -->
<div id="propose-strategy-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('propose-strategy-modal')">&times;</span>
        <h2>Propose New Strategy</h2>
        <form id="propose-strategy-form">
            <div class="form-group">
                <label>Strategy Name</label>
                <input type="text" id="strategy-name" required>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea id="strategy-description" rows="4" required></textarea>
            </div>
            <div class="form-group">
                <label>Strategy Type</label>
                <select id="strategy-type" required>
                    <option value="">Select Type</option>
                    <option value="momentum">Momentum</option>
                    <option value="mean_reversion">Mean Reversion</option>
                    <option value="arbitrage">Arbitrage</option>
                    <option value="market_making">Market Making</option>
                    <option value="trend_following">Trend Following</option>
                </select>
            </div>
            <div class="form-group">
                <label>Risk Level</label>
                <select id="risk-level" required>
                    <option value="">Select Risk Level</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
            <div class="form-group">
                <label>Expected Return (%)</label>
                <input type="number" id="expected-return" step="0.1" min="0">
            </div>
            <div class="form-group">
                <label>Strategy Parameters (JSON)</label>
                <textarea id="strategy-parameters" rows="6" placeholder='{"param1": "value1", "param2": "value2"}'></textarea>
            </div>
            <button type="submit">Submit Proposal</button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/club_dashboard.js"></script>
<script>
    // Initialize club dashboard data
    window.clubData = {
        user: {{ user|tojson }},
        clubStats: {{ club_stats|tojson }},
        pendingStrategies: {{ pending_strategies|tojson }},
        activeVotes: {{ active_votes|tojson }},
        recentActivities: {{ recent_activities|tojson }},
        topStrategies: {{ top_strategies|tojson }},
        topMembers: {{ top_members|tojson }},
        notifications: {{ notifications|tojson }}
    };

    // Initialize club dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeClubDashboard();
    });
</script>
{% endblock %}
