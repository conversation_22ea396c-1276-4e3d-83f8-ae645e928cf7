{% extends "base.html" %}

{% block title %}Money Circle - Member Directory{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/club.css">
<link rel="stylesheet" href="/static/css/member_directory.css">
{% endblock %}

{% block content %}
<div class="club-dashboard-container">
    <!-- Member Directory Grid -->
    <div class="club-dashboard-grid">
        <!-- Directory Header -->
        <section class="directory-header">
        <div class="header-content">
            <h1>👥 Member Directory</h1>
            <p>Connect with fellow traders and investment professionals</p>
        </div>

        <!-- Member Stats -->
        <div class="member-stats">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <div class="stat-value">{{ member_stats.total_members }}</div>
                    <div class="stat-label">Total Members</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏆</div>
                <div class="stat-content">
                    <div class="stat-value">{{ member_stats.active_traders }}</div>
                    <div class="stat-label">Active Traders</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <div class="stat-value">{{ member_stats.avg_performance|round(1) }}%</div>
                    <div class="stat-label">Avg Performance</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💼</div>
                <div class="stat-content">
                    <div class="stat-value">{{ member_stats.total_trades }}</div>
                    <div class="stat-label">Total Trades</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="directory-filters">
        <div class="search-bar">
            <input type="text" id="member-search" placeholder="Search members..." onkeyup="filterMembers()">
            <button onclick="filterMembers()">🔍</button>
        </div>

        <div class="filter-controls">
            <select id="experience-filter" onchange="filterMembers()">
                <option value="">All Experience Levels</option>
                <option value="conservative">Conservative</option>
                <option value="moderate">Moderate</option>
                <option value="aggressive">Aggressive</option>
            </select>

            <select id="specialization-filter" onchange="filterMembers()">
                <option value="">All Specializations</option>
                <option value="day_trading">Day Trading</option>
                <option value="swing_trading">Swing Trading</option>
                <option value="scalping">Scalping</option>
                <option value="position_trading">Position Trading</option>
                <option value="arbitrage">Arbitrage</option>
            </select>

            <select id="performance-filter" onchange="filterMembers()">
                <option value="">All Performance</option>
                <option value="positive">Positive Only</option>
                <option value="top_10">Top 10%</option>
                <option value="top_25">Top 25%</option>
            </select>

            <select id="sort-filter" onchange="filterMembers()">
                <option value="performance">Sort by Performance</option>
                <option value="trades">Sort by Trade Count</option>
                <option value="recent">Sort by Recent Activity</option>
                <option value="alphabetical">Sort Alphabetically</option>
            </select>
        </div>
    </section>

    <!-- Top Performers -->
    <section class="top-performers">
        <h2>🏆 Top Performers</h2>
        <div class="leaderboard">
            {% for member in top_performers %}
            <div class="leaderboard-item">
                <div class="rank">
                    {% if loop.index == 1 %}🥇
                    {% elif loop.index == 2 %}🥈
                    {% elif loop.index == 3 %}🥉
                    {% else %}#{{ loop.index }}
                    {% endif %}
                </div>
                <div class="member-info">
                    <div class="member-name">{{ member.username }}</div>
                    <div class="member-title">{{ member.trading_style|title }} Trader</div>
                </div>
                <div class="member-metrics">
                    <div class="performance {{ 'positive' if member.total_pnl >= 0 else 'negative' }}">
                        {{ '+' if member.total_pnl >= 0 else '' }}{{ member.total_pnl|round(2) }}%
                    </div>
                    <div class="trades-count">{{ member.trades_count }} trades</div>
                </div>
                <div class="member-actions">
                    <button onclick="viewMemberProfile({{ member.id }})" class="btn-sm btn-secondary">
                        View Profile
                    </button>
                    {% if member.id not in user_connections %}
                    <button onclick="connectMember({{ member.id }})" class="btn-sm btn-primary">
                        Connect
                    </button>
                    {% else %}
                    <button onclick="disconnectMember({{ member.id }})" class="btn-sm btn-danger">
                        Disconnect
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- All Members -->
    <section class="all-members">
        <h2>📋 All Members</h2>
        <div class="members-grid" id="members-grid">
            {% for member in all_members %}
            <div class="member-card"
                 data-experience="{{ member.risk_tolerance }}"
                 data-specialization="{{ member.trading_style }}"
                 data-performance="{{ member.total_pnl }}"
                 data-name="{{ member.username|lower }}">

                <div class="member-header">
                    <div class="member-avatar">
                        <div class="avatar-circle">{{ member.username[0]|upper }}</div>
                        {% if member.is_online %}
                        <div class="online-indicator"></div>
                        {% endif %}
                    </div>
                    <div class="member-basic-info">
                        <h4>{{ member.username }}</h4>
                        <div class="member-role">{{ member.role|title }}</div>
                        <div class="member-joined">Joined {{ member.created_at.strftime('%b %Y') }}</div>
                    </div>
                </div>

                <div class="member-badges">
                    <span class="badge experience-badge exp-{{ member.risk_tolerance }}">
                        {{ member.risk_tolerance|title }}
                    </span>
                    <span class="badge specialization-badge">
                        {{ member.trading_style|replace('_', ' ')|title }}
                    </span>
                    {% if member.achievements %}
                    {% for achievement in member.achievements[:2] %}
                    <span class="badge achievement-badge">{{ achievement.name }}</span>
                    {% endfor %}
                    {% endif %}
                </div>

                <div class="member-stats">
                    <div class="stat-row">
                        <span class="stat-label">Performance:</span>
                        <span class="stat-value {{ 'positive' if member.total_pnl >= 0 else 'negative' }}">
                            {{ '+' if member.total_pnl >= 0 else '' }}{{ member.total_pnl|round(2) }}%
                        </span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Trades:</span>
                        <span class="stat-value">{{ member.trades_count }}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Win Rate:</span>
                        <span class="stat-value">{{ member.win_rate|round(1) }}%</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Reputation:</span>
                        <span class="stat-value">{{ member.reputation_score|round(1) }}</span>
                    </div>
                </div>

                {% if member.bio %}
                <div class="member-bio">
                    <p>{{ member.bio[:100] }}{% if member.bio|length > 100 %}...{% endif %}</p>
                </div>
                {% endif %}

                <div class="member-activity">
                    <div class="activity-item">
                        <span class="activity-label">Last Active:</span>
                        <span class="activity-value">{{ member.last_login_at.strftime('%Y-%m-%d') if member.last_login_at else 'Never' }}</span>
                    </div>
                    {% if member.favorite_pairs %}
                    <div class="activity-item">
                        <span class="activity-label">Favorite Pairs:</span>
                        <span class="activity-value">{{ member.favorite_pairs[:3]|join(', ') }}</span>
                    </div>
                    {% endif %}
                </div>

                <div class="member-actions">
                    <button onclick="viewMemberProfile({{ member.id }})" class="btn-sm btn-secondary">
                        📊 Profile
                    </button>
                    {% if member.id not in user_connections %}
                    <button onclick="connectMember({{ member.id }})" class="btn-sm btn-primary">
                        🤝 Connect
                    </button>
                    {% else %}
                    <button onclick="disconnectMember({{ member.id }})" class="btn-sm btn-danger">
                        ❌ Disconnect
                    </button>
                    {% endif %}
                    <button onclick="sendMessage({{ member.id }})" class="btn-sm btn-secondary">
                        💬 Message
                    </button>
                </div>

                <div class="member-footer">
                    {% if member.id in user_connections %}
                    <span class="connection-status connected">✅ Connected</span>
                    {% endif %}
                    {% if member.strategies_count > 0 %}
                    <span class="strategies-count">{{ member.strategies_count }} strategies</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- My Connections -->
    <section class="my-connections">
        <h2>🤝 My Connections</h2>
        {% if user_connections_details %}
        <div class="connections-grid">
            {% for connection in user_connections_details %}
            <div class="connection-card">
                <div class="connection-info">
                    <div class="avatar-circle">{{ connection.username[0]|upper }}</div>
                    <div class="connection-details">
                        <h4>{{ connection.username }}</h4>
                        <div class="connection-stats">
                            <span class="performance {{ 'positive' if connection.total_pnl >= 0 else 'negative' }}">
                                {{ '+' if connection.total_pnl >= 0 else '' }}{{ connection.total_pnl|round(2) }}%
                            </span>
                            <span class="trades">{{ connection.trades_count }} trades</span>
                        </div>
                    </div>
                </div>
                <div class="connection-actions">
                    <button onclick="viewMemberProfile({{ connection.id }})" class="btn-sm btn-secondary">
                        Profile
                    </button>
                    <button onclick="sendMessage({{ connection.id }})" class="btn-sm btn-secondary">
                        Message
                    </button>
                    <button onclick="disconnectMember({{ connection.id }})" class="btn-sm btn-danger">
                        Disconnect
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <p>You haven't connected with any members yet. Browse the directory above to find traders with similar interests and strategies.</p>
        </div>
        {% endif %}
    </section>
    </div> <!-- End club-dashboard-grid -->
</div>

<!-- Member Profile Modal -->
<div id="member-profile-modal" class="modal">
    <div class="modal-content large">
        <span class="close" onclick="closeModal('member-profile-modal')">&times;</span>
        <div id="member-profile-content">
            <!-- Member profile details will be loaded here -->
        </div>
    </div>
</div>

<!-- Send Message Modal -->
<div id="send-message-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('send-message-modal')">&times;</span>
        <h2>Send Message</h2>
        <form id="send-message-form">
            <input type="hidden" id="recipient-id">
            <div class="form-group">
                <label>To:</label>
                <span id="recipient-name"></span>
            </div>
            <div class="form-group">
                <label>Subject</label>
                <input type="text" id="message-subject" required>
            </div>
            <div class="form-group">
                <label>Message</label>
                <textarea id="message-content" rows="6" required></textarea>
            </div>
            <button type="submit">Send Message</button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/member_directory.js"></script>
<script>
    // Initialize member directory data
    window.memberDirectoryData = {
        user: {{ user|tojson }},
        memberStats: {{ member_stats|tojson }},
        topPerformers: {{ top_performers|tojson }},
        allMembers: {{ all_members|tojson }},
        userConnections: {{ user_connections|tojson }},
        userConnectionsDetails: {{ user_connections_details|tojson }}
    };

    // Initialize member directory
    document.addEventListener('DOMContentLoaded', function() {
        initializeMemberDirectory();
    });
</script>
{% endblock %}
