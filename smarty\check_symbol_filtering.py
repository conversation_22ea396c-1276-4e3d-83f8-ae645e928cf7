#!/usr/bin/env python3
"""
Check Symbol Filtering in SQLite Bus
Diagnostic script to verify symbol-specific data filtering.
"""

import sqlite3
import json
from datetime import datetime
from collections import defaultdict

def check_symbol_filtering():
    """Check what streams and symbols are in the database."""
    print("🔍 SYMBOL FILTERING DIAGNOSTIC")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect("data/bus.db")
        cursor = conn.cursor()
        
        # Get all unique streams
        cursor.execute("SELECT DISTINCT stream FROM messages ORDER BY stream")
        streams = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 Found {len(streams)} unique streams:")
        for stream in streams:
            print(f"   - {stream}")
        
        print("\n" + "=" * 50)
        
        # Analyze stream patterns
        stream_patterns = defaultdict(list)
        for stream in streams:
            if 'market.' in stream:
                parts = stream.split('.')
                if len(parts) >= 3:
                    symbol = parts[1]
                    data_type = '.'.join(parts[2:])
                    stream_patterns[symbol].append(data_type)
        
        print("📈 SYMBOL-SPECIFIC STREAMS:")
        for symbol, data_types in stream_patterns.items():
            print(f"   {symbol}:")
            for data_type in data_types:
                print(f"      - {data_type}")
        
        print("\n" + "=" * 50)
        
        # Test symbol filtering for BTC-USDT
        test_symbol = "BTC-USDT"
        print(f"🧪 TESTING SYMBOL FILTERING FOR {test_symbol}")
        
        # Test market data filtering
        cursor.execute("""
            SELECT COUNT(*) FROM messages
            WHERE (stream LIKE ? OR stream LIKE ? OR stream LIKE ?)
        """, (f"market.{test_symbol}.%", f"kline.{test_symbol}.%", f"htx.{test_symbol}.%"))
        
        filtered_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM messages WHERE stream LIKE 'market.%'")
        total_market_count = cursor.fetchone()[0]
        
        print(f"   Total market messages: {total_market_count}")
        print(f"   {test_symbol} filtered messages: {filtered_count}")
        print(f"   Filtering efficiency: {(filtered_count/total_market_count*100):.1f}%" if total_market_count > 0 else "   No market data")
        
        # Get latest message for BTC-USDT
        cursor.execute("""
            SELECT stream, ts, payload FROM messages
            WHERE (stream LIKE ? OR stream LIKE ? OR stream LIKE ?)
            ORDER BY ts DESC LIMIT 1
        """, (f"market.{test_symbol}.%", f"kline.{test_symbol}.%", f"htx.{test_symbol}.%"))
        
        latest_row = cursor.fetchone()
        if latest_row:
            latest_time = datetime.fromtimestamp(latest_row[1])
            age = (datetime.now() - latest_time).total_seconds()
            print(f"   Latest {test_symbol} message: {latest_row[0]} ({age:.1f}s ago)")
            
            try:
                payload = json.loads(latest_row[2])
                print(f"   Sample data: {payload}")
            except:
                print(f"   Raw payload: {latest_row[2][:100]}...")
        else:
            print(f"   ❌ No {test_symbol} messages found!")
        
        print("\n" + "=" * 50)
        
        # Test ETH-USDT filtering
        test_symbol2 = "ETH-USDT"
        print(f"🧪 TESTING SYMBOL FILTERING FOR {test_symbol2}")
        
        cursor.execute("""
            SELECT COUNT(*) FROM messages
            WHERE (stream LIKE ? OR stream LIKE ? OR stream LIKE ?)
        """, (f"market.{test_symbol2}.%", f"kline.{test_symbol2}.%", f"htx.{test_symbol2}.%"))
        
        eth_count = cursor.fetchone()[0]
        print(f"   {test_symbol2} filtered messages: {eth_count}")
        
        if eth_count > 0:
            cursor.execute("""
                SELECT stream, ts FROM messages
                WHERE (stream LIKE ? OR stream LIKE ? OR stream LIKE ?)
                ORDER BY ts DESC LIMIT 1
            """, (f"market.{test_symbol2}.%", f"kline.{test_symbol2}.%", f"htx.{test_symbol2}.%"))
            
            eth_latest = cursor.fetchone()
            if eth_latest:
                eth_time = datetime.fromtimestamp(eth_latest[1])
                eth_age = (datetime.now() - eth_time).total_seconds()
                print(f"   Latest {test_symbol2} message: {eth_latest[0]} ({eth_age:.1f}s ago)")
        
        print("\n" + "=" * 50)
        
        # Check recent activity by symbol
        print("📊 RECENT ACTIVITY BY SYMBOL (last 5 minutes):")
        five_min_ago = (datetime.now().timestamp() - 300)
        
        cursor.execute("""
            SELECT stream, COUNT(*) as count FROM messages
            WHERE ts > ? AND stream LIKE 'market.%'
            GROUP BY stream
            ORDER BY count DESC
        """, (five_min_ago,))
        
        recent_activity = cursor.fetchall()
        if recent_activity:
            for stream, count in recent_activity:
                print(f"   {stream}: {count} messages")
        else:
            print("   ❌ No recent market activity found!")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ SYMBOL FILTERING DIAGNOSTIC COMPLETE")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if filtered_count == 0:
            print("   1. ❌ No BTC-USDT data found - check HTX data producer")
            print("   2. ❌ Verify HTX client is publishing with correct stream names")
        elif filtered_count < total_market_count * 0.3:
            print("   1. ⚠️ Low BTC-USDT data ratio - may be mixed symbol data")
            print("   2. ✅ Symbol filtering is working but check data producer")
        else:
            print("   1. ✅ Symbol filtering appears to be working correctly")
            print("   2. ✅ Good BTC-USDT data coverage found")
        
        if len(recent_activity) == 0:
            print("   3. ❌ No recent data - start HTX data producer")
        elif len(recent_activity) > 2:
            print("   3. ✅ Multiple symbols active - symbol switching should work")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_symbol_filtering()
