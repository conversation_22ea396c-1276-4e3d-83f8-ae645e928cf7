"""
HTX Futures message parsing utilities.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Union, List

from core.events import (
    Kline, Trade, OrderbookLevel, OrderbookDelta, Position,
    OrderResponse, Fill, Side, OrderType
)
from .types import APIResponse, HTXError, ValidationError

logger = logging.getLogger(__name__)


class MessageParser:
    """
    Parses HTX WebSocket and REST API messages into domain objects.
    """
    
    def __init__(self, debug: bool = False):
        """
        Initialize message parser.
        
        Args:
            debug: Enable debug logging
        """
        self.debug = debug

    def parse_market_message(self, msg: Dict[str, Any]) -> Optional[Union[Kline, Trade, OrderbookDelta]]:
        """
        Parse a market data message into a domain object.

        Args:
            msg: Raw WebSocket message

        Returns:
            Parsed domain object or None
        """
        try:
            if not self._validate_market_message(msg):
                return None
                
            channel = msg["ch"]
            tick = msg["tick"]
            ts = msg["ts"]

            # Extract symbol from channel
            parts = channel.split(".")
            if len(parts) < 2:
                logger.warning(f"Invalid channel format: {channel}")
                return None
                
            symbol = parts[1]

            # Parse based on channel type
            if "kline" in channel:
                return self._parse_kline(symbol, tick, ts, parts)
            elif "trade" in channel:
                return self._parse_trade(symbol, tick, ts)
            elif "depth" in channel:
                return self._parse_orderbook(symbol, tick, ts)
            else:
                if self.debug:
                    logger.debug(f"Unknown market channel type: {channel}")
                return None

        except Exception as e:
            logger.error(f"Error parsing market message: {e}")
            if self.debug:
                logger.debug(f"Message content: {msg}")
            return None

    def parse_private_message(self, msg: Dict[str, Any]) -> Optional[Union[Position, OrderResponse, Fill]]:
        """
        Parse a private message into a domain object.

        Args:
            msg: Raw WebSocket message

        Returns:
            Parsed domain object or None
        """
        try:
            if not self._validate_private_message(msg):
                return None
                
            topic = msg.get("topic", "")
            data = msg.get("data", {})

            # Parse based on topic type
            if "positions" in topic:
                return self._parse_position(data)
            elif "orders" in topic:
                return self._parse_order_response(data)
            elif "matchOrders" in topic:
                return self._parse_fill(data)
            else:
                if self.debug:
                    logger.debug(f"Unknown private topic: {topic}")
                return None

        except Exception as e:
            logger.error(f"Error parsing private message: {e}")
            if self.debug:
                logger.debug(f"Message content: {msg}")
            return None

    def _validate_market_message(self, msg: Dict[str, Any]) -> bool:
        """Validate market message structure."""
        required_fields = ["ch", "tick", "ts"]
        return all(field in msg for field in required_fields)

    def _validate_private_message(self, msg: Dict[str, Any]) -> bool:
        """Validate private message structure."""
        return "topic" in msg and "data" in msg

    def _parse_kline(self, symbol: str, tick: Dict[str, Any], ts: int, parts: List[str]) -> Optional[Kline]:
        """Parse kline data."""
        try:
            if len(parts) < 4:
                logger.warning(f"Invalid kline channel format: {'.'.join(parts)}")
                return None
                
            interval = parts[3]
            
            # Validate required fields
            required_fields = ["open", "high", "low", "close", "vol"]
            if not all(field in tick for field in required_fields):
                logger.warning(f"Missing required kline fields: {tick}")
                return None

            return Kline(
                symbol=symbol,
                timestamp=datetime.fromtimestamp(ts / 1000),
                open=float(tick["open"]),
                high=float(tick["high"]),
                low=float(tick["low"]),
                close=float(tick["close"]),
                volume=float(tick["vol"]),
                interval=interval,
                trades=int(tick.get("count", 0)),
                closed=True
            )
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing kline data: {e}")
            return None

    def _parse_trade(self, symbol: str, tick: Dict[str, Any], ts: int) -> Optional[Trade]:
        """Parse trade data."""
        try:
            # HTX sends an array of trades
            if "data" not in tick or not tick["data"]:
                return None
                
            trades = []
            for trade_data in tick["data"]:
                # Validate required fields
                required_fields = ["price", "amount", "direction", "ts", "id"]
                if not all(field in trade_data for field in required_fields):
                    continue
                    
                trade = Trade(
                    symbol=symbol,
                    timestamp=datetime.fromtimestamp(trade_data["ts"] / 1000),
                    price=float(trade_data["price"]),
                    quantity=float(trade_data["amount"]),
                    side=Side.BUY if trade_data["direction"] == "buy" else Side.SELL,
                    trade_id=str(trade_data["id"]),
                    is_buyer_maker=trade_data["direction"] == "buy"
                )
                trades.append(trade)

            # Return the most recent trade
            return trades[-1] if trades else None
            
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing trade data: {e}")
            return None

    def _parse_orderbook(self, symbol: str, tick: Dict[str, Any], ts: int) -> Optional[OrderbookDelta]:
        """Parse orderbook data."""
        try:
            # Validate required fields
            if "bids" not in tick or "asks" not in tick:
                logger.warning(f"Missing orderbook data: {tick}")
                return None

            bids = []
            asks = []
            
            # Parse bids
            for bid_data in tick["bids"]:
                if len(bid_data) >= 2:
                    bids.append(OrderbookLevel(
                        price=float(bid_data[0]),
                        quantity=float(bid_data[1])
                    ))
                    
            # Parse asks
            for ask_data in tick["asks"]:
                if len(ask_data) >= 2:
                    asks.append(OrderbookLevel(
                        price=float(ask_data[0]),
                        quantity=float(ask_data[1])
                    ))

            return OrderbookDelta(
                symbol=symbol,
                timestamp=datetime.fromtimestamp(ts / 1000),
                bids=bids,
                asks=asks,
                is_snapshot=True  # HTX always sends full snapshots
            )
            
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing orderbook data: {e}")
            return None

    def _parse_position(self, data: Dict[str, Any]) -> Optional[Position]:
        """Parse position data."""
        try:
            # Validate required fields
            required_fields = ["symbol", "direction", "volume"]
            if not all(field in data for field in required_fields):
                logger.warning(f"Missing required position fields: {data}")
                return None

            symbol = data["symbol"]
            side_str = data["direction"].upper()
            side = Side.BUY if side_str == "BUY" else Side.SELL

            return Position(
                symbol=symbol,
                side=side,
                size=float(data["volume"]),
                entry_price=float(data.get("cost_open", 0)),
                leverage=float(data.get("lever_rate", 1)),
                liquidation_price=float(data.get("liquidation_price", 0)),
                unrealized_pnl=float(data.get("unrealized_profit", 0)),
                realized_pnl=float(data.get("profit", 0)),
                margin=float(data.get("position_margin", 0)),
                timestamp=datetime.fromtimestamp(int(data.get("ts", 0)) / 1000)
            )
            
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing position data: {e}")
            return None

    def _parse_order_response(self, data: Dict[str, Any]) -> Optional[OrderResponse]:
        """Parse order response data."""
        try:
            # Validate required fields
            required_fields = ["symbol", "order_id"]
            if not all(field in data for field in required_fields):
                logger.warning(f"Missing required order fields: {data}")
                return None

            return OrderResponse(
                symbol=data["symbol"],
                order_id=str(data["order_id"]),
                client_order_id=str(data.get("client_order_id", "")),
                status=data.get("status", ""),
                timestamp=datetime.fromtimestamp(int(data.get("ts", 0)) / 1000)
            )
            
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing order response data: {e}")
            return None

    def _parse_fill(self, data: Dict[str, Any]) -> Optional[Fill]:
        """Parse fill data."""
        try:
            # Validate required fields
            required_fields = ["symbol", "order_id", "price", "filled_qty"]
            if not all(field in data for field in required_fields):
                logger.warning(f"Missing required fill fields: {data}")
                return None

            side_str = data.get("direction", "").upper()
            side = Side.BUY if side_str == "BUY" else Side.SELL

            return Fill(
                symbol=data["symbol"],
                order_id=str(data["order_id"]),
                trade_id=str(data.get("match_id", "")),
                side=side,
                price=float(data["price"]),
                quantity=float(data["filled_qty"]),
                commission=float(data.get("fee", 0)),
                commission_asset=data.get("fee_asset", ""),
                timestamp=datetime.fromtimestamp(int(data.get("ts", 0)) / 1000)
            )
            
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing fill data: {e}")
            return None

    def parse_api_response(self, response: Dict[str, Any]) -> APIResponse:
        """
        Parse REST API response into standardized format.
        
        Args:
            response: Raw API response
            
        Returns:
            Standardized API response
        """
        try:
            # Check for HTX API error format
            if response.get("status") == "error":
                error_msg = response.get("err-msg", "Unknown API error")
                error_code = response.get("err-code", "unknown")
                
                return APIResponse(
                    success=False,
                    error=error_msg,
                    error_code=str(error_code),
                    timestamp=datetime.now()
                )
            
            # Success response
            return APIResponse(
                success=True,
                data=response.get("data"),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error parsing API response: {e}")
            return APIResponse(
                success=False,
                error=f"Response parsing error: {e}",
                timestamp=datetime.now()
            )
