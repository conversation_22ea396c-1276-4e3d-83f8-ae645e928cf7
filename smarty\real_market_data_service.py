#!/usr/bin/env python3
"""
Real Market Data Service for Smart-Trader Control Center

Fetches actual live cryptocurrency prices from HTX websocket only.
"""

import asyncio
import logging
import random
import time
import yaml
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class RealMarketDataService:
    """Real market data service that uses HTX websocket data only."""

    def __init__(self):
        """Initialize real market data service."""
        self.running = False
        self.market_data: Dict[str, Dict[str, Any]] = {}

        # Supported symbols
        self.symbols = {
            "BTCUSDT": {"symbol": "BTC"},
            "ETHUSDT": {"symbol": "ETH"},
            "ADAUSDT": {"symbol": "ADA"},
            "DOTUSDT": {"symbol": "DOT"},
            "LINKUSDT": {"symbol": "LINK"},
            "LTCUSDT": {"symbol": "LTC"},
            "BCHUSDT": {"symbol": "BCH"},
            "XLMUSDT": {"symbol": "XLM"},
            "EOSUSDT": {"symbol": "EOS"},
            "TRXUSDT": {"symbol": "TRX"}
        }

        # Cache for data updates
        self.last_fetch = 0
        self.cache_duration = 30  # 30 seconds cache

    def _is_demo_mode(self) -> bool:
        """Check if demo mode is enabled."""
        try:
            with open("config.yaml", "r") as f:
                config = yaml.safe_load(f)
                return config.get("demo_mode", False) or config.get("mode") == "demo"
        except Exception as e:
            logger.debug(f"Error checking demo mode: {e}")
            return False

    async def start(self) -> None:
        """Start the real market data service."""
        if self.running:
            return

        self.running = True
        logger.info("Starting Real Market Data Service (HTX websocket only)...")

        # Initial data fetch from HTX websocket
        await self._fetch_htx_data()

        # Start update loop
        asyncio.create_task(self._update_loop())

        logger.info(f"Real Market Data Service started for {len(self.symbols)} symbols")

    async def stop(self) -> None:
        """Stop the real market data service."""
        self.running = False
        logger.info("Real Market Data Service stopped")

    async def _update_loop(self) -> None:
        """Main update loop for real market data."""
        while self.running:
            try:
                await self._fetch_htx_data()
                await asyncio.sleep(30)  # Update every 30 seconds

            except Exception as e:
                logger.error(f"Error in real market data update loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _fetch_htx_data(self) -> None:
        """Fetch data from HTX websocket via SQLite bus."""
        current_time = time.time()

        # Check cache
        if current_time - self.last_fetch < self.cache_duration:
            return

        try:
            # Try to get data from SQLite bus (HTX websocket data)
            from bus_reader import get_bus_reader, BUS_READER_AVAILABLE

            if BUS_READER_AVAILABLE:
                bus_reader = get_bus_reader()
                htx_data = bus_reader.get_real_market_data()

                if htx_data:
                    self.market_data = htx_data
                    self.last_fetch = current_time
                    logger.info("Successfully updated market data from HTX websocket")
                    return

            # Check if demo mode is enabled
            demo_mode = self._is_demo_mode()
            if demo_mode:
                await self._use_demo_data()
                self.last_fetch = current_time
                logger.info("Using demo market data")
            else:
                logger.warning("No HTX websocket data available and not in demo mode")

        except Exception as e:
            logger.error(f"Failed to fetch HTX market data: {e}")
            # Only use demo data if in demo mode
            demo_mode = self._is_demo_mode()
            if demo_mode:
                await self._use_demo_data()

    async def _use_demo_data(self) -> None:
        """Use demo data when in demo mode."""
        logger.info("Using demo market data")

        # Demo prices (approximate current values)
        demo_prices = {
            "BTCUSDT": 97000.0,
            "ETHUSDT": 3550.0,
            "ADAUSDT": 1.00,
            "DOTUSDT": 7.50,
            "LINKUSDT": 14.50,
            "LTCUSDT": 72.00,
            "BCHUSDT": 245.00,
            "XLMUSDT": 0.118,
            "EOSUSDT": 0.842,
            "TRXUSDT": 0.108
        }

        for symbol, price in demo_prices.items():
            if symbol in self.symbols:
                # Calculate realistic values
                spread = price * 0.001
                change_24h_pct = random.uniform(-5, 5)  # Random ±5%
                change_24h = price * (change_24h_pct / 100)
                volume_24h = random.uniform(1000000, 10000000)

                self.market_data[symbol] = {
                    "symbol": symbol,
                    "price": price + random.uniform(-price*0.01, price*0.01),  # Small random variation
                    "volume": volume_24h,
                    "bid": price - spread/2,
                    "ask": price + spread/2,
                    "high_24h": price * 1.02,
                    "low_24h": price * 0.98,
                    "change_24h": change_24h,
                    "change_percent_24h": change_24h_pct,
                    "timestamp": datetime.now().isoformat(),
                    "last_update": time.time(),
                    "source": "demo_data"
                }

    def get_market_data(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current market data."""
        if symbol:
            return self.market_data.get(symbol, {})
        return self.market_data

    def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary statistics."""
        if not self.market_data:
            return {}

        total_volume = sum(data.get("volume", 0) for data in self.market_data.values())
        gainers = [data for data in self.market_data.values() if data.get("change_percent_24h", 0) > 0]
        losers = [data for data in self.market_data.values() if data.get("change_percent_24h", 0) < 0]

        if self.market_data:
            top_gainer = max(self.market_data.values(), key=lambda x: x.get("change_percent_24h", 0))
            top_loser = min(self.market_data.values(), key=lambda x: x.get("change_percent_24h", 0))
        else:
            top_gainer = top_loser = {}

        return {
            "total_symbols": len(self.market_data),
            "total_volume_24h": total_volume,
            "gainers_count": len(gainers),
            "losers_count": len(losers),
            "top_gainer": {
                "symbol": top_gainer.get("symbol", ""),
                "change_percent": top_gainer.get("change_percent_24h", 0),
                "price": top_gainer.get("price", 0)
            },
            "top_loser": {
                "symbol": top_loser.get("symbol", ""),
                "change_percent": top_loser.get("change_percent_24h", 0),
                "price": top_loser.get("price", 0)
            },
            "timestamp": datetime.now().isoformat(),
            "data_source": "htx_websocket"
        }

    def get_symbol_list(self) -> List[str]:
        """Get list of available symbols."""
        return list(self.symbols.keys())

    def get_price(self, symbol: str) -> float:
        """Get current price for a symbol."""
        return self.market_data.get(symbol, {}).get("price", 0.0)

    def get_change_percent(self, symbol: str) -> float:
        """Get 24h change percent for a symbol."""
        return self.market_data.get(symbol, {}).get("change_percent_24h", 0.0)

    def is_data_fresh(self) -> bool:
        """Check if data is fresh (updated within last 5 minutes)."""
        if not self.market_data:
            return False

        current_time = time.time()
        for data in self.market_data.values():
            last_update = data.get("last_update", 0)
            if current_time - last_update > 300:  # 5 minutes
                return False
        return True


# Global real market data service instance
real_market_data_service = RealMarketDataService()


async def start_real_market_data() -> None:
    """Start the global real market data service."""
    await real_market_data_service.start()


async def stop_real_market_data() -> None:
    """Stop the global real market data service."""
    await real_market_data_service.stop()


def get_real_market_data(symbol: Optional[str] = None) -> Dict[str, Any]:
    """Get current real market data."""
    return real_market_data_service.get_market_data(symbol)


def get_real_market_summary() -> Dict[str, Any]:
    """Get real market summary."""
    return real_market_data_service.get_market_summary()


def get_real_symbol_list() -> List[str]:
    """Get list of available symbols."""
    return real_market_data_service.get_symbol_list()


def get_real_current_price(symbol: str) -> float:
    """Get current real price for a symbol."""
    return real_market_data_service.get_price(symbol)


def get_real_price_change(symbol: str) -> float:
    """Get 24h real price change percent for a symbol."""
    return real_market_data_service.get_change_percent(symbol)


def is_real_data_fresh() -> bool:
    """Check if real data is fresh."""
    return real_market_data_service.is_data_fresh()
