#!/usr/bin/env python3
"""
Frontend Testing Suite for Smart Trader Dashboard

Tests all aspects of the frontend data flow:
- API endpoints
- WebSocket connections
- Data formatting
- Signal display
- Real-time updates
"""

import asyncio
import json
import logging
import sqlite3
import time
import aiohttp
import websockets
from datetime import datetime
from typing import Dict, Any, List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FrontendTester:
    """Comprehensive frontend testing suite."""
    
    def __init__(self, base_url: str = "http://localhost:8082"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://") + "/ws"
        self.session = None
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run all frontend tests."""
        logger.info("🧪 Starting Frontend Test Suite")
        logger.info("=" * 50)
        
        # Initialize HTTP session
        self.session = aiohttp.ClientSession()
        
        try:
            # Test API endpoints
            await self.test_api_endpoints()
            
            # Test WebSocket connection
            await self.test_websocket_connection()
            
            # Test data flow
            await self.test_data_flow()
            
            # Test signal publishing
            await self.test_signal_publishing()
            
            # Generate test report
            self.generate_test_report()
            
        finally:
            if self.session:
                await self.session.close()
    
    async def test_api_endpoints(self):
        """Test all API endpoints."""
        logger.info("🔍 Testing API Endpoints...")
        
        endpoints = [
            "/api/market",
            "/api/signals", 
            "/api/trades",
            "/api/stats",
            "/api/orderbook",
            "/api/recent-trades",
            "/api/ai-analysis",
            "/api/market-sentiment",
            "/api/debug"
        ]
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.test_results[f"api_{endpoint.replace('/', '_')}"] = {
                            "status": "✅ PASS",
                            "data_keys": list(data.keys()) if isinstance(data, dict) else "non-dict",
                            "data_sample": str(data)[:100] + "..." if len(str(data)) > 100 else str(data)
                        }
                        logger.info(f"✅ {endpoint}: OK - {len(str(data))} chars")
                    else:
                        self.test_results[f"api_{endpoint.replace('/', '_')}"] = {
                            "status": f"❌ FAIL - HTTP {response.status}",
                            "error": await response.text()
                        }
                        logger.error(f"❌ {endpoint}: HTTP {response.status}")
                        
            except Exception as e:
                self.test_results[f"api_{endpoint.replace('/', '_')}"] = {
                    "status": f"❌ FAIL - Exception",
                    "error": str(e)
                }
                logger.error(f"❌ {endpoint}: {e}")
    
    async def test_websocket_connection(self):
        """Test WebSocket connection and data flow."""
        logger.info("🔌 Testing WebSocket Connection...")
        
        try:
            # Test connection
            async with websockets.connect(self.ws_url) as websocket:
                logger.info("✅ WebSocket connected successfully")
                
                # Wait for initial data
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(message)
                    
                    self.test_results["websocket_connection"] = {
                        "status": "✅ PASS",
                        "data_keys": list(data.keys()) if isinstance(data, dict) else "non-dict",
                        "message_size": len(message)
                    }
                    logger.info(f"✅ WebSocket data received: {len(message)} chars")
                    logger.info(f"   Data keys: {list(data.keys()) if isinstance(data, dict) else 'non-dict'}")
                    
                except asyncio.TimeoutError:
                    self.test_results["websocket_connection"] = {
                        "status": "⚠️ TIMEOUT - No data received",
                        "note": "Connection works but no data flowing"
                    }
                    logger.warning("⚠️ WebSocket connected but no data received within 10s")
                    
        except Exception as e:
            self.test_results["websocket_connection"] = {
                "status": f"❌ FAIL - {str(e)}",
                "error": str(e)
            }
            logger.error(f"❌ WebSocket connection failed: {e}")
    
    async def test_data_flow(self):
        """Test data flow from SQLite bus to frontend."""
        logger.info("📊 Testing Data Flow...")
        
        try:
            # Check SQLite bus directly
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()
            
            # Check recent messages
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (time.time() - 300,))
            recent_messages = cursor.fetchone()[0]
            
            # Check signals
            cursor.execute("SELECT COUNT(*) FROM messages WHERE stream LIKE 'signals.%'")
            total_signals = cursor.fetchone()[0]
            
            # Check latest signal
            cursor.execute("SELECT ts, payload FROM messages WHERE stream LIKE 'signals.%' ORDER BY ts DESC LIMIT 1")
            latest_signal = cursor.fetchone()
            
            conn.close()
            
            self.test_results["data_flow"] = {
                "status": "✅ PASS" if recent_messages > 0 else "⚠️ NO RECENT DATA",
                "recent_messages": recent_messages,
                "total_signals": total_signals,
                "latest_signal_time": datetime.fromtimestamp(latest_signal[0]).strftime("%H:%M:%S") if latest_signal else "None",
                "latest_signal_data": json.loads(latest_signal[1]) if latest_signal else None
            }
            
            logger.info(f"📊 Recent messages (5min): {recent_messages}")
            logger.info(f"📊 Total signals: {total_signals}")
            if latest_signal:
                signal_data = json.loads(latest_signal[1])
                logger.info(f"📊 Latest signal: {signal_data.get('action')} at {datetime.fromtimestamp(latest_signal[0]).strftime('%H:%M:%S')}")
            
        except Exception as e:
            self.test_results["data_flow"] = {
                "status": f"❌ FAIL - {str(e)}",
                "error": str(e)
            }
            logger.error(f"❌ Data flow test failed: {e}")
    
    async def test_signal_publishing(self):
        """Test signal publishing to the bus."""
        logger.info("📡 Testing Signal Publishing...")
        
        try:
            # Import the bus for testing
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            
            from pipeline.databus import SQLiteBus
            
            # Create test signal
            test_signal = {
                "symbol": "BTC-USDT",
                "action": "TEST",
                "score": 0.999,
                "confidence": 0.999,
                "price": 109000.00,
                "rationale": "Frontend test signal",
                "timestamp": datetime.now().isoformat(),
                "source": "frontend_test"
            }
            
            # Publish test signal
            bus = SQLiteBus(path="data/bus.db", poll_interval=0.1)
            bus.publish("signals.testing", time.time(), test_signal)
            bus.close()
            
            # Verify it was stored
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()
            cursor.execute("SELECT payload FROM messages WHERE stream = 'signals.testing' ORDER BY ts DESC LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                stored_signal = json.loads(result[0])
                self.test_results["signal_publishing"] = {
                    "status": "✅ PASS",
                    "test_signal_stored": True,
                    "stored_data": stored_signal
                }
                logger.info("✅ Test signal published and stored successfully")
            else:
                self.test_results["signal_publishing"] = {
                    "status": "❌ FAIL - Signal not stored",
                    "test_signal_stored": False
                }
                logger.error("❌ Test signal was not stored in database")
                
        except Exception as e:
            self.test_results["signal_publishing"] = {
                "status": f"❌ FAIL - {str(e)}",
                "error": str(e)
            }
            logger.error(f"❌ Signal publishing test failed: {e}")
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 60)
        logger.info("📋 FRONTEND TEST REPORT")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if "✅ PASS" in result.get("status", ""))
        
        logger.info(f"📊 Total Tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {total_tests - passed_tests}")
        logger.info(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📝 Detailed Results:")
        for test_name, result in self.test_results.items():
            logger.info(f"  {test_name}: {result['status']}")
            if "data_keys" in result:
                logger.info(f"    Data keys: {result['data_keys']}")
            if "error" in result:
                logger.info(f"    Error: {result['error']}")
        
        # Specific diagnostics
        logger.info("\n🔍 Diagnostics:")
        
        # Check if signals are reaching the API
        signals_api = self.test_results.get("api__api_signals", {})
        if "✅ PASS" in signals_api.get("status", ""):
            logger.info("✅ Signals API is working")
        else:
            logger.info("❌ Signals API has issues")
        
        # Check WebSocket
        ws_result = self.test_results.get("websocket_connection", {})
        if "✅ PASS" in ws_result.get("status", ""):
            logger.info("✅ WebSocket is receiving data")
        elif "TIMEOUT" in ws_result.get("status", ""):
            logger.info("⚠️ WebSocket connects but no data flowing")
        else:
            logger.info("❌ WebSocket connection failed")
        
        # Check data flow
        data_flow = self.test_results.get("data_flow", {})
        if data_flow.get("recent_messages", 0) > 0:
            logger.info(f"✅ Data is flowing ({data_flow['recent_messages']} recent messages)")
        else:
            logger.info("❌ No recent data in SQLite bus")
        
        if data_flow.get("total_signals", 0) > 0:
            logger.info(f"✅ Signals exist in database ({data_flow['total_signals']} total)")
        else:
            logger.info("❌ No signals found in database")


async def main():
    """Run the frontend test suite."""
    tester = FrontendTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
