#!/usr/bin/env python3
"""
Dropdown Positioning Test Suite
Tests the fixed dropdown positioning for:
- User profile dropdown positioning
- Notification panel positioning
- Responsive behavior
- Cross-browser compatibility
"""

import requests
import json
import time
from datetime import datetime

class DropdownPositioningTester:
    def __init__(self, base_url="http://localhost:8087"):
        self.base_url = base_url
        self.session = requests.Session()

    def run_positioning_tests(self):
        """Run comprehensive dropdown positioning tests."""
        print("🎯 DROPDOWN POSITIONING TEST SUITE")
        print("=" * 60)

        # Test 1: Platform Health Check
        self.test_platform_health()

        # Test 2: Authentication
        self.test_authentication()

        # Test 3: CSS Positioning Rules
        self.test_css_positioning_rules()

        # Test 4: JavaScript Positioning Functions
        self.test_javascript_positioning_functions()

        # Test 5: Header Structure
        self.test_header_structure()

        # Test 6: Responsive CSS Rules
        self.test_responsive_css_rules()

        # Test 7: Event Handlers
        self.test_event_handlers()

        print("\n" + "=" * 60)
        print("✅ DROPDOWN POSITIONING TEST COMPLETE")
        print("\n🌟 MANUAL TESTING INSTRUCTIONS:")
        print("1. Open http://localhost:8087/dashboard in your browser")
        print("2. Click on 'Welcome, epinnox ADMIN' - dropdown should appear below it")
        print("3. Click on the notification bell - panel should appear below the bell")
        print("4. Resize the browser window - dropdowns should reposition correctly")
        print("5. Test on mobile view (F12 > Device toolbar)")

    def test_platform_health(self):
        """Test platform health."""
        print("\n📊 Testing Platform Health...")

        try:
            resp = self.session.get(f"{self.base_url}/health", timeout=10)
            if resp.status_code == 200:
                print("✅ Platform is running")
                return True
            else:
                print(f"❌ Platform health check failed: {resp.status_code}")
                return False
        except Exception as e:
            print(f"❌ Platform health check error: {e}")
            return False

    def test_authentication(self):
        """Test authentication flow."""
        print("\n🔐 Testing Authentication...")

        try:
            # Login with epinnox credentials
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }

            resp = self.session.post(f"{self.base_url}/login", data=login_data, timeout=10)
            if resp.status_code in [200, 302]:
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def test_css_positioning_rules(self):
        """Test CSS positioning rules for dropdowns."""
        print("\n🎨 Testing CSS Positioning Rules...")

        try:
            resp = self.session.get(f"{self.base_url}/static/css/unified_header.css", timeout=10)
            if resp.status_code == 200:
                css_content = resp.text

                # Check for user dropdown positioning
                user_dropdown_checks = [
                    '.user-profile-dropdown',
                    'position: relative',
                    '.user-dropdown-menu',
                    'position: absolute',
                    'top: calc(100% + 8px)',
                    'right: 0'
                ]

                found_user_rules = []
                for check in user_dropdown_checks:
                    if check in css_content:
                        found_user_rules.append(check)

                print(f"✅ User dropdown CSS rules: {len(found_user_rules)}/6 found")
                for rule in found_user_rules:
                    print(f"   ✓ {rule}")

                # Check for notifications panel positioning
                notifications_checks = [
                    '.notifications-panel',
                    'position: fixed',
                    'width: 380px',
                    'z-index: 1002'
                ]

                found_notification_rules = []
                for check in notifications_checks:
                    if check in css_content:
                        found_notification_rules.append(check)

                print(f"✅ Notifications panel CSS rules: {len(found_notification_rules)}/4 found")
                for rule in found_notification_rules:
                    print(f"   ✓ {rule}")

                # Check for responsive rules
                responsive_checks = [
                    '@media (max-width: 768px)',
                    'calc(100vw - 40px)',
                    'left: 10px !important'
                ]

                found_responsive_rules = []
                for check in responsive_checks:
                    if check in css_content:
                        found_responsive_rules.append(check)

                print(f"✅ Responsive CSS rules: {len(found_responsive_rules)}/3 found")
                for rule in found_responsive_rules:
                    print(f"   ✓ {rule}")

                return len(found_user_rules) >= 4 and len(found_notification_rules) >= 3
            else:
                print(f"❌ CSS file failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ CSS test error: {e}")
            return False

    def test_javascript_positioning_functions(self):
        """Test JavaScript positioning functions."""
        print("\n🔧 Testing JavaScript Positioning Functions...")

        try:
            resp = self.session.get(f"{self.base_url}/static/js/header_navigation.js", timeout=10)
            if resp.status_code == 200:
                js_content = resp.text

                # Check for positioning functions
                positioning_functions = [
                    'positionNotificationsPanel',
                    'getBoundingClientRect',
                    'window.innerWidth',
                    'window.innerHeight',
                    'panel.style.left',
                    'panel.style.top',
                    'bellRect.left',
                    'bellRect.bottom'
                ]

                found_functions = []
                for func in positioning_functions:
                    if func in js_content:
                        found_functions.append(func)

                print(f"✅ Positioning functions: {len(found_functions)}/8 found")
                for func in found_functions:
                    print(f"   ✓ {func}")

                # Check for event handlers
                event_handlers = [
                    'window.addEventListener(\'resize\'',
                    'window.addEventListener(\'scroll\'',
                    'document.addEventListener(\'click\'',
                    'positionNotificationsPanel(notificationsPanel)'
                ]

                found_handlers = []
                for handler in event_handlers:
                    if handler in js_content:
                        found_handlers.append(handler)

                print(f"✅ Event handlers: {len(found_handlers)}/4 found")
                for handler in found_handlers:
                    print(f"   ✓ {handler}")

                return len(found_functions) >= 6 and len(found_handlers) >= 3
            else:
                print(f"❌ JavaScript file failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ JavaScript test error: {e}")
            return False

    def test_header_structure(self):
        """Test header structure for proper element IDs and classes."""
        print("\n🏗️ Testing Header Structure...")

        try:
            resp = self.session.get(f"{self.base_url}/dashboard", timeout=10)
            if resp.status_code == 200:
                content = resp.text

                # Check for required elements
                required_elements = [
                    'id="notification-badge"',
                    'class="user-profile-dropdown"',
                    'class="user-dropdown-menu"',
                    'onclick="toggleNotifications()"',
                    'onclick="toggleUserDropdown()"',
                    'Welcome, epinnox',
                    'ADMIN'
                ]

                found_elements = []
                for element in required_elements:
                    if element in content:
                        found_elements.append(element)

                print(f"✅ Header elements: {len(found_elements)}/7 found")
                for element in found_elements:
                    print(f"   ✓ {element}")

                # Check for proper HTML structure - dropdown menu should be nested inside user profile
                print("\n🔧 Testing HTML Structure Fix...")
                user_profile_start = content.find('<div class="user-profile-dropdown"')
                if user_profile_start != -1:
                    # Find the closing div for user-profile-dropdown
                    div_count = 0
                    pos = user_profile_start
                    user_profile_end = -1

                    while pos < len(content):
                        if content[pos:pos+4] == '<div':
                            div_count += 1
                        elif content[pos:pos+6] == '</div>':
                            div_count -= 1
                            if div_count == 0:
                                user_profile_end = pos + 6
                                break
                        pos += 1

                    if user_profile_end != -1:
                        user_profile_section = content[user_profile_start:user_profile_end]
                        if 'class="user-dropdown-menu"' in user_profile_section:
                            print("   ✅ User dropdown menu is properly nested inside user profile container")
                            html_structure_correct = True
                        else:
                            print("   ❌ User dropdown menu is NOT nested inside user profile container")
                            html_structure_correct = False
                    else:
                        print("   ❌ Could not find closing tag for user profile dropdown")
                        html_structure_correct = False
                else:
                    print("   ❌ User profile dropdown container not found")
                    html_structure_correct = False

                # Check for proper script loading
                script_checks = [
                    'header_navigation.js',
                    'personal_dashboard.js',
                    'window.MoneyCircle'
                ]

                found_scripts = []
                for script in script_checks:
                    if script in content:
                        found_scripts.append(script)

                print(f"✅ Script loading: {len(found_scripts)}/3 found")
                for script in found_scripts:
                    print(f"   ✓ {script}")

                return len(found_elements) >= 5 and len(found_scripts) >= 2 and html_structure_correct
            else:
                print(f"❌ Dashboard page failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ Header structure test error: {e}")
            return False

    def test_responsive_css_rules(self):
        """Test responsive CSS rules."""
        print("\n📱 Testing Responsive CSS Rules...")

        try:
            resp = self.session.get(f"{self.base_url}/static/css/unified_header.css", timeout=10)
            if resp.status_code == 200:
                css_content = resp.text

                # Check for mobile-specific rules
                mobile_rules = [
                    '@media (max-width: 768px)',
                    '.user-dropdown-menu',
                    'min-width: calc(100vw - 40px)',
                    '.notifications-panel',
                    'width: calc(100vw - 20px)',
                    'left: 10px !important'
                ]

                found_mobile_rules = []
                for rule in mobile_rules:
                    if rule in css_content:
                        found_mobile_rules.append(rule)

                print(f"✅ Mobile CSS rules: {len(found_mobile_rules)}/6 found")
                for rule in found_mobile_rules:
                    print(f"   ✓ {rule}")

                return len(found_mobile_rules) >= 4
            else:
                print(f"❌ CSS file failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ Responsive CSS test error: {e}")
            return False

    def test_event_handlers(self):
        """Test event handler setup."""
        print("\n⚡ Testing Event Handlers...")

        try:
            resp = self.session.get(f"{self.base_url}/static/js/header_navigation.js", timeout=10)
            if resp.status_code == 200:
                js_content = resp.text

                # Check for event handler setup
                event_setup = [
                    'setupNotifications',
                    'addEventListener(\'resize\'',
                    'addEventListener(\'scroll\'',
                    'addEventListener(\'click\'',
                    'toggleNotifications',
                    'toggleUserDropdown',
                    'closeAllDropdowns'
                ]

                found_events = []
                for event in event_setup:
                    if event in js_content:
                        found_events.append(event)

                print(f"✅ Event handlers: {len(found_events)}/7 found")
                for event in found_events:
                    print(f"   ✓ {event}")

                return len(found_events) >= 5
            else:
                print(f"❌ JavaScript file failed: {resp.status_code}")
                return False

        except Exception as e:
            print(f"❌ Event handlers test error: {e}")
            return False

def main():
    """Run the dropdown positioning test suite."""
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    tester = DropdownPositioningTester()
    tester.run_positioning_tests()

    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
