#!/usr/bin/env python3
"""
Database Cleanup Script for Smart Trader

This script cleans up old messages from the SQLite bus to improve performance.
"""

import sqlite3
import time
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_database():
    """Clean up old messages from the database."""
    db_path = "data/bus.db"
    
    try:
        logger.info("🧹 Starting database cleanup...")
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current message count
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_before = cursor.fetchone()[0]
        logger.info(f"📊 Total messages before cleanup: {total_before:,}")
        
        # Keep only last 2 hours of data (7200 seconds)
        cutoff_time = time.time() - 7200
        cutoff_dt = datetime.fromtimestamp(cutoff_time)
        logger.info(f"🕒 Keeping messages newer than: {cutoff_dt}")
        
        # Delete old messages
        cursor.execute("DELETE FROM messages WHERE ts < ?", (cutoff_time,))
        deleted_count = cursor.rowcount
        
        # Commit changes
        conn.commit()
        
        # Get new message count
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_after = cursor.fetchone()[0]
        
        # Vacuum to reclaim space
        logger.info("🔧 Vacuuming database to reclaim space...")
        cursor.execute("VACUUM")
        conn.commit()
        
        # Close connection
        conn.close()
        
        logger.info(f"✅ Database cleanup complete!")
        logger.info(f"📊 Messages before: {total_before:,}")
        logger.info(f"📊 Messages after: {total_after:,}")
        logger.info(f"🗑️ Messages deleted: {deleted_count:,}")
        logger.info(f"💾 Space reclaimed through VACUUM")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database cleanup failed: {e}")
        return False

if __name__ == "__main__":
    cleanup_database()
