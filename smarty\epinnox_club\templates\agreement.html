<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Circle Investment Club - Membership Agreement</title>
    <link rel="stylesheet" href="/static/css/club.css">
    <style>
        .agreement-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .agreement-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .agreement-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 2.2rem;
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .progress-step {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
        }

        .progress-step.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .progress-step.completed {
            color: #28a745;
            font-weight: 600;
        }

        .progress-step:not(:last-child)::after {
            content: "→";
            margin: 0 1rem;
            color: var(--text-secondary);
        }

        .agreement-content {
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.6;
        }

        .agreement-content h2 {
            color: var(--primary-color);
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }

        .agreement-content h2:first-child {
            margin-top: 0;
        }

        .agreement-content p {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .agreement-content ul {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }

        .agreement-content li {
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .acceptance-section {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .acceptance-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .acceptance-checkbox input[type="checkbox"] {
            margin-right: 0.75rem;
            margin-top: 0.25rem;
            transform: scale(1.2);
        }

        .acceptance-checkbox label {
            color: var(--text-primary);
            font-weight: 500;
            line-height: 1.5;
        }

        .digital-signature {
            margin-bottom: 1.5rem;
        }

        .digital-signature label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
        }

        .digital-signature input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            background: var(--input-bg);
            color: var(--text-primary);
        }

        .digital-signature input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
        }

        .agreement-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-accept {
            background: var(--primary-color);
            color: var(--bg-primary);
        }

        .btn-accept:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }

        .btn-accept:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        .btn-decline {
            background: #dc3545;
            color: white;
        }

        .btn-decline:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .legal-notice {
            background: rgba(108, 117, 125, 0.1);
            border-left: 4px solid #6c757d;
            padding: 1rem;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="agreement-container">
        <div class="agreement-header">
            <h1>💰 Money Circle Investment Club</h1>
            <p>Membership Agreement & Terms of Service</p>
        </div>

        <div class="progress-indicator">
            <div class="progress-step completed">Step 1: Registration</div>
            <div class="progress-step active">Step 2: Agreement</div>
        </div>

        {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endif %}

        <div class="agreement-content" id="agreementContent">
            <h2>1. Welcome to Money Circle Investment Club</h2>
            <p>Welcome to Money Circle Investment Club ("the Club"), an exclusive community of sophisticated investors dedicated to collaborative trading and investment strategies. By joining our club, you become part of a select group committed to financial excellence and mutual success.</p>

            <h2>2. Membership Terms</h2>
            <p>Your membership in Money Circle Investment Club grants you access to:</p>
            <ul>
                <li>Advanced trading strategies and market analysis tools</li>
                <li>Real-time market data and trading signals</li>
                <li>Collaborative investment opportunities with fellow members</li>
                <li>Educational resources and expert market insights</li>
                <li>Secure exchange integration for multiple trading platforms</li>
            </ul>

            <h2>3. Investment Risks and Responsibilities</h2>
            <p><strong>IMPORTANT:</strong> All trading and investment activities carry inherent risks. You acknowledge that:</p>
            <ul>
                <li>Past performance does not guarantee future results</li>
                <li>You may lose some or all of your invested capital</li>
                <li>You are solely responsible for your trading decisions</li>
                <li>The Club provides tools and information, not financial advice</li>
                <li>You should only invest what you can afford to lose</li>
            </ul>

            <h2>4. Platform Security and Data Protection</h2>
            <p>We take your security seriously and implement industry-standard measures:</p>
            <ul>
                <li>End-to-end encryption for all sensitive data</li>
                <li>Secure API key storage with advanced encryption</li>
                <li>Regular security audits and updates</li>
                <li>No storage of exchange passwords or private keys</li>
                <li>Compliance with data protection regulations</li>
            </ul>

            <h2>5. Code of Conduct</h2>
            <p>As a member, you agree to:</p>
            <ul>
                <li>Maintain the confidentiality of club strategies and information</li>
                <li>Treat fellow members with respect and professionalism</li>
                <li>Use the platform responsibly and in accordance with all applicable laws</li>
                <li>Not share your account credentials with others</li>
                <li>Report any security concerns or suspicious activity immediately</li>
            </ul>

            <h2>6. Limitation of Liability</h2>
            <p>Money Circle Investment Club, its operators, and affiliates shall not be liable for any direct, indirect, incidental, or consequential damages arising from your use of the platform or participation in trading activities. Your use of the platform is at your own risk.</p>

            <h2>7. Agreement Updates</h2>
            <p>This agreement may be updated from time to time. Continued use of the platform constitutes acceptance of any modifications. We will notify members of significant changes via email or platform notifications.</p>
        </div>

        <form method="POST" action="/agreement" id="agreementForm">
            <!-- CSRF Token for security -->
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div class="acceptance-section">
                <div class="acceptance-checkbox">
                    <input type="checkbox" id="agreement_read" name="agreement_read" required>
                    <label for="agreement_read">
                        I have read and fully understand the Money Circle Investment Club Membership Agreement and Terms of Service above.
                    </label>
                </div>

                <div class="acceptance-checkbox">
                    <input type="checkbox" id="risk_acknowledgment" name="risk_acknowledgment" required>
                    <label for="risk_acknowledgment">
                        I acknowledge that trading and investing involves substantial risk of loss and that I am solely responsible for my trading decisions.
                    </label>
                </div>

                <div class="acceptance-checkbox">
                    <input type="checkbox" id="age_confirmation" name="age_confirmation" required>
                    <label for="age_confirmation">
                        I confirm that I am at least 18 years of age and have the legal capacity to enter into this agreement.
                    </label>
                </div>

                <div class="digital-signature">
                    <label for="digital_signature">Digital Signature (Type your full name):</label>
                    <input type="text" id="digital_signature" name="digital_signature" required
                           placeholder="Enter your full legal name">
                </div>
            </div>

            <div class="agreement-buttons">
                <a href="/logout" class="btn btn-decline">Decline & Exit</a>
                <button type="submit" class="btn btn-accept" id="acceptButton" disabled>
                    Accept Agreement & Continue
                </button>
            </div>
        </form>

        <div class="legal-notice">
            <strong>Legal Notice:</strong> By clicking "Accept Agreement & Continue", you are providing your digital signature and consent to be bound by this agreement. This constitutes a legally binding contract. Your acceptance will be recorded with timestamp and IP address for legal compliance.
        </div>
    </div>

    <script>
        // Enable/disable accept button based on form completion
        function checkFormCompletion() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const signature = document.getElementById('digital_signature');
            const acceptButton = document.getElementById('acceptButton');

            let allChecked = true;
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) allChecked = false;
            });

            const hasSignature = signature.value.trim().length >= 2;

            acceptButton.disabled = !(allChecked && hasSignature);
        }

        // Add event listeners
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', checkFormCompletion);
        });

        document.getElementById('digital_signature').addEventListener('input', checkFormCompletion);

        // Prevent double submission
        document.getElementById('agreementForm').addEventListener('submit', function(e) {
            const acceptButton = document.getElementById('acceptButton');
            acceptButton.disabled = true;
            acceptButton.textContent = 'Processing...';
        });

        // Auto-scroll to bottom of agreement when user starts reading
        let hasScrolled = false;
        document.getElementById('agreementContent').addEventListener('scroll', function() {
            if (!hasScrolled) {
                hasScrolled = true;
                // User has started reading, enable form interaction
            }
        });
    </script>
</body>
</html>
