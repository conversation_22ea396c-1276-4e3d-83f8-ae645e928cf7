#!/usr/bin/env python3
"""
Money Circle Production Deployment Script
Automated deployment for production environment.
"""

import os
import sys
import subprocess
import shutil
import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionDeployer:
    """Production deployment manager for Money Circle."""
    
    def __init__(self, deployment_path: str = "/opt/money_circle"):
        self.deployment_path = Path(deployment_path)
        self.backup_path = Path(f"{deployment_path}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.source_path = Path(__file__).parent.parent
        
    def validate_environment(self):
        """Validate production environment requirements."""
        logger.info("🔍 Validating production environment...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            raise Exception("Python 3.8+ required for production deployment")
        
        # Check required environment variables
        required_env_vars = [
            'PROD_HOST',
            'PROD_PORT', 
            'SECRET_KEY',
            'DATABASE_PATH'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.warning(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
            logger.info("💡 Using default values for missing variables")
        
        # Check SSL certificates if HTTPS is enabled
        if os.getenv('FORCE_HTTPS', 'True').lower() == 'true':
            ssl_cert = os.getenv('SSL_CERT_PATH')
            ssl_key = os.getenv('SSL_KEY_PATH')
            
            if ssl_cert and ssl_key:
                if not Path(ssl_cert).exists():
                    logger.warning(f"⚠️ SSL certificate not found: {ssl_cert}")
                if not Path(ssl_key).exists():
                    logger.warning(f"⚠️ SSL key not found: {ssl_key}")
            else:
                logger.warning("⚠️ HTTPS enabled but SSL certificates not configured")
        
        logger.info("✅ Environment validation complete")
    
    def backup_existing_deployment(self):
        """Backup existing deployment if it exists."""
        if self.deployment_path.exists():
            logger.info(f"📦 Backing up existing deployment to {self.backup_path}")
            shutil.copytree(self.deployment_path, self.backup_path)
            logger.info("✅ Backup completed")
        else:
            logger.info("ℹ️ No existing deployment found")
    
    def install_dependencies(self):
        """Install production dependencies."""
        logger.info("📦 Installing production dependencies...")
        
        requirements_file = self.source_path / "requirements.txt"
        if requirements_file.exists():
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], check=True, capture_output=True, text=True)
                logger.info("✅ Dependencies installed successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Failed to install dependencies: {e}")
                raise
        else:
            logger.warning("⚠️ requirements.txt not found")
    
    def deploy_application(self):
        """Deploy application files to production directory."""
        logger.info(f"🚀 Deploying application to {self.deployment_path}")
        
        # Create deployment directory
        self.deployment_path.mkdir(parents=True, exist_ok=True)
        
        # Copy application files
        files_to_copy = [
            "app.py",
            "config.py",
            "requirements.txt",
            "auth/",
            "database/",
            "dashboards/",
            "exchanges/",
            "middleware/",
            "club/",
            "templates/",
            "static/",
            "docs/"
        ]
        
        for item in files_to_copy:
            source = self.source_path / item
            dest = self.deployment_path / item
            
            if source.exists():
                if source.is_dir():
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(source, dest)
                    logger.info(f"📁 Copied directory: {item}")
                else:
                    dest.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source, dest)
                    logger.info(f"📄 Copied file: {item}")
            else:
                logger.warning(f"⚠️ Source not found: {item}")
        
        logger.info("✅ Application deployment complete")
    
    def setup_production_environment(self):
        """Setup production environment configuration."""
        logger.info("⚙️ Setting up production environment...")
        
        # Create production directories
        prod_dirs = [
            "data",
            "logs", 
            "static/css",
            "static/js",
            "static/images",
            "ssl"
        ]
        
        for directory in prod_dirs:
            dir_path = self.deployment_path / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"📁 Created directory: {directory}")
        
        # Set proper permissions
        try:
            os.chmod(self.deployment_path, 0o755)
            logger.info("🔒 Set directory permissions")
        except Exception as e:
            logger.warning(f"⚠️ Could not set permissions: {e}")
        
        logger.info("✅ Production environment setup complete")
    
    def create_systemd_service(self):
        """Create systemd service file for production."""
        logger.info("🔧 Creating systemd service...")
        
        service_content = f"""[Unit]
Description=Money Circle Investment Club Platform
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory={self.deployment_path}
Environment=FLASK_ENV=production
Environment=PYTHONPATH={self.deployment_path}
ExecStart={sys.executable} app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
        
        service_file = Path("/etc/systemd/system/money-circle.service")
        try:
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            # Reload systemd and enable service
            subprocess.run(["systemctl", "daemon-reload"], check=True)
            subprocess.run(["systemctl", "enable", "money-circle"], check=True)
            
            logger.info("✅ Systemd service created and enabled")
        except PermissionError:
            logger.warning("⚠️ Need sudo privileges to create systemd service")
            logger.info(f"💡 Manual service file content saved to: {self.deployment_path}/money-circle.service")
            with open(self.deployment_path / "money-circle.service", 'w') as f:
                f.write(service_content)
        except Exception as e:
            logger.error(f"❌ Failed to create systemd service: {e}")
    
    def setup_nginx_config(self):
        """Create nginx configuration for reverse proxy."""
        logger.info("🌐 Creating nginx configuration...")
        
        nginx_config = f"""server {{
    listen 80;
    listen [::]:80;
    server_name money-circle.local;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name money-circle.local;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/money-circle.crt;
    ssl_certificate_key /etc/ssl/private/money-circle.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Static files
    location /static/ {{
        alias {self.deployment_path}/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
    
    # Proxy to Money Circle application
    location / {{
        proxy_pass http://127.0.0.1:8085;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }}
}}
"""
        
        nginx_file = Path("/etc/nginx/sites-available/money-circle")
        try:
            with open(nginx_file, 'w') as f:
                f.write(nginx_config)
            
            # Enable site
            symlink_path = Path("/etc/nginx/sites-enabled/money-circle")
            if symlink_path.exists():
                symlink_path.unlink()
            symlink_path.symlink_to(nginx_file)
            
            # Test nginx configuration
            subprocess.run(["nginx", "-t"], check=True)
            
            logger.info("✅ Nginx configuration created")
        except PermissionError:
            logger.warning("⚠️ Need sudo privileges to configure nginx")
            logger.info(f"💡 Manual nginx config saved to: {self.deployment_path}/nginx-money-circle.conf")
            with open(self.deployment_path / "nginx-money-circle.conf", 'w') as f:
                f.write(nginx_config)
        except Exception as e:
            logger.error(f"❌ Failed to configure nginx: {e}")
    
    def deploy(self):
        """Execute complete production deployment."""
        logger.info("🚀 Starting Money Circle production deployment...")
        
        try:
            # Validation
            self.validate_environment()
            
            # Backup
            self.backup_existing_deployment()
            
            # Install dependencies
            self.install_dependencies()
            
            # Deploy application
            self.deploy_application()
            
            # Setup environment
            self.setup_production_environment()
            
            # Create service files
            self.create_systemd_service()
            self.setup_nginx_config()
            
            logger.info("🎉 Production deployment completed successfully!")
            logger.info(f"📍 Application deployed to: {self.deployment_path}")
            logger.info("🔧 Next steps:")
            logger.info("   1. Configure SSL certificates")
            logger.info("   2. Set environment variables")
            logger.info("   3. Start services: sudo systemctl start money-circle")
            logger.info("   4. Reload nginx: sudo systemctl reload nginx")
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            
            # Restore backup if deployment fails
            if self.backup_path.exists() and self.deployment_path.exists():
                logger.info("🔄 Restoring from backup...")
                shutil.rmtree(self.deployment_path)
                shutil.move(self.backup_path, self.deployment_path)
                logger.info("✅ Backup restored")
            
            raise

def main():
    """Main deployment function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Money Circle Production Deployment")
    parser.add_argument("--path", default="/opt/money_circle", 
                       help="Deployment path (default: /opt/money_circle)")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate environment, don't deploy")
    
    args = parser.parse_args()
    
    deployer = ProductionDeployer(args.path)
    
    if args.validate_only:
        deployer.validate_environment()
        logger.info("✅ Environment validation complete")
    else:
        deployer.deploy()

if __name__ == "__main__":
    main()
