"""
Multi-Exchange Client for Smart Trader

Provides a unified interface to multiple cryptocurrency exchanges
as an alternative to HTX, with automatic fallback mechanisms.
"""

import asyncio
import json
import logging
import time
import websockets
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable

logger = logging.getLogger(__name__)


class MultiExchangeClient:
    """
    Multi-exchange client that provides HTX-compatible market data
    from multiple sources with automatic fallback.
    """

    def __init__(self):
        self.publisher = None
        self.running = False
        self.active_connections = {}
        self.symbol_map = {
            "BTC-USDT": {
                "binance": "btcusdt",
                "bybit": "BTCUSDT",
                "okx": "BTC-USDT",
                "gate": "BTC_USDT"
            },
            "ETH-USDT": {
                "ethusdt": "ethusdt",
                "bybit": "ETHUSDT",
                "okx": "ETH-USDT",
                "gate": "ETH_USDT"
            }
        }

        # Exchange configurations
        self.exchanges = {
            "binance": {
                "ws_url": "wss://stream.binance.com:9443/ws",
                "priority": 1,
                "status": "disconnected"
            },
            "bybit": {
                "ws_url": "wss://stream.bybit.com/v5/public/spot",
                "priority": 2,
                "status": "disconnected"
            },
            "okx": {
                "ws_url": "wss://ws.okx.com:8443/ws/v5/public",
                "priority": 3,
                "status": "disconnected"
            },
            "gate": {
                "ws_url": "wss://api.gateio.ws/ws/v4/",
                "priority": 4,
                "status": "disconnected"
            }
        }

    def set_publisher(self, publisher: Callable):
        """Set the message publisher function."""
        self.publisher = publisher
        logger.info("Message bus publisher set for multi-exchange client")

    async def connect(self) -> bool:
        """Connect to exchanges in priority order until one succeeds."""
        logger.info("🔄 Connecting to exchanges...")

        # Sort exchanges by priority
        sorted_exchanges = sorted(
            self.exchanges.items(),
            key=lambda x: x[1]["priority"]
        )

        for exchange_id, config in sorted_exchanges:
            try:
                logger.info(f"🔄 Attempting {exchange_id} connection...")

                if await self._connect_exchange(exchange_id):
                    logger.info(f"✅ Connected to {exchange_id}")
                    return True
                else:
                    logger.warning(f"❌ Failed to connect to {exchange_id}")

            except Exception as e:
                logger.error(f"❌ {exchange_id} connection error: {e}")
                continue

        logger.error("❌ All exchange connections failed")
        return False

    async def _connect_exchange(self, exchange_id: str) -> bool:
        """Connect to a specific exchange."""
        try:
            if exchange_id == "binance":
                return await self._connect_binance()
            elif exchange_id == "bybit":
                return await self._connect_bybit()
            elif exchange_id == "okx":
                return await self._connect_okx()
            elif exchange_id == "gate":
                return await self._connect_gate()
            else:
                logger.warning(f"Unknown exchange: {exchange_id}")
                return False

        except Exception as e:
            logger.error(f"Error connecting to {exchange_id}: {e}")
            return False

    async def _connect_binance(self) -> bool:
        """Connect to Binance WebSocket."""
        try:
            # Build subscription message
            streams = []
            for symbol_data in self.symbol_map.values():
                binance_symbol = symbol_data.get("binance")
                if binance_symbol:
                    streams.extend([
                        f"{binance_symbol}@kline_1s",
                        f"{binance_symbol}@trade",
                        f"{binance_symbol}@depth20@100ms"
                    ])

            if not streams:
                return False

            # Connect to WebSocket
            ws_url = f"{self.exchanges['binance']['ws_url']}/{'/'.join(streams)}"
            ws = await websockets.connect(ws_url)

            self.active_connections["binance"] = ws
            self.exchanges["binance"]["status"] = "connected"

            # Start message handling
            asyncio.create_task(self._handle_binance_messages(ws))

            return True

        except Exception as e:
            logger.error(f"Binance connection failed: {e}")
            return False

    async def _connect_bybit(self) -> bool:
        """Connect to Bybit WebSocket."""
        try:
            ws = await websockets.connect(self.exchanges["bybit"]["ws_url"])

            # Subscribe to channels
            for symbol_data in self.symbol_map.values():
                bybit_symbol = symbol_data.get("bybit")
                if bybit_symbol:
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [
                            f"kline.1.{bybit_symbol}",
                            f"publicTrade.{bybit_symbol}",
                            f"orderbook.1.{bybit_symbol}"
                        ]
                    }
                    await ws.send(json.dumps(subscribe_msg))

            self.active_connections["bybit"] = ws
            self.exchanges["bybit"]["status"] = "connected"

            # Start message handling
            asyncio.create_task(self._handle_bybit_messages(ws))

            return True

        except Exception as e:
            logger.error(f"Bybit connection failed: {e}")
            return False

    async def _connect_okx(self) -> bool:
        """Connect to OKX WebSocket."""
        try:
            ws = await websockets.connect(self.exchanges["okx"]["ws_url"])

            # Subscribe to channels
            for symbol_data in self.symbol_map.values():
                okx_symbol = symbol_data.get("okx")
                if okx_symbol:
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [
                            {"channel": "candle1s", "instId": okx_symbol},
                            {"channel": "trades", "instId": okx_symbol},
                            {"channel": "books5", "instId": okx_symbol}
                        ]
                    }
                    await ws.send(json.dumps(subscribe_msg))

            self.active_connections["okx"] = ws
            self.exchanges["okx"]["status"] = "connected"

            # Start message handling
            asyncio.create_task(self._handle_okx_messages(ws))

            return True

        except Exception as e:
            logger.error(f"OKX connection failed: {e}")
            return False

    async def _connect_gate(self) -> bool:
        """Connect to Gate.io WebSocket."""
        try:
            ws = await websockets.connect(self.exchanges["gate"]["ws_url"])

            # Subscribe to channels
            for symbol_data in self.symbol_map.values():
                gate_symbol = symbol_data.get("gate")
                if gate_symbol:
                    subscribe_msg = {
                        "method": "SUBSCRIBE",
                        "params": [
                            f"spot.candlesticks",
                            f"spot.trades",
                            f"spot.order_book"
                        ],
                        "id": int(time.time())
                    }
                    await ws.send(json.dumps(subscribe_msg))

            self.active_connections["gate"] = ws
            self.exchanges["gate"]["status"] = "connected"

            # Start message handling
            asyncio.create_task(self._handle_gate_messages(ws))

            return True

        except Exception as e:
            logger.error(f"Gate.io connection failed: {e}")
            return False

    async def _handle_binance_messages(self, ws):
        """Handle Binance WebSocket messages."""
        try:
            async for message in ws:
                data = json.loads(message)
                await self._process_binance_message(data)
        except Exception as e:
            logger.error(f"Binance message handling error: {e}")
            self.exchanges["binance"]["status"] = "disconnected"

    async def _process_binance_message(self, data: Dict):
        """Process Binance message and convert to HTX format."""
        try:
            if 'stream' not in data:
                return

            stream = data['stream']
            payload = data['data']

            # Extract symbol and stream type
            parts = stream.split('@')
            if len(parts) < 2:
                return

            binance_symbol = parts[0]
            stream_type = parts[1]

            # Find HTX symbol
            htx_symbol = None
            for symbol, mapping in self.symbol_map.items():
                if mapping.get("binance") == binance_symbol:
                    htx_symbol = symbol
                    break

            if not htx_symbol:
                return

            # Convert based on stream type
            if stream_type.startswith('kline'):
                await self._convert_binance_kline(htx_symbol, payload)
            elif stream_type == 'trade':
                await self._convert_binance_trade(htx_symbol, payload)
            elif stream_type.startswith('depth'):
                await self._convert_binance_depth(htx_symbol, payload)

        except Exception as e:
            logger.error(f"Error processing Binance message: {e}")

    async def _convert_binance_kline(self, htx_symbol: str, kline_data: Dict):
        """Convert Binance kline to HTX format."""
        try:
            k = kline_data['k']

            htx_kline = {
                "ch": f"market.{htx_symbol}.kline.1s",
                "ts": int(time.time() * 1000),
                "tick": {
                    "id": int(k['t'] / 1000),
                    "open": float(k['o']),
                    "close": float(k['c']),
                    "high": float(k['h']),
                    "low": float(k['l']),
                    "amount": float(k['v']),
                    "vol": float(k['q']),
                    "count": int(k['n'])
                }
            }

            if self.publisher:
                self.publisher("htx.kline", time.time(), htx_kline)

        except Exception as e:
            logger.error(f"Error converting Binance kline: {e}")

    async def _convert_binance_trade(self, htx_symbol: str, trade_data: Dict):
        """Convert Binance trade to HTX format."""
        try:
            htx_trade = {
                "ch": f"market.{htx_symbol}.trade.detail",
                "ts": int(time.time() * 1000),
                "tick": {
                    "data": [{
                        "id": trade_data['t'],
                        "ts": trade_data['T'],
                        "price": float(trade_data['p']),
                        "amount": float(trade_data['q']),
                        "direction": "buy" if trade_data['m'] else "sell"
                    }]
                }
            }

            if self.publisher:
                self.publisher("htx.trade", time.time(), htx_trade)

        except Exception as e:
            logger.error(f"Error converting Binance trade: {e}")

    async def _convert_binance_depth(self, htx_symbol: str, depth_data: Dict):
        """Convert Binance depth to HTX format."""
        try:
            htx_depth = {
                "ch": f"market.{htx_symbol}.depth.step0",
                "ts": int(time.time() * 1000),
                "tick": {
                    "bids": [[float(bid[0]), float(bid[1])] for bid in depth_data['bids']],
                    "asks": [[float(ask[0]), float(ask[1])] for ask in depth_data['asks']],
                    "version": depth_data.get('lastUpdateId', int(time.time())),
                    "ts": int(time.time() * 1000)
                }
            }

            if self.publisher:
                self.publisher("htx.orderbook", time.time(), htx_depth)

        except Exception as e:
            logger.error(f"Error converting Binance depth: {e}")

    async def close(self):
        """Close all connections."""
        self.running = False

        for exchange_id, ws in self.active_connections.items():
            try:
                await ws.close()
                logger.info(f"Closed {exchange_id} connection")
            except Exception as e:
                logger.error(f"Error closing {exchange_id}: {e}")

        self.active_connections.clear()

    def get_status(self) -> Dict[str, str]:
        """Get status of all exchanges."""
        return {
            exchange_id: config["status"]
            for exchange_id, config in self.exchanges.items()
        }

    async def _handle_bybit_messages(self, ws):
        """Handle Bybit WebSocket messages."""
        try:
            async for message in ws:
                data = json.loads(message)
                await self._process_bybit_message(data)
        except Exception as e:
            logger.error(f"Bybit message handling error: {e}")
            self.exchanges["bybit"]["status"] = "disconnected"

    async def _process_bybit_message(self, data: Dict):
        """Process Bybit message and convert to HTX format."""
        try:
            # Bybit message processing logic would go here
            # For now, just log the message
            logger.debug(f"Bybit message: {data}")
        except Exception as e:
            logger.error(f"Error processing Bybit message: {e}")

    async def _handle_okx_messages(self, ws):
        """Handle OKX WebSocket messages."""
        try:
            async for message in ws:
                data = json.loads(message)
                await self._process_okx_message(data)
        except Exception as e:
            logger.error(f"OKX message handling error: {e}")
            self.exchanges["okx"]["status"] = "disconnected"

    async def _process_okx_message(self, data: Dict):
        """Process OKX message and convert to HTX format."""
        try:
            # OKX message processing logic would go here
            # For now, just log the message
            logger.debug(f"OKX message: {data}")
        except Exception as e:
            logger.error(f"Error processing OKX message: {e}")

    async def _handle_gate_messages(self, ws):
        """Handle Gate.io WebSocket messages."""
        try:
            async for message in ws:
                data = json.loads(message)
                await self._process_gate_message(data)
        except Exception as e:
            logger.error(f"Gate.io message handling error: {e}")
            self.exchanges["gate"]["status"] = "disconnected"

    async def _process_gate_message(self, data: Dict):
        """Process Gate.io message and convert to HTX format."""
        try:
            # Gate.io message processing logic would go here
            # For now, just log the message
            logger.debug(f"Gate.io message: {data}")
        except Exception as e:
            logger.error(f"Error processing Gate.io message: {e}")
