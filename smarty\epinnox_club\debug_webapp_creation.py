#!/usr/bin/env python3
"""
Debug script to test web app creation specifically.
"""

import sys
import traceback
import asyncio
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_webapp_creation():
    """Test web app creation step by step."""
    try:
        print("Testing web app creation...")
        from app import MoneyCircleApp

        # Create app instance
        print("1. Creating MoneyCircleApp instance...")
        app = MoneyCircleApp('development')
        print("✅ MoneyCircleApp instance created")

        # Test create_app method
        print("2. Testing create_app method...")
        webapp = await app.create_app()
        print("✅ Web app created successfully")

        return True
    except Exception as e:
        print(f"❌ Web app creation error: {e}")
        traceback.print_exc()
        return False

def test_session_key_generation():
    """Test session key generation specifically."""
    try:
        print("Testing session key generation...")
        from config import get_config
        from cryptography.fernet import Fernet
        import base64

        config = get_config('development')
        print(f"SECRET_KEY: {config.SECRET_KEY[:20]}... (length: {len(config.SECRET_KEY)})")

        # Test the key conversion logic from app.py
        if isinstance(config.SECRET_KEY, str):
            # Convert hex string to bytes and pad/truncate to 32 bytes
            key_bytes = bytes.fromhex(config.SECRET_KEY)[:32]
            key_bytes = key_bytes.ljust(32, b'\0')  # Pad with zeros if needed
            secret_key = base64.urlsafe_b64encode(key_bytes)
            print(f"✅ Converted key: {secret_key[:20]}... (length: {len(secret_key)})")

            # Test if it's a valid Fernet key
            fernet = Fernet(secret_key)
            print("✅ Valid Fernet key created")

            # Test encryption/decryption
            test_data = b"test data"
            encrypted = fernet.encrypt(test_data)
            decrypted = fernet.decrypt(encrypted)
            print(f"✅ Encryption test: {test_data} -> {encrypted[:20]}... -> {decrypted}")

        return True
    except Exception as e:
        print(f"❌ Session key error: {e}")
        traceback.print_exc()
        return False

def test_aiohttp_session():
    """Test aiohttp session setup."""
    try:
        print("Testing aiohttp session setup...")
        from aiohttp import web
        from aiohttp_session import setup
        from aiohttp_session.cookie_storage import EncryptedCookieStorage
        from config import get_config

        # Create a test app
        app = web.Application()

        # Use the same logic as in app.py
        config = get_config('development')
        if isinstance(config.SECRET_KEY, str):
            # Convert hex string to bytes and pad/truncate to 32 bytes
            secret_key = bytes.fromhex(config.SECRET_KEY)[:32]
            secret_key = secret_key.ljust(32, b'\0')  # Pad with zeros if needed
        else:
            secret_key = config.SECRET_KEY[:32].ljust(32, b'\0')

        print(f"✅ Generated session key: {secret_key[:20]}... (length: {len(secret_key)})")

        # Setup session storage
        setup(app, EncryptedCookieStorage(secret_key))
        print("✅ Session storage setup successful")

        return True
    except Exception as e:
        print(f"❌ aiohttp session error: {e}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🔍 Debugging web app creation...")

    tests = [
        test_session_key_generation,
        test_aiohttp_session,
        test_webapp_creation
    ]

    for test in tests:
        print(f"\n{'='*50}")
        if asyncio.iscoroutinefunction(test):
            result = await test()
        else:
            result = test()

        if not result:
            print(f"❌ Test failed: {test.__name__}")
            break
        print(f"✅ Test passed: {test.__name__}")

    print(f"\n{'='*50}")
    print("🏁 Debug complete")

if __name__ == '__main__':
    asyncio.run(main())
