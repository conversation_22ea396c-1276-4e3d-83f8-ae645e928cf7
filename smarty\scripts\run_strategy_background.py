#!/usr/bin/env python3
"""
Background Strategy Runner
Run individual strategies in background mode for testing
"""

import subprocess
import sys
import time
import signal
from pathlib import Path
import colorama
from colorama import Fore, Style

colorama.init(autoreset=True)

class BackgroundStrategyRunner:
    """Run strategies in background mode."""
    
    def __init__(self):
        self.processes = {}
        
        # Strategy commands
        self.strategies = {
            'smart_model_integrated': {
                'command': 'python orchestrator.py --debug',
                'description': 'Smart Model Integrated Strategy',
                'log_file': 'logs/smart_model_integrated_bg.log'
            },
            'smart_strategy_only': {
                'command': 'python run_smart_strategy_live.py',
                'description': 'Smart Strategy Only',
                'log_file': 'logs/smart_strategy_only_bg.log'
            },
            'order_flow': {
                'command': 'python live_dataframe_strategy_runner.py',
                'description': 'Order Flow Strategy',
                'log_file': 'logs/order_flow_bg.log'
            },
            'data_producer': {
                'command': 'python feeds/htx_data_producer.py',
                'description': 'HTX Data Producer',
                'log_file': 'logs/data_producer_bg.log'
            },
            'dashboard': {
                'command': 'python live_dashboard.py',
                'description': 'Live Dashboard',
                'log_file': 'logs/dashboard_bg.log'
            }
        }
    
    def start_strategy(self, strategy_name: str, detached: bool = False) -> bool:
        """Start a strategy in background."""
        if strategy_name not in self.strategies:
            print(f"{Fore.RED}❌ Unknown strategy: {strategy_name}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Available strategies: {', '.join(self.strategies.keys())}{Style.RESET_ALL}")
            return False
        
        strategy = self.strategies[strategy_name]
        
        # Ensure logs directory exists
        Path('logs').mkdir(exist_ok=True)
        
        try:
            print(f"{Fore.YELLOW}🚀 Starting {strategy['description']} in background...{Style.RESET_ALL}")
            
            # Open log file
            log_file = open(strategy['log_file'], 'w')
            
            if detached:
                # Fully detached mode - no connection to parent
                process = subprocess.Popen(
                    strategy['command'].split(),
                    cwd=Path.cwd(),
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    start_new_session=True  # Detach from parent session
                )
            else:
                # Background mode - still connected to parent
                process = subprocess.Popen(
                    strategy['command'].split(),
                    cwd=Path.cwd(),
                    stdout=log_file,
                    stderr=subprocess.STDOUT
                )
            
            # Brief check if process started
            time.sleep(2)
            
            if process.poll() is None:
                self.processes[strategy_name] = {
                    'process': process,
                    'log_file': log_file,
                    'log_path': strategy['log_file']
                }
                print(f"{Fore.GREEN}✅ {strategy['description']} started (PID: {process.pid}){Style.RESET_ALL}")
                print(f"{Fore.CYAN}📝 Logs: {strategy['log_file']}{Style.RESET_ALL}")
                return True
            else:
                log_file.close()
                print(f"{Fore.RED}❌ {strategy['description']} failed to start{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start {strategy['description']}: {e}{Style.RESET_ALL}")
            return False
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """Stop a running strategy."""
        if strategy_name not in self.processes:
            print(f"{Fore.YELLOW}⚠️ Strategy {strategy_name} is not running{Style.RESET_ALL}")
            return False
        
        try:
            proc_info = self.processes[strategy_name]
            process = proc_info['process']
            log_file = proc_info['log_file']
            
            print(f"{Fore.YELLOW}🛑 Stopping {strategy_name}...{Style.RESET_ALL}")
            
            # Terminate process
            process.terminate()
            
            try:
                process.wait(timeout=10)
                print(f"{Fore.GREEN}✅ {strategy_name} stopped gracefully{Style.RESET_ALL}")
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                print(f"{Fore.YELLOW}⚠️ {strategy_name} force killed{Style.RESET_ALL}")
            
            # Close log file
            log_file.close()
            
            # Remove from processes
            del self.processes[strategy_name]
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error stopping {strategy_name}: {e}{Style.RESET_ALL}")
            return False
    
    def status(self):
        """Show status of all strategies."""
        print(f"{Fore.CYAN + Style.BRIGHT}📊 STRATEGY STATUS{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'='*50}{Style.RESET_ALL}")
        
        for name, strategy in self.strategies.items():
            if name in self.processes:
                process = self.processes[name]['process']
                if process.poll() is None:
                    status = f"{Fore.GREEN}✅ RUNNING (PID: {process.pid}){Style.RESET_ALL}"
                    log_path = self.processes[name]['log_path']
                    print(f"{strategy['description']}: {status}")
                    print(f"  📝 Log: {log_path}")
                else:
                    status = f"{Fore.RED}❌ STOPPED{Style.RESET_ALL}"
                    print(f"{strategy['description']}: {status}")
            else:
                status = f"{Fore.YELLOW}⚪ NOT STARTED{Style.RESET_ALL}"
                print(f"{strategy['description']}: {status}")
    
    def tail_logs(self, strategy_name: str, lines: int = 20):
        """Show recent log entries for a strategy."""
        if strategy_name not in self.strategies:
            print(f"{Fore.RED}❌ Unknown strategy: {strategy_name}{Style.RESET_ALL}")
            return
        
        log_path = self.strategies[strategy_name]['log_file']
        
        if not Path(log_path).exists():
            print(f"{Fore.YELLOW}⚠️ No log file found: {log_path}{Style.RESET_ALL}")
            return
        
        try:
            with open(log_path, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                print(f"{Fore.CYAN}📝 Last {len(recent_lines)} lines from {strategy_name}:{Style.RESET_ALL}")
                print(f"{Fore.WHITE}{'='*50}{Style.RESET_ALL}")
                
                for line in recent_lines:
                    print(line.rstrip())
                    
        except Exception as e:
            print(f"{Fore.RED}❌ Error reading log: {e}{Style.RESET_ALL}")
    
    def cleanup_all(self):
        """Stop all running strategies."""
        print(f"{Fore.YELLOW}🧹 Stopping all strategies...{Style.RESET_ALL}")
        
        for strategy_name in list(self.processes.keys()):
            self.stop_strategy(strategy_name)
        
        print(f"{Fore.GREEN}✅ All strategies stopped{Style.RESET_ALL}")

def main():
    """Main CLI interface."""
    runner = BackgroundStrategyRunner()
    
    if len(sys.argv) < 2:
        print(f"{Fore.CYAN + Style.BRIGHT}Background Strategy Runner{Style.RESET_ALL}")
        print(f"\nUsage: python {sys.argv[0]} <command> [strategy_name]")
        print(f"\nCommands:")
        print(f"  start <strategy>     - Start strategy in background")
        print(f"  start-detached <strategy> - Start strategy fully detached")
        print(f"  stop <strategy>      - Stop running strategy")
        print(f"  status               - Show all strategy status")
        print(f"  logs <strategy>      - Show recent logs")
        print(f"  cleanup              - Stop all strategies")
        print(f"\nAvailable strategies:")
        for name, strategy in runner.strategies.items():
            print(f"  {name:<20} - {strategy['description']}")
        return
    
    command = sys.argv[1].lower()
    
    # Setup signal handler for cleanup
    def signal_handler(signum, frame):
        print(f"\n{Fore.YELLOW}🛑 Received signal {signum}. Cleaning up...{Style.RESET_ALL}")
        runner.cleanup_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        if command == 'start':
            if len(sys.argv) < 3:
                print(f"{Fore.RED}❌ Please specify strategy name{Style.RESET_ALL}")
                return
            strategy_name = sys.argv[2]
            runner.start_strategy(strategy_name, detached=False)
            
        elif command == 'start-detached':
            if len(sys.argv) < 3:
                print(f"{Fore.RED}❌ Please specify strategy name{Style.RESET_ALL}")
                return
            strategy_name = sys.argv[2]
            runner.start_strategy(strategy_name, detached=True)
            
        elif command == 'stop':
            if len(sys.argv) < 3:
                print(f"{Fore.RED}❌ Please specify strategy name{Style.RESET_ALL}")
                return
            strategy_name = sys.argv[2]
            runner.stop_strategy(strategy_name)
            
        elif command == 'status':
            runner.status()
            
        elif command == 'logs':
            if len(sys.argv) < 3:
                print(f"{Fore.RED}❌ Please specify strategy name{Style.RESET_ALL}")
                return
            strategy_name = sys.argv[2]
            lines = int(sys.argv[3]) if len(sys.argv) > 3 else 20
            runner.tail_logs(strategy_name, lines)
            
        elif command == 'cleanup':
            runner.cleanup_all()
            
        else:
            print(f"{Fore.RED}❌ Unknown command: {command}{Style.RESET_ALL}")
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Interrupted by user{Style.RESET_ALL}")
        runner.cleanup_all()
    except Exception as e:
        print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
