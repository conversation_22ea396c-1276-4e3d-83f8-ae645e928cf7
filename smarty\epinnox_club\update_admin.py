#!/usr/bin/env python3
"""
Update user role to admin
"""

import sqlite3
import os

def update_admin_role():
    try:
        # Check if database exists
        db_path = 'data/money_circle.db'
        if not os.path.exists(db_path):
            print(f"Database not found at {db_path}")
            return

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # First check if user exists
        cursor.execute("SELECT username, role FROM users WHERE username = 'epinnox'")
        result = cursor.fetchone()

        if result:
            print(f"Found user {result[0]} with role: {result[1]}")

            # Update epinnox to admin
            cursor.execute("UPDATE users SET role = 'admin' WHERE username = 'epinnox'")

            # Verify update
            cursor.execute("SELECT username, role FROM users WHERE username = 'epinnox'")
            result = cursor.fetchone()

            if result:
                print(f"User {result[0]} role updated to: {result[1]}")

            conn.commit()
        else:
            print("User epinnox not found")

            # Show all users
            cursor.execute("SELECT username, role FROM users LIMIT 5")
            users = cursor.fetchall()
            print("Available users:")
            for user in users:
                print(f"  {user[0]}: {user[1]}")

        conn.close()

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_admin_role()
