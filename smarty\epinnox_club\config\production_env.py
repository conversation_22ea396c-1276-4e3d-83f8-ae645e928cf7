#!/usr/bin/env python3
"""
Production Environment Configuration for Money Circle
Secure environment variable management for live HTX API integration
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from cryptography.fernet import Fernet
import base64
import json

logger = logging.getLogger(__name__)

@dataclass
class HTXAPIConfig:
    """HTX API configuration for production."""
    api_key: str
    api_secret: str
    passphrase: Optional[str] = None
    environment: str = "mainnet"  # mainnet or testnet
    base_url: str = "https://api-usdt.linear.contract.huobi.pro"
    ws_url: str = "wss://api-usdt.linear.contract.huobi.pro/ws"
    rate_limit: int = 100  # requests per minute
    timeout: int = 30  # seconds

@dataclass
class ExchangeAPIConfig:
    """Multi-exchange API configuration."""
    htx: HTXAPIConfig
    binance: Optional[Dict[str, str]] = None
    bybit: Optional[Dict[str, str]] = None

@dataclass
class RiskManagementConfig:
    """Risk management configuration."""
    max_position_size: float = 1000.0  # USDT
    max_leverage: int = 10
    max_daily_loss: float = 500.0  # USDT
    max_drawdown_pct: float = 5.0  # 5%
    stop_loss_pct: float = 2.0  # 2%
    take_profit_pct: float = 4.0  # 4%
    position_sizing_method: str = "fixed"  # fixed, kelly, volatility
    risk_per_trade_pct: float = 1.0  # 1% of portfolio per trade

@dataclass
class ProductionConfig:
    """Complete production configuration."""
    environment: str
    debug: bool
    exchanges: ExchangeAPIConfig
    risk_management: RiskManagementConfig
    database_url: str
    redis_url: Optional[str] = None
    monitoring_enabled: bool = True
    backup_enabled: bool = True
    ssl_enabled: bool = True

class ProductionEnvironmentManager:
    """Manages production environment configuration and secrets."""
    
    def __init__(self):
        self.environment = os.getenv('ENVIRONMENT', 'development')
        self.encryption_key = self._get_or_create_encryption_key()
        self.config = self._load_configuration()
        
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data."""
        key_env = os.getenv('ENCRYPTION_KEY')
        if key_env:
            return base64.urlsafe_b64decode(key_env.encode())
        
        # Generate new key for development
        if self.environment == 'development':
            key = Fernet.generate_key()
            logger.warning("Generated new encryption key for development. Set ENCRYPTION_KEY env var for production.")
            return key
        else:
            raise ValueError("ENCRYPTION_KEY environment variable required for production")
    
    def _load_configuration(self) -> ProductionConfig:
        """Load production configuration from environment variables."""
        try:
            # HTX API Configuration
            htx_config = HTXAPIConfig(
                api_key=self._get_required_env('HTX_API_KEY'),
                api_secret=self._get_required_env('HTX_API_SECRET'),
                passphrase=os.getenv('HTX_PASSPHRASE'),
                environment=os.getenv('HTX_ENVIRONMENT', 'testnet'),
                base_url=os.getenv('HTX_BASE_URL', 'https://api-usdt.linear.contract.huobi.pro'),
                ws_url=os.getenv('HTX_WS_URL', 'wss://api-usdt.linear.contract.huobi.pro/ws'),
                rate_limit=int(os.getenv('HTX_RATE_LIMIT', '100')),
                timeout=int(os.getenv('HTX_TIMEOUT', '30'))
            )
            
            # Exchange configurations
            exchanges = ExchangeAPIConfig(
                htx=htx_config,
                binance=self._get_optional_exchange_config('BINANCE'),
                bybit=self._get_optional_exchange_config('BYBIT')
            )
            
            # Risk management configuration
            risk_config = RiskManagementConfig(
                max_position_size=float(os.getenv('MAX_POSITION_SIZE', '1000.0')),
                max_leverage=int(os.getenv('MAX_LEVERAGE', '10')),
                max_daily_loss=float(os.getenv('MAX_DAILY_LOSS', '500.0')),
                max_drawdown_pct=float(os.getenv('MAX_DRAWDOWN_PCT', '5.0')),
                stop_loss_pct=float(os.getenv('STOP_LOSS_PCT', '2.0')),
                take_profit_pct=float(os.getenv('TAKE_PROFIT_PCT', '4.0')),
                position_sizing_method=os.getenv('POSITION_SIZING_METHOD', 'fixed'),
                risk_per_trade_pct=float(os.getenv('RISK_PER_TRADE_PCT', '1.0'))
            )
            
            # Complete configuration
            config = ProductionConfig(
                environment=self.environment,
                debug=os.getenv('DEBUG', 'false').lower() == 'true',
                exchanges=exchanges,
                risk_management=risk_config,
                database_url=os.getenv('DATABASE_URL', 'sqlite:///data/money_circle.db'),
                redis_url=os.getenv('REDIS_URL'),
                monitoring_enabled=os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',
                backup_enabled=os.getenv('BACKUP_ENABLED', 'true').lower() == 'true',
                ssl_enabled=os.getenv('SSL_ENABLED', 'true').lower() == 'true'
            )
            
            logger.info(f"✅ Production configuration loaded for environment: {self.environment}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load production configuration: {e}")
            raise
    
    def _get_required_env(self, key: str) -> str:
        """Get required environment variable."""
        value = os.getenv(key)
        if not value:
            if self.environment == 'production':
                raise ValueError(f"Required environment variable {key} not set")
            else:
                # Return demo values for development
                demo_values = {
                    'HTX_API_KEY': 'demo_api_key_for_development',
                    'HTX_API_SECRET': 'demo_api_secret_for_development'
                }
                return demo_values.get(key, f'demo_{key.lower()}')
        return value
    
    def _get_optional_exchange_config(self, exchange_prefix: str) -> Optional[Dict[str, str]]:
        """Get optional exchange configuration."""
        api_key = os.getenv(f'{exchange_prefix}_API_KEY')
        api_secret = os.getenv(f'{exchange_prefix}_API_SECRET')
        
        if api_key and api_secret:
            config = {
                'api_key': api_key,
                'api_secret': api_secret
            }
            
            # Optional passphrase for some exchanges
            passphrase = os.getenv(f'{exchange_prefix}_PASSPHRASE')
            if passphrase:
                config['passphrase'] = passphrase
                
            return config
        
        return None
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        fernet = Fernet(self.encryption_key)
        encrypted_data = fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        fernet = Fernet(self.encryption_key)
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = fernet.decrypt(decoded_data)
        return decrypted_data.decode()
    
    def validate_api_credentials(self) -> Dict[str, bool]:
        """Validate API credentials for all configured exchanges."""
        validation_results = {}
        
        # Validate HTX credentials
        try:
            htx_config = self.config.exchanges.htx
            if htx_config.api_key and htx_config.api_secret:
                # Basic validation - check if credentials are not demo values
                is_valid = (
                    not htx_config.api_key.startswith('demo_') and
                    not htx_config.api_secret.startswith('demo_') and
                    len(htx_config.api_key) > 10 and
                    len(htx_config.api_secret) > 10
                )
                validation_results['htx'] = is_valid
            else:
                validation_results['htx'] = False
        except Exception as e:
            logger.error(f"HTX credential validation error: {e}")
            validation_results['htx'] = False
        
        # Validate other exchanges if configured
        for exchange in ['binance', 'bybit']:
            exchange_config = getattr(self.config.exchanges, exchange)
            if exchange_config:
                try:
                    is_valid = (
                        len(exchange_config['api_key']) > 10 and
                        len(exchange_config['api_secret']) > 10
                    )
                    validation_results[exchange] = is_valid
                except:
                    validation_results[exchange] = False
            else:
                validation_results[exchange] = None  # Not configured
        
        return validation_results
    
    def get_htx_config(self) -> HTXAPIConfig:
        """Get HTX API configuration."""
        return self.config.exchanges.htx
    
    def get_risk_config(self) -> RiskManagementConfig:
        """Get risk management configuration."""
        return self.config.risk_management
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == 'production'
    
    def is_live_trading_enabled(self) -> bool:
        """Check if live trading is enabled."""
        return (
            self.config.exchanges.htx.environment == 'mainnet' and
            self.validate_api_credentials()['htx']
        )
    
    def get_environment_status(self) -> Dict[str, Any]:
        """Get comprehensive environment status."""
        validation_results = self.validate_api_credentials()
        
        return {
            'environment': self.environment,
            'debug': self.config.debug,
            'live_trading_enabled': self.is_live_trading_enabled(),
            'htx_environment': self.config.exchanges.htx.environment,
            'api_credentials_valid': validation_results,
            'risk_management': {
                'max_position_size': self.config.risk_management.max_position_size,
                'max_leverage': self.config.risk_management.max_leverage,
                'max_daily_loss': self.config.risk_management.max_daily_loss,
                'stop_loss_pct': self.config.risk_management.stop_loss_pct
            },
            'monitoring_enabled': self.config.monitoring_enabled,
            'backup_enabled': self.config.backup_enabled,
            'ssl_enabled': self.config.ssl_enabled
        }

# Global instance
production_env = ProductionEnvironmentManager()

def get_production_config() -> ProductionConfig:
    """Get the global production configuration."""
    return production_env.config

def get_htx_config() -> HTXAPIConfig:
    """Get HTX API configuration."""
    return production_env.get_htx_config()

def get_risk_config() -> RiskManagementConfig:
    """Get risk management configuration."""
    return production_env.get_risk_config()

def is_production() -> bool:
    """Check if running in production."""
    return production_env.is_production()

def is_live_trading_enabled() -> bool:
    """Check if live trading is enabled."""
    return production_env.is_live_trading_enabled()
