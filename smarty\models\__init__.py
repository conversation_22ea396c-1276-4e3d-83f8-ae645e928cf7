"""
Models package for the smart-trader system.
Contains non-LLM models for market analysis.
"""

from typing import Protocol, Dict, Any


class BaseModel(Protocol):
    """Protocol for all models."""
    
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.
        
        Args:
            features: Dictionary of input features
            
        Returns:
            Dictionary of prediction results
        """
        ...
