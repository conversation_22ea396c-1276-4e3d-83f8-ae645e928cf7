#!/usr/bin/env python3
"""
IMMEDIATE TEST: Security Middleware Fix
Quick test to verify the security middleware parameter fix works
"""

import asyncio
import aiohttp
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_middleware_fix():
    """Test that the middleware fix resolves the 'Application' object error."""
    logger.info("🧪 TESTING SECURITY MIDDLEWARE FIX")
    logger.info("=" * 50)
    
    base_url = "http://localhost:8086"
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # Test 1: Health endpoint (should work)
            logger.info("📋 Testing health endpoint...")
            try:
                async with session.get(f"{base_url}/health") as resp:
                    if resp.status == 200:
                        logger.info("✅ Health endpoint working")
                    else:
                        logger.error(f"❌ Health endpoint failed: {resp.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Health endpoint error: {e}")
                return False
            
            # Test 2: Login page (this was failing before)
            logger.info("📋 Testing login page...")
            try:
                async with session.get(f"{base_url}/login") as resp:
                    if resp.status == 200:
                        logger.info("✅ Login page working - NO 'Application' object error!")
                        return True
                    else:
                        logger.error(f"❌ Login page failed: {resp.status}")
                        return False
            except Exception as e:
                logger.error(f"❌ Login page error: {e}")
                return False
    
    except Exception as e:
        logger.error(f"❌ Test session error: {e}")
        return False

async def main():
    """Main test function."""
    print("🔧 Testing Security Middleware Fix")
    print("Verifying 'Application' object error is resolved")
    print()
    
    try:
        success = await test_middleware_fix()
        
        if success:
            print("\n🎉 MIDDLEWARE FIX SUCCESSFUL!")
            print("✅ Security middleware working correctly")
            print("✅ No more 'Application' object errors")
            print("✅ Login page accessible")
            print("✅ Platform ready for full testing")
            print("\n🌐 Access: http://localhost:8086")
            print("🔐 Login: epinnox / securepass123")
        else:
            print("\n❌ MIDDLEWARE STILL HAS ISSUES")
            print("🔧 Check server logs for more details")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        return 1

if __name__ == '__main__':
    exit(asyncio.run(main()))
