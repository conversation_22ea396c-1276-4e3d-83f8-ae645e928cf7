#!/usr/bin/env python3
"""
Check database schema and create admin user if needed
"""

import sqlite3
import bcrypt
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database():
    """Check database schema and create admin user."""
    db_path = 'data/money_circle.db'
    
    try:
        # Ensure data directory exists
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== DATABASE SCHEMA CHECK ===")
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"Tables: {tables}")
        
        # Check users table schema
        if 'users' in tables:
            cursor.execute("PRAGMA table_info(users)")
            user_columns = [row[1] for row in cursor.fetchall()]
            print(f"Users table columns: {user_columns}")
            
            # Check if epinnox user exists
            cursor.execute("SELECT username, email, role FROM users WHERE username = 'epinnox'")
            user = cursor.fetchone()
            if user:
                print(f"Epinnox user found: {user}")
            else:
                print("Epinnox user NOT found - creating...")
                
                # Create epinnox user
                hashed_password = bcrypt.hashpw('securepass123'.encode('utf-8'), bcrypt.gensalt())
                cursor.execute("""
                    INSERT INTO users (username, email, hashed_password, role, email_verified, agreement_accepted, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, ('epinnox', '<EMAIL>', hashed_password, 'admin', True, True, True))
                conn.commit()
                print("✅ Epinnox admin user created!")
        
        # Check user_sessions table schema
        if 'user_sessions' in tables:
            cursor.execute("PRAGMA table_info(user_sessions)")
            session_columns = [row[1] for row in cursor.fetchall()]
            print(f"User_sessions table columns: {session_columns}")
        else:
            print("User_sessions table NOT found")
            
        # Check notifications table schema
        if 'notifications' in tables:
            cursor.execute("PRAGMA table_info(notifications)")
            notification_columns = [row[1] for row in cursor.fetchall()]
            print(f"Notifications table columns: {notification_columns}")
        else:
            print("Notifications table NOT found")
            
        conn.close()
        print("✅ Database check completed")
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_database()
