#!/usr/bin/env python3
"""
Money Circle Investment Club Configuration
Configuration settings for the Epinnox multi-user trading platform.
"""

import os
import secrets
from pathlib import Path

class Config:
    """Base configuration class."""

    # Application settings
    APP_NAME = "Money Circle"
    APP_DESCRIPTION = "Epinnox Investment Club Platform"
    VERSION = "1.0.0"

    # Server settings
    HOST = os.getenv("HOST", "localhost")
    PORT = int(os.getenv("PORT", 8086))
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"

    # Database settings
    DATABASE_PATH = os.getenv("DATABASE_PATH", "data/money_circle.db")
    SQLITE_BUS_PATH = os.getenv("SQLITE_BUS_PATH", "data/bus.db")

    # Security settings
    SECRET_KEY = os.getenv("SECRET_KEY", secrets.token_hex(32))
    SESSION_TIMEOUT = int(os.getenv("SESSION_TIMEOUT", 24 * 60 * 60))  # 24 hours
    ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")  # Will be handled by encryption manager

    # Authentication settings
    MAX_LOGIN_ATTEMPTS = int(os.getenv("MAX_LOGIN_ATTEMPTS", 5))
    LOCKOUT_DURATION = int(os.getenv("LOCKOUT_DURATION", 300))  # 5 minutes
    PASSWORD_MIN_LENGTH = int(os.getenv("PASSWORD_MIN_LENGTH", 8))

    # Trading settings
    DEFAULT_SYMBOL = os.getenv("DEFAULT_SYMBOL", "BTC-USDT")
    SUPPORTED_EXCHANGES = ["HTX", "Binance", "Bybit"]
    MAX_POSITIONS_PER_USER = int(os.getenv("MAX_POSITIONS_PER_USER", 10))

    # Club settings
    MIN_VOTES_FOR_STRATEGY = int(os.getenv("MIN_VOTES_FOR_STRATEGY", 3))
    STRATEGY_VOTE_DURATION = int(os.getenv("STRATEGY_VOTE_DURATION", 7 * 24 * 60 * 60))  # 7 days

    # Rate limiting
    API_RATE_LIMIT = int(os.getenv("API_RATE_LIMIT", 100))  # requests per minute
    WEBSOCKET_MAX_CONNECTIONS = int(os.getenv("WEBSOCKET_MAX_CONNECTIONS", 50))

    # Logging settings
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "logs/money_circle.log")

    @classmethod
    def init_directories(cls):
        """Initialize required directories."""
        directories = [
            Path(cls.DATABASE_PATH).parent,
            Path(cls.LOG_FILE).parent,
            "static/css",
            "static/js",
            "static/images",
            "templates"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    LOG_LEVEL = "WARNING"

    # Enhanced security for production
    SESSION_TIMEOUT = 8 * 60 * 60  # 8 hours
    MAX_LOGIN_ATTEMPTS = 3
    LOCKOUT_DURATION = 600  # 10 minutes

    # Production server settings
    HOST = os.getenv("PROD_HOST", "0.0.0.0")
    PORT = int(os.getenv("PROD_PORT", 8085))

    # SSL/HTTPS settings
    SSL_CERT_PATH = os.getenv("SSL_CERT_PATH")
    SSL_KEY_PATH = os.getenv("SSL_KEY_PATH")
    FORCE_HTTPS = os.getenv("FORCE_HTTPS", "True").lower() == "true"

    # Compression settings
    ENABLE_COMPRESSION = os.getenv("ENABLE_COMPRESSION", "True").lower() == "true"
    COMPRESSION_LEVEL = int(os.getenv("COMPRESSION_LEVEL", 6))

    # Security headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' wss: ws:",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
    }

    # Performance settings
    STATIC_FILE_CACHE_TIMEOUT = 31536000  # 1 year
    API_CACHE_TIMEOUT = 300  # 5 minutes

    # Database settings for production
    DATABASE_POOL_SIZE = int(os.getenv("DATABASE_POOL_SIZE", 20))
    DATABASE_TIMEOUT = int(os.getenv("DATABASE_TIMEOUT", 30))

    # Monitoring and health checks
    HEALTH_CHECK_ENABLED = os.getenv("HEALTH_CHECK_ENABLED", "True").lower() == "true"
    METRICS_ENABLED = os.getenv("METRICS_ENABLED", "True").lower() == "true"

    # CDN settings
    CDN_URL = os.getenv("CDN_URL")
    STATIC_URL_PREFIX = os.getenv("STATIC_URL_PREFIX", "/static/")

class TestingConfig(Config):
    """Testing configuration."""
    DEBUG = True
    DATABASE_PATH = "data/test_money_circle.db"
    LOG_LEVEL = "DEBUG"

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """Get configuration based on environment."""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')

    return config_map.get(config_name, DevelopmentConfig)
