"""
Enhanced utility functions for the smart-trader system.

This package provides modular, well-tested utility functions organized by domain:
- time_utils: Time and timestamp utilities
- logging_utils: Enhanced logging setup and management
- auth_utils: Authentication and cryptographic utilities
- async_utils: Async programming helpers and decorators
- math_utils: Mathematical and statistical functions

All utilities include comprehensive error handling, type hints, and performance optimizations.
"""

# Import all utilities for backward compatibility
from .time_utils import (
    utc_timestamp,
    iso_timestamp,
    timestamp_to_datetime,
    datetime_to_timestamp,
    format_duration
)

from .logging_utils import (
    setup_logging,
    get_logger,
    log_function_call,
    log_performance_metrics,
    configure_third_party_loggers,
    add_context_to_logger
)

from .auth_utils import (
    generate_signature,
    generate_signature_base64,
    create_api_signature,
    validate_signature,
    generate_nonce,
    hash_password,
    verify_password,
    create_jwt_payload
)

from .async_utils import (
    retry_async,
    retry_async_decorator,
    timer,
    timeout_after,
    gather_with_concurrency,
    run_periodic,
    Async<PERSON>ontext<PERSON>anager,
    TaskManager
)

from .math_utils import (
    calculate_vwap,
    calculate_rolling_vwap,
    calculate_rsi,
    calculate_rolling_rsi,
    calculate_imbalance,
    normalize_array,
    format_price,
    round_quantity,
    calculate_percentage_change,
    calculate_sharpe_ratio,
    calculate_order_flow_imbalance,
    validate_arrays
)

# Version information
__version__ = "2.0.0"
__author__ = "Smart Trader Team"

# Backward compatibility - maintain original function names
# These are the functions that were in the original utils.py
__all__ = [
    # Time utilities
    "utc_timestamp",
    "iso_timestamp",
    "timestamp_to_datetime",
    "datetime_to_timestamp",
    "format_duration",

    # Logging utilities
    "setup_logging",
    "get_logger",
    "log_function_call",
    "log_performance_metrics",
    "configure_third_party_loggers",
    "add_context_to_logger",

    # Authentication utilities
    "generate_signature",
    "generate_signature_base64",
    "create_api_signature",
    "validate_signature",
    "generate_nonce",
    "hash_password",
    "verify_password",
    "create_jwt_payload",

    # Async utilities
    "retry_async",
    "retry_async_decorator",
    "timer",
    "timeout_after",
    "gather_with_concurrency",
    "run_periodic",
    "AsyncContextManager",
    "TaskManager",

    # Math utilities
    "calculate_vwap",
    "calculate_rolling_vwap",
    "calculate_rsi",
    "calculate_rolling_rsi",
    "calculate_imbalance",
    "normalize_array",
    "format_price",
    "round_quantity",
    "calculate_percentage_change",
    "calculate_sharpe_ratio",
    "calculate_order_flow_imbalance",
    "validate_arrays",

    # Package info
    "__version__",
]

# Convenience imports for common patterns
from .time_utils import utc_timestamp as get_timestamp  # Legacy alias
from .math_utils import calculate_vwap as vwap  # Legacy alias
from .math_utils import calculate_rsi as rsi  # Legacy alias

# Add legacy aliases to __all__
__all__.extend(["get_timestamp", "vwap", "rsi"])

# Module-level configuration
import logging

# Set up default logger for the utils package
_logger = logging.getLogger(__name__)

def get_utils_info():
    """
    Get information about the utils package.

    Returns:
        Dictionary with package information
    """
    return {
        "version": __version__,
        "author": __author__,
        "modules": [
            "time_utils",
            "logging_utils",
            "auth_utils",
            "async_utils",
            "math_utils"
        ],
        "total_functions": len(__all__),
        "description": "Enhanced utility functions for smart-trader system"
    }


def run_all_tests():
    """
    Run tests for all utility modules.

    This function imports and runs the test code from each module.
    """
    import importlib
    import sys

    modules = [
        "core.utils.time_utils",
        "core.utils.logging_utils",
        "core.utils.auth_utils",
        "core.utils.async_utils",
        "core.utils.math_utils"
    ]

    print(f"Running tests for utils package v{__version__}")
    print("=" * 50)

    for module_name in modules:
        try:
            print(f"\nTesting {module_name}...")
            module = importlib.import_module(module_name)

            # Run the module's test code if it exists
            if hasattr(module, '__main__'):
                # Execute the module's test code
                exec(compile(open(module.__file__).read(), module.__file__, 'exec'))
            else:
                print(f"No tests found for {module_name}")

        except Exception as e:
            print(f"Error testing {module_name}: {e}")

    print("\n" + "=" * 50)
    print("All utility tests completed")


# Performance monitoring
_performance_stats = {
    "function_calls": {},
    "total_execution_time": {},
    "error_counts": {}
}


def get_performance_stats():
    """
    Get performance statistics for utility functions.

    Returns:
        Dictionary with performance statistics
    """
    return _performance_stats.copy()


def reset_performance_stats():
    """Reset performance statistics."""
    global _performance_stats
    _performance_stats = {
        "function_calls": {},
        "total_execution_time": {},
        "error_counts": {}
    }


# Initialize third-party logger configuration
configure_third_party_loggers(logging.WARNING)

# Log package initialization
_logger.info(f"Smart Trader utils package v{__version__} initialized")
_logger.debug(f"Available functions: {len(__all__)}")

# Validate critical dependencies
try:
    import numpy as np
    import pandas as pd
    _logger.debug("NumPy and Pandas dependencies available")
except ImportError as e:
    _logger.warning(f"Optional dependencies not available: {e}")

# Export package metadata
__package_info__ = get_utils_info()
