#!/usr/bin/env python3
"""
🚀 Live Trading System Launcher

Unified launcher for the complete Epinnox Smart Trading System.
Starts all components in the correct order for live trading operations.
"""

import asyncio
import subprocess
import sys
import time
import logging
import signal
import os
from pathlib import Path
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveTradingSystemLauncher:
    """Launch and manage the complete live trading system."""
    
    def __init__(self):
        self.processes = {}
        self.running = False
        
        # Component configurations
        self.components = {
            "dashboard": {
                "script": "live_dashboard.py",
                "port": 8082,
                "description": "Main Epinnox Trading Dashboard",
                "critical": True,
                "startup_delay": 2
            },
            "performance_monitor": {
                "script": "real_time_performance_monitor.py", 
                "port": 8083,
                "description": "Real-time Performance Monitor",
                "critical": False,
                "startup_delay": 5
            }
        }
    
    async def launch_system(self):
        """Launch the complete live trading system."""
        logger.info("🚀 Launching Epinnox Live Trading System")
        logger.info("=" * 60)
        
        # Pre-launch checks
        if not await self._pre_launch_checks():
            logger.error("❌ Pre-launch checks failed")
            return False
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.running = True
        
        try:
            # Launch components in order
            await self._launch_components()
            
            # System ready
            await self._system_ready()
            
            # Monitor system health
            await self._monitor_system()
            
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested")
        except Exception as e:
            logger.error(f"❌ System error: {e}")
        finally:
            await self._shutdown_system()
        
        return True
    
    async def _pre_launch_checks(self) -> bool:
        """Perform pre-launch system checks."""
        logger.info("🔍 Performing pre-launch checks...")
        
        checks = [
            ("Configuration files", self._check_config_files),
            ("Database connectivity", self._check_database),
            ("Port availability", self._check_ports),
            ("Dependencies", self._check_dependencies)
        ]
        
        all_passed = True
        for check_name, check_func in checks:
            try:
                result = await check_func()
                if result:
                    logger.info(f"   ✅ {check_name}: PASSED")
                else:
                    logger.error(f"   ❌ {check_name}: FAILED")
                    all_passed = False
            except Exception as e:
                logger.error(f"   ❌ {check_name}: ERROR - {e}")
                all_passed = False
        
        return all_passed
    
    async def _check_config_files(self) -> bool:
        """Check required configuration files exist."""
        required_files = [
            "trading_config_live.yaml",
            "config.yaml"
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                logger.error(f"Missing configuration file: {file_path}")
                return False
        
        return True
    
    async def _check_database(self) -> bool:
        """Check database connectivity."""
        try:
            import sqlite3
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return True
        except Exception as e:
            logger.error(f"Database check failed: {e}")
            return False
    
    async def _check_ports(self) -> bool:
        """Check required ports are available."""
        import socket
        
        for component_name, config in self.components.items():
            port = config["port"]
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    result = s.connect_ex(('localhost', port))
                    if result == 0:
                        logger.warning(f"Port {port} already in use (may be from previous run)")
            except Exception as e:
                logger.error(f"Port check failed for {port}: {e}")
                return False
        
        return True
    
    async def _check_dependencies(self) -> bool:
        """Check required Python dependencies."""
        required_modules = [
            "aiohttp", "websockets", "sqlite3", "yaml", 
            "pandas", "numpy", "asyncio"
        ]
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                logger.error(f"Missing required module: {module}")
                return False
        
        return True
    
    async def _launch_components(self):
        """Launch all system components."""
        logger.info("🔄 Launching system components...")
        
        for component_name, config in self.components.items():
            await self._launch_component(component_name, config)
            
            # Wait for component to start
            await asyncio.sleep(config["startup_delay"])
            
            # Verify component is running
            if not await self._verify_component(component_name, config):
                if config["critical"]:
                    raise Exception(f"Critical component {component_name} failed to start")
                else:
                    logger.warning(f"Non-critical component {component_name} failed to start")
    
    async def _launch_component(self, name: str, config: Dict[str, Any]):
        """Launch a single system component."""
        logger.info(f"🚀 Starting {config['description']}...")
        
        try:
            # Launch the component
            process = subprocess.Popen(
                [sys.executable, config["script"]],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[name] = {
                "process": process,
                "config": config,
                "start_time": time.time()
            }
            
            logger.info(f"   ✅ {name} started (PID: {process.pid})")
            
        except Exception as e:
            logger.error(f"   ❌ Failed to start {name}: {e}")
            raise
    
    async def _verify_component(self, name: str, config: Dict[str, Any]) -> bool:
        """Verify a component is running correctly."""
        if name not in self.processes:
            return False
        
        process = self.processes[name]["process"]
        
        # Check if process is still running
        if process.poll() is not None:
            logger.error(f"❌ Component {name} exited unexpectedly")
            return False
        
        # For components with ports, check if port is responding
        if "port" in config:
            import socket
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(5)
                    result = s.connect_ex(('localhost', config["port"]))
                    if result == 0:
                        logger.info(f"   ✅ {name} responding on port {config['port']}")
                        return True
                    else:
                        logger.warning(f"   ⚠️ {name} not yet responding on port {config['port']}")
                        return False
            except Exception as e:
                logger.warning(f"   ⚠️ Port check failed for {name}: {e}")
                return False
        
        return True
    
    async def _system_ready(self):
        """System is ready for operations."""
        logger.info("\n🎉 EPINNOX LIVE TRADING SYSTEM READY")
        logger.info("=" * 60)
        logger.info("📊 Dashboard: http://localhost:8082/dashboard")
        logger.info("🎯 Performance Monitor: ws://localhost:8083")
        logger.info("🔐 Login: username=epinnox, password=securepass123")
        logger.info("")
        logger.info("💰 Account Balance: $100.00")
        logger.info("📈 Daily Risk Limit: $5.00")
        logger.info("🎯 Position Risk Limit: $2.00")
        logger.info("")
        logger.info("🚀 READY FOR LIVE TRADING OPERATIONS")
        logger.info("=" * 60)
    
    async def _monitor_system(self):
        """Monitor system health and components."""
        logger.info("🔄 System monitoring started...")
        
        while self.running:
            try:
                # Check all components
                failed_components = []
                
                for name, process_info in self.processes.items():
                    process = process_info["process"]
                    config = process_info["config"]
                    
                    if process.poll() is not None:
                        failed_components.append(name)
                        logger.error(f"❌ Component {name} has stopped")
                        
                        # Restart critical components
                        if config["critical"]:
                            logger.info(f"🔄 Restarting critical component {name}")
                            await self._launch_component(name, config)
                
                # Log system status
                if not failed_components:
                    logger.info("✅ All components running normally")
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _shutdown_system(self):
        """Shutdown all system components."""
        logger.info("🛑 Shutting down Epinnox Live Trading System...")
        
        self.running = False
        
        # Terminate all processes
        for name, process_info in self.processes.items():
            process = process_info["process"]
            config = process_info["config"]
            
            logger.info(f"🛑 Stopping {config['description']}...")
            
            try:
                # Graceful shutdown
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                    logger.info(f"   ✅ {name} stopped gracefully")
                except subprocess.TimeoutExpired:
                    # Force kill if needed
                    process.kill()
                    logger.warning(f"   ⚠️ {name} force killed")
                    
            except Exception as e:
                logger.error(f"   ❌ Error stopping {name}: {e}")
        
        logger.info("✅ System shutdown complete")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"🛑 Received signal {signum}, initiating shutdown...")
        self.running = False

async def main():
    """Main launcher."""
    print("""
🚀 EPINNOX LIVE TRADING SYSTEM LAUNCHER
=====================================

This will start the complete live trading system including:
- Main Trading Dashboard (port 8082)
- Real-time Performance Monitor (port 8083)
- Strategy Management System
- Risk Management Controls

⚠️  IMPORTANT: This system is configured for LIVE TRADING with real funds.
    Make sure you understand the risks before proceeding.

Account Configuration:
- Balance: $100.00
- Daily Risk Limit: $5.00 (5%)
- Position Risk Limit: $2.00 (2%)

Press Ctrl+C to stop the system at any time.
""")
    
    # Confirmation prompt
    try:
        response = input("Do you want to start the live trading system? (yes/no): ").lower().strip()
        if response not in ['yes', 'y']:
            print("❌ Launch cancelled")
            return
    except KeyboardInterrupt:
        print("\n❌ Launch cancelled")
        return
    
    # Launch system
    launcher = LiveTradingSystemLauncher()
    success = await launcher.launch_system()
    
    if success:
        print("✅ System shutdown completed successfully")
    else:
        print("❌ System encountered errors during operation")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
