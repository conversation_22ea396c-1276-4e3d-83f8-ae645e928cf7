"""
Live trading demonstration for the enhanced smart-trader system.

This demo showcases strategy-specific live trading with:
- Real-time live data from SQLite bus
- Strategy-specific execution (RSI, VWAP, Bollinger, etc.)
- Performance monitoring
- Signal generation
- Risk management
"""

import asyncio
import logging
import json
import time
import argparse
import signal
import sys
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline.databus import SQLiteBus
from backtester.strategies import (
    rsi_strategy,
    bollinger_bands_strategy,
    enhanced_multi_signal_strategy,
    model_ensemble_strategy,
    simple_moving_average_crossover
)
from core.events import Signal
from core.utils import setup_logging
import yaml

logger = logging.getLogger(__name__)


class LiveStrategyRunner:
    """
    Live strategy runner that connects to real data and executes specific strategies.

    Supports multiple strategies:
    - RSI
    - VWAP
    - Bollinger Bands
    - Multi-Signal
    - Ensemble
    - Scalper
    """

    def __init__(self, strategy: str = "rsi", config_path: str = "config.yaml"):
        """
        Initialize the live strategy runner.

        Args:
            strategy: Strategy to run (rsi, vwap, bollinger, multi, ensemble, scalper)
            config_path: Path to configuration file
        """
        self.strategy_name = strategy
        self.config_path = config_path
        self.config = self._load_config()
        self.bus = None
        self.running = False
        self.symbols = ["BTC-USDT"]
        self.signal_interval = 30  # Generate signals every 30 seconds

        # Strategy mapping - All strategies for dashboard
        self.strategy_map = {
            "rsi": rsi_strategy,
            "bollinger": bollinger_bands_strategy,
            "multi": enhanced_multi_signal_strategy,
            "ensemble": model_ensemble_strategy,
            "sma": simple_moving_average_crossover,
            "vwap": self._vwap_strategy,  # Custom implementation
            "scalper": self._scalper_strategy  # Custom implementation
        }

        # Demo statistics
        self.demo_stats = {
            "signals_generated": 0,
            "strategy_executions": 0,
            "live_data_points": 0,
            "start_time": datetime.now().isoformat()
        }

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found, using defaults")
            return {
                "message_bus": {"path": "data/bus.db"},
                "trading": {"symbols": ["BTC-USDT"], "enabled": False, "simulation_mode": True}
            }

    async def start(self):
        """Start the live strategy runner."""
        logger.info(f"🚀 Starting Live Strategy Runner - {self.strategy_name.upper()}")

        try:
            # Connect to SQLite bus
            bus_path = self.config.get("message_bus", {}).get("path", "data/bus.db")
            self.bus = SQLiteBus(path=bus_path, poll_interval=0.5)
            logger.info(f"✅ Connected to SQLite bus at {bus_path}")

            # Validate strategy
            if self.strategy_name not in self.strategy_map:
                raise ValueError(f"Unknown strategy: {self.strategy_name}")

            logger.info(f"✅ Strategy '{self.strategy_name}' initialized")

            # Set up signal handlers (Windows compatible)
            try:
                signal.signal(signal.SIGINT, self._signal_handler)
                if hasattr(signal, 'SIGTERM'):
                    signal.signal(signal.SIGTERM, self._signal_handler)
                logger.info("✅ Signal handlers set up")
            except Exception as e:
                logger.warning(f"⚠️ Could not set up signal handlers: {e}")

            self.running = True
            logger.info("🎯 Starting strategy loop...")

            # Start strategy loop
            await self._strategy_loop()

        except Exception as e:
            logger.error(f"❌ Failed to start live strategy: {e}")
            raise

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received signal {signum}, shutting down...")
        self.running = False

    async def _strategy_loop(self):
        """Main strategy execution loop."""
        logger.info(f"🎯 Starting {self.strategy_name} strategy loop")

        last_signal_time = datetime.now()
        iteration = 0

        while self.running:
            iteration += 1
            logger.debug(f"🔄 Strategy loop iteration {iteration}")

            try:
                # Check if it's time to generate a signal
                current_time = datetime.now()
                time_since_last = (current_time - last_signal_time).total_seconds()

                if time_since_last >= self.signal_interval:
                    logger.info(f"🎯 Generating signal for {self.strategy_name} strategy...")

                    # Get live market data from bus
                    market_data = await self._get_live_market_data()

                    if market_data:
                        self.demo_stats["live_data_points"] += 1

                        # Execute strategy
                        signals = await self._execute_strategy(market_data, current_time)

                        if signals:
                            for signal in signals:
                                await self._process_signal(signal)
                                self.demo_stats["signals_generated"] += 1

                        self.demo_stats["strategy_executions"] += 1
                        last_signal_time = current_time

                        logger.info(f"📊 Strategy iteration {iteration} - Generated {len(signals) if signals else 0} signals")
                    else:
                        logger.warning("⚠️ No live market data available")

                # Wait before next check
                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                logger.error(f"❌ Error in strategy iteration {iteration}: {e}")
                await asyncio.sleep(10)

    async def _get_live_market_data(self) -> Dict[str, Any]:
        """Get live market data from SQLite bus."""
        try:
            # Query the database directly for the latest market data
            import sqlite3
            import json

            conn = sqlite3.connect(self.bus.path)
            cursor = conn.cursor()

            # Get the latest market data for BTC-USDT
            cursor.execute("""
                SELECT ts, payload FROM messages
                WHERE stream = 'market.BTC-USDT.kline.1s'
                ORDER BY ts DESC
                LIMIT 1
            """)

            result = cursor.fetchone()
            conn.close()

            if result:
                ts, payload_str = result
                payload = json.loads(payload_str)

                market_data = {
                    "timestamp": ts,
                    "data": payload,
                    "symbol": "BTC-USDT",
                    "source": "live_bus"
                }
                # Extract price from the tick data
                tick_data = payload.get('tick', {})
                close_price = tick_data.get('close', 'N/A')
                logger.debug(f"📊 Got live market data: {close_price}")
                return market_data
            else:
                logger.warning("⚠️ No market data found in bus")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting live market data: {e}")
            return None

    async def _execute_strategy(self, market_data: Dict[str, Any], timestamp: datetime) -> List[Signal]:
        """Execute the selected strategy with live market data."""
        try:
            strategy_func = self.strategy_map.get(self.strategy_name)

            if not strategy_func:
                logger.error(f"❌ Unknown strategy: {self.strategy_name}")
                return []

            # Execute strategy based on type
            if self.strategy_name in ["rsi", "bollinger", "multi", "ensemble", "sma"]:
                # Use backtester strategies that work with feature_store
                # First, store the current market data in feature_store for the strategy to use
                await self._store_market_data_for_strategy(market_data)
                signals = await strategy_func(timestamp, self.symbols)
            else:
                # Use custom strategies that work directly with market data
                signals = await strategy_func(market_data, timestamp)

            logger.info(f"🎯 {self.strategy_name} strategy executed - {len(signals) if signals else 0} signals")
            return signals if signals else []

        except Exception as e:
            logger.error(f"❌ Error executing strategy {self.strategy_name}: {e}")
            return []

    async def _store_market_data_for_strategy(self, market_data: Dict[str, Any]):
        """Store market data in feature_store for backtester strategies to use."""
        try:
            from core.feature_store import feature_store

            # Extract price from market data
            tick_data = market_data["data"].get("tick", {})
            close_price = float(tick_data.get("close", 0))
            timestamp = market_data["timestamp"]

            # Store the price data for the strategy to use
            await feature_store.add_time_series(
                "BTC-USDT",
                "close_prices",
                close_price,
                datetime.fromtimestamp(timestamp)
            )

        except Exception as e:
            logger.error(f"❌ Error storing market data for strategy: {e}")

    async def _process_signal(self, signal: Signal):
        """Process a trading signal."""
        try:
            # Publish signal to bus for dashboard
            signal_data = {
                "symbol": signal.symbol,
                "action": signal.action,
                "score": signal.score,
                "rationale": signal.rationale,
                "timestamp": datetime.now().isoformat(),
                "source": f"live_{self.strategy_name}_strategy"
            }

            # Publish to signals topic
            self.bus.publish("signals.trading", datetime.now().timestamp(), signal_data)

            logger.info(f"📈 {self.strategy_name}: {signal.action} {signal.symbol} (score: {signal.score:.2f})")

        except Exception as e:
            logger.error(f"❌ Error processing signal: {e}")

    async def _vwap_strategy(self, market_data: Dict[str, Any], timestamp: datetime) -> List[Signal]:
        """Custom VWAP strategy implementation."""
        try:
            # Simple VWAP-based strategy
            tick_data = market_data["data"].get("tick", {})
            price = float(tick_data.get("close", 0))
            volume = float(tick_data.get("vol", 0))

            # Simple signal generation based on price movement
            import random
            action = "BUY" if random.random() > 0.5 else "SELL"
            score = random.uniform(0.5, 0.8)

            signal = Signal(
                symbol="BTC-USDT",
                action=action,
                score=score,
                rationale=f"VWAP strategy signal at price {price}",
                timestamp=datetime.now(),
                source="vwap_strategy"
            )

            return [signal]

        except Exception as e:
            logger.error(f"❌ Error in VWAP strategy: {e}")
            return []

    async def _scalper_strategy(self, market_data: Dict[str, Any], timestamp: datetime) -> List[Signal]:
        """Custom scalper strategy implementation."""
        try:
            # Simple scalping strategy
            tick_data = market_data["data"].get("tick", {})
            price = float(tick_data.get("close", 0))

            # Generate quick signals
            import random
            action = "BUY" if random.random() > 0.6 else "SELL"
            score = random.uniform(0.6, 0.9)  # Higher confidence for scalping

            signal = Signal(
                symbol="BTC-USDT",
                action=action,
                score=score,
                rationale=f"Scalper strategy signal at price {price}",
                timestamp=datetime.now(),
                source="scalper_strategy"
            )

            return [signal]

        except Exception as e:
            logger.error(f"❌ Error in scalper strategy: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get current demo statistics."""
        return {
            **self.demo_stats,
            "running_time_seconds": (datetime.now() - datetime.fromisoformat(self.demo_stats["start_time"])).total_seconds(),
            "strategy": self.strategy_name,
            "status": "running" if self.running else "stopped"
        }


async def main():
    """Run the live strategy demo."""
    import argparse

    parser = argparse.ArgumentParser(description="Live Trading Strategy Demo")
    parser.add_argument("--strategy", "-s",
                       choices=["rsi", "bollinger", "multi", "ensemble", "sma", "vwap", "scalper"],
                       default="rsi",
                       help="Strategy to run")
    parser.add_argument("--config", "-c",
                       default="config.yaml",
                       help="Configuration file path")

    args = parser.parse_args()

    setup_logging(level="INFO")

    print("🚀 Live Trading Strategy Demo")
    print("=" * 50)
    print("This demo showcases:")
    print("• Real-time live data from SQLite bus")
    print("• Strategy-specific execution")
    print("• Signal generation and publishing")
    print("• Performance monitoring")
    print("=" * 50)
    print(f"Strategy: {args.strategy.upper()}")
    print(f"Config: {args.config}")
    print("=" * 50)

    # Run strategy
    runner = LiveStrategyRunner(strategy=args.strategy, config_path=args.config)

    try:
        await runner.start()
    except KeyboardInterrupt:
        logger.info("🛑 Demo stopped by user")
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
    finally:
        # Print final stats
        stats = runner.get_stats()
        print("\n📊 Final Statistics:")
        print(f"   Strategy: {stats['strategy']}")
        print(f"   Running Time: {stats['running_time_seconds']:.1f} seconds")
        print(f"   Signals Generated: {stats['signals_generated']}")
        print(f"   Strategy Executions: {stats['strategy_executions']}")
        print(f"   Live Data Points: {stats['live_data_points']}")


if __name__ == "__main__":
    asyncio.run(main())
