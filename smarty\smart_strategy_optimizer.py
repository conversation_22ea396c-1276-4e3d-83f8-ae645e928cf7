#!/usr/bin/env python3
"""
Smart Strategy Optimizer - Focused on optimizing the smart_model_integrated_strategy

This tool specifically optimizes and backtests your smart strategy with:
- Component weight optimization
- Threshold tuning
- Signal analysis
- Performance improvement suggestions
"""

import os
import json
import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import yaml

from backtester.backtester import Backtester
from backtester.strategies import smart_model_integrated_strategy
from backtester.optimizer import StrategyOptimizer
from backtester.visualizer import BacktestVisualizer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SmartStrategyOptimizer:
    """Specialized optimizer for the smart_model_integrated_strategy."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the smart strategy optimizer."""
        self.config = self._load_config(config_path)
        self.results = {}

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}

    async def optimize_smart_strategy(
        self,
        symbols: List[str] = ["BTC-USDT"],
        start_date: str = "2023-01-01",
        end_date: str = "2023-12-31",
        optimization_type: str = "comprehensive",  # "weights", "thresholds", "comprehensive"
        output_dir: str = "results/smart_strategy_optimization"
    ) -> Dict[str, Any]:
        """
        Optimize the smart strategy with different approaches.

        Args:
            symbols: List of symbols to test
            start_date: Start date for optimization
            end_date: End date for optimization
            optimization_type: Type of optimization ("weights", "thresholds", "comprehensive")
            output_dir: Output directory for results

        Returns:
            Dictionary containing optimization results
        """
        logger.info(f"🎯 Optimizing Smart Strategy ({optimization_type})")
        logger.info(f"📊 Symbols: {', '.join(symbols)}")
        logger.info(f"📅 Period: {start_date} to {end_date}")

        os.makedirs(output_dir, exist_ok=True)

        results = {
            'optimization_type': optimization_type,
            'symbols': symbols,
            'period': f"{start_date} to {end_date}",
            'baseline_performance': {},
            'optimized_performance': {},
            'best_parameters': {},
            'improvement_analysis': {},
            'component_analysis': {}
        }

        # Step 1: Run baseline test
        logger.info("📊 Running baseline performance test...")
        baseline_results = await self._run_baseline_test(symbols, start_date, end_date, output_dir)
        if baseline_results:
            results['baseline_performance'] = baseline_results

        # Step 2: Run optimization based on type
        if optimization_type == "weights":
            opt_results = await self._optimize_component_weights(symbols, start_date, end_date, output_dir)
        elif optimization_type == "thresholds":
            opt_results = await self._optimize_thresholds(symbols, start_date, end_date, output_dir)
        else:  # comprehensive
            opt_results = await self._comprehensive_optimization(symbols, start_date, end_date, output_dir)

        if opt_results:
            results['optimized_performance'] = opt_results
            results['best_parameters'] = opt_results.get('best_params', {})

        # Step 3: Analyze improvements
        if baseline_results and opt_results:
            improvement_analysis = self._analyze_improvements(baseline_results, opt_results)
            results['improvement_analysis'] = improvement_analysis

        # Step 4: Component analysis
        component_analysis = await self._analyze_strategy_components(symbols, start_date, end_date, output_dir)
        results['component_analysis'] = component_analysis

        # Save results
        self._save_optimization_results(results, output_dir)

        # Generate report
        self._generate_optimization_report(results, output_dir)

        logger.info("✅ Smart strategy optimization completed!")
        return results

    async def _run_baseline_test(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run baseline test with default parameters."""
        try:
            backtester = Backtester(
                config=self.config,
                output_dir=os.path.join(output_dir, "baseline")
            )

            await backtester.initialize()
            await backtester.load_data(symbols, start_date, end_date)

            # Use default smart strategy
            async def signal_generator(timestamp, symbols_list):
                return await smart_model_integrated_strategy(timestamp, symbols_list)

            success = await backtester.run_backtest(signal_generator=signal_generator)

            if success:
                return {
                    'metrics': backtester.metrics,
                    'trades': backtester.trades,
                    'signals': backtester.signals,
                    'equity_curve': backtester.equity_curve
                }
            else:
                logger.error("Baseline test failed")
                return None

        except Exception as e:
            logger.error(f"Error in baseline test: {e}")
            return None

    async def _optimize_component_weights(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Optimize the component weights of the smart strategy."""
        logger.info("🔧 Optimizing component weights...")

        # Define weight ranges for each component
        weight_ranges = {
            'technical_weight': [0.1, 0.2, 0.3, 0.4, 0.5],
            'vwap_weight': [0.1, 0.15, 0.2, 0.25, 0.3],
            'rsi_weight': [0.05, 0.1, 0.15, 0.2, 0.25],
            'volatility_weight': [0.05, 0.08, 0.1, 0.12, 0.15],
            'funding_weight': [0.05, 0.08, 0.1, 0.12, 0.15],
            'oi_weight': [0.05, 0.08, 0.1, 0.12, 0.15],
            'ensemble_weight': [0.02, 0.03, 0.05, 0.07, 0.1]
        }

        return await self._run_parameter_optimization(
            symbols, start_date, end_date, weight_ranges,
            os.path.join(output_dir, "weight_optimization")
        )

    async def _optimize_thresholds(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Optimize the buy/sell thresholds."""
        logger.info("🎯 Optimizing signal thresholds...")

        # Define threshold ranges
        threshold_ranges = {
            'base_buy_threshold': [0.1, 0.2, 0.3, 0.4, 0.5],
            'base_sell_threshold': [-0.5, -0.4, -0.3, -0.2, -0.1],
            'confidence_multiplier': [0.8, 0.9, 1.0, 1.1, 1.2]
        }

        return await self._run_parameter_optimization(
            symbols, start_date, end_date, threshold_ranges,
            os.path.join(output_dir, "threshold_optimization")
        )

    async def _comprehensive_optimization(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run comprehensive optimization of both weights and thresholds."""
        logger.info("🚀 Running comprehensive optimization...")

        # Combined parameter ranges (smaller ranges for faster optimization)
        param_ranges = {
            'technical_weight': [0.2, 0.3, 0.4],
            'vwap_weight': [0.15, 0.2, 0.25],
            'rsi_weight': [0.1, 0.15, 0.2],
            'volatility_weight': [0.08, 0.1, 0.12],
            'funding_weight': [0.08, 0.1, 0.12],
            'oi_weight': [0.08, 0.1, 0.12],
            'ensemble_weight': [0.03, 0.05, 0.07],
            'base_buy_threshold': [0.2, 0.3, 0.4],
            'base_sell_threshold': [-0.4, -0.3, -0.2]
        }

        return await self._run_parameter_optimization(
            symbols, start_date, end_date, param_ranges,
            os.path.join(output_dir, "comprehensive_optimization")
        )

    async def _run_parameter_optimization(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        param_ranges: Dict[str, List],
        output_dir: str
    ) -> Optional[Dict[str, Any]]:
        """Run parameter optimization with given ranges."""
        try:
            # Create modified strategy function that accepts parameters
            async def parameterized_smart_strategy(timestamp, symbols_list, **params):
                # This would need to be implemented to pass parameters to the strategy
                # For now, we'll use the default strategy
                return await smart_model_integrated_strategy(timestamp, symbols_list)

            # Create backtester
            backtester = Backtester(config=self.config, output_dir=output_dir)
            await backtester.initialize()
            await backtester.load_data(symbols, start_date, end_date)

            # Create optimizer
            optimizer = StrategyOptimizer(
                backtester=backtester,
                strategy_func=parameterized_smart_strategy,
                param_ranges=param_ranges,
                optimization_target='sharpe_ratio',
                output_dir=output_dir
            )

            # Run optimization
            total_combinations = 1
            for param_values in param_ranges.values():
                total_combinations *= len(param_values)

            if total_combinations <= 200:  # Use grid search for smaller spaces
                results = await optimizer.grid_search(verbose=True)
            else:  # Use genetic algorithm for larger spaces
                results = await optimizer.genetic_algorithm(
                    population_size=30,
                    generations=15,
                    verbose=True
                )

            return results

        except Exception as e:
            logger.error(f"Error in parameter optimization: {e}")
            return None

    async def _analyze_strategy_components(
        self,
        symbols: List[str],
        start_date: str,
        end_date: str,
        output_dir: str
    ) -> Dict[str, Any]:
        """Analyze individual strategy components."""
        logger.info("🔍 Analyzing strategy components...")

        analysis = {
            'component_availability': {},
            'signal_strength': {},
            'component_correlation': {}
        }

        # Test each component individually
        components = {
            'technical': 0.3,
            'vwap': 0.2,
            'rsi_model': 0.15,
            'volatility': 0.1,
            'funding': 0.1,
            'open_interest': 0.1,
            'ensemble': 0.05
        }

        for component, default_weight in components.items():
            try:
                # Test component availability by running a short backtest
                # This is a simplified analysis - in practice you'd want to check
                # the actual component functions
                analysis['component_availability'][component] = {
                    'default_weight': default_weight,
                    'status': 'available'  # Simplified for now
                }

            except Exception as e:
                analysis['component_availability'][component] = {
                    'default_weight': default_weight,
                    'status': 'error',
                    'error': str(e)
                }

        return analysis

    def _analyze_improvements(
        self,
        baseline_results: Dict[str, Any],
        optimized_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze improvements from optimization."""
        baseline_metrics = baseline_results.get('metrics', {})
        optimized_metrics = optimized_results.get('best_metrics', {})

        # Extract metrics (handle both dict and object formats)
        def get_metric(metrics, metric_name):
            if isinstance(metrics, dict):
                return metrics.get(metric_name, 0)
            else:
                return getattr(metrics, metric_name, 0)

        baseline_return = get_metric(baseline_metrics, 'total_return')
        optimized_return = get_metric(optimized_metrics, 'total_return')

        baseline_sharpe = get_metric(baseline_metrics, 'sharpe_ratio')
        optimized_sharpe = get_metric(optimized_metrics, 'sharpe_ratio')

        baseline_drawdown = get_metric(baseline_metrics, 'max_drawdown')
        optimized_drawdown = get_metric(optimized_metrics, 'max_drawdown')

        baseline_win_rate = get_metric(baseline_metrics, 'win_rate')
        optimized_win_rate = get_metric(optimized_metrics, 'win_rate')

        return {
            'return_improvement': {
                'baseline': baseline_return,
                'optimized': optimized_return,
                'improvement': optimized_return - baseline_return,
                'improvement_pct': ((optimized_return - baseline_return) / abs(baseline_return) * 100) if baseline_return != 0 else 0
            },
            'sharpe_improvement': {
                'baseline': baseline_sharpe,
                'optimized': optimized_sharpe,
                'improvement': optimized_sharpe - baseline_sharpe,
                'improvement_pct': ((optimized_sharpe - baseline_sharpe) / abs(baseline_sharpe) * 100) if baseline_sharpe != 0 else 0
            },
            'drawdown_improvement': {
                'baseline': baseline_drawdown,
                'optimized': optimized_drawdown,
                'improvement': baseline_drawdown - optimized_drawdown,  # Positive is better (less drawdown)
                'improvement_pct': ((baseline_drawdown - optimized_drawdown) / abs(baseline_drawdown) * 100) if baseline_drawdown != 0 else 0
            },
            'win_rate_improvement': {
                'baseline': baseline_win_rate,
                'optimized': optimized_win_rate,
                'improvement': optimized_win_rate - baseline_win_rate,
                'improvement_pct': ((optimized_win_rate - baseline_win_rate) / baseline_win_rate * 100) if baseline_win_rate != 0 else 0
            }
        }

    def _save_optimization_results(self, results: Dict[str, Any], output_dir: str):
        """Save optimization results to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save main results
        results_file = os.path.join(output_dir, f"smart_strategy_optimization_{timestamp}.json")

        # Convert to serializable format
        serializable_results = self._make_serializable(results)

        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)

        # Save best parameters separately
        if 'best_parameters' in results and results['best_parameters']:
            params_file = os.path.join(output_dir, f"best_smart_strategy_params_{timestamp}.json")
            with open(params_file, 'w') as f:
                json.dump(results['best_parameters'], f, indent=2)

        logger.info(f"Optimization results saved to {output_dir}")

    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    def _generate_optimization_report(self, results: Dict[str, Any], output_dir: str):
        """Generate a comprehensive optimization report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(output_dir, f"smart_strategy_optimization_report_{timestamp}.txt")

        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("SMART STRATEGY OPTIMIZATION REPORT\n")
            f.write("=" * 80 + "\n\n")

            # Basic info
            f.write(f"Optimization Type: {results.get('optimization_type', 'Unknown')}\n")
            f.write(f"Symbols: {', '.join(results.get('symbols', []))}\n")
            f.write(f"Period: {results.get('period', 'Unknown')}\n")
            f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Performance comparison
            if 'baseline_performance' in results and 'optimized_performance' in results:
                f.write("📊 PERFORMANCE COMPARISON\n")
                f.write("-" * 50 + "\n")

                baseline_metrics = results['baseline_performance'].get('metrics', {})
                optimized_metrics = results['optimized_performance'].get('best_metrics', {})

                # Helper function to get metrics
                def get_metric(metrics, metric_name):
                    if isinstance(metrics, dict):
                        return metrics.get(metric_name, 0)
                    else:
                        return getattr(metrics, metric_name, 0)

                metrics_to_compare = [
                    ('Total Return', 'total_return', '{:.2%}'),
                    ('Sharpe Ratio', 'sharpe_ratio', '{:.3f}'),
                    ('Max Drawdown', 'max_drawdown', '{:.2%}'),
                    ('Win Rate', 'win_rate', '{:.2%}'),
                    ('Total Trades', 'total_trades', '{:.0f}')
                ]

                f.write(f"{'Metric':<15} {'Baseline':<12} {'Optimized':<12} {'Improvement':<12}\n")
                f.write("-" * 55 + "\n")

                for metric_name, metric_key, format_str in metrics_to_compare:
                    baseline_val = get_metric(baseline_metrics, metric_key)
                    optimized_val = get_metric(optimized_metrics, metric_key)

                    if baseline_val != 0:
                        improvement = ((optimized_val - baseline_val) / abs(baseline_val)) * 100
                        improvement_str = f"{improvement:+.1f}%"
                    else:
                        improvement_str = "N/A"

                    f.write(f"{metric_name:<15} {format_str.format(baseline_val):<12} {format_str.format(optimized_val):<12} {improvement_str:<12}\n")

                f.write("\n")

            # Best parameters
            if 'best_parameters' in results and results['best_parameters']:
                f.write("🎯 OPTIMIZED PARAMETERS\n")
                f.write("-" * 50 + "\n")

                best_params = results['best_parameters']
                for param_name, param_value in best_params.items():
                    if isinstance(param_value, float):
                        f.write(f"{param_name}: {param_value:.3f}\n")
                    else:
                        f.write(f"{param_name}: {param_value}\n")

                f.write("\n")

            # Recommendations
            f.write("💡 RECOMMENDATIONS\n")
            f.write("-" * 50 + "\n")

            if 'improvement_analysis' in results:
                improvement = results['improvement_analysis']

                # Check for significant improvements
                return_improvement = improvement.get('return_improvement', {}).get('improvement_pct', 0)
                sharpe_improvement = improvement.get('sharpe_improvement', {}).get('improvement_pct', 0)

                if return_improvement > 10:
                    f.write("🎉 Significant return improvement found! Use optimized parameters.\n")
                elif return_improvement > 5:
                    f.write("✅ Moderate return improvement. Consider using optimized parameters.\n")
                else:
                    f.write("⚠️ Limited return improvement. May need different optimization approach.\n")

                if sharpe_improvement > 20:
                    f.write("🎉 Excellent risk-adjusted return improvement!\n")
                elif sharpe_improvement > 10:
                    f.write("✅ Good risk-adjusted return improvement.\n")
                else:
                    f.write("⚠️ Limited Sharpe ratio improvement. Consider risk management changes.\n")

            f.write("\nNext Steps:\n")
            f.write("1. Test optimized parameters in paper trading\n")
            f.write("2. Monitor performance over different market conditions\n")
            f.write("3. Consider walk-forward analysis for robustness\n")
            f.write("4. Implement dynamic parameter adjustment\n")

            f.write("\n" + "=" * 80 + "\n")


async def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Smart Strategy Optimizer")
    parser.add_argument('--symbols', nargs='+', default=['BTC-USDT'], help='Symbols to optimize')
    parser.add_argument('--start-date', default='2023-01-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2023-12-31', help='End date (YYYY-MM-DD)')
    parser.add_argument('--optimization-type', choices=['weights', 'thresholds', 'comprehensive'],
                       default='comprehensive', help='Type of optimization')
    parser.add_argument('--output-dir', default='results/smart_strategy_optimization', help='Output directory')
    parser.add_argument('--config', default='config.yaml', help='Config file path')

    args = parser.parse_args()

    print("🎯 Smart Strategy Optimizer")
    print("=" * 50)
    print(f"Optimization Type: {args.optimization_type}")
    print(f"Symbols: {', '.join(args.symbols)}")
    print(f"Period: {args.start_date} to {args.end_date}")
    print()

    # Create optimizer
    optimizer = SmartStrategyOptimizer(config_path=args.config)

    # Run optimization
    results = await optimizer.optimize_smart_strategy(
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date,
        optimization_type=args.optimization_type,
        output_dir=args.output_dir
    )

    print("\n🎉 Smart Strategy Optimization Completed!")
    print(f"📁 Results saved to: {args.output_dir}")

    # Print key results
    if 'improvement_analysis' in results:
        improvement = results['improvement_analysis']
        return_improvement = improvement.get('return_improvement', {}).get('improvement_pct', 0)
        sharpe_improvement = improvement.get('sharpe_improvement', {}).get('improvement_pct', 0)

        print(f"\n📊 Key Improvements:")
        print(f"  Return: {return_improvement:+.1f}%")
        print(f"  Sharpe Ratio: {sharpe_improvement:+.1f}%")

    if 'best_parameters' in results and results['best_parameters']:
        print(f"\n🎯 Found {len(results['best_parameters'])} optimized parameters")
        print("Check the report for detailed parameter values.")


if __name__ == "__main__":
    asyncio.run(main())
