"""
Simple test script for the SQLiteBus.
"""

import sys
import time
import logging
from datetime import datetime

# Add current directory to path
sys.path.append('.')

# Import SQLiteBus
from pipeline.databus import SQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function."""
    logger.info("Starting SQLiteBus test...")
    
    # Create bus instance
    bus = SQLiteBus(path="data/test_bus.db", poll_interval=0.5)
    logger.info("SQLiteBus initialized")
    
    # Define callback function
    def on_message(ts, payload):
        logger.info(f"Received message at {datetime.fromtimestamp(ts)}: {payload}")
    
    # Subscribe to a stream
    bus.subscribe("test_stream", on_message)
    logger.info("Subscribed to test_stream")
    
    # Publish some messages
    for i in range(5):
        ts = time.time()
        payload = {
            "index": i,
            "message": f"Test message {i}",
            "timestamp": datetime.now().isoformat()
        }
        bus.publish("test_stream", ts, payload)
        logger.info(f"Published message {i}")
        time.sleep(1)
    
    # Wait for messages to be processed
    logger.info("Waiting for messages to be processed...")
    time.sleep(2)
    
    # Get message count
    count = bus.get_message_count()
    logger.info(f"Total messages in database: {count}")
    
    # Clean up
    bus.close()
    logger.info("SQLiteBus closed")


if __name__ == "__main__":
    main()
