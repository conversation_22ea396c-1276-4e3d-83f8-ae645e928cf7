#!/usr/bin/env python3
"""
Trading Analytics and Performance Tracking System
Comprehensive analytics for Money Circle trading performance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import numpy as np
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class TradeRecord:
    """Individual trade record."""
    trade_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    entry_price: float
    exit_price: Optional[float]
    quantity: float
    leverage: float
    entry_time: datetime
    exit_time: Optional[datetime]
    pnl: float
    pnl_pct: float
    fees: float
    strategy: Optional[str] = None
    notes: Optional[str] = None

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    # Basic metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # P&L metrics
    total_pnl: float
    gross_profit: float
    gross_loss: float
    profit_factor: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_pct: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trade metrics
    avg_win: float
    avg_loss: float
    avg_trade: float
    largest_win: float
    largest_loss: float
    
    # Time metrics
    avg_trade_duration: timedelta
    max_trade_duration: timedelta
    min_trade_duration: timedelta
    
    # Advanced metrics
    var_95: float  # Value at Risk
    expected_shortfall: float
    kelly_criterion: float
    recovery_factor: float

@dataclass
class StrategyPerformance:
    """Strategy-specific performance metrics."""
    strategy_name: str
    metrics: PerformanceMetrics
    trades: List[TradeRecord]
    active_since: datetime
    last_trade: Optional[datetime]
    
@dataclass
class MarketAnalysis:
    """Market analysis and regime detection."""
    symbol: str
    current_regime: str  # 'trending', 'ranging', 'volatile'
    volatility_percentile: float
    trend_strength: float
    support_levels: List[float]
    resistance_levels: List[float]
    correlation_matrix: Dict[str, float]

class TradingAnalytics:
    """Comprehensive trading analytics and performance tracking system."""
    
    def __init__(self, db_path: str = "data/trading_analytics.db"):
        """Initialize trading analytics system."""
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Data storage
        self.trades: List[TradeRecord] = []
        self.performance_cache: Dict[str, PerformanceMetrics] = {}
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.market_analysis: Dict[str, MarketAnalysis] = {}
        
        # Configuration
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        self.trading_days_per_year = 365
        
        logger.info("📊 Trading Analytics system initialized")
    
    def _init_database(self):
        """Initialize SQLite database for trade storage."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trades (
                        trade_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL,
                        quantity REAL NOT NULL,
                        leverage REAL NOT NULL,
                        entry_time TEXT NOT NULL,
                        exit_time TEXT,
                        pnl REAL NOT NULL,
                        pnl_pct REAL NOT NULL,
                        fees REAL NOT NULL,
                        strategy TEXT,
                        notes TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS performance_snapshots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        strategy TEXT,
                        total_trades INTEGER,
                        win_rate REAL,
                        total_pnl REAL,
                        max_drawdown REAL,
                        sharpe_ratio REAL,
                        profit_factor REAL,
                        data TEXT  -- JSON data
                    )
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol);
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_trades_strategy ON trades(strategy);
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time);
                """)
                
                conn.commit()
                logger.info("✅ Trading analytics database initialized")
                
        except Exception as e:
            logger.error(f"❌ Error initializing database: {e}")
    
    async def record_trade(self, trade: TradeRecord):
        """Record a new trade in the analytics system."""
        try:
            # Add to memory
            self.trades.append(trade)
            
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO trades 
                    (trade_id, symbol, side, entry_price, exit_price, quantity, leverage,
                     entry_time, exit_time, pnl, pnl_pct, fees, strategy, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade.trade_id, trade.symbol, trade.side, trade.entry_price,
                    trade.exit_price, trade.quantity, trade.leverage,
                    trade.entry_time.isoformat(), 
                    trade.exit_time.isoformat() if trade.exit_time else None,
                    trade.pnl, trade.pnl_pct, trade.fees, trade.strategy, trade.notes
                ))
                conn.commit()
            
            # Update performance cache
            await self._update_performance_cache()
            
            logger.info(f"📝 Trade recorded: {trade.symbol} {trade.side} P&L: ${trade.pnl:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Error recording trade: {e}")
    
    async def load_trades_from_db(self, days_back: int = 30) -> List[TradeRecord]:
        """Load trades from database."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM trades 
                    WHERE entry_time >= ? 
                    ORDER BY entry_time DESC
                """, (cutoff_date.isoformat(),))
                
                trades = []
                for row in cursor.fetchall():
                    trade = TradeRecord(
                        trade_id=row[0],
                        symbol=row[1],
                        side=row[2],
                        entry_price=row[3],
                        exit_price=row[4],
                        quantity=row[5],
                        leverage=row[6],
                        entry_time=datetime.fromisoformat(row[7]),
                        exit_time=datetime.fromisoformat(row[8]) if row[8] else None,
                        pnl=row[9],
                        pnl_pct=row[10],
                        fees=row[11],
                        strategy=row[12],
                        notes=row[13]
                    )
                    trades.append(trade)
                
                self.trades = trades
                logger.info(f"📚 Loaded {len(trades)} trades from database")
                return trades
                
        except Exception as e:
            logger.error(f"❌ Error loading trades: {e}")
            return []
    
    async def calculate_performance_metrics(self, trades: List[TradeRecord] = None,
                                          strategy: str = None) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics."""
        try:
            # Use provided trades or filter by strategy
            if trades is None:
                if strategy:
                    trades = [t for t in self.trades if t.strategy == strategy]
                else:
                    trades = self.trades
            
            if not trades:
                return self._empty_performance_metrics()
            
            # Completed trades only
            completed_trades = [t for t in trades if t.exit_time is not None]
            
            if not completed_trades:
                return self._empty_performance_metrics()
            
            # Basic metrics
            total_trades = len(completed_trades)
            winning_trades = len([t for t in completed_trades if t.pnl > 0])
            losing_trades = len([t for t in completed_trades if t.pnl < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # P&L metrics
            total_pnl = sum(t.pnl for t in completed_trades)
            gross_profit = sum(t.pnl for t in completed_trades if t.pnl > 0)
            gross_loss = abs(sum(t.pnl for t in completed_trades if t.pnl < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Trade metrics
            wins = [t.pnl for t in completed_trades if t.pnl > 0]
            losses = [t.pnl for t in completed_trades if t.pnl < 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            avg_trade = total_pnl / total_trades if total_trades > 0 else 0
            largest_win = max(wins) if wins else 0
            largest_loss = min(losses) if losses else 0
            
            # Time metrics
            durations = []
            for trade in completed_trades:
                if trade.exit_time and trade.entry_time:
                    duration = trade.exit_time - trade.entry_time
                    durations.append(duration)
            
            avg_trade_duration = np.mean(durations) if durations else timedelta(0)
            max_trade_duration = max(durations) if durations else timedelta(0)
            min_trade_duration = min(durations) if durations else timedelta(0)
            
            # Risk metrics
            returns = [t.pnl for t in completed_trades]
            max_drawdown, max_drawdown_pct = self._calculate_drawdown(completed_trades)
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            sortino_ratio = self._calculate_sortino_ratio(returns)
            calmar_ratio = (total_pnl / abs(max_drawdown)) if max_drawdown != 0 else 0
            
            # Advanced metrics
            var_95 = np.percentile(returns, 5) if returns else 0
            tail_returns = [r for r in returns if r <= var_95]
            expected_shortfall = np.mean(tail_returns) if tail_returns else 0
            kelly_criterion = self._calculate_kelly_criterion(wins, losses)
            recovery_factor = total_pnl / abs(max_drawdown) if max_drawdown != 0 else 0
            
            return PerformanceMetrics(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                gross_profit=gross_profit,
                gross_loss=gross_loss,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                max_drawdown_pct=max_drawdown_pct,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                avg_win=avg_win,
                avg_loss=avg_loss,
                avg_trade=avg_trade,
                largest_win=largest_win,
                largest_loss=largest_loss,
                avg_trade_duration=avg_trade_duration,
                max_trade_duration=max_trade_duration,
                min_trade_duration=min_trade_duration,
                var_95=var_95,
                expected_shortfall=expected_shortfall,
                kelly_criterion=kelly_criterion,
                recovery_factor=recovery_factor
            )
            
        except Exception as e:
            logger.error(f"❌ Error calculating performance metrics: {e}")
            return self._empty_performance_metrics()
    
    def _empty_performance_metrics(self) -> PerformanceMetrics:
        """Return empty performance metrics."""
        return PerformanceMetrics(
            total_trades=0, winning_trades=0, losing_trades=0, win_rate=0.0,
            total_pnl=0.0, gross_profit=0.0, gross_loss=0.0, profit_factor=0.0,
            max_drawdown=0.0, max_drawdown_pct=0.0, sharpe_ratio=0.0,
            sortino_ratio=0.0, calmar_ratio=0.0, avg_win=0.0, avg_loss=0.0,
            avg_trade=0.0, largest_win=0.0, largest_loss=0.0,
            avg_trade_duration=timedelta(0), max_trade_duration=timedelta(0),
            min_trade_duration=timedelta(0), var_95=0.0, expected_shortfall=0.0,
            kelly_criterion=0.0, recovery_factor=0.0
        )
    
    def _calculate_drawdown(self, trades: List[TradeRecord]) -> Tuple[float, float]:
        """Calculate maximum drawdown."""
        if not trades:
            return 0.0, 0.0
        
        # Calculate cumulative P&L
        cumulative_pnl = []
        running_total = 0
        for trade in sorted(trades, key=lambda t: t.entry_time):
            running_total += trade.pnl
            cumulative_pnl.append(running_total)
        
        # Calculate drawdown
        peak = cumulative_pnl[0]
        max_dd = 0
        max_dd_pct = 0
        
        for pnl in cumulative_pnl:
            if pnl > peak:
                peak = pnl
            
            drawdown = peak - pnl
            if drawdown > max_dd:
                max_dd = drawdown
                max_dd_pct = (drawdown / peak * 100) if peak > 0 else 0
        
        return max_dd, max_dd_pct
    
    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calculate Sharpe ratio."""
        if len(returns) < 2:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns, ddof=1)
        
        if std_return == 0:
            return 0.0
        
        # Annualize
        daily_risk_free = self.risk_free_rate / self.trading_days_per_year
        excess_return = mean_return - daily_risk_free
        
        return (excess_return / std_return) * np.sqrt(self.trading_days_per_year)
    
    def _calculate_sortino_ratio(self, returns: List[float]) -> float:
        """Calculate Sortino ratio."""
        if len(returns) < 2:
            return 0.0
        
        mean_return = np.mean(returns)
        negative_returns = [r for r in returns if r < 0]
        
        if not negative_returns:
            return float('inf')
        
        downside_std = np.std(negative_returns, ddof=1)
        
        if downside_std == 0:
            return 0.0
        
        daily_risk_free = self.risk_free_rate / self.trading_days_per_year
        excess_return = mean_return - daily_risk_free
        
        return (excess_return / downside_std) * np.sqrt(self.trading_days_per_year)
    
    def _calculate_kelly_criterion(self, wins: List[float], losses: List[float]) -> float:
        """Calculate Kelly criterion for optimal position sizing."""
        if not wins or not losses:
            return 0.0
        
        win_rate = len(wins) / (len(wins) + len(losses))
        avg_win = np.mean(wins)
        avg_loss = abs(np.mean(losses))
        
        if avg_loss == 0:
            return 0.0
        
        win_loss_ratio = avg_win / avg_loss
        kelly = win_rate - ((1 - win_rate) / win_loss_ratio)
        
        return max(0, min(kelly, 0.25))  # Cap at 25% for safety
    
    async def _update_performance_cache(self):
        """Update performance metrics cache."""
        try:
            # Overall performance
            self.performance_cache['overall'] = await self.calculate_performance_metrics()
            
            # Strategy-specific performance
            strategies = set(t.strategy for t in self.trades if t.strategy)
            for strategy in strategies:
                self.performance_cache[strategy] = await self.calculate_performance_metrics(strategy=strategy)
            
        except Exception as e:
            logger.error(f"❌ Error updating performance cache: {e}")
    
    async def get_performance_report(self, strategy: str = None, days_back: int = 30) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        try:
            # Load recent trades
            await self.load_trades_from_db(days_back)
            
            # Calculate metrics
            metrics = await self.calculate_performance_metrics(strategy=strategy)
            
            # Get strategy breakdown
            strategy_breakdown = {}
            strategies = set(t.strategy for t in self.trades if t.strategy)
            for strat in strategies:
                strategy_breakdown[strat] = await self.calculate_performance_metrics(strategy=strat)
            
            # Recent trades
            recent_trades = sorted(self.trades, key=lambda t: t.entry_time, reverse=True)[:20]
            
            return {
                'period': f"Last {days_back} days",
                'generated_at': datetime.now().isoformat(),
                'overall_metrics': metrics.__dict__,
                'strategy_breakdown': {k: v.__dict__ for k, v in strategy_breakdown.items()},
                'recent_trades': [t.__dict__ for t in recent_trades],
                'summary': {
                    'total_trades': metrics.total_trades,
                    'win_rate': f"{metrics.win_rate:.1%}",
                    'total_pnl': f"${metrics.total_pnl:.2f}",
                    'profit_factor': f"{metrics.profit_factor:.2f}",
                    'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
                    'max_drawdown': f"${metrics.max_drawdown:.2f} ({metrics.max_drawdown_pct:.1f}%)"
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating performance report: {e}")
            return {'error': str(e)}
