/**
 * Money Circle Club Analytics JavaScript
 * Handles interactive charts, data visualization, and analytics features
 */

class ClubAnalytics {
    constructor() {
        this.data = {};
        this.charts = {};
        this.currentTimeRange = '30d';
        this.currentTab = 'overview';
        
        this.init();
    }
    
    init() {
        this.loadData();
        this.bindEvents();
        this.initializeCharts();
    }
    
    loadData() {
        if (window.analyticsData) {
            this.data = window.analyticsData;
        }
    }
    
    bindEvents() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // Time range selector
        const timeRangeSelect = document.getElementById('timeRangeSelect');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', (e) => {
                this.currentTimeRange = e.target.value;
                this.updateAnalytics();
            });
        }
        
        // Chart controls
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const container = e.target.closest('.chart-container');
                const metric = e.target.dataset.metric;
                if (container && metric) {
                    this.updateChart(container, metric);
                }
            });
        });
    }
    
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.analytics-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
        
        this.currentTab = tabName;
        
        // Initialize charts for the new tab
        setTimeout(() => {
            this.initializeTabCharts(tabName);
        }, 100);
    }
    
    initializeCharts() {
        this.initializeTabCharts('overview');
    }
    
    initializeTabCharts(tabName) {
        switch (tabName) {
            case 'overview':
                this.initializePerformanceChart();
                break;
            case 'performance':
                this.initializeReturnsDistributionChart();
                this.initializeWinRateChart();
                break;
            case 'portfolio':
                this.initializeAllocationChart();
                break;
            case 'strategies':
                this.initializeStrategyComparisonChart();
                break;
            case 'members':
                this.initializeMemberGrowthChart();
                this.initializeMemberDistributionChart();
                break;
            case 'risk':
                this.initializeRiskReturnChart();
                this.initializeDrawdownChart();
                break;
        }
    }
    
    initializePerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx || this.charts.performance) return;
        
        // Mock performance data
        const performanceData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Club Performance',
                data: [5.2, 8.1, 12.3, 15.7, 18.9, 22.4],
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };
        
        this.charts.performance = new Chart(ctx, {
            type: 'line',
            data: performanceData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeReturnsDistributionChart() {
        const ctx = document.getElementById('returnsDistributionChart');
        if (!ctx || this.charts.returnsDistribution) return;
        
        const distributionData = {
            labels: ['<-10%', '-10% to -5%', '-5% to 0%', '0% to 5%', '5% to 10%', '10% to 15%', '>15%'],
            datasets: [{
                label: 'Number of Members',
                data: [2, 5, 8, 15, 20, 12, 8],
                backgroundColor: [
                    '#ef4444',
                    '#f97316',
                    '#eab308',
                    '#22c55e',
                    '#10b981',
                    '#06b6d4',
                    '#8b5cf6'
                ]
            }]
        };
        
        this.charts.returnsDistribution = new Chart(ctx, {
            type: 'bar',
            data: distributionData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeWinRateChart() {
        const ctx = document.getElementById('winRateChart');
        if (!ctx || this.charts.winRate) return;
        
        const winRateData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Win Rate',
                data: [65, 68, 72, 70, 75, 78],
                borderColor: '#22c55e',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };
        
        this.charts.winRate = new Chart(ctx, {
            type: 'line',
            data: winRateData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeAllocationChart() {
        const ctx = document.getElementById('allocationChart');
        if (!ctx || this.charts.allocation) return;
        
        const allocationData = {
            labels: ['BTC', 'ETH', 'BNB', 'ADA', 'SOL'],
            datasets: [{
                data: [35.5, 25.2, 15.8, 12.1, 11.4],
                backgroundColor: [
                    '#f7931a',
                    '#627eea',
                    '#f3ba2f',
                    '#0033ad',
                    '#9945ff'
                ]
            }]
        };
        
        this.charts.allocation = new Chart(ctx, {
            type: 'doughnut',
            data: allocationData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#e2e8f0',
                            padding: 20
                        }
                    }
                }
            }
        });
    }
    
    initializeStrategyComparisonChart() {
        const ctx = document.getElementById('strategyComparisonChart');
        if (!ctx || this.charts.strategyComparison) return;
        
        const strategies = this.data.strategyAnalytics || [];
        const labels = strategies.slice(0, 5).map(s => s.title);
        const returns = strategies.slice(0, 5).map(s => s.avg_return);
        
        const comparisonData = {
            labels: labels,
            datasets: [{
                label: 'Average Return (%)',
                data: returns,
                backgroundColor: 'rgba(139, 92, 246, 0.8)',
                borderColor: '#8b5cf6',
                borderWidth: 1
            }]
        };
        
        this.charts.strategyComparison = new Chart(ctx, {
            type: 'bar',
            data: comparisonData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeMemberGrowthChart() {
        const ctx = document.getElementById('memberGrowthChart');
        if (!ctx || this.charts.memberGrowth) return;
        
        const growthData = this.data.growthTracking?.member_growth || [];
        const labels = growthData.map(d => new Date(d.date).toLocaleDateString());
        const members = growthData.map(d => d.members);
        
        const memberGrowthData = {
            labels: labels,
            datasets: [{
                label: 'Total Members',
                data: members,
                borderColor: '#22c55e',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };
        
        this.charts.memberGrowth = new Chart(ctx, {
            type: 'line',
            data: memberGrowthData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeMemberDistributionChart() {
        const ctx = document.getElementById('memberDistributionChart');
        if (!ctx || this.charts.memberDistribution) return;
        
        const distributionData = {
            labels: ['Top 10%', 'Top 25%', 'Above Average', 'Average', 'Below Average'],
            datasets: [{
                data: [8, 12, 20, 25, 10],
                backgroundColor: [
                    '#8b5cf6',
                    '#22c55e',
                    '#3b82f6',
                    '#f59e0b',
                    '#ef4444'
                ]
            }]
        };
        
        this.charts.memberDistribution = new Chart(ctx, {
            type: 'pie',
            data: distributionData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#e2e8f0',
                            padding: 20
                        }
                    }
                }
            }
        });
    }
    
    initializeRiskReturnChart() {
        const ctx = document.getElementById('riskReturnChart');
        if (!ctx || this.charts.riskReturn) return;
        
        const members = this.data.memberAnalytics || [];
        const riskReturnData = {
            datasets: [{
                label: 'Members',
                data: members.map(m => ({
                    x: Math.random() * 20 + 5, // Mock volatility
                    y: m.avg_return
                })),
                backgroundColor: 'rgba(139, 92, 246, 0.6)',
                borderColor: '#8b5cf6'
            }]
        };
        
        this.charts.riskReturn = new Chart(ctx, {
            type: 'scatter',
            data: riskReturnData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Risk (Volatility %)',
                            color: '#94a3b8'
                        },
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Return (%)',
                            color: '#94a3b8'
                        },
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    initializeDrawdownChart() {
        const ctx = document.getElementById('drawdownChart');
        if (!ctx || this.charts.drawdown) return;
        
        const drawdownData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Max Drawdown',
                data: [-5.2, -3.1, -7.3, -2.7, -4.9, -1.4],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };
        
        this.charts.drawdown = new Chart(ctx, {
            type: 'line',
            data: drawdownData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#94a3b8',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    updateChart(container, metric) {
        // Update chart controls
        container.querySelectorAll('.chart-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        container.querySelector(`[data-metric="${metric}"]`).classList.add('active');
        
        // Update chart data based on metric
        // This would fetch new data and update the chart
        console.log(`Updating chart with metric: ${metric}`);
    }
    
    updateAnalytics() {
        // This would fetch new data based on the time range
        console.log(`Updating analytics for time range: ${this.currentTimeRange}`);
        
        // Destroy existing charts and reinitialize
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
        
        // Reinitialize charts for current tab
        setTimeout(() => {
            this.initializeTabCharts(this.currentTab);
        }, 100);
    }
    
    exportReport(format) {
        console.log(`Exporting report in ${format} format`);
        // This would generate and download the report
        this.showNotification(`Report export in ${format.toUpperCase()} format started`, 'info');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global functions for onclick handlers
function updateAnalytics() {
    if (window.clubAnalytics) {
        window.clubAnalytics.updateAnalytics();
    }
}

function exportReport(format) {
    if (window.clubAnalytics) {
        window.clubAnalytics.exportReport(format);
    }
}

// Initialize analytics when DOM is loaded
function initializeClubAnalytics() {
    window.clubAnalytics = new ClubAnalytics();
}
