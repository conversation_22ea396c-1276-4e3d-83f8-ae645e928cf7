#!/usr/bin/env python3
"""
HTX Futures Trading Client
Professional CCXT-based integration for HTX USDT-M futures trading
Specialized for DOGE/USDT futures with real-time position monitoring
"""

import ccxt
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import time

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """HTX Futures position data."""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    mark_price: float
    pnl_usd: float
    pnl_percent: float
    margin: float
    leverage: float
    liquidation_price: float
    timestamp: datetime

@dataclass
class AccountBalance:
    """HTX Futures account balance."""
    total_balance: float
    available_balance: float
    margin_balance: float
    unrealized_pnl: float
    margin_ratio: float
    timestamp: datetime

@dataclass
class MarketData:
    """Real-time market data for DOGE/USDT."""
    symbol: str
    price: float
    bid: float
    ask: float
    volume_24h: float
    change_24h_percent: float
    funding_rate: float
    next_funding_time: datetime
    timestamp: datetime

class HTXFuturesClient:
    """Professional HTX Futures trading client with CCXT integration."""

    def __init__(self, api_key: str = "", secret: str = "", passphrase: str = "", testnet: bool = True):
        """Initialize HTX Futures client."""
        self.api_key = api_key
        self.secret = secret
        self.passphrase = passphrase
        self.testnet = testnet

        # Initialize CCXT exchange
        self.exchange = None
        self.connected = False

        # Data storage
        self.positions: Dict[str, Position] = {}
        self.account_balance: Optional[AccountBalance] = None
        self.market_data: Dict[str, MarketData] = {}

        # Configuration
        self.symbol = 'DOGE/USDT:USDT'  # HTX futures format
        self.update_interval = 1.0  # 1 second updates

        # Control flags
        self.running = False

        logger.info("[HTX] HTX Futures client initialized")

    async def connect(self) -> bool:
        """Connect to HTX Futures API."""
        try:
            # Configure CCXT exchange
            exchange_config = {
                'apiKey': self.api_key,
                'secret': self.secret,
                'password': self.passphrase,
                'sandbox': self.testnet,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',  # Use futures/swap
                }
            }

            self.exchange = ccxt.htx(exchange_config)

            # Test connection
            if self.api_key and self.secret:
                # Test with account balance call
                await self.exchange.fetch_balance()
                logger.info("✅ HTX Futures API connection successful")
            else:
                logger.info("📊 HTX Futures client in read-only mode (no API keys)")

            self.connected = True
            return True

        except Exception as e:
            logger.error(f"❌ HTX Futures connection failed: {e}")
            self.connected = False
            return False

    async def start_monitoring(self):
        """Start real-time position and market data monitoring."""
        if not self.connected:
            await self.connect()

        if not self.connected:
            logger.error("❌ Cannot start monitoring - not connected")
            return

        self.running = True
        logger.info("🚀 Starting HTX Futures monitoring...")

        # Start monitoring tasks
        tasks = [
            self._monitor_positions(),
            self._monitor_account_balance(),
            self._monitor_market_data(),
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _monitor_positions(self):
        """Monitor open positions with real-time PnL updates."""
        while self.running:
            try:
                if not self.api_key:
                    await asyncio.sleep(self.update_interval)
                    continue

                # Fetch positions
                positions = await self.exchange.fetch_positions([self.symbol])

                for pos_data in positions:
                    if pos_data['contracts'] > 0:  # Only active positions
                        position = Position(
                            symbol=pos_data['symbol'],
                            side='long' if pos_data['side'] == 'long' else 'short',
                            size=float(pos_data['contracts']),
                            entry_price=float(pos_data['entryPrice'] or 0),
                            mark_price=float(pos_data['markPrice'] or 0),
                            pnl_usd=float(pos_data['unrealizedPnl'] or 0),
                            pnl_percent=float(pos_data['percentage'] or 0),
                            margin=float(pos_data['initialMargin'] or 0),
                            leverage=float(pos_data['leverage'] or 1),
                            liquidation_price=float(pos_data['liquidationPrice'] or 0),
                            timestamp=datetime.now()
                        )

                        self.positions[pos_data['symbol']] = position

                        logger.debug(f"📊 Position update: {position.symbol} {position.side} "
                                   f"${position.pnl_usd:.2f} ({position.pnl_percent:.2f}%)")

            except Exception as e:
                logger.error(f"❌ Error monitoring positions: {e}")

            await asyncio.sleep(self.update_interval)

    async def _monitor_account_balance(self):
        """Monitor account balance and margin information."""
        while self.running:
            try:
                if not self.api_key:
                    await asyncio.sleep(self.update_interval * 5)  # Less frequent for balance
                    continue

                # Fetch balance
                balance = await self.exchange.fetch_balance()

                if 'USDT' in balance:
                    usdt_balance = balance['USDT']

                    self.account_balance = AccountBalance(
                        total_balance=float(usdt_balance.get('total', 0)),
                        available_balance=float(usdt_balance.get('free', 0)),
                        margin_balance=float(usdt_balance.get('used', 0)),
                        unrealized_pnl=sum(pos.pnl_usd for pos in self.positions.values()),
                        margin_ratio=0.0,  # Calculate if needed
                        timestamp=datetime.now()
                    )

                    logger.debug(f"💰 Balance: ${self.account_balance.total_balance:.2f} "
                               f"(Available: ${self.account_balance.available_balance:.2f})")

            except Exception as e:
                logger.error(f"❌ Error monitoring balance: {e}")

            await asyncio.sleep(self.update_interval * 5)  # Update every 5 seconds

    async def _monitor_market_data(self):
        """Monitor real-time market data for DOGE/USDT."""
        while self.running:
            try:
                # Fetch ticker
                ticker = await self.exchange.fetch_ticker(self.symbol)

                # Fetch funding rate
                funding_rate = 0.0
                next_funding = datetime.now() + timedelta(hours=8)  # Default 8h funding

                try:
                    funding_info = await self.exchange.fetch_funding_rate(self.symbol)
                    funding_rate = float(funding_info.get('fundingRate', 0))
                    if funding_info.get('fundingDatetime'):
                        next_funding = datetime.fromisoformat(funding_info['fundingDatetime'].replace('Z', '+00:00'))
                except:
                    pass  # Funding rate not critical

                self.market_data[self.symbol] = MarketData(
                    symbol=self.symbol,
                    price=float(ticker['last']),
                    bid=float(ticker['bid'] or ticker['last']),
                    ask=float(ticker['ask'] or ticker['last']),
                    volume_24h=float(ticker['quoteVolume'] or 0),
                    change_24h_percent=float(ticker['percentage'] or 0),
                    funding_rate=funding_rate,
                    next_funding_time=next_funding,
                    timestamp=datetime.now()
                )

                logger.debug(f"📈 Market: {self.symbol} ${ticker['last']:.6f} "
                           f"({ticker['percentage']:.2f}%)")

            except Exception as e:
                logger.error(f"❌ Error monitoring market data: {e}")

            await asyncio.sleep(self.update_interval)

    async def place_market_order(self, side: str, amount: float, leverage: int = 10,
                               reduce_only: bool = False) -> Optional[Dict]:
        """Place a market order for DOGE/USDT futures."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot place order - no API keys configured")
                return None

            # Set leverage first
            await self.exchange.set_leverage(leverage, self.symbol)

            # Prepare order parameters
            params = {
                'type': 'swap',
                'reduceOnly': reduce_only
            }

            # Place market order
            order = await self.exchange.create_market_order(
                symbol=self.symbol,
                side=side,  # 'buy' or 'sell'
                amount=amount,
                params=params
            )

            logger.info(f"✅ Market order placed: {side} {amount} {self.symbol} at {leverage}x leverage (reduce_only: {reduce_only})")
            return order

        except Exception as e:
            logger.error(f"❌ Error placing market order: {e}")
            return None

    async def place_limit_order(self, side: str, amount: float, price: float,
                              leverage: int = 10, reduce_only: bool = False) -> Optional[Dict]:
        """Place a limit order for DOGE/USDT futures."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot place order - no API keys configured")
                return None

            # Set leverage first
            await self.exchange.set_leverage(leverage, self.symbol)

            # Prepare order parameters
            params = {
                'type': 'swap',
                'reduceOnly': reduce_only
            }

            # Place limit order
            order = await self.exchange.create_limit_order(
                symbol=self.symbol,
                side=side,
                amount=amount,
                price=price,
                params=params
            )

            logger.info(f"✅ Limit order placed: {side} {amount} {self.symbol} at ${price} with {leverage}x leverage")
            return order

        except Exception as e:
            logger.error(f"❌ Error placing limit order: {e}")
            return None

    async def place_stop_order(self, side: str, amount: float, stop_price: float,
                             leverage: int = 10, reduce_only: bool = True) -> Optional[Dict]:
        """Place a stop-loss order for DOGE/USDT futures."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot place order - no API keys configured")
                return None

            # Set leverage first
            await self.exchange.set_leverage(leverage, self.symbol)

            # Prepare stop order parameters
            params = {
                'type': 'swap',
                'stopPrice': stop_price,
                'reduceOnly': reduce_only
            }

            # Place stop market order
            order = await self.exchange.create_order(
                symbol=self.symbol,
                type='stop_market',
                side=side,
                amount=amount,
                price=None,
                params=params
            )

            logger.info(f"✅ Stop order placed: {side} {amount} {self.symbol} at stop ${stop_price}")
            return order

        except Exception as e:
            logger.error(f"❌ Error placing stop order: {e}")
            return None

    async def close_position(self, position_side: str = None) -> Optional[Dict]:
        """Close DOGE/USDT position (or specific side)."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot close position - no API keys configured")
                return None

            # Get current positions
            positions = await self.exchange.fetch_positions([self.symbol])

            closed_orders = []
            for position in positions:
                if position['contracts'] > 0:  # Has open position
                    # Determine close side (opposite of position)
                    close_side = 'sell' if position['side'] == 'long' else 'buy'

                    # Skip if specific side requested and doesn't match
                    if position_side and position['side'] != position_side:
                        continue

                    # Close with market order
                    close_order = await self.place_market_order(
                        side=close_side,
                        amount=position['contracts'],
                        leverage=int(position.get('leverage', 10)),
                        reduce_only=True
                    )

                    if close_order:
                        closed_orders.append(close_order)
                        logger.info(f"✅ Closed {position['side']} position: {position['contracts']} contracts")

            return closed_orders if closed_orders else None

        except Exception as e:
            logger.error(f"❌ Error closing position: {e}")
            return None

    async def get_order_book(self, depth: int = 20) -> Optional[Dict]:
        """Get real-time order book for DOGE/USDT."""
        try:
            orderbook = await self.exchange.fetch_order_book(self.symbol, depth)
            return {
                'symbol': self.symbol,
                'bids': orderbook['bids'][:depth],
                'asks': orderbook['asks'][:depth],
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"❌ Error fetching order book: {e}")
            return None

    async def get_recent_trades(self, limit: int = 50) -> Optional[List[Dict]]:
        """Get recent trades for DOGE/USDT."""
        try:
            trades = await self.exchange.fetch_trades(self.symbol, limit=limit)
            return [{
                'id': trade['id'],
                'price': trade['price'],
                'amount': trade['amount'],
                'side': trade['side'],
                'timestamp': trade['timestamp']
            } for trade in trades]
        except Exception as e:
            logger.error(f"❌ Error fetching recent trades: {e}")
            return None

    async def cancel_order(self, order_id: str) -> bool:
        """Cancel a specific order."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot cancel order - no API keys configured")
                return False

            await self.exchange.cancel_order(order_id, self.symbol)
            logger.info(f"✅ Order cancelled: {order_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Error cancelling order: {e}")
            return False

    async def cancel_all_orders(self) -> bool:
        """Cancel all open orders for DOGE/USDT."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot cancel orders - no API keys configured")
                return False

            # Get open orders
            orders = await self.exchange.fetch_open_orders(self.symbol)

            cancelled_count = 0
            for order in orders:
                try:
                    await self.exchange.cancel_order(order['id'], self.symbol)
                    cancelled_count += 1
                except:
                    pass  # Continue with other orders

            logger.info(f"✅ Cancelled {cancelled_count} orders")
            return True

        except Exception as e:
            logger.error(f"❌ Error cancelling all orders: {e}")
            return False

    async def get_open_orders(self) -> Optional[List[Dict]]:
        """Get all open orders for DOGE/USDT."""
        try:
            if not self.api_key:
                return []

            orders = await self.exchange.fetch_open_orders(self.symbol)
            return [{
                'id': order['id'],
                'symbol': order['symbol'],
                'side': order['side'],
                'type': order['type'],
                'amount': order['amount'],
                'price': order['price'],
                'filled': order['filled'],
                'remaining': order['remaining'],
                'status': order['status'],
                'timestamp': order['timestamp']
            } for order in orders]

        except Exception as e:
            logger.error(f"❌ Error fetching open orders: {e}")
            return None

    async def close_position_legacy(self, symbol: str = None) -> bool:
        """Close position for specified symbol or DOGE/USDT."""
        try:
            if not self.api_key:
                logger.error("❌ Cannot close position - no API keys configured")
                return False

            target_symbol = symbol or self.symbol

            if target_symbol not in self.positions:
                logger.warning(f"⚠️ No open position for {target_symbol}")
                return False

            position = self.positions[target_symbol]

            # Close position by placing opposite order
            close_side = 'sell' if position.side == 'long' else 'buy'

            order = await self.exchange.create_market_order(
                symbol=target_symbol,
                side=close_side,
                amount=abs(position.size),
                params={'type': 'swap', 'reduceOnly': True}
            )

            logger.info(f"✅ Position closed: {target_symbol} {position.side} {position.size}")
            return True

        except Exception as e:
            logger.error(f"❌ Error closing position: {e}")
            return False

    async def get_current_position(self, symbol: str = None) -> Optional[Position]:
        """Get current position for symbol."""
        target_symbol = symbol or self.symbol
        return self.positions.get(target_symbol)

    async def get_account_info(self) -> Optional[AccountBalance]:
        """Get current account balance information."""
        return self.account_balance

    async def get_market_price(self, symbol: str = None) -> float:
        """Get current market price."""
        target_symbol = symbol or self.symbol
        market_data = self.market_data.get(target_symbol)
        return market_data.price if market_data else 0.0

    def stop_monitoring(self):
        """Stop all monitoring tasks."""
        self.running = False
        logger.info("🛑 HTX Futures monitoring stopped")

    async def disconnect(self):
        """Disconnect from HTX Futures API."""
        self.stop_monitoring()
        if self.exchange:
            await self.exchange.close()
        self.connected = False
        logger.info("🔌 HTX Futures client disconnected")
