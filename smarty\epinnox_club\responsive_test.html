<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Circle - Responsive Design Test</title>
    <link rel="stylesheet" href="/static/css/design_system.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--primary-600);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: var(--bg-card);
            border-radius: 12px;
            border: 1px solid var(--border-primary);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: var(--touch-target-min);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        /* Responsive test grid */
        @media (min-width: 640px) {
            .test-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (min-width: 768px) {
            .test-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .test-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        /* Breakpoint indicators */
        .breakpoint-indicator::before {
            content: "XS (< 640px)";
        }
        
        @media (min-width: 640px) {
            .breakpoint-indicator::before {
                content: "SM (≥ 640px)";
            }
        }
        
        @media (min-width: 768px) {
            .breakpoint-indicator::before {
                content: "MD (≥ 768px)";
            }
        }
        
        @media (min-width: 1024px) {
            .breakpoint-indicator::before {
                content: "LG (≥ 1024px)";
            }
        }
        
        @media (min-width: 1280px) {
            .breakpoint-indicator::before {
                content: "XL (≥ 1280px)";
            }
        }
        
        @media (min-width: 1536px) {
            .breakpoint-indicator::before {
                content: "2XL (≥ 1536px)";
            }
        }
    </style>
</head>
<body>
    <div class="breakpoint-indicator"></div>
    
    <div class="test-container">
        <h1 style="color: var(--warning-400); text-align: center; margin-bottom: 30px;">
            Money Circle Responsive Design Test
        </h1>
        
        <!-- Touch Target Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Touch Target Test</h2>
            <p style="color: var(--text-secondary);">All buttons should be at least 44px tall for touch accessibility.</p>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                <button class="btn btn-primary touch-target">Primary Button</button>
                <button class="btn btn-secondary touch-target">Secondary Button</button>
                <button class="place-order-btn" style="width: auto; min-width: 150px;">Order Button</button>
            </div>
        </div>
        
        <!-- Grid Layout Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Responsive Grid Test</h2>
            <p style="color: var(--text-secondary);">Grid should adapt: 1 column (XS), 2 columns (SM), 3 columns (MD), 4 columns (LG+).</p>
            
            <div class="test-grid">
                <div class="test-card">Card 1</div>
                <div class="test-card">Card 2</div>
                <div class="test-card">Card 3</div>
                <div class="test-card">Card 4</div>
                <div class="test-card">Card 5</div>
                <div class="test-card">Card 6</div>
                <div class="test-card">Card 7</div>
                <div class="test-card">Card 8</div>
            </div>
        </div>
        
        <!-- Dashboard Grid Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Dashboard Layout Test</h2>
            <p style="color: var(--text-secondary);">Dashboard grid should be 1 column on mobile, 2 columns on tablet+.</p>
            
            <div class="dashboard-grid">
                <div class="portfolio-overview">
                    <h3>Portfolio Overview (Full Width)</h3>
                    <div class="portfolio-cards">
                        <div class="portfolio-card">
                            <h4>Total Value</h4>
                            <div class="value">$1,234.56</div>
                            <div class="change positive">****%</div>
                        </div>
                        <div class="portfolio-card">
                            <h4>Available Balance</h4>
                            <div class="value">$567.89</div>
                            <div class="change negative">-1.2%</div>
                        </div>
                    </div>
                </div>
                
                <div class="exchange-connections">
                    <h3>Exchange Connections</h3>
                    <div class="exchange-grid">
                        <div class="exchange-card connected">
                            <h4>Binance</h4>
                            <div class="connection-status" style="color: var(--success-500);">Connected</div>
                        </div>
                    </div>
                </div>
                
                <div class="trading-interface">
                    <h3>Trading Interface</h3>
                    <div class="trading-tabs">
                        <button class="tab-btn active">Market Order</button>
                        <button class="tab-btn">Limit Order</button>
                    </div>
                    <button class="place-order-btn">Place Order</button>
                </div>
            </div>
        </div>
        
        <!-- Market Widgets Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Market Widgets Test</h2>
            <p style="color: var(--text-secondary);">Market widgets should stack on mobile, 2 columns on tablet, 3 columns on desktop.</p>
            
            <div class="market-widgets">
                <div class="market-widget">
                    <h4>Price Chart</h4>
                    <div style="height: 150px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        Chart Area
                    </div>
                </div>
                <div class="market-widget">
                    <h4>Order Book</h4>
                    <div style="height: 150px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        Order Book
                    </div>
                </div>
                <div class="market-widget">
                    <h4>Recent Trades</h4>
                    <div style="height: 150px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        Trades List
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Typography Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Typography Responsiveness</h2>
            <p style="color: var(--text-secondary);">Text should scale appropriately on different screen sizes.</p>
            
            <h1 class="text-4xl" style="color: var(--warning-400);">Heading 1 (4xl)</h1>
            <h2 class="text-3xl" style="color: var(--warning-400);">Heading 2 (3xl)</h2>
            <h3 class="text-2xl" style="color: var(--warning-400);">Heading 3 (2xl)</h3>
            <p class="text-base" style="color: var(--text-secondary);">Body text should remain readable at all screen sizes.</p>
        </div>
        
        <!-- Touch Interaction Test -->
        <div class="test-section">
            <h2 style="color: var(--primary-400);">Touch Interaction Test</h2>
            <p style="color: var(--text-secondary);">Test hover vs touch interactions (hover effects should only work on hover-capable devices).</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div class="portfolio-card">
                    <h4>Hover/Touch Card</h4>
                    <p>Should respond to both hover and touch</p>
                </div>
                <div class="analytics-card">
                    <h4>Analytics Card</h4>
                    <div class="metric">$1,234</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactivity for testing
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Log screen size changes
        window.addEventListener('resize', function() {
            console.log(`Screen size: ${window.innerWidth}x${window.innerHeight}`);
        });
        
        console.log(`Initial screen size: ${window.innerWidth}x${window.innerHeight}`);
    </script>
</body>
</html>
