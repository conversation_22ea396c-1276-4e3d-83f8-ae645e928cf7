"""
Backtesting framework for the smart-trader system.
"""

from .backtester import Backtester
from .visualizer import BacktestVisualizer
from .optimizer import StrategyOptimizer
from .strategies import (
    simple_moving_average_crossover,
    bollinger_bands_strategy,
    rsi_strategy,
    enhanced_multi_signal_strategy,
    model_ensemble_strategy,
    smart_model_integrated_strategy
)
from .analytics import EnhancedAnalytics, PerformanceMetrics

__all__ = [
    'Backtester',
    'BacktestVisualizer',
    'StrategyOptimizer',
    'simple_moving_average_crossover',
    'bollinger_bands_strategy',
    'rsi_strategy',
    'enhanced_multi_signal_strategy',
    'model_ensemble_strategy',
    'smart_model_integrated_strategy',
    'EnhancedAnalytics',
    'PerformanceMetrics'
]
