/* Money Circle Critical CSS - Above-the-fold optimization */

/* Critical CSS Variables - Inline for fastest loading */
:root{
    --primary-600:#8b5cf6;
    --warning-400:#fbbf24;
    --bg-primary:#0f1419;
    --bg-secondary:#1a1f2e;
    --bg-card:rgba(255,255,255,.05);
    --text-primary:#f1f5f9;
    --text-secondary:#e2e8f0;
    --border-primary:rgba(255,255,255,.1);
    --space-4:1rem;
    --space-6:1.5rem;
    --radius-lg:.5rem;
    --radius-xl:.75rem;
    --transition-normal:300ms ease;
    --touch-target-min:44px;
    --mobile-gap:.75rem;
}

/* Critical Base Styles */
*{box-sizing:border-box}
html{scroll-behavior:smooth}
body{
    font-family:'Inter','Segoe UI','Roboto','Helvetica Neue',sans-serif;
    background:linear-gradient(135deg,var(--bg-primary) 0%,var(--bg-secondary) 100%);
    color:var(--text-secondary);
    line-height:1.5;
    margin:0;
    padding:0;
    min-height:100vh;
    -webkit-font-smoothing:antialiased;
    -moz-osx-font-smoothing:grayscale;
    display:flex;
    flex-direction:column;
}

/* Critical Layout */
.main-content{
    flex:1;
    padding:var(--space-6) 0;
    min-height:calc(100vh - 200px);
}

.container{
    width:100%;
    max-width:1400px;
    margin:0 auto;
    padding:0 var(--space-4);
}

/* Critical Dashboard Grid - Above the fold */
.dashboard-grid{
    display:grid;
    grid-template-columns:1fr;
    gap:var(--mobile-gap);
    margin-bottom:30px;
}

/* Critical Portfolio Overview - First visible element */
.portfolio-overview{
    grid-column:1/-1;
    background:rgba(0,0,0,.3);
    border-radius:16px;
    padding:25px;
    border:1px solid rgba(255,255,255,.1);
    backdrop-filter:blur(10px);
}

.portfolio-overview h2{
    color:var(--warning-400);
    margin-bottom:20px;
    font-size:1.5em;
    font-weight:600;
}

.portfolio-cards{
    display:grid;
    grid-template-columns:repeat(auto-fit,minmax(250px,1fr));
    gap:20px;
}

.portfolio-card{
    background:rgba(255,255,255,.05);
    border-radius:12px;
    padding:20px;
    border:1px solid rgba(255,255,255,.1);
    transition:all .3s ease;
    min-height:var(--touch-target-min);
    touch-action:manipulation;
}

.portfolio-card h3{
    color:#94a3b8;
    font-size:.9em;
    margin-bottom:10px;
    text-transform:uppercase;
    letter-spacing:.5px;
}

.portfolio-card .value{
    color:#ffffff;
    font-size:1.8em;
    font-weight:700;
    margin-bottom:8px;
}

.portfolio-card .change{
    font-size:.9em;
    font-weight:600;
}

.portfolio-card .change.positive{color:#4CAF50}
.portfolio-card .change.negative{color:#f44336}

/* Critical Button Styles */
.btn{
    display:inline-flex;
    align-items:center;
    justify-content:center;
    gap:.5rem;
    padding:.75rem 1.5rem;
    border:none;
    border-radius:var(--radius-lg);
    font-weight:500;
    font-size:.875rem;
    text-decoration:none;
    cursor:pointer;
    transition:var(--transition-normal);
    white-space:nowrap;
    min-height:var(--touch-target-min);
    touch-action:manipulation;
    -webkit-tap-highlight-color:transparent;
}

.btn-primary{
    background:linear-gradient(135deg,var(--primary-600),#7c3aed);
    color:white;
}

/* Critical Text Utilities */
.text-primary{color:var(--text-primary)}
.text-secondary{color:var(--text-secondary)}

/* Critical Responsive - Mobile First */
@media (min-width:768px){
    .dashboard-grid{
        grid-template-columns:1fr 1fr;
        gap:30px;
    }
}

/* Critical Touch Interactions */
@media (hover:hover){
    .portfolio-card:hover{
        transform:translateY(-2px);
        box-shadow:0 8px 20px rgba(255,215,0,.1);
        border-color:rgba(255,215,0,.3);
    }
    .btn-primary:hover{
        background:linear-gradient(135deg,#7c3aed,#6d28d9);
        transform:translateY(-1px);
        box-shadow:0 0 20px rgba(139,92,246,.3);
    }
}

@media (hover:none){
    .portfolio-card:focus,.portfolio-card:active{
        background:rgba(255,255,255,.08);
        border-color:rgba(255,215,0,.3);
        transform:scale(.98);
    }
    .btn:focus,.btn:active{
        background:rgba(255,255,255,.08);
        transform:scale(.98);
    }
}

/* Critical Mobile Optimization */
@media (max-width:474px){
    .container{padding:0 1rem}
    .main-content{padding:1rem 0}
    .portfolio-cards{grid-template-columns:1fr;gap:.75rem}
}

/* Loading States - Critical for perceived performance */
.loading{
    opacity:.7;
    pointer-events:none;
}

.loading::after{
    content:'';
    position:absolute;
    top:50%;
    left:50%;
    width:20px;
    height:20px;
    margin:-10px 0 0 -10px;
    border:2px solid var(--primary-600);
    border-radius:50%;
    border-top-color:transparent;
    animation:spin 1s linear infinite;
}

@keyframes spin{
    to{transform:rotate(360deg)}
}

/* Critical Performance Optimizations */
.will-change-transform{will-change:transform}
.gpu-accelerated{transform:translateZ(0)}

/* Preload Critical Fonts */
@font-face{
    font-family:'Inter';
    font-style:normal;
    font-weight:400;
    font-display:swap;
    src:local('Inter Regular'),local('Inter-Regular');
}

@font-face{
    font-family:'Inter';
    font-style:normal;
    font-weight:500;
    font-display:swap;
    src:local('Inter Medium'),local('Inter-Medium');
}

@font-face{
    font-family:'Inter';
    font-style:normal;
    font-weight:600;
    font-display:swap;
    src:local('Inter SemiBold'),local('Inter-SemiBold');
}

/* Critical Backdrop Filter Fallback */
@supports not (backdrop-filter:blur(10px)){
    .portfolio-overview{
        background:rgba(0,0,0,.5);
    }
}

/* Critical Print Styles */
@media print{
    .dashboard-grid{display:block!important}
    .portfolio-card{break-inside:avoid;margin-bottom:20px}
    .btn{display:none!important}
}
