<!-- Money Circle Unified Header -->
<header class="unified-header">
    <div class="header-content">
        <!-- Branding Section -->
        <div class="header-branding">
            <div class="logo">E</div>
            <div class="brand-text">
                <h1>Money Circle</h1>
                <p>Investment Club</p>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="header-nav" id="main-navigation">
            <!-- Navigation will be populated by JavaScript based on user role -->
        </nav>

        <!-- User Info Section -->
        <div class="header-user-info">
            <!-- Notification Badge -->
            <div class="notification-badge" id="notification-badge" onclick="toggleNotifications()">
                <span class="notification-icon">🔔</span>
                <span class="notification-count" id="notification-count">0</span>
            </div>

            <!-- User Profile Dropdown -->
            <div class="user-profile-dropdown" onclick="toggleUserDropdown()">
                <div class="user-avatar" id="user-avatar">
                    <!-- Avatar will be populated by JavaScript -->
                </div>
                <div class="user-details" id="user-details">
                    <!-- User details will be populated by JavaScript -->
                </div>
                <div class="dropdown-arrow">▼</div>
            </div>

            <!-- User Dropdown Menu -->
            <div class="user-dropdown-menu" id="user-dropdown-menu">
                <div class="dropdown-header">
                    <div class="dropdown-user-info" id="dropdown-user-info">
                        <!-- User info will be populated by JavaScript -->
                    </div>
                </div>
                <div class="dropdown-divider"></div>
                <a href="/profile" class="dropdown-item">
                    <span class="dropdown-icon">👤</span>
                    Profile Settings
                </a>
                <a href="/notifications" class="dropdown-item">
                    <span class="dropdown-icon">🔔</span>
                    Notifications
                </a>
                <div class="dropdown-divider"></div>
                <a href="/logout" class="dropdown-item logout">
                    <span class="dropdown-icon">🚪</span>
                    Logout
                </a>
            </div>
        </div>

        <!-- Mobile Menu Toggle -->
        <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav" id="mobile-navigation">
        <!-- Mobile navigation will be populated by JavaScript -->
    </nav>
</header>

<!-- Breadcrumb Navigation (optional, shown on specific pages) -->
{% if breadcrumbs %}
<div class="header-breadcrumb">
    <div class="breadcrumb-content">
        {% for breadcrumb in breadcrumbs %}
            {% if loop.last %}
                <span class="breadcrumb-item active">{{ breadcrumb.title }}</span>
            {% else %}
                <a href="{{ breadcrumb.url }}" class="breadcrumb-item">{{ breadcrumb.title }}</a>
                <span class="breadcrumb-separator">›</span>
            {% endif %}
        {% endfor %}
    </div>
</div>
{% endif %}
