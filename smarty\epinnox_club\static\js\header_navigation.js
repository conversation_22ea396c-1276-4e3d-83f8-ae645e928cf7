/**
 * Header Navigation and Role-Based Access Control
 * Handles dynamic navigation menu generation based on user roles
 */

// Navigation configuration based on user roles
const NAVIGATION_CONFIG = {
    admin: [
        { path: '/dashboard', label: '📊 Dashboard', icon: '📊' },
        { path: '/club', label: '🏛️ Club', icon: '🏛️' },
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👥 Members', icon: '👥' },
        { path: '/club/analytics', label: '📈 Analytics', icon: '📈' },
        { path: '/admin', label: '🛡️ Admin', icon: '🛡️', adminOnly: true }
    ],
    member: [
        { path: '/dashboard', label: '📊 Dashboard', icon: '📊' },
        { path: '/club', label: '🏛️ Club', icon: '🏛️' },
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👥 Members', icon: '👥' },
        { path: '/club/analytics', label: '📈 Analytics', icon: '📈' }
    ],
    viewer: [
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👥 Members', icon: '👥' },
        { path: '/club/analytics', label: '📈 Analytics', icon: '📈' }
    ]
};

// Global variables
let currentUser = null;
let notificationCount = 0;

/**
 * Initialize header navigation
 */
function initializeHeaderNavigation() {
    // Get user data from global context or API
    getCurrentUser().then(user => {
        if (user) {
            currentUser = user;
            setupNavigation();
            setupUserProfile();
            setupNotifications();
            setupMobileMenu();
        }
    });
}

/**
 * Get current user data
 */
async function getCurrentUser() {
    try {
        // Try to get user from global context first
        if (window.currentUser) {
            return window.currentUser;
        }
        
        // Fallback to API call
        const response = await fetch('/api/user/current');
        if (response.ok) {
            return await response.json();
        }
        
        return null;
    } catch (error) {
        console.error('Error getting current user:', error);
        return null;
    }
}

/**
 * Setup navigation menu based on user role
 */
function setupNavigation() {
    const navContainer = document.getElementById('main-navigation');
    const mobileNavContainer = document.getElementById('mobile-navigation');
    
    if (!navContainer || !currentUser) return;
    
    const userRole = currentUser.role || 'viewer';
    const navItems = NAVIGATION_CONFIG[userRole] || NAVIGATION_CONFIG.viewer;
    const currentPath = window.location.pathname;
    
    // Clear existing navigation
    navContainer.innerHTML = '';
    if (mobileNavContainer) {
        mobileNavContainer.innerHTML = '';
    }
    
    // Generate navigation items
    navItems.forEach(item => {
        const navLink = createNavLink(item, currentPath);
        navContainer.appendChild(navLink);
        
        // Add to mobile navigation
        if (mobileNavContainer) {
            const mobileNavLink = createNavLink(item, currentPath);
            mobileNavContainer.appendChild(mobileNavLink);
        }
    });
}

/**
 * Create navigation link element
 */
function createNavLink(item, currentPath) {
    const link = document.createElement('a');
    link.href = item.path;
    link.className = 'nav-link';
    link.textContent = item.label;
    
    // Add role-based styling
    if (item.adminOnly) {
        link.classList.add('admin-only');
    } else if (currentUser.role === 'member' || currentUser.role === 'admin') {
        link.classList.add('member-plus');
    } else {
        link.classList.add('viewer-plus');
    }
    
    // Mark active link
    if (currentPath === item.path || 
        (item.path !== '/' && currentPath.startsWith(item.path))) {
        link.classList.add('active');
    }
    
    return link;
}

/**
 * Setup user profile section
 */
function setupUserProfile() {
    if (!currentUser) return;
    
    // Update user avatar
    const userAvatar = document.getElementById('user-avatar');
    if (userAvatar) {
        userAvatar.textContent = currentUser.username.charAt(0).toUpperCase();
        
        // Set role-based avatar color
        const roleColors = {
            admin: 'linear-gradient(135deg, #f44336, #d32f2f)',
            member: 'linear-gradient(135deg, #2196F3, #1976D2)',
            viewer: 'linear-gradient(135deg, #4CAF50, #45a049)'
        };
        userAvatar.style.background = roleColors[currentUser.role] || roleColors.viewer;
    }
    
    // Update user details
    const userDetails = document.getElementById('user-details');
    if (userDetails) {
        userDetails.innerHTML = `
            <p class="user-welcome">Welcome, ${currentUser.username}</p>
            <p class="user-role">${currentUser.role.toUpperCase()}</p>
        `;
    }
    
    // Update dropdown user info
    const dropdownUserInfo = document.getElementById('dropdown-user-info');
    if (dropdownUserInfo) {
        dropdownUserInfo.innerHTML = `
            <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
            <div>
                <div style="color: white; font-weight: 600;">${currentUser.username}</div>
                <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">${currentUser.email}</div>
                <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.75rem; text-transform: uppercase;">${currentUser.role}</div>
            </div>
        `;
    }
}

/**
 * Setup notifications
 */
function setupNotifications() {
    // Get notification count
    getNotificationCount().then(count => {
        updateNotificationBadge(count);
    });
    
    // Setup periodic notification updates
    setInterval(() => {
        getNotificationCount().then(count => {
            updateNotificationBadge(count);
        });
    }, 30000); // Update every 30 seconds
}

/**
 * Get notification count from API
 */
async function getNotificationCount() {
    try {
        const response = await fetch('/api/notifications/count');
        if (response.ok) {
            const data = await response.json();
            return data.count || 0;
        }
        return 0;
    } catch (error) {
        console.error('Error getting notification count:', error);
        return 0;
    }
}

/**
 * Update notification badge
 */
function updateNotificationBadge(count) {
    notificationCount = count;
    const badge = document.getElementById('notification-count');
    if (badge) {
        badge.textContent = count;
        badge.classList.toggle('hidden', count === 0);
    }
}

/**
 * Toggle notifications panel
 */
function toggleNotifications() {
    // TODO: Implement notifications panel
    console.log('Toggle notifications panel');
}

/**
 * Toggle user dropdown menu
 */
function toggleUserDropdown() {
    const dropdown = document.getElementById('user-dropdown-menu');
    const trigger = document.querySelector('.user-profile-dropdown');
    
    if (dropdown && trigger) {
        const isActive = dropdown.classList.contains('active');
        
        if (isActive) {
            dropdown.classList.remove('active');
            trigger.classList.remove('active');
        } else {
            dropdown.classList.add('active');
            trigger.classList.add('active');
        }
    }
}

/**
 * Setup mobile menu
 */
function setupMobileMenu() {
    // Close dropdown when clicking outside
    document.addEventListener('click', (event) => {
        const dropdown = document.getElementById('user-dropdown-menu');
        const trigger = document.querySelector('.user-profile-dropdown');
        
        if (dropdown && trigger && 
            !trigger.contains(event.target) && 
            !dropdown.contains(event.target)) {
            dropdown.classList.remove('active');
            trigger.classList.remove('active');
        }
    });
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
    const mobileNav = document.getElementById('mobile-navigation');
    const toggle = document.querySelector('.mobile-menu-toggle');
    
    if (mobileNav && toggle) {
        const isActive = mobileNav.classList.contains('active');
        
        if (isActive) {
            mobileNav.classList.remove('active');
            toggle.classList.remove('active');
        } else {
            mobileNav.classList.add('active');
            toggle.classList.add('active');
        }
    }
}

/**
 * Check if user has permission for a specific route
 */
function hasPermission(route, userRole = null) {
    const role = userRole || (currentUser && currentUser.role) || 'viewer';
    const navItems = NAVIGATION_CONFIG[role] || NAVIGATION_CONFIG.viewer;
    
    return navItems.some(item => 
        item.path === route || 
        (route.startsWith(item.path) && item.path !== '/')
    );
}

/**
 * Redirect if user doesn't have permission
 */
function checkRoutePermission() {
    const currentPath = window.location.pathname;
    
    if (currentUser && !hasPermission(currentPath)) {
        // Redirect to appropriate default page based on role
        const defaultRoutes = {
            admin: '/dashboard',
            member: '/dashboard',
            viewer: '/club/analytics'
        };
        
        const defaultRoute = defaultRoutes[currentUser.role] || '/club/analytics';
        window.location.href = defaultRoute;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeHeaderNavigation();
    
    // Check route permission after a short delay to ensure user data is loaded
    setTimeout(checkRoutePermission, 1000);
});

// Export functions for global use
window.toggleNotifications = toggleNotifications;
window.toggleUserDropdown = toggleUserDropdown;
window.toggleMobileMenu = toggleMobileMenu;
window.hasPermission = hasPermission;
