/**
 * Header Navigation and Role-Based Access Control
 * Handles dynamic navigation menu generation based on user roles
 */

// Navigation configuration based on user roles
const NAVIGATION_CONFIG = {
    admin: [
        { path: '/dashboard', label: '📊 Dashboard', icon: '📊' },
        { path: '/live-trading', label: '⚡ Live Trading', icon: '⚡', epinnoxOnly: true },
        { path: '/auto-trader', label: '🤖 Auto Trader', icon: '🤖', memberPlus: true },
        { path: '/signals', label: '📡 Signals', icon: '📡', memberPlus: true },
        { path: '/portfolio-analytics', label: '📈 Portfolio', icon: '📈', memberPlus: true },
        { path: '/social-trading', label: '👥 Social', icon: '👥', memberPlus: true },
        { path: '/club', label: '🏛️ Club', icon: '🏛️' },
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👤 Members', icon: '👤' },
        { path: '/club/analytics', label: '📊 Analytics', icon: '📊' },
        { path: '/admin', label: '🛡️ Admin', icon: '🛡️', adminOnly: true }
    ],
    member: [
        { path: '/dashboard', label: '📊 Dashboard', icon: '📊' },
        { path: '/auto-trader', label: '🤖 Auto Trader', icon: '🤖', memberPlus: true },
        { path: '/signals', label: '📡 Signals', icon: '📡', memberPlus: true },
        { path: '/portfolio-analytics', label: '📈 Portfolio', icon: '📈', memberPlus: true },
        { path: '/social-trading', label: '👥 Social', icon: '👥', memberPlus: true },
        { path: '/club', label: '🏛️ Club', icon: '🏛️' },
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👤 Members', icon: '👤' },
        { path: '/club/analytics', label: '📊 Analytics', icon: '📊' }
    ],
    viewer: [
        { path: '/dashboard', label: '📊 Dashboard', icon: '📊' },
        { path: '/club', label: '🏛️ Club', icon: '🏛️' },
        { path: '/club/strategies', label: '🎯 Strategies', icon: '🎯' },
        { path: '/club/members', label: '👤 Members', icon: '👤' },
        { path: '/club/analytics', label: '📊 Analytics', icon: '📊' }
    ]
};

// Global variables
let currentUser = null;
let notificationCount = 0;

/**
 * Initialize header navigation
 */
function initializeHeaderNavigation() {
    // Get user data from global context or API
    getCurrentUser().then(user => {
        if (user) {
            currentUser = user;
            setupNavigation();
            setupUserProfile();
            setupNotifications();
            setupMobileMenu();
        }
    });
}

/**
 * Get current user data
 */
async function getCurrentUser() {
    try {
        // Try to get user from global context first
        if (window.currentUser) {
            return window.currentUser;
        }

        // Fallback to API call
        const response = await fetch('/api/user/current');
        if (response.ok) {
            return await response.json();
        }

        return null;
    } catch (error) {
        console.error('Error getting current user:', error);
        return null;
    }
}

/**
 * Setup navigation menu based on user role
 */
function setupNavigation() {
    const navContainer = document.getElementById('main-navigation');
    const mobileNavContainer = document.getElementById('mobile-navigation');

    if (!navContainer || !currentUser) return;

    const userRole = currentUser.role || 'viewer';
    const navItems = NAVIGATION_CONFIG[userRole] || NAVIGATION_CONFIG.viewer;
    const currentPath = window.location.pathname;

    // Clear existing navigation
    navContainer.innerHTML = '';
    if (mobileNavContainer) {
        mobileNavContainer.innerHTML = '';
    }

    // Generate navigation items
    navItems.forEach(item => {
        // Check if item should be shown for current user
        if (item.epinnoxOnly && currentUser.username !== 'epinnox') {
            return; // Skip epinnox-only items for other users
        }

        const navLink = createNavLink(item, currentPath);
        navContainer.appendChild(navLink);

        // Add to mobile navigation
        if (mobileNavContainer) {
            const mobileNavLink = createNavLink(item, currentPath);
            mobileNavContainer.appendChild(mobileNavLink);
        }
    });
}

/**
 * Create navigation link element
 */
function createNavLink(item, currentPath) {
    const link = document.createElement('a');
    link.href = item.path;
    link.className = 'nav-link';
    link.textContent = item.label;

    // Add role-based styling
    if (item.epinnoxOnly) {
        link.classList.add('epinnox-only');
    } else if (item.adminOnly) {
        link.classList.add('admin-only');
    } else if (item.memberPlus) {
        link.classList.add('member-plus');
    } else if (currentUser.role === 'member' || currentUser.role === 'admin') {
        link.classList.add('member-plus');
    } else {
        link.classList.add('viewer-plus');
    }

    // Mark active link
    if (currentPath === item.path ||
        (item.path !== '/' && currentPath.startsWith(item.path))) {
        link.classList.add('active');
    }

    return link;
}

/**
 * Setup user profile section
 */
function setupUserProfile() {
    if (!currentUser) return;

    // Update user avatar
    const userAvatar = document.getElementById('user-avatar');
    if (userAvatar) {
        userAvatar.textContent = currentUser.username.charAt(0).toUpperCase();

        // Set role-based avatar color
        const roleColors = {
            admin: 'linear-gradient(135deg, #f44336, #d32f2f)',
            member: 'linear-gradient(135deg, #2196F3, #1976D2)',
            viewer: 'linear-gradient(135deg, #4CAF50, #45a049)'
        };
        userAvatar.style.background = roleColors[currentUser.role] || roleColors.viewer;
    }

    // Update user details
    const userDetails = document.getElementById('user-details');
    if (userDetails) {
        userDetails.innerHTML = `
            <p class="user-welcome">Welcome, ${currentUser.username}</p>
            <p class="user-role">${currentUser.role.toUpperCase()}</p>
        `;
    }

    // Update dropdown user info
    const dropdownUserInfo = document.getElementById('dropdown-user-info');
    if (dropdownUserInfo) {
        dropdownUserInfo.innerHTML = `
            <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
            <div>
                <div style="color: white; font-weight: 600;">${currentUser.username}</div>
                <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">${currentUser.email}</div>
                <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.75rem; text-transform: uppercase;">${currentUser.role}</div>
            </div>
        `;
    }
}

/**
 * Setup notifications
 */
function setupNotifications() {
    // Get notification count
    getNotificationCount().then(count => {
        updateNotificationBadge(count);
    });

    // Setup periodic notification updates
    setInterval(() => {
        getNotificationCount().then(count => {
            updateNotificationBadge(count);
        });
    }, 30000); // Update every 30 seconds
}

/**
 * Get notification count from API
 */
async function getNotificationCount() {
    try {
        const response = await fetch('/api/notifications/count');
        if (response.ok) {
            const data = await response.json();
            return data.count || 0;
        }
        return 0;
    } catch (error) {
        console.error('Error getting notification count:', error);
        return 0;
    }
}

/**
 * Update notification badge
 */
function updateNotificationBadge(count) {
    notificationCount = count;
    const badge = document.getElementById('notification-count');
    if (badge) {
        badge.textContent = count;
        badge.classList.toggle('hidden', count === 0);
    }
}

/**
 * Toggle notifications panel
 */
function toggleNotifications() {
    console.log('Toggle notifications panel');

    // Check if notifications panel exists
    let notificationsPanel = document.getElementById('notifications-panel');

    if (!notificationsPanel) {
        // Create notifications panel if it doesn't exist
        notificationsPanel = createNotificationsPanel();
        document.body.appendChild(notificationsPanel);
    }

    // Toggle panel visibility
    const isVisible = notificationsPanel.classList.contains('active');

    if (isVisible) {
        notificationsPanel.classList.remove('active');
    } else {
        notificationsPanel.classList.add('active');
        // Load notifications when opening
        loadNotifications();
    }

    // Close panel when clicking outside
    setTimeout(() => {
        document.addEventListener('click', closeNotificationsOnOutsideClick, { once: true });
    }, 100);
}

/**
 * Create notifications panel
 */
function createNotificationsPanel() {
    const panel = document.createElement('div');
    panel.id = 'notifications-panel';
    panel.className = 'notifications-panel';

    panel.innerHTML = `
        <div class="notifications-header">
            <h3>Notifications</h3>
            <button class="close-btn" onclick="closeNotifications()">×</button>
        </div>
        <div class="notifications-content" id="notifications-content">
            <div class="loading-notifications">
                <div class="spinner"></div>
                <p>Loading notifications...</p>
            </div>
        </div>
        <div class="notifications-footer">
            <a href="/notifications" class="view-all-btn">View All Notifications</a>
        </div>
    `;

    return panel;
}

/**
 * Load notifications
 */
async function loadNotifications() {
    const content = document.getElementById('notifications-content');
    if (!content) return;

    try {
        const response = await fetch('/api/notifications/recent');
        if (response.ok) {
            const notifications = await response.json();
            displayNotifications(notifications);
        } else {
            displayNotificationsError();
        }
    } catch (error) {
        console.error('Error loading notifications:', error);
        displayNotificationsError();
    }
}

/**
 * Display notifications
 */
function displayNotifications(notifications) {
    const content = document.getElementById('notifications-content');
    if (!content) return;

    if (notifications.length === 0) {
        content.innerHTML = `
            <div class="no-notifications">
                <div class="no-notifications-icon">🔔</div>
                <p>No new notifications</p>
            </div>
        `;
        return;
    }

    const notificationsHtml = notifications.map(notification => `
        <div class="notification-item ${notification.is_read ? 'read' : 'unread'}" data-id="${notification.id}">
            <div class="notification-icon">${getNotificationIcon(notification.type)}</div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${formatNotificationTime(notification.created_at)}</div>
            </div>
            ${!notification.is_read ? '<div class="unread-indicator"></div>' : ''}
        </div>
    `).join('');

    content.innerHTML = notificationsHtml;

    // Add click handlers to mark as read
    content.querySelectorAll('.notification-item.unread').forEach(item => {
        item.addEventListener('click', () => markNotificationAsRead(item.dataset.id));
    });
}

/**
 * Display notifications error
 */
function displayNotificationsError() {
    const content = document.getElementById('notifications-content');
    if (!content) return;

    content.innerHTML = `
        <div class="notifications-error">
            <div class="error-icon">⚠️</div>
            <p>Failed to load notifications</p>
            <button onclick="loadNotifications()" class="retry-btn">Retry</button>
        </div>
    `;
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
    const icons = {
        'trade': '💰',
        'alert': '🚨',
        'system': '⚙️',
        'message': '💬',
        'update': '🔄',
        'success': '✅',
        'warning': '⚠️',
        'error': '❌'
    };
    return icons[type] || '🔔';
}

/**
 * Format notification time
 */
function formatNotificationTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
}

/**
 * Mark notification as read
 */
async function markNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST'
        });

        if (response.ok) {
            // Update UI
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('unread');
                item.classList.add('read');
                const indicator = item.querySelector('.unread-indicator');
                if (indicator) indicator.remove();
            }

            // Update notification count
            updateNotificationCount();
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
}

/**
 * Close notifications panel
 */
function closeNotifications() {
    const panel = document.getElementById('notifications-panel');
    if (panel) {
        panel.classList.remove('active');
    }
}

/**
 * Close notifications when clicking outside
 */
function closeNotificationsOnOutsideClick(event) {
    const panel = document.getElementById('notifications-panel');
    const badge = document.getElementById('notification-badge');

    if (panel && !panel.contains(event.target) && !badge.contains(event.target)) {
        closeNotifications();
    }
}

/**
 * Toggle user dropdown menu
 */
function toggleUserDropdown() {
    console.log('Toggle user dropdown');

    const dropdown = document.getElementById('user-dropdown-menu');
    const trigger = document.querySelector('.user-profile-dropdown');

    if (!dropdown || !trigger) {
        console.error('User dropdown elements not found');
        return;
    }

    const isActive = dropdown.classList.contains('active');

    // Close any other open dropdowns first
    closeAllDropdowns();

    if (!isActive) {
        dropdown.classList.add('active');
        trigger.classList.add('active');

        // Add click outside listener
        setTimeout(() => {
            document.addEventListener('click', closeUserDropdownOnOutsideClick, { once: true });
        }, 100);

        console.log('User dropdown opened');
    } else {
        dropdown.classList.remove('active');
        trigger.classList.remove('active');
        console.log('User dropdown closed');
    }
}

/**
 * Close user dropdown when clicking outside
 */
function closeUserDropdownOnOutsideClick(event) {
    const dropdown = document.getElementById('user-dropdown-menu');
    const trigger = document.querySelector('.user-profile-dropdown');

    if (dropdown && trigger &&
        !dropdown.contains(event.target) &&
        !trigger.contains(event.target)) {
        dropdown.classList.remove('active');
        trigger.classList.remove('active');
        console.log('User dropdown closed (outside click)');
    }
}

/**
 * Close all dropdowns
 */
function closeAllDropdowns() {
    // Close user dropdown
    const userDropdown = document.getElementById('user-dropdown-menu');
    const userTrigger = document.querySelector('.user-profile-dropdown');
    if (userDropdown && userTrigger) {
        userDropdown.classList.remove('active');
        userTrigger.classList.remove('active');
    }

    // Close notifications panel
    const notificationsPanel = document.getElementById('notifications-panel');
    if (notificationsPanel) {
        notificationsPanel.classList.remove('active');
    }

    // Close mobile menu
    const mobileNav = document.getElementById('mobile-navigation');
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileNav && mobileToggle) {
        mobileNav.classList.remove('active');
        mobileToggle.classList.remove('active');
    }
}

/**
 * Setup mobile menu
 */
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-navigation');

    if (mobileToggle && mobileNav) {
        mobileToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Mobile menu toggle clicked');

            // Close other dropdowns first
            closeAllDropdowns();

            const isActive = mobileNav.classList.contains('active');

            if (!isActive) {
                mobileNav.classList.add('active');
                mobileToggle.classList.add('active');
                document.body.classList.add('mobile-menu-open');

                // Add click outside listener
                setTimeout(() => {
                    document.addEventListener('click', closeMobileMenuOnOutsideClick, { once: true });
                }, 100);

                console.log('Mobile menu opened');
            } else {
                closeMobileMenu();
            }
        });

        // Close mobile menu when clicking on navigation links
        const mobileLinks = mobileNav.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                closeMobileMenu();
            });
        });
    }
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-navigation');

    if (mobileToggle && mobileNav) {
        mobileNav.classList.remove('active');
        mobileToggle.classList.remove('active');
        document.body.classList.remove('mobile-menu-open');
        console.log('Mobile menu closed');
    }
}

/**
 * Close mobile menu when clicking outside
 */
function closeMobileMenuOnOutsideClick(event) {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-navigation');

    if (mobileToggle && mobileNav &&
        !mobileNav.contains(event.target) &&
        !mobileToggle.contains(event.target)) {
        closeMobileMenu();
    }
}

/**
 * Toggle mobile menu (legacy function - now handled by setupMobileMenu)
 */
function toggleMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileToggle) {
        mobileToggle.click();
    }
}

/**
 * Check if user has permission for a specific route
 */
function hasPermission(route, userRole = null) {
    const role = userRole || (currentUser && currentUser.role) || 'viewer';
    const navItems = NAVIGATION_CONFIG[role] || NAVIGATION_CONFIG.viewer;

    return navItems.some(item =>
        item.path === route ||
        (route.startsWith(item.path) && item.path !== '/')
    );
}

/**
 * Redirect if user doesn't have permission
 */
function checkRoutePermission() {
    const currentPath = window.location.pathname;

    if (currentUser && !hasPermission(currentPath)) {
        // Redirect to appropriate default page based on role
        const defaultRoutes = {
            admin: '/dashboard',
            member: '/dashboard',
            viewer: '/club/analytics'
        };

        const defaultRoute = defaultRoutes[currentUser.role] || '/club/analytics';
        window.location.href = defaultRoute;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeHeaderNavigation();

    // Check route permission after a short delay to ensure user data is loaded
    setTimeout(checkRoutePermission, 1000);
});

// Export functions for global use
window.toggleNotifications = toggleNotifications;
window.toggleUserDropdown = toggleUserDropdown;
window.toggleMobileMenu = toggleMobileMenu;
window.closeMobileMenu = closeMobileMenu;
window.closeNotifications = closeNotifications;
window.closeAllDropdowns = closeAllDropdowns;
window.hasPermission = hasPermission;
