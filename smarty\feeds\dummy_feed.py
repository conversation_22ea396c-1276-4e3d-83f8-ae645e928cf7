"""
Dummy feed for the smart-trader system.

This module provides a dummy feed that generates random data.
"""

import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

from core.events import Kline, Trade, OrderbookDelta, Side
from .base_feed import BaseFeed

logger = logging.getLogger(__name__)

class DummyFeed(BaseFeed):
    """
    Dummy feed that generates random data.

    This class provides a dummy feed that generates random data
    for testing purposes.
    """

    def __init__(self, symbols: List[str] = None, interval: float = 1.0):
        """
        Initialize the dummy feed.

        Args:
            symbols: List of symbols to generate data for
            interval: Interval between data points in seconds
        """
        super().__init__("dummy", symbols)
        self.interval = interval
        self.tasks = []
        self.prices = {}
        self.volumes = {}

    async def _start(self):
        """Start the dummy feed."""
        # Initialize prices and volumes
        for symbol in self.symbols:
            self.prices[symbol] = 50000.0  # Initial price
            self.volumes[symbol] = 1.0  # Initial volume

        # Create tasks for each symbol
        for symbol in self.symbols:
            self.tasks.append(asyncio.create_task(self._generate_klines(symbol)))
            self.tasks.append(asyncio.create_task(self._generate_trades(symbol)))
            self.tasks.append(asyncio.create_task(self._generate_orderbook(symbol)))

        logger.info(f"Started dummy feed for {len(self.symbols)} symbols")

    async def _stop(self):
        """Stop the dummy feed."""
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()

        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)

        self.tasks = []
        logger.info("Stopped dummy feed")

    async def _generate_klines(self, symbol: str):
        """
        Generate random klines.

        Args:
            symbol: Symbol to generate klines for
        """
        try:
            while self.running:
                # Generate random price movement
                price_change = random.uniform(-0.01, 0.01) * self.prices[symbol]
                self.prices[symbol] += price_change

                # Generate random volume
                volume_change = random.uniform(-0.1, 0.1) * self.volumes[symbol]
                self.volumes[symbol] = max(0.1, self.volumes[symbol] + volume_change)

                # Create kline
                now = datetime.now()
                kline = Kline(
                    symbol=symbol,
                    interval="1m",
                    open_time=now - timedelta(minutes=1),
                    open=self.prices[symbol] - price_change,
                    high=max(self.prices[symbol], self.prices[symbol] - price_change),
                    low=min(self.prices[symbol], self.prices[symbol] - price_change),
                    close=self.prices[symbol],
                    volume=self.volumes[symbol],
                    close_time=now,
                    quote_volume=self.prices[symbol] * self.volumes[symbol],
                    count=random.randint(10, 100),
                    timestamp=now
                )

                # Emit kline
                self._emit("kline", kline)

                # Wait for next interval
                await asyncio.sleep(self.interval)

        except asyncio.CancelledError:
            logger.debug(f"Cancelled kline generation for {symbol}")
        except Exception as e:
            logger.error(f"Error generating klines for {symbol}: {e}")

    async def _generate_trades(self, symbol: str):
        """
        Generate random trades.

        Args:
            symbol: Symbol to generate trades for
        """
        try:
            while self.running:
                # Generate random price
                price = self.prices[symbol] * random.uniform(0.999, 1.001)

                # Generate random quantity
                quantity = random.uniform(0.001, 0.1)

                # Generate random side
                side = random.choice([Side.BUY, Side.SELL])

                # Create trade
                trade = Trade(
                    symbol=symbol,
                    id=str(random.randint(1000000, 9999999)),
                    price=price,
                    quantity=quantity,
                    side=side,
                    timestamp=datetime.now()
                )

                # Emit trade
                self._emit("trade", trade)

                # Wait for next interval
                await asyncio.sleep(self.interval / 10)  # More frequent than klines

        except asyncio.CancelledError:
            logger.debug(f"Cancelled trade generation for {symbol}")
        except Exception as e:
            logger.error(f"Error generating trades for {symbol}: {e}")

    async def _generate_orderbook(self, symbol: str):
        """
        Generate random orderbook updates.

        Args:
            symbol: Symbol to generate orderbook updates for
        """
        try:
            while self.running:
                # Generate random bids and asks
                price = self.prices[symbol]
                bids = []
                asks = []

                # Generate bids (lower than current price)
                for i in range(5):
                    bid_price = price * (1 - 0.001 * (i + 1))
                    bid_quantity = random.uniform(0.1, 1.0)
                    bids.append([bid_price, bid_quantity])

                # Generate asks (higher than current price)
                for i in range(5):
                    ask_price = price * (1 + 0.001 * (i + 1))
                    ask_quantity = random.uniform(0.1, 1.0)
                    asks.append([ask_price, ask_quantity])

                # Create orderbook delta
                orderbook = OrderbookDelta(
                    symbol=symbol,
                    bids=bids,
                    asks=asks,
                    timestamp=datetime.now()
                )

                # Emit orderbook
                self._emit("orderbook", orderbook)

                # Wait for next interval
                await asyncio.sleep(self.interval / 5)  # More frequent than klines

        except asyncio.CancelledError:
            logger.debug(f"Cancelled orderbook generation for {symbol}")
        except Exception as e:
            logger.error(f"Error generating orderbook for {symbol}: {e}")

    def add_symbol(self, symbol: str):
        """
        Add a symbol to the feed.

        Args:
            symbol: Symbol to add
        """
        if not self.running:
            super().add_symbol(symbol)
        else:
            logger.warning("Cannot add symbol while feed is running")

    def remove_symbol(self, symbol: str):
        """
        Remove a symbol from the feed.

        Args:
            symbol: Symbol to remove
        """
        if not self.running:
            super().remove_symbol(symbol)
        else:
            logger.warning("Cannot remove symbol while feed is running")
