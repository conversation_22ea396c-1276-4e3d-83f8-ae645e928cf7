#!/usr/bin/env python3
"""
API endpoint diagnostic test for Epinnox Investment Club dashboard.
Tests all API endpoints to identify loading issues.
"""

import requests
import json
import time

def test_api_endpoint(endpoint, description, port=8082):
    """Test a specific API endpoint."""
    url = f"http://localhost:{port}{endpoint}"
    try:
        response = requests.get(url, timeout=5)
        
        if response.status_code == 401:
            return {"status": "AUTH_REQUIRED", "message": "Authentication required"}
        elif response.status_code == 200:
            try:
                data = response.json()
                return {"status": "SUCCESS", "data": data}
            except json.JSONDecodeError:
                return {"status": "INVALID_JSON", "message": "Response not valid JSON"}
        else:
            return {"status": "ERROR", "code": response.status_code, "message": response.text}
    except requests.exceptions.ConnectionError:
        return {"status": "CONNECTION_ERROR", "message": "Cannot connect to dashboard"}
    except requests.exceptions.Timeout:
        return {"status": "TIMEOUT", "message": "Request timed out"}
    except Exception as e:
        return {"status": "EXCEPTION", "message": str(e)}

def test_authenticated_endpoints():
    """Test API endpoints with authentication."""
    print("🔍 Testing API Endpoints (Authentication Required)...")
    
    endpoints = [
        ("/api/market-data", "Market Data"),
        ("/api/orderbook", "Order Book"),
        ("/api/recent-trades", "Recent Trades"),
        ("/api/debug", "Debug Information"),
        ("/api/strategy/status", "Strategy Status"),
        ("/api/ai-analysis", "AI Analysis"),
        ("/api/market-sentiment", "Market Sentiment"),
        ("/api/stats", "System Stats")
    ]
    
    results = {}
    for endpoint, description in endpoints:
        print(f"   Testing {description}...")
        result = test_api_endpoint(endpoint, description)
        results[description] = result
        
        if result["status"] == "SUCCESS":
            print(f"   ✅ {description}: OK")
        elif result["status"] == "AUTH_REQUIRED":
            print(f"   🔐 {description}: Authentication required (expected)")
        else:
            print(f"   ❌ {description}: {result['status']} - {result.get('message', 'Unknown error')}")
    
    return results

def test_dashboard_connectivity():
    """Test basic dashboard connectivity."""
    print("🔍 Testing Dashboard Connectivity...")
    
    ports = [8082, 8083, 8084]
    for port in ports:
        try:
            response = requests.get(f"http://localhost:{port}/", timeout=2)
            if response.status_code in [200, 302, 401]:  # Any of these means server is running
                print(f"   ✅ Dashboard found on port {port}")
                return port
        except:
            continue
    
    print("   ❌ No dashboard found on common ports")
    return None

def simulate_authenticated_request():
    """Simulate what happens when user is authenticated."""
    print("\n🔍 Simulating Authenticated API Calls...")
    print("   Note: This requires manual login at http://localhost:8082")
    print("   Login credentials: epinnox / securepass123")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Test if we can access protected endpoints
    endpoints_to_test = [
        "/api/orderbook",
        "/api/recent-trades", 
        "/api/debug"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = session.get(f"http://localhost:8082{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {endpoint}: {len(str(data))} bytes of data")
            elif response.status_code == 401:
                print(f"   🔐 {endpoint}: Authentication required")
            else:
                print(f"   ❌ {endpoint}: Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: {e}")

def test_websocket_endpoint():
    """Test WebSocket endpoint availability."""
    print("\n🔍 Testing WebSocket Endpoint...")
    try:
        # Test WebSocket endpoint (should return 400 for HTTP request)
        response = requests.get("http://localhost:8082/ws", timeout=5)
        if response.status_code == 400:
            print("   ✅ WebSocket endpoint available (returns 400 for HTTP as expected)")
        elif response.status_code == 401:
            print("   🔐 WebSocket endpoint requires authentication")
        else:
            print(f"   ❓ WebSocket endpoint returned status {response.status_code}")
    except Exception as e:
        print(f"   ❌ WebSocket test failed: {e}")

def main():
    """Run comprehensive API diagnostic."""
    print("🎯 EPINNOX DASHBOARD API DIAGNOSTIC")
    print("=" * 50)
    
    # Test basic connectivity
    active_port = test_dashboard_connectivity()
    if not active_port:
        print("❌ Dashboard not accessible - cannot run API tests")
        return
    
    # Test API endpoints
    api_results = test_authenticated_endpoints()
    
    # Test WebSocket
    test_websocket_endpoint()
    
    # Simulate authenticated requests
    simulate_authenticated_request()
    
    print("\n📊 API DIAGNOSTIC SUMMARY")
    print("=" * 30)
    
    auth_required_count = sum(1 for r in api_results.values() if r["status"] == "AUTH_REQUIRED")
    success_count = sum(1 for r in api_results.values() if r["status"] == "SUCCESS")
    error_count = len(api_results) - auth_required_count - success_count
    
    print(f"Total Endpoints Tested: {len(api_results)}")
    print(f"Authentication Required: {auth_required_count} (expected)")
    print(f"Successful Responses: {success_count}")
    print(f"Errors: {error_count}")
    
    if error_count == 0:
        print("\n✅ All API endpoints responding correctly")
        print("🔐 Login required at: http://localhost:8082")
        print("   Credentials: epinnox / securepass123")
    else:
        print(f"\n❌ {error_count} API endpoints have issues")

if __name__ == "__main__":
    main()
