#!/usr/bin/env python3
"""
Money Circle Enhanced Club Analytics
Comprehensive club-wide performance dashboards with real-time data and interactive visualizations.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
import aiohttp_jinja2
from auth.decorators import get_current_user
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from club.analytics import ClubAnalytics
from club.social_trading import SocialTrading

logger = logging.getLogger(__name__)

class EnhancedClubAnalytics:
    """Professional club analytics dashboard with comprehensive metrics."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
        self.analytics = ClubAnalytics(db_manager)
        self.social_trading = SocialTrading(db_manager)

    async def serve_club_analytics(self, request: web.Request) -> web.Response:
        """Serve the enhanced club analytics dashboard."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get analytics data
        club_overview = self._get_club_overview()
        performance_metrics = self._get_performance_metrics()
        portfolio_analysis = self._get_portfolio_analysis()
        strategy_analytics = self._get_strategy_analytics()
        member_analytics = self._get_member_analytics()
        risk_metrics = self._get_risk_metrics()
        growth_tracking = self._get_growth_tracking()

        # Prepare template context
        context = {
            'user': user,
            'club_overview': club_overview,
            'performance_metrics': performance_metrics,
            'portfolio_analysis': portfolio_analysis,
            'strategy_analytics': strategy_analytics,
            'member_analytics': member_analytics,
            'risk_metrics': risk_metrics,
            'growth_tracking': growth_tracking,
            'current_path': request.path  # Add current path for navigation
        }

        # Render using template
        return aiohttp_jinja2.render_template('club_analytics.html', request, context)

    def _get_club_overview(self) -> Dict[str, Any]:
        """Get club overview metrics."""
        try:
            # Get current portfolio value and performance
            cursor = self.db.conn.execute("""
                SELECT
                    COUNT(DISTINCT u.id) as total_members,
                    COUNT(DISTINCT CASE WHEN tp.date >= date('now', '-30 days') THEN u.id END) as active_traders,
                    COUNT(DISTINCT sp.id) as active_strategies,
                    COUNT(tp.id) as total_trades,
                    COALESCE(AVG(tp.total_return), 0) as avg_return,
                    COALESCE(AVG(tp.win_rate), 0) as avg_win_rate,
                    COALESCE(SUM(tp.portfolio_value), 0) as total_value,
                    COALESCE(AVG(tp.trade_size), 0) as avg_trade_size
                FROM users u
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                LEFT JOIN strategy_proposals sp ON u.id = sp.user_id AND sp.status = 'approved' AND sp.is_active = 1
                WHERE u.role IN ('member', 'admin')
            """)

            row = cursor.fetchone()

            # Get best performing strategy
            cursor = self.db.conn.execute("""
                SELECT sp.title FROM strategy_proposals sp
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved'
                GROUP BY sp.id, sp.title
                ORDER BY AVG(spr.total_return) DESC
                LIMIT 1
            """)
            best_strategy = cursor.fetchone()

            # Get top performer
            cursor = self.db.conn.execute("""
                SELECT u.username FROM users u
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                GROUP BY u.id, u.username
                ORDER BY AVG(tp.total_return) DESC
                LIMIT 1
            """)
            top_performer = cursor.fetchone()

            return {
                'total_members': row[0] or 0,
                'active_traders': row[1] or 0,
                'active_strategies': row[2] or 0,
                'total_trades': row[3] or 0,
                'total_return': row[4] or 0.0,
                'win_rate': row[5] or 0.0,
                'total_value': row[6] or 0.0,
                'avg_trade_size': row[7] or 0.0,
                'best_strategy': best_strategy[0] if best_strategy else 'N/A',
                'top_performer': top_performer[0] if top_performer else 'N/A',
                # Mock change values (would be calculated from historical data)
                'value_change': 5.2,
                'return_change': 2.1,
                'win_rate_change': 1.5,
                'strategies_change': 2
            }

        except Exception as e:
            logger.error(f"Error getting club overview: {e}")
            return {
                'total_members': 0,
                'active_traders': 0,
                'active_strategies': 0,
                'total_trades': 0,
                'total_return': 0.0,
                'win_rate': 0.0,
                'total_value': 0.0,
                'avg_trade_size': 0.0,
                'best_strategy': 'N/A',
                'top_performer': 'N/A',
                'value_change': 0.0,
                'return_change': 0.0,
                'win_rate_change': 0.0,
                'strategies_change': 0
            }

    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    AVG(total_return) as avg_return,
                    STDEV(total_return) as return_volatility,
                    AVG(sharpe_ratio) as avg_sharpe,
                    AVG(win_rate) as avg_win_rate,
                    MAX(total_return) as max_return,
                    MIN(total_return) as min_return,
                    AVG(max_drawdown) as avg_drawdown
                FROM trading_performance
                WHERE total_return IS NOT NULL
            """)

            row = cursor.fetchone()
            return {
                'avg_return': row[0] or 0.0,
                'return_volatility': row[1] or 0.0,
                'avg_sharpe': row[2] or 0.0,
                'avg_win_rate': row[3] or 0.0,
                'max_return': row[4] or 0.0,
                'min_return': row[5] or 0.0,
                'avg_drawdown': row[6] or 0.0
            }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}

    def _get_portfolio_analysis(self) -> Dict[str, Any]:
        """Get portfolio allocation and analysis."""
        try:
            # Mock portfolio data (would come from actual portfolio tracking)
            return {
                'allocations': [
                    {'asset': 'BTC', 'percentage': 35.5, 'value': 35500, 'return': 12.3},
                    {'asset': 'ETH', 'percentage': 25.2, 'value': 25200, 'return': 8.7},
                    {'asset': 'BNB', 'percentage': 15.8, 'value': 15800, 'return': 15.2},
                    {'asset': 'ADA', 'percentage': 12.1, 'value': 12100, 'return': -2.1},
                    {'asset': 'SOL', 'percentage': 11.4, 'value': 11400, 'return': 22.8}
                ],
                'total_value': 100000,
                'diversification_score': 85.2,
                'correlation_risk': 'Medium'
            }

        except Exception as e:
            logger.error(f"Error getting portfolio analysis: {e}")
            return {}

    def _get_strategy_analytics(self) -> List[Dict[str, Any]]:
        """Get strategy performance analytics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    sp.title,
                    sp.strategy_type,
                    AVG(spr.total_return) as avg_return,
                    AVG(spr.win_rate) as win_rate,
                    AVG(spr.sharpe_ratio) as sharpe_ratio,
                    COUNT(spr.id) as trades_count,
                    MAX(spr.followers_count) as followers
                FROM strategy_proposals sp
                LEFT JOIN strategy_performance spr ON sp.id = spr.strategy_id
                WHERE sp.status = 'approved'
                GROUP BY sp.id, sp.title, sp.strategy_type
                ORDER BY avg_return DESC
                LIMIT 10
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'title': row[0],
                    'type': row[1],
                    'avg_return': row[2] or 0.0,
                    'win_rate': row[3] or 0.0,
                    'sharpe_ratio': row[4] or 0.0,
                    'trades_count': row[5] or 0,
                    'followers': row[6] or 0
                })

            return strategies

        except Exception as e:
            logger.error(f"Error getting strategy analytics: {e}")
            return []

    def _get_member_analytics(self) -> List[Dict[str, Any]]:
        """Get member performance analytics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    u.username,
                    mp.display_name,
                    AVG(tp.total_return) as avg_return,
                    AVG(tp.win_rate) as win_rate,
                    COUNT(tp.id) as trades_count,
                    AVG(tp.sharpe_ratio) as sharpe_ratio,
                    SUM(tp.portfolio_value) as total_value
                FROM users u
                LEFT JOIN member_profiles mp ON u.id = mp.user_id
                LEFT JOIN trading_performance tp ON u.id = tp.user_id
                WHERE u.role IN ('member', 'admin') AND tp.total_return IS NOT NULL
                GROUP BY u.id, u.username, mp.display_name
                ORDER BY avg_return DESC
                LIMIT 15
            """)

            members = []
            for row in cursor.fetchall():
                members.append({
                    'username': row[0],
                    'display_name': row[1] or row[0],
                    'avg_return': row[2] or 0.0,
                    'win_rate': row[3] or 0.0,
                    'trades_count': row[4] or 0,
                    'sharpe_ratio': row[5] or 0.0,
                    'total_value': row[6] or 0.0
                })

            return members

        except Exception as e:
            logger.error(f"Error getting member analytics: {e}")
            return []

    def _get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk analysis metrics."""
        try:
            cursor = self.db.conn.execute("""
                SELECT
                    AVG(volatility) as avg_volatility,
                    AVG(max_drawdown) as avg_drawdown,
                    AVG(sharpe_ratio) as avg_sharpe,
                    STDEV(total_return) as return_std
                FROM trading_performance
                WHERE volatility IS NOT NULL
            """)

            row = cursor.fetchone()
            return {
                'avg_volatility': row[0] or 0.0,
                'avg_drawdown': row[1] or 0.0,
                'avg_sharpe': row[2] or 0.0,
                'return_std': row[3] or 0.0,
                'var_95': -2.5,  # Mock Value at Risk
                'beta': 1.2,     # Mock Beta
                'correlation': 0.75  # Mock correlation with market
            }

        except Exception as e:
            logger.error(f"Error getting risk metrics: {e}")
            return {}

    def _get_growth_tracking(self) -> Dict[str, Any]:
        """Get growth tracking data."""
        try:
            # Mock growth data (would come from historical tracking)
            return {
                'member_growth': [
                    {'date': '2024-01-01', 'members': 45},
                    {'date': '2024-02-01', 'members': 52},
                    {'date': '2024-03-01', 'members': 61},
                    {'date': '2024-04-01', 'members': 68},
                    {'date': '2024-05-01', 'members': 75}
                ],
                'value_growth': [
                    {'date': '2024-01-01', 'value': 85000},
                    {'date': '2024-02-01', 'value': 92000},
                    {'date': '2024-03-01', 'value': 98000},
                    {'date': '2024-04-01', 'value': 105000},
                    {'date': '2024-05-01', 'value': 112000}
                ]
            }

        except Exception as e:
            logger.error(f"Error getting growth tracking: {e}")
            return {}

    def _render_performance_table(self, metrics: Dict[str, Any]) -> str:
        """Render performance metrics table."""
        if not metrics:
            return '<div class="empty-data">No performance data available</div>'

        return f"""
        <table class="metrics-table">
            <tr>
                <td>Average Return</td>
                <td class="metric-value positive">{metrics['avg_return']:+.2f}%</td>
            </tr>
            <tr>
                <td>Return Volatility</td>
                <td class="metric-value">{metrics['return_volatility']:.2f}%</td>
            </tr>
            <tr>
                <td>Sharpe Ratio</td>
                <td class="metric-value">{metrics['avg_sharpe']:.2f}</td>
            </tr>
            <tr>
                <td>Win Rate</td>
                <td class="metric-value">{metrics['avg_win_rate']:.1f}%</td>
            </tr>
            <tr>
                <td>Max Return</td>
                <td class="metric-value positive">{metrics['max_return']:+.2f}%</td>
            </tr>
            <tr>
                <td>Max Drawdown</td>
                <td class="metric-value negative">{metrics['avg_drawdown']:.2f}%</td>
            </tr>
        </table>
        """

    def _render_asset_performance(self, portfolio: Dict[str, Any]) -> str:
        """Render asset performance list."""
        if not portfolio or 'allocations' not in portfolio:
            return '<div class="empty-data">No portfolio data available</div>'

        assets_html = ""
        for asset in portfolio['allocations']:
            return_class = "positive" if asset['return'] > 0 else "negative"
            assets_html += f"""
            <div class="asset-item">
                <div class="asset-info">
                    <span class="asset-name">{asset['asset']}</span>
                    <span class="asset-allocation">{asset['percentage']:.1f}%</span>
                </div>
                <div class="asset-metrics">
                    <span class="asset-value">${asset['value']:,.0f}</span>
                    <span class="asset-return {return_class}">{asset['return']:+.1f}%</span>
                </div>
            </div>
            """

        return assets_html

    def _render_risk_indicators(self, risk_metrics: Dict[str, Any]) -> str:
        """Render risk indicators."""
        if not risk_metrics:
            return '<div class="empty-data">No risk data available</div>'

        return f"""
        <div class="risk-indicator">
            <span class="risk-label">Volatility</span>
            <span class="risk-value">{risk_metrics['avg_volatility']:.2f}%</span>
        </div>
        <div class="risk-indicator">
            <span class="risk-label">Max Drawdown</span>
            <span class="risk-value negative">{risk_metrics['avg_drawdown']:.2f}%</span>
        </div>
        <div class="risk-indicator">
            <span class="risk-label">VaR (95%)</span>
            <span class="risk-value negative">{risk_metrics['var_95']:.2f}%</span>
        </div>
        <div class="risk-indicator">
            <span class="risk-label">Beta</span>
            <span class="risk-value">{risk_metrics['beta']:.2f}</span>
        </div>
        """

    def _render_strategy_table(self, strategies: List[Dict[str, Any]]) -> str:
        """Render strategy performance table."""
        if not strategies:
            return '<div class="empty-data">No strategy data available</div>'

        table_html = """
        <table class="strategy-table">
            <thead>
                <tr>
                    <th>Strategy</th>
                    <th>Type</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Sharpe</th>
                    <th>Trades</th>
                    <th>Followers</th>
                </tr>
            </thead>
            <tbody>
        """

        for strategy in strategies:
            return_class = "positive" if strategy['avg_return'] > 0 else "negative"
            table_html += f"""
            <tr>
                <td class="strategy-name">{strategy['title']}</td>
                <td class="strategy-type">{strategy['type'].replace('_', ' ').title()}</td>
                <td class="return-value {return_class}">{strategy['avg_return']:+.2f}%</td>
                <td class="win-rate">{strategy['win_rate']:.1f}%</td>
                <td class="sharpe-ratio">{strategy['sharpe_ratio']:.2f}</td>
                <td class="trades-count">{strategy['trades_count']}</td>
                <td class="followers-count">{strategy['followers']}</td>
            </tr>
            """

        table_html += """
            </tbody>
        </table>
        """

        return table_html

    def _render_member_table(self, members: List[Dict[str, Any]]) -> str:
        """Render member performance table."""
        if not members:
            return '<div class="empty-data">No member data available</div>'

        table_html = """
        <table class="member-table">
            <thead>
                <tr>
                    <th>Member</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Sharpe</th>
                    <th>Trades</th>
                    <th>Portfolio Value</th>
                </tr>
            </thead>
            <tbody>
        """

        for member in members:
            return_class = "positive" if member['avg_return'] > 0 else "negative"
            table_html += f"""
            <tr>
                <td class="member-name">{member['display_name']}</td>
                <td class="return-value {return_class}">{member['avg_return']:+.2f}%</td>
                <td class="win-rate">{member['win_rate']:.1f}%</td>
                <td class="sharpe-ratio">{member['sharpe_ratio']:.2f}</td>
                <td class="trades-count">{member['trades_count']}</td>
                <td class="portfolio-value">${member['total_value']:,.0f}</td>
            </tr>
            """

        table_html += """
            </tbody>
        </table>
        """

        return table_html

    def _render_risk_cards(self, risk_metrics: Dict[str, Any]) -> str:
        """Render risk overview cards."""
        if not risk_metrics:
            return '<div class="empty-data">No risk data available</div>'

        return f"""
        <div class="risk-card">
            <div class="risk-card-header">
                <span class="risk-icon">📊</span>
                <span class="risk-title">Volatility</span>
            </div>
            <div class="risk-card-value">{risk_metrics['avg_volatility']:.2f}%</div>
            <div class="risk-card-status moderate">Moderate</div>
        </div>
        <div class="risk-card">
            <div class="risk-card-header">
                <span class="risk-icon">📉</span>
                <span class="risk-title">Max Drawdown</span>
            </div>
            <div class="risk-card-value">{risk_metrics['avg_drawdown']:.2f}%</div>
            <div class="risk-card-status low">Acceptable</div>
        </div>
        <div class="risk-card">
            <div class="risk-card-header">
                <span class="risk-icon">⚡</span>
                <span class="risk-title">Sharpe Ratio</span>
            </div>
            <div class="risk-card-value">{risk_metrics['avg_sharpe']:.2f}</div>
            <div class="risk-card-status good">Good</div>
        </div>
        <div class="risk-card">
            <div class="risk-card-header">
                <span class="risk-icon">🎯</span>
                <span class="risk-title">Beta</span>
            </div>
            <div class="risk-card-value">{risk_metrics['beta']:.2f}</div>
            <div class="risk-card-status moderate">Moderate</div>
        </div>
        """
