#!/usr/bin/env python3
"""
Fix Demo Account <PERSON><PERSON>nti<PERSON>
Diagnose and fix authentication issues with demo accounts.
"""

import sqlite3
import bcrypt
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_fix_demo_auth():
    """Check and fix demo account authentication."""
    db_path = "data/money_circle.db"
    
    if not Path(db_path).exists():
        logger.error(f"Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        
        # Check if demo users exist
        demo_users = ['trader_alex', 'crypto_sarah', 'quant_mike', 'forex_emma']
        
        logger.info("Checking demo users...")
        for username in demo_users:
            user = conn.execute("SELECT * FROM users WHERE username = ?", (username,)).fetchone()
            if user:
                logger.info(f"✅ {username}: Found (role: {user['role']})")
                
                # Test password verification
                test_password = "securepass123"
                stored_hash = user['hashed_password']
                
                try:
                    # Try bcrypt verification
                    is_valid = bcrypt.checkpw(test_password.encode('utf-8'), stored_hash.encode('utf-8'))
                    logger.info(f"   Password verification: {'✅ Valid' if is_valid else '❌ Invalid'}")
                    
                    if not is_valid:
                        # Re-hash the password with bcrypt
                        logger.info(f"   Fixing password hash for {username}...")
                        new_hash = bcrypt.hashpw(test_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                        
                        conn.execute("""
                            UPDATE users 
                            SET hashed_password = ?, agreement_accepted = 1, email_verified = 1
                            WHERE username = ?
                        """, (new_hash, username))
                        
                        logger.info(f"   ✅ Password hash updated for {username}")
                        
                except Exception as e:
                    logger.error(f"   ❌ Password verification error for {username}: {e}")
                    
                    # Re-hash the password with bcrypt
                    logger.info(f"   Fixing password hash for {username}...")
                    new_hash = bcrypt.hashpw(test_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                    
                    conn.execute("""
                        UPDATE users 
                        SET hashed_password = ?, agreement_accepted = 1, email_verified = 1
                        WHERE username = ?
                    """, (new_hash, username))
                    
                    logger.info(f"   ✅ Password hash updated for {username}")
                    
            else:
                logger.warning(f"❌ {username}: Not found - creating...")
                
                # Create missing demo user
                password_hash = bcrypt.hashpw("securepass123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                
                conn.execute("""
                    INSERT INTO users (username, email, hashed_password, role, agreement_accepted, email_verified, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    username,
                    f"{username}@example.com",
                    password_hash,
                    'member',
                    True,
                    True,
                    True
                ))
                
                logger.info(f"   ✅ Created {username}")
        
        conn.commit()
        conn.close()
        
        logger.info("\n✅ Demo account authentication fix completed!")
        logger.info("All demo users should now be able to login with password: securepass123")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing demo authentication: {e}")
        return False

if __name__ == "__main__":
    check_and_fix_demo_auth()
