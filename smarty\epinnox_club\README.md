# 💰 Money Circle Investment Club Platform

A comprehensive investment club platform for collaborative trading, portfolio management, and social trading features.

## 🌟 Features

### **Phase 1: Admin Dashboard & Role-Based Navigation**
- 🛡️ **Admin Dashboard** with comprehensive platform oversight
- 👥 **Role-Based Access Control** (Admin, Member, Viewer)
- 🧭 **Dynamic Navigation** based on user permissions
- 📊 **User Management** with role assignment capabilities

### **Phase 2: Auto Trader & Trading Signals**
- 🤖 **Auto Trader Dashboard** with strategy automation
- 📡 **Trading Signals Dashboard** with real-time market analysis
- ⚡ **Live Trading Interface** with position management
- 🎯 **Strategy Engine** with multiple trading algorithms

### **Phase 3: Portfolio Analytics & Social Trading**
- 📈 **Portfolio Analytics** with advanced performance metrics
- 👥 **Social Trading** with strategy sharing and following
- 🏆 **Community Leaderboards** with performance rankings
- 💬 **Discussion Forums** for strategy collaboration

### **Phase 4: Production Deployment & Security**
- 🔒 **Enterprise Security** with rate limiting and CSRF protection
- 📊 **Performance Monitoring** with real-time metrics
- 💾 **Automated Backups** with integrity verification
- 🏥 **Health Monitoring** with comprehensive status checks

## 🚀 Technology Stack

- **Backend**: Python 3.9+, aiohttp, asyncio
- **Database**: SQLite with WAL mode for production
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Real-time**: WebSocket connections for live data
- **Security**: JWT authentication, bcrypt password hashing
- **Monitoring**: Custom performance monitoring and alerting
- **Deployment**: Docker-ready with production configurations

## 📊 Market Data Integration

- **Binance**: Cryptocurrency market data
- **HTX (Huobi)**: Futures and spot trading data
- **Bybit**: Derivatives and perpetual contracts
- **Real-time WebSocket**: Live price feeds and order book data

## 🛡️ Security Features

- **Authentication**: JWT-based session management
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Configurable request throttling
- **CSRF Protection**: Cross-site request forgery prevention
- **Security Headers**: Comprehensive HTTP security headers
- **Input Sanitization**: XSS and injection attack prevention

## 📈 Performance Features

- **Real-time Monitoring**: Response time and system metrics
- **Database Optimization**: Indexed queries and WAL mode
- **Caching**: Intelligent caching for improved performance
- **Load Balancing**: Ready for horizontal scaling
- **Health Checks**: Automated system health monitoring

## 🏗️ Architecture

```
Money Circle Platform
├── 🎯 Core Application (app.py)
├── 🗄️ Database Layer (SQLite + Models)
├── 🔐 Authentication & Security
├── 📊 Market Data Management
├── 🤖 Trading Engine & Strategies
├── 📈 Analytics & Reporting
├── 👥 Social Trading Features
├── 🔧 Admin & Management Tools
└── 🚀 Production Deployment
```

## 🚀 Quick Start

### **Prerequisites**
- Python 3.9 or higher
- pip (Python package manager)
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/money-circle.git
   cd money-circle
   ```

2. **Create virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Initialize database**
   ```bash
   python -c "from database.models import DatabaseManager; DatabaseManager().initialize_database()"
   ```

5. **Start the application**
   ```bash
   python app.py
   ```

6. **Access the platform**
   - Open your browser to `http://localhost:8086`
   - Login with: `epinnox` / `securepass123`

## 🌐 Production Deployment

### **Environment Variables**
```bash
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8086
JWT_SECRET=your-secure-jwt-secret
DATABASE_URL=sqlite:///data/money_circle.db
LIVE_TRADING_ENABLED=true
TESTNET_MODE=false
```

### **Docker Deployment**
```bash
# Build image
docker build -t money-circle .

# Run container
docker run -d \
  --name money-circle \
  -p 8086:8086 \
  -v $(pwd)/data:/app/data \
  -e ENVIRONMENT=production \
  money-circle
```

## 📊 API Endpoints

### **Authentication**
- `POST /login` - User authentication
- `POST /logout` - User logout
- `GET /api/user/current` - Get current user info

### **Trading**
- `GET /api/market/overview` - Market data overview
- `POST /api/trading/order` - Place trading order
- `GET /api/trading/positions` - Get user positions
- `GET /api/trading/history` - Trading history

### **Admin**
- `GET /api/admin/users` - User management
- `GET /api/system/status` - System status
- `GET /api/system/metrics` - Performance metrics
- `POST /api/system/backup` - Create backup

### **Health Check**
- `GET /health` - Application health status

## 🧪 Testing

```bash
# Run basic health check
curl http://localhost:8086/health

# Run load test
ab -n 100 -c 10 http://localhost:8086/

# Test authentication
curl -X POST http://localhost:8086/login \
  -H "Content-Type: application/json" \
  -d '{"username":"epinnox","password":"securepass123"}'
```

## 📁 Project Structure

```
money-circle/
├── 📄 app.py                 # Main application entry point
├── 📊 database/              # Database models and management
├── 🔐 auth/                  # Authentication and authorization
├── 📈 dashboards/            # Dashboard implementations
├── 🤖 trading/               # Trading engine and strategies
├── 👥 club/                  # Social trading and club features
├── 🔧 admin/                 # Admin tools and management
├── 🚀 deployment/            # Production deployment scripts
├── 📊 monitoring/            # Performance monitoring
├── 💾 backup/                # Backup and recovery
├── 🔒 security/              # Security management
├── 🌐 static/                # Static assets (CSS, JS, images)
├── 📄 templates/             # HTML templates
├── 📋 requirements.txt       # Python dependencies
└── 🐳 Dockerfile            # Container configuration
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/yourusername/money-circle/wiki)
- **Issues**: [GitHub Issues](https://github.com/yourusername/money-circle/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/money-circle/discussions)

## 🎯 Roadmap

- [ ] **Mobile App**: React Native mobile application
- [ ] **Advanced Analytics**: Machine learning trading insights
- [ ] **Multi-Exchange**: Additional exchange integrations
- [ ] **API Gateway**: RESTful API for third-party integrations
- [ ] **Microservices**: Scalable microservice architecture

## 🏆 Acknowledgments

- **Epinnox Investment Club** - Platform requirements and testing
- **Market Data Providers** - Binance, HTX, Bybit APIs
- **Open Source Community** - Python, aiohttp, and related libraries

---

**Built with ❤️ for the Epinnox Investment Club Community**

*Money Circle - Where Investment Meets Innovation*
