# 💰 Money Circle - Epinnox Investment Club Platform

A comprehensive multi-user investment club platform that transforms the single-user Smart Trader dashboard into a collaborative trading environment for Epinnox members.

## 🌟 Features

### 🔐 Multi-User Authentication
- Secure user registration and login system
- Role-based access control (<PERSON><PERSON>, Member, Viewer)
- Session management with encrypted cookies
- Rate limiting and lockout protection

### 💹 Personal Trading Dashboard
- Individual portfolio overview with real-time balances
- Multi-exchange account integration (HTX, Binance, Bybit)
- Live position tracking with P&L calculations
- Personal trade history and performance analytics

### 🔗 Exchange Integration
- Secure API key encryption using Fernet symmetric encryption
- Support for multiple exchanges per user
- Real-time balance and position synchronization
- Automated credential validation

### 🏛️ Club Features
- Strategy proposal and voting system
- Club-wide performance analytics
- Member activity feed
- Collaborative decision making

### 🛡️ Security
- Encrypted API key storage
- Secure password hashing with bcrypt
- CSRF protection and secure sessions
- Audit logging for all trading activities

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- SQLite 3
- Access to exchange APIs (HTX, Binance, Bybit)

### Installation

1. **Clone and navigate to the project:**
   ```bash
   cd smarty/epinnox_club
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables (optional):**
   ```bash
   export FLASK_ENV=development
   export SECRET_KEY=your-secret-key-here
   export DATABASE_PATH=data/money_circle.db
   ```

4. **Initialize the database:**
   ```bash
   python -c "from database.models import DatabaseManager; DatabaseManager('data/money_circle.db')"
   ```

5. **Start the application:**
   ```bash
   python app.py
   ```

6. **Access the platform:**
   - Open http://localhost:8084
   - Login with default admin credentials:
     - Username: `epinnox`
     - Password: `securepass123`

## 📁 Project Structure

```
epinnox_club/
├── app.py                      # Main application entry point
├── config.py                   # Configuration management
├── requirements.txt            # Python dependencies
├── README.md                   # This file
├── auth/
│   ├── user_manager.py         # User authentication and management
│   └── decorators.py           # Authentication decorators
├── exchanges/
│   ├── account_manager.py      # Exchange account management
│   └── encryption_utils.py    # Secure API key encryption
├── dashboards/
│   ├── personal_dashboard.py   # Individual user dashboard
│   └── club_dashboard.py       # Club-wide dashboard (future)
├── trading/
│   ├── personal_trader.py      # Individual trading interface (future)
│   └── strategy_executor.py   # Strategy execution engine (future)
├── club/
│   ├── strategy_governance.py  # Strategy voting system (future)
│   └── activity_feed.py        # Member activity tracking (future)
├── database/
│   ├── models.py               # Database models and schema
│   └── migrations.py           # Database migrations (future)
├── static/
│   ├── css/                    # Stylesheets
│   └── js/                     # JavaScript files
└── templates/                  # HTML templates (future)
```

## 🔧 Configuration

### Environment Variables
- `FLASK_ENV`: Environment (development/production/testing)
- `HOST`: Server host (default: localhost)
- `PORT`: Server port (default: 8084)
- `SECRET_KEY`: Session encryption key
- `DATABASE_PATH`: SQLite database path
- `ENCRYPTION_KEY`: API key encryption key

### Database Schema
The platform uses SQLite with the following main tables:
- `users`: User accounts and roles
- `user_exchanges`: Encrypted exchange API credentials
- `strategy_proposals`: Club strategy proposals
- `strategy_votes`: Member votes on strategies
- `user_positions`: Trading positions
- `user_trades`: Trade history

## 🔐 Security Features

### API Key Encryption
All exchange API keys are encrypted using Fernet symmetric encryption before storage:
```python
from exchanges.encryption_utils import encrypt_api_credentials

encrypted = encrypt_api_credentials(api_key, secret_key, passphrase)
```

### User Authentication
Secure authentication with bcrypt password hashing and session management:
```python
from auth.user_manager import UserManager

user_manager = UserManager(db_manager)
user = user_manager.authenticate_user(username, password, ip_address)
```

### Role-Based Access Control
Three user roles with different permissions:
- **Admin**: Full platform access, user management, strategy approval
- **Member**: Personal trading, strategy voting, club participation
- **Viewer**: Read-only access to club data

## 🔌 Exchange Integration

### Supported Exchanges
- **HTX (Huobi)**: Spot and futures trading
- **Binance**: Spot and futures trading
- **Bybit**: Derivatives trading

### Adding Exchange Accounts
Users can securely add exchange accounts through the dashboard:
1. Navigate to Exchange Accounts section
2. Click "Add Exchange Account"
3. Select exchange and enter API credentials
4. Credentials are validated and encrypted before storage

## 🎯 Migration from Single-User Dashboard

The platform maintains compatibility with the existing Smart Trader system:
- Reuses existing data reading components
- Maintains WebSocket real-time updates
- Preserves strategy execution framework
- Extends functionality for multi-user scenarios

## 🚧 Development Roadmap

### Phase 1 (Current)
- ✅ Multi-user authentication system
- ✅ Personal dashboard with portfolio overview
- ✅ Exchange account management
- ✅ Secure API key encryption

### Phase 2 (Next)
- 🔄 Individual trading interface
- 🔄 Strategy assignment to personal accounts
- 🔄 Club governance and voting system
- 🔄 Advanced analytics and reporting

### Phase 3 (Future)
- 📋 Social trading features
- 📋 Copy trading functionality
- 📋 Advanced risk management
- 📋 Mobile application

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is proprietary software for Epinnox Investment Club.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**Money Circle** - Empowering collaborative trading for the Epinnox community 🚀
