#!/usr/bin/env python
"""
Monitor the smart-trader system's performance.
"""

import os
import sys
import yaml
import argparse
import logging
import sqlite3
import time
import json
from datetime import datetime, timedelta
from tabulate import tabulate
import matplotlib.pyplot as plt
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("monitor")


class Monitor:
    """Monitor for the smart-trader system."""
    
    def __init__(self, config_path):
        """Initialize the monitor."""
        self.config_path = config_path
        self.config = self._load_config()
        self.bus_db_path = self.config.get("message_bus", {}).get("path", "data/testnet_bus.db")
        self.feature_db_path = self.config.get("feature_store", {}).get("path", "data/testnet_features.db")
        self.symbols = self.config.get("trading", {}).get("symbols", ["BTC-USDT"])
        
        # Check if databases exist
        self._check_databases()
    
    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            sys.exit(1)
    
    def _check_databases(self):
        """Check if databases exist."""
        if not os.path.exists(self.bus_db_path):
            logger.warning(f"Message bus database not found: {self.bus_db_path}")
        else:
            logger.info(f"Found message bus database: {self.bus_db_path}")
        
        if not os.path.exists(self.feature_db_path):
            logger.warning(f"Feature store database not found: {self.feature_db_path}")
        else:
            logger.info(f"Found feature store database: {self.feature_db_path}")
    
    def get_recent_messages(self, stream, limit=10):
        """Get recent messages from a stream."""
        if not os.path.exists(self.bus_db_path):
            logger.error(f"Message bus database not found: {self.bus_db_path}")
            return []
        
        try:
            conn = sqlite3.connect(self.bus_db_path)
            cursor = conn.cursor()
            
            # Query for recent messages
            cursor.execute(
                "SELECT timestamp, payload FROM messages WHERE stream = ? ORDER BY timestamp DESC LIMIT ?",
                (stream, limit)
            )
            
            messages = []
            for ts, payload in cursor.fetchall():
                try:
                    # Parse payload
                    payload_dict = json.loads(payload)
                    messages.append((ts, payload_dict))
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse payload: {payload}")
            
            conn.close()
            return messages
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            return []
    
    def get_feature(self, symbol, feature_key):
        """Get a feature from the feature store."""
        if not os.path.exists(self.feature_db_path):
            logger.error(f"Feature store database not found: {self.feature_db_path}")
            return None
        
        try:
            conn = sqlite3.connect(self.feature_db_path)
            cursor = conn.cursor()
            
            # Query for feature
            cursor.execute(
                "SELECT value FROM features WHERE symbol = ? AND key = ? ORDER BY timestamp DESC LIMIT 1",
                (symbol, feature_key)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                try:
                    return json.loads(result[0])
                except json.JSONDecodeError:
                    return result[0]
            
            return None
        except Exception as e:
            logger.error(f"Error getting feature: {e}")
            return None
    
    def get_feature_time_series(self, symbol, feature_key, limit=100):
        """Get a feature time series from the feature store."""
        if not os.path.exists(self.feature_db_path):
            logger.error(f"Feature store database not found: {self.feature_db_path}")
            return []
        
        try:
            conn = sqlite3.connect(self.feature_db_path)
            cursor = conn.cursor()
            
            # Query for feature time series
            cursor.execute(
                "SELECT timestamp, value FROM feature_time_series WHERE symbol = ? AND key = ? ORDER BY timestamp DESC LIMIT ?",
                (symbol, feature_key, limit)
            )
            
            series = []
            for ts, value in cursor.fetchall():
                try:
                    # Parse value
                    value_parsed = json.loads(value)
                    series.append((ts, value_parsed))
                except json.JSONDecodeError:
                    try:
                        # Try to convert to float
                        series.append((ts, float(value)))
                    except ValueError:
                        series.append((ts, value))
            
            conn.close()
            return series
        except Exception as e:
            logger.error(f"Error getting feature time series: {e}")
            return []
    
    def show_llm_metrics(self, symbol):
        """Show LLM metrics for a symbol."""
        # Get LLM metrics
        call_count = self.get_feature(symbol, "metrics.llm.call_count") or 0
        valid_calls = self.get_feature(symbol, "metrics.llm.valid_calls") or 0
        invalid_calls = self.get_feature(symbol, "metrics.llm.invalid_calls") or 0
        error_rate = self.get_feature(symbol, "metrics.llm.health.error_rate") or 0.0
        avg_latency = self.get_feature(symbol, "metrics.llm.health.avg_latency") or 0.0
        
        # Get recent latency values
        latency_series = self.get_feature_time_series(symbol, "metrics.llm.latency", limit=10)
        
        # Get recent confidence values
        confidence_series = self.get_feature_time_series(symbol, "metrics.llm.confidence", limit=10)
        
        # Print metrics
        print(f"\n=== LLM Metrics for {symbol} ===")
        print(f"Call count: {call_count}")
        print(f"Valid calls: {valid_calls}")
        print(f"Invalid calls: {invalid_calls}")
        print(f"Error rate: {error_rate:.2%}")
        print(f"Average latency: {avg_latency:.2f}s")
        
        # Print recent latency values
        if latency_series:
            print("\nRecent latency values:")
            for ts, value in latency_series:
                dt = datetime.fromtimestamp(ts)
                print(f"  {dt.strftime('%Y-%m-%d %H:%M:%S')}: {value:.2f}s")
        
        # Print recent confidence values
        if confidence_series:
            print("\nRecent confidence values:")
            for ts, value in confidence_series:
                dt = datetime.fromtimestamp(ts)
                print(f"  {dt.strftime('%Y-%m-%d %H:%M:%S')}: {value:.2f}")
    
    def show_recent_signals(self, symbol, limit=5):
        """Show recent signals for a symbol."""
        # Get recent fused signals
        fused_signals = self.get_recent_messages(f"signals.fused", limit=limit)
        
        # Get recent LLM signals
        llm_signals = self.get_recent_messages(f"signals.llm", limit=limit)
        
        # Print fused signals
        print(f"\n=== Recent Fused Signals for {symbol} ===")
        if fused_signals:
            rows = []
            for ts, signal in fused_signals:
                if signal.get("symbol") == symbol:
                    dt = datetime.fromtimestamp(ts)
                    rows.append([
                        dt.strftime("%Y-%m-%d %H:%M:%S"),
                        signal.get("decision", "UNKNOWN"),
                        f"{signal.get('score', 0.0):+.2f}",
                        f"{signal.get('confidence', 0.0):.2f}"
                    ])
            
            if rows:
                print(tabulate(rows, headers=["Timestamp", "Decision", "Score", "Confidence"]))
            else:
                print("No fused signals found")
        else:
            print("No fused signals found")
        
        # Print LLM signals
        print(f"\n=== Recent LLM Signals for {symbol} ===")
        if llm_signals:
            rows = []
            for ts, signal in llm_signals:
                if signal.get("symbol") == symbol:
                    dt = datetime.fromtimestamp(ts)
                    rows.append([
                        dt.strftime("%Y-%m-%d %H:%M:%S"),
                        signal.get("action", "UNKNOWN"),
                        f"{signal.get('confidence', 0.0):.2f}",
                        signal.get("rationale", "")[:50] + "..." if len(signal.get("rationale", "")) > 50 else signal.get("rationale", "")
                    ])
            
            if rows:
                print(tabulate(rows, headers=["Timestamp", "Action", "Confidence", "Rationale"]))
            else:
                print("No LLM signals found")
        else:
            print("No LLM signals found")
    
    def show_recent_trades(self, symbol, limit=5):
        """Show recent trades for a symbol."""
        # Get recent trades
        trades = self.get_recent_messages(f"trades.executed", limit=limit)
        
        # Print trades
        print(f"\n=== Recent Trades for {symbol} ===")
        if trades:
            rows = []
            for ts, trade in trades:
                if trade.get("symbol") == symbol:
                    dt = datetime.fromtimestamp(ts)
                    rows.append([
                        dt.strftime("%Y-%m-%d %H:%M:%S"),
                        trade.get("side", "UNKNOWN"),
                        f"{trade.get('quantity', 0.0):.4f}",
                        f"{trade.get('price', 0.0):.2f}",
                        f"{trade.get('pnl', 0.0):+.2f}",
                        f"{trade.get('pnl_pct', 0.0):+.2f}%",
                        trade.get("source", "UNKNOWN")
                    ])
            
            if rows:
                print(tabulate(rows, headers=["Timestamp", "Side", "Quantity", "Price", "PnL", "PnL %", "Source"]))
            else:
                print("No trades found")
        else:
            print("No trades found")
    
    def show_account_state(self):
        """Show account state."""
        # Get account state
        account_state = self.get_recent_messages(f"account.state", limit=1)
        
        # Get positions state
        positions_state = self.get_recent_messages(f"positions.state", limit=1)
        
        # Get orders state
        orders_state = self.get_recent_messages(f"orders.state", limit=1)
        
        # Print account state
        print("\n=== Account State ===")
        if account_state:
            ts, state = account_state[0]
            dt = datetime.fromtimestamp(ts)
            print(f"Timestamp: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Total balance: ${state.get('total', 0.0):.2f}")
            print(f"Available balance: ${state.get('available', 0.0):.2f}")
            print(f"Reserved balance: ${state.get('reserved', 0.0):.2f}")
        else:
            print("No account state found")
        
        # Print positions
        print("\n=== Open Positions ===")
        if positions_state:
            ts, state = positions_state[0]
            positions = state.get("positions", [])
            
            if positions:
                rows = []
                for position in positions:
                    symbol = position.get("symbol", "UNKNOWN")
                    side = position.get("side", "UNKNOWN")
                    size = position.get("size", 0.0)
                    entry_price = position.get("entry_price", 0.0)
                    mark_price = position.get("mark_price", 0.0)
                    
                    # Calculate P&L percentage
                    if entry_price > 0:
                        if side.upper() == "LONG":
                            pnl_pct = (mark_price - entry_price) / entry_price * 100
                        else:
                            pnl_pct = (entry_price - mark_price) / entry_price * 100
                    else:
                        pnl_pct = 0.0
                    
                    rows.append([
                        symbol,
                        side,
                        f"{size:.4f}",
                        f"{entry_price:.2f}",
                        f"{mark_price:.2f}",
                        f"{pnl_pct:+.2f}%"
                    ])
                
                print(tabulate(rows, headers=["Symbol", "Side", "Size", "Entry Price", "Mark Price", "P&L %"]))
            else:
                print("No open positions")
        else:
            print("No positions state found")
        
        # Print orders
        print("\n=== Open Orders ===")
        if orders_state:
            ts, state = orders_state[0]
            orders = state.get("orders", [])
            
            if orders:
                rows = []
                for order in orders:
                    symbol = order.get("symbol", "UNKNOWN")
                    side = order.get("side", "UNKNOWN")
                    quantity = order.get("quantity", 0.0)
                    price = order.get("price", "MKT")
                    status = order.get("status", "UNKNOWN")
                    
                    price_str = f"{price:.2f}" if isinstance(price, (int, float)) else price
                    
                    rows.append([
                        symbol,
                        side,
                        f"{quantity:.4f}",
                        price_str,
                        status
                    ])
                
                print(tabulate(rows, headers=["Symbol", "Side", "Quantity", "Price", "Status"]))
            else:
                print("No open orders")
        else:
            print("No orders state found")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Monitor the smart-trader system")
    parser.add_argument("--config", default="config_testnet.yaml", help="Path to configuration file")
    parser.add_argument("--symbol", default="BTC-USDT", help="Symbol to monitor")
    args = parser.parse_args()
    
    # Create monitor
    monitor = Monitor(args.config)
    
    # Show metrics
    monitor.show_llm_metrics(args.symbol)
    
    # Show recent signals
    monitor.show_recent_signals(args.symbol)
    
    # Show recent trades
    monitor.show_recent_trades(args.symbol)
    
    # Show account state
    monitor.show_account_state()


if __name__ == "__main__":
    main()
