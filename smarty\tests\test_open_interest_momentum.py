"""
Tests for the Open Interest Momentum model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.open_interest_momentum import OpenInterestMomentumModel, OISignal
from core.feature_store import feature_store


class TestOpenInterestMomentumModel(unittest.TestCase):
    """Test the Open Interest Momentum model."""

    def setUp(self):
        """Set up test environment."""
        self.model = OpenInterestMomentumModel(
            delta_window=5,
            z_score_window=20,
            threshold_z=1.0,
            mode="contrarian"
        )

    def test_delta_calculation(self):
        """Test open interest delta calculation."""
        # Create test open interest data
        symbol = "BTC-USDT"
        now = datetime.now()

        # Add open interest to cache
        self.model._oi_cache[symbol] = []
        for i in range(10):
            # Open interest increases by 100 each step
            oi = 10000 + (i * 100)
            time = now - timedelta(minutes=10-i)
            self.model._oi_cache[symbol].append((time, oi))

        # Calculate delta
        delta = self.model._calculate_delta(symbol)

        # Delta should be the difference between latest and 5 minutes ago
        expected_delta = 500  # 5 steps * 100
        self.assertEqual(delta, expected_delta)

    def test_z_score_calculation(self):
        """Test z-score calculation."""
        # Create test delta data
        symbol = "BTC-USDT"
        now = datetime.now()

        # Add deltas to cache
        self.model._delta_cache[symbol] = []
        for i in range(10):
            # Constant delta of 100
            delta = 100
            time = now - timedelta(minutes=10-i)
            self.model._delta_cache[symbol].append((time, delta))

        # Add an outlier delta
        self.model._delta_cache[symbol].append((now, 500))  # 5x the normal delta

        # Calculate z-score
        z_score = self.model._calculate_z_score(symbol)

        # Z-score should be positive (outlier is higher than mean)
        self.assertGreater(z_score, 0)

    def test_action_from_signal_contrarian(self):
        """Test action determination with contrarian approach."""
        # Test increasing open interest
        action, confidence = self.model._get_action_from_signal(
            OISignal.INCREASING, 2.0
        )
        self.assertEqual(action, "SELL")  # Contrarian: sell when OI increases
        self.assertGreater(confidence, 0)

        # Test decreasing open interest
        action, confidence = self.model._get_action_from_signal(
            OISignal.DECREASING, -2.0
        )
        self.assertEqual(action, "BUY")  # Contrarian: buy when OI decreases
        self.assertGreater(confidence, 0)

        # Test neutral
        action, confidence = self.model._get_action_from_signal(
            OISignal.NEUTRAL, 0.0
        )
        self.assertEqual(action, "HOLD")
        self.assertEqual(confidence, 0.0)

    def test_action_from_signal_trend(self):
        """Test action determination with trend-following approach."""
        # Create a trend-following model
        trend_model = OpenInterestMomentumModel(mode="trend")

        # Test increasing open interest
        action, confidence = trend_model._get_action_from_signal(
            OISignal.INCREASING, 2.0
        )
        self.assertEqual(action, "BUY")  # Trend: buy when OI increases
        self.assertGreater(confidence, 0)

        # Test decreasing open interest
        action, confidence = trend_model._get_action_from_signal(
            OISignal.DECREASING, -2.0
        )
        self.assertEqual(action, "SELL")  # Trend: sell when OI decreases
        self.assertGreater(confidence, 0)

    def test_weight_calculation(self):
        """Test weight calculation."""
        # Test base weight
        features = {}
        weight = self.model._calculate_weight(features)
        self.assertEqual(weight, self.model.base_weight)

        # Test weight boost in low volatility
        features = {"volatility_regime_prediction": {"regime": "LOW"}}
        weight = self.model._calculate_weight(features)
        self.assertEqual(weight, self.model.base_weight * 2.0)

        # Test weight boost in very low volatility
        features = {"volatility_regime_prediction": {"regime": "VERY_LOW"}}
        weight = self.model._calculate_weight(features)
        self.assertEqual(weight, self.model.base_weight * 2.0)

        # Test no weight boost in normal volatility
        features = {"volatility_regime_prediction": {"regime": "NORMAL"}}
        weight = self.model._calculate_weight(features)
        self.assertEqual(weight, self.model.base_weight)

    def test_predict_with_synthetic_data(self):
        """Test predict method with synthetic data."""
        async def test_predict_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic open interest data with a steady increase
            for i in range(30):
                # Open interest increases by 100 each step
                oi = 10000 + (i * 100)
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "open_interest", oi)
                await feature_store.add_time_series(symbol, "open_interest_history", oi, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # Verify prediction structure
                    self.assertIn("open_interest", prediction)
                    self.assertIn("open_interest_delta", prediction)
                    self.assertIn("open_interest_delta_z", prediction)
                    self.assertIn("signal", prediction)
                    self.assertIn("action", prediction)
                    self.assertIn("confidence", prediction)
                    self.assertIn("weight", prediction)

                    # With steadily increasing open interest, we should get INCREASING
                    self.assertEqual(prediction["signal"], OISignal.INCREASING.value)

                    # With contrarian approach, we should get SELL
                    self.assertEqual(prediction["action"], "SELL")

                    # Verify confidence is between 0 and 1
                    self.assertGreaterEqual(prediction["confidence"], 0.0)
                    self.assertLessEqual(prediction["confidence"], 1.0)

        # Run async test
        asyncio.run(test_predict_async())

    def test_predict_with_flat_data(self):
        """Test predict method with flat open interest data."""
        async def test_flat_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic open interest data with a constant value
            for i in range(30):
                # Constant open interest
                oi = 10000
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "open_interest", oi)
                await feature_store.add_time_series(symbol, "open_interest_history", oi, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # With flat open interest, we should get NEUTRAL
                    self.assertEqual(prediction["signal"], OISignal.NEUTRAL.value)

                    # With neutral signal, we should get HOLD
                    self.assertEqual(prediction["action"], "HOLD")

                    # Verify delta is close to zero
                    self.assertAlmostEqual(prediction["open_interest_delta"], 0.0, places=6)

        # Run async test
        asyncio.run(test_flat_data_async())

    def test_predict_with_decreasing_data(self):
        """Test predict method with decreasing open interest data."""
        async def test_decreasing_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"
            now = datetime.now()

            # Create synthetic open interest data with a steady decrease
            for i in range(30):
                # Open interest decreases by 100 each step
                oi = 13000 - (i * 100)
                time = now - timedelta(minutes=30-i)

                # Store in feature store
                await feature_store.set(symbol, "open_interest", oi)
                await feature_store.add_time_series(symbol, "open_interest_history", oi, time)

                # Call predict
                features = {
                    "symbol": symbol,
                    "timestamp": time
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == 29:
                    # With steadily decreasing open interest, we should get DECREASING
                    self.assertEqual(prediction["signal"], OISignal.DECREASING.value)

                    # With contrarian approach, we should get BUY
                    self.assertEqual(prediction["action"], "BUY")

                    # Verify delta is negative
                    self.assertLess(prediction["open_interest_delta"], 0)

        # Run async test
        asyncio.run(test_decreasing_data_async())

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Clear feature store
            await feature_store.clear()

            symbol = "BTC-USDT"

            # Call predict without any data in feature store
            features = {
                "symbol": symbol,
                "timestamp": datetime.now()
            }

            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["signal"], OISignal.NEUTRAL.value)
            self.assertEqual(prediction["action"], "HOLD")
            self.assertEqual(prediction["confidence"], 0.0)

        # Run async test
        asyncio.run(test_insufficient_data_async())


if __name__ == "__main__":
    unittest.main()
