"""
Tests for the SQLiteBus implementation.
"""

import os
import time
import asyncio
import unittest
import tempfile
from datetime import datetime
from typing import List, Dict, Any

from pipeline.databus import SQLiteBus, InMemoryBus


class TestSQLiteBus(unittest.TestCase):
    """Test cases for the SQLiteBus implementation."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary database file
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = os.path.join(self.temp_dir.name, "test_bus.db")
        
        # Create bus instance
        self.bus = SQLiteBus(path=self.db_path, poll_interval=0.1)
    
    def tearDown(self):
        """Clean up test environment."""
        # Close bus
        self.bus.close()
        
        # Clean up temporary directory
        self.temp_dir.cleanup()
    
    def test_publish_subscribe(self):
        """Test basic publish/subscribe functionality."""
        # Create a list to store received messages
        received_messages = []
        
        # Define callback function
        def callback(ts, payload):
            received_messages.append((ts, payload))
        
        # Subscribe to a stream
        self.bus.subscribe("test_stream", callback)
        
        # Publish a message
        test_ts = time.time()
        test_payload = {"key": "value", "number": 42}
        self.bus.publish("test_stream", test_ts, test_payload)
        
        # Wait for message to be processed
        time.sleep(0.2)
        
        # Check that message was received
        self.assertEqual(len(received_messages), 1)
        self.assertEqual(received_messages[0][0], test_ts)
        self.assertEqual(received_messages[0][1], test_payload)
    
    def test_multiple_subscribers(self):
        """Test multiple subscribers to the same stream."""
        # Create lists to store received messages
        received_messages1 = []
        received_messages2 = []
        
        # Define callback functions
        def callback1(ts, payload):
            received_messages1.append((ts, payload))
        
        def callback2(ts, payload):
            received_messages2.append((ts, payload))
        
        # Subscribe to a stream
        self.bus.subscribe("test_stream", callback1)
        self.bus.subscribe("test_stream", callback2)
        
        # Publish a message
        test_ts = time.time()
        test_payload = {"key": "value", "number": 42}
        self.bus.publish("test_stream", test_ts, test_payload)
        
        # Wait for message to be processed
        time.sleep(0.2)
        
        # Check that message was received by both subscribers
        self.assertEqual(len(received_messages1), 1)
        self.assertEqual(len(received_messages2), 1)
        self.assertEqual(received_messages1[0][0], test_ts)
        self.assertEqual(received_messages2[0][0], test_ts)
        self.assertEqual(received_messages1[0][1], test_payload)
        self.assertEqual(received_messages2[0][1], test_payload)
    
    def test_multiple_streams(self):
        """Test publishing to multiple streams."""
        # Create lists to store received messages
        received_messages1 = []
        received_messages2 = []
        
        # Define callback functions
        def callback1(ts, payload):
            received_messages1.append((ts, payload))
        
        def callback2(ts, payload):
            received_messages2.append((ts, payload))
        
        # Subscribe to different streams
        self.bus.subscribe("stream1", callback1)
        self.bus.subscribe("stream2", callback2)
        
        # Publish messages to different streams
        test_ts1 = time.time()
        test_payload1 = {"stream": 1, "value": "first"}
        self.bus.publish("stream1", test_ts1, test_payload1)
        
        test_ts2 = time.time()
        test_payload2 = {"stream": 2, "value": "second"}
        self.bus.publish("stream2", test_ts2, test_payload2)
        
        # Wait for messages to be processed
        time.sleep(0.2)
        
        # Check that messages were received by the correct subscribers
        self.assertEqual(len(received_messages1), 1)
        self.assertEqual(len(received_messages2), 1)
        self.assertEqual(received_messages1[0][0], test_ts1)
        self.assertEqual(received_messages2[0][0], test_ts2)
        self.assertEqual(received_messages1[0][1], test_payload1)
        self.assertEqual(received_messages2[0][1], test_payload2)
    
    def test_unsubscribe(self):
        """Test unsubscribing from a stream."""
        # Create a list to store received messages
        received_messages = []
        
        # Define callback function
        def callback(ts, payload):
            received_messages.append((ts, payload))
        
        # Subscribe to a stream
        self.bus.subscribe("test_stream", callback)
        
        # Publish a message
        test_ts1 = time.time()
        test_payload1 = {"message": 1}
        self.bus.publish("test_stream", test_ts1, test_payload1)
        
        # Wait for message to be processed
        time.sleep(0.2)
        
        # Check that message was received
        self.assertEqual(len(received_messages), 1)
        
        # Unsubscribe
        self.bus.unsubscribe("test_stream", callback)
        
        # Publish another message
        test_ts2 = time.time()
        test_payload2 = {"message": 2}
        self.bus.publish("test_stream", test_ts2, test_payload2)
        
        # Wait for message to be processed
        time.sleep(0.2)
        
        # Check that second message was not received
        self.assertEqual(len(received_messages), 1)
    
    def test_cleanup(self):
        """Test cleaning up old messages."""
        # Publish some messages
        for i in range(10):
            self.bus.publish("test_stream", time.time() - (i * 86400), {"index": i})
        
        # Check message count
        self.assertEqual(self.bus.get_message_count(), 10)
        
        # Clean up messages older than 5 days
        deleted = self.bus.cleanup_old_messages(max_age_days=5.0)
        
        # Check that 5 messages were deleted
        self.assertEqual(deleted, 5)
        
        # Check message count
        self.assertEqual(self.bus.get_message_count(), 5)
    
    def test_in_memory_bus(self):
        """Test the InMemoryBus implementation."""
        # Create bus instance
        bus = InMemoryBus()
        
        # Create a list to store received messages
        received_messages = []
        
        # Define callback function
        def callback(ts, payload):
            received_messages.append((ts, payload))
        
        # Subscribe to a stream
        bus.subscribe("test_stream", callback)
        
        # Publish a message
        test_ts = time.time()
        test_payload = {"key": "value", "number": 42}
        bus.publish("test_stream", test_ts, test_payload)
        
        # Check that message was received immediately
        self.assertEqual(len(received_messages), 1)
        self.assertEqual(received_messages[0][0], test_ts)
        self.assertEqual(received_messages[0][1], test_payload)
        
        # Close bus
        bus.close()


if __name__ == "__main__":
    unittest.main()
