#!/usr/bin/env python3
"""
Real-Time Market Data Service for Smart-Trader Control Center

Provides live market data feeds, price alerts, and market analysis.
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class MarketTick:
    """Market tick data structure."""
    symbol: str
    price: float
    volume: float
    timestamp: str
    bid: float
    ask: float
    high_24h: float
    low_24h: float
    change_24h: float
    change_percent_24h: float


@dataclass
class MarketAlert:
    """Market alert configuration."""
    id: str
    symbol: str
    condition: str  # 'above', 'below', 'change_percent'
    value: float
    active: bool
    created_at: str
    triggered_at: Optional[str] = None


class MarketDataService:
    """Real-time market data service."""
    
    def __init__(self):
        """Initialize market data service."""
        self.subscribers: List[Callable] = []
        self.market_data: Dict[str, MarketTick] = {}
        self.alerts: Dict[str, MarketAlert] = {}
        self.running = False
        
        # Supported symbols
        self.symbols = [
            "BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT",
            "LTCUSDT", "BCHUSDT", "XLMUSDT", "EOSUSDT", "TRXUSDT"
        ]
        
        # Initialize with mock data
        self._initialize_mock_data()

    def _initialize_mock_data(self) -> None:
        """Initialize with mock market data."""
        base_prices = {
            "BTCUSDT": 43500.0,
            "ETHUSDT": 2650.0,
            "ADAUSDT": 0.45,
            "DOTUSDT": 7.25,
            "LINKUSDT": 14.80,
            "LTCUSDT": 72.50,
            "BCHUSDT": 245.0,
            "XLMUSDT": 0.12,
            "EOSUSDT": 0.85,
            "TRXUSDT": 0.105
        }
        
        for symbol, base_price in base_prices.items():
            # Generate realistic market data
            price = base_price * (1 + random.uniform(-0.02, 0.02))
            volume = random.uniform(1000000, 10000000)
            spread = price * 0.001  # 0.1% spread
            
            change_24h = random.uniform(-0.08, 0.08)  # ±8% daily change
            
            self.market_data[symbol] = MarketTick(
                symbol=symbol,
                price=price,
                volume=volume,
                timestamp=datetime.now().isoformat(),
                bid=price - spread/2,
                ask=price + spread/2,
                high_24h=price * (1 + abs(change_24h) * 0.7),
                low_24h=price * (1 - abs(change_24h) * 0.7),
                change_24h=price * change_24h,
                change_percent_24h=change_24h * 100
            )

    async def start(self) -> None:
        """Start the market data service."""
        if self.running:
            return
        
        self.running = True
        logger.info("Starting market data service...")
        
        # Start data update loop
        asyncio.create_task(self._update_loop())
        
        logger.info(f"Market data service started for {len(self.symbols)} symbols")

    async def stop(self) -> None:
        """Stop the market data service."""
        self.running = False
        logger.info("Market data service stopped")

    async def _update_loop(self) -> None:
        """Main update loop for market data."""
        while self.running:
            try:
                # Update all symbols
                for symbol in self.symbols:
                    await self._update_symbol_data(symbol)
                
                # Check alerts
                await self._check_alerts()
                
                # Notify subscribers
                await self._notify_subscribers()
                
                # Wait before next update
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                logger.error(f"Error in market data update loop: {e}")
                await asyncio.sleep(5)

    async def _update_symbol_data(self, symbol: str) -> None:
        """Update data for a specific symbol."""
        if symbol not in self.market_data:
            return
        
        current_tick = self.market_data[symbol]
        
        # Simulate realistic price movement
        price_change = random.uniform(-0.005, 0.005)  # ±0.5% per update
        new_price = current_tick.price * (1 + price_change)
        
        # Ensure price doesn't go negative
        new_price = max(new_price, current_tick.price * 0.5)
        
        # Update volume
        volume_change = random.uniform(-0.1, 0.1)
        new_volume = current_tick.volume * (1 + volume_change)
        
        # Calculate spread
        spread = new_price * 0.001
        
        # Update 24h stats (simplified)
        new_high = max(current_tick.high_24h, new_price)
        new_low = min(current_tick.low_24h, new_price)
        
        # Calculate 24h change (mock)
        change_24h = new_price - (current_tick.price - current_tick.change_24h)
        change_percent_24h = (change_24h / (new_price - change_24h)) * 100
        
        # Update the tick
        self.market_data[symbol] = MarketTick(
            symbol=symbol,
            price=new_price,
            volume=new_volume,
            timestamp=datetime.now().isoformat(),
            bid=new_price - spread/2,
            ask=new_price + spread/2,
            high_24h=new_high,
            low_24h=new_low,
            change_24h=change_24h,
            change_percent_24h=change_percent_24h
        )

    async def _check_alerts(self) -> None:
        """Check and trigger market alerts."""
        for alert_id, alert in self.alerts.items():
            if not alert.active or alert.triggered_at:
                continue
            
            if alert.symbol not in self.market_data:
                continue
            
            current_price = self.market_data[alert.symbol].price
            triggered = False
            
            if alert.condition == "above" and current_price > alert.value:
                triggered = True
            elif alert.condition == "below" and current_price < alert.value:
                triggered = True
            elif alert.condition == "change_percent":
                change_percent = self.market_data[alert.symbol].change_percent_24h
                if abs(change_percent) > alert.value:
                    triggered = True
            
            if triggered:
                alert.triggered_at = datetime.now().isoformat()
                logger.info(f"Alert triggered: {alert.symbol} {alert.condition} {alert.value}")
                
                # Notify subscribers about alert
                await self._notify_alert(alert)

    async def _notify_subscribers(self) -> None:
        """Notify all subscribers of market data updates."""
        if not self.subscribers:
            return
        
        market_update = {
            "type": "market_data",
            "timestamp": datetime.now().isoformat(),
            "data": {symbol: asdict(tick) for symbol, tick in self.market_data.items()}
        }
        
        for callback in self.subscribers:
            try:
                await callback(market_update)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")

    async def _notify_alert(self, alert: MarketAlert) -> None:
        """Notify subscribers about triggered alert."""
        alert_notification = {
            "type": "market_alert",
            "timestamp": datetime.now().isoformat(),
            "alert": asdict(alert),
            "current_price": self.market_data[alert.symbol].price
        }
        
        for callback in self.subscribers:
            try:
                await callback(alert_notification)
            except Exception as e:
                logger.error(f"Error notifying alert: {e}")

    def subscribe(self, callback: Callable) -> None:
        """Subscribe to market data updates."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback: Callable) -> None:
        """Unsubscribe from market data updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def get_market_data(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get current market data."""
        if symbol:
            return asdict(self.market_data.get(symbol)) if symbol in self.market_data else {}
        return {symbol: asdict(tick) for symbol, tick in self.market_data.items()}

    def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary statistics."""
        if not self.market_data:
            return {}
        
        total_volume = sum(tick.volume for tick in self.market_data.values())
        gainers = [tick for tick in self.market_data.values() if tick.change_percent_24h > 0]
        losers = [tick for tick in self.market_data.values() if tick.change_percent_24h < 0]
        
        top_gainer = max(self.market_data.values(), key=lambda x: x.change_percent_24h)
        top_loser = min(self.market_data.values(), key=lambda x: x.change_percent_24h)
        
        return {
            "total_symbols": len(self.market_data),
            "total_volume_24h": total_volume,
            "gainers_count": len(gainers),
            "losers_count": len(losers),
            "top_gainer": {
                "symbol": top_gainer.symbol,
                "change_percent": top_gainer.change_percent_24h
            },
            "top_loser": {
                "symbol": top_loser.symbol,
                "change_percent": top_loser.change_percent_24h
            },
            "timestamp": datetime.now().isoformat()
        }

    def create_alert(self, symbol: str, condition: str, value: float) -> str:
        """Create a new market alert."""
        alert_id = f"alert_{int(time.time())}_{random.randint(1000, 9999)}"
        
        alert = MarketAlert(
            id=alert_id,
            symbol=symbol,
            condition=condition,
            value=value,
            active=True,
            created_at=datetime.now().isoformat()
        )
        
        self.alerts[alert_id] = alert
        logger.info(f"Created alert: {symbol} {condition} {value}")
        
        return alert_id

    def get_alerts(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all alerts."""
        alerts = list(self.alerts.values())
        if active_only:
            alerts = [alert for alert in alerts if alert.active]
        
        return [asdict(alert) for alert in alerts]

    def delete_alert(self, alert_id: str) -> bool:
        """Delete an alert."""
        if alert_id in self.alerts:
            del self.alerts[alert_id]
            logger.info(f"Deleted alert: {alert_id}")
            return True
        return False


# Global market data service instance
market_service = MarketDataService()


async def start_market_service() -> None:
    """Start the global market service."""
    await market_service.start()


async def stop_market_service() -> None:
    """Stop the global market service."""
    await market_service.stop()


def get_market_data(symbol: Optional[str] = None) -> Dict[str, Any]:
    """Get current market data."""
    return market_service.get_market_data(symbol)


def get_market_summary() -> Dict[str, Any]:
    """Get market summary."""
    return market_service.get_market_summary()


def subscribe_to_market_data(callback: Callable) -> None:
    """Subscribe to market data updates."""
    market_service.subscribe(callback)


def create_market_alert(symbol: str, condition: str, value: float) -> str:
    """Create a market alert."""
    return market_service.create_alert(symbol, condition, value)
