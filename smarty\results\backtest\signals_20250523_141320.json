[{"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-23T08:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-23T11:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-25T22:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-26T13:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-26T20:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-27T04:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-27T17:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-27T19:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-02-28T06:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-05T03:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-08T07:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-08T15:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-09T02:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-09T07:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-10T15:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-10T23:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-11T02:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-12T02:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-13T09:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-13T23:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-14T06:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-17T13:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-17T21:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-19T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-19T21:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-20T02:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-03-20T23:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-21T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-30T13:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-03-30T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-01T21:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-02T00:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-02T16:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-03T16:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-04T11:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-04T16:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-05T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-07T23:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-08T14:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-08T21:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-09T00:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-09T03:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-11T00:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-11T07:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-15T12:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-16T19:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-18T01:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-18T08:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-18T10:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-18T14:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-20T22:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-22T18:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-23T15:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-04-25T00:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-25T19:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-27T01:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-27T14:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-27T17:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-04-30T16:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-02T00:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-02T08:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-03T06:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-03T12:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-04T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-06T06:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-07T03:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-07T23:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-08T13:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-09T01:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-09T03:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-09T15:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-09T17:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-10T08:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-13T18:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-16T04:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-16T12:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-16T16:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "BUY", "score": 0.65, "timestamp": "2025-05-17T15:00:00", "source": "rsi", "rationale": "RSI crossed above oversold level (30.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-19T20:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-20T08:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-20T15:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-20T19:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-20T21:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-21T00:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}, {"symbol": "BTC-USDT", "action": "SELL", "score": 0.65, "timestamp": "2025-05-21T16:00:00", "source": "rsi", "rationale": "RSI crossed below overbought level (70.0)"}]