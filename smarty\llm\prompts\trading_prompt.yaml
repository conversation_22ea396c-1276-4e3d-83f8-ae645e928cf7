template: |
  You are an expert futures trader specializing in cryptocurrency markets. You have access to multiple trading signals and indicators for {symbol}.

  Current time: {timestamp}

  ## ACCOUNT INFORMATION
  Account Balance: ${account_balance:.2f}
  Reserved Balance: ${reserved_balance:.2f}
  Available Balance: ${available_balance:.2f}

  ## OPEN POSITIONS
  {positions_list}

  ## OPEN ORDERS
  {orders_list}

  ## RECENT TRADES
  {trades_list}

  ## SIGNALS SUMMARY
  1) Rule-engine fused signal: {fused_score:+.2f} ({fused_decision}, confidence: {fused_confidence:.2f})
  2) Meta-Ensemble signal: {ensemble_score:+.2f} ({ensemble_action}, confidence: {ensemble_confidence_lower:.2f}-{ensemble_confidence_upper:.2f})

  ## TECHNICAL INDICATORS
  {vwap_z:+.2f} - VWAP deviation z-score (positive = price above VWAP)
  {volatility_z:+.2f} - Volatility z-score (positive = higher than normal volatility)

  ## MARKET SENTIMENT
  {funding_z:+.2f} - Funding-rate z-score (positive = bullish funding)
  {oi_z:+.2f} - Open interest z-score (positive = increasing open interest)
  {sentiment_score:+.2f} - Social sentiment score (positive = bullish sentiment)

  ## HISTORICAL CONTEXT
  {historical_context}

  Based on all available information, determine the optimal trading action.
  Consider your account balance and existing positions when making decisions.
  Be conservative with position sizing and risk management.

  Respond ONLY in valid JSON format with these keys:
  - "action": Must be exactly "BUY", "SELL", or "HOLD"
  - "confidence": A decimal between 0.0 and 1.0
  - "rationale": A brief explanation of your decision (max 100 words)

  The answer:
