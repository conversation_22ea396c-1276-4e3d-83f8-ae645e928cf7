#!/usr/bin/env python3
"""
🔥 Strategy Startup Test

Simple test to verify that each strategy can start without immediate crashes.
This bypasses encoding issues and focuses on actual functionality.
"""

import subprocess
import time
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_strategy_startup(name: str, command: str, timeout: int = 5) -> bool:
    """Test if a strategy can start without immediate failure."""
    logger.info(f"🚀 Testing {name}...")
    logger.info(f"   Command: {command}")
    
    try:
        # Start the process
        proc = subprocess.Popen(
            command.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for a few seconds
        time.sleep(timeout)
        
        # Check if still running
        if proc.poll() is None:
            # Still running - success!
            logger.info(f"✅ {name}: Started successfully (PID: {proc.pid})")
            
            # Clean shutdown
            proc.terminate()
            try:
                proc.wait(timeout=10)
                logger.info(f"✅ {name}: Stopped cleanly")
            except subprocess.TimeoutExpired:
                proc.kill()
                logger.warning(f"⚠️ {name}: Force killed")
            
            return True
        else:
            # Process died
            stdout, stderr = proc.communicate()
            logger.error(f"❌ {name}: Failed to start (exit code: {proc.returncode})")
            if stderr:
                logger.error(f"   Error: {stderr[:200]}...")
            return False
            
    except Exception as e:
        logger.error(f"❌ {name}: Exception during test: {e}")
        return False

def main():
    """Test all strategies."""
    logger.info("🔥 Strategy Startup Test")
    logger.info("=" * 50)
    
    strategies = {
        "Smart Model Integrated": "python orchestrator.py --debug",
        "Smart Strategy Only": "python run_smart_strategy_live.py", 
        "Order Flow": "python live_dataframe_strategy_runner.py"
    }
    
    results = {}
    passed = 0
    
    for name, command in strategies.items():
        success = test_strategy_startup(name, command)
        results[name] = success
        if success:
            passed += 1
        logger.info("")
    
    # Summary
    logger.info("📊 Test Results:")
    logger.info(f"   Passed: {passed}/{len(strategies)}")
    
    for name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"   {name}: {status}")
    
    if passed == len(strategies):
        logger.info("🎉 ALL STRATEGIES CAN START SUCCESSFULLY!")
        return 0
    else:
        logger.error("🚨 SOME STRATEGIES FAILED TO START!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
