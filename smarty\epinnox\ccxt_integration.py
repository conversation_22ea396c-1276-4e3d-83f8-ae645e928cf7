"""
CCXT Integration for Epinnox Trading System

This module provides a unified interface for interacting with multiple exchanges
using the CCXT library, enhanced for the Epinnox smart strategy.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import ccxt.pro as ccxt

from .core.events import Kline, Trade, ExchangeInfo, MarketInfo
from .core.feature_store import feature_store
from .core.utils import retry_async, normalize_symbol

logger = logging.getLogger(__name__)


class CCXTManager:
    """
    CCXT Manager for multi-exchange integration.
    
    Provides unified interface for:
    - Market data streaming
    - Order execution
    - Account management
    - Exchange information
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize CCXT Manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.exchange_configs = config.get("exchanges", {})
        self.enabled_exchanges = self.exchange_configs.get("enabled", ["binance"])
        self.default_exchange = self.exchange_configs.get("default", "binance")
        
        # Market data subscriptions
        self.subscriptions: Dict[str, List[str]] = {}
        self.running = False
        
    async def initialize(self) -> None:
        """Initialize all enabled exchanges."""
        logger.info("Initializing CCXT exchanges...")
        
        for exchange_id in self.enabled_exchanges:
            try:
                await self._initialize_exchange(exchange_id)
                logger.info(f"✅ Initialized {exchange_id}")
            except Exception as e:
                logger.error(f"❌ Failed to initialize {exchange_id}: {e}")
    
    async def _initialize_exchange(self, exchange_id: str) -> None:
        """
        Initialize a single exchange.
        
        Args:
            exchange_id: Exchange identifier (e.g., 'binance', 'bybit')
        """
        exchange_config = self.exchange_configs.get(exchange_id, {})
        
        # Create exchange instance
        exchange_class = getattr(ccxt, exchange_id)
        exchange = exchange_class({
            'apiKey': exchange_config.get('api_key', ''),
            'secret': exchange_config.get('api_secret', ''),
            'password': exchange_config.get('passphrase', ''),  # For OKX
            'sandbox': exchange_config.get('sandbox', True),
            'enableRateLimit': True,
            'rateLimit': exchange_config.get('rate_limit', 1200),
        })
        
        # Test connection
        await exchange.load_markets()
        
        self.exchanges[exchange_id] = exchange
        self.subscriptions[exchange_id] = []
        
        logger.info(f"Loaded {len(exchange.markets)} markets for {exchange_id}")
    
    async def start_market_data(self, symbols: List[str]) -> None:
        """
        Start market data streaming for specified symbols.
        
        Args:
            symbols: List of symbols to stream (e.g., ['BTC/USDT', 'ETH/USDT'])
        """
        logger.info(f"Starting market data for symbols: {symbols}")
        
        self.running = True
        
        # Start streaming for each exchange
        tasks = []
        for exchange_id, exchange in self.exchanges.items():
            for symbol in symbols:
                # Normalize symbol for this exchange
                normalized_symbol = self._normalize_symbol_for_exchange(symbol, exchange_id)
                
                if normalized_symbol in exchange.markets:
                    # Start OHLCV streaming
                    tasks.append(self._stream_ohlcv(exchange_id, normalized_symbol))
                    
                    # Start trades streaming
                    tasks.append(self._stream_trades(exchange_id, normalized_symbol))
                    
                    self.subscriptions[exchange_id].append(normalized_symbol)
                    logger.info(f"Subscribed to {normalized_symbol} on {exchange_id}")
                else:
                    logger.warning(f"Symbol {normalized_symbol} not found on {exchange_id}")
        
        # Start all streaming tasks
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _stream_ohlcv(self, exchange_id: str, symbol: str) -> None:
        """
        Stream OHLCV data for a symbol.
        
        Args:
            exchange_id: Exchange identifier
            symbol: Trading symbol
        """
        exchange = self.exchanges[exchange_id]
        
        try:
            while self.running:
                try:
                    # Watch OHLCV data (1-minute candles)
                    ohlcv = await exchange.watch_ohlcv(symbol, '1m')
                    
                    if ohlcv:
                        # Convert to Kline object
                        latest_candle = ohlcv[-1]
                        kline = Kline(
                            symbol=symbol,
                            timestamp=datetime.fromtimestamp(latest_candle[0] / 1000),
                            open=latest_candle[1],
                            high=latest_candle[2],
                            low=latest_candle[3],
                            close=latest_candle[4],
                            volume=latest_candle[5],
                            interval="1m",
                            closed=True
                        )
                        
                        # Store in feature store
                        await self._process_kline(kline, exchange_id)
                        
                except Exception as e:
                    logger.error(f"Error streaming OHLCV for {symbol} on {exchange_id}: {e}")
                    await asyncio.sleep(5)  # Wait before retrying
                    
        except asyncio.CancelledError:
            logger.info(f"OHLCV streaming cancelled for {symbol} on {exchange_id}")
    
    async def _stream_trades(self, exchange_id: str, symbol: str) -> None:
        """
        Stream trade data for a symbol.
        
        Args:
            exchange_id: Exchange identifier
            symbol: Trading symbol
        """
        exchange = self.exchanges[exchange_id]
        
        try:
            while self.running:
                try:
                    # Watch trades
                    trades = await exchange.watch_trades(symbol)
                    
                    for trade_data in trades:
                        trade = Trade(
                            symbol=symbol,
                            timestamp=datetime.fromtimestamp(trade_data['timestamp'] / 1000),
                            price=trade_data['price'],
                            quantity=trade_data['amount'],
                            side=trade_data['side'].upper(),
                            trade_id=str(trade_data['id'])
                        )
                        
                        # Store in feature store
                        await self._process_trade(trade, exchange_id)
                        
                except Exception as e:
                    logger.error(f"Error streaming trades for {symbol} on {exchange_id}: {e}")
                    await asyncio.sleep(5)  # Wait before retrying
                    
        except asyncio.CancelledError:
            logger.info(f"Trade streaming cancelled for {symbol} on {exchange_id}")
    
    async def _process_kline(self, kline: Kline, exchange_id: str) -> None:
        """
        Process incoming kline data.
        
        Args:
            kline: Kline object
            exchange_id: Exchange identifier
        """
        # Store current price
        await feature_store.set(kline.symbol, "close", kline.close, exchange=exchange_id)
        await feature_store.set(kline.symbol, "volume", kline.volume, exchange=exchange_id)
        await feature_store.set(kline.symbol, "high", kline.high, exchange=exchange_id)
        await feature_store.set(kline.symbol, "low", kline.low, exchange=exchange_id)
        await feature_store.set(kline.symbol, "open", kline.open, exchange=exchange_id)
        
        # Store price time series
        await feature_store.add_time_series(
            kline.symbol, "close_prices", kline.close, kline.timestamp, exchange=exchange_id
        )
        await feature_store.add_time_series(
            kline.symbol, "volumes", kline.volume, kline.timestamp, exchange=exchange_id
        )
        
        logger.debug(f"Processed kline for {kline.symbol} on {exchange_id}: {kline.close}")
    
    async def _process_trade(self, trade: Trade, exchange_id: str) -> None:
        """
        Process incoming trade data.
        
        Args:
            trade: Trade object
            exchange_id: Exchange identifier
        """
        # Store latest trade
        await feature_store.set(kline.symbol, "last_trade_price", trade.price, exchange=exchange_id)
        await feature_store.set(kline.symbol, "last_trade_size", trade.quantity, exchange=exchange_id)
        
        # Store trade time series
        await feature_store.add_time_series(
            trade.symbol, "trades", {
                "price": trade.price,
                "quantity": trade.quantity,
                "side": trade.side
            }, trade.timestamp, exchange=exchange_id
        )
        
        logger.debug(f"Processed trade for {trade.symbol} on {exchange_id}: {trade.price}")
    
    def _normalize_symbol_for_exchange(self, symbol: str, exchange_id: str) -> str:
        """
        Normalize symbol format for specific exchange.
        
        Args:
            symbol: Universal symbol format (e.g., 'BTC/USDT')
            exchange_id: Exchange identifier
            
        Returns:
            Exchange-specific symbol format
        """
        # Most exchanges use the same format, but some may differ
        return symbol
    
    async def get_exchange_info(self, exchange_id: str) -> ExchangeInfo:
        """
        Get exchange information.
        
        Args:
            exchange_id: Exchange identifier
            
        Returns:
            ExchangeInfo object
        """
        exchange = self.exchanges.get(exchange_id)
        if not exchange:
            raise ValueError(f"Exchange {exchange_id} not initialized")
        
        return ExchangeInfo(
            exchange_id=exchange_id,
            name=exchange.name,
            has_futures=exchange.has.get('future', False),
            has_spot=exchange.has.get('spot', True),
            rate_limit=exchange.rateLimit,
            sandbox=exchange.sandbox
        )
    
    async def get_market_info(self, symbol: str, exchange_id: str) -> Optional[MarketInfo]:
        """
        Get market information for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange_id: Exchange identifier
            
        Returns:
            MarketInfo object or None if not found
        """
        exchange = self.exchanges.get(exchange_id)
        if not exchange:
            return None
        
        market = exchange.markets.get(symbol)
        if not market:
            return None
        
        return MarketInfo(
            symbol=symbol,
            exchange=exchange_id,
            base=market['base'],
            quote=market['quote'],
            active=market['active'],
            type=market['type'],
            spot=market.get('spot', False),
            future=market.get('future', False),
            swap=market.get('swap', False),
            linear=market.get('linear'),
            inverse=market.get('inverse'),
            contract_size=market.get('contractSize'),
            precision=market.get('precision', {}),
            limits=market.get('limits', {}),
            info=market.get('info', {})
        )
    
    async def stop(self) -> None:
        """Stop all market data streaming."""
        logger.info("Stopping CCXT market data streaming...")
        
        self.running = False
        
        # Close all exchange connections
        for exchange_id, exchange in self.exchanges.items():
            try:
                await exchange.close()
                logger.info(f"Closed connection to {exchange_id}")
            except Exception as e:
                logger.error(f"Error closing {exchange_id}: {e}")
        
        logger.info("CCXT Manager stopped")
    
    def get_available_symbols(self, exchange_id: str) -> List[str]:
        """
        Get available symbols for an exchange.
        
        Args:
            exchange_id: Exchange identifier
            
        Returns:
            List of available symbols
        """
        exchange = self.exchanges.get(exchange_id)
        if not exchange:
            return []
        
        return list(exchange.markets.keys())
    
    def get_subscribed_symbols(self, exchange_id: str) -> List[str]:
        """
        Get subscribed symbols for an exchange.
        
        Args:
            exchange_id: Exchange identifier
            
        Returns:
            List of subscribed symbols
        """
        return self.subscriptions.get(exchange_id, [])
