#!/usr/bin/env python3
"""
Database schema creation script for Money Circle demo data.
Creates all required tables for the comprehensive demo data seeding.
"""

import sqlite3
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoTableCreator:
    def __init__(self, db_path='data/money_circle.db'):
        self.db_path = db_path
        self.conn = None

    def connect_db(self):
        """Connect to the database."""
        try:
            # Ensure data directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            logger.info(f"✅ Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False

    def create_trading_performance_table(self):
        """Create trading_performance table."""
        try:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS trading_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    total_return REAL DEFAULT 0,
                    daily_return REAL DEFAULT 0,
                    portfolio_value REAL DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    volatility REAL DEFAULT 0,
                    sharpe_ratio REAL DEFAULT 0,
                    trades_count INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, date)
                )
            """)
            logger.info("✅ Created trading_performance table")
        except Exception as e:
            logger.error(f"❌ Error creating trading_performance table: {e}")

    def create_strategy_tables(self):
        """Create strategy-related tables."""
        try:
            # Strategy proposals table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_proposals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    strategy_type TEXT DEFAULT 'general',
                    risk_level TEXT DEFAULT 'medium',
                    min_investment REAL DEFAULT 1000,
                    status TEXT DEFAULT 'pending',
                    performance_fee REAL DEFAULT 20,
                    management_fee REAL DEFAULT 2,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # Strategy performance table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    portfolio_value REAL DEFAULT 0,
                    daily_return REAL DEFAULT 0,
                    total_return REAL DEFAULT 0,
                    drawdown REAL DEFAULT 0,
                    trades_count INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id),
                    UNIQUE(strategy_id, date)
                )
            """)

            # Strategy following table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_following (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    follower_id INTEGER NOT NULL,
                    strategy_id INTEGER NOT NULL,
                    strategy_creator_id INTEGER NOT NULL,
                    investment_amount REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (follower_id) REFERENCES users (id),
                    FOREIGN KEY (strategy_id) REFERENCES strategy_proposals (id),
                    FOREIGN KEY (strategy_creator_id) REFERENCES users (id),
                    UNIQUE(follower_id, strategy_id)
                )
            """)

            logger.info("✅ Created strategy tables")
        except Exception as e:
            logger.error(f"❌ Error creating strategy tables: {e}")

    def create_member_tables(self):
        """Create member-related tables."""
        try:
            # Member profiles table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS member_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL UNIQUE,
                    display_name TEXT,
                    bio TEXT,
                    location TEXT,
                    specialization TEXT DEFAULT 'general',
                    experience_level TEXT DEFAULT 'beginner',
                    profile_image TEXT,
                    verified BOOLEAN DEFAULT FALSE,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # User connections table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_connections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    connected_user_id INTEGER NOT NULL,
                    connection_type TEXT DEFAULT 'following',
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (connected_user_id) REFERENCES users (id),
                    UNIQUE(user_id, connected_user_id)
                )
            """)

            # Member achievements table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS member_achievements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    achievement_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    icon TEXT,
                    earned_date TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, achievement_id)
                )
            """)

            logger.info("✅ Created member tables")
        except Exception as e:
            logger.error(f"❌ Error creating member tables: {e}")

    def create_analytics_tables(self):
        """Create analytics and reporting tables."""
        try:
            # Club analytics table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS club_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    calculation_date TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    UNIQUE(metric_name, calculation_date)
                )
            """)

            # User sessions table (for tracking active users)
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token TEXT NOT NULL UNIQUE,
                    created_at TEXT NOT NULL,
                    expires_at TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # Notifications table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    notification_type TEXT DEFAULT 'info',
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            logger.info("✅ Created analytics tables")
        except Exception as e:
            logger.error(f"❌ Error creating analytics tables: {e}")

    def create_indexes(self):
        """Create database indexes for better performance."""
        try:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_trading_performance_user_date ON trading_performance(user_id, date)",
                "CREATE INDEX IF NOT EXISTS idx_trading_performance_date ON trading_performance(date)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_date ON strategy_performance(strategy_id, date)",
                "CREATE INDEX IF NOT EXISTS idx_user_connections_user ON user_connections(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_member_achievements_user ON member_achievements(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_club_analytics_date ON club_analytics(calculation_date)",
                "CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id)"
            ]

            for index_sql in indexes:
                self.conn.execute(index_sql)

            logger.info("✅ Created database indexes")
        except Exception as e:
            logger.error(f"❌ Error creating indexes: {e}")

    def create_all_tables(self):
        """Create all required tables for demo data."""
        logger.info("🚀 Creating Money Circle demo database tables...")

        if not self.connect_db():
            return False

        try:
            # Create all tables
            self.create_trading_performance_table()
            self.create_strategy_tables()
            self.create_member_tables()
            self.create_analytics_tables()
            self.create_indexes()

            # Commit all changes
            self.conn.commit()

            logger.info("✅ All demo database tables created successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Table creation failed: {e}")
            return False

        finally:
            if self.conn:
                self.conn.close()

def main():
    """Main function to create demo tables."""
    creator = DemoTableCreator()
    success = creator.create_all_tables()

    if success:
        print("\nMONEY CIRCLE DEMO TABLES CREATED!")
        print("=" * 50)
        print("All required tables created successfully")
        print("Database indexes optimized for performance")
        print("Ready for demo data seeding")
        print("\nNext step: Run seed_demo_data.py")
        return 0
    else:
        print("\nTABLE CREATION FAILED - Check logs for details")
        return 1

if __name__ == "__main__":
    exit(main())
