#!/usr/bin/env python3
"""
Verify that demo data has been successfully created.
"""

import sqlite3
from datetime import datetime

def verify_demo_data():
    """Verify all demo data is properly created."""
    print("🔍 VERIFYING MONEY CIRCLE DEMO DATA")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/money_circle.db')
        conn.row_factory = sqlite3.Row
        
        # 1. Check users
        print("👥 USERS:")
        users = conn.execute("SELECT username, role, is_active FROM users ORDER BY username").fetchall()
        for user in users:
            status = "✅ Active" if user['is_active'] else "❌ Inactive"
            print(f"   {user['username']} ({user['role']}) - {status}")
        
        # 2. Check member profiles
        print(f"\n📋 MEMBER PROFILES:")
        profiles = conn.execute("""
            SELECT u.username, mp.display_name, mp.trading_style, mp.reputation_score
            FROM users u
            JOIN member_profiles mp ON u.id = mp.user_id
            ORDER BY u.username
        """).fetchall()
        
        for profile in profiles:
            print(f"   {profile['username']}: {profile['display_name']} ({profile['trading_style']}) - {profile['reputation_score']:.1f}★")
        
        # 3. Check trading data
        print(f"\n💹 TRADING DATA:")
        trading_stats = conn.execute("""
            SELECT 
                u.username,
                COUNT(DISTINCT ut.id) as trades_count,
                COUNT(DISTINCT up.id) as positions_count,
                ROUND(SUM(up.pnl), 2) as total_pnl
            FROM users u
            LEFT JOIN user_trades ut ON u.id = ut.user_id
            LEFT JOIN user_positions up ON u.id = up.user_id AND up.status = 'open'
            WHERE u.username != 'epinnox'
            GROUP BY u.id
            ORDER BY u.username
        """).fetchall()
        
        for stats in trading_stats:
            pnl_display = f"${stats['total_pnl']:,.2f}" if stats['total_pnl'] else "$0.00"
            print(f"   {stats['username']}: {stats['trades_count']} trades, {stats['positions_count']} positions, {pnl_display} P&L")
        
        # 4. Check strategy data
        print(f"\n🎯 STRATEGY DATA:")
        strategies = conn.execute("SELECT name, status FROM strategy_proposals ORDER BY name").fetchall()
        for strategy in strategies:
            print(f"   {strategy['name']} ({strategy['status']})")
        
        # 5. Summary statistics
        print(f"\n📊 SUMMARY STATISTICS:")
        
        total_users = conn.execute("SELECT COUNT(*) as count FROM users").fetchone()['count']
        total_profiles = conn.execute("SELECT COUNT(*) as count FROM member_profiles").fetchone()['count']
        total_trades = conn.execute("SELECT COUNT(*) as count FROM user_trades").fetchone()['count']
        total_positions = conn.execute("SELECT COUNT(*) as count FROM user_positions WHERE status = 'open'").fetchone()['count']
        total_strategies = conn.execute("SELECT COUNT(*) as count FROM strategy_proposals").fetchone()['count']
        
        print(f"   Total Users: {total_users}")
        print(f"   Member Profiles: {total_profiles}")
        print(f"   Total Trades: {total_trades}")
        print(f"   Open Positions: {total_positions}")
        print(f"   Strategies: {total_strategies}")
        
        # 6. Check data quality
        print(f"\n✅ DATA QUALITY CHECKS:")
        
        # Users with profiles
        users_with_profiles = conn.execute("""
            SELECT COUNT(*) as count FROM users u
            JOIN member_profiles mp ON u.id = mp.user_id
            WHERE u.username != 'epinnox'
        """).fetchone()['count']
        
        # Users with trading data
        users_with_trades = conn.execute("""
            SELECT COUNT(DISTINCT u.id) as count FROM users u
            JOIN user_trades ut ON u.id = ut.user_id
            WHERE u.username != 'epinnox'
        """).fetchone()['count']
        
        # Users with positions
        users_with_positions = conn.execute("""
            SELECT COUNT(DISTINCT u.id) as count FROM users u
            JOIN user_positions up ON u.id = up.user_id
            WHERE u.username != 'epinnox' AND up.status = 'open'
        """).fetchone()['count']
        
        demo_users_count = total_users - 1  # Excluding epinnox
        
        print(f"   Demo users with profiles: {users_with_profiles}/{demo_users_count}")
        print(f"   Demo users with trades: {users_with_trades}/{demo_users_count}")
        print(f"   Demo users with positions: {users_with_positions}/{demo_users_count}")
        
        # Calculate success rate
        profile_rate = (users_with_profiles / demo_users_count) * 100 if demo_users_count > 0 else 0
        trading_rate = (users_with_trades / demo_users_count) * 100 if demo_users_count > 0 else 0
        position_rate = (users_with_positions / demo_users_count) * 100 if demo_users_count > 0 else 0
        
        print(f"\n📈 SUCCESS RATES:")
        print(f"   Profile completion: {profile_rate:.1f}%")
        print(f"   Trading data: {trading_rate:.1f}%")
        print(f"   Position data: {position_rate:.1f}%")
        
        overall_success = (profile_rate + trading_rate + position_rate) / 3
        
        print(f"\n🎯 OVERALL SUCCESS: {overall_success:.1f}%")
        
        if overall_success >= 90:
            print("🎉 EXCELLENT! Demo data is comprehensive and ready!")
            success_level = "EXCELLENT"
        elif overall_success >= 70:
            print("✅ GOOD! Demo data is mostly complete.")
            success_level = "GOOD"
        elif overall_success >= 50:
            print("⚠️ FAIR! Demo data has some gaps.")
            success_level = "FAIR"
        else:
            print("❌ POOR! Demo data needs significant work.")
            success_level = "POOR"
        
        conn.close()
        
        # Final recommendations
        print(f"\n🎯 DEMO ENVIRONMENT STATUS: {success_level}")
        print("=" * 60)
        
        if overall_success >= 70:
            print("✅ READY FOR DEMONSTRATION!")
            print("✅ Login credentials: username/securepass123")
            print("✅ Member directory populated")
            print("✅ Trading data available")
            print("✅ Portfolio balances visible")
            print("\n🌐 Access points:")
            print("   • Login: http://localhost:8084/login")
            print("   • Dashboard: http://localhost:8084/dashboard")
            print("   • Members: http://localhost:8084/club/members")
            print("   • Strategies: http://localhost:8084/club/strategies")
            print("   • Analytics: http://localhost:8084/club/analytics")
        else:
            print("⚠️ NEEDS MORE WORK")
            print("Some demo data is missing or incomplete")
        
        return overall_success >= 70
        
    except Exception as e:
        print(f"❌ Error verifying demo data: {e}")
        return False

def main():
    """Main verification function."""
    success = verify_demo_data()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
