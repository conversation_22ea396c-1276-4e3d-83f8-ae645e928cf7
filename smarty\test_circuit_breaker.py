#!/usr/bin/env python3
"""
🔥 Circuit Breaker Test

Test the production-grade circuit breaker implementation to ensure
proper fail-fast behavior and automatic recovery.
"""

import asyncio
import time
import logging
import sys
from core.circuit_breaker import (
    CircuitBreaker, CircuitBreakerConfig, CircuitState,
    CircuitBreakerOpenError, circuit_manager,
    create_exchange_breaker, create_database_breaker
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CircuitBreakerTest:
    """Test suite for circuit breaker functionality."""

    def __init__(self):
        self.test_results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0
        }

    async def test_basic_functionality(self) -> bool:
        """Test basic circuit breaker open/close functionality."""
        logger.info("🧪 Testing basic circuit breaker functionality...")

        # Create test circuit breaker
        config = CircuitBreakerConfig(
            failure_threshold=2,
            recovery_timeout=1.0,
            success_threshold=1,
            timeout=0.5
        )
        breaker = CircuitBreaker("test_basic", config)

        try:
            # Test successful operation
            async def success_func():
                return "success"

            result = await breaker.call(success_func)
            assert result == "success"
            assert breaker.state == CircuitState.CLOSED
            logger.info("   ✅ Successful operation works")

            # Test failure operation
            async def failure_func():
                raise Exception("test failure")

            # First failure
            try:
                await breaker.call(failure_func)
                assert False, "Should have raised exception"
            except Exception as e:
                assert "test failure" in str(e)
                assert breaker.state == CircuitState.CLOSED  # Still closed

            # Second failure - should open circuit
            try:
                await breaker.call(failure_func)
                assert False, "Should have raised exception"
            except Exception as e:
                assert "test failure" in str(e)
                assert breaker.state == CircuitState.OPEN  # Now open

            logger.info("   ✅ Circuit opens after threshold failures")

            # Test circuit breaker open behavior
            try:
                await breaker.call(success_func)
                assert False, "Should have raised CircuitBreakerOpenError"
            except CircuitBreakerOpenError:
                logger.info("   ✅ Circuit breaker blocks calls when open")

            # Wait for recovery timeout (need to wait for backoff time)
            await asyncio.sleep(2.1)  # Wait for exponential backoff

            # Should now allow one test call (half-open)
            result = await breaker.call(success_func)
            assert result == "success"
            assert breaker.state == CircuitState.CLOSED  # Should close after success
            logger.info("   ✅ Circuit breaker recovers after timeout")

            return True

        except Exception as e:
            logger.error(f"   ❌ Basic functionality test failed: {e}")
            return False

    async def test_timeout_handling(self) -> bool:
        """Test circuit breaker timeout functionality."""
        logger.info("🧪 Testing timeout handling...")

        config = CircuitBreakerConfig(
            failure_threshold=1,
            timeout=0.1  # Very short timeout
        )
        breaker = CircuitBreaker("test_timeout", config)

        try:
            async def slow_func():
                await asyncio.sleep(0.2)  # Longer than timeout
                return "should not reach here"

            try:
                await breaker.call(slow_func)
                assert False, "Should have timed out"
            except Exception as e:  # Could be TimeoutError or wrapped exception
                if "timed out" in str(e).lower():
                    logger.info("   ✅ Timeout handling works")
                    assert breaker.state == CircuitState.OPEN  # Should open after timeout
                    return True
                else:
                    raise e

        except Exception as e:
            logger.error(f"   ❌ Timeout test failed: {e}")
            return False

    async def test_metrics_collection(self) -> bool:
        """Test metrics collection functionality."""
        logger.info("🧪 Testing metrics collection...")

        breaker = CircuitBreaker("test_metrics")

        try:
            # Generate some activity
            async def success_func():
                return "success"

            async def failure_func():
                raise Exception("test failure")

            # Some successes
            for _ in range(3):
                await breaker.call(success_func)

            # Some failures
            for _ in range(2):
                try:
                    await breaker.call(failure_func)
                except:
                    pass

            # Check metrics
            metrics = breaker.get_metrics()

            assert metrics["total_calls"] == 5
            assert metrics["total_successes"] == 3
            assert metrics["total_failures"] == 2
            assert metrics["name"] == "test_metrics"

            logger.info("   ✅ Metrics collection works")
            logger.info(f"   📊 Metrics: {metrics['total_calls']} calls, "
                       f"{metrics['total_successes']} successes, "
                       f"{metrics['total_failures']} failures")

            return True

        except Exception as e:
            logger.error(f"   ❌ Metrics test failed: {e}")
            return False

    async def test_circuit_manager(self) -> bool:
        """Test circuit breaker manager functionality."""
        logger.info("🧪 Testing circuit breaker manager...")

        try:
            # Create different types of breakers
            exchange_breaker = create_exchange_breaker("test_exchange")
            database_breaker = create_database_breaker("test_database")

            assert exchange_breaker.name == "exchange_test_exchange"
            assert database_breaker.name == "database_test_database"

            # Test manager metrics
            all_metrics = circuit_manager.get_all_metrics()
            assert "exchange_test_exchange" in all_metrics
            assert "database_test_database" in all_metrics

            # Test system health
            health = circuit_manager.get_system_health()
            assert health["status"] == "healthy"
            assert health["total_breakers"] >= 2

            logger.info("   ✅ Circuit breaker manager works")
            logger.info(f"   📊 System health: {health['status']} "
                       f"({health['total_breakers']} breakers)")

            return True

        except Exception as e:
            logger.error(f"   ❌ Manager test failed: {e}")
            return False

    async def run_test(self, test_name: str, test_func) -> bool:
        """Run a single test and record results."""
        self.test_results["total_tests"] += 1

        try:
            success = await test_func()
            if success:
                self.test_results["passed"] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                self.test_results["failed"] += 1
                logger.error(f"❌ {test_name}: FAILED")
            return success

        except Exception as e:
            self.test_results["failed"] += 1
            logger.error(f"❌ {test_name}: EXCEPTION - {e}")
            return False

    async def run_all_tests(self):
        """Run all circuit breaker tests."""
        logger.info("🔥 Circuit Breaker Test Suite")
        logger.info("=" * 50)

        # Run all tests
        await self.run_test("Basic Functionality", self.test_basic_functionality)
        await self.run_test("Timeout Handling", self.test_timeout_handling)
        await self.run_test("Metrics Collection", self.test_metrics_collection)
        await self.run_test("Circuit Manager", self.test_circuit_manager)

        # Print summary
        logger.info("\n📊 Test Summary")
        logger.info("=" * 50)
        logger.info(f"Total Tests: {self.test_results['total_tests']}")
        logger.info(f"Passed: {self.test_results['passed']}")
        logger.info(f"Failed: {self.test_results['failed']}")

        success_rate = self.test_results['passed'] / self.test_results['total_tests']
        logger.info(f"Success Rate: {success_rate:.1%}")

        if self.test_results['failed'] == 0:
            logger.info("🎉 ALL CIRCUIT BREAKER TESTS PASSED!")
            return 0
        else:
            logger.error("🚨 SOME CIRCUIT BREAKER TESTS FAILED!")
            return 1

async def main():
    """Main test runner."""
    tester = CircuitBreakerTest()
    return await tester.run_all_tests()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
