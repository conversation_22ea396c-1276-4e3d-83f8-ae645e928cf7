#!/usr/bin/env python3
"""
Centralized Logging System for Smart Trading Strategies

Provides standardized logging across all strategy files with:
- Time-based log file naming: logs/YYYYMMDD_HH_strategy_name.log
- Structured logging with signal events, trade execution, LLM responses
- Error stack traces and strategy state changes
- Performance metrics and health monitoring
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import json
import traceback

class StrategyLogger:
    """Centralized logger for trading strategies."""

    def __init__(self, strategy_name: str, log_level: str = "INFO"):
        self.strategy_name = strategy_name
        self.log_level = getattr(logging, log_level.upper())

        # Create logs directory
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # Generate time-based log filename
        timestamp = datetime.now().strftime("%Y%m%d_%H")
        self.log_file = self.log_dir / f"{timestamp}_{strategy_name}.log"

        # Set up logger
        self.logger = logging.getLogger(f"strategy.{strategy_name}")
        self.logger.setLevel(self.log_level)

        # Clear existing handlers
        self.logger.handlers.clear()

        # Create formatters
        self.detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
        )

        self.json_formatter = JsonFormatter()

        # File handler with rotation and UTF-8 encoding
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'  # Fix Unicode encoding issues
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(self.detailed_formatter)
        self.logger.addHandler(file_handler)

        # Console handler with Unicode support
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(UnicodeFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(console_handler)

        # JSON log handler for structured events with UTF-8 encoding
        json_log_file = self.log_dir / f"{timestamp}_{strategy_name}_events.json"
        json_handler = logging.FileHandler(json_log_file, encoding='utf-8')
        json_handler.setLevel(logging.INFO)
        json_handler.setFormatter(self.json_formatter)

        # Create separate logger for JSON events
        self.event_logger = logging.getLogger(f"events.{strategy_name}")
        self.event_logger.setLevel(logging.INFO)
        self.event_logger.handlers.clear()
        self.event_logger.addHandler(json_handler)

        # Log initialization
        self.info(f"🚀 Strategy logger initialized for {strategy_name}")
        self.info(f"📁 Log file: {self.log_file}")
        self.info(f"📊 JSON events: {json_log_file}")

    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)

    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)

    def error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)

    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)

    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self.logger.critical(message, **kwargs)

    def log_signal_generation(self, signal_data: Dict[str, Any]):
        """Log signal generation event."""
        event = {
            "event_type": "signal_generation",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "signal": signal_data
        }

        self.event_logger.info(json.dumps(event))
        self.info(f"🎯 Signal generated: {signal_data.get('action', 'UNKNOWN')} "
                 f"confidence={signal_data.get('confidence', 0.0):.3f} "
                 f"price={signal_data.get('price', 0.0)}")

    def log_trade_execution(self, trade_data: Dict[str, Any], success: bool = True):
        """Log trade execution attempt."""
        event = {
            "event_type": "trade_execution",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "trade": trade_data,
            "success": success
        }

        self.event_logger.info(json.dumps(event))

        if success:
            self.info(f"✅ Trade executed: {trade_data.get('action', 'UNKNOWN')} "
                     f"size={trade_data.get('size', 0.0)} "
                     f"price={trade_data.get('price', 0.0)}")
        else:
            self.error(f"❌ Trade execution failed: {trade_data}")

    def log_llm_response(self, prompt: str, response: str, response_time_ms: float, success: bool = True):
        """Log LLM interaction."""
        event = {
            "event_type": "llm_response",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "prompt_length": len(prompt),
            "response_length": len(response),
            "response_time_ms": response_time_ms,
            "success": success
        }

        self.event_logger.info(json.dumps(event))

        if success:
            self.info(f"[LLM] Response received: {response_time_ms:.1f}ms, "
                     f"response_length={len(response)}")
        else:
            self.error(f"[LLM] Response failed: timeout or error after {response_time_ms:.1f}ms")

    def log_error_with_stack(self, message: str, exception: Exception):
        """Log error with full stack trace."""
        stack_trace = traceback.format_exc()

        event = {
            "event_type": "error",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "message": message,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "stack_trace": stack_trace
        }

        self.event_logger.info(json.dumps(event))
        self.error(f"❌ {message}: {exception}")
        self.debug(f"Stack trace: {stack_trace}")

    def log_strategy_state_change(self, old_state: str, new_state: str, context: Dict[str, Any] = None):
        """Log strategy state changes."""
        event = {
            "event_type": "state_change",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "old_state": old_state,
            "new_state": new_state,
            "context": context or {}
        }

        self.event_logger.info(json.dumps(event))
        self.info(f"🔄 State change: {old_state} → {new_state}")

    def log_performance_metrics(self, metrics: Dict[str, Any]):
        """Log performance metrics."""
        event = {
            "event_type": "performance_metrics",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "metrics": metrics
        }

        self.event_logger.info(json.dumps(event))
        self.info(f"📊 Performance metrics: {metrics}")

    def log_market_data_update(self, symbol: str, price: float, volume: float, indicators: Dict[str, Any] = None):
        """Log market data updates."""
        event = {
            "event_type": "market_data",
            "timestamp": datetime.now().isoformat(),
            "strategy": self.strategy_name,
            "symbol": symbol,
            "price": price,
            "volume": volume,
            "indicators": indicators or {}
        }

        self.event_logger.info(json.dumps(event))
        self.debug(f"[MARKET] Data: {symbol} price={price} volume={volume}")


class UnicodeFormatter(logging.Formatter):
    """Custom formatter that handles Unicode characters safely."""

    def format(self, record):
        """Format log record with Unicode character handling."""
        try:
            # Get the formatted message
            formatted = super().format(record)

            # Replace problematic Unicode characters with safe alternatives
            unicode_replacements = {
                '\U0001f680': '[ROCKET]',  # 🚀
                '\U0001f4c1': '[FOLDER]',  # 📁
                '\U0001f4ca': '[CHART]',   # 📊
                '\U0001f4c5': '[CALENDAR]', # 📅
                '\U0001f4b0': '[MONEY]',   # 💰
                '\U0001f4b8': '[MONEY_WINGS]', # 💸
                '\U0001f9ea': '[TEST_TUBE]', # 🧪
                '\U0001f41b': '[BUG]',     # 🐛
                '\U0001f9e0': '[BRAIN]',   # 🧠
                '\U0001f4dd': '[MEMO]',    # 📝
                '\U0001f3af': '[TARGET]',  # 🎯
                '\U0001f4e1': '[SATELLITE]', # 📡
                '\U0001f4c8': '[CHART_UP]', # 📈
                '\U0001f4c9': '[CHART_DOWN]', # 📉
                '\U0001f527': '[WRENCH]',  # 🔧
                '\U0001f4bb': '[LAPTOP]',  # 💻
                '\U0001f310': '[GLOBE]',   # 🌐
                '\U0001f512': '[LOCK]',    # 🔒
                '\U0001f513': '[UNLOCK]',  # 🔓
            }

            for unicode_char, replacement in unicode_replacements.items():
                formatted = formatted.replace(unicode_char, replacement)

            return formatted

        except Exception:
            # Fallback to basic ASCII-safe formatting
            return f"{record.levelname}: {record.getMessage()}"


class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record):
        """Format log record as JSON."""
        try:
            # Try to parse message as JSON first
            return record.getMessage()
        except:
            # Fallback to standard formatting
            log_entry = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno
            }
            return json.dumps(log_entry)


def get_strategy_logger(strategy_name: str, log_level: str = "INFO") -> StrategyLogger:
    """Get or create a strategy logger."""
    return StrategyLogger(strategy_name, log_level)


def setup_strategy_logging(strategy_name: str, log_level: str = "INFO") -> StrategyLogger:
    """Set up logging for a strategy with standardized configuration."""
    logger = get_strategy_logger(strategy_name, log_level)

    # Log system information
    logger.info(f"🎯 Strategy: {strategy_name}")
    logger.info(f"📅 Session: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🔧 Log level: {log_level}")
    logger.info(f"💻 Python: {sys.version}")
    logger.info(f"📁 Working directory: {os.getcwd()}")

    return logger


# Pre-configured loggers for the 3 working strategies
def get_smart_model_logger(log_level: str = "INFO") -> StrategyLogger:
    """Get logger for Smart Model Integrated strategy."""
    return setup_strategy_logging("smart_model_integrated", log_level)


def get_smart_strategy_logger(log_level: str = "INFO") -> StrategyLogger:
    """Get logger for Smart Strategy Only."""
    return setup_strategy_logging("smart_strategy_only", log_level)


def get_order_flow_logger(log_level: str = "INFO") -> StrategyLogger:
    """Get logger for Order Flow Analysis strategy."""
    return setup_strategy_logging("order_flow", log_level)


# Example usage patterns for each strategy type
"""
# In orchestrator.py (Smart Model Integrated):
from utils.centralized_logging import get_smart_model_logger

logger = get_smart_model_logger()

# Signal generation
logger.log_signal_generation({
    "action": "BUY",
    "confidence": 0.85,
    "price": 45000.0,
    "reasoning": "Strong bullish signals from multiple models"
})

# LLM interaction
logger.log_llm_response(prompt, response, response_time_ms, success=True)

# Error handling
try:
    # strategy logic
    pass
except Exception as e:
    logger.log_error_with_stack("Strategy execution failed", e)

# State changes
logger.log_strategy_state_change("initializing", "running", {"models_loaded": 5})
"""
