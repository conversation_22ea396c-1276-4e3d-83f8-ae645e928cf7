#!/usr/bin/env python3
"""
Comprehensive test suite for Enhanced Smart Trader Dashboard
Tests all components, API endpoints, and functionality
"""

import unittest
import requests
import json
import time
from datetime import datetime
import sys
import os

class TestEnhancedDashboard(unittest.TestCase):
    """Test suite for Enhanced Dashboard functionality."""
    
    BASE_URL = "http://localhost:8082"
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        print("\n🧪 Enhanced Dashboard Test Suite")
        print("=" * 60)
        print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Testing dashboard at: {cls.BASE_URL}")
        print("=" * 60)
        
        # Wait for dashboard to be ready
        print("⏳ Waiting for dashboard to be ready...")
        time.sleep(3)
    
    def test_01_dashboard_accessibility(self):
        """Test if main dashboard page is accessible."""
        print("\n📊 Testing Dashboard Accessibility...")
        
        try:
            response = requests.get(f"{self.BASE_URL}/", timeout=10)
            self.assertEqual(response.status_code, 200)
            self.assertIn("Smart Trader Live Dashboard", response.text)
            print("  ✅ Main dashboard page accessible")
        except Exception as e:
            self.fail(f"Dashboard not accessible: {e}")
    
    def test_02_original_api_endpoints(self):
        """Test original API endpoints that should be working."""
        print("\n🔌 Testing Original API Endpoints...")
        
        endpoints = [
            ("/api/signals", "Trading Signals"),
            ("/api/trades", "Active Trades"), 
            ("/api/stats", "System Stats")
        ]
        
        for endpoint, description in endpoints:
            with self.subTest(endpoint=endpoint):
                try:
                    response = requests.get(f"{self.BASE_URL}{endpoint}", timeout=10)
                    self.assertEqual(response.status_code, 200)
                    data = response.json()
                    self.assertIsInstance(data, (dict, list))
                    print(f"  ✅ {description}: {endpoint}")
                except Exception as e:
                    print(f"  ❌ {description}: {endpoint} - {e}")
                    self.fail(f"{description} endpoint failed: {e}")
    
    def test_03_new_api_endpoints(self):
        """Test new API endpoints for enhanced features."""
        print("\n🆕 Testing New API Endpoints...")
        
        endpoints = [
            ("/api/market", "Market Data"),
            ("/api/ai-analysis", "AI Analysis"),
            ("/api/market-sentiment", "Market Sentiment"),
            ("/api/orderbook", "Order Book"),
            ("/api/recent-trades", "Recent Trades")
        ]
        
        working_endpoints = []
        failing_endpoints = []
        
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.BASE_URL}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    working_endpoints.append((endpoint, description))
                    print(f"  ✅ {description}: {endpoint}")
                else:
                    failing_endpoints.append((endpoint, description, response.status_code))
                    print(f"  ❌ {description}: {endpoint} - Status {response.status_code}")
            except Exception as e:
                failing_endpoints.append((endpoint, description, str(e)))
                print(f"  💥 {description}: {endpoint} - {e}")
        
        # Store results for later analysis
        self.working_new_endpoints = working_endpoints
        self.failing_new_endpoints = failing_endpoints
    
    def test_04_websocket_connection(self):
        """Test WebSocket connection for real-time updates."""
        print("\n🔄 Testing WebSocket Connection...")
        
        try:
            # Test if WebSocket endpoint exists
            response = requests.get(f"{self.BASE_URL}/ws", timeout=5)
            # WebSocket endpoints typically return 400 for HTTP requests
            self.assertIn(response.status_code, [400, 426])  # Bad Request or Upgrade Required
            print("  ✅ WebSocket endpoint exists")
        except Exception as e:
            print(f"  ❌ WebSocket test failed: {e}")
    
    def test_05_dashboard_ui_components(self):
        """Test if enhanced UI components are present in HTML."""
        print("\n🎨 Testing Dashboard UI Components...")
        
        try:
            response = requests.get(f"{self.BASE_URL}/", timeout=10)
            html_content = response.text
            
            # Test for new sections
            ui_components = [
                ("Live Market Activity & AI Analysis", "Market activity section"),
                ("Trading Operations", "Trading operations section"),
                ("Market Depth", "Order book component"),
                ("Recent Trades", "Trade stream component"),
                ("AI Market Analysis", "AI analysis component"),
                ("Market Sentiment", "Sentiment component"),
                ("Price Chart", "Chart component")
            ]
            
            present_components = []
            missing_components = []
            
            for component_text, description in ui_components:
                if component_text in html_content:
                    present_components.append(description)
                    print(f"  ✅ {description}")
                else:
                    missing_components.append(description)
                    print(f"  ❌ {description}")
            
            self.assertGreater(len(present_components), 0, "No enhanced UI components found")
            
        except Exception as e:
            self.fail(f"UI component test failed: {e}")
    
    def test_06_data_flow_integration(self):
        """Test if data is flowing through the system."""
        print("\n📊 Testing Data Flow Integration...")
        
        try:
            # Test system stats to see if data is flowing
            response = requests.get(f"{self.BASE_URL}/api/stats", timeout=10)
            self.assertEqual(response.status_code, 200)
            
            stats = response.json()
            system_stats = stats.get('system', {})
            
            # Check if system is processing data
            total_messages = system_stats.get('total_messages', 0)
            last_activity = system_stats.get('last_activity', 'N/A')
            
            print(f"  📈 Total messages: {total_messages}")
            print(f"  ⏰ Last activity: {last_activity}")
            
            if total_messages > 0:
                print("  ✅ Data is flowing through the system")
            else:
                print("  ⚠️  No data flow detected")
                
        except Exception as e:
            print(f"  ❌ Data flow test failed: {e}")
    
    def test_07_trading_signals(self):
        """Test trading signal functionality."""
        print("\n🎯 Testing Trading Signals...")
        
        try:
            response = requests.get(f"{self.BASE_URL}/api/signals", timeout=10)
            self.assertEqual(response.status_code, 200)
            
            signals = response.json()
            print(f"  📊 Signals found: {len(signals)}")
            
            if signals:
                latest_signal = signals[0]
                print(f"  🎯 Latest signal: {latest_signal}")
                print("  ✅ Trading signals are working")
            else:
                print("  ⚠️  No trading signals found (system may be initializing)")
                
        except Exception as e:
            print(f"  ❌ Trading signals test failed: {e}")

if __name__ == "__main__":
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedDashboard)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Generate comprehensive report
    print("\n" + "=" * 60)
    print("📋 COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"📊 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failures}")
    print(f"   💥 Errors: {errors}")
    print(f"   📈 Success Rate: {(passed/total_tests)*100:.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}")
    
    if result.errors:
        print(f"\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}")
    
    print(f"\n🎯 Recommendations:")
    if failures + errors == 0:
        print("   🎉 All tests passed! Dashboard is fully operational.")
    else:
        print("   🔧 Issues found that need to be addressed:")
        if failures > 0:
            print("   - Fix failing API endpoints")
        if errors > 0:
            print("   - Resolve system errors")
        print("   - Restart dashboard service if needed")
        print("   - Verify route registration")
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
