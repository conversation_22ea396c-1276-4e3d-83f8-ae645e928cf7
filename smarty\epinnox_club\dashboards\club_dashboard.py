#!/usr/bin/env python3
"""
Money Circle Club Dashboard
Main club interface with strategy governance, social features, and analytics.
"""

import logging
from typing import Dict, List, Any, Optional
from aiohttp import web
from datetime import datetime, timedelta
import aiohttp_jinja2
from auth.decorators import get_current_user
from database.models import DatabaseManager
from database.club_models import ClubDatabaseManager
from club.strategy_governance import StrategyGovernance
from club.social_trading import SocialTrading
from club.analytics import ClubAnalytics

logger = logging.getLogger(__name__)

class ClubDashboard:
    """Club dashboard for collaborative features."""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.club_db = ClubDatabaseManager(db_manager)
        self.strategy_governance = StrategyGovernance(db_manager)
        self.social_trading = SocialTrading(db_manager)
        self.analytics = ClubAnalytics(db_manager)

    async def serve_club_dashboard(self, request: web.Request) -> web.Response:
        """Serve the main club dashboard."""
        user = get_current_user(request)
        if not user:
            return web.Response(status=302, headers={'Location': '/login'})

        # Get club overview data
        club_overview = self.analytics.generate_club_overview()
        pending_strategies = self.strategy_governance.get_pending_strategies()
        voting_strategies = self.strategy_governance.get_voting_strategies()
        approved_strategies = self.strategy_governance.get_approved_strategies()
        activity_feed = self.social_trading.get_member_activity_feed(limit=20)
        leaderboard = self.social_trading.get_member_leaderboard('reputation', 'monthly', 10)

        # Prepare template context
        context = {
            'user': user,
            'club_stats': {
                'total_members': club_overview.get('members', {}).get('total', 0),
                'active_strategies': club_overview.get('strategies', {}).get('active', 0),
                'performance': club_overview.get('trading', {}).get('avg_return', 0),
                'total_volume': club_overview.get('aum', 0)
            },
            'pending_strategies': pending_strategies if user.get('role') == 'admin' else [],
            'active_votes': voting_strategies,
            'recent_activities': self._format_activities(activity_feed),
            'top_strategies': approved_strategies[:5],
            'top_members': leaderboard,
            'notifications': [],  # TODO: Get actual notifications
            'current_path': request.path  # Add current path for navigation
        }

        # Render using template
        return aiohttp_jinja2.render_template('club_dashboard.html', request, context)

    def _format_activities(self, activities):
        """Format activities for template."""
        formatted = []
        for activity in activities:
            formatted.append({
                'icon': self._get_activity_icon(activity.get('activity_type', '')),
                'text': f"{activity.get('display_name', 'Unknown')} {activity.get('formatted_message', '')}",
                'timestamp': self._format_time_ago(activity.get('timestamp', ''))
            })
        return formatted

    def _render_admin_review_section(self, pending_strategies: List[Dict], user: Dict) -> str:
        """Render admin review section."""
        if not pending_strategies:
            return """
            <div class="admin-review-section">
                <h3>⚖️ Admin Review Queue</h3>
                <div class="empty-state">No strategies pending review</div>
            </div>
            """

        strategies_html = ""
        for strategy in pending_strategies:
            strategies_html += f"""
            <div class="pending-strategy-card">
                <div class="strategy-header">
                    <h4>{strategy['title']}</h4>
                    <span class="strategy-type">{strategy['strategy_type']}</span>
                </div>
                <div class="strategy-meta">
                    <span class="proposer">By: {strategy['proposer_display_name']}</span>
                    <span class="risk-level risk-{strategy['risk_level']}">{strategy['risk_level'].title()}</span>
                    <span class="expected-return">Target: {strategy['expected_return']:.1f}%</span>
                </div>
                <div class="strategy-description">
                    {strategy['description'][:150]}{'...' if len(strategy['description']) > 150 else ''}
                </div>
                <div class="admin-actions">
                    <button class="btn-approve" onclick="reviewStrategy({strategy['id']}, 'approve')">Approve</button>
                    <button class="btn-reject" onclick="reviewStrategy({strategy['id']}, 'reject')">Reject</button>
                    <button class="btn-changes" onclick="reviewStrategy({strategy['id']}, 'request_changes')">Request Changes</button>
                </div>
            </div>
            """

        return f"""
        <div class="admin-review-section">
            <h3>⚖️ Admin Review Queue ({len(pending_strategies)})</h3>
            <div class="pending-strategies">
                {strategies_html}
            </div>
        </div>
        """

    def _render_voting_strategies(self, voting_strategies: List[Dict]) -> str:
        """Render voting strategies section."""
        if not voting_strategies:
            return '<div class="empty-state">No strategies currently in voting</div>'

        strategies_html = ""
        for strategy in voting_strategies:
            deadline = datetime.fromisoformat(strategy['voting_deadline']) if strategy.get('voting_deadline') else None
            days_left = (deadline - datetime.now()).days if deadline else 0

            strategies_html += f"""
            <div class="voting-strategy-card">
                <div class="strategy-header">
                    <h4>{strategy['title']}</h4>
                    <span class="vote-count">{strategy['vote_count']} votes</span>
                </div>
                <div class="strategy-meta">
                    <span class="proposer">By: {strategy['proposer_display_name']}</span>
                    <span class="deadline">⏰ {days_left} days left</span>
                </div>
                <div class="voting-actions">
                    <button class="vote-btn approve" onclick="castVote({strategy['id']}, 'approve')">👍 Approve</button>
                    <button class="vote-btn reject" onclick="castVote({strategy['id']}, 'reject')">👎 Reject</button>
                    <button class="vote-btn abstain" onclick="castVote({strategy['id']}, 'abstain')">🤷 Abstain</button>
                </div>
                <a href="/club/strategies/{strategy['id']}" class="view-details">View Details →</a>
            </div>
            """

        return strategies_html

    def _render_approved_strategies(self, approved_strategies: List[Dict]) -> str:
        """Render approved strategies section."""
        if not approved_strategies:
            return '<div class="empty-state">No approved strategies yet</div>'

        strategies_html = ""
        for strategy in approved_strategies:
            strategies_html += f"""
            <div class="approved-strategy-card">
                <div class="strategy-header">
                    <h4>{strategy['title']}</h4>
                    <span class="followers">{strategy['followers_count']} followers</span>
                </div>
                <div class="strategy-meta">
                    <span class="return">📈 {strategy['avg_return']:.2f}% avg return</span>
                    <span class="type">{strategy['strategy_type']}</span>
                </div>
                <div class="strategy-actions">
                    <button class="btn-follow" onclick="followStrategy({strategy['id']})">Follow</button>
                    <a href="/club/strategies/{strategy['id']}" class="btn-view">View</a>
                </div>
            </div>
            """

        return strategies_html

    def _render_activity_feed(self, activities: List[Dict]) -> str:
        """Render activity feed."""
        if not activities:
            return '<div class="empty-state">No recent activity</div>'

        activities_html = ""
        for activity in activities:
            time_ago = self._format_time_ago(activity['timestamp'])
            activities_html += f"""
            <div class="activity-item" data-type="{activity['activity_type']}">
                <div class="activity-avatar">
                    <span class="avatar-initial">{activity['display_name'][0].upper()}</span>
                </div>
                <div class="activity-content">
                    <div class="activity-text">
                        <strong>{activity['display_name']}</strong> {activity['formatted_message']}
                    </div>
                    <div class="activity-time">{time_ago}</div>
                </div>
                <div class="activity-type-icon">
                    {self._get_activity_icon(activity['activity_type'])}
                </div>
            </div>
            """

        return activities_html

    def _render_leaderboard(self, leaderboard: List[Dict]) -> str:
        """Render member leaderboard."""
        if not leaderboard:
            return '<div class="empty-state">No leaderboard data</div>'

        leaderboard_html = ""
        for member in leaderboard:
            rank_class = ""
            if member['rank'] == 1:
                rank_class = "gold"
            elif member['rank'] == 2:
                rank_class = "silver"
            elif member['rank'] == 3:
                rank_class = "bronze"

            leaderboard_html += f"""
            <div class="leaderboard-item {rank_class}">
                <div class="rank">#{member['rank']}</div>
                <div class="member-info">
                    <div class="member-name">{member['display_name']}</div>
                    <div class="member-stats">
                        <span class="reputation">⭐ {member['reputation_score']:.1f}</span>
                        <span class="trades">📊 {member['total_trades']} trades</span>
                    </div>
                </div>
                <div class="member-score">{member['reputation_score']:.0f}</div>
            </div>
            """

        return leaderboard_html

    def _format_time_ago(self, timestamp: str) -> str:
        """Format timestamp as time ago."""
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now()
            diff = now - dt

            if diff.days > 0:
                return f"{diff.days}d ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours}h ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes}m ago"
            else:
                return "Just now"
        except:
            return "Unknown"

    def _get_activity_icon(self, activity_type: str) -> str:
        """Get icon for activity type."""
        icons = {
            'strategy_proposal': '📝',
            'vote': '🗳️',
            'strategy_follow': '👥',
            'trade': '💹',
            'comment': '💬',
            'admin_review': '⚖️'
        }
        return icons.get(activity_type, '📊')

    def _render_modals(self) -> str:
        """Render modal dialogs."""
        return """
        <!-- Propose Strategy Modal -->
        <div id="proposeStrategyModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h2>📝 Propose New Strategy</h2>
                    <span class="close" onclick="closeModal('proposeStrategyModal')">&times;</span>
                </div>
                <form id="proposeStrategyForm" class="strategy-form">
                    <div class="form-section">
                        <h3>Basic Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="strategyTitle">Strategy Title *</label>
                                <input type="text" id="strategyTitle" name="title" required>
                            </div>
                            <div class="form-group">
                                <label for="strategyType">Strategy Type *</label>
                                <select id="strategyType" name="strategy_type" required>
                                    <option value="">Select Type</option>
                                    <option value="momentum">Momentum</option>
                                    <option value="mean_reversion">Mean Reversion</option>
                                    <option value="arbitrage">Arbitrage</option>
                                    <option value="market_making">Market Making</option>
                                    <option value="trend_following">Trend Following</option>
                                    <option value="scalping">Scalping</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="strategyDescription">Description *</label>
                            <textarea id="strategyDescription" name="description" rows="4" required
                                placeholder="Describe your strategy, its logic, and how it works..."></textarea>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Risk & Performance</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="riskLevel">Risk Level *</label>
                                <select id="riskLevel" name="risk_level" required>
                                    <option value="">Select Risk Level</option>
                                    <option value="low">Low Risk</option>
                                    <option value="medium">Medium Risk</option>
                                    <option value="high">High Risk</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="expectedReturn">Expected Return (%) *</label>
                                <input type="number" id="expectedReturn" name="expected_return"
                                       step="0.1" min="0" max="1000" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="maxDrawdown">Max Drawdown (%) *</label>
                                <input type="number" id="maxDrawdown" name="max_drawdown"
                                       step="0.1" min="0" max="100" required>
                            </div>
                            <div class="form-group">
                                <label for="timeHorizon">Time Horizon *</label>
                                <select id="timeHorizon" name="time_horizon" required>
                                    <option value="">Select Horizon</option>
                                    <option value="scalping">Scalping (minutes)</option>
                                    <option value="intraday">Intraday (hours)</option>
                                    <option value="swing">Swing (days)</option>
                                    <option value="position">Position (weeks/months)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>Strategy Parameters</h3>
                        <div class="form-group">
                            <label for="strategyParameters">Parameters (JSON format)</label>
                            <textarea id="strategyParameters" name="parameters" rows="3"
                                placeholder='{"param1": "value1", "param2": 123}'></textarea>
                            <small>Optional: Specify strategy parameters in JSON format</small>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('proposeStrategyModal')">Cancel</button>
                        <button type="submit" class="btn-primary">Submit Proposal</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Vote Modal -->
        <div id="voteModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🗳️ Cast Your Vote</h2>
                    <span class="close" onclick="closeModal('voteModal')">&times;</span>
                </div>
                <form id="voteForm">
                    <input type="hidden" id="voteStrategyId" name="strategy_id">
                    <input type="hidden" id="voteType" name="vote">

                    <div class="vote-confirmation">
                        <p>You are voting <strong id="voteAction"></strong> on strategy:</p>
                        <h3 id="voteStrategyTitle"></h3>
                    </div>

                    <div class="form-group">
                        <label for="voteReasoning">Reasoning (Optional)</label>
                        <textarea id="voteReasoning" name="reasoning" rows="3"
                            placeholder="Share your reasoning for this vote..."></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('voteModal')">Cancel</button>
                        <button type="submit" class="btn-primary">Submit Vote</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Follow Strategy Modal -->
        <div id="followStrategyModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>👥 Follow Strategy</h2>
                    <span class="close" onclick="closeModal('followStrategyModal')">&times;</span>
                </div>
                <form id="followStrategyForm">
                    <input type="hidden" id="followStrategyId" name="strategy_id">

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoExecute" name="auto_execute">
                            Enable Auto-Execution
                        </label>
                        <small>Automatically execute trades from this strategy</small>
                    </div>

                    <div class="form-group">
                        <label for="allocationPercentage">Portfolio Allocation (%)</label>
                        <input type="number" id="allocationPercentage" name="allocation_percentage"
                               min="0" max="100" step="0.1" value="10">
                        <small>Percentage of your portfolio to allocate to this strategy</small>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal('followStrategyModal')">Cancel</button>
                        <button type="submit" class="btn-primary">Follow Strategy</button>
                    </div>
                </form>
            </div>
        </div>
        """