#!/bin/bash
# Simple health check script for the smart-trader system

# Configuration
CONFIG_FILE="config.yaml"
MAX_FUNDING_AGE_MINUTES=15
MAX_LLM_LATENCY=5.0
SYMBOL="BTC-USDT"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo "Running health checks for smart-trader system..."
echo "================================================"

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo -e "${RED}ERROR: Python not found. Please install Python.${NC}"
    exit 1
fi

# Run the health check script
echo -e "${YELLOW}Running comprehensive health check...${NC}"
python health_check.py --config $CONFIG_FILE
HEALTH_CHECK_RESULT=$?

if [ $HEALTH_CHECK_RESULT -eq 0 ]; then
    echo -e "${GREEN}All health checks passed!${NC}"
else
    echo -e "${RED}Some health checks failed. See above for details.${NC}"
    exit 1
fi

echo "================================================"
echo -e "${GREEN}Health check completed successfully.${NC}"
exit 0
