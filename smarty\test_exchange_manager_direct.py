#!/usr/bin/env python3
"""
Test the exchange account manager directly with HTX credentials.
"""

import sys
import os
import yaml
from pathlib import Path

# Add the epinnox_club directory to the path
sys.path.insert(0, str(Path(__file__).parent / 'epinnox_club'))

from database.models import DatabaseManager
from exchanges.account_manager import ExchangeAccountManager

def load_credentials():
    """Load credentials from credentials.yaml"""
    cred_file = Path("credentials.yaml")
    if not cred_file.exists():
        print("❌ credentials.yaml not found")
        return None

    with open(cred_file, 'r') as f:
        creds = yaml.safe_load(f)

    return creds

def test_exchange_manager():
    """Test the exchange account manager directly"""
    print("🔧 Testing Exchange Account Manager directly...")

    # Initialize database
    db_path = Path("epinnox_club/data/money_circle.db")
    db_manager = DatabaseManager(str(db_path))

    # Initialize exchange account manager
    exchange_manager = ExchangeAccountManager(db_manager)

    # Check supported exchanges
    supported = exchange_manager.get_supported_exchanges()
    print("📊 Supported exchanges:")
    for name, config in supported.items():
        print(f"  🏦 {name}: {config['name']} (testnet: {config['testnet_available']})")

    return exchange_manager, db_manager

def test_add_htx_account(exchange_manager, api_key, secret_key, account_name):
    """Test adding HTX account"""
    print(f"\n🔧 Testing HTX account addition: {account_name}")

    # Test user ID (assuming user 1 exists)
    user_id = 1

    # Try both exchange names
    for exchange_name in ['huobi', 'HTX']:
        print(f"\n📊 Trying exchange name: {exchange_name}")

        try:
            success = exchange_manager.add_exchange_account(
                user_id=user_id,
                exchange_name=exchange_name,
                api_key=api_key,
                secret_key=secret_key
            )

            if success:
                print(f"✅ Successfully added HTX account with name: {exchange_name}")

                # Test getting user exchanges
                user_exchanges = exchange_manager.get_user_exchanges(user_id)
                print(f"📋 User now has {len(user_exchanges)} exchange accounts")

                for exchange in user_exchanges:
                    status = "active" if exchange.is_active else "inactive"
                    print(f"  🏦 {exchange.exchange_name}: {status}")

                return True
            else:
                print(f"❌ Failed to add account with name: {exchange_name}")

        except Exception as e:
            print(f"❌ Exception adding {exchange_name}: {e}")

    return False

def main():
    print("🚀 Direct Exchange Manager HTX Test")
    print("=" * 50)

    # Load credentials
    creds = load_credentials()
    if not creds:
        sys.exit(1)

    # Get HTX credentials
    accounts = creds.get('accounts', [])
    default_account = creds.get('default_account', 'EPX')

    # Find the default HTX account
    htx_account = None
    for account in accounts:
        if account.get('name') == default_account and account.get('exchange') == 'huobi':
            htx_account = account
            break

    if not htx_account:
        print("❌ No default HTX account found")
        sys.exit(1)

    api_key = htx_account.get('api_key')
    secret_key = htx_account.get('secret_key')
    account_name = htx_account.get('name')

    print(f"📋 Using account: {account_name} - {htx_account.get('description')}")
    print(f"📊 API Key: {api_key[:8]}...")

    try:
        # Test exchange manager
        exchange_manager, db_manager = test_exchange_manager()

        # Test adding HTX account
        success = test_add_htx_account(exchange_manager, api_key, secret_key, account_name)

        print("\n" + "=" * 50)
        if success:
            print("🎉 Direct exchange manager test: SUCCESS")
            print("✅ HTX account successfully added via exchange manager")
        else:
            print("❌ Direct exchange manager test: FAILED")
            print("❌ Could not add HTX account via exchange manager")

        return 0 if success else 1

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
