#!/usr/bin/env python3
"""
🚀 Production Readiness Test Suite

Comprehensive testing for the Epinnox Smart Trading Dashboard to ensure
production-grade reliability, data flow, and system health monitoring.

Usage:
    python test_production_readiness.py
"""

import asyncio
import aiohttp
import json
import time
import logging
import sys
import subprocess
import os
from typing import Dict, Any, List
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionReadinessTest:
    """Comprehensive production readiness testing suite."""
    
    def __init__(self, dashboard_url: str = "http://localhost:8082"):
        self.dashboard_url = dashboard_url
        self.session = None
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "warnings": 0,
            "test_details": {}
        }
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def run_test(self, test_name: str, test_func, *args, **kwargs) -> bool:
        """Run a single test and record results."""
        logger.info(f"🧪 Running test: {test_name}")
        self.test_results["total_tests"] += 1
        
        try:
            result = await test_func(*args, **kwargs)
            
            if result.get("status") == "pass":
                self.test_results["passed"] += 1
                logger.info(f"✅ {test_name}: PASSED")
            elif result.get("status") == "warning":
                self.test_results["warnings"] += 1
                logger.warning(f"⚠️ {test_name}: WARNING - {result.get('message', '')}")
            else:
                self.test_results["failed"] += 1
                logger.error(f"❌ {test_name}: FAILED - {result.get('message', '')}")
            
            self.test_results["test_details"][test_name] = result
            return result.get("status") == "pass"
            
        except Exception as e:
            self.test_results["failed"] += 1
            logger.error(f"❌ {test_name}: EXCEPTION - {e}")
            self.test_results["test_details"][test_name] = {
                "status": "failed",
                "message": f"Exception: {e}",
                "error": str(e)
            }
            return False
    
    async def test_dashboard_accessibility(self) -> Dict[str, Any]:
        """Test if dashboard is accessible and responsive."""
        try:
            start_time = time.time()
            async with self.session.get(f"{self.dashboard_url}/dashboard") as resp:
                response_time = time.time() - start_time
                
                if resp.status == 200:
                    if response_time < 2.0:
                        return {
                            "status": "pass",
                            "message": f"Dashboard accessible in {response_time:.2f}s",
                            "response_time": response_time
                        }
                    else:
                        return {
                            "status": "warning",
                            "message": f"Dashboard slow (took {response_time:.2f}s)",
                            "response_time": response_time
                        }
                else:
                    return {
                        "status": "failed",
                        "message": f"Dashboard returned HTTP {resp.status}",
                        "status_code": resp.status
                    }
                    
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Dashboard accessibility test failed: {e}",
                "error": str(e)
            }
    
    async def test_system_health_endpoint(self) -> Dict[str, Any]:
        """Test the comprehensive system health monitoring."""
        try:
            async with self.session.get(f"{self.dashboard_url}/api/health/system") as resp:
                if resp.status == 200:
                    health_data = await resp.json()
                    
                    # Check required fields
                    required_fields = ["timestamp", "system_status", "components"]
                    missing_fields = [field for field in required_fields if field not in health_data]
                    
                    if missing_fields:
                        return {
                            "status": "failed",
                            "message": f"Missing health fields: {missing_fields}",
                            "health_data": health_data
                        }
                    
                    system_status = health_data.get("system_status", "unknown")
                    if system_status in ["healthy", "warning"]:
                        return {
                            "status": "pass",
                            "message": f"System health: {system_status}",
                            "health_data": health_data
                        }
                    else:
                        return {
                            "status": "warning",
                            "message": f"System health degraded: {system_status}",
                            "health_data": health_data
                        }
                else:
                    return {
                        "status": "failed",
                        "message": f"Health endpoint returned HTTP {resp.status}",
                        "status_code": resp.status
                    }
                    
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Health endpoint test failed: {e}",
                "error": str(e)
            }
    
    async def test_data_flow_continuity(self) -> Dict[str, Any]:
        """Test that market data is flowing continuously."""
        try:
            # Get initial market data
            async with self.session.get(f"{self.dashboard_url}/api/market-data") as resp:
                if resp.status != 200:
                    return {
                        "status": "failed",
                        "message": f"Market data endpoint returned HTTP {resp.status}",
                        "status_code": resp.status
                    }
                
                initial_data = await resp.json()
                initial_timestamp = initial_data.get("timestamp", 0)
            
            # Wait and check for new data
            await asyncio.sleep(5)
            
            async with self.session.get(f"{self.dashboard_url}/api/market-data") as resp:
                if resp.status == 200:
                    new_data = await resp.json()
                    new_timestamp = new_data.get("timestamp", 0)
                    
                    if new_timestamp > initial_timestamp:
                        return {
                            "status": "pass",
                            "message": "Market data flowing continuously",
                            "initial_timestamp": initial_timestamp,
                            "new_timestamp": new_timestamp,
                            "time_diff": new_timestamp - initial_timestamp
                        }
                    else:
                        return {
                            "status": "warning",
                            "message": "Market data appears stale",
                            "initial_timestamp": initial_timestamp,
                            "new_timestamp": new_timestamp
                        }
                else:
                    return {
                        "status": "failed",
                        "message": f"Second market data request failed: HTTP {resp.status}",
                        "status_code": resp.status
                    }
                    
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Data flow test failed: {e}",
                "error": str(e)
            }
    
    async def test_strategy_management(self) -> Dict[str, Any]:
        """Test strategy start/stop functionality."""
        try:
            # Get available strategies
            async with self.session.get(f"{self.dashboard_url}/api/strategy/status") as resp:
                if resp.status != 200:
                    return {
                        "status": "failed",
                        "message": f"Strategy status endpoint failed: HTTP {resp.status}",
                        "status_code": resp.status
                    }
                
                status_data = await resp.json()
                available_strategies = status_data.get("available_strategies", [])
                
                if not available_strategies:
                    return {
                        "status": "failed",
                        "message": "No strategies available for testing",
                        "status_data": status_data
                    }
                
                # Test starting the first strategy
                test_strategy = available_strategies[0]
                logger.info(f"   Testing strategy: {test_strategy}")
                
                # Start strategy
                start_data = {"strategy": test_strategy}
                async with self.session.post(
                    f"{self.dashboard_url}/api/strategy/start",
                    json=start_data
                ) as resp:
                    if resp.status == 200:
                        start_result = await resp.json()
                        if start_result.get("success"):
                            # Wait a moment
                            await asyncio.sleep(3)
                            
                            # Stop strategy
                            async with self.session.post(f"{self.dashboard_url}/api/strategy/stop") as resp:
                                if resp.status == 200:
                                    stop_result = await resp.json()
                                    if stop_result.get("success"):
                                        return {
                                            "status": "pass",
                                            "message": f"Strategy '{test_strategy}' started and stopped successfully",
                                            "test_strategy": test_strategy
                                        }
                                    else:
                                        return {
                                            "status": "warning",
                                            "message": f"Strategy started but failed to stop: {stop_result.get('message')}",
                                            "test_strategy": test_strategy
                                        }
                                else:
                                    return {
                                        "status": "warning",
                                        "message": f"Strategy stop request failed: HTTP {resp.status}",
                                        "test_strategy": test_strategy
                                    }
                        else:
                            return {
                                "status": "failed",
                                "message": f"Strategy start failed: {start_result.get('message')}",
                                "test_strategy": test_strategy
                            }
                    else:
                        return {
                            "status": "failed",
                            "message": f"Strategy start request failed: HTTP {resp.status}",
                            "test_strategy": test_strategy
                        }
                        
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Strategy management test failed: {e}",
                "error": str(e)
            }
    
    async def test_api_endpoints_load(self) -> Dict[str, Any]:
        """Test API endpoints under concurrent load."""
        try:
            endpoints = [
                "/api/market-data",
                "/api/signals", 
                "/api/trades",
                "/api/stats",
                "/api/strategy/status"
            ]
            
            # Make concurrent requests to all endpoints
            tasks = []
            for endpoint in endpoints:
                for _ in range(3):  # 3 requests per endpoint
                    task = self.session.get(f"{self.dashboard_url}{endpoint}")
                    tasks.append(task)
            
            start_time = time.time()
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            successful_requests = 0
            failed_requests = 0
            
            for response in responses:
                if isinstance(response, Exception):
                    failed_requests += 1
                else:
                    if response.status == 200:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                    response.close()
            
            success_rate = successful_requests / len(responses)
            
            if success_rate >= 0.9:  # 90% success rate
                return {
                    "status": "pass",
                    "message": f"Load test passed: {success_rate:.1%} success rate",
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "total_time": total_time,
                    "success_rate": success_rate
                }
            else:
                return {
                    "status": "warning",
                    "message": f"Load test degraded: {success_rate:.1%} success rate",
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "total_time": total_time,
                    "success_rate": success_rate
                }
                
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Load test failed: {e}",
                "error": str(e)
            }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run the complete production readiness test suite."""
        logger.info("🚀 Starting Production Readiness Test Suite")
        logger.info("=" * 60)
        
        # Test 1: Dashboard Accessibility
        await self.run_test("Dashboard Accessibility", self.test_dashboard_accessibility)
        
        # Test 2: System Health Monitoring
        await self.run_test("System Health Endpoint", self.test_system_health_endpoint)
        
        # Test 3: Data Flow Continuity
        await self.run_test("Data Flow Continuity", self.test_data_flow_continuity)
        
        # Test 4: Strategy Management
        await self.run_test("Strategy Management", self.test_strategy_management)
        
        # Test 5: API Load Testing
        await self.run_test("API Load Testing", self.test_api_endpoints_load)
        
        return self.test_results
    
    def print_summary(self):
        """Print test results summary."""
        logger.info("\n📊 Production Readiness Test Summary")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {self.test_results['total_tests']}")
        logger.info(f"Passed: {self.test_results['passed']}")
        logger.info(f"Warnings: {self.test_results['warnings']}")
        logger.info(f"Failed: {self.test_results['failed']}")
        
        success_rate = self.test_results['passed'] / self.test_results['total_tests']
        logger.info(f"Success Rate: {success_rate:.1%}")
        
        if self.test_results['failed'] == 0:
            if self.test_results['warnings'] == 0:
                logger.info("🎉 ALL TESTS PASSED - PRODUCTION READY!")
            else:
                logger.info("✅ TESTS PASSED WITH WARNINGS - REVIEW RECOMMENDED")
        else:
            logger.error("🚨 SOME TESTS FAILED - REQUIRES FIXES BEFORE PRODUCTION")

async def main():
    """Main test runner."""
    logger.info("🚀 Production Readiness Test Suite")
    logger.info("⚠️ Make sure the dashboard is running on localhost:8082")
    logger.info("")
    
    # Wait for user confirmation
    try:
        input("Press Enter when dashboard is ready, or Ctrl+C to cancel...")
    except KeyboardInterrupt:
        logger.info("Test cancelled by user")
        return 1
    
    async with ProductionReadinessTest() as tester:
        results = await tester.run_all_tests()
        tester.print_summary()
        
        # Save results
        results_file = f"production_readiness_results_{int(time.time())}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"📄 Results saved to: {results_file}")
        
        # Return appropriate exit code
        if results['failed'] > 0:
            return 1
        else:
            return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
