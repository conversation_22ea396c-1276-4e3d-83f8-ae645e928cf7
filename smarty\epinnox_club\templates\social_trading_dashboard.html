{% extends "base.html" %}

{% block title %}Social Trading - Money Circle{% endblock %}

{% block extra_css %}
<style>
    .social-trading-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        min-height: 100vh;
    }

    .social-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .social-header h1 {
        color: #ffffff;
        font-size: 2.5rem;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #4CAF50, #2196F3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .social-header p {
        color: #b0b0b0;
        font-size: 1.1rem;
    }

    .social-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .social-section {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .section-title {
        color: #ffffff;
        font-size: 1.4rem;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-title .icon {
        font-size: 1.6rem;
    }

    .strategy-card {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
    }

    .strategy-card:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(76, 175, 80, 0.3);
        transform: translateY(-2px);
    }

    .strategy-title {
        color: #ffffff;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .strategy-creator {
        color: #4CAF50;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .strategy-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .stat-item {
        text-align: center;
    }

    .stat-value {
        color: #ffffff;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .stat-label {
        color: #b0b0b0;
        font-size: 0.8rem;
    }

    .follow-btn, .unfollow-btn {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .unfollow-btn {
        background: linear-gradient(45deg, #f44336, #d32f2f);
    }

    .follow-btn:hover, .unfollow-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 3px solid #4CAF50;
    }

    .activity-icon {
        font-size: 1.5rem;
        width: 40px;
        text-align: center;
    }

    .activity-content {
        flex: 1;
    }

    .activity-text {
        color: #ffffff;
        margin-bottom: 5px;
    }

    .activity-time {
        color: #b0b0b0;
        font-size: 0.8rem;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }

    .metric-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .metric-value {
        color: #4CAF50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .metric-label {
        color: #b0b0b0;
        font-size: 0.9rem;
    }

    .leaderboard-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 8px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .leaderboard-item:hover {
        background: rgba(255, 255, 255, 0.08);
    }

    .rank {
        color: #FFD700;
        font-size: 1.2rem;
        font-weight: 700;
        width: 30px;
        text-align: center;
    }

    .trader-info {
        flex: 1;
    }

    .trader-name {
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .trader-return {
        color: #4CAF50;
        font-size: 0.9rem;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #b0b0b0;
    }

    .empty-state .icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .social-grid {
            grid-template-columns: 1fr;
        }
        
        .metrics-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .social-header h1 {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="social-trading-container">
    <!-- Header -->
    <div class="social-header">
        <h1>👥 Social Trading Hub</h1>
        <p>Connect, follow, and learn from top traders in the Money Circle community</p>
    </div>

    <!-- User Metrics -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value" id="followers-count">0</div>
            <div class="metric-label">Followers</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="following-count">0</div>
            <div class="metric-label">Following</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="strategies-shared">0</div>
            <div class="metric-label">Strategies Shared</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="reputation-score">0</div>
            <div class="metric-label">Reputation Score</div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="social-grid">
        <!-- Top Performers -->
        <div class="social-section">
            <h2 class="section-title">
                <span class="icon">🏆</span>
                Top Performers
            </h2>
            <div id="top-performers">
                <div class="empty-state">
                    <div class="icon">📊</div>
                    <p>Loading top performers...</p>
                </div>
            </div>
        </div>

        <!-- Strategy Leaderboard -->
        <div class="social-section">
            <h2 class="section-title">
                <span class="icon">📈</span>
                Strategy Leaderboard
            </h2>
            <div id="strategy-leaderboard">
                <div class="empty-state">
                    <div class="icon">🎯</div>
                    <p>Loading strategy rankings...</p>
                </div>
            </div>
        </div>

        <!-- Followed Strategies -->
        <div class="social-section">
            <h2 class="section-title">
                <span class="icon">👁️</span>
                Followed Strategies
            </h2>
            <div id="followed-strategies">
                <div class="empty-state">
                    <div class="icon">💼</div>
                    <p>You're not following any strategies yet</p>
                </div>
            </div>
        </div>

        <!-- Community Activity -->
        <div class="social-section">
            <h2 class="section-title">
                <span class="icon">🌟</span>
                Community Activity
            </h2>
            <div id="community-activity">
                <div class="empty-state">
                    <div class="icon">💬</div>
                    <p>Loading community activity...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Social trading data (passed from backend)
const socialData = {{ social_data|safe }};

// Initialize social trading dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeSocialDashboard();
});

function initializeSocialDashboard() {
    try {
        const data = typeof socialData === 'string' ? JSON.parse(socialData) : socialData;
        
        // Update metrics
        updateMetrics(data.social_metrics || {});
        
        // Update sections
        updateTopPerformers(data.top_performers || []);
        updateStrategyLeaderboard(data.strategy_leaderboard || []);
        updateFollowedStrategies(data.followed_strategies || []);
        updateCommunityActivity(data.community_activity || []);
        
        console.log('Social trading dashboard initialized');
    } catch (error) {
        console.error('Error initializing social dashboard:', error);
        showErrorState();
    }
}

function updateMetrics(metrics) {
    document.getElementById('followers-count').textContent = metrics.followers || 0;
    document.getElementById('following-count').textContent = metrics.following || 0;
    document.getElementById('strategies-shared').textContent = metrics.strategies_shared || 0;
    document.getElementById('reputation-score').textContent = metrics.reputation_score || 0;
}

function updateTopPerformers(performers) {
    const container = document.getElementById('top-performers');
    
    if (!performers || performers.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="icon">📊</div>
                <p>No top performers data available</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = performers.map(performer => `
        <div class="leaderboard-item">
            <div class="rank">#${performer.rank || '?'}</div>
            <div class="trader-info">
                <div class="trader-name">${performer.username || 'Unknown'}</div>
                <div class="trader-return">+${performer.total_return || 0}%</div>
            </div>
        </div>
    `).join('');
}

function updateStrategyLeaderboard(strategies) {
    const container = document.getElementById('strategy-leaderboard');
    
    if (!strategies || strategies.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="icon">🎯</div>
                <p>No strategies available</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = strategies.map((strategy, index) => `
        <div class="strategy-card">
            <div class="strategy-title">${strategy.title || 'Untitled Strategy'}</div>
            <div class="strategy-creator">by ${strategy.creator || 'Unknown'}</div>
            <div class="strategy-stats">
                <div class="stat-item">
                    <div class="stat-value">${strategy.followers || 0}</div>
                    <div class="stat-label">Followers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${strategy.return_rate || 0}%</div>
                    <div class="stat-label">Return</div>
                </div>
            </div>
        </div>
    `).join('');
}

function updateFollowedStrategies(strategies) {
    const container = document.getElementById('followed-strategies');
    
    if (!strategies || strategies.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="icon">💼</div>
                <p>You're not following any strategies yet</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = strategies.map(strategy => `
        <div class="strategy-card">
            <div class="strategy-title">${strategy.title || 'Untitled Strategy'}</div>
            <div class="strategy-creator">by ${strategy.creator || 'Unknown'}</div>
            <button class="unfollow-btn" onclick="unfollowStrategy(${strategy.id})">
                Unfollow
            </button>
        </div>
    `).join('');
}

function updateCommunityActivity(activities) {
    const container = document.getElementById('community-activity');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="icon">💬</div>
                <p>No recent community activity</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">${activity.icon || '📈'}</div>
            <div class="activity-content">
                <div class="activity-text">
                    <strong>${activity.username || 'Unknown'}</strong> ${activity.content || 'performed an action'}
                </div>
                <div class="activity-time">${formatTime(activity.timestamp)}</div>
            </div>
        </div>
    `).join('');
}

function formatTime(timestamp) {
    if (!timestamp) return 'Unknown time';
    try {
        const date = new Date(timestamp);
        return date.toLocaleString();
    } catch (error) {
        return 'Invalid time';
    }
}

function unfollowStrategy(strategyId) {
    // TODO: Implement unfollow functionality
    console.log('Unfollow strategy:', strategyId);
}

function showErrorState() {
    document.querySelectorAll('.social-section').forEach(section => {
        const content = section.querySelector('div:last-child');
        content.innerHTML = `
            <div class="empty-state">
                <div class="icon">⚠️</div>
                <p>Error loading data</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
