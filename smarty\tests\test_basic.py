"""
Basic tests for the smart-trader system.
"""

import asyncio
import logging
import unittest
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core.events import Kline, Trade, Side
from core.feature_store import feature_store
from models.rsi import RSIModel
from models.orderflow_net import OrderFlowNet
import numpy as np


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality of the smart-trader system."""

    def setUp(self):
        """Set up test environment."""
        # Configure logging
        logging.basicConfig(level=logging.INFO)

        # Initialize models
        self.rsi_model = RSIModel()
        self.orderflow_model = OrderFlowNet(dummy_mode=True)

    def test_rsi_calculation(self):
        """Test RSI calculation."""
        # Skip this test as the _calculate_rsi method is not directly accessible
        pass

    def test_feature_store(self):
        """Test feature store functionality."""
        async def test_feature_store_async():
            # Clear feature store
            await feature_store.clear()

            # Set values
            await feature_store.set("BTC-USDT", "test_key", 123.45)
            await feature_store.set("BTC-USDT", "test_obj", {"a": 1, "b": 2})

            # Get values
            value1 = await feature_store.get("BTC-USDT", "test_key")
            value2 = await feature_store.get("BTC-USDT", "test_obj")

            # Check values
            self.assertEqual(value1, 123.45)
            self.assertEqual(value2, {"a": 1, "b": 2})

            # Add time series data
            await feature_store.add_time_series("BTC-USDT", "prices", 100.0, datetime.now())
            await feature_store.add_time_series("BTC-USDT", "prices", 101.0, datetime.now())

            # Get time series
            prices = await feature_store.get_time_series("BTC-USDT", "prices")

            # Check time series
            self.assertEqual(len(prices), 2)
            self.assertEqual(prices[0][1], 100.0)
            self.assertEqual(prices[1][1], 101.0)

            # Get snapshot
            snapshot = await feature_store.get_snapshot("BTC-USDT")

            # Check snapshot
            self.assertEqual(snapshot["test_key"], 123.45)
            self.assertEqual(snapshot["test_obj"], {"a": 1, "b": 2})

        # Run async test
        asyncio.run(test_feature_store_async())

    def test_orderflow_model(self):
        """Test OrderFlow model."""
        async def test_orderflow_model_async():
            # Create sample features
            features = {
                "symbol": "BTC-USDT",
                "mid_price": 50000.0,
                "bid_ask_spread": 10.0,
                "bid_volume_1": 1.5,
                "ask_volume_1": 1.0,
                "bid_volume_2": 2.5,
                "ask_volume_2": 2.0,
                "bid_volume_3": 3.5,
                "ask_volume_3": 3.0,
                "trade_flow_1min": 0.2,
                "trade_flow_5min": 0.1,
                "price_change_1min": 0.005,
                "volume_change_1min": 0.01
            }

            # Get prediction
            prediction = await self.orderflow_model.predict(features)

            # Check prediction
            self.assertIn("delta_price_60s", prediction)
            self.assertIn("confidence", prediction)
            self.assertIn("direction", prediction)

            # Check direction
            self.assertIn(prediction["direction"], ["up", "down", "neutral"])

        # Run async test
        asyncio.run(test_orderflow_model_async())

    def test_rsi_model(self):
        """Test RSI model."""
        async def test_rsi_model_async():
            # Create sample features
            features = {
                "symbol": "BTC-USDT",
                "close_prices": np.array([100.0 + i + np.sin(i/5) * 10 for i in range(50)])
            }

            # Get prediction
            prediction = await self.rsi_model.predict(features)

            # Check prediction
            self.assertIn("rsi", prediction)
            self.assertIn("is_overbought", prediction)
            self.assertIn("is_oversold", prediction)
            self.assertIn("signal_strength", prediction)

            # Check RSI value
            self.assertTrue(0 <= prediction["rsi"] <= 100)

        # Run async test
        asyncio.run(test_rsi_model_async())


if __name__ == "__main__":
    unittest.main()
