/* Money Circle Browser Fallbacks - Cross-Browser Compatibility */

/* ========================================
   CSS Grid Fallbacks for Internet Explorer
   ======================================== */

/* Dashboard Grid Fallback */
.dashboard-grid {
    /* IE11 Flexbox fallback */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: -15px; /* Negative margin for gap simulation */
}

.dashboard-grid > * {
    /* IE11 flex item sizing */
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    margin: 15px; /* Simulate grid gap */
    min-width: 0; /* Prevent flex item overflow */
}

/* Two-column layout for larger screens */
@media (min-width: 768px) {
    .dashboard-grid > * {
        -ms-flex: 1 1 calc(50% - 30px);
        flex: 1 1 calc(50% - 30px);
    }
    
    /* Full-width items */
    .portfolio-overview,
    .market-data,
    .performance-analytics {
        -ms-flex: 1 1 calc(100% - 30px);
        flex: 1 1 calc(100% - 30px);
    }
}

/* Portfolio Cards Fallback */
.portfolio-cards {
    /* IE11 Flexbox fallback */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: -10px;
}

.portfolio-card {
    /* IE11 flex item sizing */
    -ms-flex: 1 1 250px;
    flex: 1 1 250px;
    margin: 10px;
    min-width: 250px;
}

/* Market Widgets Fallback */
.market-widgets {
    /* IE11 Flexbox fallback */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: -10px;
}

.market-widget {
    /* IE11 flex item sizing */
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    margin: 10px;
}

@media (min-width: 768px) {
    .market-widget {
        -ms-flex: 1 1 calc(50% - 20px);
        flex: 1 1 calc(50% - 20px);
    }
}

@media (min-width: 1024px) {
    .market-widget:first-child {
        -ms-flex: 2 1 calc(50% - 20px);
        flex: 2 1 calc(50% - 20px);
    }
    
    .market-widget:not(:first-child) {
        -ms-flex: 1 1 calc(25% - 20px);
        flex: 1 1 calc(25% - 20px);
    }
}

/* ========================================
   CSS Custom Properties Fallbacks
   ======================================== */

/* Color Fallbacks for IE11 */
.portfolio-overview h2,
.exchange-connections h2,
.trading-interface h2,
.market-data h2,
.performance-analytics h2 {
    color: #FFD700; /* Fallback for var(--warning-400) */
}

.portfolio-card,
.exchange-card,
.market-widget,
.analytics-card {
    background: rgba(255, 255, 255, 0.05); /* Fallback for var(--bg-card) */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Fallback for var(--border-primary) */
}

.btn-primary {
    background: #8b5cf6; /* Fallback for var(--primary-600) */
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.05); /* Fallback for var(--bg-card) */
    color: #e2e8f0; /* Fallback for var(--text-secondary) */
}

/* Text Color Fallbacks */
.text-primary { color: #f1f5f9; }
.text-secondary { color: #e2e8f0; }
.text-tertiary { color: #94a3b8; }
.text-muted { color: #64748b; }

/* ========================================
   Backdrop Filter Fallbacks
   ======================================== */

/* Fallback for backdrop-filter (Firefox < 103, IE) */
.portfolio-overview,
.exchange-connections,
.trading-interface,
.market-data,
.performance-analytics {
    /* Enhanced background for browsers without backdrop-filter */
    background: rgba(0, 0, 0, 0.4);
}

/* Progressive enhancement for backdrop-filter */
@supports (backdrop-filter: blur(10px)) {
    .portfolio-overview,
    .exchange-connections,
    .trading-interface,
    .market-data,
    .performance-analytics {
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
    }
}

/* ========================================
   Flexbox Prefix Fallbacks
   ======================================== */

/* IE10/11 Flexbox prefixes */
.flex {
    display: -ms-flexbox;
    display: flex;
}

.flex-col {
    -ms-flex-direction: column;
    flex-direction: column;
}

.items-center {
    -ms-flex-align: center;
    align-items: center;
}

.justify-center {
    -ms-flex-pack: center;
    justify-content: center;
}

.justify-between {
    -ms-flex-pack: justify;
    justify-content: space-between;
}

/* ========================================
   Transform Prefix Fallbacks
   ======================================== */

/* Vendor prefixes for transforms */
.portfolio-card:hover,
.analytics-card:hover,
.exchange-card:hover {
    -webkit-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    transform: translateY(-2px);
}

.place-order-btn:hover {
    -webkit-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    transform: translateY(-2px);
}

/* ========================================
   Media Query Fallbacks
   ======================================== */

/* Fallback for hover media queries */
.portfolio-card:hover,
.analytics-card:hover,
.exchange-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 215, 0, 0.3);
}

/* IE11 doesn't support @media (hover: hover) */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .portfolio-card:hover,
    .analytics-card:hover,
    .exchange-card:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 215, 0, 0.3);
        -ms-transform: translateY(-2px);
        transform: translateY(-2px);
    }
}

/* ========================================
   Viewport Units Fallbacks
   ======================================== */

/* IE11 viewport unit fallbacks */
body {
    min-height: 100vh; /* Modern browsers */
    min-height: 100%; /* IE fallback */
}

/* IE11 specific fixes */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    html, body {
        height: 100%;
    }
    
    body {
        min-height: 100%;
    }
}

/* ========================================
   Progressive Enhancement
   ======================================== */

/* Feature detection for modern browsers */
@supports (display: grid) {
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--mobile-gap, 15px);
        margin: 0; /* Reset flexbox margin */
    }
    
    .dashboard-grid > * {
        margin: 0; /* Reset flexbox margin */
    }
    
    @media (min-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
    }
}

@supports (--css: variables) {
    /* Use CSS custom properties when supported */
    .portfolio-overview h2,
    .exchange-connections h2,
    .trading-interface h2,
    .market-data h2,
    .performance-analytics h2 {
        color: var(--warning-400);
    }
    
    .portfolio-card,
    .exchange-card,
    .market-widget,
    .analytics-card {
        background: var(--bg-card);
        border: 1px solid var(--border-primary);
    }
}

/* ========================================
   Print Styles
   ======================================== */

@media print {
    /* Ensure compatibility across browsers for printing */
    .dashboard-grid {
        display: block !important;
    }
    
    .portfolio-card,
    .exchange-card,
    .market-widget {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    /* Hide interactive elements in print */
    .btn,
    .tab-btn,
    .place-order-btn {
        display: none !important;
    }
}
