# 🚀 Epinnox Smart Trading Dashboard - Production Deployment Guide

## 📋 Quick Start

### **1. Start the Dashboard**
```bash
cd smarty
python live_dashboard.py
```

### **2. Access the Dashboard**
- **URL**: http://localhost:8082/dashboard
- **Username**: epinnox
- **Password**: securepass123

### **3. Monitor System Health**
- **Health Endpoint**: http://localhost:8082/api/health/system
- **Dashboard**: Real-time component monitoring

## 🎯 Verified Trading Strategies

### **Available Strategies (All Production-Ready)**

1. **Smart Model Integrated**
   - **Description**: Full system with LLM integration
   - **Command**: `python orchestrator.py --debug`
   - **Features**: Advanced AI analysis, multi-model fusion
   - **Status**: ✅ Verified working

2. **Smart Strategy Only**
   - **Description**: Technical analysis only
   - **Command**: `python run_smart_strategy_live.py`
   - **Features**: RSI, MACD, Bollinger Bands analysis
   - **Status**: ✅ Verified working

3. **Order Flow**
   - **Description**: DataFrame-based analysis
   - **Command**: `python live_dataframe_strategy_runner.py`
   - **Features**: Real-time market analysis, signal generation
   - **Status**: ✅ Verified working

## 🔧 System Architecture

### **Core Components**
```
Dashboard (live_dashboard.py)
├── Authentication System (Epinnox credentials)
├── Strategy Manager (3 verified strategies)
├── Data Producer (Auto-start with fallback)
├── Health Monitor (Component-level monitoring)
├── Circuit Breakers (Production-grade error handling)
└── WebSocket (Real-time updates)
```

### **Data Flow**
```
HTX Exchange → Binance Fallback → Multi-Exchange → SQLite Bus → Strategies → Dashboard
```

## 📊 Production Features

### **✅ Auto-Start Data Producer**
- Automatically launches HTX data producer on dashboard startup
- Fallback to Binance if HTX is geo-blocked
- Process monitoring and automatic restart
- Clean shutdown on dashboard termination

### **✅ Health Monitoring**
- **Endpoint**: `/api/health/system`
- **Components Monitored**:
  - Exchange connectivity
  - Data producer process health
  - Strategy process validation
  - Database performance
  - Data flow continuity

### **✅ Circuit Breaker Protection**
- Fail-fast patterns for external dependencies
- Exponential backoff with configurable limits
- Automatic recovery testing
- Service-specific configurations

### **✅ Multi-Exchange Fallback**
- Primary: HTX WebSocket
- Secondary: Binance WebSocket
- Additional: OKX, Bybit, Gate.io
- Seamless failover on geo-blocking

## 🎯 Trading Operations

### **Starting a Strategy**
1. Access dashboard at http://localhost:8082/dashboard
2. Login with Epinnox credentials
3. Select strategy from dropdown
4. Click "Start Strategy"
5. Monitor real-time performance

### **Strategy Management**
- **Mutual Exclusion**: Only one strategy runs at a time
- **Real-time Monitoring**: Live performance metrics
- **Clean Shutdown**: Proper process termination
- **Error Recovery**: Automatic restart on failure

### **Live Market Data**
- **Real-time Processing**: 1-second intervals
- **Technical Analysis**: RSI, MACD, Bollinger Bands
- **Signal Generation**: Confidence-based decisions
- **Market Sentiment**: AI-powered analysis

## 🔒 Security & Authentication

### **Epinnox Investment Club Access**
- **Username**: epinnox
- **Password**: securepass123
- **Session Management**: Secure session-based auth
- **API Protection**: All endpoints require authentication

### **Security Features**
- Professional login screen
- Session timeout management
- API endpoint protection
- Secure credential handling

## 📈 Monitoring & Maintenance

### **Health Check Endpoints**
```bash
# System health
curl http://localhost:8082/api/health/system

# Strategy status
curl http://localhost:8082/api/strategy/status

# Market data
curl http://localhost:8082/api/market-data
```

### **Log Monitoring**
- **Dashboard Logs**: Real-time in console
- **Strategy Logs**: Forwarded with [STRATEGY:name] prefix
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Response times and success rates

### **Process Management**
```bash
# Check dashboard process
ps aux | grep live_dashboard.py

# Check strategy processes
ps aux | grep -E "(orchestrator|run_smart_strategy|live_dataframe)"

# Monitor system resources
top -p $(pgrep -f live_dashboard.py)
```

## 🚨 Troubleshooting

### **Common Issues & Solutions**

#### **Dashboard Won't Start**
```bash
# Check port availability
netstat -an | grep 8082

# Check Python dependencies
pip install -r requirements.txt

# Check database permissions
ls -la data/bus.db
```

#### **Strategy Fails to Start**
```bash
# Check strategy script exists
ls -la orchestrator.py run_smart_strategy_live.py live_dataframe_strategy_runner.py

# Test strategy manually
python orchestrator.py --debug

# Check SQLite bus
sqlite3 data/bus.db ".tables"
```

#### **No Market Data**
```bash
# Check data producer
ps aux | grep htx_data_producer

# Test exchange connectivity
python -c "import requests; print(requests.get('https://api.htx.pro/market/detail/merged?symbol=btcusdt').status_code)"

# Check fallback to Binance
python -c "import requests; print(requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT').status_code)"
```

### **Performance Optimization**
- **Database**: SQLite WAL mode enabled for better performance
- **Memory**: Efficient data structures and cleanup
- **CPU**: Optimized algorithms and caching
- **Network**: Connection pooling and retry mechanisms

## 📋 Production Checklist

### **Pre-Deployment**
- [x] All strategies tested and verified
- [x] Health monitoring operational
- [x] Authentication system configured
- [x] Data sources validated
- [x] Error handling tested
- [x] Performance benchmarked

### **Post-Deployment**
- [ ] Monitor system health for 24 hours
- [ ] Validate strategy performance
- [ ] Check data flow continuity
- [ ] Review error logs
- [ ] Confirm backup procedures
- [ ] Document any issues

## 🎯 Success Metrics

### **System Performance**
- **Uptime**: Target 99.9%
- **Response Time**: < 2 seconds for all endpoints
- **Error Rate**: < 0.1% for critical functions
- **Data Latency**: < 5 seconds for market data

### **Trading Performance**
- **Strategy Startup**: < 1 second
- **Signal Generation**: Real-time processing
- **Market Analysis**: Multi-indicator approach
- **Risk Management**: Confidence-based decisions

## 🚀 Next Steps

### **Immediate Actions**
1. **Deploy to production environment**
2. **Configure monitoring alerts**
3. **Set up backup procedures**
4. **Train operators on dashboard usage**

### **Future Enhancements**
1. **Additional trading pairs**
2. **Enhanced LLM integration**
3. **Advanced risk management**
4. **Portfolio management features**

## 📞 Support

### **System Status**
- **Health Dashboard**: http://localhost:8082/api/health/system
- **Real-time Monitoring**: Dashboard interface
- **Log Analysis**: Console output and log files

### **Emergency Procedures**
1. **Stop all strategies**: Use dashboard stop button
2. **Restart system**: `Ctrl+C` then restart `python live_dashboard.py`
3. **Check system health**: Access health endpoint
4. **Review logs**: Check console output for errors

---

**Deployment Guide Version**: 1.0  
**Last Updated**: May 30, 2025  
**Status**: ✅ **PRODUCTION READY**
