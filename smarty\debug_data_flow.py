#!/usr/bin/env python3
"""
Debug script to observe the complete data fetching process.
This script will help us identify where the data flow is breaking.
"""

import asyncio
import json
import time
import sqlite3
from datetime import datetime
from feeds.htx_futures import HTXFuturesClient
from pipeline.databus import SQLiteBus
import logging

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataFlowDebugger:
    """Debug the complete data flow from HTX WebSocket to SQLite bus."""

    def __init__(self):
        self.htx_client = None
        self.bus = None
        self.message_count = 0
        self.last_message_time = None

    async def setup(self):
        """Set up HTX client and SQLite bus."""
        logger.info("🔧 Setting up HTX client and SQLite bus...")

        # Initialize HTX client
        self.htx_client = HTXFuturesClient(testnet=False)
        self.htx_client.simulation_mode = True

        # Initialize SQLite bus
        self.bus = SQLiteBus(path="data/debug_bus.db", poll_interval=0.5)

        # Set publisher for HTX client
        self.htx_client.set_publisher(self.bus.publish)

        logger.info("✅ Setup complete")

    async def test_websocket_connection(self):
        """Test basic WebSocket connection."""
        logger.info("🌐 Testing WebSocket connection...")

        try:
            await self.htx_client.connect()
            logger.info("✅ WebSocket connected successfully")
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket connection failed: {e}")
            return False

    async def test_subscriptions(self):
        """Test market data subscriptions."""
        logger.info("📡 Testing market data subscriptions...")

        symbol = "BTC-USDT"
        subscriptions = []

        # Test kline subscription
        kline_channel = self.htx_client.CHANNEL_KLINE.format(symbol=symbol, interval="1s")
        logger.info(f"📊 Subscribing to klines: {kline_channel}")
        if await self.htx_client.subscribe(kline_channel):
            subscriptions.append("kline")
            logger.info("✅ Kline subscription successful")
        else:
            logger.error("❌ Kline subscription failed")

        # Test trade subscription
        trade_channel = self.htx_client.CHANNEL_TRADE.format(symbol=symbol)
        logger.info(f"💱 Subscribing to trades: {trade_channel}")
        if await self.htx_client.subscribe(trade_channel):
            subscriptions.append("trade")
            logger.info("✅ Trade subscription successful")
        else:
            logger.error("❌ Trade subscription failed")

        # Test depth subscription
        depth_channel = self.htx_client.CHANNEL_DEPTH.format(symbol=symbol)
        logger.info(f"📈 Subscribing to depth: {depth_channel}")
        if await self.htx_client.subscribe(depth_channel):
            subscriptions.append("depth")
            logger.info("✅ Depth subscription successful")
        else:
            logger.error("❌ Depth subscription failed")

        logger.info(f"📋 Total successful subscriptions: {len(subscriptions)}")
        return subscriptions

    async def monitor_raw_websocket(self, duration=30):
        """Monitor raw WebSocket messages."""
        logger.info(f"👁️ Monitoring raw WebSocket messages for {duration} seconds...")

        start_time = time.time()
        message_count = 0

        while time.time() - start_time < duration:
            try:
                # Check if WebSocket is still connected
                if not self.htx_client.market_ws or self.htx_client.market_ws.closed:
                    logger.error("❌ WebSocket connection lost")
                    break

                # Try to receive a message with timeout
                try:
                    msg = await asyncio.wait_for(
                        self.htx_client.market_ws.receive(),
                        timeout=1.0
                    )

                    if msg.type == msg.type.TEXT:
                        data = json.loads(msg.data)
                        message_count += 1

                        # Log different message types
                        if "ping" in data:
                            logger.debug("🏓 Received ping")
                        elif "pong" in data:
                            logger.debug("🏓 Received pong")
                        elif "subbed" in data:
                            logger.info(f"✅ Subscription confirmed: {data}")
                        elif "ch" in data and "tick" in data:
                            logger.info(f"📊 Market data received: {data['ch']}")
                            logger.debug(f"   Data: {data}")
                        else:
                            logger.info(f"❓ Unknown message: {data}")

                    elif msg.type == msg.type.BINARY:
                        logger.info("📦 Received binary message (compressed)")
                        message_count += 1

                except asyncio.TimeoutError:
                    # No message received in 1 second, continue
                    pass

            except Exception as e:
                logger.error(f"❌ Error monitoring WebSocket: {e}")
                break

        logger.info(f"📊 Raw WebSocket monitoring complete. Received {message_count} messages")
        return message_count

    async def monitor_parsed_messages(self, duration=30):
        """Monitor parsed messages from HTX client queue."""
        logger.info(f"🔍 Monitoring parsed messages for {duration} seconds...")

        start_time = time.time()
        message_count = 0

        while time.time() - start_time < duration:
            try:
                # Get next message from HTX client queue
                message = await self.htx_client.get_next_market_message(timeout=1.0)

                if message:
                    message_count += 1
                    logger.info(f"📨 Parsed message #{message_count}: {type(message).__name__}")
                    logger.debug(f"   Content: {message}")

            except asyncio.TimeoutError:
                # No message received, continue
                pass
            except Exception as e:
                logger.error(f"❌ Error getting parsed message: {e}")
                break

        logger.info(f"📊 Parsed message monitoring complete. Received {message_count} messages")
        return message_count

    async def monitor_database(self, duration=30):
        """Monitor SQLite database for new messages."""
        logger.info(f"🗄️ Monitoring SQLite database for {duration} seconds...")

        start_time = time.time()
        initial_count = self.get_db_message_count()

        while time.time() - start_time < duration:
            await asyncio.sleep(1)
            current_count = self.get_db_message_count()
            new_messages = current_count - initial_count

            if new_messages > 0:
                logger.info(f"📊 Database has {new_messages} new messages (total: {current_count})")
                self.show_recent_db_messages()

        final_count = self.get_db_message_count()
        total_new = final_count - initial_count
        logger.info(f"📊 Database monitoring complete. Added {total_new} new messages")
        return total_new

    def get_db_message_count(self):
        """Get total message count from database."""
        try:
            conn = sqlite3.connect("data/debug_bus.db")
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM messages")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"❌ Error getting DB count: {e}")
            return 0

    def show_recent_db_messages(self, limit=5):
        """Show recent messages from database."""
        try:
            conn = sqlite3.connect("data/debug_bus.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT stream, datetime(ts, 'unixepoch') as time, payload
                FROM messages
                ORDER BY ts DESC
                LIMIT ?
            """, (limit,))

            messages = cursor.fetchall()
            for stream, time, payload in messages:
                logger.info(f"   📝 {time} | {stream} | {payload[:100]}...")
            conn.close()
        except Exception as e:
            logger.error(f"❌ Error showing DB messages: {e}")

    async def run_complete_test(self):
        """Run complete data flow test."""
        logger.info("🚀 Starting complete data flow test...")

        # Setup
        await self.setup()

        # Test connection
        if not await self.test_websocket_connection():
            return

        # Test subscriptions
        subscriptions = await self.test_subscriptions()
        if not subscriptions:
            logger.error("❌ No successful subscriptions, aborting test")
            return

        # Wait a moment for subscriptions to be processed
        logger.info("⏳ Waiting 5 seconds for subscriptions to be processed...")
        await asyncio.sleep(5)

        # Monitor different levels of the data flow
        logger.info("🔍 Starting multi-level monitoring...")

        # Create monitoring tasks
        tasks = [
            asyncio.create_task(self.monitor_raw_websocket(30)),
            asyncio.create_task(self.monitor_parsed_messages(30)),
            asyncio.create_task(self.monitor_database(30))
        ]

        # Run all monitoring tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Report results
        logger.info("📊 FINAL RESULTS:")
        logger.info(f"   Raw WebSocket messages: {results[0] if not isinstance(results[0], Exception) else 'ERROR'}")
        logger.info(f"   Parsed messages: {results[1] if not isinstance(results[1], Exception) else 'ERROR'}")
        logger.info(f"   Database messages: {results[2] if not isinstance(results[2], Exception) else 'ERROR'}")

        # Show final database state
        self.show_db_summary()

        # Cleanup
        await self.cleanup()

    def show_db_summary(self):
        """Show database summary."""
        try:
            conn = sqlite3.connect("data/debug_bus.db")
            cursor = conn.cursor()

            # Get stream counts
            cursor.execute("SELECT stream, COUNT(*) as count FROM messages GROUP BY stream ORDER BY count DESC")
            streams = cursor.fetchall()

            logger.info("📊 DATABASE SUMMARY:")
            if streams:
                for stream, count in streams:
                    logger.info(f"   {stream}: {count} messages")
            else:
                logger.info("   No messages in database")

            conn.close()
        except Exception as e:
            logger.error(f"❌ Error showing DB summary: {e}")

    async def cleanup(self):
        """Clean up resources."""
        logger.info("🧹 Cleaning up...")

        if self.htx_client:
            await self.htx_client.disconnect()

        if self.bus:
            self.bus.close()

        logger.info("✅ Cleanup complete")

async def main():
    """Main function."""
    debugger = DataFlowDebugger()
    await debugger.run_complete_test()

if __name__ == "__main__":
    # Fix for Windows: Use SelectorEventLoop instead of ProactorEventLoop
    import sys
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
