#!/usr/bin/env python
"""
Analyze End-to-End Test Results for Smart-Trader System with Phi-3.1 Mini

This script analyzes the results of the end-to-end test for the smart-trader system
using Phi-3.1 Mini for LLM inference and real market data.
"""

import os
import sys
import json
import glob
import argparse
import logging
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("phi-e2e-test-analyzer")

def find_latest_results():
    """Find the latest test results file."""
    results_files = glob.glob("logs/phi_e2e_test_results_*.json")
    if not results_files:
        logger.error("No test results found")
        return None
    
    # Sort by modification time (newest first)
    results_files.sort(key=os.path.getmtime, reverse=True)
    return results_files[0]

def load_results(results_path):
    """Load test results from a file."""
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        return results
    except Exception as e:
        logger.error(f"Error loading results: {e}")
        return None

def analyze_results(results):
    """Analyze test results."""
    if not results:
        logger.error("No results to analyze")
        return
    
    # Extract metrics
    metrics = results.get("metrics", {})
    llm_responses = results.get("llm_responses", [])
    
    # Print summary
    logger.info("=" * 50)
    logger.info("Test Results Summary")
    logger.info("=" * 50)
    logger.info(f"Success: {results.get('success', False)}")
    logger.info(f"Message: {results.get('message', '')}")
    logger.info("-" * 50)
    logger.info("Metrics:")
    logger.info(f"  Klines received: {metrics.get('klines_received', 0)}")
    logger.info(f"  Trades received: {metrics.get('trades_received', 0)}")
    logger.info(f"  Orderbook updates: {metrics.get('orderbook_updates', 0)}")
    logger.info(f"  Signals generated: {metrics.get('signals_generated', 0)}")
    logger.info(f"  LLM calls: {metrics.get('llm_calls', 0)}")
    
    # Calculate LLM response time statistics
    llm_response_times = metrics.get("llm_response_time", [])
    if llm_response_times:
        avg_response_time = sum(llm_response_times) / len(llm_response_times)
        min_response_time = min(llm_response_times)
        max_response_time = max(llm_response_times)
        
        logger.info(f"  Average LLM response time: {avg_response_time:.2f}s")
        logger.info(f"  Minimum LLM response time: {min_response_time:.2f}s")
        logger.info(f"  Maximum LLM response time: {max_response_time:.2f}s")
    
    logger.info(f"  Errors: {metrics.get('errors', 0)}")
    logger.info(f"  Warnings: {metrics.get('warnings', 0)}")
    logger.info("-" * 50)
    
    # Analyze LLM responses
    if llm_responses:
        logger.info("LLM Responses:")
        
        # Count actions
        actions = {}
        for response in llm_responses:
            action = response.get("action")
            if action in actions:
                actions[action] += 1
            else:
                actions[action] = 1
        
        for action, count in actions.items():
            logger.info(f"  {action}: {count} ({count / len(llm_responses) * 100:.1f}%)")
        
        # Calculate average score
        scores = [response.get("score", 0) for response in llm_responses]
        avg_score = sum(scores) / len(scores)
        logger.info(f"  Average score: {avg_score:.2f}")
        
        # Plot LLM response times
        plot_response_times(llm_responses)
        
        # Plot action distribution
        plot_action_distribution(actions)
    
    logger.info("=" * 50)

def plot_response_times(llm_responses):
    """Plot LLM response times."""
    try:
        # Extract response times
        times = [response.get("processing_time", 0) for response in llm_responses]
        timestamps = [datetime.fromisoformat(response.get("timestamp")) for response in llm_responses]
        
        # Create figure
        plt.figure(figsize=(10, 6))
        plt.plot(timestamps, times, 'o-')
        plt.title("LLM Response Times")
        plt.xlabel("Time")
        plt.ylabel("Response Time (s)")
        plt.grid(True)
        
        # Save figure
        plt.savefig("logs/llm_response_times.png")
        logger.info("LLM response times plot saved to logs/llm_response_times.png")
    
    except Exception as e:
        logger.error(f"Error plotting response times: {e}")

def plot_action_distribution(actions):
    """Plot action distribution."""
    try:
        # Extract actions and counts
        labels = list(actions.keys())
        counts = list(actions.values())
        
        # Create figure
        plt.figure(figsize=(8, 8))
        plt.pie(counts, labels=labels, autopct='%1.1f%%')
        plt.title("LLM Action Distribution")
        
        # Save figure
        plt.savefig("logs/llm_action_distribution.png")
        logger.info("LLM action distribution plot saved to logs/llm_action_distribution.png")
    
    except Exception as e:
        logger.error(f"Error plotting action distribution: {e}")

def main():
    """Main entry point."""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Analyze End-to-End Test Results for Smart-Trader System with Phi-3.1 Mini")
    parser.add_argument("--results", type=str, help="Path to results file")
    args = parser.parse_args()
    
    # Find results file
    results_path = args.results
    if not results_path:
        results_path = find_latest_results()
    
    if not results_path:
        logger.error("No results file specified or found")
        return
    
    logger.info(f"Analyzing results from {results_path}")
    
    # Load results
    results = load_results(results_path)
    
    # Analyze results
    analyze_results(results)

if __name__ == "__main__":
    main()
