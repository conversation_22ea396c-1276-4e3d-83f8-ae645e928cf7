<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Circle - Professional Investment Club Platform</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/static/css/unified_header.css" as="style">
    <link rel="preload" href="/static/css/unified_footer.css" as="style">
    <link rel="preload" href="/static/css/design_system.css" as="style">
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="/static/css/unified_header.css">
    <link rel="stylesheet" href="/static/css/unified_footer.css">
    <link rel="stylesheet" href="/static/css/design_system.css">
    
    <!-- Home page specific styles -->
    <style>
        /* Home Page Specific Styles */
        .home-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4, #10b981);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }

        .hero-btn {
            padding: 16px 32px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 180px;
        }

        .hero-btn.primary {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
        }

        .hero-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(139, 92, 246, 0.4);
        }

        .hero-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .hero-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .features-section {
            padding: 80px 0;
            background: rgba(0, 0, 0, 0.3);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .features-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            color: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 40px 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(139, 92, 246, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: white;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            font-size: 1rem;
        }

        .stats-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .stats-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            color: white;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 8px;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .cta-section {
            padding: 100px 0;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            color: white;
        }

        .cta-description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .cta-title {
                font-size: 2rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-content > * {
            animation: fadeInUp 0.8s ease-out;
        }

        .feature-card {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <div class="home-container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Money Circle</h1>
                <p class="hero-subtitle">
                    Professional Investment Club Platform for Smart Trading & Collaborative Wealth Building
                </p>
                
                <div class="hero-buttons">
                    <a href="/register" class="hero-btn primary">Join the Club</a>
                    <a href="/login" class="hero-btn secondary">Member Login</a>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="features-container">
                <h2 class="features-title">Platform Features</h2>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">📈</span>
                        <h3 class="feature-title">Live Trading</h3>
                        <p class="feature-description">
                            Professional trading interface with real-time market data, 
                            advanced charting, and automated trading strategies.
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🤝</span>
                        <h3 class="feature-title">Collaborative Strategies</h3>
                        <p class="feature-description">
                            Share and follow successful trading strategies from 
                            experienced club members with proven track records.
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <h3 class="feature-title">Advanced Analytics</h3>
                        <p class="feature-description">
                            Comprehensive portfolio analytics, performance tracking, 
                            and risk management tools for informed decision making.
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🔒</span>
                        <h3 class="feature-title">Secure & Private</h3>
                        <p class="feature-description">
                            Bank-level security with encrypted API connections, 
                            secure data storage, and privacy-first architecture.
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🎯</span>
                        <h3 class="feature-title">Auto Trading</h3>
                        <p class="feature-description">
                            Automated trading signals and execution with customizable 
                            risk parameters and stop-loss protection.
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">👥</span>
                        <h3 class="feature-title">Member Community</h3>
                        <p class="feature-description">
                            Connect with like-minded investors, share insights, 
                            and learn from successful trading strategies.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Active Members</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">12</span>
                        <span class="stat-label">Trading Strategies</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Market Monitoring</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">3</span>
                        <span class="stat-label">Major Exchanges</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Ready to Start Trading?</h2>
                <p class="cta-description">
                    Join the Money Circle investment club today and gain access to professional 
                    trading tools, collaborative strategies, and a community of successful traders.
                </p>
                
                <div class="hero-buttons">
                    <a href="/register" class="hero-btn primary">Get Started Now</a>
                    <a href="/login" class="hero-btn secondary">I'm Already a Member</a>
                </div>
            </div>
        </section>
    </div>

    <!-- JavaScript for enhanced interactivity -->
    <script>
        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add loading states to buttons
        document.querySelectorAll('.hero-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#')) {
                    this.style.opacity = '0.7';
                    this.innerHTML += ' <span style="margin-left: 8px;">⏳</span>';
                }
            });
        });

        // Animate stats on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.8s ease-out';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.feature-card, .stat-item').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
