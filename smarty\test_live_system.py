#!/usr/bin/env python3
"""
Live test suite for the operational Smart-Trader system.
Tests real functionality while the system is running.
"""

import requests
import sqlite3
import json
import time
from datetime import datetime
import sys

class LiveSystemTester:
    def __init__(self):
        self.control_center_url = "http://localhost:8082"
        self.dashboard_url = "http://localhost:8080"
        self.results = []

    def test_control_center_api(self):
        """Test Control Center API."""
        print("🔍 Testing Control Center API...")
        try:
            response = requests.get(f"{self.control_center_url}/api/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API Status: {response.status_code}")
                print(f"   • Smart-trader available: {data.get('smart_trader_available')}")
                print(f"   • Orchestrator running: {data['system_status']['orchestrator']['running']}")
                print(f"   • Testnet running: {data['system_status']['testnet']['running']}")
                return True, data
            else:
                print(f"❌ API failed with status: {response.status_code}")
                return False, None
        except Exception as e:
            print(f"❌ API test failed: {e}")
            return False, None

    def test_database_activity(self):
        """Test database for real-time activity."""
        print("\n🔍 Testing Database Activity...")
        try:
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()

            # Get total messages
            cursor.execute("SELECT COUNT(*) FROM messages")
            total_messages = cursor.fetchone()[0]

            # Get recent messages (last 5 minutes)
            five_minutes_ago = time.time() - 300
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (five_minutes_ago,))
            recent_messages = cursor.fetchone()[0]

            # Get unique streams
            cursor.execute("SELECT DISTINCT stream FROM messages ORDER BY stream")
            streams = [row[0] for row in cursor.fetchall()]

            # Get latest message
            cursor.execute("SELECT stream, ts, payload FROM messages ORDER BY ts DESC LIMIT 1")
            latest = cursor.fetchone()

            print(f"✅ Database Active")
            print(f"   • Total messages: {total_messages:,}")
            print(f"   • Recent messages (5min): {recent_messages}")
            print(f"   • Active streams: {len(streams)}")
            if latest:
                latest_time = datetime.fromtimestamp(latest[1]).strftime('%H:%M:%S')
                print(f"   • Latest: {latest[0]} at {latest_time}")

            # Show key streams
            key_streams = [s for s in streams if any(x in s for x in ['account', 'market', 'features', 'signals'])]
            if key_streams:
                print(f"   • Key streams: {', '.join(key_streams[:8])}")

            conn.close()
            return True, {"total": total_messages, "recent": recent_messages, "streams": len(streams)}

        except Exception as e:
            print(f"❌ Database test failed: {e}")
            return False, None

    def test_market_data_flow(self):
        """Test real-time market data."""
        print("\n🔍 Testing Market Data Flow...")
        try:
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()

            # Check for market data in last 10 minutes
            ten_minutes_ago = time.time() - 600
            cursor.execute("""
                SELECT stream, COUNT(*) as count, MAX(ts) as latest_ts
                FROM messages
                WHERE ts > ? AND stream LIKE 'market.%'
                GROUP BY stream
                ORDER BY latest_ts DESC
            """, (ten_minutes_ago,))

            market_data = cursor.fetchall()

            # Check for feature data
            cursor.execute("""
                SELECT stream, COUNT(*) as count, MAX(ts) as latest_ts
                FROM messages
                WHERE ts > ? AND stream LIKE 'features.%'
                GROUP BY stream
                ORDER BY latest_ts DESC
            """, (ten_minutes_ago,))

            feature_data = cursor.fetchall()

            print(f"✅ Market Data Flowing")
            if market_data:
                print(f"   • Market streams: {len(market_data)}")
                for stream, count, latest_ts in market_data[:3]:
                    latest_time = datetime.fromtimestamp(latest_ts).strftime('%H:%M:%S')
                    print(f"     - {stream}: {count} msgs (latest: {latest_time})")

            if feature_data:
                print(f"   • Feature streams: {len(feature_data)}")
                for stream, count, latest_ts in feature_data[:3]:
                    latest_time = datetime.fromtimestamp(latest_ts).strftime('%H:%M:%S')
                    print(f"     - {stream}: {count} msgs (latest: {latest_time})")

            conn.close()
            return True, {"market_streams": len(market_data), "feature_streams": len(feature_data)}

        except Exception as e:
            print(f"❌ Market data test failed: {e}")
            return False, None

    def test_account_integration(self):
        """Test account data integration."""
        print("\n🔍 Testing Account Integration...")
        try:
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()

            # Get latest account state
            cursor.execute("""
                SELECT payload, ts
                FROM messages
                WHERE stream = 'account.state'
                ORDER BY ts DESC
                LIMIT 1
            """)

            account_data = cursor.fetchone()

            if account_data:
                account_info = json.loads(account_data[0])
                last_update = datetime.fromtimestamp(account_data[1])

                print(f"✅ Account Connected")
                print(f"   • Balance: ${account_info.get('total', 0):.2f} USDT")
                print(f"   • Available: ${account_info.get('available', 0):.2f} USDT")
                print(f"   • Last update: {last_update.strftime('%H:%M:%S')}")

                conn.close()
                return True, account_info
            else:
                print(f"⚠️  No account data found")
                conn.close()
                return False, None

        except Exception as e:
            print(f"❌ Account test failed: {e}")
            return False, None

    def test_llm_activity(self):
        """Test LLM signal generation."""
        print("\n🔍 Testing LLM Activity...")
        try:
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()

            # Check for LLM signals
            thirty_minutes_ago = time.time() - 1800
            cursor.execute("""
                SELECT COUNT(*)
                FROM messages
                WHERE stream = 'signals.fused'
                AND ts > ?
            """, (thirty_minutes_ago,))

            signal_count = cursor.fetchone()[0]

            # Get latest signal
            cursor.execute("""
                SELECT payload, ts
                FROM messages
                WHERE stream = 'signals.fused'
                ORDER BY ts DESC
                LIMIT 1
            """)

            latest_signal = cursor.fetchone()

            if signal_count > 0 or latest_signal:
                print(f"✅ LLM Active")
                print(f"   • Signals generated (30min): {signal_count}")
                if latest_signal:
                    signal_time = datetime.fromtimestamp(latest_signal[1]).strftime('%H:%M:%S')
                    print(f"   • Latest signal: {signal_time}")
                    try:
                        signal_data = json.loads(latest_signal[0])
                        if 'action' in signal_data:
                            print(f"   • Action: {signal_data.get('action', 'N/A')}")
                        if 'confidence' in signal_data:
                            print(f"   • Confidence: {signal_data.get('confidence', 'N/A')}")
                    except:
                        pass
            else:
                print(f"⚠️  No recent LLM signals")

            conn.close()
            return True, {"signals": signal_count, "has_latest": latest_signal is not None}

        except Exception as e:
            print(f"❌ LLM test failed: {e}")
            return False, None

    def test_dashboard_connectivity(self):
        """Test dashboard connectivity."""
        print("\n🔍 Testing Dashboard...")
        try:
            response = requests.get(f"{self.dashboard_url}/", timeout=5)
            if response.status_code == 200:
                print(f"✅ Dashboard Online")
                print(f"   • URL: {self.dashboard_url}")
                print(f"   • Status: {response.status_code}")
                return True, {"status": response.status_code}
            else:
                print(f"⚠️  Dashboard status: {response.status_code}")
                return False, None
        except Exception as e:
            print(f"❌ Dashboard test failed: {e}")
            return False, None

    def test_real_time_updates(self):
        """Test real-time system updates."""
        print("\n🔍 Testing Real-time Updates...")
        try:
            conn = sqlite3.connect("data/bus.db")
            cursor = conn.cursor()

            # Get initial count
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (time.time() - 60,))
            initial_count = cursor.fetchone()[0]

            print(f"   • Initial messages (1min): {initial_count}")
            print("   • Waiting 5 seconds for new data...")

            # Wait for new messages
            time.sleep(5)

            # Get final count
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (time.time() - 60,))
            final_count = cursor.fetchone()[0]

            new_messages = final_count - initial_count

            if new_messages > 0:
                print(f"✅ System Active")
                print(f"   • New messages: {new_messages}")
                print(f"   • Messages/second: {new_messages/5:.1f}")
            else:
                print(f"⚠️  No new messages detected")

            conn.close()
            return True, {"new_messages": new_messages}

        except Exception as e:
            print(f"❌ Real-time test failed: {e}")
            return False, None

    def run_all_tests(self):
        """Run all tests and provide summary."""
        print("🧪 SMART-TRADER LIVE SYSTEM TESTS")
        print("=" * 50)

        tests = [
            ("Control Center API", self.test_control_center_api),
            ("Database Activity", self.test_database_activity),
            ("Market Data Flow", self.test_market_data_flow),
            ("Account Integration", self.test_account_integration),
            ("LLM Activity", self.test_llm_activity),
            ("Dashboard Connectivity", self.test_dashboard_connectivity),
            ("Real-time Updates", self.test_real_time_updates)
        ]

        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            try:
                success, data = test_func()
                if success:
                    passed += 1
                    self.results.append({"test": test_name, "status": "PASS", "data": data})
                else:
                    self.results.append({"test": test_name, "status": "FAIL", "data": data})
            except Exception as e:
                print(f"❌ {test_name}: Exception - {e}")
                self.results.append({"test": test_name, "status": "ERROR", "error": str(e)})

        # Print summary
        print("\n" + "=" * 50)
        print("🎯 TEST RESULTS SUMMARY")
        print("=" * 50)
        print(f"📊 Score: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

        if passed == total:
            print("🎉 ALL TESTS PASSED! System is fully operational!")
        elif passed >= total * 0.8:
            print("✅ SYSTEM MOSTLY OPERATIONAL")
        else:
            print("⚠️  SYSTEM NEEDS ATTENTION")

        return passed, total

def main():
    """Run the live system tests."""
    tester = LiveSystemTester()
    passed, total = tester.run_all_tests()

    if passed == total:
        print("\n🚀 Your Smart-Trader system is ready for action!")
        print("💡 You can now:")
        print("   • Access your unified dashboard at http://localhost:8081")
        print("   • Monitor real-time signals and control all operations")
        print("   • View live market data and AI decisions")
        print("   • Execute trades with your $100 balance")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
