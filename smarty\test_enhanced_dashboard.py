#!/usr/bin/env python3
"""
Test script for the Enhanced Smart Trader Dashboard
Tests all new API endpoints and demonstrates functionality
"""

import requests
import json
import time
from datetime import datetime

def test_api_endpoint(endpoint, description):
    """Test a single API endpoint."""
    try:
        print(f"\n🔍 Testing {description}...")
        print(f"   URL: http://localhost:8082{endpoint}")
        
        response = requests.get(f'http://localhost:8082{endpoint}', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS - Status: {response.status_code}")
            print(f"   📊 Data: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"   ❌ FAILED - Status: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ TIMEOUT - Endpoint took too long to respond")
        return False
    except requests.exceptions.ConnectionError:
        print(f"   🔌 CONNECTION ERROR - Cannot connect to dashboard")
        return False
    except Exception as e:
        print(f"   💥 ERROR - {str(e)}")
        return False

def main():
    """Test all enhanced dashboard endpoints."""
    print("🎯 Smart Trader Enhanced Dashboard API Test")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test endpoints
    endpoints = [
        ("/api/market", "Market Data API"),
        ("/api/ai-analysis", "AI Analysis API"),
        ("/api/market-sentiment", "Market Sentiment API"),
        ("/api/orderbook", "Order Book API"),
        ("/api/recent-trades", "Recent Trades API"),
        ("/api/signals", "Trading Signals API"),
        ("/api/trades", "Active Trades API"),
        ("/api/stats", "System Stats API"),
    ]
    
    results = []
    
    for endpoint, description in endpoints:
        success = test_api_endpoint(endpoint, description)
        results.append((endpoint, description, success))
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, _, success in results if success)
    total = len(results)
    
    for endpoint, description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {description}")
    
    print(f"\n🎯 Results: {passed}/{total} endpoints working")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Enhanced dashboard is fully functional!")
        print("\n🌐 Dashboard Features Available:")
        print("   📈 Live Market Activity & AI Analysis (Top Section)")
        print("   ⚡ Trading Operations (Bottom Section)")
        print("   🔄 Real-time WebSocket updates")
        print("   📊 Comprehensive market data")
        print("   🧠 AI-powered market analysis")
        print("   🎭 Market sentiment analysis")
    else:
        print(f"⚠️  {total - passed} endpoints need attention")
    
    print(f"\n🌐 Dashboard URL: http://localhost:8082")
    print("💡 Refresh your browser to see the enhanced layout!")

if __name__ == "__main__":
    main()
