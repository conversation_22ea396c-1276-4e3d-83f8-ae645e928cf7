"""
Tests for the Volatility Regime model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime

from models.volatility_regime import VolatilityRegimeModel, VolatilityRegime


class TestVolatilityRegimeModel(unittest.TestCase):
    """Test the Volatility Regime model."""

    def setUp(self):
        """Set up test environment."""
        self.model = VolatilityRegimeModel()

    def test_volatility_calculation(self):
        """Test volatility calculation."""
        # Generate sample price data with known volatility
        np.random.seed(42)  # For reproducibility

        # Low volatility series
        low_vol_prices = np.cumsum(np.random.normal(0, 0.001, 100)) + 100

        # High volatility series
        high_vol_prices = np.cumsum(np.random.normal(0, 0.01, 100)) + 100

        # Calculate volatility
        low_vol = self.model._get_current_volatility(low_vol_prices.tolist())
        high_vol = self.model._get_current_volatility(high_vol_prices.tolist())

        # High volatility should be greater than low volatility
        self.assertGreater(high_vol, low_vol)

    def test_regime_classification(self):
        """Test regime classification."""
        # Override the thresholds for testing
        self.model.low_vol_threshold = 0.3  # 30th percentile
        self.model.high_vol_threshold = 0.7  # 70th percentile

        # Generate sample volatility history
        self.model._historical_vol["BTC-USDT"] = [0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1]

        # Test low volatility classification (0.01 is at 0th percentile)
        regime, confidence = self.model._classify_regime("BTC-USDT", 0.01)
        self.assertEqual(regime, VolatilityRegime.LOW_VOL)

        # Test normal volatility classification (0.05 is at 40th percentile)
        regime, confidence = self.model._classify_regime("BTC-USDT", 0.05)
        self.assertEqual(regime, VolatilityRegime.NORMAL)

        # Test high volatility classification (0.1 is at 90th percentile)
        regime, confidence = self.model._classify_regime("BTC-USDT", 0.1)
        self.assertEqual(regime, VolatilityRegime.HIGH_VOL)

    def test_position_size_multiplier(self):
        """Test position size multiplier."""
        # Low volatility should increase position size
        low_vol_multiplier = self.model._get_position_size_multiplier(VolatilityRegime.LOW_VOL)

        # Normal volatility should use normal position size
        normal_multiplier = self.model._get_position_size_multiplier(VolatilityRegime.NORMAL)

        # High volatility should decrease position size
        high_vol_multiplier = self.model._get_position_size_multiplier(VolatilityRegime.HIGH_VOL)

        # Check relationships
        self.assertGreater(low_vol_multiplier, normal_multiplier)
        self.assertLess(high_vol_multiplier, normal_multiplier)

    def test_predict(self):
        """Test predict method."""
        async def test_predict_async():
            # Generate sample price data
            np.random.seed(42)
            prices = np.cumsum(np.random.normal(0, 0.005, 100)) + 100

            # Create features dictionary
            features = {
                "symbol": "BTC-USDT",
                "close_prices": prices.tolist(),
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Check prediction keys
            self.assertIn("regime", prediction)
            self.assertIn("confidence", prediction)
            self.assertIn("volatility", prediction)
            self.assertIn("percentile", prediction)
            self.assertIn("action", prediction)
            self.assertIn("position_size_multiplier", prediction)

            # Check regime is one of the valid regimes
            self.assertIn(prediction["regime"], [r.value for r in VolatilityRegime])

            # Check confidence is between 0 and 1
            self.assertGreaterEqual(prediction["confidence"], 0.0)
            self.assertLessEqual(prediction["confidence"], 1.0)

            # Check action is one of the valid actions
            self.assertIn(prediction["action"], ["BUY", "SELL", "HOLD"])

        # Run async test
        asyncio.run(test_predict_async())

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Create features dictionary with insufficient data
            features = {
                "symbol": "BTC-USDT",
                "close_prices": [100.0, 101.0, 102.0],  # Not enough data
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["regime"], VolatilityRegime.NORMAL.value)
            self.assertEqual(prediction["confidence"], 0.5)

        # Run async test
        asyncio.run(test_insufficient_data_async())


if __name__ == "__main__":
    unittest.main()
