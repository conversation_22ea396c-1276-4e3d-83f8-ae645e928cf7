"""
HTX Futures main client - orchestrates WebSocket and REST components.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable, Union

import aiohttp

from core.events import (
    Kline, Trade, OrderbookDelta, Position, Order, OrderResponse, Fill
)
from .constants import __version__, DEFAULT_CONFIG
from .types import (
    HTXConfig, ConnectionState, HealthStatus, PublisherFunction,
    HTXError, ConnectionError as HTXConnectionError
)
from .websocket import WebSocketManager
from .rest import HTXRestClient
from .parser import MessageParser

logger = logging.getLogger(__name__)


class HTXFuturesClient:
    """
    Enhanced HTX Futures API client with modular architecture.
    
    This is the main client that orchestrates WebSocket and REST components
    with improved error handling, metrics, and reliability features.
    """
    
    def __init__(
        self,
        api_key: str = "",
        api_secret: str = "",
        testnet: bool = False,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize the HTX Futures client.

        Args:
            api_key: API key for authenticated requests
            api_secret: API secret for authenticated requests
            testnet: Whether to use testnet
            config: Additional configuration options
            **kwargs: Legacy configuration parameters
        """
        # Merge configuration
        merged_config = {**DEFAULT_CONFIG}
        if config:
            merged_config.update(config)
        merged_config.update(kwargs)
        
        # Create configuration object
        self.config = HTXConfig(
            api_key=api_key,
            api_secret=api_secret,
            testnet=testnet,
            **merged_config
        )
        
        # Initialize components
        self.session: Optional[aiohttp.ClientSession] = None
        self.parser = MessageParser(debug=self.config.debug)
        self.ws_manager: Optional[WebSocketManager] = None
        self.rest_client: Optional[HTXRestClient] = None
        
        # Connection state
        self.connected = False
        self.authenticated = False
        
        # Message queues for backward compatibility
        self.market_queue = asyncio.Queue(maxsize=self.config.queue_size)
        self.private_queue = asyncio.Queue(maxsize=self.config.queue_size)
        
        # Message bus publisher
        self._publish: Optional[PublisherFunction] = None
        
        # Simulation mode flag for backward compatibility
        self.simulation_mode = False
        
        # Subscription tracking
        self.subscriptions = set()
        
        logger.info(f"HTX Futures Client v{__version__} initialized")
        if self.config.debug:
            logger.debug(f"Configuration: {self.config}")

    async def connect(self) -> None:
        """Connect to HTX Futures WebSocket and REST API."""
        try:
            logger.info("Connecting to HTX Futures API...")
            
            # Create HTTP session
            if self.session is None:
                self.session = aiohttp.ClientSession()
            
            # Initialize components
            self.ws_manager = WebSocketManager(self.config, self.session)
            self.rest_client = HTXRestClient(self.config, self.session, self.parser)
            
            # Set up message handlers
            self.ws_manager.set_message_handler("market_data", self._handle_market_data)
            self.ws_manager.set_message_handler("private_data", self._handle_private_data)
            
            # Connect WebSocket components
            await self.ws_manager.connect_market()
            
            # Connect private WebSocket if credentials provided
            if self.config.api_key and self.config.api_secret:
                await self.ws_manager.connect_private()
                self.authenticated = True
            
            self.connected = True
            logger.info("Successfully connected to HTX Futures API")
            
        except Exception as e:
            logger.error(f"Failed to connect to HTX Futures API: {e}")
            await self.close()
            raise HTXConnectionError(f"Connection failed: {e}")

    async def _handle_market_data(self, data: Dict[str, Any]) -> None:
        """Handle market data messages."""
        try:
            parsed_msg = self.parser.parse_market_message(data)
            if parsed_msg:
                # Add to queue for backward compatibility
                try:
                    self.market_queue.put_nowait(parsed_msg)
                except asyncio.QueueFull:
                    logger.warning("Market queue full, dropping message")
                
                # Publish to message bus
                if self._publish:
                    await self._publish_market_message(parsed_msg)
                    
        except Exception as e:
            logger.error(f"Error handling market data: {e}")

    async def _handle_private_data(self, data: Dict[str, Any]) -> None:
        """Handle private data messages."""
        try:
            parsed_msg = self.parser.parse_private_message(data)
            if parsed_msg:
                # Add to queue for backward compatibility
                try:
                    self.private_queue.put_nowait(parsed_msg)
                except asyncio.QueueFull:
                    logger.warning("Private queue full, dropping message")
                
                # Publish to message bus
                if self._publish:
                    await self._publish_private_message(parsed_msg)
                    
        except Exception as e:
            logger.error(f"Error handling private data: {e}")

    async def _publish_market_message(self, msg: Union[Kline, Trade, OrderbookDelta]) -> None:
        """Publish market message to message bus."""
        try:
            if isinstance(msg, Kline):
                self._publish(
                    f"market.{msg.symbol}.kline.{msg.interval}",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "interval": msg.interval,
                        "open": msg.open,
                        "high": msg.high,
                        "low": msg.low,
                        "close": msg.close,
                        "volume": msg.volume
                    }
                )
            elif isinstance(msg, Trade):
                self._publish(
                    f"market.{msg.symbol}.trade.detail",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "price": msg.price,
                        "quantity": msg.quantity,
                        "side": msg.side.value
                    }
                )
            elif isinstance(msg, OrderbookDelta):
                bids = [[level.price, level.quantity] for level in msg.bids]
                asks = [[level.price, level.quantity] for level in msg.asks]
                
                self._publish(
                    f"market.{msg.symbol}.depth.step0",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "bids": bids,
                        "asks": asks,
                        "is_snapshot": msg.is_snapshot
                    }
                )
        except Exception as e:
            logger.error(f"Error publishing market message: {e}")

    async def _publish_private_message(self, msg: Union[Position, OrderResponse, Fill]) -> None:
        """Publish private message to message bus."""
        try:
            if isinstance(msg, Position):
                self._publish(
                    "htx.position",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "side": msg.side.value,
                        "size": msg.size,
                        "entry_price": msg.entry_price,
                        "leverage": msg.leverage,
                        "liquidation_price": msg.liquidation_price,
                        "unrealized_pnl": msg.unrealized_pnl,
                        "realized_pnl": msg.realized_pnl,
                        "margin": msg.margin
                    }
                )
            elif isinstance(msg, OrderResponse):
                self._publish(
                    "htx.order",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "order_id": msg.order_id,
                        "client_order_id": msg.client_order_id,
                        "status": msg.status
                    }
                )
            elif isinstance(msg, Fill):
                self._publish(
                    "htx.fill",
                    msg.timestamp.timestamp(),
                    {
                        "symbol": msg.symbol,
                        "order_id": msg.order_id,
                        "trade_id": msg.trade_id,
                        "side": msg.side.value,
                        "price": msg.price,
                        "quantity": msg.quantity,
                        "commission": msg.commission,
                        "commission_asset": msg.commission_asset
                    }
                )
        except Exception as e:
            logger.error(f"Error publishing private message: {e}")

    def set_publisher(self, publish_fn: PublisherFunction) -> None:
        """Set the message bus publisher function."""
        self._publish = publish_fn
        logger.info("Message bus publisher set for HTX Futures client")

    async def subscribe(self, channel: str) -> bool:
        """Subscribe to a WebSocket channel."""
        if not self.connected or not self.ws_manager:
            logger.error("Cannot subscribe: not connected")
            return False
            
        success = await self.ws_manager.subscribe(channel)
        if success:
            self.subscriptions.add(channel)
            
        return success

    async def unsubscribe(self, channel: str) -> bool:
        """Unsubscribe from a WebSocket channel."""
        if not self.connected or not self.ws_manager:
            logger.error("Cannot unsubscribe: not connected")
            return False
            
        # Remove from tracking
        self.subscriptions.discard(channel)
        
        # TODO: Implement unsubscribe in WebSocketManager
        logger.warning("Unsubscribe not yet implemented in WebSocketManager")
        return True

    # Queue methods for backward compatibility
    async def get_next_market_message(self, timeout: float = None) -> Optional[Union[Kline, Trade, OrderbookDelta]]:
        """Get the next market data message from the queue."""
        try:
            return await asyncio.wait_for(self.market_queue.get(), timeout)
        except asyncio.TimeoutError:
            return None

    async def get_next_private_message(self, timeout: float = None) -> Optional[Union[Position, OrderResponse, Fill]]:
        """Get the next private message from the queue."""
        try:
            return await asyncio.wait_for(self.private_queue.get(), timeout)
        except asyncio.TimeoutError:
            return None

    # REST API methods - delegate to rest client
    async def get_contract_info(self, symbol: str = None) -> Dict[str, Any]:
        """Get contract information."""
        if not self.rest_client:
            raise HTXError("REST client not initialized")
        response = await self.rest_client.get_contract_info(symbol)
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        if not self.rest_client:
            raise HTXError("REST client not initialized")
            
        # Handle simulation mode
        if self.simulation_mode:
            from executors.htx_executor import HTXExecutor
            executor = HTXExecutor.get_instance()
            if executor:
                sim_balance = executor.sim_balance
                return {
                    "status": "ok",
                    "data": [{
                        "margin_asset": "USDT",
                        "margin_balance": sim_balance,
                        "margin_available": sim_balance,
                        "total_equity": sim_balance,
                        "available_balance": sim_balance
                    }]
                }
        
        response = await self.rest_client.get_account_info()
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_positions(self, symbol: str = None) -> List[Position]:
        """Get positions as domain objects."""
        if not self.rest_client:
            raise HTXError("REST client not initialized")
        return await self.rest_client.get_positions(symbol)

    async def place_order(self, order: Order) -> OrderResponse:
        """Place an order."""
        if not self.rest_client:
            raise HTXError("REST client not initialized")
            
        response = await self.rest_client.place_order(order)
        if not response.success:
            raise HTXError(f"Order placement failed: {response.error}")
            
        # Extract order info from response
        order_data = response.data or {}
        return OrderResponse(
            symbol=order.symbol,
            order_id=str(order_data.get("order_id", "")),
            client_order_id=str(order_data.get("client_order_id", "")),
            status="SUBMITTED",
            timestamp=datetime.now()
        )

    async def health_check(self) -> HealthStatus:
        """Perform comprehensive health check."""
        issues = []
        
        # Check REST API
        rest_healthy = False
        if self.rest_client:
            rest_healthy = await self.rest_client.health_check()
        if not rest_healthy:
            issues.append("REST API not responding")
        
        # Check WebSocket connections
        ws_healthy = False
        if self.ws_manager:
            ws_healthy = (
                self.ws_manager.market_state == ConnectionState.CONNECTED and
                (not self.config.api_key or self.ws_manager.private_state == ConnectionState.AUTHENTICATED)
            )
        if not ws_healthy:
            issues.append("WebSocket connections not healthy")
        
        is_healthy = rest_healthy and ws_healthy and len(issues) == 0
        
        return HealthStatus(
            is_healthy=is_healthy,
            connection_state=self.ws_manager.market_state if self.ws_manager else ConnectionState.DISCONNECTED,
            last_check=datetime.now(),
            issues=issues,
            metrics=self.ws_manager.metrics if self.ws_manager else None
        )

    async def close(self) -> None:
        """Close all connections and clean up resources."""
        logger.info("Closing HTX Futures client...")
        
        # Close WebSocket manager
        if self.ws_manager:
            await self.ws_manager.close()
        
        # Close HTTP session
        if self.session:
            await self.session.close()
        
        self.connected = False
        self.authenticated = False
        
        logger.info("HTX Futures client closed")
