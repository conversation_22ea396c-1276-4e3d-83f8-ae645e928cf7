#!/usr/bin/env python3
"""
Quick Deployment Test for Money Circle
Simple test to verify Railway deployment is working
"""

import asyncio
import aiohttp
import sys
from datetime import datetime

async def test_deployment(url):
    """Quick test of deployment functionality."""
    print(f"🔍 Testing Money Circle deployment at: {url}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        tests = []
        
        # Test 1: Health Check
        try:
            async with session.get(f"{url}/health", timeout=10) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    tests.append(("Health Check", "✅ PASS", f"Status: {data.get('status')}"))
                else:
                    tests.append(("Health Check", "❌ FAIL", f"HTTP {resp.status}"))
        except Exception as e:
            tests.append(("Health Check", "❌ ERROR", str(e)))
        
        # Test 2: Homepage
        try:
            async with session.get(url, timeout=10) as resp:
                if resp.status in [200, 302]:  # 302 = redirect to login
                    tests.append(("Homepage", "✅ PASS", f"HTTP {resp.status}"))
                else:
                    tests.append(("Homepage", "❌ FAIL", f"HTTP {resp.status}"))
        except Exception as e:
            tests.append(("Homepage", "❌ ERROR", str(e)))
        
        # Test 3: Login Page
        try:
            async with session.get(f"{url}/login", timeout=10) as resp:
                if resp.status == 200:
                    tests.append(("Login Page", "✅ PASS", "Login page accessible"))
                else:
                    tests.append(("Login Page", "❌ FAIL", f"HTTP {resp.status}"))
        except Exception as e:
            tests.append(("Login Page", "❌ ERROR", str(e)))
        
        # Test 4: Static Files
        try:
            async with session.get(f"{url}/static/css/dashboard.css", timeout=10) as resp:
                if resp.status == 200:
                    tests.append(("Static Files", "✅ PASS", "CSS files loading"))
                else:
                    tests.append(("Static Files", "❌ FAIL", f"HTTP {resp.status}"))
        except Exception as e:
            tests.append(("Static Files", "❌ ERROR", str(e)))
        
        # Test 5: API Endpoint
        try:
            async with session.get(f"{url}/api/system/status", timeout=10) as resp:
                if resp.status in [200, 302, 401, 403]:  # Valid responses
                    tests.append(("API Endpoints", "✅ PASS", f"API responding (HTTP {resp.status})"))
                else:
                    tests.append(("API Endpoints", "❌ FAIL", f"HTTP {resp.status}"))
        except Exception as e:
            tests.append(("API Endpoints", "❌ ERROR", str(e)))
    
    # Print results
    print("\n📋 Test Results:")
    print("-" * 30)
    passed = 0
    total = len(tests)
    
    for test_name, status, details in tests:
        print(f"{status} {test_name}: {details}")
        if "PASS" in status:
            passed += 1
    
    print(f"\n📊 Summary: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Money Circle deployment is working correctly")
        print(f"🔗 Platform URL: {url}")
        print("🔐 Login with: epinnox / securepass123")
    elif passed >= total * 0.8:
        print("\n✅ MOSTLY WORKING!")
        print("⚠️ Some minor issues detected, but platform is functional")
    else:
        print("\n❌ DEPLOYMENT ISSUES DETECTED")
        print("🔧 Please check Railway logs and environment variables")
    
    return passed == total

async def main():
    if len(sys.argv) != 2:
        print("Usage: python quick_deployment_test.py <URL>")
        print("Example: python quick_deployment_test.py https://your-app.railway.app")
        return
    
    url = sys.argv[1].rstrip('/')
    success = await test_deployment(url)
    
    if success:
        print(f"\n🚀 Ready for Epinnox investment club members!")
        print(f"📱 Share this URL: {url}")
    else:
        print(f"\n🔧 Fix issues before sharing with members")

if __name__ == "__main__":
    asyncio.run(main())
