#!/usr/bin/env python3
"""
Final test to verify Money Circle is working correctly
Tests all major functionality after middleware fixes
"""

import asyncio
import aiohttp
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_money_circle():
    """Test Money Circle platform functionality."""
    logger.info("🧪 Testing Money Circle Platform")
    logger.info("=" * 50)
    
    base_url = "http://localhost:8086"
    tests_passed = 0
    total_tests = 5
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # Test 1: Health endpoint
            logger.info("📋 Test 1: Health Endpoint")
            try:
                async with session.get(f"{base_url}/health") as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        headers = dict(resp.headers)
                        
                        logger.info(f"✅ Health endpoint working (HTTP {resp.status})")
                        logger.info(f"   Status: {data.get('status', 'unknown')}")
                        
                        if 'X-Response-Time' in headers:
                            logger.info(f"   Performance: {headers['X-Response-Time']}")
                        
                        tests_passed += 1
                    else:
                        logger.error(f"❌ Health endpoint failed (HTTP {resp.status})")
            except Exception as e:
                logger.error(f"❌ Health endpoint error: {e}")
            
            # Test 2: Homepage redirect
            logger.info("\n📋 Test 2: Homepage")
            try:
                async with session.get(base_url, allow_redirects=False) as resp:
                    if resp.status == 302:
                        logger.info("✅ Homepage redirects correctly")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ Homepage failed (HTTP {resp.status})")
            except Exception as e:
                logger.error(f"❌ Homepage error: {e}")
            
            # Test 3: Login page
            logger.info("\n📋 Test 3: Login Page")
            try:
                async with session.get(f"{base_url}/login") as resp:
                    if resp.status == 200:
                        content = await resp.text()
                        if 'Money Circle' in content:
                            logger.info("✅ Login page loads correctly")
                            tests_passed += 1
                        else:
                            logger.warning("⚠️ Login page content may be incomplete")
                    else:
                        logger.error(f"❌ Login page failed (HTTP {resp.status})")
            except Exception as e:
                logger.error(f"❌ Login page error: {e}")
            
            # Test 4: Static files
            logger.info("\n📋 Test 4: Static Files")
            try:
                async with session.get(f"{base_url}/static/css/dashboard.css") as resp:
                    if resp.status == 200:
                        logger.info("✅ Static files serving correctly")
                        tests_passed += 1
                    else:
                        logger.warning(f"⚠️ Static files issue (HTTP {resp.status})")
            except Exception as e:
                logger.warning(f"⚠️ Static files warning: {e}")
            
            # Test 5: API endpoint (should require auth)
            logger.info("\n📋 Test 5: API Endpoints")
            try:
                async with session.get(f"{base_url}/api/system/status") as resp:
                    if resp.status in [200, 302, 401, 403]:  # Valid responses
                        logger.info(f"✅ API endpoints responding (HTTP {resp.status})")
                        tests_passed += 1
                    else:
                        logger.error(f"❌ API endpoints failed (HTTP {resp.status})")
            except Exception as e:
                logger.error(f"❌ API endpoints error: {e}")
    
    except Exception as e:
        logger.error(f"❌ Test session error: {e}")
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")
    logger.info(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("✅ Money Circle is working perfectly")
        logger.info("✅ No middleware errors")
        logger.info("✅ Performance monitoring active")
        logger.info("✅ Ready for full testing and deployment")
        
        logger.info(f"\n🌐 Platform URL: {base_url}")
        logger.info("🔐 Login with: epinnox / securepass123")
        logger.info("🎯 Test all features:")
        logger.info("   • Admin Dashboard")
        logger.info("   • Auto Trader")
        logger.info("   • Trading Signals")
        logger.info("   • Portfolio Analytics")
        logger.info("   • Social Trading")
        
    elif tests_passed >= total_tests * 0.8:
        logger.info("\n✅ MOSTLY WORKING!")
        logger.info("⚠️ Some minor issues detected")
        logger.info("🎯 Platform is functional for testing")
        
    else:
        logger.error("\n❌ SIGNIFICANT ISSUES DETECTED")
        logger.error("🔧 Check server logs for errors")
    
    return tests_passed >= total_tests * 0.8

async def main():
    """Main test function."""
    print("🧪 Money Circle Final Functionality Test")
    print("Testing after middleware fixes")
    print()
    
    try:
        success = await test_money_circle()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        return 1

if __name__ == '__main__':
    exit(asyncio.run(main()))
