#!/usr/bin/env python3
"""
Money Circle Personal Trading Interface
Individual trading functionality for users.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from database.models import DatabaseManager, UserTrade, UserPosition
from exchanges.account_manager import ExchangeAccountManager

logger = logging.getLogger(__name__)

class PersonalTrader:
    """Personal trading interface for individual users."""
    
    def __init__(self, db_manager: DatabaseManager, exchange_manager: ExchangeAccountManager):
        self.db = db_manager
        self.exchange_manager = exchange_manager
        
        # Risk management settings
        self.max_position_size = 1000.0  # Max position size in USDT
        self.max_leverage = 10.0
        self.max_daily_trades = 50
        
    async def place_market_order(self, user_id: int, exchange_name: str, 
                               symbol: str, side: str, amount: float,
                               strategy_name: str = "Manual") -> Optional[Dict[str, Any]]:
        """Place a market order for the user."""
        try:
            # Validate order parameters
            if not self._validate_order_params(user_id, exchange_name, symbol, side, amount):
                return None
            
            # Check risk limits
            if not await self._check_risk_limits(user_id, exchange_name, symbol, amount):
                logger.warning(f"Risk limits exceeded for user {user_id}")
                return None
            
            # Place order through exchange manager
            order = self.exchange_manager.place_order(
                user_id=user_id,
                exchange_name=exchange_name,
                symbol=symbol,
                order_type="market",
                side=side,
                amount=amount
            )
            
            if order:
                # Record trade in database
                await self._record_trade(user_id, exchange_name, symbol, side, 
                                       amount, order.get('price', 0), 
                                       order.get('fee', 0), "market", strategy_name)
                
                # Update positions
                await self._update_position(user_id, exchange_name, symbol, side, amount, order.get('price', 0))
                
                logger.info(f"✅ Market order placed: {side} {amount} {symbol} for user {user_id}")
                return order
            
            return None
            
        except Exception as e:
            logger.error(f"Market order error: {e}")
            return None
    
    async def place_limit_order(self, user_id: int, exchange_name: str,
                              symbol: str, side: str, amount: float, price: float,
                              strategy_name: str = "Manual") -> Optional[Dict[str, Any]]:
        """Place a limit order for the user."""
        try:
            # Validate order parameters
            if not self._validate_order_params(user_id, exchange_name, symbol, side, amount, price):
                return None
            
            # Check risk limits
            if not await self._check_risk_limits(user_id, exchange_name, symbol, amount):
                logger.warning(f"Risk limits exceeded for user {user_id}")
                return None
            
            # Place order through exchange manager
            order = self.exchange_manager.place_order(
                user_id=user_id,
                exchange_name=exchange_name,
                symbol=symbol,
                order_type="limit",
                side=side,
                amount=amount,
                price=price
            )
            
            if order:
                logger.info(f"✅ Limit order placed: {side} {amount} {symbol} @ {price} for user {user_id}")
                return order
            
            return None
            
        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return None
    
    async def close_position(self, user_id: int, exchange_name: str, 
                           symbol: str, percentage: float = 100.0) -> Optional[Dict[str, Any]]:
        """Close a position (partially or fully)."""
        try:
            # Get current position
            positions = self.exchange_manager.get_user_positions(user_id, exchange_name)
            position = None
            
            for pos in positions:
                if pos.get('symbol') == symbol and pos.get('size', 0) != 0:
                    position = pos
                    break
            
            if not position:
                logger.warning(f"No open position found for {symbol}")
                return None
            
            # Calculate close amount
            position_size = abs(position.get('size', 0))
            close_amount = position_size * (percentage / 100.0)
            
            # Determine close side (opposite of position)
            position_side = position.get('side', 'long')
            close_side = 'sell' if position_side == 'long' else 'buy'
            
            # Place market order to close position
            order = await self.place_market_order(
                user_id=user_id,
                exchange_name=exchange_name,
                symbol=symbol,
                side=close_side,
                amount=close_amount,
                strategy_name="Position Close"
            )
            
            if order:
                logger.info(f"✅ Position closed: {percentage}% of {symbol} for user {user_id}")
                return order
            
            return None
            
        except Exception as e:
            logger.error(f"Close position error: {e}")
            return None
    
    async def set_stop_loss(self, user_id: int, exchange_name: str,
                          symbol: str, stop_price: float) -> bool:
        """Set stop-loss for a position."""
        try:
            # Get current position
            positions = self.exchange_manager.get_user_positions(user_id, exchange_name)
            position = None
            
            for pos in positions:
                if pos.get('symbol') == symbol and pos.get('size', 0) != 0:
                    position = pos
                    break
            
            if not position:
                logger.warning(f"No open position found for {symbol}")
                return False
            
            # Create stop-loss order
            position_side = position.get('side', 'long')
            stop_side = 'sell' if position_side == 'long' else 'buy'
            position_size = abs(position.get('size', 0))
            
            # Place stop order (implementation depends on exchange)
            # For now, we'll store it in our database for monitoring
            await self._store_stop_order(user_id, exchange_name, symbol, 
                                       stop_side, position_size, stop_price, 'stop_loss')
            
            logger.info(f"✅ Stop-loss set: {symbol} @ {stop_price} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Set stop-loss error: {e}")
            return False
    
    async def set_take_profit(self, user_id: int, exchange_name: str,
                            symbol: str, target_price: float) -> bool:
        """Set take-profit for a position."""
        try:
            # Get current position
            positions = self.exchange_manager.get_user_positions(user_id, exchange_name)
            position = None
            
            for pos in positions:
                if pos.get('symbol') == symbol and pos.get('size', 0) != 0:
                    position = pos
                    break
            
            if not position:
                logger.warning(f"No open position found for {symbol}")
                return False
            
            # Create take-profit order
            position_side = position.get('side', 'long')
            tp_side = 'sell' if position_side == 'long' else 'buy'
            position_size = abs(position.get('size', 0))
            
            # Store take-profit order for monitoring
            await self._store_stop_order(user_id, exchange_name, symbol,
                                       tp_side, position_size, target_price, 'take_profit')
            
            logger.info(f"✅ Take-profit set: {symbol} @ {target_price} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Set take-profit error: {e}")
            return False
    
    def _validate_order_params(self, user_id: int, exchange_name: str,
                             symbol: str, side: str, amount: float, 
                             price: Optional[float] = None) -> bool:
        """Validate order parameters."""
        try:
            # Basic validation
            if not all([user_id, exchange_name, symbol, side, amount]):
                logger.error("Missing required order parameters")
                return False
            
            if side not in ['buy', 'sell']:
                logger.error(f"Invalid side: {side}")
                return False
            
            if amount <= 0:
                logger.error(f"Invalid amount: {amount}")
                return False
            
            if price is not None and price <= 0:
                logger.error(f"Invalid price: {price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Order validation error: {e}")
            return False
    
    async def _check_risk_limits(self, user_id: int, exchange_name: str,
                               symbol: str, amount: float) -> bool:
        """Check risk management limits."""
        try:
            # Check daily trade limit
            today = datetime.now().date()
            cursor = self.db.conn.execute("""
                SELECT COUNT(*) FROM user_trades 
                WHERE user_id = ? AND DATE(timestamp) = ?
            """, (user_id, today.isoformat()))
            
            daily_trades = cursor.fetchone()[0]
            if daily_trades >= self.max_daily_trades:
                logger.warning(f"Daily trade limit exceeded: {daily_trades}")
                return False
            
            # Check position size limit
            # This is a simplified check - in production, you'd want more sophisticated risk management
            if amount * 50000 > self.max_position_size:  # Assuming ~$50k per BTC
                logger.warning(f"Position size limit exceeded: {amount}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Risk check error: {e}")
            return False
    
    async def _record_trade(self, user_id: int, exchange_name: str, symbol: str,
                          side: str, size: float, price: float, fee: float,
                          order_type: str, strategy_name: str):
        """Record trade in database."""
        try:
            self.db.conn.execute("""
                INSERT INTO user_trades 
                (user_id, exchange_name, symbol, side, size, price, fee, order_type, strategy_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (user_id, exchange_name, symbol, side, size, price, fee, order_type, strategy_name))
            
            self.db.conn.commit()
            logger.debug(f"Trade recorded: {side} {size} {symbol} @ {price}")
            
        except Exception as e:
            logger.error(f"Trade recording error: {e}")
    
    async def _update_position(self, user_id: int, exchange_name: str, symbol: str,
                             side: str, amount: float, price: float):
        """Update position in database."""
        try:
            # Get existing position
            cursor = self.db.conn.execute("""
                SELECT id, size, entry_price FROM user_positions 
                WHERE user_id = ? AND exchange_name = ? AND symbol = ? AND status = 'open'
            """, (user_id, exchange_name, symbol))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing position
                pos_id, current_size, entry_price = existing
                
                if side == 'buy':
                    new_size = current_size + amount
                    new_entry_price = ((current_size * entry_price) + (amount * price)) / new_size
                else:
                    new_size = current_size - amount
                    new_entry_price = entry_price  # Keep original entry price
                
                if abs(new_size) < 0.0001:  # Position closed
                    self.db.conn.execute("""
                        UPDATE user_positions 
                        SET status = 'closed', closed_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (pos_id,))
                else:
                    self.db.conn.execute("""
                        UPDATE user_positions 
                        SET size = ?, entry_price = ?
                        WHERE id = ?
                    """, (new_size, new_entry_price, pos_id))
            else:
                # Create new position
                position_side = 'long' if side == 'buy' else 'short'
                position_size = amount if side == 'buy' else -amount
                
                self.db.conn.execute("""
                    INSERT INTO user_positions 
                    (user_id, exchange_name, symbol, side, size, entry_price)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (user_id, exchange_name, symbol, position_side, position_size, price))
            
            self.db.conn.commit()
            
        except Exception as e:
            logger.error(f"Position update error: {e}")
    
    async def _store_stop_order(self, user_id: int, exchange_name: str, symbol: str,
                              side: str, amount: float, trigger_price: float, order_type: str):
        """Store stop/take-profit order for monitoring."""
        try:
            # Create a simple stop orders table if it doesn't exist
            self.db.conn.execute("""
                CREATE TABLE IF NOT EXISTS user_stop_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    exchange_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    amount REAL NOT NULL,
                    trigger_price REAL NOT NULL,
                    order_type TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            self.db.conn.execute("""
                INSERT INTO user_stop_orders 
                (user_id, exchange_name, symbol, side, amount, trigger_price, order_type)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user_id, exchange_name, symbol, side, amount, trigger_price, order_type))
            
            self.db.conn.commit()
            
        except Exception as e:
            logger.error(f"Stop order storage error: {e}")
    
    async def get_user_trades(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get user's recent trades."""
        try:
            cursor = self.db.conn.execute("""
                SELECT exchange_name, symbol, side, size, price, fee, 
                       order_type, strategy_name, timestamp
                FROM user_trades 
                WHERE user_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (user_id, limit))
            
            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'exchange': row[0],
                    'symbol': row[1],
                    'side': row[2],
                    'size': row[3],
                    'price': row[4],
                    'fee': row[5],
                    'order_type': row[6],
                    'strategy': row[7],
                    'timestamp': row[8]
                })
            
            return trades
            
        except Exception as e:
            logger.error(f"Get trades error: {e}")
            return []
    
    async def get_user_positions(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user's open positions."""
        try:
            cursor = self.db.conn.execute("""
                SELECT exchange_name, symbol, side, size, entry_price, 
                       current_price, pnl, leverage, created_at
                FROM user_positions 
                WHERE user_id = ? AND status = 'open'
                ORDER BY created_at DESC
            """, (user_id,))
            
            positions = []
            for row in cursor.fetchall():
                positions.append({
                    'exchange': row[0],
                    'symbol': row[1],
                    'side': row[2],
                    'size': row[3],
                    'entry_price': row[4],
                    'current_price': row[5],
                    'pnl': row[6],
                    'leverage': row[7],
                    'created_at': row[8]
                })
            
            return positions
            
        except Exception as e:
            logger.error(f"Get positions error: {e}")
            return []
