2025-05-24 18:21:30,753 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 18:21:30,753 - testnet-launcher - INFO - ==================================================
2025-05-24 18:21:30,754 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T18:21:30.754181
2025-05-24 18:21:30,754 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 18:21:30,754 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 18:21:30,754 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 18:21:30,754 - testnet-launcher - INFO - ==================================================
2025-05-24 18:21:30,758 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 18:21:30,767 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 18:21:30,768 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 18:21:30,768 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 18:21:30,768 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 18:21:30,768 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 18:21:30,779 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 18:21:30,780 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 18:21:30,787 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 18:21:30,791 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 18:21:30,792 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 18:21:30,792 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 18:21:32,012 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:21:33,545 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:21:33,546 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 18:21:33,546 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 18:21:33,547 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 18:21:33,547 - orchestrator - INFO - Starting orchestrator...
2025-05-24 18:21:34,776 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 18:21:34,776 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 18:21:34,776 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 18:21:34,777 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 18:21:34,777 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 18:21:35,972 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 18:21:35,973 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 18:21:35,973 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 18:21:35,973 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 18:21:35,974 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 18:21:35,975 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 18:21:35,976 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 18:21:35,977 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 00:00:00+00:00
2025-05-24 18:21:35,978 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 18:21:35,978 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 18:21:35,978 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 18:21:35,978 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 18:21:35,979 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 18:21:35,979 - llm_consumer - INFO - LLM Consumer started
2025-05-24 18:21:35,980 - orchestrator - INFO - LLM Consumer started
2025-05-24 18:21:35,981 - position_manager - INFO - Position monitoring started
2025-05-24 18:21:35,981 - orchestrator - INFO - Position manager started
2025-05-24 18:21:35,981 - orchestrator - INFO - Starting event loop
2025-05-24 18:21:35,981 - orchestrator - INFO - Started bus maintenance task
2025-05-24 18:21:35,981 - position_manager - INFO - Starting position monitor loop
2025-05-24 18:21:35,982 - orchestrator - INFO - Starting account information update task
2025-05-24 18:21:35,982 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 18:21:35,982 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 18:21:35,989 - orchestrator - INFO - Starting health check task
2025-05-24 18:21:35,990 - orchestrator - INFO - Starting position monitoring task
2025-05-24 18:21:35,990 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 18:21:35,994 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 18:21:35,998 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 18:21:36,721 - feeds.htx_funding - WARNING - Used text fallback for funding rate JSON parsing: content_type='text/plain'
2025-05-24 18:21:36,721 - feeds.htx_funding - INFO - Successfully fetched funding rate for BTC-USDT: 0.********
2025-05-24 18:47:46,439 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 18:47:46,439 - testnet-launcher - INFO - ==================================================
2025-05-24 18:47:46,439 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T18:47:46.***********-05-24 18:47:46,440 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 18:47:46,440 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 18:47:46,440 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 18:47:46,440 - testnet-launcher - INFO - ==================================================
2025-05-24 18:47:46,440 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 18:47:46,442 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 18:47:46,442 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 18:47:46,442 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 18:47:46,443 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 18:47:46,443 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 18:47:46,446 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 18:47:46,446 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 18:47:46,448 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 18:47:46,449 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 18:47:46,450 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 18:47:46,450 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 18:47:47,536 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:47:48,667 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:47:48,669 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 18:47:48,669 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 18:47:48,670 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 18:47:48,670 - orchestrator - INFO - Starting orchestrator...
2025-05-24 18:47:49,885 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 18:47:49,886 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 18:47:49,886 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 18:47:49,886 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 18:47:49,886 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 18:47:51,084 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 18:47:51,085 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 18:47:51,085 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 18:47:51,085 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 18:47:51,085 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 18:47:51,086 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 18:47:51,087 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 18:47:51,088 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 18:47:51,089 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 18:47:51,090 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 18:47:51,091 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 18:47:51,092 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 00:00:00+00:00
2025-05-24 18:47:51,093 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 18:47:51,093 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 18:47:51,093 - llm_consumer - INFO - LLM Consumer started
2025-05-24 18:47:51,093 - orchestrator - INFO - LLM Consumer started
2025-05-24 18:47:51,093 - position_manager - INFO - Position monitoring started
2025-05-24 18:47:51,093 - orchestrator - INFO - Position manager started
2025-05-24 18:47:51,094 - orchestrator - INFO - Starting event loop
2025-05-24 18:47:51,094 - orchestrator - INFO - Started bus maintenance task
2025-05-24 18:47:51,094 - position_manager - INFO - Starting position monitor loop
2025-05-24 18:47:51,094 - orchestrator - INFO - Starting account information update task
2025-05-24 18:47:51,094 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 18:47:51,094 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 18:47:51,102 - orchestrator - INFO - Starting health check task
2025-05-24 18:47:51,102 - orchestrator - INFO - Starting position monitoring task
2025-05-24 18:47:51,103 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 18:47:51,106 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 18:47:51,110 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 18:47:52,312 - feeds.htx_open_interest - WARNING - Used text fallback for open interest JSON parsing: content_type='text/plain'
2025-05-24 18:54:31,086 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 18:54:31,086 - testnet-launcher - INFO - ==================================================
2025-05-24 18:54:31,086 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T18:54:31.***********-05-24 18:54:31,086 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 18:54:31,086 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 18:54:31,087 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 18:54:31,087 - testnet-launcher - INFO - ==================================================
2025-05-24 18:54:31,087 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 18:54:31,089 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 18:54:31,089 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 18:54:31,089 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 18:54:31,090 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 18:54:31,090 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 18:54:31,094 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 18:54:31,094 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 18:54:31,097 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 18:54:31,097 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 18:54:31,098 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 18:54:31,099 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 18:54:32,268 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:54:33,500 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 18:54:33,501 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 18:54:33,501 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 18:54:33,502 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 18:54:33,503 - orchestrator - INFO - Starting orchestrator...
2025-05-24 18:54:34,715 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 18:54:34,716 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 18:54:34,716 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 18:54:34,716 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 18:54:34,717 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 18:54:35,890 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 18:54:35,890 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 18:54:35,890 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 18:54:35,890 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 18:54:35,890 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 18:54:35,891 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 18:54:35,892 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 18:54:35,893 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 18:54:35,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 00:00:00+00:00
2025-05-24 18:54:35,895 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 18:54:35,895 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 18:54:35,896 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 18:54:35,896 - llm_consumer - INFO - LLM Consumer started
2025-05-24 18:54:35,896 - orchestrator - INFO - LLM Consumer started
2025-05-24 18:54:35,896 - position_manager - INFO - Position monitoring started
2025-05-24 18:54:35,896 - orchestrator - INFO - Position manager started
2025-05-24 18:54:35,896 - orchestrator - INFO - Starting event loop
2025-05-24 18:54:35,897 - orchestrator - INFO - Started bus maintenance task
2025-05-24 18:54:35,897 - position_manager - INFO - Starting position monitor loop
2025-05-24 18:54:35,897 - orchestrator - INFO - Starting account information update task
2025-05-24 18:54:35,897 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 18:54:35,897 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 18:54:35,902 - orchestrator - INFO - Starting health check task
2025-05-24 18:54:35,902 - orchestrator - INFO - Starting position monitoring task
2025-05-24 18:54:35,903 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 18:54:35,907 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 18:54:35,911 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 18:54:36,409 - feeds.htx_funding - WARNING - Used text fallback for funding rate JSON parsing: content_type='text/plain'
2025-05-24 18:54:36,409 - feeds.htx_funding - INFO - Successfully fetched funding rate for BTC-USDT: 0.********
2025-05-24 19:03:08,361 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 19:03:08,361 - testnet-launcher - INFO - ==================================================
2025-05-24 19:03:08,361 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T19:03:08.***********-05-24 19:03:08,361 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 19:03:08,361 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 19:03:08,361 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 19:03:08,361 - testnet-launcher - INFO - ==================================================
2025-05-24 19:03:08,361 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 19:03:08,363 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 19:03:08,363 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 19:03:08,363 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 19:03:08,363 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 19:03:08,363 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 19:03:08,366 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 19:03:08,366 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 19:03:08,368 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 19:03:08,369 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 19:03:08,370 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 19:03:08,370 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 19:03:09,690 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 19:03:10,817 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 19:03:10,819 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 19:03:10,819 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 19:03:10,820 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 19:03:10,820 - orchestrator - INFO - Starting orchestrator...
2025-05-24 19:03:12,132 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 19:03:12,133 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 19:03:12,133 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 19:03:12,133 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 19:03:12,134 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 19:03:13,303 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 19:03:13,304 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 19:03:13,305 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 19:03:13,306 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 19:03:13,307 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 19:03:13,308 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 19:03:13,309 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 19:03:13,309 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 19:03:13,310 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 19:03:13,310 - llm_consumer - INFO - LLM Consumer started
2025-05-24 19:03:13,310 - orchestrator - INFO - LLM Consumer started
2025-05-24 19:03:13,310 - position_manager - INFO - Position monitoring started
2025-05-24 19:03:13,310 - orchestrator - INFO - Position manager started
2025-05-24 19:03:13,310 - orchestrator - INFO - Starting event loop
2025-05-24 19:03:13,310 - orchestrator - INFO - Started bus maintenance task
2025-05-24 19:03:13,310 - position_manager - INFO - Starting position monitor loop
2025-05-24 19:03:13,311 - orchestrator - INFO - Starting account information update task
2025-05-24 19:03:13,311 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 19:03:13,311 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 19:03:13,319 - orchestrator - INFO - Starting health check task
2025-05-24 19:03:13,319 - orchestrator - INFO - Starting position monitoring task
2025-05-24 19:03:13,319 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 19:03:13,325 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 19:03:13,328 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 19:03:14,057 - feeds.htx_funding - WARNING - Used text fallback for funding rate JSON parsing: content_type='text/plain'
2025-05-24 19:03:14,057 - feeds.htx_funding - INFO - Successfully fetched funding rate for BTC-USDT: 0.********
2025-05-24 19:11:12,056 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 19:11:12,056 - testnet-launcher - INFO - ==================================================
2025-05-24 19:11:12,057 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T19:11:12.***********-05-24 19:11:12,057 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 19:11:12,057 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 19:11:12,057 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 19:11:12,057 - testnet-launcher - INFO - ==================================================
2025-05-24 19:11:12,057 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 19:11:12,059 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 19:11:12,059 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 19:11:12,059 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 19:11:12,059 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 19:11:12,059 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 19:11:12,062 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 19:11:12,062 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 19:11:12,064 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 19:11:12,065 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 19:11:12,066 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 19:11:12,066 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 19:11:13,076 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 19:11:14,021 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 19:11:14,023 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 19:11:14,023 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 19:11:14,024 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 19:11:14,024 - orchestrator - INFO - Starting orchestrator...
2025-05-24 19:11:15,246 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 19:11:15,247 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 19:11:15,247 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 19:11:15,247 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 19:11:15,247 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 19:11:16,420 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 19:11:16,421 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 19:11:16,422 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 19:11:16,423 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 19:11:16,424 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 19:11:16,425 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 19:11:16,426 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 19:11:16,426 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 19:11:16,426 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 19:11:16,426 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 19:11:16,426 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 19:11:16,427 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 19:11:16,427 - llm_consumer - INFO - LLM Consumer started
2025-05-24 19:11:16,427 - orchestrator - INFO - LLM Consumer started
2025-05-24 19:11:16,427 - position_manager - INFO - Position monitoring started
2025-05-24 19:11:16,427 - orchestrator - INFO - Position manager started
2025-05-24 19:11:16,427 - orchestrator - INFO - Starting event loop
2025-05-24 19:11:16,427 - orchestrator - INFO - Started bus maintenance task
2025-05-24 19:11:16,427 - position_manager - INFO - Starting position monitor loop
2025-05-24 19:11:16,428 - orchestrator - INFO - Starting account information update task
2025-05-24 19:11:16,428 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 19:11:16,428 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 19:11:16,436 - orchestrator - INFO - Starting health check task
2025-05-24 19:11:16,436 - orchestrator - INFO - Starting position monitoring task
2025-05-24 19:11:16,436 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 19:11:16,440 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 19:11:16,444 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 19:14:05,847 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 19:14:05,847 - testnet-launcher - INFO - ==================================================
2025-05-24 19:14:05,847 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T19:14:05.***********-05-24 19:14:05,847 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 19:14:05,847 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 19:14:05,847 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 19:14:05,847 - testnet-launcher - INFO - ==================================================
2025-05-24 19:14:05,847 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 19:14:05,849 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 19:14:05,849 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 19:14:05,849 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 19:14:05,849 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 19:14:05,849 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 19:14:05,852 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 19:14:05,852 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 19:14:05,854 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 19:14:05,854 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 19:14:05,855 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 19:14:05,855 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 19:18:49,045 - llm.llama_bridge - ERROR - Failed to load LLM model: [Errno 22] Invalid argument
2025-05-24 19:18:50,251 - llm_consumer - ERROR - Error initializing LLM: [Errno 22] Invalid argument
2025-05-24 19:18:50,490 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 19:18:50,490 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 19:18:50,491 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 19:18:50,491 - orchestrator - INFO - Starting orchestrator...
2025-05-24 19:18:50,497 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 19:18:50,497 - position_manager - WARNING - Position monitoring not running
2025-05-24 19:18:50,497 - orchestrator - INFO - Position manager stopped
2025-05-24 19:18:50,497 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 19:18:50,497 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 19:18:50,497 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 19:18:50,497 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 19:18:50,497 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 19:18:50,727 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 19:18:50,727 - orchestrator - INFO - Message bus closed
2025-05-24 19:18:50,727 - orchestrator - INFO - Orchestrator stopped
2025-05-24 19:18:50,727 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 19:24:59,158 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 19:24:59,158 - testnet-launcher - INFO - ==================================================
2025-05-24 19:24:59,158 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T19:24:59.158491
2025-05-24 19:24:59,158 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 19:24:59,158 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 19:24:59,158 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 19:24:59,158 - testnet-launcher - INFO - ==================================================
2025-05-24 19:24:59,158 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 19:24:59,160 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 19:24:59,160 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 19:24:59,160 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 19:24:59,160 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 19:24:59,160 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 19:24:59,163 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 19:24:59,163 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 19:24:59,165 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 19:24:59,166 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 19:24:59,167 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 19:24:59,167 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 19:27:57,582 - llm.llama_bridge - ERROR - Failed to load LLM model: [Errno 22] Invalid argument
2025-05-24 19:27:58,675 - llm_consumer - ERROR - Error initializing LLM: [Errno 22] Invalid argument
2025-05-24 19:27:58,853 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 19:27:58,853 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 19:27:58,855 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 19:27:58,855 - orchestrator - INFO - Starting orchestrator...
2025-05-24 19:27:58,864 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 19:27:58,864 - position_manager - WARNING - Position monitoring not running
2025-05-24 19:27:58,864 - orchestrator - INFO - Position manager stopped
2025-05-24 19:27:58,864 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 19:27:58,864 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 19:27:58,864 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 19:27:58,865 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 19:27:58,865 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 19:27:58,904 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 19:27:58,905 - orchestrator - INFO - Message bus closed
2025-05-24 19:27:58,905 - orchestrator - INFO - Orchestrator stopped
2025-05-24 19:27:58,905 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 19:59:05,140 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 19:59:05,141 - testnet-launcher - INFO - ==================================================
2025-05-24 19:59:05,141 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T19:59:05.141187
2025-05-24 19:59:05,141 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 19:59:05,141 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 19:59:05,141 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 19:59:05,141 - testnet-launcher - INFO - ==================================================
2025-05-24 19:59:05,141 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 19:59:05,142 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 19:59:05,142 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 19:59:05,142 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 19:59:05,142 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 19:59:05,142 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 19:59:05,145 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 19:59:05,146 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 19:59:05,148 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 19:59:05,148 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 19:59:05,149 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 19:59:05,149 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 20:02:53,093 - llm.llama_bridge - ERROR - Failed to load LLM model: [Errno 22] Invalid argument
2025-05-24 20:02:54,339 - llm_consumer - ERROR - Error initializing LLM: [Errno 22] Invalid argument
2025-05-24 20:02:54,520 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 20:02:54,520 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 20:02:54,521 - position_manager - INFO - Position manager initialized with stop-loss: 2.0%, take-profit: 4.0%, trailing-stop: True
2025-05-24 20:02:54,521 - orchestrator - INFO - Starting orchestrator...
2025-05-24 20:02:54,528 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 20:02:54,528 - position_manager - WARNING - Position monitoring not running
2025-05-24 20:02:54,528 - orchestrator - INFO - Position manager stopped
2025-05-24 20:02:54,528 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 20:02:54,528 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 20:02:54,528 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 20:02:54,529 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 20:02:54,529 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 20:02:54,994 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 20:02:54,994 - orchestrator - INFO - Message bus closed
2025-05-24 20:02:54,994 - orchestrator - INFO - Orchestrator stopped
2025-05-24 20:02:54,994 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 20:06:46,793 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 20:06:46,793 - testnet-launcher - INFO - ==================================================
2025-05-24 20:06:46,793 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T20:06:46.793843
2025-05-24 20:06:46,793 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 20:06:46,793 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 20:06:46,793 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 20:06:46,793 - testnet-launcher - INFO - ==================================================
2025-05-24 20:06:46,793 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 20:06:46,796 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 20:06:46,796 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 20:06:46,796 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 20:06:46,796 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 20:06:46,796 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 20:06:48,953 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 20:06:48,957 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 20:06:48,959 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 20:06:48,960 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 20:06:48,960 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 20:06:48,961 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 20:06:48,961 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 20:06:48,962 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 20:06:48,962 - orchestrator - INFO - Starting orchestrator...
2025-05-24 20:06:50,181 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 20:06:50,181 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 20:06:50,182 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 20:06:50,182 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 20:06:50,182 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 20:06:51,500 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 20:06:51,500 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 20:06:51,500 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 20:06:51,500 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 20:06:51,500 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 20:06:51,501 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 20:10:07,946 - orchestrator - INFO - Keyboard interrupt received
2025-05-24 20:10:07,946 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 20:10:07,946 - position_manager - WARNING - Position monitoring not running
2025-05-24 20:10:07,946 - orchestrator - INFO - Position manager stopped
2025-05-24 20:10:07,946 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 20:10:07,946 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 20:10:07,946 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 20:10:07,947 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 20:10:07,947 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 20:10:08,057 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 20:10:08,057 - orchestrator - INFO - Message bus closed
2025-05-24 20:10:08,057 - orchestrator - INFO - Orchestrator stopped
2025-05-24 20:10:08,057 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 20:11:11,332 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 20:11:11,332 - testnet-launcher - INFO - ==================================================
2025-05-24 20:11:11,332 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T20:11:11.332877
2025-05-24 20:11:11,332 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 20:11:11,332 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 20:11:11,332 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 20:11:11,332 - testnet-launcher - INFO - ==================================================
2025-05-24 20:11:11,332 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 20:11:11,335 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 20:11:11,335 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 20:11:11,335 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 20:11:11,335 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 20:11:11,335 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 20:11:12,412 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 20:11:12,415 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 20:11:12,416 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 20:11:12,417 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 20:11:12,417 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 20:11:12,418 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 20:11:12,418 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 20:11:12,418 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 20:11:12,418 - orchestrator - INFO - Starting orchestrator...
2025-05-24 20:11:13,614 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 20:11:13,614 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 20:11:13,614 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 20:11:13,614 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 20:11:13,614 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 20:11:14,812 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 20:44:42,197 - orchestrator - INFO - Keyboard interrupt received
2025-05-24 20:44:42,198 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 20:44:42,198 - position_manager - WARNING - Position monitoring not running
2025-05-24 20:44:42,198 - orchestrator - INFO - Position manager stopped
2025-05-24 20:44:42,198 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 20:44:42,198 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 20:44:42,198 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 20:44:42,200 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 20:44:42,200 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 20:44:42,233 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 20:44:42,233 - orchestrator - INFO - Message bus closed
2025-05-24 20:44:42,233 - orchestrator - INFO - Orchestrator stopped
2025-05-24 20:44:42,233 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 21:12:03,447 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:12:03,447 - testnet-launcher - INFO - ==================================================
2025-05-24 21:12:03,447 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:12:03.447568
2025-05-24 21:12:03,447 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:12:03,447 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:12:03,447 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:12:03,447 - testnet-launcher - INFO - ==================================================
2025-05-24 21:12:03,447 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:12:03,449 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:12:03,450 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:12:03,450 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:12:03,450 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:12:03,450 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:12:03,453 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 21:12:03,454 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 21:12:03,457 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:12:03,458 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:12:03,478 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:12:03,478 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:12:03,479 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:12:03,480 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:12:03,481 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:12:03,481 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:12:04,718 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 21:12:04,718 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 21:12:04,718 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 21:12:04,718 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 21:12:04,719 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 21:12:05,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 21:12:05,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 21:12:05,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 21:12:05,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 21:12:05,894 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 21:17:45,940 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 21:17:45,941 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:17:45,941 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:17:45,941 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:17:45,941 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:17:45,942 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 21:17:45,942 - llm_consumer - INFO - LLM Consumer started
2025-05-24 21:17:45,942 - orchestrator - INFO - LLM Consumer started
2025-05-24 21:17:45,942 - position_manager - INFO - Position monitoring started
2025-05-24 21:17:45,942 - orchestrator - INFO - Position manager started
2025-05-24 21:17:45,942 - orchestrator - INFO - Starting event loop
2025-05-24 21:17:45,942 - orchestrator - INFO - Started bus maintenance task
2025-05-24 21:17:45,942 - position_manager - INFO - Starting position monitor loop
2025-05-24 21:17:45,962 - orchestrator - INFO - Event loop cancelled
2025-05-24 21:17:45,962 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 21:17:46,058 - feeds.htx_futures - INFO - WebSocket connection closed
2025-05-24 21:17:46,058 - feeds.htx_futures - INFO - Reconnecting to market WebSocket...
2025-05-24 21:17:46,942 - position_manager - INFO - Position monitoring stopped
2025-05-24 21:17:46,942 - orchestrator - INFO - Position manager stopped
2025-05-24 21:17:46,943 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 21:17:46,943 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 21:17:46,943 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 21:17:46,943 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 21:17:46,943 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 21:17:46,995 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 21:17:46,996 - orchestrator - INFO - Message bus closed
2025-05-24 21:17:46,996 - orchestrator - INFO - Orchestrator stopped
2025-05-24 21:17:46,996 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 21:20:40,453 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:20:40,453 - testnet-launcher - INFO - ==================================================
2025-05-24 21:20:40,453 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:20:40.453240
2025-05-24 21:20:40,453 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:20:40,453 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:20:40,453 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:20:40,453 - testnet-launcher - INFO - ==================================================
2025-05-24 21:20:40,453 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:20:40,454 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:20:40,455 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:20:40,455 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:20:40,455 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:20:40,455 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:20:40,458 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 21:20:40,459 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 21:20:40,461 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:20:40,461 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:20:40,462 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:20:40,463 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:20:40,464 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:20:40,464 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:20:40,464 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:20:40,464 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:20:41,653 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 21:20:41,654 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 21:20:41,654 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 21:20:41,654 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 21:20:41,654 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 21:20:42,843 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 21:20:42,843 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 21:20:42,843 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 21:20:42,843 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 21:20:42,844 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 21:22:51,691 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 21:22:51,692 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 21:22:51,693 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:22:51,693 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 21:22:51,693 - llm_consumer - INFO - LLM Consumer started
2025-05-24 21:22:51,693 - orchestrator - INFO - LLM Consumer started
2025-05-24 21:22:51,693 - position_manager - INFO - Position monitoring started
2025-05-24 21:22:51,693 - orchestrator - INFO - Position manager started
2025-05-24 21:22:51,693 - orchestrator - INFO - Starting event loop
2025-05-24 21:22:51,693 - orchestrator - INFO - Started bus maintenance task
2025-05-24 21:22:51,694 - position_manager - INFO - Starting position monitor loop
2025-05-24 21:22:51,715 - orchestrator - INFO - Event loop cancelled
2025-05-24 21:22:51,715 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 21:22:51,943 - feeds.htx_futures - INFO - WebSocket connection closed
2025-05-24 21:22:51,943 - feeds.htx_futures - INFO - Reconnecting to market WebSocket...
2025-05-24 21:22:52,708 - position_manager - INFO - Position monitoring stopped
2025-05-24 21:22:52,708 - orchestrator - INFO - Position manager stopped
2025-05-24 21:22:52,708 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 21:22:52,708 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 21:22:52,708 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 21:22:52,708 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 21:22:52,708 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 21:22:53,162 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 21:22:53,162 - orchestrator - INFO - Message bus closed
2025-05-24 21:22:53,162 - orchestrator - INFO - Orchestrator stopped
2025-05-24 21:22:53,162 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 21:23:03,051 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:23:03,052 - testnet-launcher - INFO - ==================================================
2025-05-24 21:23:03,052 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:23:03.052928
2025-05-24 21:23:03,052 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:23:03,052 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:23:03,053 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:23:03,053 - testnet-launcher - INFO - ==================================================
2025-05-24 21:23:03,053 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:23:03,055 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:23:03,055 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:23:03,055 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:23:03,056 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:23:03,056 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:23:09,603 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 21:23:09,607 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:23:09,607 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:23:09,609 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:23:09,609 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:23:09,610 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:23:09,610 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:23:09,611 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:23:09,611 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:23:10,816 - feeds.htx_futures - INFO - Connected to HTX Futures market WebSocket
2025-05-24 21:23:10,816 - feeds.htx_futures - INFO - Connected to HTX Futures API
2025-05-24 21:23:10,816 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.kline.15min
2025-05-24 21:23:10,818 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.trade.detail
2025-05-24 21:23:10,818 - feeds.htx_futures - INFO - Subscribed to market channel: market.BTC-USDT.depth.step0
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.4522779169388e-05 at 2025-05-25 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4105380709449e-05 at 2025-05-24 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.5746802538034e-05 at 2025-05-24 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.080802710673e-06 at 2025-05-24 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.3561780833068e-05 at 2025-05-23 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8294906614722e-05 at 2025-05-23 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.7316573363865e-05 at 2025-05-23 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.4209907708378e-05 at 2025-05-22 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000138051929488006 at 2025-05-22 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6591327314706e-05 at 2025-05-22 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000113721471995802 at 2025-05-21 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -5.34557755009e-05 at 2025-05-21 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.6856613213348e-05 at 2025-05-21 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.719953589356e-06 at 2025-05-20 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.486554761213e-06 at 2025-05-20 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.8764078010916e-05 at 2025-05-20 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.3482126148634e-05 at 2025-05-19 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-19 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.7834022634089e-05 at 2025-05-19 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.4603241859277e-05 at 2025-05-18 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.69212814454e-07 at 2025-05-18 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.8044375813686e-05 at 2025-05-18 00:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0003156065514e-05 at 2025-05-17 16:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: -6.012618327909e-06 at 2025-05-17 08:00:00+00:00
2025-05-24 21:23:12,000 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.5355055752863e-05 at 2025-05-17 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -3.0142521498504e-05 at 2025-05-16 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.8711099259886e-05 at 2025-05-16 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.6815762934024e-05 at 2025-05-16 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.986995744638e-06 at 2025-05-15 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.7934523891965e-05 at 2025-05-15 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.73474843624e-07 at 2025-05-15 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.7283598748723e-05 at 2025-05-14 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.2079566158193e-05 at 2025-05-14 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9713787758718e-05 at 2025-05-14 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.8514256021968e-05 at 2025-05-13 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.1164775480602e-05 at 2025-05-13 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 4.0128033419669e-05 at 2025-05-13 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 1.9300730580161e-05 at 2025-05-12 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.1799965644125e-05 at 2025-05-12 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 3.341284884382e-05 at 2025-05-12 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.4716642381189e-05 at 2025-05-11 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 7.8860359601193e-05 at 2025-05-11 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -1.2874743878851e-05 at 2025-05-11 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -7.636112910541e-06 at 2025-05-10 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.535711546116e-05 at 2025-05-10 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 2.9073223572217e-05 at 2025-05-10 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.147116111385e-06 at 2025-05-09 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0156621587794e-05 at 2025-05-09 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 8.624531884034e-05 at 2025-05-09 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -8.1003539888703e-05 at 2025-05-08 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000157666091905947 at 2025-05-08 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 5.6439736829815e-05 at 2025-05-08 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -0.000106722287286226 at 2025-05-07 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: -4.0382500432016e-05 at 2025-05-07 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 6.760409278087e-05 at 2025-05-07 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-06 00:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 16:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Stored funding rate for BTC-USDT: 0.0001 at 2025-05-05 08:00:00+00:00
2025-05-24 21:23:12,002 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:23:12,002 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:23:12,002 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:23:12,002 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:23:12,002 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:23:12,002 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:23:12,004 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:23:12,004 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:23:12,004 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:23:12,004 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:23:12,004 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 21:23:12,004 - llm_consumer - INFO - LLM Consumer started
2025-05-24 21:23:12,004 - orchestrator - INFO - LLM Consumer started
2025-05-24 21:23:12,004 - position_manager - INFO - Position monitoring started
2025-05-24 21:23:12,004 - orchestrator - INFO - Position manager started
2025-05-24 21:23:12,004 - orchestrator - INFO - Starting event loop
2025-05-24 21:23:12,004 - orchestrator - INFO - Started bus maintenance task
2025-05-24 21:23:12,004 - position_manager - INFO - Starting position monitor loop
2025-05-24 21:23:12,004 - orchestrator - INFO - Starting account information update task
2025-05-24 21:23:12,004 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 21:23:12,004 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 21:23:12,021 - orchestrator - INFO - Starting health check task
2025-05-24 21:23:12,022 - orchestrator - INFO - Starting position monitoring task
2025-05-24 21:23:12,022 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 21:23:12,025 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 21:23:12,027 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 21:28:15,017 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:28:15,017 - testnet-launcher - INFO - ==================================================
2025-05-24 21:28:15,017 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:28:15.***********-05-24 21:28:15,017 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:28:15,017 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:28:15,017 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:28:15,017 - testnet-launcher - INFO - ==================================================
2025-05-24 21:28:15,017 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:28:15,019 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:28:15,019 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:28:15,019 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:28:15,019 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:28:15,019 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:28:15,022 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 21:28:15,023 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 21:28:15,025 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:28:15,026 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:28:15,027 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:28:15,027 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:28:15,028 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:28:15,028 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:28:15,029 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:28:15,029 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:28:16,212 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 21:28:17,421 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:28:17,421 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 21:28:17,421 - llm_consumer - INFO - LLM Consumer started
2025-05-24 21:33:18,636 - orchestrator - INFO - LLM Consumer started
2025-05-24 21:33:18,636 - position_manager - INFO - Position monitoring started
2025-05-24 21:33:18,636 - orchestrator - INFO - Position manager started
2025-05-24 21:33:18,636 - orchestrator - INFO - Starting event loop
2025-05-24 21:33:18,636 - orchestrator - INFO - Started bus maintenance task
2025-05-24 21:33:18,637 - position_manager - INFO - Starting position monitor loop
2025-05-24 21:33:18,655 - orchestrator - INFO - Event loop cancelled
2025-05-24 21:33:18,655 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 21:33:18,896 - feeds.htx_futures - INFO - WebSocket connection closed
2025-05-24 21:33:18,897 - feeds.htx_futures - INFO - Reconnecting to market WebSocket...
2025-05-24 21:33:19,640 - position_manager - INFO - Position monitoring stopped
2025-05-24 21:33:19,640 - orchestrator - INFO - Position manager stopped
2025-05-24 21:33:19,640 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 21:33:19,640 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 21:33:19,640 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 21:33:19,640 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 21:33:19,640 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 21:33:20,008 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 21:33:20,008 - orchestrator - INFO - Message bus closed
2025-05-24 21:33:20,008 - orchestrator - INFO - Orchestrator stopped
2025-05-24 21:33:20,008 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 21:44:59,510 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:44:59,510 - testnet-launcher - INFO - ==================================================
2025-05-24 21:44:59,510 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:44:59.510877
2025-05-24 21:44:59,510 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:44:59,510 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:44:59,510 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:44:59,510 - testnet-launcher - INFO - ==================================================
2025-05-24 21:44:59,511 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:44:59,512 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:44:59,512 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:44:59,513 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:44:59,513 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:44:59,513 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:44:59,516 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 21:44:59,517 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 21:44:59,519 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:44:59,519 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:44:59,520 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:44:59,520 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:44:59,521 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:44:59,521 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:44:59,522 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:44:59,522 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:45:00,714 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 21:45:01,929 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:45:01,930 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 21:45:01,930 - llm_consumer - INFO - LLM Consumer started
2025-05-24 21:45:43,022 - orchestrator - INFO - LLM Consumer started
2025-05-24 21:45:43,022 - position_manager - INFO - Position monitoring started
2025-05-24 21:45:43,022 - orchestrator - INFO - Position manager started
2025-05-24 21:45:43,023 - orchestrator - INFO - Starting event loop
2025-05-24 21:45:43,023 - orchestrator - INFO - Started bus maintenance task
2025-05-24 21:45:43,023 - position_manager - INFO - Starting position monitor loop
2025-05-24 21:45:43,042 - orchestrator - INFO - Event loop cancelled
2025-05-24 21:45:43,042 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 21:45:43,699 - feeds.htx_futures - INFO - Market WebSocket is closed, attempting to reconnect...
2025-05-24 21:45:43,699 - feeds.htx_futures - INFO - Reconnecting to market WebSocket...
2025-05-24 21:45:44,029 - position_manager - INFO - Position monitoring stopped
2025-05-24 21:45:44,029 - orchestrator - INFO - Position manager stopped
2025-05-24 21:45:44,029 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 21:45:44,029 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 21:45:44,030 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 21:45:44,030 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 21:45:44,030 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 21:45:44,074 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 21:45:44,074 - orchestrator - INFO - Message bus closed
2025-05-24 21:45:44,074 - orchestrator - INFO - Orchestrator stopped
2025-05-24 21:45:44,074 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-24 21:49:44,722 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 21:49:44,722 - testnet-launcher - INFO - ==================================================
2025-05-24 21:49:44,722 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T21:49:44.722116
2025-05-24 21:49:44,722 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 21:49:44,722 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 21:49:44,722 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 21:49:44,722 - testnet-launcher - INFO - ==================================================
2025-05-24 21:49:44,722 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 21:49:44,723 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 21:49:44,723 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 21:49:44,723 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 21:49:44,723 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 21:49:44,723 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 21:49:44,727 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-24 21:49:44,727 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-24 21:49:44,729 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 21:49:44,730 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 21:49:44,731 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 21:49:44,731 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 21:49:44,732 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 21:49:44,732 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 21:49:44,732 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 21:49:44,732 - orchestrator - INFO - Starting orchestrator...
2025-05-24 21:49:45,963 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 21:49:47,213 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 21:49:47,213 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 21:49:47,213 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 21:49:47,213 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 21:49:47,213 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 21:49:47,214 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:08:25,730 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 22:08:25,730 - testnet-launcher - INFO - ==================================================
2025-05-24 22:08:25,730 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T22:08:25.730339
2025-05-24 22:08:25,730 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 22:08:25,730 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 22:08:25,730 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 22:08:25,730 - testnet-launcher - INFO - ==================================================
2025-05-24 22:08:25,730 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 22:08:25,734 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 22:08:25,736 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 22:08:25,736 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 22:08:25,736 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 22:08:25,737 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 22:08:26,748 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 22:08:26,750 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 22:08:26,752 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 22:08:26,753 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 22:08:26,753 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 22:08:26,755 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 22:08:26,755 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 22:08:26,756 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 22:08:26,756 - orchestrator - INFO - Starting orchestrator...
2025-05-24 22:08:28,014 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 22:08:29,200 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 22:08:29,200 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 22:08:29,205 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 22:08:29,205 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:08:29,205 - llm_consumer - INFO - LLM Consumer started
2025-05-24 22:08:29,205 - orchestrator - INFO - LLM Consumer started
2025-05-24 22:08:29,205 - position_manager - INFO - Position monitoring started
2025-05-24 22:08:29,205 - orchestrator - INFO - Position manager started
2025-05-24 22:08:29,205 - orchestrator - INFO - Starting event loop
2025-05-24 22:08:29,205 - orchestrator - INFO - Started bus maintenance task
2025-05-24 22:08:29,205 - position_manager - INFO - Starting position monitor loop
2025-05-24 22:08:29,205 - orchestrator - INFO - Starting account information update task
2025-05-24 22:08:29,207 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 22:08:29,207 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 22:08:29,214 - orchestrator - INFO - Starting health check task
2025-05-24 22:08:29,214 - orchestrator - INFO - Starting position monitoring task
2025-05-24 22:08:29,214 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 22:08:29,219 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 22:08:29,220 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 22:08:29,730 - feeds.htx_funding - WARNING - Used text fallback for funding rate JSON parsing: content_type='text/plain'
2025-05-24 22:08:29,730 - feeds.htx_funding - INFO - Successfully fetched funding rate for BTC-USDT: 0.********
2025-05-24 22:10:21,215 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 22:10:21,216 - testnet-launcher - INFO - ==================================================
2025-05-24 22:10:21,216 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T22:10:21.***********-05-24 22:10:21,216 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 22:10:21,216 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 22:10:21,216 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 22:10:21,216 - testnet-launcher - INFO - ==================================================
2025-05-24 22:10:21,216 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 22:10:21,218 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 22:10:21,218 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 22:10:21,218 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 22:10:21,218 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 22:10:21,218 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 22:10:22,205 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 22:10:22,210 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 22:10:22,210 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 22:10:22,210 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 22:10:22,210 - llm_consumer - INFO - Using dummy LLM mode
2025-05-24 22:10:22,214 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 22:10:22,214 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 22:10:22,215 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 22:10:22,216 - orchestrator - INFO - Starting orchestrator...
2025-05-24 22:10:23,422 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 22:10:24,620 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 22:10:24,621 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 22:10:24,621 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 22:10:24,622 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:10:24,622 - llm_consumer - INFO - LLM Consumer started
2025-05-24 22:10:24,622 - orchestrator - INFO - LLM Consumer started
2025-05-24 22:10:24,622 - position_manager - INFO - Position monitoring started
2025-05-24 22:10:24,622 - orchestrator - INFO - Position manager started
2025-05-24 22:10:24,622 - orchestrator - INFO - Starting event loop
2025-05-24 22:10:24,622 - orchestrator - INFO - Started bus maintenance task
2025-05-24 22:10:24,622 - position_manager - INFO - Starting position monitor loop
2025-05-24 22:10:24,622 - orchestrator - INFO - Starting account information update task
2025-05-24 22:10:24,622 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 22:10:24,622 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 22:10:24,630 - orchestrator - INFO - Starting health check task
2025-05-24 22:10:24,630 - orchestrator - INFO - Starting position monitoring task
2025-05-24 22:10:24,630 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 22:10:24,635 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 22:10:24,637 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 22:23:44,080 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 22:23:44,080 - testnet-launcher - INFO - ==================================================
2025-05-24 22:23:44,080 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T22:23:44.***********-05-24 22:23:44,080 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 22:23:44,080 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 22:23:44,080 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 22:23:44,080 - testnet-launcher - INFO - ==================================================
2025-05-24 22:23:44,080 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 22:23:44,082 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 22:23:44,082 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 22:23:44,082 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 22:23:44,083 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 22:23:44,083 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 22:23:45,156 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 22:23:45,159 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 22:23:45,160 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 22:23:45,162 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 22:23:45,162 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 22:23:46,195 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:23:47,362 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:23:47,364 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 22:23:47,364 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 22:23:47,365 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 22:23:47,365 - orchestrator - INFO - Starting orchestrator...
2025-05-24 22:23:48,580 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 22:23:49,798 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 22:23:49,798 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 22:23:49,798 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 22:23:49,798 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 22:23:49,799 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 22:23:49,799 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 22:23:49,799 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 22:23:49,799 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 22:23:49,799 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 22:23:49,800 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 22:23:49,800 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:23:49,800 - llm_consumer - INFO - LLM Consumer started
2025-05-24 22:23:49,800 - orchestrator - INFO - LLM Consumer started
2025-05-24 22:23:49,800 - position_manager - INFO - Position monitoring started
2025-05-24 22:23:49,800 - orchestrator - INFO - Position manager started
2025-05-24 22:23:49,801 - orchestrator - INFO - Starting event loop
2025-05-24 22:23:49,801 - orchestrator - INFO - Started bus maintenance task
2025-05-24 22:23:49,801 - position_manager - INFO - Starting position monitor loop
2025-05-24 22:23:49,801 - orchestrator - INFO - Starting account information update task
2025-05-24 22:23:49,801 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 22:23:49,801 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 22:23:49,806 - orchestrator - INFO - Starting health check task
2025-05-24 22:23:49,806 - orchestrator - INFO - Starting position monitoring task
2025-05-24 22:23:49,806 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 22:23:49,808 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 22:23:49,810 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 22:39:40,073 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 22:39:40,073 - testnet-launcher - INFO - ==================================================
2025-05-24 22:39:40,073 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T22:39:40.***********-05-24 22:39:40,073 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 22:39:40,073 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 22:39:40,073 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 22:39:40,073 - testnet-launcher - INFO - ==================================================
2025-05-24 22:39:40,073 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 22:39:40,073 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 22:39:40,073 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 22:39:40,073 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 22:39:40,073 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 22:39:40,073 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 22:39:41,078 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 22:39:41,080 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 22:39:41,080 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 22:39:41,082 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 22:39:41,082 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 22:39:42,101 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:39:43,143 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:39:43,144 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 22:39:43,144 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 22:39:43,145 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 22:39:43,145 - orchestrator - INFO - Starting orchestrator...
2025-05-24 22:39:44,627 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 22:39:45,798 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 22:39:45,798 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 22:39:45,800 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 22:39:45,800 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 22:39:45,800 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 22:39:45,800 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:39:45,800 - llm_consumer - INFO - LLM Consumer started
2025-05-24 22:39:45,800 - orchestrator - INFO - LLM Consumer started
2025-05-24 22:39:45,800 - position_manager - INFO - Position monitoring started
2025-05-24 22:39:45,800 - orchestrator - INFO - Position manager started
2025-05-24 22:39:45,800 - orchestrator - INFO - Starting event loop
2025-05-24 22:39:45,800 - orchestrator - INFO - Started bus maintenance task
2025-05-24 22:39:45,800 - position_manager - INFO - Starting position monitor loop
2025-05-24 22:39:45,800 - orchestrator - INFO - Starting account information update task
2025-05-24 22:39:45,800 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 22:39:45,800 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 22:39:45,810 - orchestrator - INFO - Starting health check task
2025-05-24 22:39:45,810 - orchestrator - INFO - Starting position monitoring task
2025-05-24 22:39:45,810 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 22:39:45,814 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 22:39:45,816 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 22:46:30,711 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 22:46:30,711 - testnet-launcher - INFO - ==================================================
2025-05-24 22:46:30,711 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T22:46:30.***********-05-24 22:46:30,712 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 22:46:30,712 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 22:46:30,712 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 22:46:30,712 - testnet-launcher - INFO - ==================================================
2025-05-24 22:46:30,712 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 22:46:30,714 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 22:46:30,714 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 22:46:30,714 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 22:46:30,715 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 22:46:30,715 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 22:46:31,781 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 22:46:31,784 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 22:46:31,786 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 22:46:31,787 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 22:46:31,787 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 22:46:32,850 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:46:33,867 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 22:46:33,868 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 22:46:33,869 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 22:46:33,870 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 22:46:33,871 - orchestrator - INFO - Starting orchestrator...
2025-05-24 22:46:35,090 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 22:46:36,279 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 22:46:36,279 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 22:46:36,279 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 22:46:36,279 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 22:46:36,280 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 22:46:36,280 - llm_consumer - INFO - LLM Consumer started
2025-05-24 22:46:36,280 - orchestrator - INFO - LLM Consumer started
2025-05-24 22:46:36,280 - position_manager - INFO - Position monitoring started
2025-05-24 22:46:36,280 - orchestrator - INFO - Position manager started
2025-05-24 22:46:36,281 - orchestrator - INFO - Starting event loop
2025-05-24 22:46:36,281 - orchestrator - INFO - Started bus maintenance task
2025-05-24 22:46:36,281 - position_manager - INFO - Starting position monitor loop
2025-05-24 22:46:36,281 - orchestrator - INFO - Starting account information update task
2025-05-24 22:46:36,281 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 22:46:36,281 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 22:46:36,288 - orchestrator - INFO - Starting health check task
2025-05-24 22:46:36,289 - orchestrator - INFO - Starting position monitoring task
2025-05-24 22:46:36,289 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 22:46:36,291 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 22:46:36,293 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 23:02:26,534 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 23:02:26,534 - testnet-launcher - INFO - ==================================================
2025-05-24 23:02:26,534 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T23:02:26.***********-05-24 23:02:26,534 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 23:02:26,534 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 23:02:26,535 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 23:02:26,535 - testnet-launcher - INFO - ==================================================
2025-05-24 23:02:26,535 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 23:02:26,537 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 23:02:26,537 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 23:02:26,537 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 23:02:26,537 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 23:02:26,537 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 23:02:27,654 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 23:02:27,657 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 23:02:27,658 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 23:02:27,660 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 23:02:27,660 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 23:02:28,746 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 23:02:29,764 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 23:02:29,767 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 23:02:29,767 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 23:02:29,768 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 23:02:29,768 - orchestrator - INFO - Starting orchestrator...
2025-05-24 23:02:30,980 - orchestrator - INFO - HTX WebSocket connected with 3 market data subscriptions
2025-05-24 23:02:32,162 - orchestrator - INFO - Loaded 60 historical funding rates for BTC-USDT
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to signals.fused
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to features.volatility
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to features.funding
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to features.open_interest
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to features.vwap
2025-05-24 23:02:32,163 - pipeline.databus - INFO - Added subscription to features.sentiment
2025-05-24 23:02:32,164 - pipeline.databus - INFO - Added subscription to account.state
2025-05-24 23:02:32,164 - pipeline.databus - INFO - Added subscription to positions.state
2025-05-24 23:02:32,164 - pipeline.databus - INFO - Added subscription to orders.state
2025-05-24 23:02:32,164 - pipeline.databus - INFO - Added subscription to trades.executed
2025-05-24 23:02:32,164 - llm_consumer - INFO - LLM Consumer started
2025-05-24 23:02:32,164 - orchestrator - INFO - LLM Consumer started
2025-05-24 23:02:32,164 - position_manager - INFO - Position monitoring started
2025-05-24 23:02:32,164 - orchestrator - INFO - Position manager started
2025-05-24 23:02:32,165 - orchestrator - INFO - Starting event loop
2025-05-24 23:02:32,165 - orchestrator - INFO - Started bus maintenance task
2025-05-24 23:02:32,165 - position_manager - INFO - Starting position monitor loop
2025-05-24 23:02:32,165 - orchestrator - INFO - Starting account information update task
2025-05-24 23:02:32,165 - feeds.htx_futures - INFO - Using simulated balance: $100.00 USDT
2025-05-24 23:02:32,165 - orchestrator - INFO - Account balance: 100.00 USDT, Available: 100.00 USDT
2025-05-24 23:02:32,172 - orchestrator - INFO - Starting health check task
2025-05-24 23:02:32,172 - orchestrator - INFO - Starting position monitoring task
2025-05-24 23:02:32,173 - orchestrator - INFO - Starting funding rate fetching task
2025-05-24 23:02:32,175 - orchestrator - INFO - Starting open interest fetching task
2025-05-24 23:02:32,177 - orchestrator - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
2025-05-24 23:18:35,605 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-24 23:18:35,605 - testnet-launcher - INFO - ==================================================
2025-05-24 23:18:35,605 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-24T23:18:35.***********-05-24 23:18:35,605 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-24 23:18:35,605 - testnet-launcher - INFO - Trading enabled: True
2025-05-24 23:18:35,605 - testnet-launcher - INFO - Simulation mode: True
2025-05-24 23:18:35,605 - testnet-launcher - INFO - ==================================================
2025-05-24 23:18:35,605 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-24 23:18:35,607 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-24 23:18:35,607 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-24 23:18:35,607 - orchestrator - INFO - Set HTX client simulation mode: True
2025-05-24 23:18:35,607 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-24 23:18:35,608 - orchestrator - INFO - Set publisher for HTX client
2025-05-24 23:18:36,684 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-24 23:18:36,686 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-24 23:18:36,687 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-24 23:18:36,689 - llm.llama_bridge - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-24 23:18:36,689 - llm.llama_bridge - INFO - Loading LLM from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-24 23:18:37,706 - llm.llama_bridge - INFO - Loaded Phi-3 model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 23:18:38,679 - llm_consumer - INFO - LLM initialized with model: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-05-24 23:18:38,681 - llm_consumer - INFO - LLM Consumer initialized
2025-05-24 23:18:38,681 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-24 23:18:38,682 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-24 23:18:38,682 - orchestrator - INFO - Starting orchestrator...
2025-05-24 23:18:39,900 - feeds.htx_futures - ERROR - Failed to connect to market WebSocket: 404, message='Invalid response status', url='wss://api.htx.com/linear-swap-ws'
2025-05-24 23:18:39,900 - orchestrator - INFO - Stopping orchestrator...
2025-05-24 23:18:39,900 - position_manager - WARNING - Position monitoring not running
2025-05-24 23:18:39,902 - orchestrator - INFO - Position manager stopped
2025-05-24 23:18:39,902 - llm_consumer - INFO - LLM Consumer stopped
2025-05-24 23:18:39,902 - orchestrator - INFO - LLM Consumer stopped
2025-05-24 23:18:39,902 - feeds.htx_futures - INFO - Closing HTX Futures client connections
2025-05-24 23:18:39,902 - feeds.htx_futures - INFO - HTX Futures client connections closed
2025-05-24 23:18:39,902 - pipeline.databus - INFO - Shutting down SQLiteBus...
2025-05-24 23:18:40,178 - pipeline.databus - INFO - SQLiteBus shut down
2025-05-24 23:18:40,178 - orchestrator - INFO - Message bus closed
2025-05-24 23:18:40,178 - orchestrator - INFO - Orchestrator stopped
2025-05-24 23:18:40,178 - testnet-launcher - ERROR - Error running orchestrator: 404, message='Invalid response status', url='wss://api.htx.com/linear-swap-ws'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\smarty\run_testnet.py", line 121, in main
    await run_orchestrator(config)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 1865, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 255, in start
    await self.htx_client.connect()
  File "C:\Users\<USER>\Documents\dev\smarty\feeds\htx_futures.py", line 137, in connect
    await market_task
  File "C:\Users\<USER>\Documents\dev\smarty\feeds\htx_futures.py", line 150, in _connect_market_ws
    self.market_ws = await self.session.ws_connect(self.WS_MARKET_URL)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\client.py", line 954, in _ws_connect
    raise WSServerHandshakeError(
aiohttp.client_exceptions.WSServerHandshakeError: 404, message='Invalid response status', url='wss://api.htx.com/linear-swap-ws'
2025-05-24 23:18:40,180 - testnet-launcher - INFO - Smart-trader system shutdown complete
2025-05-25 14:01:02,835 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-25 14:01:02,835 - testnet-launcher - INFO - ==================================================
2025-05-25 14:01:02,835 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-25T14:01:02.835440
2025-05-25 14:01:02,835 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-25 14:01:02,835 - testnet-launcher - INFO - Trading enabled: False
2025-05-25 14:01:02,835 - testnet-launcher - INFO - Simulation mode: False
2025-05-25 14:01:02,835 - testnet-launcher - INFO - ==================================================
2025-05-25 14:01:02,835 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-25 14:01:02,836 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-25 14:01:02,836 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-25 14:01:02,836 - feeds.htx.client - INFO - HTX Futures Client v2.0.0 initialized
2025-05-25 14:01:02,836 - feeds.htx_futures - INFO - HTX Futures Client (backward-compatible wrapper) initialized
2025-05-25 14:01:02,837 - orchestrator - INFO - Set HTX client simulation mode: False
2025-05-25 14:01:02,837 - feeds.htx.client - INFO - Message bus publisher set for HTX Futures client
2025-05-25 14:01:02,837 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-25 14:01:02,837 - orchestrator - INFO - Set publisher for HTX client
2025-05-25 14:01:02,839 - models.garch_volatility - WARNING - arch package not available, falling back to statsmodels
2025-05-25 14:01:02,839 - models.garch_volatility - WARNING - Neither arch nor statsmodels available, using simple volatility estimation
2025-05-25 14:01:02,841 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-25 14:01:02,841 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-25 14:01:02,843 - llm.enhanced_llm_consumer - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-25 14:01:02,843 - llm.enhanced_llm_manager - INFO - Loading LLM model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-25 14:01:05,447 - llm.enhanced_llm_manager - INFO - LLM model loaded and tested successfully
2025-05-25 14:01:05,447 - llm.enhanced_llm_manager - INFO - Enhanced LLM Manager initialized (dummy_mode=False)
2025-05-25 14:01:05,447 - llm.enhanced_llm_consumer - INFO - Enhanced LLM Consumer initialized
2025-05-25 14:01:05,447 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-25 14:01:05,448 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-25 14:01:05,448 - orchestrator - INFO - Starting orchestrator...
2025-05-25 14:01:05,448 - feeds.htx.client - INFO - Connecting to HTX Futures API...
2025-05-25 14:01:06,666 - feeds.htx.websocket - ERROR - Failed to connect to market WebSocket: 404, message='Invalid response status', url='wss://api.htx.com/linear-swap-ws'
2025-05-26 18:27:59,312 - testnet-launcher - INFO - File logging configured: logs/smart_trader.log
2025-05-26 18:27:59,313 - testnet-launcher - INFO - ==================================================
2025-05-26 18:27:59,314 - testnet-launcher - INFO - Starting smart-trader system on testnet at 2025-05-26T18:27:59.313374
2025-05-26 18:27:59,314 - testnet-launcher - INFO - Trading symbols: ['BTC-USDT']
2025-05-26 18:27:59,314 - testnet-launcher - INFO - Trading enabled: True
2025-05-26 18:27:59,314 - testnet-launcher - INFO - Simulation mode: False
2025-05-26 18:27:59,314 - testnet-launcher - INFO - ==================================================
2025-05-26 18:27:59,314 - pipeline.databus - INFO - Creating SQLiteBus with path=data/bus.db, poll_interval=0.5
2025-05-26 18:27:59,316 - pipeline.databus - INFO - SQLiteBus initialized with database at data/bus.db
2025-05-26 18:27:59,316 - orchestrator - INFO - Initialized message bus: SQLiteBus
2025-05-26 18:27:59,316 - feeds.htx.client - INFO - HTX Futures Client v2.0.0 initialized
2025-05-26 18:27:59,316 - feeds.htx_futures - INFO - HTX Futures Client (backward-compatible wrapper) initialized
2025-05-26 18:27:59,316 - orchestrator - INFO - Set HTX client simulation mode: False
2025-05-26 18:27:59,316 - feeds.htx.client - INFO - Message bus publisher set for HTX Futures client
2025-05-26 18:27:59,316 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-26 18:27:59,316 - orchestrator - INFO - Set publisher for HTX client
2025-05-26 18:28:00,251 - models.garch_volatility - INFO - Using arch package for GARCH modeling
2025-05-26 18:28:00,253 - orchestrator - WARNING - SignalStar client not initialized, social sentiment model disabled
2025-05-26 18:28:00,254 - models.meta_ensemble - INFO - Meta-Ensemble model initialized with 9 base models
2025-05-26 18:28:00,255 - llm.enhanced_llm_consumer - INFO - Loaded prompt template from llm/prompts/trading_prompt_phi.yaml
2025-05-26 18:28:00,255 - llm.enhanced_llm_manager - INFO - Loading LLM model from C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf...
2025-05-26 18:28:04,367 - llm.enhanced_llm_manager - INFO - LLM model loaded and tested successfully
2025-05-26 18:28:04,367 - llm.enhanced_llm_manager - INFO - Enhanced LLM Manager initialized (dummy_mode=False)
2025-05-26 18:28:04,368 - llm.enhanced_llm_consumer - INFO - Enhanced LLM Consumer initialized
2025-05-26 18:28:04,368 - executors.htx_executor - INFO - Initialized simulation balance: $100.00 USDT
2025-05-26 18:28:04,369 - position_manager - INFO - Position manager initialized with stop-loss: 3%, take-profit: 1%, trailing-stop: True
2025-05-26 18:28:04,370 - orchestrator - INFO - Starting orchestrator...
2025-05-26 18:28:04,370 - feeds.htx.client - INFO - Connecting to HTX Futures API...
2025-05-26 18:28:04,524 - feeds.htx.websocket - ERROR - Failed to connect to market WebSocket: Cannot connect to host api-usdt.linear.contract.huobi.pro:443 ssl:default [DNS server returned answer with no data]
