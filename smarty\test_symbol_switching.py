#!/usr/bin/env python3
"""
Test Symbol Switching Functionality
Verify that the LiveDataReader correctly filters data by symbol.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from live_dashboard import LiveDataReader
import j<PERSON>

def test_symbol_switching():
    """Test symbol switching functionality."""
    print("🧪 TESTING SYMBOL SWITCHING FUNCTIONALITY")
    print("=" * 60)
    
    # Initialize data reader
    data_reader = LiveDataReader()
    
    # Test symbols
    test_symbols = ["BTC-USDT", "ETH-USDT"]
    
    for symbol in test_symbols:
        print(f"\n📊 TESTING {symbol}")
        print("-" * 30)
        
        # Test market data
        market_data = data_reader.get_latest_market_data(symbol)
        print(f"Market Data:")
        print(f"  Symbol: {market_data.get('symbol', 'N/A')}")
        print(f"  Price: {market_data.get('price', 'N/A')}")
        print(f"  Is Live: {market_data.get('is_live', False)}")
        print(f"  Data Age: {market_data.get('data_age_seconds', 999):.1f}s")
        
        # Test order book
        orderbook = data_reader.get_order_book(symbol, limit=3)
        print(f"Order Book:")
        print(f"  Bids: {len(orderbook.get('bids', []))} levels")
        print(f"  Asks: {len(orderbook.get('asks', []))} levels")
        if orderbook.get('bids'):
            print(f"  Best Bid: {orderbook['bids'][0].get('price', 'N/A')}")
        if orderbook.get('asks'):
            print(f"  Best Ask: {orderbook['asks'][0].get('price', 'N/A')}")
        
        # Test recent trades
        trades = data_reader.get_recent_trades(symbol, limit=3)
        print(f"Recent Trades:")
        print(f"  Count: {len(trades)}")
        if trades:
            latest_trade = trades[0]
            print(f"  Latest: {latest_trade.get('price', 'N/A')} @ {latest_trade.get('time', 'N/A')}")
        
        # Test AI analysis
        analysis = data_reader.get_ai_analysis(symbol)
        print(f"AI Analysis:")
        print(f"  RSI: {analysis.get('rsi', 'N/A')}")
        print(f"  Trend: {analysis.get('trend', 'N/A')}")
        
        # Test market sentiment
        sentiment = data_reader.get_market_sentiment(symbol)
        print(f"Market Sentiment:")
        print(f"  Overall: {sentiment.get('overall', 'N/A')}")
        print(f"  Buy Pressure: {sentiment.get('buy_pressure', 'N/A')}")
    
    print("\n" + "=" * 60)
    print("✅ SYMBOL SWITCHING TEST COMPLETE")
    
    # Verify different data for different symbols
    btc_data = data_reader.get_latest_market_data("BTC-USDT")
    eth_data = data_reader.get_latest_market_data("ETH-USDT")
    
    btc_price = btc_data.get('price', '$0')
    eth_price = eth_data.get('price', '$0')
    
    print(f"\n🔍 VERIFICATION:")
    print(f"BTC-USDT Price: {btc_price}")
    print(f"ETH-USDT Price: {eth_price}")
    
    if btc_price != eth_price and btc_price != '$0' and eth_price != '$0':
        print("✅ SUCCESS: Different prices for different symbols - filtering works!")
    elif btc_price == '$0' or eth_price == '$0':
        print("⚠️ WARNING: Some symbols have no data - check data producer")
    else:
        print("❌ ISSUE: Same price for different symbols - filtering may not be working")

if __name__ == "__main__":
    test_symbol_switching()
