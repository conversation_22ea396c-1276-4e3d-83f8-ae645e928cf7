#!/usr/bin/env python3
"""
Header Navigation Functionality Test Suite
Tests all the enhanced header navigation features including:
- User profile dropdown functionality
- Notification bell and panel
- Mobile menu toggle
- JavaScript initialization
- API endpoints
"""

import requests
import json
import time
from datetime import datetime

class HeaderNavigationTester:
    def __init__(self, base_url="http://localhost:8086"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def run_comprehensive_test(self):
        """Run comprehensive test of header navigation functionality."""
        print("🧪 HEADER NAVIGATION FUNCTIONALITY TEST SUITE")
        print("=" * 60)
        
        # Test 1: Platform Health Check
        self.test_platform_health()
        
        # Test 2: Authentication Flow
        self.test_authentication_flow()
        
        # Test 3: Header JavaScript Loading
        self.test_header_javascript_loading()
        
        # Test 4: Notification API Endpoints
        self.test_notification_api_endpoints()
        
        # Test 5: User Profile API
        self.test_user_profile_api()
        
        # Test 6: Live Trading Page Header
        self.test_live_trading_header()
        
        # Test 7: Header Elements Presence
        self.test_header_elements_presence()
        
        print("\n" + "=" * 60)
        print("✅ HEADER NAVIGATION FUNCTIONALITY TEST COMPLETE")
        
    def test_platform_health(self):
        """Test platform health and availability."""
        print("\n📊 Testing Platform Health...")
        
        try:
            resp = self.session.get(f"{self.base_url}/health", timeout=10)
            if resp.status_code == 200:
                health_data = resp.json()
                print(f"✅ Platform Status: {health_data.get('status', 'unknown')}")
                print(f"✅ Database: {health_data.get('database', 'unknown')}")
                print(f"✅ Market Data: {health_data.get('market_data', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {resp.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
            
    def test_authentication_flow(self):
        """Test authentication and session management."""
        print("\n🔐 Testing Authentication Flow...")
        
        try:
            # Get login page
            resp = self.session.get(f"{self.base_url}/login", timeout=10)
            if resp.status_code != 200:
                print(f"❌ Login page failed: {resp.status_code}")
                return False
                
            # Extract CSRF token from login page
            csrf_token = self._extract_csrf_token(resp.text)
            if not csrf_token:
                print("⚠️ CSRF token not found, proceeding without it")
                
            # Login with epinnox credentials
            login_data = {
                'username': 'epinnox',
                'password': 'securepass123'
            }
            
            if csrf_token:
                login_data['csrf_token'] = csrf_token
            
            resp = self.session.post(f"{self.base_url}/login", data=login_data, timeout=10)
            if resp.status_code in [200, 302]:  # Success or redirect
                print("✅ Authentication successful")
                print("✅ Session established")
                return True
            else:
                print(f"❌ Authentication failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
            
    def test_header_javascript_loading(self):
        """Test header JavaScript file loading."""
        print("\n📜 Testing Header JavaScript Loading...")
        
        try:
            resp = self.session.get(f"{self.base_url}/static/js/header_navigation.js", timeout=10)
            if resp.status_code == 200:
                js_content = resp.text
                
                # Check for key functions
                required_functions = [
                    'toggleNotifications',
                    'toggleUserDropdown',
                    'toggleMobileMenu',
                    'initializeHeaderNavigation',
                    'closeAllDropdowns'
                ]
                
                found_functions = []
                missing_functions = []
                
                for func in required_functions:
                    if f"function {func}" in js_content or f"{func} =" in js_content:
                        found_functions.append(func)
                    else:
                        missing_functions.append(func)
                        
                print(f"✅ JavaScript file loaded: {len(js_content)} characters")
                print(f"✅ Found {len(found_functions)}/5 required functions:")
                for func in found_functions:
                    print(f"   ✓ {func}")
                    
                if missing_functions:
                    print(f"⚠️ Missing functions:")
                    for func in missing_functions:
                        print(f"   ✗ {func}")
                        
                return len(found_functions) >= 4  # Pass if most functions found
            else:
                print(f"❌ JavaScript loading failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ JavaScript test error: {e}")
            return False
            
    def test_notification_api_endpoints(self):
        """Test notification API endpoints."""
        print("\n🔔 Testing Notification API Endpoints...")
        
        try:
            # Test recent notifications endpoint
            resp = self.session.get(f"{self.base_url}/api/notifications/recent", timeout=10)
            if resp.status_code == 200:
                notifications = resp.json()
                print(f"✅ Recent notifications API accessible")
                print(f"✅ Returned {len(notifications)} notifications")
                
                # Check notification structure
                if notifications and isinstance(notifications, list):
                    sample_notification = notifications[0]
                    required_fields = ['id', 'type', 'title', 'message', 'is_read', 'created_at']
                    
                    found_fields = []
                    for field in required_fields:
                        if field in sample_notification:
                            found_fields.append(field)
                            
                    print(f"✅ Notification structure: {len(found_fields)}/6 fields present")
                    
                    # Test mark as read endpoint
                    if notifications:
                        notification_id = notifications[0]['id']
                        resp = self.session.post(
                            f"{self.base_url}/api/notifications/{notification_id}/read",
                            timeout=10
                        )
                        if resp.status_code in [200, 403]:  # 403 = CSRF protection
                            print("✅ Mark as read endpoint accessible")
                        else:
                            print(f"⚠️ Mark as read endpoint status: {resp.status_code}")
                            
                return True
            else:
                print(f"❌ Notifications API failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Notifications API error: {e}")
            return False
            
    def test_user_profile_api(self):
        """Test user profile API."""
        print("\n👤 Testing User Profile API...")
        
        try:
            resp = self.session.get(f"{self.base_url}/api/user/current", timeout=10)
            if resp.status_code == 200:
                user_data = resp.json()
                print("✅ User profile API accessible")
                
                # Check user data structure
                if 'username' in user_data:
                    print(f"✅ Username: {user_data['username']}")
                if 'role' in user_data:
                    print(f"✅ Role: {user_data['role']}")
                if 'user_id' in user_data:
                    print(f"✅ User ID: {user_data['user_id']}")
                    
                return True
            else:
                print(f"❌ User profile API failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ User profile API error: {e}")
            return False
            
    def test_live_trading_header(self):
        """Test header functionality on live trading page."""
        print("\n⚡ Testing Live Trading Page Header...")
        
        try:
            resp = self.session.get(f"{self.base_url}/live-trading", timeout=10)
            if resp.status_code == 200:
                content = resp.text
                
                # Check for header navigation JavaScript inclusion
                js_checks = [
                    'header_navigation.js',
                    'initializeHeader',
                    'initializeHeaderFallback',
                    'toggleNotifications',
                    'toggleUserDropdown'
                ]
                
                found_js_elements = []
                for check in js_checks:
                    if check in content:
                        found_js_elements.append(check)
                        
                print(f"✅ Live trading page loaded")
                print(f"✅ Header JavaScript integration: {len(found_js_elements)}/5 elements found")
                for element in found_js_elements:
                    print(f"   ✓ {element}")
                    
                return len(found_js_elements) >= 3
            else:
                print(f"❌ Live trading page failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Live trading header test error: {e}")
            return False
            
    def test_header_elements_presence(self):
        """Test presence of header elements in HTML."""
        print("\n🎨 Testing Header Elements Presence...")
        
        try:
            resp = self.session.get(f"{self.base_url}/live-trading", timeout=10)
            if resp.status_code == 200:
                content = resp.text
                
                # Check for header elements
                header_elements = {
                    'notification-badge': 'Notification Bell',
                    'user-profile-dropdown': 'User Profile Dropdown',
                    'user-dropdown-menu': 'User Dropdown Menu',
                    'mobile-menu-toggle': 'Mobile Menu Toggle',
                    'mobile-navigation': 'Mobile Navigation',
                    'onclick="toggleNotifications()"': 'Notification Click Handler',
                    'onclick="toggleUserDropdown()"': 'User Dropdown Click Handler',
                    'onclick="toggleMobileMenu()"': 'Mobile Menu Click Handler'
                }
                
                found_elements = []
                missing_elements = []
                
                for element, description in header_elements.items():
                    if element in content:
                        found_elements.append(description)
                    else:
                        missing_elements.append(description)
                        
                print(f"✅ Found {len(found_elements)}/8 header elements:")
                for element in found_elements:
                    print(f"   ✓ {element}")
                    
                if missing_elements:
                    print(f"⚠️ Missing elements:")
                    for element in missing_elements:
                        print(f"   ✗ {element}")
                        
                return len(found_elements) >= 6  # Pass if most elements found
            else:
                print(f"❌ Header elements test failed: {resp.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Header elements test error: {e}")
            return False
            
    def _extract_csrf_token(self, html_content):
        """Extract CSRF token from HTML content."""
        import re
        
        # Look for CSRF token in various formats
        patterns = [
            r'name="csrf_token"[^>]*value="([^"]*)"',
            r'value="([^"]*)"[^>]*name="csrf_token"',
            r'csrf_token["\']:\s*["\']([^"\']*)["\']',
            r'_token["\']:\s*["\']([^"\']*)["\']'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content)
            if match:
                return match.group(1)
                
        return None

def main():
    """Run the header navigation functionality test suite."""
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = HeaderNavigationTester()
    tester.run_comprehensive_test()
    
    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
