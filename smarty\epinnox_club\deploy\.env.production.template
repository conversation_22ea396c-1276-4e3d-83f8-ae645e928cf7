# Money Circle Production Environment Configuration
# Copy this file to .env.production and configure for your environment

# Application Environment
FLASK_ENV=production

# Server Configuration
PROD_HOST=0.0.0.0
PROD_PORT=8085

# Security Configuration
SECRET_KEY=your-secret-key-here-generate-with-secrets.token_hex(32)
ENCRYPTION_KEY=your-encryption-key-here

# SSL/HTTPS Configuration
FORCE_HTTPS=True
SSL_CERT_PATH=/etc/ssl/certs/money-circle.crt
SSL_KEY_PATH=/etc/ssl/private/money-circle.key

# Database Configuration
DATABASE_PATH=/opt/money_circle/data/money_circle.db
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30

# Performance Configuration
ENABLE_COMPRESSION=True
COMPRESSION_LEVEL=6

# Security Settings
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=600
SESSION_TIMEOUT=28800

# Rate Limiting
API_RATE_LIMIT=100
WEBSOCKET_MAX_CONNECTIONS=50

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True

# CDN Configuration (optional)
# CDN_URL=https://cdn.yourdomain.com
# STATIC_URL_PREFIX=/static/

# Logging Configuration
LOG_LEVEL=WARNING
LOG_FILE=/opt/money_circle/logs/money_circle.log

# Trading Configuration
DEFAULT_SYMBOL=BTC-USDT
MAX_POSITIONS_PER_USER=10

# Club Configuration
MIN_VOTES_FOR_STRATEGY=3
STRATEGY_VOTE_DURATION=604800

# Email Configuration (optional)
# SMTP_HOST=smtp.yourdomain.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-smtp-password
# SMTP_USE_TLS=True

# Backup Configuration (optional)
# BACKUP_ENABLED=True
# BACKUP_INTERVAL=86400
# BACKUP_RETENTION_DAYS=30
# BACKUP_PATH=/opt/money_circle/backups/

# External Services (optional)
# REDIS_URL=redis://localhost:6379/0
# CELERY_BROKER_URL=redis://localhost:6379/0
