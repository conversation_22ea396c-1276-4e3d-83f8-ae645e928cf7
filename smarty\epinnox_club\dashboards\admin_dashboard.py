#!/usr/bin/env python3
"""
Admin Dashboard Handler
Comprehensive admin panel for Money Circle platform management.
"""

import logging
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from aiohttp import web
from auth.decorators import require_admin, get_current_user, is_admin
from database.models import DatabaseManager

logger = logging.getLogger(__name__)

class AdminDashboard:
    """Admin dashboard handler for platform management."""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    async def dashboard_page(self, request: web.Request) -> web.Response:
        """Render admin dashboard page."""
        try:
            user = get_current_user(request)
            if not is_admin(request):
                return web.Response(
                    status=302,
                    headers={'Location': '/dashboard?error=admin_required'}
                )

            # Get comprehensive admin data
            admin_data = await self._get_admin_dashboard_data()

            # Prepare template context
            context = {
                'user': user,
                'admin_data': admin_data,
                'current_path': request.path,
                'page_title': 'Admin Dashboard'
            }

            # Render template
            with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
                template = f.read()

            # Simple template rendering (replace with proper template engine if needed)
            for key, value in context.items():
                if isinstance(value, (dict, list)):
                    template = template.replace(f'{{{{{key}}}}}', json.dumps(value))
                else:
                    template = template.replace(f'{{{{{key}}}}}', str(value))

            return web.Response(text=template, content_type='text/html')

        except Exception as e:
            logger.error(f"Admin dashboard error: {e}")
            return web.Response(
                text=f"Admin dashboard error: {e}",
                status=500
            )

    async def _get_admin_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive admin dashboard data."""
        conn = None
        try:
            # Use a dedicated connection for admin data
            conn = sqlite3.connect('data/money_circle.db')
            conn.row_factory = sqlite3.Row

            # Platform-wide KPIs
            kpis = self._get_platform_kpis_sync(conn)

            # User management data
            user_stats = self._get_user_statistics_sync(conn)
            recent_users = self._get_recent_users_sync(conn)

            # Strategy oversight data
            strategy_stats = self._get_strategy_statistics_sync(conn)
            pending_strategies = self._get_pending_strategies_sync(conn)

            # System health data
            system_health = self._get_system_health_sync(conn)

            # Recent activity logs
            recent_logs = self._get_recent_activity_logs_sync(conn)

            return {
                'kpis': kpis,
                'user_stats': user_stats,
                'recent_users': recent_users,
                'strategy_stats': strategy_stats,
                'pending_strategies': pending_strategies,
                'system_health': system_health,
                'recent_logs': recent_logs,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting admin data: {e}")
            # Return default data structure to prevent template errors
            return {
                'kpis': {
                    'total_users': 0,
                    'active_users': 0,
                    'total_strategies': 0,
                    'total_trades_30d': 0,
                    'total_volume_30d': 0,
                    'trading_users_30d': 0
                },
                'user_stats': {},
                'recent_users': [],
                'strategy_stats': {},
                'pending_strategies': [],
                'system_health': {'status': 'error', 'message': str(e)},
                'recent_logs': [],
                'last_updated': datetime.now().isoformat()
            }
        finally:
            if conn:
                conn.close()

    def _get_platform_kpis_sync(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Get platform-wide KPIs."""
        try:
            # User statistics
            cursor = conn.execute("""
                SELECT
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
                    COUNT(CASE WHEN role = 'member' THEN 1 END) as member_count,
                    COUNT(CASE WHEN role = 'viewer' THEN 1 END) as viewer_count
                FROM users
            """)
            user_stats = cursor.fetchone()

            # Strategy statistics (with table existence check)
            strategy_stats = (0, 0, 0, 0)
            try:
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total_strategies,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_strategies,
                        COUNT(CASE WHEN status = 'pending_review' THEN 1 END) as pending_strategies,
                        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_strategies
                    FROM strategy_proposals
                """)
                strategy_stats = cursor.fetchone()
            except sqlite3.OperationalError:
                logger.warning("strategy_proposals table not found, using default values")

            # Trading statistics (with table existence check)
            trading_stats = (0, 0, 0)
            try:
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total_trades,
                        COALESCE(SUM(size * price), 0) as total_volume,
                        COUNT(DISTINCT user_id) as trading_users
                    FROM user_trades
                    WHERE timestamp >= date('now', '-30 days')
                """)
                trading_stats = cursor.fetchone()
            except sqlite3.OperationalError:
                logger.warning("user_trades table not found, using default values")

            return {
                'total_users': user_stats[0] if user_stats else 0,
                'active_users': user_stats[1] if user_stats else 0,
                'admin_count': user_stats[2] if user_stats else 0,
                'member_count': user_stats[3] if user_stats else 0,
                'viewer_count': user_stats[4] if user_stats else 0,
                'total_strategies': strategy_stats[0] if strategy_stats else 0,
                'approved_strategies': strategy_stats[1] if strategy_stats else 0,
                'pending_strategies': strategy_stats[2] if strategy_stats else 0,
                'active_strategies': strategy_stats[3] if strategy_stats else 0,
                'total_trades_30d': trading_stats[0] if trading_stats else 0,
                'total_volume_30d': trading_stats[1] if trading_stats else 0,
                'trading_users_30d': trading_stats[2] if trading_stats else 0
            }

        except Exception as e:
            logger.error(f"Error getting platform KPIs: {e}")
            return {
                'total_users': 0,
                'active_users': 0,
                'admin_count': 0,
                'member_count': 0,
                'viewer_count': 0,
                'total_strategies': 0,
                'approved_strategies': 0,
                'pending_strategies': 0,
                'active_strategies': 0,
                'total_trades_30d': 0,
                'total_volume_30d': 0,
                'trading_users_30d': 0
            }

    def _get_user_statistics_sync(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Get user statistics."""
        try:
            # User growth over time
            cursor = conn.execute("""
                SELECT
                    DATE(date_joined) as join_date,
                    COUNT(*) as new_users
                FROM users
                WHERE date_joined >= date('now', '-30 days')
                GROUP BY DATE(date_joined)
                ORDER BY join_date
            """)
            user_growth = [{'date': row[0], 'count': row[1]} for row in cursor.fetchall()]

            # Role distribution
            cursor = conn.execute("""
                SELECT role, COUNT(*) as count
                FROM users
                GROUP BY role
            """)
            role_distribution = [{'role': row[0], 'count': row[1]} for row in cursor.fetchall()]

            return {
                'user_growth': user_growth,
                'role_distribution': role_distribution
            }

        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}

    def _get_recent_users_sync(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get recent users."""
        try:
            cursor = conn.execute("""
                SELECT id, username, email, role, date_joined, is_active
                FROM users
                ORDER BY date_joined DESC
                LIMIT 10
            """)

            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'role': row[3],
                    'date_joined': row[4],
                    'is_active': bool(row[5])
                })

            return users

        except Exception as e:
            logger.error(f"Error getting recent users: {e}")
            return []

    def _get_strategy_statistics_sync(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Get strategy statistics."""
        try:
            # Strategy status distribution (with table existence check)
            status_distribution = []
            try:
                cursor = conn.execute("""
                    SELECT status, COUNT(*) as count
                    FROM strategy_proposals
                    GROUP BY status
                """)
                status_distribution = [{'status': row[0], 'count': row[1]} for row in cursor.fetchall()]
            except sqlite3.OperationalError:
                logger.warning("strategy_proposals table not found")

            # Strategy performance metrics (with table existence check)
            performance_stats = (0, 0, 0)
            try:
                cursor = conn.execute("""
                    SELECT
                        AVG(expected_return) as avg_expected_return,
                        AVG(max_drawdown) as avg_max_drawdown,
                        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count
                    FROM strategy_proposals
                    WHERE status = 'approved'
                """)
                performance_stats = cursor.fetchone()
            except sqlite3.OperationalError:
                logger.warning("strategy_proposals table not found")

            return {
                'status_distribution': status_distribution,
                'avg_expected_return': performance_stats[0] if performance_stats else 0,
                'avg_max_drawdown': performance_stats[1] if performance_stats else 0,
                'active_count': performance_stats[2] if performance_stats else 0
            }

        except Exception as e:
            logger.error(f"Error getting strategy statistics: {e}")
            return {}

    def _get_pending_strategies_sync(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get pending strategy proposals."""
        try:
            # Check if strategy_proposals table exists
            cursor = conn.execute("""
                SELECT
                    sp.id, sp.title, sp.description, sp.risk_level,
                    sp.expected_return, sp.created_at,
                    u.username as proposer
                FROM strategy_proposals sp
                JOIN users u ON sp.proposed_by = u.id
                WHERE sp.status = 'pending_review'
                ORDER BY sp.created_at ASC
            """)

            strategies = []
            for row in cursor.fetchall():
                strategies.append({
                    'id': row[0],
                    'title': row[1],
                    'description': row[2],
                    'risk_level': row[3],
                    'expected_return': row[4],
                    'created_at': row[5],
                    'proposer': row[6]
                })

            return strategies

        except sqlite3.OperationalError:
            logger.warning("strategy_proposals table not found")
            return []
        except Exception as e:
            logger.error(f"Error getting pending strategies: {e}")
            return []

    def _get_system_health_sync(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Get system health metrics."""
        try:
            # Database health
            cursor = conn.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]

            # Active sessions (with table existence check)
            active_sessions = 0
            try:
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM user_sessions
                    WHERE is_active = 1 AND expires_at > datetime('now')
                """)
                active_sessions = cursor.fetchone()[0]
            except sqlite3.OperationalError:
                logger.warning("user_sessions table not found")

            # Recent errors (would need error logging table)
            error_count = 0  # Placeholder

            return {
                'database_tables': table_count,
                'active_sessions': active_sessions,
                'recent_errors': error_count,
                'status': 'healthy' if table_count > 0 else 'warning'
            }

        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {'status': 'error', 'message': str(e)}

    def _get_recent_activity_logs_sync(self, conn: sqlite3.Connection) -> List[Dict[str, Any]]:
        """Get recent activity logs."""
        try:
            # This would typically come from an audit log table
            # For now, we'll use recent user activities as a proxy
            logs = []
            try:
                cursor = conn.execute("""
                    SELECT
                        'user_login' as action_type,
                        u.username,
                        us.created_at as timestamp,
                        us.ip_address
                    FROM user_sessions us
                    JOIN users u ON us.user_id = u.id
                    WHERE us.created_at >= datetime('now', '-24 hours')
                    ORDER BY us.created_at DESC
                    LIMIT 20
                """)

                for row in cursor.fetchall():
                    logs.append({
                        'action_type': row[0],
                        'username': row[1],
                        'timestamp': row[2],
                        'details': f"Login from {row[3] or 'unknown IP'}"
                    })
            except sqlite3.OperationalError:
                logger.warning("user_sessions table not found")
                # Create some sample activity logs
                logs = [
                    {
                        'action_type': 'system_start',
                        'username': 'system',
                        'timestamp': datetime.now().isoformat(),
                        'details': 'Money Circle platform started'
                    }
                ]

            return logs

        except Exception as e:
            logger.error(f"Error getting activity logs: {e}")
            return []
