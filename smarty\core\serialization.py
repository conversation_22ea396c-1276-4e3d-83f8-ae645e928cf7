"""
Serialization utilities for the smart-trader system.
"""

import json
from datetime import datetime
from typing import Dict, Any, Union, List

from core.events import Signal, Order, Position, Kline, Trade, OrderbookDelta


def json_serialize(obj: Any) -> Any:
    """
    Custom JSON serializer for objects not serializable by default json code.
    
    Args:
        obj: Object to serialize
        
    Returns:
        JSON serializable object
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    
    # Handle enum types
    if hasattr(obj, 'value'):
        return obj.value
    
    # Default behavior
    raise TypeError(f"Type {type(obj)} not serializable")


def signal_to_dict(signal: Signal) -> Dict[str, Any]:
    """
    Convert a Signal object to a dictionary with JSON-serializable values.
    
    Args:
        signal: Signal object
        
    Returns:
        Dictionary representation of the signal
    """
    signal_dict = signal.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(signal_dict.get('timestamp'), datetime):
        signal_dict['timestamp'] = signal_dict['timestamp'].isoformat()
    
    # Convert action enum to string
    if hasattr(signal_dict.get('action'), 'value'):
        signal_dict['action'] = signal_dict['action'].value
    
    return signal_dict


def order_to_dict(order: Order) -> Dict[str, Any]:
    """
    Convert an Order object to a dictionary with JSON-serializable values.
    
    Args:
        order: Order object
        
    Returns:
        Dictionary representation of the order
    """
    order_dict = order.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(order_dict.get('timestamp'), datetime):
        order_dict['timestamp'] = order_dict['timestamp'].isoformat()
    
    # Convert side enum to string
    if hasattr(order_dict.get('side'), 'value'):
        order_dict['side'] = order_dict['side'].value
    
    # Convert order_type enum to string
    if hasattr(order_dict.get('order_type'), 'value'):
        order_dict['order_type'] = order_dict['order_type'].value
    
    return order_dict


def position_to_dict(position: Position) -> Dict[str, Any]:
    """
    Convert a Position object to a dictionary with JSON-serializable values.
    
    Args:
        position: Position object
        
    Returns:
        Dictionary representation of the position
    """
    position_dict = position.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(position_dict.get('timestamp'), datetime):
        position_dict['timestamp'] = position_dict['timestamp'].isoformat()
    
    # Convert side enum to string
    if hasattr(position_dict.get('side'), 'value'):
        position_dict['side'] = position_dict['side'].value
    
    return position_dict


def kline_to_dict(kline: Kline) -> Dict[str, Any]:
    """
    Convert a Kline object to a dictionary with JSON-serializable values.
    
    Args:
        kline: Kline object
        
    Returns:
        Dictionary representation of the kline
    """
    kline_dict = kline.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(kline_dict.get('timestamp'), datetime):
        kline_dict['timestamp'] = kline_dict['timestamp'].isoformat()
    
    return kline_dict


def trade_to_dict(trade: Trade) -> Dict[str, Any]:
    """
    Convert a Trade object to a dictionary with JSON-serializable values.
    
    Args:
        trade: Trade object
        
    Returns:
        Dictionary representation of the trade
    """
    trade_dict = trade.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(trade_dict.get('timestamp'), datetime):
        trade_dict['timestamp'] = trade_dict['timestamp'].isoformat()
    
    # Convert side enum to string if present
    if hasattr(trade_dict.get('side'), 'value'):
        trade_dict['side'] = trade_dict['side'].value
    
    return trade_dict


def orderbook_to_dict(orderbook: OrderbookDelta) -> Dict[str, Any]:
    """
    Convert an OrderbookDelta object to a dictionary with JSON-serializable values.
    
    Args:
        orderbook: OrderbookDelta object
        
    Returns:
        Dictionary representation of the orderbook
    """
    orderbook_dict = orderbook.__dict__.copy()
    
    # Convert datetime to ISO format
    if isinstance(orderbook_dict.get('timestamp'), datetime):
        orderbook_dict['timestamp'] = orderbook_dict['timestamp'].isoformat()
    
    return orderbook_dict
