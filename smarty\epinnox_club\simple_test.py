#!/usr/bin/env python3
"""
Simple test for Money Circle login functionality
"""

import requests
import sys

def test_login():
    """Test the login functionality using requests."""
    print("[TEST] Testing Money Circle login functionality...")

    # Test data
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }

    try:
        # Test login endpoint
        print(f"[TEST] Attempting login with username: {login_data['username']}")

        response = requests.post(
            'http://localhost:8085/login',
            data=login_data,
            allow_redirects=False,
            timeout=10
        )

        print(f"[RESPONSE] Status: {response.status_code}")
        print(f"[RESPONSE] Headers: {dict(response.headers)}")

        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"[SUCCESS] Login successful! Redirecting to: {location}")
            return True
        elif response.status_code == 200:
            if 'Invalid username or password' in response.text:
                print("[ERROR] Invalid credentials")
            else:
                print("[INFO] Login page returned (may need to check form)")
            return False
        else:
            print(f"[ERROR] Unexpected response: {response.status_code}")
            print(f"[ERROR] Response text: {response.text[:200]}...")
            return False

    except Exception as e:
        print(f"[ERROR] Login test failed: {e}")
        return False

def test_user_exists():
    """Test if the epinnox user exists in the database."""
    print("\n[TEST] Checking if epinnox user exists...")

    try:
        from database.models import DatabaseManager
        from auth.user_manager import UserManager

        # Initialize database and user manager
        db = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db)

        # Check if user exists
        user = user_manager.get_user_by_username('epinnox')
        if user:
            print(f"[OK] User found: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Agreement accepted: {user.agreement_accepted}")
            return True
        else:
            print("[ERROR] User 'epinnox' not found!")
            return False

    except Exception as e:
        print(f"[ERROR] Database check failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Money Circle login tests...\n")

    # Test if user exists
    user_exists = test_user_exists()

    if user_exists:
        # Test login functionality
        login_success = test_login()

        if login_success:
            print("\n✅ All tests passed! Login is working.")
        else:
            print("\n❌ Login test failed.")
    else:
        print("\n❌ User does not exist. Cannot test login.")

if __name__ == '__main__':
    main()
