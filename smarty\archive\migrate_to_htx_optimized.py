#!/usr/bin/env python
"""
Migration script to migrate to HTX-optimized SQLiteBus configuration.

This script:
1. Creates a backup of the current configuration
2. Creates a new HTX-optimized configuration
3. Migrates data from the current database to the new database
4. Updates the configuration to use the HTX-optimized settings
"""

import argparse
import json
import logging
import os
import shutil
import sys
import time
import yaml
from datetime import datetime
from typing import Dict, Any

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from optimized_databus import OptimizedSQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Configuration dictionary
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}


def save_config(config: Dict[str, Any], config_path: str) -> bool:
    """
    Save configuration to YAML file.

    Args:
        config: Configuration dictionary
        config_path: Path to the configuration file

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create backup
        backup_path = f"{config_path}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        if os.path.exists(config_path):
            shutil.copy2(config_path, backup_path)
            logger.info(f"Created backup of configuration at {backup_path}")

        # Save new config
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)

        return True
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")
        return False


def create_htx_optimized_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create HTX-optimized configuration.

    Args:
        config: Current configuration dictionary

    Returns:
        HTX-optimized configuration dictionary
    """
    # Make a copy of the config
    new_config = config.copy()

    # Set up pipeline settings
    if "pipeline" not in new_config:
        new_config["pipeline"] = {}

    # Set bus type to optimized_sqlite
    new_config["pipeline"]["bus"] = "optimized_sqlite"

    # Set up optimized SQLite settings
    new_config["pipeline"]["optimized_sqlite"] = {
        # Database settings
        "path": "data/htx_optimized_bus.db",

        # Performance settings
        "poll_interval": 0.05,  # 50ms polling for low latency
        "batch_size": 100,      # Default batch size
        "batch_timeout": 0.5,   # Default timeout

        # SQLite optimizations
        "cache_size": 20000,    # Larger cache for better performance
        "mmap_size": 100000000, # 100MB memory map for better read performance
        "busy_timeout": 5000,   # 5 seconds busy timeout
        "synchronous": "NORMAL", # Balance between performance and durability

        # Stream-specific settings
        "stream_config": {
            # Market data streams (high frequency, small messages)
            "htx.trade": {
                "batch_size": 200,
                "batch_timeout": 0.2,
                "compression": False
            },
            "htx.kline": {
                "batch_size": 100,
                "batch_timeout": 0.3,
                "compression": False
            },

            # Orderbook streams (medium frequency, larger messages)
            "htx.orderbook": {
                "batch_size": 50,
                "batch_timeout": 0.5,
                "compression": True,
                "compression_level": 6,
                "compression_threshold": 512
            },

            # Model output streams (low frequency, small messages)
            "model.*": {
                "batch_size": 20,
                "batch_timeout": 1.0,
                "compression": False
            },

            # LLM output streams (very low frequency, larger messages)
            "llm.*": {
                "batch_size": 10,
                "batch_timeout": 2.0,
                "compression": True,
                "compression_level": 9,
                "compression_threshold": 256
            }
        },

        # Priority streams (processed with higher priority)
        "priority_streams": [
            "htx.trade",
            "htx.orderbook",
            "model.volatility_regime",
            "model.liquidity_imbalance"
        ],

        # Compression settings (global defaults)
        "compression": False,
        "compression_level": 6,
        "compression_threshold": 1024
    }

    # Maintenance settings
    new_config["pipeline"]["cleanup_interval_hours"] = 6
    new_config["pipeline"]["message_retention_days"] = 3

    return new_config


def migrate_data(source_path: str, target_path: str, batch_size: int = 100) -> int:
    """
    Migrate data from source database to target database.

    Args:
        source_path: Path to the source SQLite database
        target_path: Path to the target SQLite database
        batch_size: Number of messages to migrate in each batch

    Returns:
        Number of messages migrated
    """
    if not os.path.exists(source_path):
        logger.error(f"Source database not found: {source_path}")
        return 0

    # Create target directory if it doesn't exist
    os.makedirs(os.path.dirname(target_path), exist_ok=True)

    # Create source bus
    source_bus = OptimizedSQLiteBus(path=source_path)

    # Create target bus with HTX-optimized settings
    target_bus = OptimizedSQLiteBus(
        path=target_path,
        poll_interval=0.05,
        batch_size=100,
        batch_timeout=0.5,
        cache_size=20000,
        stream_config={
            "htx.trade": {
                "batch_size": 200,
                "batch_timeout": 0.2,
                "compression": False
            },
            "htx.orderbook": {
                "batch_size": 50,
                "batch_timeout": 0.5,
                "compression": True,
                "compression_level": 6,
                "compression_threshold": 512
            }
        },
        priority_streams=[
            "htx.trade",
            "htx.orderbook",
            "model.volatility_regime",
            "model.liquidity_imbalance"
        ]
    )

    # Get source database statistics
    source_stats = source_bus.get_database_stats()
    logger.info(f"Source database has {source_stats['total_messages']} messages")

    # Define a callback to migrate messages
    migrated_count = 0

    def migrate_callback(stream: str, ts: float, payload: dict) -> None:
        nonlocal migrated_count
        target_bus.publish(stream, ts, payload)
        migrated_count += 1

        if migrated_count % 1000 == 0:
            logger.info(f"Migrated {migrated_count} messages")

    # Subscribe to all streams
    for stream in source_stats['stream_counts'].keys():
        source_bus.subscribe(stream, lambda ts, payload, s=stream: migrate_callback(s, ts, payload))

    # Wait for migration to complete
    logger.info("Waiting for migration to complete...")
    start_time = time.time()
    timeout = 300  # 5 minutes

    while migrated_count < source_stats['total_messages']:
        if time.time() - start_time > timeout:
            logger.warning(f"Migration timed out after {timeout} seconds")
            break
        time.sleep(1)

    # Close buses
    source_bus.close()
    target_bus.close()

    logger.info(f"Migration complete: {migrated_count} messages migrated")
    return migrated_count


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Migrate to HTX-optimized SQLiteBus configuration")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--migrate-data", action="store_true", help="Migrate data from current database to new database")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually save the configuration")
    parser.add_argument("--output", default="config.yaml", help="Output configuration file path")

    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)
    if not config:
        logger.error(f"Failed to load configuration from {args.config}")
        return

    # Create HTX-optimized configuration
    htx_config = create_htx_optimized_config(config)

    # Print configuration changes
    print("\nHTX-Optimized Configuration Changes:")
    print("-----------------------------------")
    print(f"Bus type: {config.get('pipeline', {}).get('bus', 'sqlite')} -> optimized_sqlite")
    print(f"Poll interval: {config.get('pipeline', {}).get('sqlite', {}).get('poll_interval', 0.5)} -> 0.05")
    print(f"Cache size: {config.get('pipeline', {}).get('optimized_sqlite', {}).get('cache_size', 10000)} -> 20000")
    print(f"Stream-specific configurations: {len(htx_config['pipeline']['optimized_sqlite']['stream_config'])} streams")
    print(f"Cleanup interval: {config.get('pipeline', {}).get('cleanup_interval_hours', 24)} -> 6 hours")
    print(f"Message retention: {config.get('pipeline', {}).get('message_retention_days', 7)} -> 3 days")

    # Migrate data if requested
    if args.migrate_data:
        source_path = config.get("pipeline", {}).get("sqlite", {}).get("path", "data/bus.db")
        target_path = htx_config["pipeline"]["optimized_sqlite"]["path"]

        logger.info(f"Migrating data from {source_path} to {target_path}")
        migrated = migrate_data(source_path, target_path)

        if migrated > 0:
            logger.info(f"Successfully migrated {migrated} messages")
        else:
            logger.warning("No messages were migrated")

    # Save configuration if not dry run
    if not args.dry_run:
        if save_config(htx_config, args.output):
            logger.info(f"Successfully saved HTX-optimized configuration to {args.output}")
        else:
            logger.error(f"Failed to save HTX-optimized configuration to {args.output}")
    else:
        logger.info("Dry run - configuration not saved")

    logger.info("Migration complete")


if __name__ == "__main__":
    main()
