#!/usr/bin/env python3
"""
Test the fixed Money Circle onboarding system with all improvements:
1. CSS styling fixes
2. Welcome page confirmation
3. Duplicate account prevention
4. Complete flow: register → agreement → welcome → dashboard
"""

import requests
import sys
import time
import random
import string

def generate_test_user():
    """Generate unique test user data."""
    suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return {
        'username': f'testuser_{suffix}',
        'email': f'test_{suffix}@example.com',
        'password': 'TestPass123',
        'confirm_password': 'TestPass123'
    }

def test_registration_page_styling():
    """Test that registration page loads with proper CSS styling."""
    print("🎨 Testing registration page CSS styling...")
    
    try:
        response = requests.get('http://localhost:8084/register')
        
        if response.status_code == 200:
            content = response.text
            
            # Check for CSS variables and styling
            if 'var(--primary-color)' in content or '#FFD700' in content:
                print("✅ CSS variables are being used")
            
            if 'Money Circle' in content:
                print("✅ Money Circle branding present")
            
            if 'Step 1: Registration' in content:
                print("✅ Progress indicator working")
            
            # Test CSS file accessibility
            css_response = requests.get('http://localhost:8084/static/css/club.css')
            if css_response.status_code == 200 and '--primary-color' in css_response.text:
                print("✅ CSS file loads correctly with variables")
                return True
            else:
                print("❌ CSS file not loading properly")
                return False
        else:
            print(f"❌ Registration page failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Registration page styling test error: {e}")
        return False

def test_complete_onboarding_flow():
    """Test the complete onboarding flow with welcome page."""
    print("\n🔄 Testing complete onboarding flow...")
    
    # Generate unique test user
    test_user = generate_test_user()
    print(f"📋 Testing with user: {test_user['username']} ({test_user['email']})")
    
    session = requests.Session()
    
    try:
        # Step 1: Register user
        print("1️⃣ Testing registration...")
        register_response = session.post(
            'http://localhost:8084/register',
            data=test_user,
            allow_redirects=False
        )
        
        if register_response.status_code != 302:
            print(f"❌ Registration failed: {register_response.status_code}")
            return False
        
        location = register_response.headers.get('Location', '')
        if '/agreement' not in location:
            print(f"❌ Registration didn't redirect to agreement: {location}")
            return False
        
        print("✅ Registration successful, redirected to agreement")
        
        # Step 2: Access agreement page
        print("2️⃣ Testing agreement page...")
        agreement_response = session.get('http://localhost:8084/agreement')
        
        if agreement_response.status_code != 200:
            print(f"❌ Agreement page failed: {agreement_response.status_code}")
            return False
        
        if 'Money Circle Investment Club' not in agreement_response.text:
            print("❌ Agreement page missing content")
            return False
        
        print("✅ Agreement page accessible")
        
        # Step 3: Accept agreement
        print("3️⃣ Testing agreement acceptance...")
        agreement_data = {
            'agreement_read': 'on',
            'risk_acknowledgment': 'on',
            'age_confirmation': 'on',
            'digital_signature': f'{test_user["username"]} Test User'
        }
        
        accept_response = session.post(
            'http://localhost:8084/agreement',
            data=agreement_data,
            allow_redirects=False
        )
        
        if accept_response.status_code != 302:
            print(f"❌ Agreement acceptance failed: {accept_response.status_code}")
            return False
        
        location = accept_response.headers.get('Location', '')
        if '/welcome' not in location:
            print(f"❌ Agreement didn't redirect to welcome: {location}")
            return False
        
        print("✅ Agreement accepted, redirected to welcome")
        
        # Step 4: Access welcome page
        print("4️⃣ Testing welcome page...")
        welcome_response = session.get('http://localhost:8084/welcome')
        
        if welcome_response.status_code != 200:
            print(f"❌ Welcome page failed: {welcome_response.status_code}")
            return False
        
        welcome_content = welcome_response.text
        if test_user['username'] not in welcome_content:
            print("❌ Welcome page missing personalization")
            return False
        
        if 'Account Successfully Created' not in welcome_content:
            print("❌ Welcome page missing success message")
            return False
        
        if 'Continue to Dashboard' not in welcome_content:
            print("❌ Welcome page missing dashboard button")
            return False
        
        print("✅ Welcome page working correctly")
        
        # Step 5: Access dashboard
        print("5️⃣ Testing dashboard access...")
        dashboard_response = session.get('http://localhost:8084/dashboard')
        
        if dashboard_response.status_code == 200:
            print("✅ Dashboard accessible after onboarding")
            return True
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Onboarding flow error: {e}")
        return False

def test_duplicate_prevention():
    """Test duplicate account prevention."""
    print("\n🚫 Testing duplicate account prevention...")
    
    # Create first user
    test_user = generate_test_user()
    
    try:
        # Register first user
        response1 = requests.post(
            'http://localhost:8084/register',
            data=test_user,
            allow_redirects=False
        )
        
        if response1.status_code != 302:
            print("❌ First registration failed")
            return False
        
        print("✅ First user registered successfully")
        
        # Try to register same username
        duplicate_user = test_user.copy()
        duplicate_user['email'] = '<EMAIL>'
        
        response2 = requests.post(
            'http://localhost:8084/register',
            data=duplicate_user,
            allow_redirects=False
        )
        
        if response2.status_code == 302:
            location = response2.headers.get('Location', '')
            if 'error=server_error' in location:
                print("✅ Duplicate username prevented")
            else:
                print(f"❌ Unexpected redirect for duplicate username: {location}")
                return False
        else:
            print(f"❌ Duplicate username not handled properly: {response2.status_code}")
            return False
        
        # Try to register same email
        duplicate_email = test_user.copy()
        duplicate_email['username'] = 'different_user'
        
        response3 = requests.post(
            'http://localhost:8084/register',
            data=duplicate_email,
            allow_redirects=False
        )
        
        if response3.status_code == 302:
            location = response3.headers.get('Location', '')
            if 'error=server_error' in location:
                print("✅ Duplicate email prevented")
                return True
            else:
                print(f"❌ Unexpected redirect for duplicate email: {location}")
                return False
        else:
            print(f"❌ Duplicate email not handled properly: {response3.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Duplicate prevention test error: {e}")
        return False

def test_welcome_to_dashboard_flow():
    """Test the welcome to dashboard transition."""
    print("\n🎯 Testing welcome to dashboard flow...")
    
    # Create and register a user
    test_user = generate_test_user()
    session = requests.Session()
    
    try:
        # Quick registration and agreement
        session.post('http://localhost:8084/register', data=test_user)
        
        agreement_data = {
            'agreement_read': 'on',
            'risk_acknowledgment': 'on',
            'age_confirmation': 'on',
            'digital_signature': f'{test_user["username"]} Test User'
        }
        session.post('http://localhost:8084/agreement', data=agreement_data)
        
        # Test welcome page
        welcome_response = session.get('http://localhost:8084/welcome')
        if welcome_response.status_code != 200:
            print("❌ Welcome page not accessible")
            return False
        
        # Test dashboard button click (simulate)
        dashboard_response = session.get('http://localhost:8084/dashboard')
        if dashboard_response.status_code == 200:
            print("✅ Dashboard accessible from welcome page")
            return True
        else:
            print(f"❌ Dashboard not accessible: {dashboard_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Welcome to dashboard flow error: {e}")
        return False

def main():
    """Run all onboarding system tests."""
    print("🚀 MONEY CIRCLE ONBOARDING SYSTEM - COMPREHENSIVE TESTING")
    print("=" * 60)
    
    tests = [
        ("Registration Page CSS Styling", test_registration_page_styling),
        ("Complete Onboarding Flow", test_complete_onboarding_flow),
        ("Duplicate Account Prevention", test_duplicate_prevention),
        ("Welcome to Dashboard Flow", test_welcome_to_dashboard_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        if test_func():
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Registration page CSS styling working")
        print("✅ Complete onboarding flow working")
        print("✅ Welcome page confirmation working")
        print("✅ Duplicate account prevention working")
        print("✅ Dashboard integration working")
        print("\n🌐 Money Circle onboarding system is fully operational!")
        return 0
    else:
        print("❌ SOME TESTS FAILED")
        print("❌ Please check the failed tests and fix the issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
