# Money Circle Investment Club Platform - .gitignore
# Exclude sensitive and generated files from version control

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Database Files (SQLite)
*.db
*.sqlite
*.sqlite3
data/
backups/

# Environment Variables & Secrets
.env
.env.local
.env.production
.env.development
config.json
production_config.json

# SSL Certificates & Keys
ssl/
*.pem
*.key
*.crt
*.p12
*.pfx

# Logs
logs/
*.log
log/

# Uploads & User Data
uploads/
user_data/

# IDE & Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
tmp/
temp/
*.tmp
*.temp

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Node.js (if any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup Files
*.backup
*.bak
*.old

# Production Deployment Files (will be created during deployment)
deployment/ssl_certificates/
deployment/production_secrets/

# Local Development Files
local_config.py
test_data/
dev_notes.txt

# API Keys & Secrets (double-check these are not committed)
*api_key*
*secret*
*password*
*token*

# Monitoring & Analytics
monitoring/data/
analytics/reports/

# Documentation Build
docs/_build/
docs/build/

# Jupyter Notebooks (if any)
.ipynb_checkpoints/

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
