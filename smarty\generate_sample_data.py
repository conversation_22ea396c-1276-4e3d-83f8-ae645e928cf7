"""
Generate sample historical data for backtesting.

This script generates synthetic price data for testing the backtester
without requiring an API connection.
"""

import os
import logging
import argparse
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def generate_random_walk(
    start_price: float,
    days: int,
    interval_hours: int = 1,
    volatility: float = 0.01,
    drift: float = 0.0001
) -> pd.DataFrame:
    """
    Generate a random walk price series.
    
    Args:
        start_price: Starting price
        days: Number of days to generate data for
        interval_hours: Interval in hours between data points
        volatility: Daily volatility
        drift: Daily drift (trend)
        
    Returns:
        DataFrame with OHLCV data
    """
    # Calculate number of intervals
    intervals_per_day = 24 // interval_hours
    num_intervals = days * intervals_per_day
    
    # Generate timestamps
    end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_date = end_date - timedelta(days=days)
    
    timestamps = []
    current_date = start_date
    for _ in range(num_intervals):
        timestamps.append(current_date)
        current_date += timedelta(hours=interval_hours)
    
    # Generate log returns with drift and volatility
    interval_volatility = volatility / np.sqrt(intervals_per_day)
    interval_drift = drift / intervals_per_day
    
    log_returns = np.random.normal(
        loc=interval_drift,
        scale=interval_volatility,
        size=num_intervals
    )
    
    # Calculate price series
    log_prices = np.cumsum(log_returns) + np.log(start_price)
    prices = np.exp(log_prices)
    
    # Generate OHLCV data
    data = []
    for i, timestamp in enumerate(timestamps):
        price = prices[i]
        
        # Generate random high/low around close price
        high_pct = np.random.uniform(0, interval_volatility * 2)
        low_pct = np.random.uniform(0, interval_volatility * 2)
        
        high = price * (1 + high_pct)
        low = price * (1 - low_pct)
        
        # Ensure high >= close >= low
        high = max(high, price)
        low = min(low, price)
        
        # Generate random open between high and low
        if i == 0:
            open_price = start_price
        else:
            open_price = prices[i-1]  # Previous close
        
        # Generate random volume
        volume = np.random.lognormal(mean=10, sigma=1)
        
        data.append([
            timestamp,
            open_price,
            high,
            low,
            price,  # close
            volume
        ])
    
    # Create DataFrame
    df = pd.DataFrame(
        data,
        columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
    )
    
    return df


def generate_sample_data(args):
    """
    Generate sample historical data for backtesting.
    
    Args:
        args: Command-line arguments
    """
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Default symbols if none provided
    if not args.symbols:
        args.symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
    
    # Generate data for each symbol
    for symbol in args.symbols:
        # Set starting price based on symbol
        if "BTC" in symbol:
            start_price = 50000.0
            volatility = 0.02
            drift = 0.0002
        elif "ETH" in symbol:
            start_price = 3000.0
            volatility = 0.025
            drift = 0.0001
        elif "SOL" in symbol:
            start_price = 100.0
            volatility = 0.03
            drift = 0.0003
        else:
            start_price = 10.0
            volatility = 0.02
            drift = 0.0001
        
        # Generate random walk
        df = generate_random_walk(
            start_price=start_price,
            days=args.days,
            interval_hours=args.interval_hours,
            volatility=volatility,
            drift=drift
        )
        
        # Save to CSV
        file_name = f"{symbol.replace('-', '_')}.csv"
        file_path = os.path.join(args.output_dir, file_name)
        df.to_csv(file_path, index=False)
        
        logger.info(f"Generated {len(df)} data points for {symbol} to {file_path}")
    
    logger.info(f"Generated sample data for {len(args.symbols)} symbols")
    logger.info(f"Data saved to {args.output_dir}")
    
    return True


def main():
    """Parse command-line arguments and generate sample data."""
    parser = argparse.ArgumentParser(description="Generate sample historical data for backtesting")
    
    parser.add_argument("--symbols", "-s", nargs="+", help="Symbols to generate data for")
    parser.add_argument("--days", "-d", type=int, default=90, help="Number of days to generate data for")
    parser.add_argument("--interval-hours", "-i", type=int, default=1, help="Interval in hours between data points")
    parser.add_argument("--output-dir", "-o", default="data/historical", help="Output directory")
    
    args = parser.parse_args()
    
    # Generate sample data
    success = generate_sample_data(args)
    
    if not success:
        logger.error("Failed to generate sample data")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
