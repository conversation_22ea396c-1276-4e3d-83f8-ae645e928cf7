# SQLiteBus: Message Bus for Smart-Trader

This module provides a durable, SQLite-backed message bus implementation for the smart-trader system. It serves as the central pipeline for communication between different components of the system.

## Overview

The SQLiteBus provides a publish-subscribe pattern with message persistence. Messages are stored in a SQLite database, allowing them to survive system restarts and providing a historical record of all events.

## Features

- **Durable messaging**: All messages are persisted to disk in a SQLite database
- **Publish-subscribe pattern**: Components can publish messages to named streams and subscribe to receive messages from specific streams
- **Message retention**: Configurable message retention policy with automatic cleanup of old messages
- **Zero external dependencies**: Uses only the Python standard library and SQLite
- **Thread-safe**: Can be safely used from multiple threads
- **Lightweight**: Minimal overhead and resource usage

## Usage

### Basic Usage

```python
from pipeline.databus import SQLiteBus

# Create a bus instance
bus = SQLiteBus(path="data/bus.db", poll_interval=0.5)

# Define a callback function
def on_message(timestamp, payload):
    print(f"Received message at {timestamp}: {payload}")

# Subscribe to a stream
bus.subscribe("my_stream", on_message)

# Publish a message
bus.publish("my_stream", time.time(), {"key": "value", "number": 42})

# When done, close the bus
bus.close()
```

### Integration with Orchestrator

The SQLiteBus is integrated into the orchestrator as follows:

```python
from pipeline.databus import create_bus

# Create bus from configuration
bus = create_bus(config)

# Set up publisher for HTX client
htx_client.set_publisher(bus.publish)

# Set up publisher for SignalStar client
signalstar_client.set_publisher(bus.publish)

# Subscribe to streams
bus.subscribe("htx.kline", on_kline_message)
bus.subscribe("htx.trade", on_trade_message)
bus.subscribe("htx.orderbook", on_orderbook_message)
bus.subscribe("htx.position", on_position_message)
bus.subscribe("signalstar.sentiment", on_sentiment_message)
```

## Configuration

The SQLiteBus can be configured in the `config.yaml` file:

```yaml
pipeline:
  bus: "sqlite"  # Options: sqlite, in_memory
  sqlite:
    path: "data/bus.db"
    poll_interval: 0.5
  cleanup_interval_hours: 24
  message_retention_days: 7
```

## Stream Naming Convention

The following stream naming convention is used:

- `htx.kline`: Candlestick data from HTX
- `htx.trade`: Trade data from HTX
- `htx.orderbook`: Orderbook data from HTX
- `htx.position`: Position updates from HTX
- `htx.order`: Order updates from HTX
- `htx.fill`: Fill updates from HTX
- `htx.funding`: Funding rate updates from HTX
- `htx.open_interest`: Open interest updates from HTX
- `signalstar.sentiment`: Social sentiment data from SignalStar

## Message Format

Messages published to the bus have the following format:

```python
{
    "stream": "htx.kline",
    "timestamp": 1621234567.89,
    "payload": {
        "symbol": "BTC-USDT",
        "interval": "15min",
        "open": 40000.0,
        "high": 41000.0,
        "low": 39500.0,
        "close": 40500.0,
        "volume": 1234.56
    }
}
```

## Implementation Details

The SQLiteBus uses a background thread to poll the database for new messages. When a new message is found, it is dispatched to all subscribers of the corresponding stream.

The database schema is simple:

```sql
CREATE TABLE messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stream TEXT NOT NULL,
    ts REAL NOT NULL,
    payload TEXT NOT NULL
)
```

Indexes are created on the `stream` and `ts` columns for efficient lookups.

## Alternative Implementations

The module also provides an `InMemoryBus` implementation for testing or low-durability scenarios. This implementation does not persist messages to disk and is therefore not suitable for production use.

## Future Extensions

The SQLiteBus can be extended in the future to support:

- Message filtering
- Message transformation
- Message replay
- Integration with external message brokers like Redis or Kafka
