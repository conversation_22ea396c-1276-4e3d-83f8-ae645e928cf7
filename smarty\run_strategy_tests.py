#!/usr/bin/env python3
"""
Strategy Test Runner
Run tests for individual strategies or all strategies to verify they're operational.
"""

import asyncio
import logging
import subprocess
import sys
import json
from datetime import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StrategyTestRunner:
    """Run tests for trading strategies."""
    
    def __init__(self):
        self.available_strategies = [
            "Smart Model Integrated",
            "Smart Strategy Only", 
            "RSI Strategy",
            "Bollinger Bands",
            "Multi-Signal",
            "Ensemble Model",
            "SMA Crossover",
            "VWAP Strategy",
            "Scalper Strategy",
            "Order Flow"
        ]
        
        self.test_scripts = {
            "Smart Model Integrated": "test_strategy_smart_model_integrated.py",
            "Smart Strategy Only": "test_strategy_smart_strategy_only.py",
            "Order Flow": "test_strategy_order_flow.py",
            # All others use the data producer test
            "RSI Strategy": "test_strategy_data_producer.py",
            "Bollinger Bands": "test_strategy_data_producer.py",
            "Multi-Signal": "test_strategy_data_producer.py",
            "Ensemble Model": "test_strategy_data_producer.py",
            "SMA Crossover": "test_strategy_data_producer.py",
            "VWAP Strategy": "test_strategy_data_producer.py",
            "Scalper Strategy": "test_strategy_data_producer.py"
        }
    
    async def run_single_test(self, strategy_name: str) -> dict:
        """Run test for a single strategy."""
        if strategy_name not in self.available_strategies:
            logger.error(f"❌ Unknown strategy: {strategy_name}")
            return {"success": False, "error": "Unknown strategy"}
        
        test_script = self.test_scripts[strategy_name]
        
        logger.info(f"🎯 Testing {strategy_name}...")
        logger.info(f"📝 Using test script: {test_script}")
        
        try:
            # Prepare command
            if test_script == "test_strategy_data_producer.py":
                cmd = [sys.executable, test_script, strategy_name]
            else:
                cmd = [sys.executable, test_script]
            
            # Run the test
            start_time = datetime.now()
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            success = process.returncode == 0
            
            result = {
                "strategy_name": strategy_name,
                "success": success,
                "duration": duration,
                "return_code": process.returncode,
                "stdout": stdout.decode('utf-8') if stdout else "",
                "stderr": stderr.decode('utf-8') if stderr else "",
                "test_script": test_script
            }
            
            # Log result
            status = "✅ PASSED" if success else "❌ FAILED"
            logger.info(f"{status} {strategy_name} ({duration:.1f}s)")
            
            if not success:
                logger.error(f"Error: {stderr.decode('utf-8')[:200]}...")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Exception testing {strategy_name}: {e}")
            return {
                "strategy_name": strategy_name,
                "success": False,
                "duration": 0,
                "error": str(e),
                "test_script": test_script
            }
    
    async def run_all_tests(self) -> dict:
        """Run tests for all strategies."""
        logger.info("🚀 EPINNOX STRATEGY TESTING SUITE")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        results = {}
        passed = 0
        failed = 0
        
        for strategy in self.available_strategies:
            result = await self.run_single_test(strategy)
            results[strategy] = result
            
            if result["success"]:
                passed += 1
            else:
                failed += 1
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        # Print summary
        logger.info("\n" + "="*60)
        logger.info("📊 TEST SUMMARY")
        logger.info("="*60)
        logger.info(f"Total Strategies: {len(self.available_strategies)}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"📊 Success Rate: {(passed/len(self.available_strategies)*100):.1f}%")
        logger.info(f"⏱️ Total Duration: {total_duration:.1f}s")
        
        logger.info("\n📋 DETAILED RESULTS:")
        for strategy, result in results.items():
            status = "✅" if result["success"] else "❌"
            duration = result.get("duration", 0)
            logger.info(f"{status} {strategy:25} ({duration:6.1f}s)")
        
        # Save results
        summary = {
            "test_timestamp": start_time.isoformat(),
            "total_strategies": len(self.available_strategies),
            "passed": passed,
            "failed": failed,
            "success_rate": passed/len(self.available_strategies)*100,
            "total_duration": total_duration,
            "results": results
        }
        
        filename = f"strategy_test_summary_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"\n💾 Results saved to: {filename}")
        
        return summary
    
    def list_strategies(self):
        """List available strategies."""
        print("\n🎯 AVAILABLE STRATEGIES:")
        print("=" * 40)
        for i, strategy in enumerate(self.available_strategies, 1):
            test_script = self.test_scripts[strategy]
            print(f"{i:2d}. {strategy:25} ({test_script})")

async def main():
    """Main execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Epinnox trading strategies")
    parser.add_argument("strategy", nargs="?", help="Strategy name to test (or 'all' for all strategies)")
    parser.add_argument("--list", action="store_true", help="List available strategies")
    
    args = parser.parse_args()
    
    runner = StrategyTestRunner()
    
    if args.list:
        runner.list_strategies()
        return
    
    if not args.strategy:
        print("Usage: python run_strategy_tests.py <strategy_name|all>")
        print("       python run_strategy_tests.py --list")
        sys.exit(1)
    
    if args.strategy.lower() == "all":
        # Run all tests
        summary = await runner.run_all_tests()
        success = summary["failed"] == 0
    else:
        # Run single test
        result = await runner.run_single_test(args.strategy)
        success = result["success"]
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
