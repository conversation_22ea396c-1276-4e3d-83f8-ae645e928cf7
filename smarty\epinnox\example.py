#!/usr/bin/env python3
"""
Epinnox Trading System Example

This script demonstrates how to use the Epinnox smart strategy
with CCXT integration for multi-exchange trading.
"""

import asyncio
import logging
import yaml
from datetime import datetime
from pathlib import Path

from .strategy import EpinnoxSmartStrategy
from .ccxt_integration import CCXTManager
from .core.utils import setup_logging

logger = logging.getLogger(__name__)


class EpinnoxTradingBot:
    """
    Main trading bot using the Epinnox smart strategy.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the trading bot.
        
        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        config_file = Path(__file__).parent / config_path
        with open(config_file, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Setup logging
        setup_logging(
            level=self.config.get("logging", {}).get("level", "INFO")
        )
        
        # Initialize components
        self.strategy = EpinnoxSmartStrategy(self.config.get("strategy", {}))
        self.ccxt_manager = CCXTManager(self.config)
        
        # Trading settings
        self.symbols = self.config.get("symbols", ["BTC/USDT"])
        self.trading_enabled = self.config.get("trading", {}).get("enabled", False)
        self.simulation_mode = self.config.get("trading", {}).get("simulation_mode", True)
        
        self.running = False
    
    async def start(self) -> None:
        """Start the trading bot."""
        logger.info("🚀 Starting Epinnox Trading Bot...")
        
        try:
            # Initialize CCXT exchanges
            await self.ccxt_manager.initialize()
            
            # Start market data streaming
            await self.ccxt_manager.start_market_data(self.symbols)
            
            # Start strategy loop
            self.running = True
            await self._strategy_loop()
            
        except Exception as e:
            logger.error(f"Error starting trading bot: {e}")
            raise
    
    async def _strategy_loop(self) -> None:
        """Main strategy execution loop."""
        logger.info("📊 Starting strategy loop...")
        
        signal_interval = 60  # Generate signals every 60 seconds
        
        while self.running:
            try:
                # Generate signals for all exchanges
                for exchange_id in self.ccxt_manager.exchanges.keys():
                    signals = await self.strategy.generate_signals(
                        timestamp=datetime.now(),
                        symbols=self.symbols,
                        exchange=exchange_id
                    )
                    
                    # Process signals
                    for signal in signals:
                        await self._process_signal(signal, exchange_id)
                
                # Wait before next iteration
                await asyncio.sleep(signal_interval)
                
            except Exception as e:
                logger.error(f"Error in strategy loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying
    
    async def _process_signal(self, signal, exchange_id: str) -> None:
        """
        Process a trading signal.
        
        Args:
            signal: Trading signal
            exchange_id: Exchange identifier
        """
        logger.info(f"📈 Signal: {signal.action} {signal.symbol} on {exchange_id}")
        logger.info(f"   Score: {signal.score:.3f}")
        logger.info(f"   Rationale: {signal.rationale}")
        
        if self.trading_enabled and not self.simulation_mode:
            # Execute real trades (implement order execution logic here)
            logger.info("🔥 Would execute real trade (not implemented)")
        else:
            # Simulation mode - just log the signal
            logger.info("📝 Simulation mode - signal logged")
    
    async def stop(self) -> None:
        """Stop the trading bot."""
        logger.info("🛑 Stopping Epinnox Trading Bot...")
        
        self.running = False
        
        # Stop CCXT manager
        await self.ccxt_manager.stop()
        
        logger.info("✅ Trading bot stopped")
    
    def get_status(self) -> dict:
        """Get current bot status."""
        return {
            "running": self.running,
            "symbols": self.symbols,
            "trading_enabled": self.trading_enabled,
            "simulation_mode": self.simulation_mode,
            "exchanges": list(self.ccxt_manager.exchanges.keys()),
            "strategy_status": self.strategy.get_model_status()
        }


async def main():
    """Main function to run the trading bot."""
    bot = EpinnoxTradingBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await bot.stop()


def run_backtest():
    """Run a simple backtest example."""
    import pandas as pd
    import numpy as np
    
    logger.info("🔄 Running backtest example...")
    
    # Generate sample data
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='1H')
    prices = 50000 + np.cumsum(np.random.randn(len(dates)) * 100)
    volumes = np.random.uniform(10, 100, len(dates))
    
    # Create sample OHLCV data
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': prices * 1.01,
        'low': prices * 0.99,
        'close': prices,
        'volume': volumes
    })
    
    logger.info(f"Generated {len(data)} sample data points")
    logger.info(f"Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
    
    # TODO: Implement actual backtesting logic
    logger.info("📊 Backtest completed (sample data)")


def optimize_strategy():
    """Run strategy optimization example."""
    logger.info("⚙️ Running strategy optimization example...")
    
    # Example optimization parameters
    optimization_params = {
        "weights": {
            "technical": [0.2, 0.3, 0.4],
            "vwap": [0.1, 0.2, 0.3],
            "rsi_model": [0.1, 0.15, 0.2]
        },
        "thresholds": {
            "base_buy_threshold": [0.2, 0.3, 0.4],
            "base_sell_threshold": [-0.4, -0.3, -0.2]
        }
    }
    
    logger.info(f"Optimization parameters: {optimization_params}")
    
    # TODO: Implement actual optimization logic
    logger.info("🎯 Optimization completed (example)")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "backtest":
            run_backtest()
        elif command == "optimize":
            optimize_strategy()
        elif command == "live":
            asyncio.run(main())
        else:
            print("Usage: python example.py [live|backtest|optimize]")
    else:
        print("Epinnox Trading System")
        print("Usage: python example.py [live|backtest|optimize]")
        print("")
        print("Commands:")
        print("  live     - Run live trading bot")
        print("  backtest - Run backtesting example")
        print("  optimize - Run strategy optimization example")
