"""
Download sample historical data for backtesting.
"""

import os
import logging
import asyncio
import argparse
from datetime import datetime, timedelta

from backtester.data_downloader import HistoricalDataDownloader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def download_sample_data(args):
    """
    Download sample historical data for backtesting.
    
    Args:
        args: Command-line arguments
    """
    # Create downloader
    downloader = HistoricalDataDownloader(output_dir=args.output_dir)
    
    # Initialize downloader
    await downloader.initialize(
        api_key=args.api_key,
        api_secret=args.api_secret,
        testnet=args.testnet
    )
    
    # Default symbols if none provided
    if not args.symbols:
        args.symbols = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
    
    # Default start date if none provided (3 months ago)
    if not args.start_date:
        start_date = datetime.now() - timedelta(days=90)
        args.start_date = start_date.strftime("%Y-%m-%d")
    
    # Default end date if none provided (today)
    if not args.end_date:
        args.end_date = datetime.now().strftime("%Y-%m-%d")
    
    logger.info(f"Downloading data for {len(args.symbols)} symbols from {args.start_date} to {args.end_date}")
    
    # Download data
    results = await downloader.download_multiple_symbols(
        symbols=args.symbols,
        interval=args.interval,
        start_time=args.start_date,
        end_time=args.end_date
    )
    
    # Print summary
    success_count = sum(1 for success in results.values() if success)
    logger.info(f"Downloaded data for {success_count}/{len(args.symbols)} symbols")
    
    # Print file paths
    logger.info(f"Data saved to {args.output_dir}")
    
    return success_count == len(args.symbols)


def main():
    """Parse command-line arguments and download sample data."""
    parser = argparse.ArgumentParser(description="Download sample historical data for backtesting")
    
    parser.add_argument("--symbols", "-s", nargs="+", help="Symbols to download data for")
    parser.add_argument("--interval", "-i", default="1h", help="Kline interval (e.g., 1m, 5m, 15m, 1h, 4h, 1d)")
    parser.add_argument("--start-date", help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", help="End date in YYYY-MM-DD format")
    parser.add_argument("--output-dir", "-o", default="data/historical", help="Output directory")
    parser.add_argument("--api-key", default="", help="API key for exchange")
    parser.add_argument("--api-secret", default="", help="API secret for exchange")
    parser.add_argument("--testnet", action="store_true", help="Use testnet")
    
    args = parser.parse_args()
    
    # Download sample data
    success = asyncio.run(download_sample_data(args))
    
    if not success:
        logger.error("Failed to download all sample data")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
