#!/usr/bin/env python3
"""
Encryption Utilities for Money Circle
Secure encryption/decryption for API keys and sensitive data.
"""

import os
import base64
import logging
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

class EncryptionManager:
    """Manages encryption and decryption of sensitive data."""

    def __init__(self, master_key: Optional[bytes] = None):
        """Initialize encryption manager with master key."""
        if master_key is None:
            master_key = self._get_or_create_master_key()

        # Ensure the key is properly formatted for <PERSON><PERSON><PERSON>
        if isinstance(master_key, str):
            master_key = master_key.encode()

        # If it's not a valid <PERSON><PERSON>t key, generate one
        try:
            self.fernet = Fernet(master_key)
        except Exception:
            logger.warning("Invalid master key format, generating new key")
            master_key = Fernet.generate_key()
            self.fernet = Fernet(master_key)

    def _get_or_create_master_key(self) -> bytes:
        """Get existing master key or create a new one."""
        key_file = "data/.encryption_key"

        try:
            # Try to load existing key
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                # Create new key
                key = Fernet.generate_key()
                os.makedirs(os.path.dirname(key_file), exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(key)

                # Set restrictive permissions
                os.chmod(key_file, 0o600)
                logger.info("🔐 Generated new encryption master key")
                return key

        except Exception as e:
            logger.error(f"Error managing master key: {e}")
            # Fallback to environment variable or generate temporary key
            env_key = os.getenv('ENCRYPTION_KEY') or os.getenv('ENCRYPTION_MASTER_KEY')
            if env_key:
                try:
                    # Try to decode as base64
                    return base64.b64decode(env_key.encode())
                except Exception:
                    # If not base64, use as-is and pad/truncate to 32 bytes
                    key_bytes = env_key.encode('utf-8')
                    if len(key_bytes) < 32:
                        key_bytes = key_bytes.ljust(32, b'\0')
                    elif len(key_bytes) > 32:
                        key_bytes = key_bytes[:32]
                    return base64.urlsafe_b64encode(key_bytes)
            else:
                logger.warning("⚠️ Using temporary encryption key - data will not persist!")
                return Fernet.generate_key()

    def encrypt(self, data: Union[str, bytes]) -> bytes:
        """Encrypt data and return encrypted bytes."""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')

            encrypted = self.fernet.encrypt(data)
            logger.debug("🔒 Data encrypted successfully")
            return encrypted

        except Exception as e:
            logger.error(f"Encryption error: {e}")
            raise

    def decrypt(self, encrypted_data: bytes) -> str:
        """Decrypt data and return as string."""
        try:
            if not encrypted_data:
                logger.warning("Empty encrypted data provided")
                return ""

            decrypted = self.fernet.decrypt(encrypted_data)
            result = decrypted.decode('utf-8')
            logger.debug("🔓 Data decrypted successfully")
            return result

        except Exception as e:
            logger.error(f"Decryption error: {e}")
            # Return empty string instead of raising to prevent crashes
            return ""

    def encrypt_api_credentials(self, api_key: str, secret_key: str,
                              passphrase: Optional[str] = None) -> dict:
        """Encrypt API credentials for storage."""
        try:
            encrypted_creds = {
                'api_key': self.encrypt(api_key),
                'secret_key': self.encrypt(secret_key)
            }

            if passphrase:
                encrypted_creds['passphrase'] = self.encrypt(passphrase)

            logger.info("🔐 API credentials encrypted successfully")
            return encrypted_creds

        except Exception as e:
            logger.error(f"API credential encryption error: {e}")
            raise

    def decrypt_api_credentials(self, encrypted_creds: dict) -> dict:
        """Decrypt API credentials for use."""
        try:
            if not encrypted_creds:
                logger.warning("Empty encrypted credentials provided")
                return {'api_key': '', 'secret_key': '', 'passphrase': ''}

            decrypted_creds = {
                'api_key': self.decrypt(encrypted_creds.get('api_key', b'')),
                'secret_key': self.decrypt(encrypted_creds.get('secret_key', b''))
            }

            if 'passphrase' in encrypted_creds and encrypted_creds['passphrase']:
                decrypted_creds['passphrase'] = self.decrypt(encrypted_creds['passphrase'])
            else:
                decrypted_creds['passphrase'] = ''

            logger.debug("🔓 API credentials decrypted successfully")
            return decrypted_creds

        except Exception as e:
            logger.error(f"API credential decryption error: {e}")
            # Return empty credentials instead of raising
            return {'api_key': '', 'secret_key': '', 'passphrase': ''}

class SecureStorage:
    """Secure storage utilities for sensitive data."""

    @staticmethod
    def derive_key_from_password(password: str, salt: bytes) -> bytes:
        """Derive encryption key from password using PBKDF2."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

    @staticmethod
    def generate_salt() -> bytes:
        """Generate random salt for key derivation."""
        return os.urandom(16)

    @staticmethod
    def secure_delete_file(filepath: str) -> bool:
        """Securely delete a file by overwriting it."""
        try:
            if not os.path.exists(filepath):
                return True

            # Get file size
            filesize = os.path.getsize(filepath)

            # Overwrite with random data multiple times
            with open(filepath, "r+b") as file:
                for _ in range(3):
                    file.seek(0)
                    file.write(os.urandom(filesize))
                    file.flush()
                    os.fsync(file.fileno())

            # Finally delete the file
            os.remove(filepath)
            logger.info(f"🗑️ Securely deleted file: {filepath}")
            return True

        except Exception as e:
            logger.error(f"Secure file deletion error: {e}")
            return False

# Global encryption manager instance
_encryption_manager = None

def get_encryption_manager() -> EncryptionManager:
    """Get global encryption manager instance."""
    global _encryption_manager
    if _encryption_manager is None:
        try:
            _encryption_manager = EncryptionManager()
        except Exception as e:
            logger.error(f"Failed to initialize encryption manager: {e}")
            # Create a simple fallback encryption manager
            _encryption_manager = EncryptionManager(Fernet.generate_key())
    return _encryption_manager

def encrypt_data(data: Union[str, bytes]) -> bytes:
    """Convenience function to encrypt data."""
    return get_encryption_manager().encrypt(data)

def decrypt_data(encrypted_data: bytes) -> str:
    """Convenience function to decrypt data."""
    return get_encryption_manager().decrypt(encrypted_data)

def encrypt_api_credentials(api_key: str, secret_key: str,
                          passphrase: Optional[str] = None) -> dict:
    """Convenience function to encrypt API credentials."""
    return get_encryption_manager().encrypt_api_credentials(api_key, secret_key, passphrase)

def decrypt_api_credentials(encrypted_creds: dict) -> dict:
    """Convenience function to decrypt API credentials."""
    return get_encryption_manager().decrypt_api_credentials(encrypted_creds)
