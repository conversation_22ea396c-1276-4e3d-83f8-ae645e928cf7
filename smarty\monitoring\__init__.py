"""
Monitoring package for the smart-trader system.

Provides real-time monitoring of model performance, trading signals,
and system health with web dashboard and alerting capabilities.
"""

from .model_monitor import ModelPerformanceMonitor, ModelMetrics, SignalMetrics, SystemHealth
# Note: SignalDashboard removed - using unified dashboard instead

__all__ = [
    'ModelPerformanceMonitor',
    'ModelMetrics',
    'SignalMetrics',
    'SystemHealth'
]
