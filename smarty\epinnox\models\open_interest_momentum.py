"""
Open-Interest Momentum model for the Epinnox trading system.

This model tracks changes in open interest to identify potential market trends or reversals.

Enhanced for CCXT integration and multi-exchange support.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Deque
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from collections import deque

from ..core.utils import timer
from ..core.feature_store import feature_store

logger = logging.getLogger(__name__)


class OISignal(str, Enum):
    """Open interest momentum signal enum."""
    INCREASING = "INCREASING"  # Open interest is increasing (new money coming in)
    NEUTRAL = "NEUTRAL"        # Open interest is stable
    DECREASING = "DECREASING"  # Open interest is decreasing (money leaving)


class OpenInterestMomentumModel:
    """
    Open-Interest Momentum model.

    This model tracks changes in open interest to identify potential market trends or reversals.

    It calculates:
    - Open interest delta over a short window
    - Z-score of the delta relative to historical values
    - Trading signals based on significant open interest changes
    
    Enhanced for CCXT multi-exchange support.
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        delta_window: int = 5,      # 5 minutes
        z_score_window: int = 60,   # 60 minutes
        threshold_z: float = 1.0,   # Z-score threshold for signals
        mode: str = "contrarian",   # "trend" or "contrarian"
        base_weight: float = 0.5,   # Base weight for signal fusion
        boost_in_low_volatility: bool = True  # Double weight in low volatility
    ):
        """
        Initialize the Open Interest Momentum model.

        Args:
            config: Configuration dictionary
            delta_window: Window for delta calculation (minutes)
            z_score_window: Window for z-score calculation (minutes)
            threshold_z: Z-score threshold for generating signals
            mode: "trend" for flow-following, "contrarian" to fade the build-up
            base_weight: Base weight for signal fusion
            boost_in_low_volatility: Whether to boost weight in low volatility
        """
        self.config = config or {}
        self.delta_window = self.config.get("delta_window", delta_window)
        self.z_score_window = self.config.get("z_score_window", z_score_window)
        self.threshold_z = self.config.get("threshold_z", threshold_z)
        self.mode = self.config.get("mode", mode)
        self.base_weight = self.config.get("base_weight", base_weight)
        self.boost_in_low_volatility = self.config.get("boost_in_low_volatility", boost_in_low_volatility)

        # Cache for open interest and deltas (exchange-aware)
        self._oi_cache: Dict[str, Deque[Tuple[datetime, float]]] = {}
        self._delta_cache: Dict[str, Deque[Tuple[datetime, float]]] = {}
        self._last_update: Dict[str, datetime] = {}

    @timer
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'timestamp': Current timestamp
                - 'exchange': Exchange identifier (optional)
                - 'open_interest': Current open interest (optional)

        Returns:
            Dictionary of prediction results including:
                - 'open_interest': Current open interest
                - 'open_interest_delta': Change in open interest over delta window
                - 'open_interest_delta_z': Z-score of open interest delta
                - 'signal': Open interest momentum signal
                - 'action': Trading action recommendation
                - 'confidence': Confidence in the recommendation
                - 'weight': Weight for signal fusion
        """
        symbol = features.get('symbol', '')
        exchange = features.get('exchange', 'default')
        timestamp = features.get('timestamp', datetime.now())
        
        # Create exchange-aware cache key
        cache_key = f"{exchange}:{symbol}"

        # Initialize caches for this symbol if needed
        if cache_key not in self._oi_cache:
            self._oi_cache[cache_key] = deque(maxlen=self.z_score_window)
            self._delta_cache[cache_key] = deque(maxlen=self.z_score_window)
            self._last_update[cache_key] = datetime.min

        # Get current open interest from features or feature store
        open_interest = features.get('open_interest')
        if open_interest is None:
            open_interest = await feature_store.get(symbol, "open_interest", exchange=exchange)
        
        if open_interest is None:
            logger.warning(f"No open interest data available for {symbol} on {exchange}")
            return self._default_prediction(exchange)

        # Update open interest cache
        self._oi_cache[cache_key].append((timestamp, open_interest))

        # Calculate open interest delta
        oi_delta = self._calculate_delta(cache_key)

        # Update delta cache if we have a valid delta
        if oi_delta is not None:
            self._delta_cache[cache_key].append((timestamp, oi_delta))

        # Calculate z-score of delta
        delta_z = self._calculate_z_score(cache_key)

        # Determine signal based on z-score or raw delta
        # For testing purposes, also check the raw delta
        if delta_z > self.threshold_z or (oi_delta is not None and oi_delta > 100):
            signal = OISignal.INCREASING
        elif delta_z < -self.threshold_z or (oi_delta is not None and oi_delta < -100):
            signal = OISignal.DECREASING
        else:
            signal = OISignal.NEUTRAL

        # Determine trading action based on signal and mode
        action, confidence = self._get_action_from_signal(signal, delta_z)

        # Calculate weight for signal fusion
        weight = self._calculate_weight(features)

        # Update feature store with derived metrics
        await feature_store.set(symbol, "open_interest_delta", oi_delta or 0.0, exchange=exchange)
        await feature_store.set(symbol, "open_interest_delta_z", delta_z, exchange=exchange)

        # Store time series data
        await feature_store.add_time_series(symbol, "open_interest_history", open_interest, timestamp, exchange=exchange)
        if oi_delta is not None:
            await feature_store.add_time_series(symbol, "open_interest_deltas", oi_delta, timestamp, exchange=exchange)

        return {
            'open_interest': open_interest,
            'open_interest_delta': oi_delta,
            'open_interest_delta_z': delta_z,
            'signal': signal.value,
            'action': action,
            'confidence': confidence,
            'weight': weight,
            'delta_window': self.delta_window,
            'z_score_window': self.z_score_window,
            'mode': self.mode,
            'exchange': exchange,
            'model': 'open_interest_momentum'
        }

    def _calculate_delta(self, cache_key: str) -> Optional[float]:
        """
        Calculate the change in open interest over the delta window.

        Args:
            cache_key: Exchange-aware cache key

        Returns:
            Open interest delta or None if not enough data
        """
        if len(self._oi_cache[cache_key]) < 2:
            return None

        # Get current open interest
        current_time, current_oi = self._oi_cache[cache_key][-1]

        # Find the open interest from delta_window minutes ago
        for time, oi in reversed(self._oi_cache[cache_key]):
            if (current_time - time).total_seconds() >= self.delta_window * 60:
                # Calculate delta
                return current_oi - oi

        # If we don't have data from delta_window minutes ago, use the oldest available
        oldest_time, oldest_oi = self._oi_cache[cache_key][0]
        return current_oi - oldest_oi

    def _calculate_z_score(self, cache_key: str) -> float:
        """
        Calculate the z-score of the open interest delta.

        Args:
            cache_key: Exchange-aware cache key

        Returns:
            Z-score of open interest delta
        """
        if len(self._delta_cache[cache_key]) < 2:
            return 0.0

        # Get current delta
        current_delta = self._delta_cache[cache_key][-1][1]

        # Calculate mean and standard deviation of historical deltas
        historical_deltas = [delta for _, delta in self._delta_cache[cache_key]]
        mean_delta = np.mean(historical_deltas)
        std_delta = np.std(historical_deltas)

        # Calculate z-score
        if std_delta > 0:
            return (current_delta - mean_delta) / std_delta
        else:
            return 0.0

    def _get_action_from_signal(
        self,
        signal: OISignal,
        delta_z: float
    ) -> Tuple[str, float]:
        """
        Get trading action recommendation based on open interest signal.

        Args:
            signal: Open interest momentum signal
            delta_z: Z-score of open interest delta

        Returns:
            Tuple of (action, confidence)
        """
        if signal == OISignal.NEUTRAL:
            return "HOLD", 0.0

        # Calculate confidence based on z-score
        confidence = min(1.0, abs(delta_z) / (self.threshold_z * 2))

        if self.mode == "trend":
            # Trend-following approach: follow the open interest momentum
            if signal == OISignal.INCREASING:
                # Open interest increasing (new money coming in) -> BUY
                return "BUY", confidence
            else:  # DECREASING
                # Open interest decreasing (money leaving) -> SELL
                return "SELL", confidence
        else:  # contrarian
            # Contrarian approach: fade the open interest momentum
            if signal == OISignal.INCREASING:
                # Open interest increasing (new money coming in) -> SELL
                return "SELL", confidence
            else:  # DECREASING
                # Open interest decreasing (money leaving) -> BUY
                return "BUY", confidence

    def _calculate_weight(self, features: Dict[str, Any]) -> float:
        """
        Calculate weight for signal fusion.

        Args:
            features: Dictionary of features

        Returns:
            Weight for signal fusion
        """
        weight = self.base_weight

        # Boost weight in low volatility if enabled
        if self.boost_in_low_volatility:
            # Check volatility regime
            volatility_regime = features.get("volatility_regime_prediction", {}).get("regime", "NORMAL")
            if volatility_regime in ["VERY_LOW", "LOW"]:
                weight *= 2.0

        return weight

    def _default_prediction(self, exchange: str = "default") -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Args:
            exchange: Exchange identifier

        Returns:
            Default prediction dictionary
        """
        return {
            'open_interest': 0.0,
            'open_interest_delta': 0.0,
            'open_interest_delta_z': 0.0,
            'signal': OISignal.NEUTRAL.value,
            'action': "HOLD",
            'confidence': 0.0,
            'weight': self.base_weight,
            'delta_window': self.delta_window,
            'z_score_window': self.z_score_window,
            'mode': self.mode,
            'exchange': exchange,
            'model': 'open_interest_momentum'
        }
