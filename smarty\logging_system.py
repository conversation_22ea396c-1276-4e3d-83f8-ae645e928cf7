#!/usr/bin/env python3
"""
Enhanced Logging System for Smart-Trader

Provides structured logging with multiple handlers, real-time streaming,
and integration with the web interface.
"""

import json
import logging
import logging.handlers
import queue
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    logger: str
    message: str
    module: str = ""
    function: str = ""
    line: int = 0
    extra: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extra is None:
            self.extra = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict())


class MemoryLogHandler(logging.Handler):
    """In-memory log handler for real-time display."""
    
    def __init__(self, max_entries: int = 1000):
        super().__init__()
        self.max_entries = max_entries
        self.entries: List[LogEntry] = []
        self.lock = threading.Lock()
        self.subscribers = []

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record."""
        try:
            entry = LogEntry(
                timestamp=datetime.fromtimestamp(record.created).isoformat(),
                level=record.levelname,
                logger=record.name,
                message=record.getMessage(),
                module=record.module if hasattr(record, 'module') else '',
                function=record.funcName if hasattr(record, 'funcName') else '',
                line=record.lineno if hasattr(record, 'lineno') else 0,
                extra=getattr(record, 'extra', {})
            )

            with self.lock:
                self.entries.append(entry)
                if len(self.entries) > self.max_entries:
                    self.entries.pop(0)

            # Notify subscribers
            self._notify_subscribers(entry)

        except Exception:
            self.handleError(record)

    def get_recent_entries(self, count: int = 100) -> List[LogEntry]:
        """Get recent log entries."""
        with self.lock:
            return self.entries[-count:] if count > 0 else self.entries.copy()

    def clear_entries(self) -> None:
        """Clear all log entries."""
        with self.lock:
            self.entries.clear()

    def subscribe(self, callback) -> None:
        """Subscribe to new log entries."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback) -> None:
        """Unsubscribe from log entries."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def _notify_subscribers(self, entry: LogEntry) -> None:
        """Notify all subscribers of new log entry."""
        for callback in self.subscribers:
            try:
                callback(entry)
            except Exception:
                pass  # Don't let subscriber errors affect logging


class JSONFormatter(logging.Formatter):
    """JSON log formatter."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            logger=record.name,
            message=record.getMessage(),
            module=getattr(record, 'module', ''),
            function=getattr(record, 'funcName', ''),
            line=getattr(record, 'lineno', 0),
            extra=getattr(record, 'extra', {})
        )
        return entry.to_json()


class ColoredFormatter(logging.Formatter):
    """Colored console formatter."""
    
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }

    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # Format message
        message = f"{color}[{timestamp}] {record.levelname:8} {record.name}: {record.getMessage()}{reset}"
        
        return message


class SmartTraderLogger:
    """Enhanced logging system for Smart-Trader."""
    
    def __init__(self, 
                 log_file: str = "smart_trader.log",
                 log_level: LogLevel = LogLevel.INFO,
                 max_file_size: int = 10485760,  # 10MB
                 backup_count: int = 5,
                 console_enabled: bool = True,
                 json_enabled: bool = True):
        """Initialize logging system."""
        
        self.log_file = Path(log_file)
        self.log_level = log_level
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.console_enabled = console_enabled
        self.json_enabled = json_enabled
        
        # Create memory handler for real-time logs
        self.memory_handler = MemoryLogHandler()
        
        # Setup logging
        self._setup_logging()
        
        # Create logger instances
        self.logger = logging.getLogger('smart_trader')
        self.web_logger = logging.getLogger('smart_trader.web')
        self.trading_logger = logging.getLogger('smart_trader.trading')
        self.llm_logger = logging.getLogger('smart_trader.llm')
        self.db_logger = logging.getLogger('smart_trader.database')

    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        # Clear existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set root level
        root_logger.setLevel(self.log_level.value)
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        if self.console_enabled:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(ColoredFormatter())
            console_handler.setLevel(self.log_level.value)
            root_logger.addHandler(console_handler)
        
        # File handler with rotation
        if self.log_file:
            self.log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.handlers.RotatingFileHandler(
                self.log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count
            )
            file_handler.setFormatter(file_formatter)
            file_handler.setLevel(self.log_level.value)
            root_logger.addHandler(file_handler)
        
        # JSON file handler
        if self.json_enabled:
            json_file = self.log_file.with_suffix('.json')
            json_handler = logging.handlers.RotatingFileHandler(
                json_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count
            )
            json_handler.setFormatter(JSONFormatter())
            json_handler.setLevel(self.log_level.value)
            root_logger.addHandler(json_handler)
        
        # Memory handler
        self.memory_handler.setLevel(self.log_level.value)
        root_logger.addHandler(self.memory_handler)

    def get_recent_logs(self, count: int = 100, level: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent log entries."""
        entries = self.memory_handler.get_recent_entries(count)
        
        if level:
            entries = [e for e in entries if e.level == level.upper()]
        
        return [e.to_dict() for e in entries]

    def clear_logs(self) -> None:
        """Clear in-memory logs."""
        self.memory_handler.clear_entries()

    def subscribe_to_logs(self, callback) -> None:
        """Subscribe to real-time log updates."""
        self.memory_handler.subscribe(callback)

    def unsubscribe_from_logs(self, callback) -> None:
        """Unsubscribe from log updates."""
        self.memory_handler.unsubscribe(callback)

    def log_trading_action(self, action: str, symbol: str, details: Dict[str, Any]) -> None:
        """Log trading action with structured data."""
        self.trading_logger.info(
            f"Trading action: {action} on {symbol}",
            extra={'action': action, 'symbol': symbol, 'details': details}
        )

    def log_llm_interaction(self, prompt: str, response: str, model: str, duration: float) -> None:
        """Log LLM interaction."""
        self.llm_logger.info(
            f"LLM interaction completed in {duration:.2f}s",
            extra={
                'model': model,
                'prompt_length': len(prompt),
                'response_length': len(response),
                'duration': duration
            }
        )

    def log_api_call(self, endpoint: str, method: str, status_code: int, duration: float) -> None:
        """Log API call."""
        self.web_logger.info(
            f"{method} {endpoint} - {status_code} ({duration:.3f}s)",
            extra={
                'endpoint': endpoint,
                'method': method,
                'status_code': status_code,
                'duration': duration
            }
        )

    def log_database_operation(self, operation: str, table: str, duration: float, rows_affected: int = 0) -> None:
        """Log database operation."""
        self.db_logger.info(
            f"Database {operation} on {table} - {rows_affected} rows ({duration:.3f}s)",
            extra={
                'operation': operation,
                'table': table,
                'duration': duration,
                'rows_affected': rows_affected
            }
        )

    def log_system_metric(self, metric_name: str, value: float, unit: str = "") -> None:
        """Log system metric."""
        self.logger.info(
            f"Metric {metric_name}: {value} {unit}",
            extra={
                'metric_name': metric_name,
                'value': value,
                'unit': unit,
                'timestamp': datetime.now().isoformat()
            }
        )

    def get_log_statistics(self) -> Dict[str, Any]:
        """Get logging statistics."""
        entries = self.memory_handler.get_recent_entries()
        
        stats = {
            'total_entries': len(entries),
            'by_level': {},
            'by_logger': {},
            'recent_errors': [],
            'time_range': {
                'start': entries[0].timestamp if entries else None,
                'end': entries[-1].timestamp if entries else None
            }
        }
        
        for entry in entries:
            # Count by level
            stats['by_level'][entry.level] = stats['by_level'].get(entry.level, 0) + 1
            
            # Count by logger
            stats['by_logger'][entry.logger] = stats['by_logger'].get(entry.logger, 0) + 1
            
            # Collect recent errors
            if entry.level in ['ERROR', 'CRITICAL']:
                stats['recent_errors'].append({
                    'timestamp': entry.timestamp,
                    'level': entry.level,
                    'message': entry.message,
                    'logger': entry.logger
                })
        
        # Keep only recent errors (last 10)
        stats['recent_errors'] = stats['recent_errors'][-10:]
        
        return stats


# Global logger instance
smart_logger = SmartTraderLogger()


def get_logger(name: str = 'smart_trader') -> logging.Logger:
    """Get logger instance."""
    return logging.getLogger(name)


def log_trading_action(action: str, symbol: str, details: Dict[str, Any]) -> None:
    """Log trading action."""
    smart_logger.log_trading_action(action, symbol, details)


def log_llm_interaction(prompt: str, response: str, model: str, duration: float) -> None:
    """Log LLM interaction."""
    smart_logger.log_llm_interaction(prompt, response, model, duration)


def log_api_call(endpoint: str, method: str, status_code: int, duration: float) -> None:
    """Log API call."""
    smart_logger.log_api_call(endpoint, method, status_code, duration)


def get_recent_logs(count: int = 100, level: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get recent logs."""
    return smart_logger.get_recent_logs(count, level)


def get_log_statistics() -> Dict[str, Any]:
    """Get log statistics."""
    return smart_logger.get_log_statistics()
