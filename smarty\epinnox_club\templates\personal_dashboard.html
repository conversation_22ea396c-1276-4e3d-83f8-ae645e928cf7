{% extends "base.html" %}

{% block title %}Money Circle - Personal Dashboard{% endblock %}

{% block extra_css %}
<!-- Dashboard CSS loaded asynchronously for performance -->
<link rel="preload" href="/static/css/dashboard.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link rel="preload" href="/static/css/club.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/club.css">
</noscript>

<!-- Preload Chart.js for better performance -->
<link rel="preload" href="https://cdn.jsdelivr.net/npm/chart.js" as="script">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Main Dashboard Content -->
    <div class="dashboard-grid">
        <!-- Portfolio Overview -->
        <section class="portfolio-overview">
            <h2>💼 Portfolio Overview</h2>
            <div class="portfolio-cards">
                <div class="portfolio-card">
                    <h3>Total Value</h3>
                    <div class="value">${{ portfolio.total_value|round(2) }}</div>
                    <div class="change {{ 'positive' if portfolio.daily_change >= 0 else 'negative' }}">
                        {{ '+' if portfolio.daily_change >= 0 else '' }}{{ portfolio.daily_change|round(2) }}%
                    </div>
                </div>
                <div class="portfolio-card">
                    <h3>Available Balance</h3>
                    <div class="value">${{ portfolio.available_balance|round(2) }}</div>
                </div>
                <div class="portfolio-card">
                    <h3>Open Positions</h3>
                    <div class="value">{{ portfolio.open_positions }}</div>
                </div>
                <div class="portfolio-card">
                    <h3>P&L Today</h3>
                    <div class="value {{ 'positive' if portfolio.daily_pnl >= 0 else 'negative' }}">
                        ${{ portfolio.daily_pnl|round(2) }}
                    </div>
                </div>
            </div>
        </section>

        <!-- Exchange Connections -->
        <section class="exchange-connections">
            <h2>🔗 Exchange Connections</h2>
            <div class="exchange-grid">
                {% for exchange in exchanges %}
                <div class="exchange-card {{ 'connected' if exchange.connected else 'disconnected' }}">
                    <h4>{{ exchange.exchange_name }}</h4>
                    <div class="connection-status">
                        {{ '🟢 Connected' if exchange.connected else '🔴 Disconnected' }}
                    </div>
                    {% if exchange.connected and exchange.balance %}
                    <div class="balance-summary">
                        <div class="balance-item">
                            <span>USDT:</span>
                            <span>${{ exchange.balance.get('USDT', 0)|round(2) }}</span>
                        </div>
                    </div>
                    {% endif %}
                    <div class="exchange-actions">
                        <button onclick="refreshExchange('{{ exchange.exchange_name }}')">🔄 Refresh</button>
                        <button onclick="removeExchange({{ exchange.id }})">🗑️ Remove</button>
                    </div>
                </div>
                {% endfor %}

                <!-- Add Exchange Card -->
                <div class="exchange-card add-exchange">
                    <h4>➕ Add Exchange</h4>
                    <p>Connect a new exchange account</p>
                    <button onclick="showAddExchangeModal()">Add Exchange</button>
                </div>
            </div>
        </section>

        <!-- Quick Trade Controls -->
        <section class="quick-trade-section">
            <h2>⚡ Quick Trade</h2>
            <div class="quick-trade-buttons">
                <!-- Quick trade buttons will be dynamically generated -->
            </div>
        </section>

        <!-- Trading Interface -->
        <section class="trading-interface">
            <h2>📈 Advanced Trading</h2>
            <div class="trading-tabs">
                <button class="tab-btn active" onclick="switchTab('market')">Market Order</button>
                <button class="tab-btn" onclick="switchTab('limit')">Limit Order</button>
                <button class="tab-btn" onclick="switchTab('positions')">Positions</button>
                <button class="tab-btn" onclick="switchTab('risk')">Risk Management</button>
            </div>

            <!-- Market Order Tab -->
            <div id="market-tab" class="tab-content active">
                <div class="order-form">
                    <div class="order-type-selector">
                        <button class="order-type-btn active" data-type="buy">Buy</button>
                        <button class="order-type-btn" data-type="sell">Sell</button>
                    </div>

                    <div class="form-group">
                        <label>Symbol</label>
                        <select id="market-symbol">
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Amount (USDT)</label>
                        <input type="number" id="market-amount" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <button class="place-order-btn" onclick="placeMarketOrder()">
                        Place Market Order
                    </button>
                </div>
            </div>

            <!-- Limit Order Tab -->
            <div id="limit-tab" class="tab-content">
                <div class="order-form">
                    <div class="order-type-selector">
                        <button class="order-type-btn active" data-type="buy">Buy</button>
                        <button class="order-type-btn" data-type="sell">Sell</button>
                    </div>

                    <div class="form-group">
                        <label>Symbol</label>
                        <select id="limit-symbol">
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Price (USDT)</label>
                        <input type="number" id="limit-price" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <div class="form-group">
                        <label>Amount (USDT)</label>
                        <input type="number" id="limit-amount" placeholder="0.00" step="0.01" min="0">
                    </div>

                    <button class="place-order-btn" onclick="placeLimitOrder()">
                        Place Limit Order
                    </button>
                </div>
            </div>

            <!-- Positions Tab -->
            <div id="positions-tab" class="tab-content">
                <div class="positions-list" id="positions-list">
                    <!-- Positions will be loaded via JavaScript -->
                </div>
            </div>
        </section>

        <!-- Market Data -->
        <section class="market-data">
            <h2>📊 Market Data</h2>
            <div class="market-widgets">
                <!-- Price Chart -->
                <div class="market-widget">
                    <h3>Price Chart</h3>
                    <canvas id="price-chart"></canvas>
                </div>

                <!-- Order Book -->
                <div class="market-widget">
                    <h3>Order Book</h3>
                    <div class="orderbook" id="orderbook">
                        <!-- Order book data will be loaded via JavaScript -->
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="market-widget">
                    <h3>Recent Trades</h3>
                    <div class="trades-list" id="trades-list">
                        <!-- Recent trades will be loaded via JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Analytics -->
        <section class="performance-analytics">
            <h2>📈 Performance Analytics</h2>
            <div class="analytics-grid">
                <div class="analytics-card">
                    <h4>Win Rate</h4>
                    <div class="metric">{{ portfolio.win_rate|round(1) }}%</div>
                </div>
                <div class="analytics-card">
                    <h4>Avg Trade</h4>
                    <div class="metric">${{ portfolio.avg_trade|round(2) }}</div>
                </div>
                <div class="analytics-card">
                    <h4>Max Drawdown</h4>
                    <div class="metric">{{ portfolio.max_drawdown|round(1) }}%</div>
                </div>
                <div class="analytics-card">
                    <h4>Sharpe Ratio</h4>
                    <div class="metric">{{ portfolio.sharpe_ratio|round(2) }}</div>
                </div>
            </div>
        </section>
    </div>

    <!-- Modals -->
    <div id="add-exchange-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('add-exchange-modal')">&times;</span>
            <h2>Add Exchange Connection</h2>
            <form id="add-exchange-form">
                <div class="form-group">
                    <label>Exchange</label>
                    <select id="exchange-type" required>
                        <option value="">Select Exchange</option>
                        <option value="binance">Binance</option>
                        <option value="htx">HTX (Huobi)</option>
                        <option value="bybit">Bybit</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>API Key</label>
                    <input type="text" id="api-key" required>
                </div>
                <div class="form-group">
                    <label>API Secret</label>
                    <input type="password" id="api-secret" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="testnet"> Use Testnet
                    </label>
                </div>
                <button type="submit">Add Exchange</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Load Chart.js asynchronously -->
<script async src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Load dashboard JavaScript asynchronously -->
<script async src="/static/js/personal_dashboard.js"></script>

<script>
    // Initialize dashboard data immediately (critical for functionality)
    window.dashboardData = {
        user: {{ user|tojson }},
        portfolio: {{ portfolio|tojson }},
        exchanges: {{ exchanges|tojson }}
    };

    // Initialize dashboard when both DOM and scripts are ready
    function initDashboard() {
        if (typeof initializePersonalDashboard === 'function') {
            initializePersonalDashboard();
        } else {
            // Retry if script not loaded yet
            setTimeout(initDashboard, 100);
        }
    }

    document.addEventListener('DOMContentLoaded', initDashboard);
</script>
{% endblock %}
