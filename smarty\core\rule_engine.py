"""
Rule engine for the smart-trader system.
Handles position sizing, risk checks, and trading rules.
"""

import logging
from typing import Dict, Any, Optional, Tuple, List

from .events import Side, Signal, Position

logger = logging.getLogger(__name__)


class RuleEngine:
    """
    Rule engine for trading decisions.

    This class handles:
    - Position sizing
    - Risk management rules
    - Signal filtering and fusion
    - Pre-trade validation
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the rule engine.

        Args:
            config: Configuration dictionary with risk parameters
        """
        self.config = config
        self.max_position_size = config.get("max_position_size", 0.0)
        self.max_leverage = config.get("max_leverage", 10.0)
        self.risk_per_trade = config.get("risk_per_trade", 0.01)  # 1% of balance
        self.min_signal_score = config.get("min_signal_score", 0.3)
        self.max_drawdown = config.get("max_drawdown", 0.1)  # 10% max drawdown
        self.contract_size = config.get("contract_size", 1.0)

        # Track equity high watermark for drawdown calculation
        self.equity_high_watermark = 0.0

        # Global trading enabled flag (can be disabled by risk rules)
        self.trading_enabled = True

    def calculate_position_size(
        self,
        balance: float,
        price: float,
        leverage: float,
        risk_multiplier: float = 1.0
    ) -> float:
        """
        Calculate the position size based on account balance and risk parameters.

        Args:
            balance: Account balance in quote currency
            price: Current price of the asset
            leverage: Leverage to use
            risk_multiplier: Multiplier to adjust risk (0.0-1.0)

        Returns:
            Position size in base currency
        """
        # Apply risk per trade to balance
        risk_amount = balance * self.risk_per_trade * risk_multiplier

        # Calculate position size
        position_value = risk_amount * leverage
        position_size = position_value / price

        # Round to contract size
        position_size = self._round_to_contract_size(position_size)

        # Apply max position size limit
        if self.max_position_size > 0 and position_size > self.max_position_size:
            position_size = self.max_position_size
            position_size = self._round_to_contract_size(position_size)

        return position_size

    def _round_to_contract_size(self, size: float) -> float:
        """
        Round the position size to the nearest contract size.

        Args:
            size: Position size

        Returns:
            Rounded position size
        """
        return round(size / self.contract_size) * self.contract_size

    def check_drawdown(self, current_equity: float) -> bool:
        """
        Check if current drawdown exceeds the maximum allowed.

        Args:
            current_equity: Current account equity

        Returns:
            True if drawdown is acceptable, False if exceeded
        """
        # Update high watermark if current equity is higher
        if current_equity > self.equity_high_watermark:
            self.equity_high_watermark = current_equity

        # Skip check if high watermark is zero (first run)
        if self.equity_high_watermark == 0:
            return True

        # Calculate drawdown
        drawdown = 1.0 - (current_equity / self.equity_high_watermark)

        # Check if drawdown exceeds maximum
        if drawdown > self.max_drawdown:
            logger.warning(f"Maximum drawdown exceeded: {drawdown:.2%} > {self.max_drawdown:.2%}")
            return False

        return True

    def validate_signal(self, signal: Signal, features: Dict[str, Any], position: Optional[Position] = None) -> Tuple[bool, str]:
        """
        Validate a trading signal against risk rules.

        Args:
            signal: The trading signal
            features: Current feature values
            position: Current position (if any)

        Returns:
            Tuple of (is_valid, reason)
        """
        # Check if trading is globally enabled
        if not self.trading_enabled:
            return False, "Trading is disabled by risk management"

        # Check signal score threshold
        if abs(signal.score) < self.min_signal_score:
            return False, f"Signal score too low: {signal.score} < {self.min_signal_score}"

        # Check RSI limits
        rsi = features.get("rsi", 50.0)
        if signal.action == Side.BUY and rsi > 80:
            return False, f"RSI overbought: {rsi} > 80"
        if signal.action == Side.SELL and rsi < 20:
            return False, f"RSI oversold: {rsi} < 20"

        # Check volatility regime if available
        volatility_regime = features.get("volatility_regime_prediction", {}).get("regime", "NORMAL")
        if volatility_regime == "HIGH_VOL" and abs(signal.score) < self.min_signal_score * 1.5:
            return False, f"Signal score too low for high volatility regime: {signal.score}"

        # Check GARCH volatility if available
        if "garch_volatility_prediction" in features:
            garch_data = features["garch_volatility_prediction"]
            # Get volatility z-score for potential future use
            _ = garch_data.get("volatility_z", 0.0)
            volatility_level = garch_data.get("volatility_level", "NORMAL")

            # If volatility is very high, require stronger signal
            if volatility_level == "VERY_HIGH" and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Signal score too low for very high GARCH volatility: {signal.score}"

            # Adjust position size based on volatility
            position_size_multiplier = garch_data.get("position_size_multiplier", 1.0)
            if position_size_multiplier < 0.7 and abs(signal.score) < self.min_signal_score * 1.2:
                return False, f"Signal score too low for reduced position size: {signal.score}"

        # Check position limits
        if position:
            # Don't increase an existing position beyond max size
            max_size = self.max_position_size

            # Adjust max size based on volatility regime
            if volatility_regime == "LOW_VOL":
                # Allow larger positions in low volatility
                max_size *= 1.2
            elif volatility_regime == "HIGH_VOL":
                # Reduce max position size in high volatility
                max_size *= 0.5

            if position.side == signal.action and position.size >= max_size:
                return False, f"Max position size reached: {position.size} >= {max_size} (regime: {volatility_regime})"

            # Don't trade against existing position unless it's a reduce-only order
            if position.side != signal.action and signal.metadata.get("reduce_only", False) == False:
                return False, f"Signal conflicts with current position side: {position.side} != {signal.action}"

        # Check VWAP deviation if available
        if "vwap_deviation_prediction" in features:
            vwap_data = features["vwap_deviation_prediction"]
            z_score = vwap_data.get("primary_z_score", 0.0)
            price = features.get("mid_price", 0.0)

            # If price is significantly above VWAP and trying to buy, require stronger signal
            if signal.action == Side.BUY and z_score > 2.0 and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Price too far above VWAP (z-score: {z_score:.2f}) for BUY with score {signal.score}"

            # If price is significantly below VWAP and trying to sell, require stronger signal
            if signal.action == Side.SELL and z_score < -2.0 and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Price too far below VWAP (z-score: {z_score:.2f}) for SELL with score {signal.score}"

        # Check liquidity imbalance if available
        if "liquidity_imbalance_prediction" in features:
            liq_data = features["liquidity_imbalance_prediction"]
            imbalance = liq_data.get("imbalance_ratio", 0.0)
            thinness = liq_data.get("thinness", 0.0)

            # If liquidity is very thin, require stronger signal
            if thinness > 0.8 and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Liquidity too thin (thinness: {thinness:.2f}) for trading with score {signal.score}"

            # If imbalance strongly contradicts signal, require stronger signal
            if signal.action == Side.BUY and imbalance < -0.5 and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Liquidity imbalance ({imbalance:.2f}) contradicts BUY signal"

            if signal.action == Side.SELL and imbalance > 0.5 and abs(signal.score) < self.min_signal_score * 1.5:
                return False, f"Liquidity imbalance ({imbalance:.2f}) contradicts SELL signal"

        # Check Bollinger Bands if available
        if "bollinger_prediction" in features:
            boll_data = features["bollinger_prediction"]
            price = features.get("mid_price", 0.0)

            # If price is above upper band and trying to buy, reject
            if signal.action == Side.BUY and price > boll_data.get("upper_band", float('inf')):
                return False, f"Price above upper Bollinger Band: {price} > {boll_data.get('upper_band')}"

            # If price is below lower band and trying to sell, reject
            if signal.action == Side.SELL and price < boll_data.get("lower_band", 0.0):
                return False, f"Price below lower Bollinger Band: {price} < {boll_data.get('lower_band')}"

        # Check funding rate if available
        if "funding_rate" in features and abs(features["funding_rate"]) > 0.001:  # 0.1% threshold
            funding_rate = features["funding_rate"]

            # If funding rate is positive (longs pay shorts) and trying to buy, be more cautious
            if signal.action == Side.BUY and funding_rate > 0.001:
                if signal.score < self.min_signal_score * 1.5:
                    return False, f"Signal score too low for positive funding rate: {signal.score}"

            # If funding rate is negative (shorts pay longs) and trying to sell, be more cautious
            if signal.action == Side.SELL and funding_rate < -0.001:
                if signal.score < self.min_signal_score * 1.5:
                    return False, f"Signal score too low for negative funding rate: {signal.score}"

        # Check funding momentum if available
        if "funding_momentum_prediction" in features:
            funding_pred = features["funding_momentum_prediction"]
            funding_z = funding_pred.get("funding_delta_z", 0.0)
            funding_signal = funding_pred.get("signal", "NEUTRAL")

            # If funding momentum strongly contradicts signal, require stronger signal
            if signal.action == Side.BUY and funding_signal == "NEGATIVE_MOMENTUM" and funding_z < -1.5:
                if abs(signal.score) < self.min_signal_score * 1.5:
                    return False, f"Funding momentum ({funding_z:.2f}) contradicts BUY signal"

            if signal.action == Side.SELL and funding_signal == "POSITIVE_MOMENTUM" and funding_z > 1.5:
                if abs(signal.score) < self.min_signal_score * 1.5:
                    return False, f"Funding momentum ({funding_z:.2f}) contradicts SELL signal"

        # Check open interest momentum if available
        if "open_interest_momentum_prediction" in features:
            oi_pred = features["open_interest_momentum_prediction"]
            oi_z = oi_pred.get("open_interest_delta_z", 0.0)
            oi_signal = oi_pred.get("signal", "NEUTRAL")
            oi_mode = oi_pred.get("mode", "contrarian")

            # Check if OI signal contradicts trading signal based on mode
            if oi_mode == "contrarian":
                # In contrarian mode, INCREASING OI -> SELL, DECREASING OI -> BUY
                if signal.action == Side.BUY and oi_signal == "INCREASING" and oi_z > 1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"OI momentum ({oi_z:.2f}, contrarian) contradicts BUY signal"

                if signal.action == Side.SELL and oi_signal == "DECREASING" and oi_z < -1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"OI momentum ({oi_z:.2f}, contrarian) contradicts SELL signal"
            else:  # trend mode
                # In trend mode, INCREASING OI -> BUY, DECREASING OI -> SELL
                if signal.action == Side.BUY and oi_signal == "DECREASING" and oi_z < -1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"OI momentum ({oi_z:.2f}, trend) contradicts BUY signal"

                if signal.action == Side.SELL and oi_signal == "INCREASING" and oi_z > 1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"OI momentum ({oi_z:.2f}, trend) contradicts SELL signal"

        # Check social sentiment if available
        if "social_sentiment_prediction" in features:
            ss_pred = features["social_sentiment_prediction"]
            ss_z = ss_pred.get("z_score", 0.0)
            ss_signal = ss_pred.get("signal", "HOLD")
            ss_contrarian = ss_pred.get("contrarian", True)

            # Check if social sentiment contradicts trading signal based on mode
            if ss_contrarian:
                # In contrarian mode, POSITIVE sentiment -> SELL, NEGATIVE sentiment -> BUY
                if signal.action == Side.BUY and ss_signal == "SELL" and ss_z > 1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"Social sentiment ({ss_z:.2f}, contrarian) contradicts BUY signal"

                if signal.action == Side.SELL and ss_signal == "BUY" and ss_z < -1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"Social sentiment ({ss_z:.2f}, contrarian) contradicts SELL signal"
            else:  # trend mode
                # In trend mode, POSITIVE sentiment -> BUY, NEGATIVE sentiment -> SELL
                if signal.action == Side.BUY and ss_signal == "SELL" and ss_z < -1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"Social sentiment ({ss_z:.2f}, trend) contradicts BUY signal"

                if signal.action == Side.SELL and ss_signal == "BUY" and ss_z > 1.5:
                    if abs(signal.score) < self.min_signal_score * 1.5:
                        return False, f"Social sentiment ({ss_z:.2f}, trend) contradicts SELL signal"

        return True, "Signal validated"

    def fuse_signals(self, signals: List[Signal], features: Dict[str, Any]) -> Optional[Signal]:
        """
        Fuse multiple signals into a single decision.

        Args:
            signals: List of signals from different sources
            features: Current feature values

        Returns:
            Fused signal or None if no decision can be made
        """
        if not signals:
            return None

        # Get volatility regime if available
        volatility_regime = features.get("volatility_regime_prediction", {}).get("regime", "NORMAL")

        # Adjust signal weights based on regime
        weighted_signals = []
        for signal in signals:
            weight = 1.0

            # Adjust weights based on source and regime
            if signal.source == "llm":
                # LLM gets higher weight in normal conditions
                if volatility_regime == "NORMAL":
                    weight = 1.5
                elif volatility_regime == "HIGH_VOL":
                    weight = 0.8  # Reduce LLM weight in high volatility

            elif signal.source == "volatility_regime":
                # Volatility regime model gets higher weight in high volatility
                if volatility_regime == "HIGH_VOL":
                    weight = 1.5

            elif signal.source.startswith("ml."):
                # ML models get higher weight in high volatility
                if volatility_regime == "HIGH_VOL":
                    weight = 1.3

            elif signal.source == "rsi":
                # RSI gets higher weight in low volatility (mean reversion)
                if volatility_regime == "LOW_VOL":
                    weight = 1.3

            elif signal.source == "vwap_deviation":
                # VWAP deviation gets higher weight in normal volatility (mean reversion)
                if volatility_regime == "NORMAL":
                    weight = 1.4
                # Check if we have liquidity information
                thinness = features.get("liquidity_imbalance_prediction", {}).get("thinness", 0.0)
                if thinness > 0.7:  # High thinness (low liquidity)
                    weight *= 0.8  # Reduce weight when liquidity is thin

            elif signal.source == "liquidity_imbalance":
                # Liquidity imbalance gets higher weight when liquidity is thin
                thinness = signal.metadata.get("thinness", 0.0)
                if thinness > 0.7:  # High thinness (low liquidity)
                    weight = 1.5  # Increase weight when liquidity is thin
                # Also consider volatility
                if volatility_regime == "HIGH_VOL":
                    weight *= 1.2  # Further increase in high volatility

            elif signal.source == "garch_volatility":
                # GARCH volatility gets higher weight in high volatility
                volatility_level = signal.metadata.get("volatility_level", "NORMAL")
                if volatility_level in ["HIGH", "VERY_HIGH"]:
                    weight = 1.4  # Increase weight in high volatility

                # Also consider position sizing recommendations
                position_size_multiplier = signal.metadata.get("position_size_multiplier", 1.0)
                if position_size_multiplier < 0.7:  # Significant reduction recommended
                    weight *= 1.3  # Further increase weight

            elif signal.source == "funding_momentum":
                # Funding momentum gets higher weight when z-score is extreme
                funding_z = signal.metadata.get("funding_delta_z", 0.0)
                if abs(funding_z) > 2.0:  # Very extreme funding rate change
                    weight = 1.6  # High weight for extreme funding changes
                elif abs(funding_z) > 1.0:  # Significant funding rate change
                    weight = 1.3  # Moderate weight for significant funding changes

                # Also consider market conditions
                if volatility_regime == "HIGH_VOL":
                    weight *= 0.8  # Reduce weight in high volatility (funding less reliable)

                # Check if we have liquidity information
                thinness = features.get("liquidity_imbalance_prediction", {}).get("thinness", 0.0)
                if thinness > 0.7:  # High thinness (low liquidity)
                    weight *= 1.2  # Increase weight when liquidity is thin (funding more important)

            elif signal.source == "open_interest_momentum":
                # Open interest momentum weight is already adjusted in the model
                # based on volatility regime, but we can further adjust here
                base_weight = signal.metadata.get("weight", 0.5)
                weight = base_weight

                # Adjust based on z-score magnitude
                oi_z = signal.metadata.get("open_interest_delta_z", 0.0)
                if abs(oi_z) > 2.0:  # Very extreme OI change
                    weight *= 1.3  # Increase weight for extreme changes

                # Consider market conditions
                if volatility_regime == "LOW_VOL" or volatility_regime == "VERY_LOW":
                    # Already boosted in model if boost_in_low_volatility is True
                    pass
                elif volatility_regime == "HIGH_VOL":
                    # In high volatility, OI changes might be less meaningful
                    weight *= 0.9

            elif signal.source == "social_sentiment":
                # Social sentiment weight is already adjusted in the model
                # based on volatility regime, but we can further adjust here
                base_weight = signal.metadata.get("weight", 0.5)
                weight = base_weight

                # Adjust based on z-score magnitude
                ss_z = signal.metadata.get("z_score", 0.0)
                if abs(ss_z) > 2.0:  # Very extreme sentiment change
                    weight *= 1.4  # Increase weight for extreme changes

                # Consider market conditions
                if volatility_regime == "HIGH_VOL":
                    # In high volatility, sentiment might be more important
                    weight *= 1.2
                elif volatility_regime == "LOW_VOL":
                    # In low volatility, sentiment might be less reliable
                    weight *= 0.8

                # Check if we have liquidity information
                thinness = features.get("liquidity_imbalance_prediction", {}).get("thinness", 0.0)
                if thinness > 0.7:  # High thinness (low liquidity)
                    weight *= 1.1  # Increase weight when liquidity is thin (sentiment more important)

            weighted_signals.append((signal, weight))

        # Start with the LLM signal as the base if available
        llm_signals = [s for s, _ in weighted_signals if s.source == "llm"]
        if llm_signals:
            base_signal = llm_signals[0]
        else:
            # Otherwise use the highest-scoring signal (weighted)
            base_signal = max([s for s, _ in weighted_signals], key=lambda s: abs(s.score))

        # Check if other models disagree with the base signal
        other_signals = [(s, w) for s, w in weighted_signals if s != base_signal]

        if other_signals:
            # Calculate weighted agreement score
            agreement_score = 0.0
            total_weight = 0.0

            for other_signal, weight in other_signals:
                # Calculate agreement between -1 and 1
                if other_signal.action == base_signal.action:
                    agreement = 1.0
                elif other_signal.action == Side.HOLD:
                    agreement = 0.0
                else:
                    agreement = -1.0

                # Weight by signal score and model weight
                agreement_score += agreement * abs(other_signal.score) * weight
                total_weight += weight

            # Normalize
            if total_weight > 0:
                agreement_score /= total_weight

            # Adjust base signal score based on agreement
            adjustment_factor = 1.0 + (agreement_score * 0.3)  # -30% to +30% adjustment
            base_signal.score *= adjustment_factor

            # Cap score at -1.0 to 1.0
            base_signal.score = max(-1.0, min(1.0, base_signal.score))

            # Update rationale
            if agreement_score > 0.3:
                base_signal.rationale += f" (Models confirm, {volatility_regime})"
            elif agreement_score < -0.3:
                base_signal.rationale += f" (Models disagree, {volatility_regime})"
            else:
                base_signal.rationale += f" (Mixed signals, {volatility_regime})"

        # Final validation
        is_valid, reason = self.validate_signal(base_signal, features)
        if not is_valid:
            logger.info(f"Fused signal rejected: {reason}")
            return None

        return base_signal
