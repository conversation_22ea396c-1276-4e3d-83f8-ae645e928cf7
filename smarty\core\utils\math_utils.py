"""
Mathematical utility functions for the smart-trader system.
"""

import numpy as np
import pandas as pd
from functools import lru_cache
from typing import Union, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

# Type aliases
ArrayLike = Union[np.ndarray, list, pd.Series]


def validate_arrays(*arrays: ArrayLike, min_length: int = 1) -> None:
    """
    Validate input arrays for mathematical operations.

    Args:
        *arrays: Arrays to validate
        min_length: Minimum required length

    Raises:
        ValueError: If validation fails
    """
    if not arrays:
        raise ValueError("At least one array must be provided")

    lengths = [len(arr) for arr in arrays]

    if len(set(lengths)) > 1:
        raise ValueError(f"All arrays must have the same length. Got lengths: {lengths}")

    if lengths[0] < min_length:
        raise ValueError(f"Arrays must have at least {min_length} elements. Got {lengths[0]}")


def calculate_vwap(prices: ArrayLike, volumes: ArrayLike) -> float:
    """
    Calculate Volume Weighted Average Price with improved validation and performance.

    Args:
        prices: Array of prices
        volumes: Array of volumes

    Returns:
        VWAP value

    Raises:
        ValueError: If arrays are invalid or volumes sum to zero
    """
    # Convert to numpy arrays for performance
    prices = np.asarray(prices, dtype=float)
    volumes = np.asarray(volumes, dtype=float)

    # Validate inputs
    validate_arrays(prices, volumes, min_length=1)

    # Check for negative volumes
    if np.any(volumes < 0):
        raise ValueError("Volumes cannot be negative")

    total_volume = np.sum(volumes)
    if total_volume == 0:
        raise ValueError("Total volume cannot be zero")

    # Calculate VWAP using vectorized operations
    return np.sum(prices * volumes) / total_volume


def calculate_rolling_vwap(
    prices: ArrayLike,
    volumes: ArrayLike,
    window: int
) -> np.ndarray:
    """
    Calculate rolling VWAP using optimized pandas operations.

    Args:
        prices: Array of prices
        volumes: Array of volumes
        window: Rolling window size

    Returns:
        Array of rolling VWAP values
    """
    # Convert to pandas Series for efficient rolling operations
    prices_series = pd.Series(prices, dtype=float)
    volumes_series = pd.Series(volumes, dtype=float)

    # Validate inputs
    validate_arrays(prices, volumes, min_length=window)

    if window < 1:
        raise ValueError("Window size must be at least 1")

    # Calculate rolling VWAP using pandas
    price_volume = prices_series * volumes_series
    rolling_pv = price_volume.rolling(window=window).sum()
    rolling_volume = volumes_series.rolling(window=window).sum()

    # Avoid division by zero
    rolling_vwap = rolling_pv / rolling_volume.replace(0, np.nan)

    return rolling_vwap.values


@lru_cache(maxsize=128)
def calculate_rsi_optimized(
    prices_tuple: tuple,
    period: int = 14
) -> float:
    """
    Calculate RSI with caching for performance optimization.

    Args:
        prices_tuple: Tuple of prices (for caching)
        period: RSI period

    Returns:
        RSI value
    """
    prices = np.array(prices_tuple)
    return _calculate_rsi_internal(prices, period)


def calculate_rsi(prices: ArrayLike, period: int = 14) -> float:
    """
    Calculate Relative Strength Index with vectorized operations.

    Args:
        prices: Array of prices
        period: RSI period (default 14)

    Returns:
        RSI value (0-100)

    Raises:
        ValueError: If insufficient data or invalid period
    """
    prices = np.asarray(prices, dtype=float)

    # Validate inputs
    validate_arrays(prices, min_length=period + 1)

    if period < 1:
        raise ValueError("Period must be at least 1")

    # Use cached version for small arrays
    if len(prices) <= 100:
        return calculate_rsi_optimized(tuple(prices), period)

    return _calculate_rsi_internal(prices, period)


def _calculate_rsi_internal(prices: np.ndarray, period: int) -> float:
    """Internal RSI calculation with vectorized operations."""
    # Calculate price changes
    deltas = np.diff(prices)

    # Separate gains and losses
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)

    # Calculate average gains and losses
    avg_gain = np.mean(gains[-period:]) if len(gains) >= period else np.mean(gains)
    avg_loss = np.mean(losses[-period:]) if len(losses) >= period else np.mean(losses)

    # Avoid division by zero
    if avg_loss == 0:
        return 100.0 if avg_gain > 0 else 50.0

    # Calculate RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return float(rsi)


def calculate_rolling_rsi(prices: ArrayLike, period: int = 14) -> np.ndarray:
    """
    Calculate rolling RSI using pandas for efficiency.

    Args:
        prices: Array of prices
        period: RSI period

    Returns:
        Array of RSI values
    """
    prices_series = pd.Series(prices, dtype=float)

    # Calculate price changes
    delta = prices_series.diff()

    # Separate gains and losses
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Calculate rolling averages
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()

    # Calculate RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi.values


def calculate_imbalance(
    bid_prices: ArrayLike,
    bid_volumes: ArrayLike,
    ask_prices: ArrayLike,
    ask_volumes: ArrayLike
) -> float:
    """
    Calculate order book imbalance with enhanced validation.

    Args:
        bid_prices: Array of bid prices
        bid_volumes: Array of bid volumes
        ask_prices: Array of ask prices
        ask_volumes: Array of ask volumes

    Returns:
        Imbalance ratio (-1 to 1, positive = more buying pressure)

    Raises:
        ValueError: If arrays are invalid
    """
    # Convert to numpy arrays
    bid_prices = np.asarray(bid_prices, dtype=float)
    bid_volumes = np.asarray(bid_volumes, dtype=float)
    ask_prices = np.asarray(ask_prices, dtype=float)
    ask_volumes = np.asarray(ask_volumes, dtype=float)

    # Validate inputs
    validate_arrays(bid_prices, bid_volumes, min_length=1)
    validate_arrays(ask_prices, ask_volumes, min_length=1)

    # Check for negative volumes
    if np.any(bid_volumes < 0) or np.any(ask_volumes < 0):
        raise ValueError("Volumes cannot be negative")

    # Calculate weighted volumes
    bid_weighted = np.sum(bid_prices * bid_volumes)
    ask_weighted = np.sum(ask_prices * ask_volumes)

    total_weighted = bid_weighted + ask_weighted

    if total_weighted == 0:
        return 0.0

    # Calculate imbalance (-1 to 1)
    imbalance = (bid_weighted - ask_weighted) / total_weighted

    return float(np.clip(imbalance, -1.0, 1.0))


def normalize_array(arr: ArrayLike, method: str = 'minmax') -> np.ndarray:
    """
    Normalize an array using different methods.

    Args:
        arr: Array to normalize
        method: Normalization method ('minmax', 'zscore', 'robust')

    Returns:
        Normalized array

    Raises:
        ValueError: If method is not supported
    """
    arr = np.asarray(arr, dtype=float)
    validate_arrays(arr, min_length=1)

    if method == 'minmax':
        # Min-max normalization to [0, 1]
        min_val, max_val = np.min(arr), np.max(arr)
        if max_val == min_val:
            return np.ones_like(arr) * 0.5
        return (arr - min_val) / (max_val - min_val)

    elif method == 'zscore':
        # Z-score normalization (mean=0, std=1)
        mean_val, std_val = np.mean(arr), np.std(arr)
        if std_val == 0:
            return np.zeros_like(arr)
        return (arr - mean_val) / std_val

    elif method == 'robust':
        # Robust normalization using median and IQR
        median_val = np.median(arr)
        q75, q25 = np.percentile(arr, [75, 25])
        iqr = q75 - q25
        if iqr == 0:
            return np.zeros_like(arr)
        return (arr - median_val) / iqr

    else:
        raise ValueError(f"Unsupported normalization method: {method}")


def format_price(price: float, tick_size: float = 0.0001) -> float:
    """
    Format price to align with tick size.

    Args:
        price: Price to format
        tick_size: Minimum price increment

    Returns:
        Formatted price
    """
    if tick_size <= 0:
        raise ValueError("Tick size must be positive")

    return round(price / tick_size) * tick_size


def round_quantity(quantity: float, lot_size: float = 1.0) -> float:
    """
    Round quantity to align with lot size.

    Args:
        quantity: Quantity to round
        lot_size: Minimum quantity increment

    Returns:
        Rounded quantity
    """
    if lot_size <= 0:
        raise ValueError("Lot size must be positive")

    return round(quantity / lot_size) * lot_size


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    Calculate percentage change between two values.

    Args:
        old_value: Original value
        new_value: New value

    Returns:
        Percentage change
    """
    if old_value == 0:
        return 0.0 if new_value == 0 else float('inf')

    return ((new_value - old_value) / abs(old_value)) * 100


def calculate_sharpe_ratio(
    returns: ArrayLike,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252
) -> float:
    """
    Calculate Sharpe ratio for returns.

    Args:
        returns: Array of returns
        risk_free_rate: Risk-free rate (annualized)
        periods_per_year: Number of periods per year

    Returns:
        Sharpe ratio
    """
    returns = np.asarray(returns, dtype=float)
    validate_arrays(returns, min_length=2)

    # Calculate excess returns
    excess_returns = returns - (risk_free_rate / periods_per_year)

    # Calculate Sharpe ratio
    mean_excess = np.mean(excess_returns)
    std_excess = np.std(excess_returns, ddof=1)

    if std_excess == 0:
        return 0.0

    return (mean_excess / std_excess) * np.sqrt(periods_per_year)


def calculate_order_flow_imbalance(
    bids: ArrayLike,
    asks: ArrayLike,
    decay: float = 0.9
) -> float:
    """
    Calculate order flow imbalance with decay factor.

    Args:
        bids: Array of bid sizes
        asks: Array of ask sizes
        decay: Decay factor for older values

    Returns:
        Order flow imbalance value (-1 to 1)
    """
    bids = np.asarray(bids, dtype=float)
    asks = np.asarray(asks, dtype=float)

    if len(bids) == 0 and len(asks) == 0:
        return 0.0

    total_bids = np.sum(bids) if len(bids) > 0 else 0.0
    total_asks = np.sum(asks) if len(asks) > 0 else 0.0

    if total_bids + total_asks == 0:
        return 0.0

    # Calculate imbalance with decay
    imbalance = (total_bids - total_asks) / (total_bids + total_asks)
    return float(np.clip(imbalance * decay, -1.0, 1.0))


if __name__ == "__main__":
    # Test mathematical utilities
    print("Testing mathematical utilities:")

    # Test VWAP calculation
    prices = [100, 101, 102, 103, 104]
    volumes = [1000, 1500, 800, 1200, 900]
    vwap = calculate_vwap(prices, volumes)
    print(f"VWAP: {vwap:.4f}")

    # Test rolling VWAP
    rolling_vwap = calculate_rolling_vwap(prices, volumes, window=3)
    print(f"Rolling VWAP: {rolling_vwap}")

    # Test RSI calculation
    price_series = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
                   46.03, 46.28, 46.28, 46.00, 46.03, 46.41, 46.22, 45.64]
    rsi = calculate_rsi(price_series, period=14)
    print(f"RSI: {rsi:.2f}")

    # Test imbalance calculation
    bid_prices = [99.5, 99.4, 99.3]
    bid_volumes = [100, 200, 150]
    ask_prices = [100.0, 100.1, 100.2]
    ask_volumes = [80, 120, 100]
    imbalance = calculate_imbalance(bid_prices, bid_volumes, ask_prices, ask_volumes)
    print(f"Order book imbalance: {imbalance:.4f}")

    # Test normalization
    data = [1, 2, 3, 4, 5, 10, 15, 20]
    normalized = normalize_array(data, method='minmax')
    print(f"Normalized data: {normalized}")

    # Test price formatting
    price = 123.456789
    formatted = format_price(price, tick_size=0.01)
    print(f"Formatted price: {formatted}")

    # Test quantity rounding
    quantity = 123.456
    rounded = round_quantity(quantity, lot_size=0.1)
    print(f"Rounded quantity: {rounded}")

    # Test percentage change
    pct_change = calculate_percentage_change(100, 105)
    print(f"Percentage change: {pct_change:.2f}%")

    # Test Sharpe ratio
    returns = np.random.normal(0.001, 0.02, 252)  # Daily returns
    sharpe = calculate_sharpe_ratio(returns, risk_free_rate=0.02)
    print(f"Sharpe ratio: {sharpe:.4f}")

    print("Mathematical utility tests completed")
