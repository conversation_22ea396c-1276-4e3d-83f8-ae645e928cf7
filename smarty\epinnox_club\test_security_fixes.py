#!/usr/bin/env python3
"""
Comprehensive Security Fixes Test Script
Tests all the critical security and API integration fixes
"""

import asyncio
import logging
import json
import os
import sys
from pathlib import Path
import aiohttp
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8087"

class SecurityFixesTester:
    """Test all security fixes."""
    
    def __init__(self):
        """Initialize the tester."""
        self.session = None
        self.csrf_token = None
        self.test_results = {}
        logger.info("🔒 Security Fixes Tester initialized")
    
    async def run_all_tests(self):
        """Run all security fix tests."""
        try:
            logger.info("=" * 70)
            logger.info("MONEY CIRCLE SECURITY FIXES TEST SUITE")
            logger.info("=" * 70)
            
            # Create session
            self.session = aiohttp.ClientSession()
            
            # Test 1: Environment Configuration
            await self._test_environment_config()
            
            # Test 2: Encryption/Decryption
            await self._test_encryption_decryption()
            
            # Test 3: CSRF Token API
            await self._test_csrf_token_api()
            
            # Test 4: Rate Limiting (Localhost Bypass)
            await self._test_rate_limiting()
            
            # Test 5: HTX API Integration
            await self._test_htx_api_integration()
            
            # Test 6: Exchange Balance API
            await self._test_exchange_balance_api()
            
            # Test 7: Security Manager
            await self._test_security_manager()
            
            # Generate test report
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            return False
        finally:
            if self.session:
                await self.session.close()
        
        return True
    
    async def _test_environment_config(self):
        """Test environment configuration."""
        logger.info("[TEST 1] Testing Environment Configuration...")
        
        try:
            # Check .env file
            env_file = Path('.env')
            if env_file.exists():
                with open(env_file, 'r') as f:
                    env_content = f.read()
                
                # Check for required variables
                required_vars = [
                    'ENCRYPTION_KEY',
                    'SECRET_KEY',
                    'HTX_API_KEY',
                    'HTX_API_SECRET'
                ]
                
                missing_vars = []
                for var in required_vars:
                    if var not in env_content:
                        missing_vars.append(var)
                
                if missing_vars:
                    logger.warning(f"⚠️ Missing environment variables: {missing_vars}")
                    self.test_results['environment_config'] = 'PARTIAL'
                else:
                    logger.info("✅ All required environment variables present")
                    self.test_results['environment_config'] = 'PASS'
            else:
                logger.error("❌ .env file not found")
                self.test_results['environment_config'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"❌ Environment config test failed: {e}")
            self.test_results['environment_config'] = 'FAIL'
    
    async def _test_encryption_decryption(self):
        """Test encryption/decryption functionality."""
        logger.info("[TEST 2] Testing Encryption/Decryption...")
        
        try:
            from exchanges.encryption_utils import EncryptionManager
            
            # Create encryption manager
            encryption_manager = EncryptionManager()
            logger.info("✅ Encryption manager created")
            
            # Test encryption/decryption
            test_data = "test_api_key_12345"
            encrypted = encryption_manager.encrypt(test_data)
            decrypted = encryption_manager.decrypt(encrypted)
            
            if decrypted == test_data:
                logger.info("✅ Encryption/decryption working correctly")
                self.test_results['encryption_decryption'] = 'PASS'
            else:
                logger.error("❌ Encryption/decryption mismatch")
                self.test_results['encryption_decryption'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"❌ Encryption/decryption test failed: {e}")
            self.test_results['encryption_decryption'] = 'FAIL'
    
    async def _test_csrf_token_api(self):
        """Test CSRF token API endpoint."""
        logger.info("[TEST 3] Testing CSRF Token API...")
        
        try:
            # Test CSRF token endpoint
            async with self.session.get(f"{BASE_URL}/api/csrf-token") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'csrf_token' in data and data['csrf_token']:
                        self.csrf_token = data['csrf_token']
                        logger.info(f"✅ CSRF token received: {self.csrf_token[:16]}...")
                        self.test_results['csrf_token_api'] = 'PASS'
                    else:
                        logger.error("❌ CSRF token not in response")
                        self.test_results['csrf_token_api'] = 'FAIL'
                else:
                    logger.error(f"❌ CSRF token API failed: {response.status}")
                    self.test_results['csrf_token_api'] = 'FAIL'
                    
        except Exception as e:
            logger.error(f"❌ CSRF token API test failed: {e}")
            self.test_results['csrf_token_api'] = 'FAIL'
    
    async def _test_rate_limiting(self):
        """Test rate limiting (should bypass localhost)."""
        logger.info("[TEST 4] Testing Rate Limiting...")
        
        try:
            # Make multiple rapid requests to test rate limiting
            request_count = 10
            success_count = 0
            
            for i in range(request_count):
                try:
                    async with self.session.get(f"{BASE_URL}/api/symbols") as response:
                        if response.status == 200:
                            success_count += 1
                        elif response.status == 429:
                            logger.warning(f"⚠️ Rate limited on request {i+1}")
                        await asyncio.sleep(0.1)  # Small delay
                except Exception:
                    pass
            
            if success_count >= request_count * 0.8:  # 80% success rate
                logger.info(f"✅ Rate limiting bypassed for localhost ({success_count}/{request_count} successful)")
                self.test_results['rate_limiting'] = 'PASS'
            else:
                logger.warning(f"⚠️ Some requests were rate limited ({success_count}/{request_count} successful)")
                self.test_results['rate_limiting'] = 'PARTIAL'
                
        except Exception as e:
            logger.error(f"❌ Rate limiting test failed: {e}")
            self.test_results['rate_limiting'] = 'FAIL'
    
    async def _test_htx_api_integration(self):
        """Test HTX API integration."""
        logger.info("[TEST 5] Testing HTX API Integration...")
        
        try:
            from trading.htx_futures_client import HTXFuturesClient
            
            # Create HTX client
            htx_client = HTXFuturesClient()
            logger.info("✅ HTX client created")
            
            # Test connection (may fail without real credentials, but should not crash)
            try:
                connected = await htx_client.connect()
                if connected:
                    logger.info("✅ HTX client connected successfully")
                    self.test_results['htx_api_integration'] = 'PASS'
                else:
                    logger.info("ℹ️ HTX client connection failed (expected with testnet)")
                    self.test_results['htx_api_integration'] = 'PARTIAL'
            except Exception as e:
                logger.info(f"ℹ️ HTX connection error (expected): {e}")
                self.test_results['htx_api_integration'] = 'PARTIAL'
                
        except Exception as e:
            logger.error(f"❌ HTX API integration test failed: {e}")
            self.test_results['htx_api_integration'] = 'FAIL'
    
    async def _test_exchange_balance_api(self):
        """Test exchange balance API with CSRF token."""
        logger.info("[TEST 6] Testing Exchange Balance API...")
        
        try:
            if not self.csrf_token:
                logger.warning("⚠️ No CSRF token available, skipping balance API test")
                self.test_results['exchange_balance_api'] = 'SKIP'
                return
            
            # Test balance API with CSRF token
            headers = {
                'X-CSRF-Token': self.csrf_token,
                'Content-Type': 'application/json'
            }
            
            # This will likely fail due to authentication, but should not fail due to CSRF
            async with self.session.post(
                f"{BASE_URL}/api/exchanges/balance/42",
                headers=headers,
                json={}
            ) as response:
                if response.status == 401:
                    logger.info("✅ Balance API returned 401 (authentication required) - CSRF token accepted")
                    self.test_results['exchange_balance_api'] = 'PASS'
                elif response.status == 403:
                    error_data = await response.json()
                    if 'CSRF' in error_data.get('error', ''):
                        logger.error("❌ CSRF token still being rejected")
                        self.test_results['exchange_balance_api'] = 'FAIL'
                    else:
                        logger.info("✅ CSRF token accepted, other authorization issue")
                        self.test_results['exchange_balance_api'] = 'PASS'
                else:
                    logger.info(f"ℹ️ Balance API returned {response.status}")
                    self.test_results['exchange_balance_api'] = 'PARTIAL'
                    
        except Exception as e:
            logger.error(f"❌ Exchange balance API test failed: {e}")
            self.test_results['exchange_balance_api'] = 'FAIL'
    
    async def _test_security_manager(self):
        """Test security manager functionality."""
        logger.info("[TEST 7] Testing Security Manager...")
        
        try:
            from security.security_manager import SecurityManager
            
            # Create security manager
            config = {
                'max_login_attempts': 5,
                'lockout_duration': 900,
                'rate_limit_requests': 100,
                'rate_limit_window': 3600,
                'session_timeout': 3600
            }
            
            security_manager = SecurityManager(config)
            logger.info("✅ Security manager created")
            
            # Test localhost detection
            localhost_ips = ['127.0.0.1', '::1', 'localhost']
            for ip in localhost_ips:
                if security_manager._is_localhost(ip):
                    logger.info(f"✅ Localhost detection working for {ip}")
                else:
                    logger.error(f"❌ Localhost detection failed for {ip}")
                    self.test_results['security_manager'] = 'FAIL'
                    return
            
            # Test CSRF token generation
            test_session = "test_session_token"
            csrf_token = security_manager.generate_csrf_token(test_session)
            if csrf_token and len(csrf_token) == 64:
                logger.info("✅ CSRF token generation working")
                self.test_results['security_manager'] = 'PASS'
            else:
                logger.error("❌ CSRF token generation failed")
                self.test_results['security_manager'] = 'FAIL'
                
        except Exception as e:
            logger.error(f"❌ Security manager test failed: {e}")
            self.test_results['security_manager'] = 'FAIL'
    
    def _generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "=" * 70)
        logger.info("SECURITY FIXES TEST RESULTS SUMMARY")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'PASS')
        partial_tests = sum(1 for result in self.test_results.values() if result == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result == 'FAIL')
        skipped_tests = sum(1 for result in self.test_results.values() if result == 'SKIP')
        
        for test_name, result in self.test_results.items():
            status_symbol = {
                'PASS': '✅ [PASS]',
                'PARTIAL': '⚠️ [PARTIAL]',
                'FAIL': '❌ [FAIL]',
                'SKIP': '⏭️ [SKIP]'
            }.get(result, '[UNKNOWN]')
            
            logger.info(f"{status_symbol} {test_name.replace('_', ' ').title()}")
        
        logger.info("\n" + "-" * 70)
        logger.info(f"TOTAL TESTS: {total_tests}")
        logger.info(f"PASSED: {passed_tests}")
        logger.info(f"PARTIAL: {partial_tests}")
        logger.info(f"FAILED: {failed_tests}")
        logger.info(f"SKIPPED: {skipped_tests}")
        
        success_rate = (passed_tests + partial_tests * 0.5) / (total_tests - skipped_tests) * 100 if total_tests > skipped_tests else 0
        logger.info(f"SUCCESS RATE: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("\n🎉 [SUCCESS] All critical security fixes are working!")
        elif success_rate >= 70:
            logger.info("\n⚠️ [WARNING] Most security fixes working, some issues remain")
        else:
            logger.info("\n❌ [ERROR] Critical security issues need attention")
        
        logger.info("=" * 70)

async def main():
    """Main test function."""
    tester = SecurityFixesTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 Security fixes test suite completed!")
    else:
        print("\n❌ Security fixes test suite failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
