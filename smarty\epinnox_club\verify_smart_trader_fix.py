#!/usr/bin/env python3
"""
Verify that the Smart Trader system runs without Bybit KeyError issues.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Configure logging to capture any remaining errors
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('smart_trader_verification.log')
    ]
)
logger = logging.getLogger(__name__)

class SmartTraderVerification:
    """Verification system for Smart Trader Bybit fix."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.error_count = 0
        self.bybit_errors = 0
        self.success_count = 0
        
    def setup_error_monitoring(self):
        """Set up error monitoring to catch any remaining issues."""
        
        # Custom log handler to count errors
        class ErrorCountHandler(logging.Handler):
            def __init__(self, verification_instance):
                super().__init__()
                self.verification = verification_instance
                
            def emit(self, record):
                if record.levelno >= logging.ERROR:
                    self.verification.error_count += 1
                    
                    # Check for specific Bybit errors
                    if 'bid1Price' in record.getMessage() or 'KeyError' in record.getMessage():
                        self.verification.bybit_errors += 1
                        logger.critical(f"🚨 BYBIT ERROR DETECTED: {record.getMessage()}")
        
        # Add error counter to root logger
        error_handler = ErrorCountHandler(self)
        logging.getLogger().addHandler(error_handler)
        
    async def simulate_market_data_processing(self):
        """Simulate market data processing to test the fix."""
        print("🧪 SIMULATING SMART TRADER MARKET DATA PROCESSING")
        print("=" * 60)
        
        try:
            # Import the fixed market data manager
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from market_data.advanced_market_data_manager import AdvancedMarketDataManager
            
            # Initialize with test configuration
            config = {
                'symbols': ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
                'update_interval': 1.0,
                'max_trade_history': 100
            }
            
            print("🔄 Initializing Advanced Market Data Manager...")
            manager = AdvancedMarketDataManager(config)
            
            # Simulate various Bybit data scenarios
            test_scenarios = [
                {
                    'name': 'Spot Market Data (No bid1Price)',
                    'data': {
                        "topic": "tickers.BTCUSDT",
                        "type": "snapshot",
                        "data": {
                            "symbol": "BTCUSDT",
                            "lastPrice": "45000.50",
                            "highPrice24h": "46000.00",
                            "lowPrice24h": "44000.00",
                            "volume24h": "1234.567",
                            "price24hPcnt": "0.025"
                        }
                    }
                },
                {
                    'name': 'Derivatives Data (With bid1Price)',
                    'data': {
                        "topic": "tickers.ETHUSDT",
                        "type": "snapshot",
                        "data": {
                            "symbol": "ETHUSDT",
                            "lastPrice": "3200.75",
                            "bid1Price": "3200.50",
                            "ask1Price": "3201.00",
                            "volume24h": "5678.901",
                            "price24hPcnt": "0.015"
                        }
                    }
                },
                {
                    'name': 'Malformed Data (Missing Fields)',
                    'data': {
                        "topic": "tickers.ADAUSDT",
                        "type": "snapshot",
                        "data": {
                            "symbol": "ADAUSDT",
                            "volume24h": "9876.543"
                            # Missing lastPrice - should be handled gracefully
                        }
                    }
                },
                {
                    'name': 'Empty Data',
                    'data': {}
                },
                {
                    'name': 'Invalid JSON Structure',
                    'data': {
                        "topic": "invalid",
                        "data": None
                    }
                }
            ]
            
            print(f"🎯 Running {len(test_scenarios)} test scenarios...")
            
            for i, scenario in enumerate(test_scenarios, 1):
                print(f"\n{i}️⃣ Testing: {scenario['name']}")
                
                try:
                    await manager._process_bybit_data(scenario['data'])
                    print(f"   ✅ Processed successfully")
                    self.success_count += 1
                    
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    # This is expected for some test cases
                    
                # Small delay between tests
                await asyncio.sleep(0.1)
            
            print(f"\n✅ Market data simulation completed")
            print(f"📊 Successful processes: {self.success_count}")
            
        except ImportError as e:
            print(f"❌ Could not import market data manager: {e}")
            return False
        except Exception as e:
            print(f"❌ Simulation failed: {e}")
            return False
            
        return True
    
    async def run_verification(self, duration_seconds=30):
        """Run verification for a specified duration."""
        print("🔍 SMART TRADER BYBIT FIX VERIFICATION")
        print("=" * 60)
        print(f"⏱️ Running verification for {duration_seconds} seconds...")
        print(f"🕐 Start time: {self.start_time.strftime('%H:%M:%S')}")
        
        # Set up error monitoring
        self.setup_error_monitoring()
        
        # Run market data simulation
        simulation_success = await self.simulate_market_data_processing()
        
        # Wait for the specified duration to monitor for any errors
        print(f"\n⏳ Monitoring for errors for {duration_seconds} seconds...")
        
        for i in range(duration_seconds):
            await asyncio.sleep(1)
            
            # Print progress every 10 seconds
            if (i + 1) % 10 == 0:
                elapsed = i + 1
                remaining = duration_seconds - elapsed
                print(f"   ⏱️ {elapsed}s elapsed, {remaining}s remaining... (Errors: {self.error_count}, Bybit: {self.bybit_errors})")
        
        # Final results
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print(f"\n📊 VERIFICATION RESULTS")
        print("=" * 60)
        print(f"⏱️ Total duration: {duration.total_seconds():.1f} seconds")
        print(f"✅ Simulation success: {simulation_success}")
        print(f"📈 Successful operations: {self.success_count}")
        print(f"❌ Total errors detected: {self.error_count}")
        print(f"🚨 Bybit-specific errors: {self.bybit_errors}")
        
        # Determine success
        if self.bybit_errors == 0:
            print(f"\n🎉 SUCCESS! No Bybit 'bid1Price' errors detected!")
            print(f"✅ The fix is working correctly")
            success = True
        else:
            print(f"\n⚠️ WARNING! {self.bybit_errors} Bybit errors still detected")
            print(f"❌ The fix may need additional work")
            success = False
            
        if self.error_count > self.bybit_errors:
            other_errors = self.error_count - self.bybit_errors
            print(f"ℹ️ Note: {other_errors} other (non-Bybit) errors detected")
        
        return success

async def main():
    """Main verification function."""
    print("🛠️ SMART TRADER BYBIT FIX VERIFICATION TOOL")
    print("=" * 60)
    print("This tool verifies that the Bybit 'bid1Price' KeyError fix is working")
    print()
    
    # Get verification duration from user
    try:
        duration_input = input("⏱️ Enter verification duration in seconds (default: 30): ").strip()
        duration = int(duration_input) if duration_input else 30
        
        if duration < 5:
            print("⚠️ Minimum duration is 5 seconds")
            duration = 5
        elif duration > 300:
            print("⚠️ Maximum duration is 300 seconds (5 minutes)")
            duration = 300
            
    except ValueError:
        print("⚠️ Invalid input, using default duration of 30 seconds")
        duration = 30
    
    # Run verification
    verifier = SmartTraderVerification()
    success = await verifier.run_verification(duration)
    
    # Final summary
    print(f"\n🎯 FINAL VERIFICATION RESULT")
    print("=" * 60)
    
    if success:
        print("🎉 BYBIT FIX VERIFICATION PASSED!")
        print("✅ No 'bid1Price' KeyError messages detected")
        print("✅ Smart Trader system is stable")
        print("✅ Market data processing is working correctly")
        print("\n🚀 The Smart Trader system is ready for production use!")
        return 0
    else:
        print("❌ BYBIT FIX VERIFICATION FAILED!")
        print("⚠️ Some Bybit errors are still occurring")
        print("🔧 Additional debugging may be required")
        print("\n📋 Check the log file: smart_trader_verification.log")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        sys.exit(1)
