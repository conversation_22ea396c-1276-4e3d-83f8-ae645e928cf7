"""
Visualization tools for backtesting results.
"""

import os
import json
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)


class BacktestVisualizer:
    """
    Visualization tools for backtesting results.

    This class provides functionality for visualizing backtesting results
    including equity curves, drawdowns, trade distributions, etc.
    """

    def __init__(self, results_dir: str, output_dir: str = "results/plots"):
        """
        Initialize the visualizer.

        Args:
            results_dir: Directory containing backtest results
            output_dir: Directory for storing visualization outputs
        """
        self.results_dir = results_dir
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Data storage
        self.equity_curve = None
        self.trades = None
        self.signals = None
        self.metrics = None

        # Plot style
        plt.style.use('seaborn-v0_8-darkgrid')

    def load_results(self, equity_file: str, trades_file: str, signals_file: str, metrics_file: str):
        """
        Load backtest results from files.

        Args:
            equity_file: Path to equity curve CSV file
            trades_file: Path to trades JSON file
            signals_file: Path to signals JSON file
            metrics_file: Path to metrics JSON file
        """
        try:
            # Load equity curve
            self.equity_curve = pd.read_csv(equity_file)
            self.equity_curve['timestamp'] = pd.to_datetime(self.equity_curve['timestamp'])

            # Load trades
            with open(trades_file, 'r') as f:
                self.trades = json.load(f)

            # Load signals
            with open(signals_file, 'r') as f:
                self.signals = json.load(f)

            # Load metrics
            with open(metrics_file, 'r') as f:
                self.metrics = json.load(f)

            logger.info(f"Loaded backtest results: {len(self.equity_curve)} equity points, {len(self.trades)} trades")

            return True

        except Exception as e:
            logger.error(f"Error loading backtest results: {e}")
            return False

    def plot_equity_curve(self, save_path: Optional[str] = None):
        """
        Plot equity curve.

        Args:
            save_path: Path to save the plot (if None, plot is displayed)
        """
        if self.equity_curve is None:
            logger.error("No equity curve data loaded")
            return

        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot equity curve
        ax.plot(self.equity_curve['timestamp'], self.equity_curve['equity'], label='Equity')

        # Format x-axis as dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())

        # Add labels and title
        ax.set_xlabel('Date')
        ax.set_ylabel('Equity ($)')
        ax.set_title('Equity Curve')

        # Add metrics as text
        if self.metrics:
            metrics_text = (
                f"Total Return: {self.metrics['total_return']:.2%}\n"
                f"Annual Return: {self.metrics['annual_return']:.2%}\n"
                f"Sharpe Ratio: {self.metrics['sharpe_ratio']:.2f}\n"
                f"Max Drawdown: {self.metrics['max_drawdown']:.2%}\n"
                f"Win Rate: {self.metrics['win_rate']:.2%}\n"
                f"Total Trades: {self.metrics['total_trades']}\n"
                f"Initial Balance: ${self.metrics['initial_balance']:.2f}\n"
                f"Final Balance: ${self.metrics['final_balance']:.2f}"
            )

            # Add text box with metrics
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax.text(0.05, 0.95, metrics_text, transform=ax.transAxes, fontsize=10,
                    verticalalignment='top', bbox=props)

        # Rotate x-axis labels
        plt.xticks(rotation=45)

        # Adjust layout
        plt.tight_layout()

        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Equity curve plot saved to {save_path}")
        else:
            plt.show()

    def plot_drawdown(self, save_path: Optional[str] = None):
        """
        Plot drawdown.

        Args:
            save_path: Path to save the plot (if None, plot is displayed)
        """
        if self.equity_curve is None:
            logger.error("No equity curve data loaded")
            return

        # Calculate drawdown
        equity = self.equity_curve.copy()
        equity['cummax'] = equity['equity'].cummax()
        equity['drawdown'] = (equity['equity'] / equity['cummax']) - 1

        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot drawdown
        ax.fill_between(equity['timestamp'], 0, equity['drawdown'] * 100, color='red', alpha=0.3)
        ax.plot(equity['timestamp'], equity['drawdown'] * 100, color='red', label='Drawdown')

        # Format x-axis as dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())

        # Add labels and title
        ax.set_xlabel('Date')
        ax.set_ylabel('Drawdown (%)')
        ax.set_title('Drawdown')

        # Add max drawdown line
        if self.metrics:
            max_dd = self.metrics['max_drawdown'] * 100
            ax.axhline(y=max_dd, color='black', linestyle='--',
                      label=f'Max Drawdown: {max_dd:.2f}%')
            ax.legend()

        # Rotate x-axis labels
        plt.xticks(rotation=45)

        # Adjust layout
        plt.tight_layout()

        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Drawdown plot saved to {save_path}")
        else:
            plt.show()

    def plot_trade_distribution(self, save_path: Optional[str] = None):
        """
        Plot trade distribution.

        Args:
            save_path: Path to save the plot (if None, plot is displayed)
        """
        if not self.trades:
            logger.error("No trade data loaded")
            return

        # Convert trades to DataFrame
        trades_df = pd.DataFrame(self.trades)

        # Convert timestamp to datetime
        trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])

        # Count trades by symbol and side
        trade_counts = trades_df.groupby(['symbol', 'side']).size().unstack(fill_value=0)

        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot trade distribution
        trade_counts.plot(kind='bar', ax=ax)

        # Add labels and title
        ax.set_xlabel('Symbol')
        ax.set_ylabel('Number of Trades')
        ax.set_title('Trade Distribution by Symbol and Side')

        # Adjust layout
        plt.tight_layout()

        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Trade distribution plot saved to {save_path}")
        else:
            plt.show()

    def plot_monthly_returns(self, save_path: Optional[str] = None):
        """
        Plot monthly returns.

        Args:
            save_path: Path to save the plot (if None, plot is displayed)
        """
        if self.equity_curve is None or len(self.equity_curve) < 2:
            logger.error("Insufficient equity curve data for monthly returns")
            # Create an empty plot
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.set_title("Monthly Returns (No Data)")
            if save_path:
                plt.savefig(save_path)
                logger.info(f"Empty monthly returns plot saved to {save_path}")
            else:
                plt.show()
            return

        # Calculate daily returns
        equity = self.equity_curve.copy()
        equity = equity.set_index('timestamp')
        equity['daily_return'] = equity['equity'].pct_change()

        # Check if we have enough data for monthly resampling
        if equity.index.min() == equity.index.max():
            logger.warning("Not enough data for monthly returns")
            # Create an empty plot
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.set_title("Monthly Returns (Insufficient Data)")
            if save_path:
                plt.savefig(save_path)
                logger.info(f"Empty monthly returns plot saved to {save_path}")
            else:
                plt.show()
            return

        # Resample to monthly returns
        try:
            monthly_returns = equity['daily_return'].resample('ME').apply(
                lambda x: (1 + x).prod() - 1
            )
        except Exception as e:
            logger.error(f"Error calculating monthly returns: {e}")
            # Create an empty plot
            fig, ax = plt.subplots(figsize=(12, 6))
            ax.set_title("Monthly Returns (Error)")
            if save_path:
                plt.savefig(save_path)
                logger.info(f"Empty monthly returns plot saved to {save_path}")
            else:
                plt.show()
            return

        fig, ax = plt.subplots(figsize=(12, 6))

        # Plot monthly returns
        monthly_returns.plot(kind='bar', ax=ax)

        # Add labels and title
        ax.set_xlabel('Month')
        ax.set_ylabel('Return (%)')
        ax.set_title('Monthly Returns')

        # Format y-axis as percentage
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.1%}'))

        # Rotate x-axis labels
        plt.xticks(rotation=45)

        # Adjust layout
        plt.tight_layout()

        # Save or show plot
        if save_path:
            plt.savefig(save_path)
            logger.info(f"Monthly returns plot saved to {save_path}")
        else:
            plt.show()

    def generate_report(self, report_dir: Optional[str] = None):
        """
        Generate a comprehensive report with all plots.

        Args:
            report_dir: Directory to save the report (if None, uses output_dir)
        """
        if report_dir is None:
            report_dir = self.output_dir

        os.makedirs(report_dir, exist_ok=True)

        # Generate timestamp for report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate plots
        self.plot_equity_curve(os.path.join(report_dir, f"equity_curve_{timestamp}.png"))
        self.plot_drawdown(os.path.join(report_dir, f"drawdown_{timestamp}.png"))
        self.plot_trade_distribution(os.path.join(report_dir, f"trade_distribution_{timestamp}.png"))
        self.plot_monthly_returns(os.path.join(report_dir, f"monthly_returns_{timestamp}.png"))

        logger.info(f"Report generated in {report_dir}")

        return report_dir
