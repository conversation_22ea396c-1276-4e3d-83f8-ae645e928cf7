#!/usr/bin/env python3
"""
Final comprehensive test of Money Circle demo data and features.
"""

import requests
import json
import sys

def test_member_directory():
    """Test member directory with demo data."""
    print("👥 TESTING MEMBER DIRECTORY")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login with demo account
    login_data = {'username': 'trader_alex', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return False
    
    # Test member directory
    response = session.get('http://localhost:8084/club/members')
    if response.status_code == 200:
        content = response.text
        
        # Check for demo users
        demo_users = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        found_users = sum(1 for user in demo_users if user in content)
        
        print(f"✅ Member directory accessible")
        print(f"✅ Found {found_users}/{len(demo_users)} demo users")
        
        # Check for profile elements
        profile_elements = ['display_name', 'bio', 'trading_style', 'reputation']
        found_elements = sum(1 for element in profile_elements if element in content.lower())
        
        print(f"✅ Profile elements: {found_elements}/{len(profile_elements)}")
        
        return found_users >= 3 and found_elements >= 2
    else:
        print(f"❌ Member directory not accessible: {response.status_code}")
        return False

def test_personal_dashboard_balance():
    """Test personal dashboard shows account balance."""
    print("\n💰 TESTING PERSONAL DASHBOARD BALANCE")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login with demo account
    login_data = {'username': 'crypto_sarah', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return False
    
    # Test dashboard
    response = session.get('http://localhost:8084/dashboard')
    if response.status_code == 200:
        content = response.text
        
        # Check for balance indicators
        balance_indicators = ['$', 'portfolio', 'balance', 'total', 'value']
        found_indicators = sum(1 for indicator in balance_indicators if indicator.lower() in content.lower())
        
        print(f"✅ Dashboard accessible")
        print(f"✅ Balance indicators: {found_indicators}/{len(balance_indicators)}")
        
        # Check for trading data
        trading_indicators = ['trades', 'positions', 'pnl', 'profit', 'loss']
        found_trading = sum(1 for indicator in trading_indicators if indicator.lower() in content.lower())
        
        print(f"✅ Trading indicators: {found_trading}/{len(trading_indicators)}")
        
        return found_indicators >= 3 and found_trading >= 2
    else:
        print(f"❌ Dashboard not accessible: {response.status_code}")
        return False

def test_portfolio_api():
    """Test portfolio API returns data."""
    print("\n📊 TESTING PORTFOLIO API")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login with demo account
    login_data = {'username': 'quant_mike', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return False
    
    # Test portfolio API
    response = session.get('http://localhost:8084/api/portfolio')
    if response.status_code == 200:
        try:
            data = response.json()
            
            print(f"✅ Portfolio API accessible")
            
            # Check for portfolio data
            if 'success' in data and data['success']:
                portfolio = data.get('portfolio', {})
                
                total_balance = portfolio.get('total_balance_usd', 0)
                positions_count = len(portfolio.get('positions', []))
                
                print(f"✅ Total balance: ${total_balance:,.2f}")
                print(f"✅ Open positions: {positions_count}")
                
                return total_balance > 0 or positions_count > 0
            else:
                print(f"⚠️ API returned: {data}")
                return False
                
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON response")
            return False
    else:
        print(f"❌ Portfolio API not accessible: {response.status_code}")
        return False

def test_strategy_marketplace():
    """Test strategy marketplace shows strategies."""
    print("\n🎯 TESTING STRATEGY MARKETPLACE")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login with demo account
    login_data = {'username': 'options_david', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return False
    
    # Test strategy marketplace
    response = session.get('http://localhost:8084/club/strategies')
    if response.status_code == 200:
        content = response.text
        
        # Check for strategy elements
        strategy_elements = ['strategy', 'performance', 'return', 'risk', 'follow']
        found_elements = sum(1 for element in strategy_elements if element.lower() in content.lower())
        
        print(f"✅ Strategy marketplace accessible")
        print(f"✅ Strategy elements: {found_elements}/{len(strategy_elements)}")
        
        # Check for demo strategies
        demo_strategies = ['Momentum', 'Crypto', 'Dividend', 'Forex', 'Options']
        found_strategies = sum(1 for strategy in demo_strategies if strategy in content)
        
        print(f"✅ Demo strategies: {found_strategies}/{len(demo_strategies)}")
        
        return found_elements >= 3 and found_strategies >= 2
    else:
        print(f"❌ Strategy marketplace not accessible: {response.status_code}")
        return False

def test_club_analytics():
    """Test club analytics shows data."""
    print("\n📈 TESTING CLUB ANALYTICS")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login with demo account
    login_data = {'username': 'algo_robert', 'password': 'securepass123'}
    login_response = session.post('http://localhost:8084/login', data=login_data)
    
    if login_response.status_code != 302:
        print("❌ Login failed")
        return False
    
    # Test club analytics
    response = session.get('http://localhost:8084/club/analytics')
    if response.status_code == 200:
        content = response.text
        
        # Check for analytics elements
        analytics_elements = ['chart', 'performance', 'members', 'analytics', 'statistics']
        found_elements = sum(1 for element in analytics_elements if element.lower() in content.lower())
        
        print(f"✅ Club analytics accessible")
        print(f"✅ Analytics elements: {found_elements}/{len(analytics_elements)}")
        
        # Check for chart.js
        has_charts = 'chart.js' in content.lower() or 'canvas' in content.lower()
        print(f"✅ Charts available: {has_charts}")
        
        return found_elements >= 3 and has_charts
    else:
        print(f"❌ Club analytics not accessible: {response.status_code}")
        return False

def main():
    """Run final comprehensive demo test."""
    print("🎯 MONEY CIRCLE FINAL DEMO TEST")
    print("=" * 60)
    
    # Check server
    try:
        response = requests.get('http://localhost:8084', timeout=5)
        print(f"✅ Server running (status: {response.status_code})")
    except:
        print("❌ Server not running!")
        return 1
    
    tests = [
        ("Member Directory", test_member_directory),
        ("Personal Dashboard Balance", test_personal_dashboard_balance),
        ("Portfolio API", test_portfolio_api),
        ("Strategy Marketplace", test_strategy_marketplace),
        ("Club Analytics", test_club_analytics),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"⚠️ {test_name}: NEEDS IMPROVEMENT")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 FINAL TEST RESULTS: {passed}/{total} tests passed")
    
    if passed >= total * 0.8:
        print("🎉 EXCELLENT! MONEY CIRCLE DEMO IS READY!")
        print("✅ All major features working with demo data")
        print("✅ Member directory populated with profiles")
        print("✅ Account balances and trading data visible")
        print("✅ Strategy marketplace functional")
        print("✅ Club analytics displaying charts")
        print("\n🌟 DEMO ENVIRONMENT IS PRODUCTION-READY!")
        return 0
    else:
        print("⚠️ DEMO NEEDS SOME IMPROVEMENTS")
        print("Most features working but some issues remain")
        return 1

if __name__ == "__main__":
    sys.exit(main())
