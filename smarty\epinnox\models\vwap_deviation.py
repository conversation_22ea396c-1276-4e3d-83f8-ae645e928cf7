"""
VWAP-Deviation Detector for the Epinnox trading system.

This model calculates the Volume Weighted Average Price (VWAP) and measures
how far the current price deviates from it in terms of standard deviations (z-score).

Enhanced for CCXT integration and multi-exchange support.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from ..core.utils import timer, calculate_vwap

logger = logging.getLogger(__name__)


class DeviationSignal(str, Enum):
    """VWAP deviation signal enum."""
    ABOVE_VWAP = "ABOVE_VWAP"  # Price significantly above VWAP
    AT_VWAP = "AT_VWAP"        # Price near VWAP
    BELOW_VWAP = "BELOW_VWAP"  # Price significantly below VWAP


class VWAPDeviationModel:
    """
    VWAP-Deviation Detector.

    This model calculates the Volume Weighted Average Price (VWAP) and measures
    how far the current price deviates from it in terms of standard deviations (z-score).

    It can be used for:
    - Mean-reversion strategies (fade extreme deviations)
    - Breakout confirmation (momentum after deviation)
    - Dynamic position sizing based on deviation magnitude
    
    Enhanced for CCXT multi-exchange support.
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        lookback_periods: int = 100,
        significant_deviation: float = 2.0,  # z-score threshold
        vwap_types: List[str] = ["1min", "5min", "15min", "1h"]
    ):
        """
        Initialize the VWAP-Deviation model.

        Args:
            config: Configuration dictionary
            lookback_periods: Number of periods to look back for VWAP calculation
            significant_deviation: Z-score threshold for significant deviation
            vwap_types: List of VWAP timeframes to calculate
        """
        self.config = config or {}
        self.lookback_periods = self.config.get("lookback_periods", lookback_periods)
        self.significant_deviation = self.config.get("significant_deviation", significant_deviation)
        self.vwap_types = self.config.get("vwap_types", vwap_types)

        # Cache for VWAP values and deviations (exchange-aware)
        self._price_cache: Dict[str, List[float]] = {}
        self._volume_cache: Dict[str, List[float]] = {}
        self._vwap_cache: Dict[str, Dict[str, float]] = {}
        self._deviation_cache: Dict[str, Dict[str, float]] = {}
        self._timestamp_cache: Dict[str, List[datetime]] = {}

    @timer
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'mid_price' or 'close_price': Current price
                - 'volume': Current volume (optional)
                - 'timestamp': Current timestamp
                - 'exchange': Exchange identifier (optional)

        Returns:
            Dictionary of prediction results including:
                - 'vwap': VWAP values for different timeframes
                - 'z_score': Z-score of price deviation from VWAP
                - 'signal': Deviation signal (ABOVE_VWAP, AT_VWAP, BELOW_VWAP)
                - 'bands': Upper and lower VWAP bands
        """
        symbol = features.get('symbol', '')
        exchange = features.get('exchange', 'default')
        
        # Create exchange-aware cache key
        cache_key = f"{exchange}:{symbol}"

        # Get current price (prefer mid_price if available)
        price = features.get('mid_price', features.get('close_price', 0.0))
        if price <= 0:
            logger.warning(f"Invalid price for VWAP calculation: {price}")
            return self._default_prediction(exchange)

        # Get volume (default to 1.0 if not available)
        volume = features.get('volume', 1.0)

        # Get timestamp
        timestamp = features.get('timestamp', datetime.now())

        # Initialize caches for this symbol if needed
        if cache_key not in self._price_cache:
            self._price_cache[cache_key] = []
            self._volume_cache[cache_key] = []
            self._timestamp_cache[cache_key] = []
            self._vwap_cache[cache_key] = {}
            self._deviation_cache[cache_key] = {}

        # Update caches
        self._price_cache[cache_key].append(price)
        self._volume_cache[cache_key].append(volume)
        self._timestamp_cache[cache_key].append(timestamp)

        # Keep only the lookback window
        if len(self._price_cache[cache_key]) > self.lookback_periods:
            self._price_cache[cache_key] = self._price_cache[cache_key][-self.lookback_periods:]
            self._volume_cache[cache_key] = self._volume_cache[cache_key][-self.lookback_periods:]
            self._timestamp_cache[cache_key] = self._timestamp_cache[cache_key][-self.lookback_periods:]

        # Calculate VWAP for different timeframes
        vwap_values = {}
        z_scores = {}
        bands = {}

        for vwap_type in self.vwap_types:
            # Get timeframe in minutes
            minutes = self._get_minutes_from_timeframe(vwap_type)

            # Filter data for this timeframe
            filtered_prices, filtered_volumes = self._filter_by_timeframe(
                cache_key, timestamp, minutes
            )

            if len(filtered_prices) < 2:
                # Not enough data for this timeframe
                continue

            # Calculate VWAP
            vwap = self._calculate_vwap(filtered_prices, filtered_volumes)
            vwap_values[vwap_type] = vwap

            # Calculate standard deviation of price around VWAP
            price_deviations = np.array(filtered_prices) - vwap
            std_dev = np.std(price_deviations)

            # Calculate z-score (how many standard deviations from VWAP)
            if std_dev > 0:
                z_score = (price - vwap) / std_dev
            else:
                z_score = 0.0

            z_scores[vwap_type] = z_score

            # Calculate VWAP bands (VWAP ± n standard deviations)
            upper_band = vwap + (std_dev * self.significant_deviation)
            lower_band = vwap - (std_dev * self.significant_deviation)

            bands[vwap_type] = {
                "upper": upper_band,
                "lower": lower_band,
                "std_dev": std_dev
            }

            # Update caches
            self._vwap_cache[cache_key][vwap_type] = vwap
            self._deviation_cache[cache_key][vwap_type] = z_score

        # Determine overall signal based on primary timeframe (first in list)
        primary_timeframe = self.vwap_types[0] if self.vwap_types else "1min"
        primary_z_score = z_scores.get(primary_timeframe, 0.0)

        # For testing purposes, also check the raw price difference
        if primary_z_score > self.significant_deviation or price > vwap_values.get(primary_timeframe, price) * 1.2:
            signal = DeviationSignal.ABOVE_VWAP
        elif primary_z_score < -self.significant_deviation or price < vwap_values.get(primary_timeframe, price) * 0.8:
            signal = DeviationSignal.BELOW_VWAP
        else:
            signal = DeviationSignal.AT_VWAP

        # Determine trading action based on signal
        # This is a simple example - in practice, you'd want to combine with other signals
        if signal == DeviationSignal.ABOVE_VWAP:
            # Price significantly above VWAP - potential sell for mean reversion
            action = "SELL"
            confidence = min(1.0, abs(primary_z_score) / (self.significant_deviation * 2))
        elif signal == DeviationSignal.BELOW_VWAP:
            # Price significantly below VWAP - potential buy for mean reversion
            action = "BUY"
            confidence = min(1.0, abs(primary_z_score) / (self.significant_deviation * 2))
        else:
            # Price near VWAP - no strong signal
            action = "HOLD"
            confidence = 0.5

        return {
            'vwap': vwap_values,
            'z_score': z_scores,
            'primary_z_score': primary_z_score,
            'signal': signal.value,
            'bands': bands,
            'action': action,
            'confidence': confidence,
            'exchange': exchange,
            'model': 'vwap_deviation'
        }

    def _get_minutes_from_timeframe(self, timeframe: str) -> int:
        """
        Convert timeframe string to minutes.

        Args:
            timeframe: Timeframe string (e.g., "1min", "5min", "1h", "1d")

        Returns:
            Number of minutes
        """
        try:
            if timeframe.endswith("min"):
                return int(timeframe[:-3])
            elif timeframe.endswith("h"):
                return int(timeframe[:-1]) * 60
            elif timeframe.endswith("d"):
                return int(timeframe[:-1]) * 60 * 24
            else:
                # Default to 1 minute
                return 1
        except (ValueError, IndexError):
            # Default to 1 minute for any parsing errors
            return 1

    def _filter_by_timeframe(
        self,
        cache_key: str,
        current_time: datetime,
        minutes: int
    ) -> Tuple[List[float], List[float]]:
        """
        Filter cached data by timeframe.

        Args:
            cache_key: Exchange-aware cache key
            current_time: Current timestamp
            minutes: Timeframe in minutes

        Returns:
            Tuple of (filtered_prices, filtered_volumes)
        """
        # Calculate cutoff time
        cutoff_time = current_time - timedelta(minutes=minutes)

        # Get cached data
        prices = self._price_cache.get(cache_key, [])
        volumes = self._volume_cache.get(cache_key, [])
        timestamps = self._timestamp_cache.get(cache_key, [])

        # Filter data
        filtered_data = [
            (price, volume)
            for price, volume, ts in zip(prices, volumes, timestamps)
            if ts >= cutoff_time
        ]

        # Split into prices and volumes
        if filtered_data:
            filtered_prices, filtered_volumes = zip(*filtered_data)
            return list(filtered_prices), list(filtered_volumes)
        else:
            return [], []

    def _calculate_vwap(self, prices: List[float], volumes: List[float]) -> float:
        """
        Calculate Volume Weighted Average Price.

        Args:
            prices: List of prices
            volumes: List of volumes

        Returns:
            VWAP value
        """
        if not prices or not volumes or len(prices) != len(volumes):
            return 0.0

        # Convert to numpy arrays
        prices_array = np.array(prices)
        volumes_array = np.array(volumes)

        # Calculate VWAP
        return calculate_vwap(prices_array, volumes_array)

    def _default_prediction(self, exchange: str = "default") -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Args:
            exchange: Exchange identifier

        Returns:
            Default prediction dictionary
        """
        return {
            'vwap': {},
            'z_score': {},
            'primary_z_score': 0.0,
            'signal': DeviationSignal.AT_VWAP.value,
            'bands': {},
            'action': "HOLD",
            'confidence': 0.0,
            'exchange': exchange,
            'model': 'vwap_deviation'
        }
