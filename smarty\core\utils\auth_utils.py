"""
Authentication utility functions for the smart-trader system.
"""

import base64
import hashlib
import hmac
import secrets
import time
from typing import Dict, Any, Optional
from urllib.parse import urlencode


def generate_signature(secret: str, message: str, algorithm: str = 'sha256') -> str:
    """
    Generate HMAC signature for API authentication with improved security.

    Args:
        secret: API secret key
        message: Message to sign
        algorithm: Hash algorithm to use

    Returns:
        Hex-encoded signature
        
    Raises:
        ValueError: If algorithm is not supported
    """
    if not secret or not message:
        raise ValueError("Secret and message cannot be empty")
    
    # Get hash function
    hash_func = getattr(hashlib, algorithm, None)
    if hash_func is None:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    return hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hash_func
    ).hexdigest()


def generate_signature_base64(secret: str, message: str, algorithm: str = 'sha256') -> str:
    """
    Generate HMAC signature in base64 format.

    Args:
        secret: API secret key
        message: Message to sign
        algorithm: Hash algorithm to use

    Returns:
        Base64-encoded signature
    """
    if not secret or not message:
        raise ValueError("Secret and message cannot be empty")
    
    hash_func = getattr(hashlib, algorithm, None)
    if hash_func is None:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    signature = hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hash_func
    ).digest()
    
    return base64.b64encode(signature).decode('utf-8')


def create_api_signature(
    method: str,
    url: str,
    params: Dict[str, Any],
    secret: str,
    timestamp: Optional[int] = None
) -> Dict[str, str]:
    """
    Create API signature with standardized format.
    
    Args:
        method: HTTP method (GET, POST, etc.)
        url: API endpoint URL
        params: Request parameters
        secret: API secret key
        timestamp: Optional timestamp (uses current time if None)
        
    Returns:
        Dictionary with signature and timestamp
    """
    if timestamp is None:
        timestamp = int(time.time())
    
    # Add timestamp to parameters
    params_with_timestamp = params.copy()
    params_with_timestamp['timestamp'] = timestamp
    
    # Sort parameters and create query string
    sorted_params = sorted(params_with_timestamp.items())
    query_string = urlencode(sorted_params)
    
    # Create message to sign
    message = f"{method}\n{url}\n{query_string}"
    
    # Generate signature
    signature = generate_signature(secret, message)
    
    return {
        'signature': signature,
        'timestamp': str(timestamp)
    }


def validate_signature(
    received_signature: str,
    expected_message: str,
    secret: str,
    algorithm: str = 'sha256'
) -> bool:
    """
    Validate HMAC signature for security.
    
    Args:
        received_signature: Signature to validate
        expected_message: Message that should have been signed
        secret: Secret key used for signing
        algorithm: Hash algorithm used
        
    Returns:
        True if signature is valid, False otherwise
    """
    try:
        expected_signature = generate_signature(secret, expected_message, algorithm)
        return hmac.compare_digest(received_signature, expected_signature)
    except Exception:
        return False


def generate_nonce(length: int = 16) -> str:
    """
    Generate a cryptographically secure random nonce.
    
    Args:
        length: Length of the nonce in bytes
        
    Returns:
        Hex-encoded nonce
    """
    return secrets.token_hex(length)


def hash_password(password: str, salt: Optional[str] = None) -> Dict[str, str]:
    """
    Hash a password with salt using PBKDF2.
    
    Args:
        password: Password to hash
        salt: Optional salt (generates random if None)
        
    Returns:
        Dictionary with hashed password and salt
    """
    if salt is None:
        salt = secrets.token_hex(32)
    elif isinstance(salt, str):
        salt = salt.encode('utf-8')
    
    # Use PBKDF2 with SHA256
    hashed = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt if isinstance(salt, bytes) else salt.encode('utf-8'),
        100000  # iterations
    )
    
    return {
        'hash': hashed.hex(),
        'salt': salt.hex() if isinstance(salt, bytes) else salt
    }


def verify_password(password: str, stored_hash: str, salt: str) -> bool:
    """
    Verify a password against stored hash.
    
    Args:
        password: Password to verify
        stored_hash: Stored password hash
        salt: Salt used for hashing
        
    Returns:
        True if password is correct, False otherwise
    """
    try:
        result = hash_password(password, salt)
        return hmac.compare_digest(result['hash'], stored_hash)
    except Exception:
        return False


def create_jwt_payload(
    user_id: str,
    expiry_minutes: int = 60,
    additional_claims: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create JWT payload with standard claims.
    
    Args:
        user_id: User identifier
        expiry_minutes: Token expiry in minutes
        additional_claims: Additional claims to include
        
    Returns:
        JWT payload dictionary
    """
    now = int(time.time())
    payload = {
        'sub': user_id,
        'iat': now,
        'exp': now + (expiry_minutes * 60),
        'jti': generate_nonce(8)  # JWT ID
    }
    
    if additional_claims:
        payload.update(additional_claims)
    
    return payload


if __name__ == "__main__":
    # Test authentication utilities
    print("Testing authentication utilities:")
    
    # Test basic signature generation
    secret = "test_secret_key"
    message = "test_message"
    signature = generate_signature(secret, message)
    print(f"HMAC signature: {signature}")
    
    # Test base64 signature
    b64_signature = generate_signature_base64(secret, message)
    print(f"Base64 signature: {b64_signature}")
    
    # Test API signature creation
    api_sig = create_api_signature(
        "GET",
        "/api/v1/test",
        {"param1": "value1", "param2": "value2"},
        secret
    )
    print(f"API signature: {api_sig}")
    
    # Test signature validation
    is_valid = validate_signature(signature, message, secret)
    print(f"Signature validation: {is_valid}")
    
    # Test nonce generation
    nonce = generate_nonce()
    print(f"Generated nonce: {nonce}")
    
    # Test password hashing
    password = "test_password"
    hashed = hash_password(password)
    print(f"Password hash: {hashed['hash'][:20]}...")
    
    # Test password verification
    is_correct = verify_password(password, hashed['hash'], hashed['salt'])
    print(f"Password verification: {is_correct}")
    
    # Test JWT payload
    jwt_payload = create_jwt_payload("user123", 30, {"role": "trader"})
    print(f"JWT payload: {jwt_payload}")
    
    print("Authentication tests completed")
