#!/usr/bin/env python3
"""
Test login functionality for Money Circle
"""

import asyncio
import aiohttp
import json

async def test_login():
    """Test the login functionality."""
    print("[TEST] Testing Money Circle login functionality...")
    
    # Test data
    login_data = {
        'username': 'epinnox',
        'password': 'securepass123'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test login endpoint
            print(f"[TEST] Attempting login with username: {login_data['username']}")
            
            async with session.post(
                'http://localhost:8080/auth/login',
                data=login_data,
                allow_redirects=False
            ) as response:
                print(f"[RESPONSE] Status: {response.status}")
                print(f"[RESPONSE] Headers: {dict(response.headers)}")
                
                if response.status == 302:
                    location = response.headers.get('Location', '')
                    print(f"[SUCCESS] Login successful! Redirecting to: {location}")
                    return True
                elif response.status == 200:
                    text = await response.text()
                    if 'Invalid username or password' in text:
                        print("[ERROR] Invalid credentials")
                    else:
                        print("[INFO] Login page returned (may need to check form)")
                    return False
                else:
                    text = await response.text()
                    print(f"[ERROR] Unexpected response: {text[:200]}...")
                    return False
                    
    except Exception as e:
        print(f"[ERROR] Login test failed: {e}")
        return False

async def test_user_exists():
    """Test if the epinnox user exists in the database."""
    print("\n[TEST] Checking if epinnox user exists...")
    
    try:
        from database.models import DatabaseManager
        from auth.user_manager import UserManager
        
        # Initialize database and user manager
        db = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db)
        
        # Check if user exists
        user = user_manager.get_user_by_username('epinnox')
        if user:
            print(f"[OK] User found: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Agreement accepted: {user.agreement_accepted}")
            return True
        else:
            print("[ERROR] User 'epinnox' not found!")
            return False
            
    except Exception as e:
        print(f"[ERROR] Database check failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Money Circle login tests...\n")
    
    # Test if user exists
    user_exists = await test_user_exists()
    
    if user_exists:
        # Test login functionality
        login_success = await test_login()
        
        if login_success:
            print("\n✅ All tests passed! Login is working.")
        else:
            print("\n❌ Login test failed.")
    else:
        print("\n❌ User does not exist. Cannot test login.")

if __name__ == '__main__':
    asyncio.run(main())
