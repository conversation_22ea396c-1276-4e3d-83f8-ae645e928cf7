#!/usr/bin/env python3
"""
Restart Money Circle server with updated HTX configuration.
"""

import subprocess
import time
import requests
import sys
import os
import signal
from pathlib import Path

def find_and_kill_server():
    """Find and kill any existing Money Circle server processes."""
    print("🔍 Looking for existing Money Circle server processes...")
    
    try:
        # Find processes using port 8084
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        pids_to_kill = []
        for line in lines:
            if ':8084' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) > 4:
                    pid = parts[-1]
                    if pid.isdigit():
                        pids_to_kill.append(int(pid))
        
        # Kill the processes
        for pid in pids_to_kill:
            try:
                print(f"🔪 Killing process {pid}...")
                subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                             capture_output=True, check=False)
                print(f"✅ Process {pid} killed")
            except Exception as e:
                print(f"⚠️ Could not kill process {pid}: {e}")
        
        if pids_to_kill:
            print("⏳ Waiting for processes to terminate...")
            time.sleep(3)
        else:
            print("✅ No existing server processes found")
            
    except Exception as e:
        print(f"⚠️ Error checking for existing processes: {e}")

def start_server():
    """Start the Money Circle server."""
    print("🚀 Starting Money Circle server with updated HTX configuration...")
    
    # Change to the correct directory
    os.chdir(Path(__file__).parent)
    
    # Start the server
    try:
        process = subprocess.Popen(
            [sys.executable, 'epinnox_club/start_money_circle.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"📍 Server process started with PID: {process.pid}")
        
        # Wait a bit for server to start
        print("⏳ Waiting for server to initialize...")
        time.sleep(5)
        
        # Check if server is responding
        for attempt in range(10):
            try:
                response = requests.get('http://localhost:8084/health', timeout=2)
                if response.status_code == 200:
                    print("✅ Server is responding to health checks")
                    return process
            except:
                pass
            
            print(f"⏳ Attempt {attempt + 1}/10: Waiting for server to respond...")
            time.sleep(2)
        
        print("⚠️ Server may not be fully ready, but process is running")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def test_htx_integration():
    """Test HTX integration with the new server."""
    print("🧪 Testing HTX integration...")
    
    try:
        # Test login
        session = requests.Session()
        login_response = session.post(
            'http://localhost:8084/login',
            data={'username': 'epinnox', 'password': 'securepass123'},
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            allow_redirects=False
        )
        
        if login_response.status_code not in [200, 302]:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("✅ Login successful")
        
        # Test HTX account addition
        htx_data = {
            'exchange': 'huobi',
            'api_key': 'test_key_for_validation',
            'secret_key': 'test_secret_for_validation'
        }
        
        response = session.post(
            'http://localhost:8084/api/exchange/add',
            json=htx_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📡 HTX test response: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        # Even if credentials are invalid, we should get a proper validation error
        # not an "unsupported exchange" error
        if response.status_code == 400:
            response_data = response.json()
            error_msg = response_data.get('error', '').lower()
            
            if 'unsupported exchange' in error_msg:
                print("❌ HTX still not supported - server needs restart")
                return False
            elif 'credential' in error_msg or 'invalid' in error_msg:
                print("✅ HTX is supported - credential validation working")
                return True
            else:
                print(f"⚠️ Unexpected error: {error_msg}")
                return False
        elif response.status_code == 200:
            print("✅ HTX account added successfully")
            return True
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    print("🔄 Money Circle Server Restart with HTX Integration")
    print("=" * 60)
    
    # Step 1: Kill existing server
    find_and_kill_server()
    
    # Step 2: Start new server
    process = start_server()
    if not process:
        print("❌ Failed to start server")
        return 1
    
    try:
        # Step 3: Test HTX integration
        if test_htx_integration():
            print("\n" + "=" * 60)
            print("🎉 SUCCESS: Money Circle server restarted with HTX support!")
            print("✅ HTX exchange integration is now working")
            print("🌐 Server running at: http://localhost:8084")
            print("🔐 Login: epinnox / securepass123")
            print("\n🎯 You can now add your HTX account via the dashboard!")
            
            # Keep server running
            print("\n⏳ Server is running. Press Ctrl+C to stop...")
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping server...")
                process.terminate()
                process.wait()
            
            return 0
        else:
            print("\n❌ HTX integration test failed")
            print("🛑 Stopping server...")
            process.terminate()
            process.wait()
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")
        process.terminate()
        process.wait()
        return 0

if __name__ == "__main__":
    sys.exit(main())
