#!/usr/bin/env python3
"""
Quick Dashboard Status Check
Immediate diagnostic for Smart Trader Dashboard issues.
"""

import sqlite3
import os
import time
import json
import requests
from datetime import datetime, timedelta

def check_bus_db():
    """Check SQLite bus database status."""
    print("🔍 Checking SQLite bus database...")
    
    bus_db_path = "data/bus.db"
    
    if not os.path.exists(bus_db_path):
        print(f"❌ Database file not found: {bus_db_path}")
        return False
    
    try:
        conn = sqlite3.connect(bus_db_path)
        cursor = conn.cursor()
        
        # Check if messages table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='messages'")
        if not cursor.fetchone():
            print("❌ Messages table does not exist")
            return False
        
        # Check total messages
        cursor.execute("SELECT COUNT(*) FROM messages")
        total = cursor.fetchone()[0]
        print(f"📊 Total messages in database: {total}")
        
        # Check recent messages (last 30 seconds)
        thirty_seconds_ago = (datetime.now() - timedelta(seconds=30)).timestamp()
        cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (thirty_seconds_ago,))
        recent = cursor.fetchone()[0]
        print(f"📈 Messages in last 30 seconds: {recent}")
        
        # Check latest message
        cursor.execute("SELECT ts, stream, payload FROM messages ORDER BY ts DESC LIMIT 1")
        latest = cursor.fetchone()
        
        if latest:
            latest_time = datetime.fromtimestamp(latest[0])
            age = (datetime.now() - latest_time).total_seconds()
            print(f"⏰ Latest message: {age:.1f} seconds ago ({latest_time})")
            print(f"📡 Latest stream: {latest[1]}")
            
            if age < 30:
                print("✅ Database is receiving LIVE data")
                return True
            else:
                print("⚠️ Database data is STALE")
                return False
        else:
            print("❌ No messages found in database")
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        conn.close()

def check_dashboard_server():
    """Check if dashboard server is running."""
    print("\n🌐 Checking dashboard server...")
    
    try:
        response = requests.get("http://localhost:8082/", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard server is RUNNING")
            
            # Test API endpoints
            api_endpoints = [
                "/api/market-data",
                "/api/signals",
                "/api/stats"
            ]
            
            for endpoint in api_endpoints:
                try:
                    api_response = requests.get(f"http://localhost:8082{endpoint}", timeout=3)
                    if api_response.status_code == 200:
                        print(f"✅ API {endpoint}: OK")
                    else:
                        print(f"⚠️ API {endpoint}: Status {api_response.status_code}")
                except Exception as e:
                    print(f"❌ API {endpoint}: {e}")
            
            return True
        else:
            print(f"⚠️ Dashboard server returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Dashboard server is NOT RUNNING")
        print("💡 Start with: python live_dashboard.py")
        return False
    except Exception as e:
        print(f"❌ Dashboard server error: {e}")
        return False

def check_processes():
    """Check for running processes."""
    print("\n🔄 Checking running processes...")
    
    try:
        import psutil
        
        # Look for HTX data producer
        htx_found = False
        dashboard_found = False
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                if 'htx_data_producer' in cmdline:
                    print(f"✅ HTX Data Producer found (PID: {proc.info['pid']})")
                    htx_found = True
                
                if 'live_dashboard' in cmdline:
                    print(f"✅ Live Dashboard found (PID: {proc.info['pid']})")
                    dashboard_found = True
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not htx_found:
            print("❌ HTX Data Producer NOT RUNNING")
            print("💡 Start with: python feeds/htx_data_producer.py")
        
        if not dashboard_found:
            print("❌ Live Dashboard process NOT FOUND")
            print("💡 Start with: python live_dashboard.py")
            
        return htx_found and dashboard_found
        
    except ImportError:
        print("⚠️ psutil not available - cannot check processes")
        print("💡 Install with: pip install psutil")
        return None

def check_websocket():
    """Quick WebSocket test."""
    print("\n🔌 Testing WebSocket connection...")
    
    try:
        import websockets
        import asyncio
        
        async def test_ws():
            try:
                uri = "ws://localhost:8082/ws"
                async with websockets.connect(uri, timeout=5) as websocket:
                    print("✅ WebSocket connected")
                    
                    # Wait for a message
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=5)
                        data = json.loads(message)
                        print(f"✅ Received data: {data.get('type', 'unknown')} at {data.get('timestamp', 'no timestamp')}")
                        return True
                    except asyncio.TimeoutError:
                        print("⚠️ WebSocket connected but no data received in 5 seconds")
                        return False
                        
            except Exception as e:
                print(f"❌ WebSocket error: {e}")
                return False
        
        return asyncio.run(test_ws())
        
    except ImportError:
        print("⚠️ websockets library not available")
        print("💡 Install with: pip install websockets")
        return None

def main():
    """Run quick diagnostics."""
    print("🎯 SMART TRADER DASHBOARD - QUICK STATUS CHECK")
    print("=" * 60)
    
    # Check each component
    db_ok = check_bus_db()
    server_ok = check_dashboard_server()
    processes_ok = check_processes()
    ws_ok = check_websocket()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    issues = []
    
    if not db_ok:
        issues.append("Database not receiving live data")
    
    if not server_ok:
        issues.append("Dashboard server not running properly")
    
    if processes_ok is False:
        issues.append("Required processes not running")
    
    if ws_ok is False:
        issues.append("WebSocket not receiving data")
    
    if not issues:
        print("✅ ALL SYSTEMS APPEAR TO BE WORKING")
        print("💡 If dashboard still not updating, check browser console for errors")
    else:
        print("❌ ISSUES FOUND:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print("\n🔧 RECOMMENDED ACTIONS:")
        if not db_ok:
            print("   1. Start HTX data producer: python feeds/htx_data_producer.py")
        if not server_ok:
            print("   2. Start dashboard server: python live_dashboard.py")
        if processes_ok is False:
            print("   3. Check that all required processes are running")
        if ws_ok is False:
            print("   4. Check dashboard background_updater is running")
    
    print("\n💡 For detailed diagnostics, run: python dashboard_diagnostics.py")

if __name__ == "__main__":
    main()
