"""
Optimize trading strategy parameters.

This script runs parameter optimization for trading strategies
using grid search or genetic algorithms.
"""

import os
import json
import logging
import asyncio
import argparse
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional

from backtester.backtester import Backtester
from backtester.optimizer import StrategyOptimizer
from backtester.strategies import simple_moving_average_crossover, bollinger_bands_strategy, rsi_strategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def optimize_strategy(args):
    """
    Run parameter optimization for a trading strategy.
    
    Args:
        args: Command-line arguments
    """
    # Load configuration
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Create backtester
    backtester = Backtester(
        config=config,
        data_dir=args.data_dir,
        output_dir=args.output_dir
    )
    
    # Get date range from data if not specified
    if not args.start_date or not args.end_date:
        # Use the dates from the generated data
        import pandas as pd
        import os
        
        # Load the first symbol's data to get date range
        symbol_file = os.path.join(args.data_dir, f"{args.symbols[0].replace('-', '_')}.csv")
        if os.path.exists(symbol_file):
            df = pd.read_csv(symbol_file)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                min_date = df['timestamp'].min().strftime("%Y-%m-%d")
                max_date = df['timestamp'].max().strftime("%Y-%m-%d")
                
                if not args.start_date:
                    args.start_date = min_date
                    logger.info(f"Using start date from data: {args.start_date}")
                
                if not args.end_date:
                    args.end_date = max_date
                    logger.info(f"Using end date from data: {args.end_date}")
    
    # Load historical data
    await backtester.load_data(
        symbols=args.symbols,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # Select strategy
    strategy_map = {
        'sma': simple_moving_average_crossover,
        'bollinger': bollinger_bands_strategy,
        'rsi': rsi_strategy
    }
    
    strategy = strategy_map.get(args.strategy)
    if not strategy:
        logger.error(f"Unknown strategy: {args.strategy}")
        return
    
    # Set parameter ranges based on strategy
    param_ranges = {}
    if args.strategy == 'sma':
        param_ranges = {
            'fast_period': list(range(args.fast_period_min, args.fast_period_max + 1, args.fast_period_step)),
            'slow_period': list(range(args.slow_period_min, args.slow_period_max + 1, args.slow_period_step))
        }
    elif args.strategy == 'bollinger':
        param_ranges = {
            'period': list(range(args.period_min, args.period_max + 1, args.period_step)),
            'std_dev': [round(x * 0.1, 1) for x in range(int(args.std_dev_min * 10), int(args.std_dev_max * 10) + 1, int(args.std_dev_step * 10))]
        }
    elif args.strategy == 'rsi':
        param_ranges = {
            'period': list(range(args.period_min, args.period_max + 1, args.period_step)),
            'oversold': list(range(args.oversold_min, args.oversold_max + 1, args.oversold_step)),
            'overbought': list(range(args.overbought_min, args.overbought_max + 1, args.overbought_step))
        }
    
    # Create optimizer
    optimizer = StrategyOptimizer(
        backtester=backtester,
        strategy_func=strategy,
        param_ranges=param_ranges,
        optimization_target=args.target_metric,
        output_dir=args.output_dir
    )
    
    # Run optimization
    if args.method == 'grid':
        results = await optimizer.grid_search(verbose=not args.quiet)
    elif args.method == 'genetic':
        results = await optimizer.genetic_algorithm(
            population_size=args.population_size,
            generations=args.generations,
            mutation_rate=args.mutation_rate,
            elite_size=args.elite_size,
            verbose=not args.quiet
        )
    else:
        logger.error(f"Unknown optimization method: {args.method}")
        return
    
    # Print best parameters
    best_params = results['best_params']
    best_metrics = results['best_metrics']
    
    logger.info("Optimization completed.")
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best {args.target_metric}: {best_metrics.get(args.target_metric, 0):.4f}")
    
    # Run backtest with best parameters
    if args.run_best:
        logger.info("Running backtest with best parameters...")
        
        # Reset backtester
        backtester.reset()
        
        # Create strategy function with best parameters
        async def signal_generator(timestamp, symbols):
            return await strategy(timestamp, symbols, **best_params)
        
        # Run backtest
        success = await backtester.run_backtest(signal_generator=signal_generator)
        
        if success:
            # Print metrics
            metrics = backtester.metrics
            logger.info("Backtest results with best parameters:")
            logger.info(f"Total Return: {metrics['total_return']:.2%}")
            logger.info(f"Annual Return: {metrics['annual_return']:.2%}")
            logger.info(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
            logger.info(f"Max Drawdown: {metrics['max_drawdown']:.2%}")
            logger.info(f"Win Rate: {metrics['win_rate']:.2%}")
            logger.info(f"Total Trades: {metrics['total_trades']}")
            logger.info(f"Initial Balance: ${metrics['initial_balance']:.2f}")
            logger.info(f"Final Balance: ${metrics['final_balance']:.2f}")
        else:
            logger.error("Backtest with best parameters failed")


def main():
    """Parse command-line arguments and run optimization."""
    parser = argparse.ArgumentParser(description="Optimize trading strategy parameters")
    
    # Basic parameters
    parser.add_argument("--config", "-c", default="config_testnet.yaml", help="Path to configuration file")
    parser.add_argument("--symbols", "-s", nargs="+", required=True, help="Symbols to backtest")
    parser.add_argument("--start-date", help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", help="End date in YYYY-MM-DD format")
    parser.add_argument("--data-dir", default="data/historical", help="Directory containing historical data")
    parser.add_argument("--output-dir", default="results/optimization", help="Directory for storing optimization results")
    
    # Strategy selection
    parser.add_argument("--strategy", "-S", choices=['sma', 'bollinger', 'rsi'], required=True,
                       help="Trading strategy to optimize")
    
    # Optimization parameters
    parser.add_argument("--method", choices=['grid', 'genetic'], default='grid',
                       help="Optimization method to use")
    parser.add_argument("--target-metric", default="sharpe_ratio",
                       choices=['sharpe_ratio', 'total_return', 'win_rate', 'max_drawdown'],
                       help="Metric to optimize")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress progress output")
    parser.add_argument("--run-best", action="store_true", help="Run backtest with best parameters")
    
    # Grid search parameters
    parser.add_argument("--fast-period-min", type=int, default=5, help="Minimum fast period for SMA strategy")
    parser.add_argument("--fast-period-max", type=int, default=20, help="Maximum fast period for SMA strategy")
    parser.add_argument("--fast-period-step", type=int, default=1, help="Step size for fast period")
    parser.add_argument("--slow-period-min", type=int, default=20, help="Minimum slow period for SMA strategy")
    parser.add_argument("--slow-period-max", type=int, default=50, help="Maximum slow period for SMA strategy")
    parser.add_argument("--slow-period-step", type=int, default=5, help="Step size for slow period")
    
    # Bollinger Bands parameters
    parser.add_argument("--period-min", type=int, default=10, help="Minimum period for Bollinger Bands or RSI")
    parser.add_argument("--period-max", type=int, default=30, help="Maximum period for Bollinger Bands or RSI")
    parser.add_argument("--period-step", type=int, default=5, help="Step size for period")
    parser.add_argument("--std-dev-min", type=float, default=1.5, help="Minimum standard deviation for Bollinger Bands")
    parser.add_argument("--std-dev-max", type=float, default=3.0, help="Maximum standard deviation for Bollinger Bands")
    parser.add_argument("--std-dev-step", type=float, default=0.5, help="Step size for standard deviation")
    
    # RSI parameters
    parser.add_argument("--oversold-min", type=int, default=20, help="Minimum oversold threshold for RSI")
    parser.add_argument("--oversold-max", type=int, default=40, help="Maximum oversold threshold for RSI")
    parser.add_argument("--oversold-step", type=int, default=5, help="Step size for oversold threshold")
    parser.add_argument("--overbought-min", type=int, default=60, help="Minimum overbought threshold for RSI")
    parser.add_argument("--overbought-max", type=int, default=80, help="Maximum overbought threshold for RSI")
    parser.add_argument("--overbought-step", type=int, default=5, help="Step size for overbought threshold")
    
    # Genetic algorithm parameters
    parser.add_argument("--population-size", type=int, default=20, help="Population size for genetic algorithm")
    parser.add_argument("--generations", type=int, default=10, help="Number of generations for genetic algorithm")
    parser.add_argument("--mutation-rate", type=float, default=0.1, help="Mutation rate for genetic algorithm")
    parser.add_argument("--elite-size", type=int, default=2, help="Elite size for genetic algorithm")
    
    args = parser.parse_args()
    
    # Run optimization
    asyncio.run(optimize_strategy(args))


if __name__ == "__main__":
    main()
