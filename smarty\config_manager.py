#!/usr/bin/env python3
"""
Configuration Manager for Smart-Trader Control Center

Handles configuration loading, validation, and hot-reloading.
"""

import json
import logging
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class TradingMode(Enum):
    """Trading mode enumeration."""
    TESTNET = "testnet"
    LIVE = "live"
    SIMULATION = "simulation"


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


@dataclass
class HTXConfig:
    """HTX exchange configuration."""
    api_key: str = ""
    api_secret: str = ""
    passphrase: str = ""
    testnet: bool = True
    base_url: str = "https://api.hbdm.com"
    testnet_url: str = "https://api.btcgateway.pro"
    timeout: int = 30
    rate_limit: int = 100


@dataclass
class LLMConfig:
    """LLM configuration."""
    model_path: str = "C:/Users/<USER>/.lmstudio/models/lmstudio-community/Phi-3.1-mini-4K-instruct-GGUF"
    model_file: str = "Phi-3.1-mini-4K-instruct-Q4_K_M.gguf"
    max_tokens: int = 512
    temperature: float = 0.7
    context_length: int = 4096
    gpu_layers: int = 0


@dataclass
class DatabaseConfig:
    """Database configuration."""
    path: str = "smart_trader.db"
    wal_mode: bool = True
    cache_size: int = 10000
    timeout: int = 30
    backup_interval: int = 3600


@dataclass
class TradingConfig:
    """Trading configuration."""
    default_symbol: str = "BTCUSDT"
    max_position_size: float = 0.1
    risk_per_trade: float = 0.02
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.10
    max_open_positions: int = 3
    min_trade_interval: int = 60


@dataclass
class WebConfig:
    """Web interface configuration."""
    host: str = "localhost"
    port: int = 8081
    debug: bool = False
    cors_enabled: bool = True
    websocket_timeout: int = 60
    max_connections: int = 100


@dataclass
class MonitoringConfig:
    """Monitoring and alerting configuration."""
    log_level: LogLevel = LogLevel.INFO
    log_file: str = "smart_trader.log"
    max_log_size: int = 10485760  # 10MB
    backup_count: int = 5
    metrics_enabled: bool = True
    health_check_interval: int = 30


@dataclass
class SmartTraderConfig:
    """Main configuration class."""
    htx: HTXConfig
    llm: LLMConfig
    database: DatabaseConfig
    trading: TradingConfig
    web: WebConfig
    monitoring: MonitoringConfig
    
    # System settings
    trading_mode: TradingMode = TradingMode.TESTNET
    auto_start: bool = False
    backup_enabled: bool = True
    
    # Timestamps
    created_at: str = ""
    updated_at: str = ""

    def __post_init__(self):
        """Post-initialization setup."""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()


class ConfigManager:
    """Configuration manager with hot-reloading and validation."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize configuration manager."""
        self.config_path = Path(config_path)
        self.config: Optional[SmartTraderConfig] = None
        self._last_modified = 0
        
    def load_config(self) -> SmartTraderConfig:
        """Load configuration from file."""
        try:
            if not self.config_path.exists():
                logger.info(f"Config file not found, creating default: {self.config_path}")
                self.config = self._create_default_config()
                self.save_config()
                return self.config

            # Check if file was modified
            current_modified = self.config_path.stat().st_mtime
            if current_modified != self._last_modified:
                logger.info(f"Loading configuration from: {self.config_path}")
                
                with open(self.config_path, 'r') as f:
                    if self.config_path.suffix.lower() == '.json':
                        data = json.load(f)
                    else:
                        data = yaml.safe_load(f)
                
                self.config = self._dict_to_config(data)
                self._last_modified = current_modified
                logger.info("Configuration loaded successfully")
            
            return self.config

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            if self.config is None:
                logger.info("Using default configuration")
                self.config = self._create_default_config()
            return self.config

    def save_config(self) -> bool:
        """Save configuration to file."""
        try:
            if self.config is None:
                logger.error("No configuration to save")
                return False

            self.config.updated_at = datetime.now().isoformat()
            data = asdict(self.config)
            
            # Convert enums to strings
            data['trading_mode'] = data['trading_mode'].value
            data['monitoring']['log_level'] = data['monitoring']['log_level'].value

            with open(self.config_path, 'w') as f:
                if self.config_path.suffix.lower() == '.json':
                    json.dump(data, f, indent=2)
                else:
                    yaml.dump(data, f, default_flow_style=False, indent=2)

            self._last_modified = self.config_path.stat().st_mtime
            logger.info(f"Configuration saved to: {self.config_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False

    def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration with new values."""
        try:
            if self.config is None:
                self.config = self._create_default_config()

            # Apply updates recursively
            self._apply_updates(asdict(self.config), updates)
            
            # Recreate config object
            updated_data = asdict(self.config)
            self._apply_updates(updated_data, updates)
            self.config = self._dict_to_config(updated_data)
            
            return self.save_config()

        except Exception as e:
            logger.error(f"Failed to update configuration: {e}")
            return False

    def _create_default_config(self) -> SmartTraderConfig:
        """Create default configuration."""
        return SmartTraderConfig(
            htx=HTXConfig(),
            llm=LLMConfig(),
            database=DatabaseConfig(),
            trading=TradingConfig(),
            web=WebConfig(),
            monitoring=MonitoringConfig()
        )

    def _dict_to_config(self, data: Dict[str, Any]) -> SmartTraderConfig:
        """Convert dictionary to configuration object."""
        # Handle enums
        if 'trading_mode' in data:
            data['trading_mode'] = TradingMode(data['trading_mode'])
        
        if 'monitoring' in data and 'log_level' in data['monitoring']:
            data['monitoring']['log_level'] = LogLevel(data['monitoring']['log_level'])

        return SmartTraderConfig(
            htx=HTXConfig(**data.get('htx', {})),
            llm=LLMConfig(**data.get('llm', {})),
            database=DatabaseConfig(**data.get('database', {})),
            trading=TradingConfig(**data.get('trading', {})),
            web=WebConfig(**data.get('web', {})),
            monitoring=MonitoringConfig(**data.get('monitoring', {})),
            trading_mode=data.get('trading_mode', TradingMode.TESTNET),
            auto_start=data.get('auto_start', False),
            backup_enabled=data.get('backup_enabled', True),
            created_at=data.get('created_at', ''),
            updated_at=data.get('updated_at', '')
        )

    def _apply_updates(self, target: Dict[str, Any], updates: Dict[str, Any]) -> None:
        """Apply updates recursively to target dictionary."""
        for key, value in updates.items():
            if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                self._apply_updates(target[key], value)
            else:
                target[key] = value

    def validate_config(self) -> tuple[bool, list[str]]:
        """Validate configuration and return status and errors."""
        errors = []
        
        if self.config is None:
            errors.append("Configuration not loaded")
            return False, errors

        # Validate HTX config
        if self.config.trading_mode == TradingMode.LIVE:
            if not self.config.htx.api_key:
                errors.append("HTX API key required for live trading")
            if not self.config.htx.api_secret:
                errors.append("HTX API secret required for live trading")

        # Validate LLM config
        llm_path = Path(self.config.llm.model_path)
        if not llm_path.exists():
            errors.append(f"LLM model path does not exist: {llm_path}")

        # Validate trading config
        if self.config.trading.max_position_size <= 0:
            errors.append("Max position size must be positive")
        if self.config.trading.risk_per_trade <= 0 or self.config.trading.risk_per_trade > 1:
            errors.append("Risk per trade must be between 0 and 1")

        # Validate web config
        if self.config.web.port < 1024 or self.config.web.port > 65535:
            errors.append("Web port must be between 1024 and 65535")

        return len(errors) == 0, errors

    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for display."""
        if self.config is None:
            return {"error": "Configuration not loaded"}

        return {
            "trading_mode": self.config.trading_mode.value,
            "htx_configured": bool(self.config.htx.api_key),
            "llm_model": Path(self.config.llm.model_path).name,
            "database_path": self.config.database.path,
            "web_port": self.config.web.port,
            "auto_start": self.config.auto_start,
            "created_at": self.config.created_at,
            "updated_at": self.config.updated_at
        }


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> SmartTraderConfig:
    """Get current configuration."""
    return config_manager.load_config()


def update_config(updates: Dict[str, Any]) -> bool:
    """Update configuration."""
    return config_manager.update_config(updates)


def validate_config() -> tuple[bool, list[str]]:
    """Validate current configuration."""
    return config_manager.validate_config()
