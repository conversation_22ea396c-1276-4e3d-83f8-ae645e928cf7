"""
Parameter optimization for trading strategies.

This module provides functionality for optimizing strategy parameters
using various methods such as grid search and genetic algorithms.
"""

import os
import json
import logging
import asyncio
import itertools
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple, Callable, Union
from concurrent.futures import ProcessPoolExecutor, as_completed

from .backtester import Backtester

logger = logging.getLogger(__name__)


class StrategyOptimizer:
    """
    Optimizer for trading strategy parameters.

    This class provides methods for optimizing strategy parameters
    using various techniques such as grid search and genetic algorithms.
    """

    def __init__(
        self,
        backtester: Backtester,
        strategy_func: Callable,
        param_ranges: Dict[str, List[Any]],
        optimization_target: str = 'sharpe_ratio',
        output_dir: str = 'results/optimization',
        max_workers: int = None
    ):
        """
        Initialize the strategy optimizer.

        Args:
            backtester: Backtester instance
            strategy_func: Strategy function to optimize
            param_ranges: Dictionary of parameter names and their possible values
            optimization_target: Metric to optimize (e.g., 'sharpe_ratio', 'total_return')
            output_dir: Directory for storing optimization results
            max_workers: Maximum number of worker processes for parallel optimization
        """
        self.backtester = backtester
        self.strategy_func = strategy_func
        self.param_ranges = param_ranges
        self.optimization_target = optimization_target
        self.output_dir = output_dir
        self.max_workers = max_workers

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Results storage
        self.results = []
        self.best_params = None
        self.best_metrics = None

    async def grid_search(self, verbose: bool = True) -> Dict[str, Any]:
        """
        Perform grid search optimization.

        This method tests all combinations of parameters in the specified ranges
        and returns the best performing parameters.

        Args:
            verbose: Whether to print progress information

        Returns:
            Dictionary containing the best parameters and their performance metrics
        """
        # Generate all parameter combinations
        param_names = list(self.param_ranges.keys())
        param_values = list(self.param_ranges.values())
        param_combinations = list(itertools.product(*param_values))

        total_combinations = len(param_combinations)
        logger.info(f"Starting grid search with {total_combinations} parameter combinations")

        # Create a list to store results
        results = []

        # Run backtests for each parameter combination
        for i, combination in enumerate(param_combinations):
            # Create parameter dictionary
            params = dict(zip(param_names, combination))

            if verbose:
                logger.info(f"Testing combination {i+1}/{total_combinations}: {params}")

            # Create strategy function with parameters
            async def signal_generator(timestamp, symbols):
                return await self.strategy_func(timestamp, symbols, **params)

            # Reset backtester state
            self.backtester.reset()

            # Run backtest
            success = await self.backtester.run_backtest(signal_generator=signal_generator)

            if success:
                # Get metrics
                metrics = self.backtester.metrics

                # Store results
                result = {
                    'params': params,
                    'metrics': metrics
                }
                results.append(result)

                if verbose:
                    target_value = metrics.get(self.optimization_target, 0)
                    logger.info(f"Result: {self.optimization_target} = {target_value:.4f}")
            else:
                logger.warning(f"Backtest failed for parameters: {params}")

        # Sort results by optimization target
        results.sort(key=lambda x: x['metrics'].get(self.optimization_target, 0), reverse=True)

        # Store results
        self.results = results

        # Get best parameters
        if results:
            self.best_params = results[0]['params']
            self.best_metrics = results[0]['metrics']

            logger.info(f"Best parameters: {self.best_params}")
            logger.info(f"Best {self.optimization_target}: {self.best_metrics.get(self.optimization_target, 0):.4f}")
        else:
            logger.warning("No valid results found")

        # Save results
        self._save_results()

        return {
            'best_params': self.best_params,
            'best_metrics': self.best_metrics,
            'all_results': results
        }

    async def genetic_algorithm(
        self,
        population_size: int = 20,
        generations: int = 10,
        mutation_rate: float = 0.1,
        elite_size: int = 2,
        verbose: bool = True
    ) -> Dict[str, Any]:
        """
        Perform genetic algorithm optimization.

        This method uses a genetic algorithm to find optimal parameters.

        Args:
            population_size: Size of the population
            generations: Number of generations
            mutation_rate: Probability of mutation
            elite_size: Number of top individuals to keep unchanged
            verbose: Whether to print progress information

        Returns:
            Dictionary containing the best parameters and their performance metrics
        """
        logger.info(f"Starting genetic algorithm optimization with {population_size} individuals and {generations} generations")

        # Initialize population
        population = self._initialize_population(population_size)

        # Results storage
        all_results = []

        # Run genetic algorithm
        for generation in range(generations):
            if verbose:
                logger.info(f"Generation {generation+1}/{generations}")

            # Evaluate population
            fitness_results = await self._evaluate_population(population, verbose)
            all_results.extend(fitness_results)

            # Sort by fitness
            fitness_results.sort(key=lambda x: x['metrics'].get(self.optimization_target, 0), reverse=True)

            # Store best result
            if generation == 0 or fitness_results[0]['metrics'].get(self.optimization_target, 0) > self.best_metrics.get(self.optimization_target, 0):
                self.best_params = fitness_results[0]['params']
                self.best_metrics = fitness_results[0]['metrics']

            if verbose:
                logger.info(f"Best {self.optimization_target} in generation {generation+1}: {fitness_results[0]['metrics'].get(self.optimization_target, 0):.4f}")

            # Stop if this is the last generation
            if generation == generations - 1:
                break

            # Select parents
            parents = self._select_parents(fitness_results, elite_size)

            # Create next generation
            population = self._create_next_generation(parents, population_size, mutation_rate, elite_size)

        # Store results
        self.results = all_results

        logger.info(f"Best parameters: {self.best_params}")
        logger.info(f"Best {self.optimization_target}: {self.best_metrics.get(self.optimization_target, 0):.4f}")

        # Save results
        self._save_results()

        return {
            'best_params': self.best_params,
            'best_metrics': self.best_metrics,
            'all_results': all_results
        }

    def _initialize_population(self, population_size: int) -> List[Dict[str, Any]]:
        """
        Initialize a random population for genetic algorithm.

        Args:
            population_size: Size of the population

        Returns:
            List of parameter dictionaries
        """
        population = []

        for _ in range(population_size):
            # Generate random parameters
            params = {}
            for param_name, param_range in self.param_ranges.items():
                # Handle different parameter types
                if isinstance(param_range[0], int):
                    # Integer parameter
                    min_val = min(param_range)
                    max_val = max(param_range)
                    params[param_name] = np.random.randint(min_val, max_val + 1)
                elif isinstance(param_range[0], float):
                    # Float parameter
                    min_val = min(param_range)
                    max_val = max(param_range)
                    params[param_name] = min_val + np.random.random() * (max_val - min_val)
                else:
                    # Categorical parameter
                    params[param_name] = np.random.choice(param_range)

            population.append(params)

        return population

    async def _evaluate_population(self, population: List[Dict[str, Any]], verbose: bool) -> List[Dict[str, Any]]:
        """
        Evaluate the fitness of each individual in the population.

        Args:
            population: List of parameter dictionaries
            verbose: Whether to print progress information

        Returns:
            List of dictionaries containing parameters and metrics
        """
        results = []

        for i, params in enumerate(population):
            if verbose:
                logger.info(f"Evaluating individual {i+1}/{len(population)}: {params}")

            # Create strategy function with parameters
            async def signal_generator(timestamp, symbols):
                return await self.strategy_func(timestamp, symbols, **params)

            # Reset backtester state
            self.backtester.reset()

            # Run backtest
            success = await self.backtester.run_backtest(signal_generator=signal_generator)

            if success:
                # Get metrics
                metrics = self.backtester.metrics

                # Store results
                result = {
                    'params': params,
                    'metrics': metrics
                }
                results.append(result)

                if verbose:
                    target_value = metrics.get(self.optimization_target, 0)
                    logger.info(f"Result: {self.optimization_target} = {target_value:.4f}")
            else:
                logger.warning(f"Backtest failed for parameters: {params}")
                # Add a result with very poor fitness
                result = {
                    'params': params,
                    'metrics': {self.optimization_target: float('-inf')}
                }
                results.append(result)

        return results

    def _select_parents(self, fitness_results: List[Dict[str, Any]], elite_size: int) -> List[Dict[str, Any]]:
        """
        Select parents for breeding using tournament selection.

        Args:
            fitness_results: List of dictionaries containing parameters and metrics
            elite_size: Number of top individuals to keep unchanged

        Returns:
            List of selected parents
        """
        # Sort by fitness
        fitness_results.sort(key=lambda x: x['metrics'].get(self.optimization_target, 0), reverse=True)

        # Keep elite individuals
        parents = [result['params'] for result in fitness_results[:elite_size]]

        # Tournament selection for the rest
        while len(parents) < len(fitness_results):
            # Select random individuals for tournament
            tournament_size = 3
            tournament = np.random.choice(len(fitness_results), tournament_size, replace=False)
            tournament_fitness = [fitness_results[i]['metrics'].get(self.optimization_target, 0) for i in tournament]

            # Select winner
            winner_idx = tournament[np.argmax(tournament_fitness)]
            parents.append(fitness_results[winner_idx]['params'])

        return parents

    def _create_next_generation(
        self,
        parents: List[Dict[str, Any]],
        population_size: int,
        mutation_rate: float,
        elite_size: int
    ) -> List[Dict[str, Any]]:
        """
        Create the next generation through crossover and mutation.

        Args:
            parents: List of parent parameter dictionaries
            population_size: Size of the population
            mutation_rate: Probability of mutation
            elite_size: Number of top individuals to keep unchanged

        Returns:
            List of parameter dictionaries for the next generation
        """
        # Keep elite individuals
        next_generation = parents[:elite_size]

        # Create children through crossover and mutation
        while len(next_generation) < population_size:
            # Select two parents
            parent1 = parents[np.random.randint(0, len(parents))]
            parent2 = parents[np.random.randint(0, len(parents))]

            # Crossover
            child = {}
            for param_name in self.param_ranges.keys():
                # 50% chance to inherit from each parent
                if np.random.random() < 0.5:
                    child[param_name] = parent1[param_name]
                else:
                    child[param_name] = parent2[param_name]

            # Mutation
            for param_name, param_range in self.param_ranges.items():
                if np.random.random() < mutation_rate:
                    if isinstance(param_range[0], int):
                        # Integer parameter
                        min_val = min(param_range)
                        max_val = max(param_range)
                        child[param_name] = np.random.randint(min_val, max_val + 1)
                    elif isinstance(param_range[0], float):
                        # Float parameter
                        min_val = min(param_range)
                        max_val = max(param_range)
                        child[param_name] = min_val + np.random.random() * (max_val - min_val)
                    else:
                        # Categorical parameter
                        child[param_name] = np.random.choice(param_range)

            next_generation.append(child)

        return next_generation

    def _save_results(self):
        """Save optimization results to files."""
        # Create timestamp for result files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save all results
        results_file = os.path.join(self.output_dir, f"optimization_results_{timestamp}.json")

        # Convert results to serializable format
        serializable_results = []
        for result in self.results:
            serializable_result = {
                'params': result['params'],
                'metrics': {k: float(v) if isinstance(v, np.float64) else v for k, v in result['metrics'].items()}
            }
            serializable_results.append(serializable_result)

        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)

        # Save best parameters
        if self.best_params:
            best_params_file = os.path.join(self.output_dir, f"best_params_{timestamp}.json")
            with open(best_params_file, 'w') as f:
                json.dump({
                    'params': self.best_params,
                    'metrics': {k: float(v) if isinstance(v, np.float64) else v for k, v in self.best_metrics.items()}
                }, f, indent=2)

        logger.info(f"Optimization results saved to {self.output_dir}")
