#!/usr/bin/env python3
"""
Debug authentication system directly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import DatabaseManager
from auth.user_manager import UserManager
import bcrypt

def debug_authentication():
    """Debug the authentication system."""
    print("🔍 DEBUGGING MONEY CIRCLE AUTHENTICATION")
    print("=" * 50)
    
    try:
        # Initialize database and user manager
        db = DatabaseManager('data/money_circle.db')
        user_manager = UserManager(db)
        
        # Test getting user
        print("Step 1: Testing user retrieval...")
        user = user_manager.get_user_by_username('epinnox')
        if user:
            print(f"✅ User found: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Agreement accepted: {user.agreement_accepted}")
            print(f"   Hash length: {len(user.hashed_password)}")
            print(f"   Hash starts with: {user.hashed_password[:10]}...")
        else:
            print("❌ User not found!")
            return False
        
        # Test password verification directly
        print("\nStep 2: Testing password verification...")
        test_password = "securepass123"
        
        # Test with bcrypt directly
        try:
            is_valid = bcrypt.checkpw(test_password.encode('utf-8'), user.hashed_password.encode('utf-8'))
            print(f"✅ Direct bcrypt verification: {is_valid}")
        except Exception as e:
            print(f"❌ Direct bcrypt verification failed: {e}")
        
        # Test with user manager method
        try:
            is_valid = user_manager._verify_password(test_password, user.hashed_password)
            print(f"✅ UserManager verification: {is_valid}")
        except Exception as e:
            print(f"❌ UserManager verification failed: {e}")
        
        # Test full authentication
        print("\nStep 3: Testing full authentication...")
        authenticated_user = user_manager.authenticate_user('epinnox', 'securepass123', '127.0.0.1')
        if authenticated_user:
            print(f"✅ Full authentication successful: {authenticated_user.username}")
        else:
            print("❌ Full authentication failed!")
            
            # Check for rate limiting
            print("\nChecking for rate limiting...")
            if user_manager._is_locked_out('epinnox'):
                print("❌ User is locked out!")
            elif user_manager._is_locked_out('127.0.0.1'):
                print("❌ IP is locked out!")
            else:
                print("✅ No rate limiting detected")
        
        # Test other demo users
        print("\nStep 4: Testing demo users...")
        demo_users = ['trader_alex', 'crypto_sarah', 'quant_mike']
        for username in demo_users:
            user = user_manager.get_user_by_username(username)
            if user:
                print(f"✅ {username}: Found")
                auth_result = user_manager.authenticate_user(username, 'securepass123', '127.0.0.1')
                if auth_result:
                    print(f"   ✅ Authentication successful")
                else:
                    print(f"   ❌ Authentication failed")
            else:
                print(f"❌ {username}: Not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    success = debug_authentication()
    
    if success:
        print("\n✅ DEBUG COMPLETED")
        print("Check the output above for authentication issues")
    else:
        print("\n❌ DEBUG FAILED")
        print("There are serious authentication system issues")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
