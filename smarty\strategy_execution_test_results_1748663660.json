{"status": "completed", "timestamp": 1748663660.5488038, "strategy_tests": {"Smart Model Integrated": {"strategy_name": "Smart Model Integrated", "start_result": {"status": "success", "message": "Strategy Smart Model Integrated started successfully", "response": {"success": true, "message": "Strategy Smart Model Integrated started successfully", "strategy": "Smart Model Integrated", "status": {"current_strategy": "Smart Model Integrated", "strategy_running": true, "active_processes": 1, "total_processes": 1, "process_details": {"Smart Model Integrated": {"pid": 33980, "running": true, "return_code": null}}}}}, "monitoring_result": {"duration": 15, "activity_log": [], "total_checks": 0}, "stop_result": {"status": "success", "message": "Strategy stopped successfully", "response": {"success": true, "message": "Strategy stopped successfully", "status": {"current_strategy": "Smart Model Integrated", "strategy_running": false, "active_processes": 0, "total_processes": 1, "process_details": {"Smart Model Integrated": {"pid": null, "running": false, "return_code": 1}}}}}, "overall_status": "failed"}, "Smart Strategy Only": {"strategy_name": "Smart Strategy Only", "start_result": {"status": "success", "message": "Strategy Smart Strategy Only started successfully", "response": {"success": true, "message": "Strategy Smart Strategy Only started successfully", "strategy": "Smart Strategy Only", "status": {"current_strategy": "Smart Strategy Only", "strategy_running": true, "active_processes": 1, "total_processes": 2, "process_details": {"Smart Model Integrated": {"pid": null, "running": false, "return_code": 1}, "Smart Strategy Only": {"pid": 15436, "running": true, "return_code": null}}}}}, "monitoring_result": {"duration": 15, "activity_log": [], "total_checks": 0}, "stop_result": {"status": "success", "message": "Strategy stopped successfully", "response": {"success": true, "message": "Strategy stopped successfully", "status": {"current_strategy": "Smart Strategy Only", "strategy_running": false, "active_processes": 0, "total_processes": 2, "process_details": {"Smart Model Integrated": {"pid": null, "running": false, "return_code": 1}, "Smart Strategy Only": {"pid": null, "running": false, "return_code": 1}}}}}, "overall_status": "failed"}, "Order Flow": {"strategy_name": "Order Flow", "start_result": {"status": "success", "message": "Strategy Order Flow started successfully", "response": {"success": true, "message": "Strategy Order Flow started successfully", "strategy": "Order Flow", "status": {"current_strategy": "Order Flow", "strategy_running": true, "active_processes": 1, "total_processes": 3, "process_details": {"Smart Model Integrated": {"pid": null, "running": false, "return_code": 1}, "Smart Strategy Only": {"pid": null, "running": false, "return_code": 1}, "Order Flow": {"pid": 29328, "running": true, "return_code": null}}}}}, "monitoring_result": {"duration": 15, "activity_log": [], "total_checks": 0}, "stop_result": {"status": "success", "message": "Strategy stopped successfully", "response": {"success": true, "message": "Strategy stopped successfully", "status": {"current_strategy": "Order Flow", "strategy_running": false, "active_processes": 0, "total_processes": 3, "process_details": {"Smart Model Integrated": {"pid": null, "running": false, "return_code": 1}, "Smart Strategy Only": {"pid": null, "running": false, "return_code": 1}, "Order Flow": {"pid": null, "running": false, "return_code": 1}}}}}, "overall_status": "failed"}}}