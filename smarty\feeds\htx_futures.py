"""
HTX Futures API client for the smart-trader system.

This module provides a backward-compatible interface to the enhanced
HTX Futures client with improved architecture, error handling, and reliability.

The new modular architecture includes:
- Separated concerns (WebSocket, REST, parsing)
- Enhanced error handling and logging
- Comprehensive type safety
- Built-in retry mechanisms
- Health monitoring
- Performance metrics

For new code, consider using the modular components directly from feeds.htx
"""

import logging
from typing import Dict, List, Optional, Any, Callable, Union

from core.events import (
    Kline, Trade, OrderbookDelta, Position,
    Order, OrderResponse, Fill
)

# Import the enhanced client
from .htx import HTXFuturesClient as EnhancedHTXFuturesClient

logger = logging.getLogger(__name__)


class HTXFuturesClient:
    """
    Backward-compatible HTX Futures API client.
    
    This class provides a backward-compatible interface to the enhanced
    HTX Futures client while maintaining the same API surface.
    
    For new code, consider using the modular components directly from feeds.htx
    """
    
    # Legacy constants for backward compatibility
    REST_BASE_URL = "https://api.htx.com"
    WS_MARKET_URL = "wss://api.htx.com/linear-swap-ws"
    WS_PRIVATE_URL = "wss://api.htx.com/linear-swap-notification"
    
    # WebSocket channels
    CHANNEL_KLINE = "market.{symbol}.kline.{interval}"
    CHANNEL_TRADE = "market.{symbol}.trade.detail"
    CHANNEL_DEPTH = "market.{symbol}.depth.step0"
    CHANNEL_POSITION = "positions.{symbol}"
    CHANNEL_ORDER = "orders.{symbol}"
    
    def __init__(
        self,
        api_key: str = "",
        api_secret: str = "",
        testnet: bool = False,
        ping_interval: int = 20,
        queue_size: int = 1000,
        debug: bool = False,
        **kwargs
    ):
        """
        Initialize the HTX Futures client.

        Args:
            api_key: API key for authenticated requests
            api_secret: API secret for authenticated requests
            testnet: Whether to use testnet
            ping_interval: WebSocket ping interval in seconds
            queue_size: Maximum size of the message queue
            debug: Enable debug logging
            **kwargs: Additional configuration options
        """
        # Create configuration for enhanced client
        config = {
            "ping_interval": ping_interval,
            "queue_size": queue_size,
            "debug": debug,
            **kwargs
        }
        
        # Initialize enhanced client
        self._enhanced_client = EnhancedHTXFuturesClient(
            api_key=api_key,
            api_secret=api_secret,
            testnet=testnet,
            config=config
        )
        
        # Legacy properties for backward compatibility
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.ping_interval = ping_interval
        
        # Simulation mode flag
        self.simulation_mode = False
        
        # Connection status
        self.connected = False
        self.authenticated = False
        
        # Subscription tracking
        self.subscriptions = set()
        
        logger.info("HTX Futures Client (backward-compatible wrapper) initialized")

    @property
    def market_queue(self):
        """Access to market queue for backward compatibility."""
        return self._enhanced_client.market_queue
    
    @property
    def private_queue(self):
        """Access to private queue for backward compatibility."""
        return self._enhanced_client.private_queue

    def set_publisher(self, publish_fn: Callable[[str, float, dict], None]) -> None:
        """Set the message bus publisher function."""
        self._enhanced_client.set_publisher(publish_fn)
        logger.info("Message bus publisher set for HTX Futures client")

    async def connect(self) -> None:
        """Connect to HTX Futures WebSocket and REST API."""
        await self._enhanced_client.connect()
        
        # Update legacy status flags
        self.connected = self._enhanced_client.connected
        self.authenticated = self._enhanced_client.authenticated
        
        # Sync simulation mode
        self._enhanced_client.simulation_mode = self.simulation_mode

    async def subscribe(self, channel: str) -> bool:
        """Subscribe to a WebSocket channel."""
        success = await self._enhanced_client.subscribe(channel)
        if success:
            self.subscriptions.add(channel)
        return success

    async def unsubscribe(self, channel: str) -> bool:
        """Unsubscribe from a WebSocket channel."""
        success = await self._enhanced_client.unsubscribe(channel)
        if success:
            self.subscriptions.discard(channel)
        return success

    async def get_next_market_message(self, timeout: float = None) -> Optional[Union[Kline, Trade, OrderbookDelta]]:
        """Get the next market data message from the queue."""
        return await self._enhanced_client.get_next_market_message(timeout)

    async def get_next_private_message(self, timeout: float = None) -> Optional[Union[Position, OrderResponse, Fill]]:
        """Get the next private message from the queue."""
        return await self._enhanced_client.get_next_private_message(timeout)

    # REST API methods - delegate to enhanced client
    async def get_contract_info(self, symbol: str = None) -> Dict[str, Any]:
        """Get contract information."""
        return await self._enhanced_client.get_contract_info(symbol)

    async def get_price_limits(self, symbol: str) -> Dict[str, Any]:
        """Get price limits for a symbol."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.get_price_limits(symbol)
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        return await self._enhanced_client.get_account_info()

    async def get_position_info(self, symbol: str = None) -> Dict[str, Any]:
        """Get position information."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.get_position_info(symbol)
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_positions(self, symbol: str = None) -> List[Position]:
        """Get positions as domain objects."""
        return await self._enhanced_client.get_positions(symbol)

    async def place_order(self, order: Order) -> OrderResponse:
        """Place an order."""
        return await self._enhanced_client.place_order(order)

    async def cancel_order(self, symbol: str, order_id: str = None, client_order_id: str = None) -> bool:
        """Cancel an order."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.cancel_order(symbol, order_id, client_order_id)
        return response.success

    async def get_order_info(self, symbol: str, order_id: str = None, client_order_id: str = None) -> Dict[str, Any]:
        """Get information about an order."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.get_order_info(symbol, order_id, client_order_id)
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_open_orders(self, symbol: str = None) -> Dict[str, Any]:
        """Get open orders."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.get_open_orders(symbol)
        return {"status": "ok" if response.success else "error", "data": response.data}

    async def get_orders(self, symbol: str = None) -> List[OrderResponse]:
        """Get open orders as domain objects."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        return await self._enhanced_client.rest_client.get_orders(symbol)

    async def get_account_balance(self) -> Dict[str, float]:
        """Get account balance."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        return await self._enhanced_client.rest_client.get_account_balance()

    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for a symbol."""
        if not self._enhanced_client.rest_client:
            raise Exception("REST client not initialized")
        response = await self._enhanced_client.rest_client.set_leverage(symbol, leverage)
        return response.success

    async def health_check(self) -> bool:
        """Perform a health check."""
        health_status = await self._enhanced_client.health_check()
        return health_status.is_healthy

    async def close(self) -> None:
        """Close all connections."""
        await self._enhanced_client.close()
        self.connected = False
        self.authenticated = False


# For backward compatibility, also export the enhanced client directly
__all__ = ["HTXFuturesClient"]
