{% extends "base.html" %}

{% block title %}Money Circle - Club Analytics{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/club.css">
<link rel="stylesheet" href="/static/css/club_analytics.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="club-analytics-container">
    <!-- Analytics Header -->
    <section class="analytics-header">
        <div class="header-content">
            <h1>📊 Club Analytics</h1>
            <p>Comprehensive performance insights and data analysis</p>
        </div>
        
        <!-- Time Period Selector -->
        <div class="time-period-selector">
            <button class="period-btn active" data-period="7d" onclick="changePeriod('7d')">7 Days</button>
            <button class="period-btn" data-period="30d" onclick="changePeriod('30d')">30 Days</button>
            <button class="period-btn" data-period="90d" onclick="changePeriod('90d')">90 Days</button>
            <button class="period-btn" data-period="1y" onclick="changePeriod('1y')">1 Year</button>
            <button class="period-btn" data-period="all" onclick="changePeriod('all')">All Time</button>
        </div>
    </section>

    <!-- Club Overview -->
    <section class="club-overview">
        <h2>🏛️ Club Overview</h2>
        <div class="overview-grid">
            <div class="overview-card">
                <div class="card-icon">💰</div>
                <div class="card-content">
                    <div class="card-value">${{ club_overview.total_portfolio_value|round(2) }}</div>
                    <div class="card-label">Total Portfolio Value</div>
                    <div class="card-change {{ 'positive' if club_overview.portfolio_change >= 0 else 'negative' }}">
                        {{ '+' if club_overview.portfolio_change >= 0 else '' }}{{ club_overview.portfolio_change|round(2) }}%
                    </div>
                </div>
            </div>
            
            <div class="overview-card">
                <div class="card-icon">👥</div>
                <div class="card-content">
                    <div class="card-value">{{ club_overview.total_members }}</div>
                    <div class="card-label">Total Members</div>
                    <div class="card-change positive">+{{ club_overview.new_members_this_month }} this month</div>
                </div>
            </div>
            
            <div class="overview-card">
                <div class="card-icon">🎯</div>
                <div class="card-content">
                    <div class="card-value">{{ club_overview.active_strategies }}</div>
                    <div class="card-label">Active Strategies</div>
                    <div class="card-change">{{ club_overview.total_strategies }} total</div>
                </div>
            </div>
            
            <div class="overview-card">
                <div class="card-icon">📈</div>
                <div class="card-content">
                    <div class="card-value">{{ club_overview.total_trades }}</div>
                    <div class="card-label">Total Trades</div>
                    <div class="card-change">{{ club_overview.trades_this_month }} this month</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Metrics -->
    <section class="performance-metrics">
        <h2>📈 Performance Metrics</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>Club Performance</h3>
                <div class="metric-chart">
                    <canvas id="performance-chart"></canvas>
                </div>
                <div class="metric-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Return:</span>
                        <span class="stat-value {{ 'positive' if performance_metrics.total_return >= 0 else 'negative' }}">
                            {{ '+' if performance_metrics.total_return >= 0 else '' }}{{ performance_metrics.total_return|round(2) }}%
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Sharpe Ratio:</span>
                        <span class="stat-value">{{ performance_metrics.sharpe_ratio|round(2) }}</span>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <h3>Risk Metrics</h3>
                <div class="risk-metrics">
                    <div class="risk-item">
                        <span class="risk-label">Max Drawdown:</span>
                        <span class="risk-value negative">{{ risk_metrics.max_drawdown|round(2) }}%</span>
                    </div>
                    <div class="risk-item">
                        <span class="risk-label">Volatility:</span>
                        <span class="risk-value">{{ risk_metrics.volatility|round(2) }}%</span>
                    </div>
                    <div class="risk-item">
                        <span class="risk-label">VaR (95%):</span>
                        <span class="risk-value">{{ risk_metrics.var_95|round(2) }}%</span>
                    </div>
                    <div class="risk-item">
                        <span class="risk-label">Beta:</span>
                        <span class="risk-value">{{ risk_metrics.beta|round(2) }}</span>
                    </div>
                </div>
            </div>
            
            <div class="metric-card">
                <h3>Trading Activity</h3>
                <div class="activity-chart">
                    <canvas id="activity-chart"></canvas>
                </div>
                <div class="activity-stats">
                    <div class="stat-item">
                        <span class="stat-label">Win Rate:</span>
                        <span class="stat-value">{{ performance_metrics.win_rate|round(1) }}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Avg Trade:</span>
                        <span class="stat-value">${{ performance_metrics.avg_trade|round(2) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Analysis -->
    <section class="portfolio-analysis">
        <h2>💼 Portfolio Analysis</h2>
        <div class="portfolio-grid">
            <div class="portfolio-card">
                <h3>Asset Allocation</h3>
                <div class="allocation-chart">
                    <canvas id="allocation-chart"></canvas>
                </div>
                <div class="allocation-legend">
                    {% for allocation in portfolio_analysis.asset_allocation %}
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: {{ allocation.color }}"></div>
                        <span class="legend-label">{{ allocation.asset }}</span>
                        <span class="legend-value">{{ allocation.percentage|round(1) }}%</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="portfolio-card">
                <h3>Top Holdings</h3>
                <div class="holdings-list">
                    {% for holding in portfolio_analysis.top_holdings %}
                    <div class="holding-item">
                        <div class="holding-info">
                            <span class="holding-symbol">{{ holding.symbol }}</span>
                            <span class="holding-name">{{ holding.name }}</span>
                        </div>
                        <div class="holding-metrics">
                            <span class="holding-value">${{ holding.value|round(2) }}</span>
                            <span class="holding-percentage">{{ holding.percentage|round(1) }}%</span>
                            <span class="holding-change {{ 'positive' if holding.change >= 0 else 'negative' }}">
                                {{ '+' if holding.change >= 0 else '' }}{{ holding.change|round(2) }}%
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="portfolio-card">
                <h3>Diversification Score</h3>
                <div class="diversification-score">
                    <div class="score-circle">
                        <div class="score-value">{{ portfolio_analysis.diversification_score|round(0) }}</div>
                        <div class="score-label">/ 100</div>
                    </div>
                    <div class="score-description">
                        {% if portfolio_analysis.diversification_score >= 80 %}
                        <span class="score-status excellent">Excellent Diversification</span>
                        {% elif portfolio_analysis.diversification_score >= 60 %}
                        <span class="score-status good">Good Diversification</span>
                        {% elif portfolio_analysis.diversification_score >= 40 %}
                        <span class="score-status moderate">Moderate Diversification</span>
                        {% else %}
                        <span class="score-status poor">Poor Diversification</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Strategy Analytics -->
    <section class="strategy-analytics">
        <h2>🎯 Strategy Analytics</h2>
        <div class="strategy-performance-table">
            <table>
                <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Creator</th>
                        <th>Performance</th>
                        <th>Followers</th>
                        <th>Win Rate</th>
                        <th>Max Drawdown</th>
                        <th>Sharpe Ratio</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for strategy in strategy_analytics %}
                    <tr>
                        <td>
                            <div class="strategy-name">{{ strategy.name }}</div>
                            <div class="strategy-category">{{ strategy.category }}</div>
                        </td>
                        <td>{{ strategy.creator_name }}</td>
                        <td class="{{ 'positive' if strategy.performance >= 0 else 'negative' }}">
                            {{ '+' if strategy.performance >= 0 else '' }}{{ strategy.performance|round(2) }}%
                        </td>
                        <td>{{ strategy.followers_count }}</td>
                        <td>{{ strategy.win_rate|round(1) }}%</td>
                        <td class="negative">{{ strategy.max_drawdown|round(1) }}%</td>
                        <td>{{ strategy.sharpe_ratio|round(2) }}</td>
                        <td>
                            <span class="status-badge {{ 'active' if strategy.is_active else 'inactive' }}">
                                {{ 'Active' if strategy.is_active else 'Inactive' }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </section>

    <!-- Member Analytics -->
    <section class="member-analytics">
        <h2>👥 Member Analytics</h2>
        <div class="member-analytics-grid">
            <div class="analytics-card">
                <h3>Member Growth</h3>
                <div class="growth-chart">
                    <canvas id="member-growth-chart"></canvas>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3>Activity Heatmap</h3>
                <div class="activity-heatmap">
                    <canvas id="activity-heatmap"></canvas>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3>Top Contributors</h3>
                <div class="contributors-list">
                    {% for contributor in member_analytics.top_contributors %}
                    <div class="contributor-item">
                        <div class="contributor-rank">#{{ loop.index }}</div>
                        <div class="contributor-info">
                            <div class="contributor-name">{{ contributor.username }}</div>
                            <div class="contributor-contribution">{{ contributor.contribution_score }} points</div>
                        </div>
                        <div class="contributor-metrics">
                            <span class="performance {{ 'positive' if contributor.performance >= 0 else 'negative' }}">
                                {{ '+' if contributor.performance >= 0 else '' }}{{ contributor.performance|round(2) }}%
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>

    <!-- Growth Tracking -->
    <section class="growth-tracking">
        <h2>📊 Growth Tracking</h2>
        <div class="growth-metrics">
            <div class="growth-card">
                <h3>Monthly Growth Rate</h3>
                <div class="growth-value {{ 'positive' if growth_tracking.monthly_growth >= 0 else 'negative' }}">
                    {{ '+' if growth_tracking.monthly_growth >= 0 else '' }}{{ growth_tracking.monthly_growth|round(2) }}%
                </div>
                <div class="growth-comparison">
                    vs {{ growth_tracking.previous_month|round(2) }}% last month
                </div>
            </div>
            
            <div class="growth-card">
                <h3>Member Retention</h3>
                <div class="growth-value">{{ growth_tracking.retention_rate|round(1) }}%</div>
                <div class="growth-comparison">
                    {{ growth_tracking.active_members }} of {{ growth_tracking.total_members }} members active
                </div>
            </div>
            
            <div class="growth-card">
                <h3>Strategy Success Rate</h3>
                <div class="growth-value">{{ growth_tracking.strategy_success_rate|round(1) }}%</div>
                <div class="growth-comparison">
                    {{ growth_tracking.successful_strategies }} of {{ growth_tracking.total_strategies }} profitable
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/club_analytics.js"></script>
<script>
    // Initialize analytics data
    window.analyticsData = {
        clubOverview: {{ club_overview|tojson }},
        performanceMetrics: {{ performance_metrics|tojson }},
        portfolioAnalysis: {{ portfolio_analysis|tojson }},
        strategyAnalytics: {{ strategy_analytics|tojson }},
        memberAnalytics: {{ member_analytics|tojson }},
        riskMetrics: {{ risk_metrics|tojson }},
        growthTracking: {{ growth_tracking|tojson }}
    };

    // Initialize analytics
    document.addEventListener('DOMContentLoaded', function() {
        initializeClubAnalytics();
    });
</script>
{% endblock %}
