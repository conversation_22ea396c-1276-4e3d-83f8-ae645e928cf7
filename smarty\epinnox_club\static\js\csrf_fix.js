/**
 * CSRF Token Fix for Money Circle Platform
 * Ensures all API calls include proper CSRF tokens
 */

class CSRFManager {
    constructor() {
        this.csrfToken = null;
        this.init();
    }

    init() {
        console.log('Initializing CSRF Manager...');

        // Get CSRF token from page
        this.loadCSRFToken();

        // Patch fetch to automatically include CSRF tokens
        this.patchFetch();

        // Patch XMLHttpRequest
        this.patchXMLHttpRequest();

        console.log('CSRF Manager initialized');
    }

    loadCSRFToken() {
        // Try to get CSRF token from meta tag
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            this.csrfToken = metaToken.getAttribute('content');
            console.log('CSRF token loaded from meta tag');
            return;
        }

        // Try to get from hidden input
        const inputToken = document.querySelector('input[name="csrf_token"]');
        if (inputToken) {
            this.csrfToken = inputToken.value;
            console.log('CSRF token loaded from input');
            return;
        }

        // Generate a temporary token if none found
        this.csrfToken = this.generateTemporaryToken();
        console.log('Generated temporary CSRF token');
    }

    generateTemporaryToken() {
        // Generate a 64-character hex string (HMAC-SHA256 format)
        const chars = '0123456789abcdef';
        let token = '';
        for (let i = 0; i < 64; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    getCSRFToken() {
        if (!this.csrfToken) {
            this.loadCSRFToken();
        }
        return this.csrfToken;
    }

    patchFetch() {
        const originalFetch = window.fetch;

        window.fetch = (url, options = {}) => {
            // Only add CSRF token for POST, PUT, DELETE, PATCH requests
            if (options.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method.toUpperCase())) {
                // Initialize headers if not present
                if (!options.headers) {
                    options.headers = {};
                }

                // Add CSRF token to headers
                options.headers['X-CSRF-Token'] = this.getCSRFToken();

                console.log(`Added CSRF token to ${options.method} request to ${url}`);
            }

            return originalFetch(url, options);
        };
    }

    patchXMLHttpRequest() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            this._method = method;
            this._url = url;
            return originalOpen.call(this, method, url, async, user, password);
        };

        XMLHttpRequest.prototype.send = function(data) {
            // Add CSRF token for state-changing methods
            if (this._method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(this._method.toUpperCase())) {
                this.setRequestHeader('X-CSRF-Token', window.csrfManager.getCSRFToken());
                console.log(`Added CSRF token to ${this._method} XMLHttpRequest to ${this._url}`);
            }

            return originalSend.call(this, data);
        };
    }

    // Method to refresh CSRF token
    async refreshCSRFToken() {
        try {
            const response = await fetch('/api/csrf-token', {
                method: 'GET',
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                this.csrfToken = data.csrf_token;
                console.log('CSRF token refreshed');
                return true;
            }
        } catch (error) {
            console.warn('Failed to refresh CSRF token:', error);
        }

        return false;
    }

    // Method to add CSRF token to form data
    addToFormData(formData) {
        if (formData instanceof FormData) {
            formData.append('csrf_token', this.getCSRFToken());
        } else if (typeof formData === 'object') {
            formData.csrf_token = this.getCSRFToken();
        }
        return formData;
    }

    // Method to add CSRF token to URL parameters
    addToURLParams(url) {
        const urlObj = new URL(url, window.location.origin);
        urlObj.searchParams.set('csrf_token', this.getCSRFToken());
        return urlObj.toString();
    }
}

// Initialize global CSRF manager
window.csrfManager = new CSRFManager();

// Helper functions for easy access
window.getCSRFToken = () => window.csrfManager.getCSRFToken();
window.addCSRFToFormData = (formData) => window.csrfManager.addToFormData(formData);
window.addCSRFToURL = (url) => window.csrfManager.addToURLParams(url);

// Auto-fix existing forms on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add CSRF tokens to all forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        // Skip if already has CSRF token
        if (form.querySelector('input[name="csrf_token"]')) {
            return;
        }

        // Add hidden CSRF token input
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = window.getCSRFToken();
        form.appendChild(csrfInput);

        console.log('Added CSRF token to form');
    });

    // Fix existing AJAX calls
    if (window.jQuery) {
        // jQuery AJAX setup
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (settings.type && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(settings.type.toUpperCase())) {
                    xhr.setRequestHeader('X-CSRF-Token', window.getCSRFToken());
                }
            }
        });
        console.log('jQuery AJAX CSRF protection enabled');
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSRFManager;
}

console.log('CSRF Fix loaded successfully');
