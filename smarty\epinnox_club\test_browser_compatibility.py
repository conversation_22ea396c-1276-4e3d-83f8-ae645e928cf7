#!/usr/bin/env python3
"""
Money Circle Cross-Browser Compatibility Testing
Comprehensive testing framework for browser compatibility verification.
"""

import requests
import re
import json
import sys
from datetime import datetime
from urllib.parse import urljoin

# Test configuration
BASE_URL = "http://localhost:8085"

# Browser compatibility matrix
BROWSER_SUPPORT = {
    "chrome": {"min_version": 88, "css_grid": True, "flexbox": True, "custom_properties": True},
    "firefox": {"min_version": 85, "css_grid": True, "flexbox": True, "custom_properties": True},
    "safari": {"min_version": 14, "css_grid": True, "flexbox": True, "custom_properties": True},
    "edge": {"min_version": 88, "css_grid": True, "flexbox": True, "custom_properties": True},
    "ie": {"min_version": 11, "css_grid": False, "flexbox": True, "custom_properties": False}
}

# CSS features to test for compatibility
CSS_FEATURES = {
    "css_grid": {
        "properties": ["display: grid", "grid-template-columns", "grid-gap", "grid-column"],
        "fallback_needed": ["ie"],
        "critical": True
    },
    "flexbox": {
        "properties": ["display: flex", "flex-direction", "justify-content", "align-items"],
        "fallback_needed": [],
        "critical": True
    },
    "custom_properties": {
        "properties": ["--primary-600", "--bg-card", "var("],
        "fallback_needed": ["ie"],
        "critical": True
    },
    "backdrop_filter": {
        "properties": ["backdrop-filter: blur"],
        "fallback_needed": ["firefox", "ie"],
        "critical": False
    },
    "css_transforms": {
        "properties": ["transform:", "translateY", "scale("],
        "fallback_needed": [],
        "critical": False
    },
    "media_queries": {
        "properties": ["@media (min-width:", "@media (max-width:", "@media (hover:"],
        "fallback_needed": [],
        "critical": True
    }
}

# Pages to test for compatibility
TEST_PAGES = [
    {"url": "/login", "name": "Login Page", "auth_required": False},
    {"url": "/dashboard", "name": "Personal Dashboard", "auth_required": True},
    {"url": "/club", "name": "Club Dashboard", "auth_required": True},
    {"url": "/analytics", "name": "Analytics Dashboard", "auth_required": True},
    {"url": "/members", "name": "Member Directory", "auth_required": True},
    {"url": "/strategies", "name": "Strategy Marketplace", "auth_required": True},
    {"url": "/responsive_test.html", "name": "Responsive Test Page", "auth_required": False}
]

def analyze_css_compatibility():
    """Analyze CSS files for browser compatibility issues"""
    print("\n🔍 Analyzing CSS Browser Compatibility...")
    
    css_files = [
        "/static/css/design_system.css",
        "/static/css/dashboard.css",
        "/static/css/club.css",
        "/static/css/unified_header.css",
        "/static/css/unified_footer.css"
    ]
    
    compatibility_report = {}
    
    for css_file in css_files:
        try:
            response = requests.get(f"{BASE_URL}{css_file}", timeout=10)
            if response.status_code == 200:
                content = response.text
                file_report = {"features_used": [], "potential_issues": [], "fallbacks_needed": []}
                
                # Check for CSS features
                for feature_name, feature_info in CSS_FEATURES.items():
                    feature_found = False
                    for prop in feature_info["properties"]:
                        if prop in content:
                            feature_found = True
                            break
                    
                    if feature_found:
                        file_report["features_used"].append(feature_name)
                        
                        # Check if fallbacks are needed
                        if feature_info["fallback_needed"]:
                            file_report["fallbacks_needed"].extend(feature_info["fallback_needed"])
                        
                        # Check for critical features
                        if feature_info["critical"] and feature_info["fallback_needed"]:
                            file_report["potential_issues"].append(
                                f"Critical feature '{feature_name}' needs fallback for: {', '.join(feature_info['fallback_needed'])}"
                            )
                
                compatibility_report[css_file] = file_report
                
                print(f"  📄 {css_file}:")
                print(f"    ✅ Features used: {', '.join(file_report['features_used'])}")
                if file_report["potential_issues"]:
                    for issue in file_report["potential_issues"]:
                        print(f"    ⚠️  {issue}")
                
        except Exception as e:
            print(f"  ❌ Error analyzing {css_file}: {str(e)}")
    
    return compatibility_report

def check_html5_compatibility():
    """Check HTML5 features and semantic elements"""
    print("\n📝 Checking HTML5 Compatibility...")
    
    try:
        response = requests.get(f"{BASE_URL}/login", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for HTML5 semantic elements
            html5_elements = ["<main>", "<section>", "<article>", "<nav>", "<header>", "<footer>"]
            found_elements = [elem for elem in html5_elements if elem in content]
            
            # Check for HTML5 input types
            html5_inputs = ['type="email"', 'type="password"', 'type="number"']
            found_inputs = [inp for inp in html5_inputs if inp in content]
            
            print(f"  ✅ HTML5 semantic elements: {len(found_elements)}/6 found")
            print(f"  ✅ HTML5 input types: {len(found_inputs)} found")
            
            # Check DOCTYPE
            if "<!DOCTYPE html>" in content:
                print(f"  ✅ HTML5 DOCTYPE declared")
            else:
                print(f"  ⚠️  HTML5 DOCTYPE not found")
                
    except Exception as e:
        print(f"  ❌ Error checking HTML5 compatibility: {str(e)}")

def test_javascript_compatibility():
    """Test JavaScript features for browser compatibility"""
    print("\n🔧 Checking JavaScript Compatibility...")
    
    # Check for modern JavaScript features in static files
    js_features = {
        "es6_arrow_functions": "=>",
        "es6_const_let": ["const ", "let "],
        "es6_template_literals": "`",
        "async_await": ["async ", "await "],
        "fetch_api": "fetch(",
        "promises": ".then("
    }
    
    try:
        # Check if there are any JavaScript files to analyze
        response = requests.get(f"{BASE_URL}/dashboard", timeout=10)
        if response.status_code in [200, 302]:  # 302 for redirect to login
            content = response.text
            
            # Look for inline JavaScript
            js_blocks = re.findall(r'<script[^>]*>(.*?)</script>', content, re.DOTALL)
            
            if js_blocks:
                print(f"  📄 Found {len(js_blocks)} JavaScript blocks")
                
                for feature_name, patterns in js_features.items():
                    if isinstance(patterns, list):
                        found = any(any(pattern in block for block in js_blocks) for pattern in patterns)
                    else:
                        found = any(patterns in block for block in js_blocks)
                    
                    if found:
                        print(f"    ✅ {feature_name}: Found")
                    else:
                        print(f"    ℹ️  {feature_name}: Not detected")
            else:
                print(f"  ℹ️  No inline JavaScript found - external files may be used")
                
    except Exception as e:
        print(f"  ❌ Error checking JavaScript compatibility: {str(e)}")

def generate_browser_support_matrix():
    """Generate browser support matrix based on features used"""
    print("\n📊 Generating Browser Support Matrix...")
    
    compatibility_report = analyze_css_compatibility()
    
    # Analyze overall compatibility
    all_features_used = set()
    critical_issues = []
    
    for file_report in compatibility_report.values():
        all_features_used.update(file_report["features_used"])
        critical_issues.extend(file_report["potential_issues"])
    
    print(f"\n🎯 Browser Support Recommendations:")
    
    for browser, support_info in BROWSER_SUPPORT.items():
        compatible = True
        issues = []
        
        for feature in all_features_used:
            if feature in CSS_FEATURES:
                feature_info = CSS_FEATURES[feature]
                if browser in feature_info["fallback_needed"]:
                    if feature_info["critical"]:
                        compatible = False
                        issues.append(f"Critical feature '{feature}' not supported")
                    else:
                        issues.append(f"Feature '{feature}' needs fallback")
        
        status = "✅ Fully Compatible" if compatible else "⚠️  Needs Fallbacks"
        if browser == "ie":
            status = "❌ Limited Support"
        
        print(f"  {browser.upper()}: {status}")
        if issues:
            for issue in issues[:3]:  # Show first 3 issues
                print(f"    • {issue}")
            if len(issues) > 3:
                print(f"    • ... and {len(issues) - 3} more issues")

def test_responsive_breakpoints():
    """Test responsive design breakpoints across browsers"""
    print("\n📱 Testing Responsive Design Compatibility...")
    
    try:
        response = requests.get(f"{BASE_URL}/static/css/design_system.css", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Count media queries
            media_queries = re.findall(r'@media[^{]+{', content)
            print(f"  ✅ Found {len(media_queries)} media queries")
            
            # Check for modern media query features
            modern_features = {
                "hover_queries": "@media (hover:",
                "pointer_queries": "@media (pointer:",
                "prefers_reduced_motion": "@media (prefers-reduced-motion:",
                "orientation_queries": "@media (orientation:"
            }
            
            for feature_name, pattern in modern_features.items():
                if pattern in content:
                    print(f"    ✅ {feature_name}: Supported")
                else:
                    print(f"    ℹ️  {feature_name}: Not used")
            
            # Check viewport units
            viewport_units = ["vw", "vh", "vmin", "vmax"]
            found_units = [unit for unit in viewport_units if unit in content]
            
            if found_units:
                print(f"  ✅ Viewport units used: {', '.join(found_units)}")
                print(f"    ⚠️  Note: IE has limited viewport unit support")
            
    except Exception as e:
        print(f"  ❌ Error testing responsive compatibility: {str(e)}")

def generate_compatibility_report():
    """Generate comprehensive compatibility report"""
    print("\n📋 Generating Compatibility Report...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "css_compatibility": analyze_css_compatibility(),
        "recommendations": {
            "supported_browsers": ["Chrome 88+", "Firefox 85+", "Safari 14+", "Edge 88+"],
            "limited_support": ["Internet Explorer 11 (with fallbacks)"],
            "critical_features": ["CSS Grid", "Flexbox", "Custom Properties"],
            "fallback_strategies": [
                "Provide flexbox fallbacks for CSS Grid in IE11",
                "Use static values as fallbacks for CSS custom properties",
                "Consider progressive enhancement for backdrop-filter"
            ]
        }
    }
    
    return report

def main():
    """Run comprehensive cross-browser compatibility testing"""
    print("🌐 Money Circle Cross-Browser Compatibility Testing")
    print("=" * 60)
    print(f"Testing at: {BASE_URL}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all compatibility tests
    analyze_css_compatibility()
    check_html5_compatibility()
    test_javascript_compatibility()
    test_responsive_breakpoints()
    generate_browser_support_matrix()
    
    print("\n" + "=" * 60)
    print("🎯 Cross-Browser Compatibility Summary")
    print("=" * 60)
    
    print(f"\n✅ Recommended Browser Support:")
    print(f"  • Chrome 88+ (Full Support)")
    print(f"  • Firefox 85+ (Full Support)")
    print(f"  • Safari 14+ (Full Support)")
    print(f"  • Edge 88+ (Full Support)")
    print(f"  • IE 11 (Limited Support with fallbacks)")
    
    print(f"\n⚠️  Potential Compatibility Issues:")
    print(f"  • CSS Grid: Needs flexbox fallback for IE11")
    print(f"  • CSS Custom Properties: Needs static fallbacks for IE11")
    print(f"  • Backdrop Filter: Limited support in older Firefox")
    print(f"  • Modern Media Queries: Limited support in IE11")
    
    print(f"\n🔧 Next Steps:")
    print(f"  1. Test on actual browsers using developer tools")
    print(f"  2. Verify responsive design across browser engines")
    print(f"  3. Test touch interactions on mobile browsers")
    print(f"  4. Validate form functionality across browsers")
    print(f"  5. Check performance across different browser engines")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        sys.exit(1)
