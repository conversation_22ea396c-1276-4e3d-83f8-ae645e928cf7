# Smart Trader System

A comprehensive trading system for HTX Futures that:
- Streams real-time market data
- Processes it through ML models and a local LLM
- Fuses outputs into trading decisions
- Executes orders securely

## Architecture

The system follows a modular architecture:

```
smart_trader/
├── core/                # Pure, framework-agnostic logic
│   ├── events.py        # Dataclasses for Kline, Trade, Position, Signal, FundingRate, OpenInterest…
│   ├── feature_store.py # In-memory + optional Redis cache
│   ├── rule_engine.py   # Position sizing, risk checks
│   └── utils.py         # Utility functions
├── feeds/               # Exchange adapters
│   ├── htx_futures.py   # REST + WS client (async)
│   ├── htx_funding.py   # Funding rate client
│   └── htx_open_interest.py # Open interest client
├── clients/             # External API clients
│   └── signalstar_client.py # Social sentiment API client
├── models/              # Non-LLM models
│   ├── rsi.py           # RSI-based model
│   ├── orderflow_net.py # Neural network for order flow analysis
│   ├── vwap_deviation.py # VWAP-based mean reversion/breakout detector
│   ├── liquidity_imbalance.py # Order book imbalance analyzer
│   ├── garch_volatility.py # GARCH-style volatility forecaster
│   ├── funding_momentum.py # Funding rate momentum tracker
│   ├── open_interest_momentum.py # Open interest momentum analyzer
│   └── social_sentiment.py # Social sentiment analyzer
├── llm/                 # All LLM-related glue
│   ├── prompts/         # Prompt templates
│   ├── enhanced_llm_manager.py    # Enhanced LLM engine
│   └── enhanced_llm_consumer.py   # Enhanced LLM consumer
├── executors/           # Order execution
│   └── htx_executor.py  # Sends orders, confirms fills
├── orchestrator.py      # High-level async "conductor"
└── tests/               # Unit tests
```

## Features

- **Real-time Data Processing**: Connects to HTX Futures WebSocket for live market data
- **Feature Store**: Maintains time series data, derived features, and model predictions
- **ML Models**: Includes RSI, OrderFlow, VWAP-Deviation, Liquidity Imbalance, GARCH Volatility, Funding Momentum, Open Interest Momentum, and Social Sentiment models
- **LLM Integration**: Uses llama.cpp for local LLM inference
- **Signal Fusion**: Combines signals from multiple sources with adaptive weights based on market conditions
- **Risk Management**: Implements position sizing and risk checks
- **Simulation Mode**: Supports paper trading for testing strategies
- **Observability**: Comprehensive logging for debugging and analysis

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/smart-trader.git
cd smart-trader
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

### Dependencies

The system requires the following key dependencies:

- **Core Dependencies**:
  - `aiohttp`: For async HTTP requests and WebSocket connections
  - `numpy` and `pandas`: For data manipulation and analysis
  - `pyyaml`: For configuration and prompt templates
  - `llama-cpp-python`: For local LLM inference

- **Model-Specific Dependencies**:
  - `arch` or `statsmodels`: For GARCH volatility modeling
  - `torch`: For neural network models (OrderFlow Net)
  - `scikit-learn`: For various ML utilities

- **Optional Dependencies**:
  - `redis`: For persistent feature store
  - `matplotlib`: For visualization and debugging
  - `pytest`: For testing

3. Download LLM model (optional):
```bash
mkdir -p models
# Download a GGUF model from https://huggingface.co/TheBloke
# For example:
# wget https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf -O models/llama-2-7b-chat.Q4_K_M.gguf
```

## Configuration

Edit the configuration in `orchestrator.py` to set your API keys, trading parameters, and model paths:

```python
config = {
    "symbols": ["BTC-USDT"],
    "api_key": "your_api_key",
    "api_secret": "your_api_secret",
    "testnet": True,
    "trading_enabled": False,
    "simulation_mode": True,

    # Model toggles
    "enable_rsi_model": True,
    "enable_orderflow_model": True,
    "enable_volatility_regime_model": True,
    "enable_vwap_deviation_model": True,
    "enable_liquidity_imbalance_model": True,
    "enable_garch_volatility_model": True,
    "enable_funding_momentum_model": True,
    "enable_open_interest_momentum_model": True,
    "enable_social_sentiment_model": True,

    # Model-specific configurations
    "vwap_deviation_config": {
        "lookback_periods": 100,
        "significant_deviation": 2.0,
        "vwap_types": ["1min", "5min", "15min", "1h"]
    },

    "liquidity_imbalance_config": {
        "window_size": 20,
        "depth_levels": 5,
        "significant_imbalance": 0.3,
        "decay_factor": 0.9
    },

    "garch_volatility_config": {
        "lookback_periods": 100,
        "update_interval": 300,  # 5 minutes in seconds
        "model_type": "GARCH",  # "GARCH", "EGARCH", or "GJR-GARCH"
    },

    "funding_momentum_config": {
        "short_window": 5,      # 5 minutes
        "long_window": 60,      # 60 minutes
        "signal_threshold": 1.0,
        "contrarian": True
    },

    "open_interest_momentum_config": {
        "delta_window": 5,      # 5 minutes
        "z_score_window": 60,   # 60 minutes
        "threshold_z": 1.0,
        "mode": "contrarian",   # "trend" or "contrarian"
        "boost_in_low_volatility": True
    },

    "social_sentiment_config": {
        "delta_window": 5,      # 5 minutes
        "z_window": 60,         # 60 minutes
        "threshold": 1.2,       # Z-score threshold for signals
        "contrarian": True,     # Whether to use contrarian signals
        "symbol": "BTC"         # Default symbol for sentiment data
    },

    "signalstar": {
        "api_key": "your_signalstar_api_key",
        "base_url": "https://api.signalstar.com/v1"
    },

    # ... other parameters
}
```

## Data Flow and Feature Store

The system follows a structured data flow:

1. **Data Ingestion**:
   - Market data from HTX WebSocket (trades, orderbook, klines)
   - Funding rates from HTX REST API (every minute)
   - Open interest from HTX REST API (every minute)
   - Social sentiment data from SignalStar API (every minute)

2. **Feature Store**:
   - In-memory storage with optional Redis persistence
   - Maintains raw data, derived features, and model predictions
   - Supports time series data with timestamps
   - Provides efficient access patterns for models

3. **Feature Calculation**:
   - Computes technical indicators (RSI, VWAP, etc.)
   - Calculates derived features (deltas, z-scores, etc.)
   - Normalizes features for model consumption

4. **Model Execution**:
   - Models consume features from the feature store
   - Predictions are stored back in the feature store
   - Models run at configurable intervals

5. **Signal Generation**:
   - Rule engine validates and fuses model signals
   - LLM provides high-level analysis and recommendations
   - Final signals trigger order execution

## Usage

Run the orchestrator:

```bash
python orchestrator.py
```

For testing:

```bash
python -m unittest discover tests
```

## Simulation Mode

By default, the system runs in simulation mode, which:
- Simulates order execution
- Tracks virtual positions and balance
- Calculates PnL
- Does not require API keys

To enable real trading:
1. Set `"trading_enabled": True`
2. Set `"simulation_mode": False`
3. Provide valid API keys

## LLM Integration

The system uses llama.cpp to run a local LLM for trading decisions. You can:
- Use different GGUF models
- Adjust the prompt template in `llm/prompts/trading_prompt.yaml`
- Configure inference parameters like temperature and context size

The LLM prompt includes sections for each model's outputs, which are conditionally included based on available data:

```yaml
# Example from trading_prompt.yaml
{% if volatility_forecast is defined %}

Volatility forecast:
  Level: {volatility_level}
  Z-score: {volatility_z}
  Position sizing: {position_size_multiplier}x
{% endif %}

{% if open_interest is defined %}

Open Interest Momentum:
  Current OI: {open_interest}
  Δ over {open_interest_window}m: {open_interest_delta}
  Z-score: {open_interest_delta_z}
  Signal: {open_interest_signal} (mode: {open_interest_mode})
{% endif %}

{% if social_sentiment is defined %}

Social Sentiment:
  Current: {social_sentiment}
  Δ: {social_delta}
  Z-score: {social_z_score}
  Signal: {social_signal} (mode: {% if social_contrarian %}contrarian{% else %}trend{% endif %})
{% endif %}
```

## Signal Fusion and Rule Engine

The system uses a sophisticated signal fusion algorithm that:

1. **Adaptive Weighting**: Adjusts model weights based on:
   - Current market volatility regime
   - Liquidity conditions
   - Historical model performance
   - Signal strength and confidence

2. **Cross-Model Interactions**:
   - Reduces VWAP weight when liquidity is thin
   - Boosts Open Interest signals in low volatility
   - Reduces Funding Momentum weight in high volatility

3. **Signal Validation Rules**:
   - Prevents trading against strong funding momentum
   - Requires stronger signals when volatility is high
   - Validates signals against multiple models
   - Implements mode-specific validation for contrarian vs. trend models

4. **Position Sizing**:
   - Uses GARCH volatility forecasts to adjust position size
   - Reduces size in high volatility environments
   - Increases size in low volatility when signals are strong
   - Adjusts stop widths based on volatility level

## Extending the System

### Adding New Models

1. Create a new model file in the `models/` directory
2. Implement the `predict` method as defined in the `BaseModel` protocol
3. Add the model to the orchestrator's `registered_models` dictionary
4. Update the LLM prompt template to include the model's outputs
5. Update the rule engine to handle the model's signals

Here's a step-by-step guide for adding a new model:

```python
# 1. Create a new model file in models/
# Example: models/my_new_model.py
class MyNewModel:
    def __init__(self, config=None):
        self.config = config or {}

    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        # Process features and return prediction
        return {
            'key_metric': 42.0,
            'confidence': 0.85,
            'action': 'BUY'  # Optional trading action
        }

# 2. Register the model in orchestrator.py
# In the _init_components method:
if self.config.get("enable_my_new_model", False):
    from models.my_new_model import MyNewModel
    self.my_new_model = MyNewModel(
        config=self.config.get("my_new_model_config", {})
    )
    self.registered_models["my_new_model"] = self.my_new_model

# 3. Update the LLM prompt template (llm/prompts/trading_prompt.yaml)
# Add a new section:
{% if my_key_metric is defined %}

My New Model:
  Key Metric: {my_key_metric}
  Confidence: {my_confidence}
{% endif %}

# 4. Update the _create_signal method to add the model's outputs to the LLM context
if "my_new_model" in model_predictions:
    my_pred = model_predictions["my_new_model"]
    context["my_key_metric"] = my_pred.get("key_metric", 0.0)
    context["my_confidence"] = my_pred.get("confidence", 0.0)
```

### Implemented Models

The system includes the following models:

1. **RSI Model**
   - Classic Relative Strength Index indicator
   - Identifies overbought and oversold conditions

2. **OrderFlow Net**
   - Neural network for order flow analysis
   - Predicts short-term price movements based on trade patterns

3. **Volatility Regime Classifier**
   - Classifies market into LOW_VOL, NORMAL, or HIGH_VOL regimes
   - Adjusts position sizing and risk parameters accordingly

4. **VWAP-Deviation Detector**
   - Captures when price moves away from the intraday average
   - Provides mean-reversion or breakout signals with z-scores
   - Feeds into both rule engine (position sizing) and LLM context

5. **Liquidity Imbalance Ratio**
   - Analyzes order book data to detect imbalances between bids and asks
   - Identifies potential short squeezes or dump runs
   - Weights signals based on market thinness

6. **GARCH-style Volatility Forecaster**
   - Produces rolling volatility estimates using GARCH(1,1) model
   - Provides volatility z-scores for dynamic position sizing
   - Adjusts stop widths based on volatility regime

7. **Funding-Rate Momentum**
   - Tracks shifts in funding rate as a contrarian or trend signal
   - Calculates z-scores of funding rate changes
   - Particularly valuable on HTX where funding rates provide alpha

8. **Open-Interest Momentum**
   - Tracks changes in futures open interest
   - Supports both trend-following and contrarian approaches
   - Boosts signal weight in low volatility environments

9. **Social Sentiment Analyzer**
   - Monitors social media sentiment via SignalStar API
   - Calculates z-scores of sentiment changes
   - Supports both contrarian and trend-following modes
   - Provides early warning of retail crowd behavior

### Recommended Models to Add

Consider implementing these additional models:

2. **Meta-Ensemble**
   - Combines all model outputs using Bayesian averaging
   - Updates weights based on recent performance
   - Provides a unified signal with confidence intervals

### Supporting New Exchanges

1. Create a new exchange adapter in the `feeds/` directory
2. Implement WebSocket and REST clients
3. Create a new executor in the `executors/` directory

## License

MIT
