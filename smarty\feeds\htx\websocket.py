"""
HTX Futures WebSocket connection management.
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime
from typing import Dict, Any, Optional, Set, Callable, Awaitable

import aiohttp

from core.utils import utc_timestamp, generate_signature
from .constants import (
    WS_MARKET_URL, WS_PRIVATE_URL,
    TESTNET_WS_MARKET_URL, TESTNET_WS_PRIVATE_URL,
    ERROR_MESSAGES, HEALTH_CHECK
)
from .types import (
    ConnectionState, MessageType, HTXConfig, ConnectionMetrics,
    ConnectionError, AuthenticationError, MessageHandler
)

logger = logging.getLogger(__name__)


class WebSocketManager:
    """
    Manages WebSocket connections with robust error handling and reconnection.
    """

    def __init__(
        self,
        config: HTXConfig,
        session: Optional[aiohttp.ClientSession] = None
    ):
        """
        Initialize WebSocket manager.

        Args:
            config: HTX configuration
            session: aiohttp session for connections
        """
        self.config = config
        self.session = session

        # Connection state
        self.market_ws: Optional[aiohttp.ClientWebSocketResponse] = None
        self.private_ws: Optional[aiohttp.ClientWebSocketResponse] = None
        self.market_state = ConnectionState.DISCONNECTED
        self.private_state = ConnectionState.DISCONNECTED

        # Locks for thread safety
        self.market_lock = asyncio.Lock()
        self.private_lock = asyncio.Lock()

        # Task management
        self.market_tasks: Set[asyncio.Task] = set()
        self.private_tasks: Set[asyncio.Task] = set()

        # Reconnection management
        self.reconnecting = False
        self.reconnect_attempts = 0

        # Metrics
        self.metrics = ConnectionMetrics()

        # Message handlers
        self.message_handlers: Dict[str, MessageHandler] = {}

        # Ping/pong tracking
        self.last_ping_time = 0.0
        self.last_pong_time = 0.0
        self.missed_pongs = 0

        # Subscriptions tracking
        self.subscriptions: Set[str] = set()

    def _get_market_url(self) -> str:
        """Get the appropriate market WebSocket URL based on testnet mode."""
        return TESTNET_WS_MARKET_URL if self.config.testnet else WS_MARKET_URL

    def _get_private_url(self) -> str:
        """Get the appropriate private WebSocket URL based on testnet mode."""
        return TESTNET_WS_PRIVATE_URL if self.config.testnet else WS_PRIVATE_URL

    async def connect_market(self) -> None:
        """Connect to market data WebSocket."""
        if self.market_state in [ConnectionState.CONNECTED, ConnectionState.CONNECTING]:
            return

        self.market_state = ConnectionState.CONNECTING

        try:
            if self.config.debug:
                logger.debug("Connecting to HTX market WebSocket...")

            market_url = self._get_market_url()
            if self.config.debug:
                logger.debug(f"Connecting to market WebSocket: {market_url}")

            self.market_ws = await self.session.ws_connect(
                market_url,
                timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
            )

            self.market_state = ConnectionState.CONNECTED
            self.metrics.connected_at = datetime.now()
            self.reconnect_attempts = 0

            # Start management tasks
            await self._start_market_tasks()

            logger.info("Connected to HTX market WebSocket")

        except Exception as e:
            self.market_state = ConnectionState.ERROR
            self.metrics.last_error = str(e)
            self.metrics.error_count += 1
            logger.error(f"Failed to connect to market WebSocket: {e}")
            raise ConnectionError(f"Market WebSocket connection failed: {e}")

    async def connect_private(self) -> None:
        """Connect and authenticate private WebSocket."""
        if not self.config.api_key or not self.config.api_secret:
            logger.warning("No API credentials provided, skipping private WebSocket")
            return

        if self.private_state in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]:
            return

        self.private_state = ConnectionState.CONNECTING

        try:
            if self.config.debug:
                logger.debug("Connecting to HTX private WebSocket...")

            private_url = self._get_private_url()
            if self.config.debug:
                logger.debug(f"Connecting to private WebSocket: {private_url}")

            self.private_ws = await self.session.ws_connect(
                private_url,
                timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
            )

            self.private_state = ConnectionState.CONNECTED

            # Authenticate
            await self._authenticate()

            # Start management tasks
            await self._start_private_tasks()

            logger.info("Connected and authenticated to HTX private WebSocket")

        except Exception as e:
            self.private_state = ConnectionState.ERROR
            self.metrics.last_error = str(e)
            self.metrics.error_count += 1
            logger.error(f"Failed to connect to private WebSocket: {e}")
            raise ConnectionError(f"Private WebSocket connection failed: {e}")

    async def _authenticate(self) -> None:
        """Authenticate with the private WebSocket."""
        timestamp = str(int(time.time()))
        message = f"AccessKeyId={self.config.api_key}&SignatureMethod=HmacSHA256&SignatureVersion=2&Timestamp={timestamp}"
        signature = generate_signature(self.config.api_secret, message)

        auth_message = {
            "op": "auth",
            "AccessKeyId": self.config.api_key,
            "SignatureMethod": "HmacSHA256",
            "SignatureVersion": "2",
            "Timestamp": timestamp,
            "Signature": signature
        }

        await self._send_private_message(auth_message)

        # Wait for auth response with timeout
        try:
            response = await asyncio.wait_for(
                self.private_ws.receive_json(),
                timeout=10.0
            )

            if response.get("op") == "auth" and response.get("err-code") == 0:
                self.private_state = ConnectionState.AUTHENTICATED
                logger.info("Successfully authenticated with HTX private WebSocket")
            else:
                error_msg = response.get("err-msg", "Unknown authentication error")
                raise AuthenticationError(f"Authentication failed: {error_msg}")

        except asyncio.TimeoutError:
            raise AuthenticationError("Authentication timeout")

    async def _start_market_tasks(self) -> None:
        """Start market WebSocket management tasks."""
        # Clear old tasks
        await self._cleanup_tasks(self.market_tasks)

        # Start new tasks
        ping_task = asyncio.create_task(self._ping_market_loop())
        handler_task = asyncio.create_task(self._handle_market_messages())
        watchdog_task = asyncio.create_task(self._market_watchdog())

        self.market_tasks.update([ping_task, handler_task, watchdog_task])

    async def _start_private_tasks(self) -> None:
        """Start private WebSocket management tasks."""
        # Clear old tasks
        await self._cleanup_tasks(self.private_tasks)

        # Start new tasks
        ping_task = asyncio.create_task(self._ping_private_loop())
        handler_task = asyncio.create_task(self._handle_private_messages())

        self.private_tasks.update([ping_task, handler_task])

    async def _cleanup_tasks(self, tasks: Set[asyncio.Task]) -> None:
        """Clean up asyncio tasks."""
        for task in tasks.copy():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            tasks.discard(task)

    async def _ping_market_loop(self) -> None:
        """Send periodic pings to market WebSocket."""
        while self.market_state == ConnectionState.CONNECTED:
            try:
                if self.market_ws and not self.market_ws.closed:
                    ping_msg = {"ping": utc_timestamp()}
                    await self._send_market_message(ping_msg)
                    self.last_ping_time = time.time()
                    self.metrics.last_ping_sent = datetime.now()

                    if self.config.debug:
                        logger.debug("Sent market ping")

                await asyncio.sleep(self.config.ping_interval)

            except Exception as e:
                logger.error(f"Error in market ping loop: {e}")
                await asyncio.sleep(5)

    async def _ping_private_loop(self) -> None:
        """Send periodic pings to private WebSocket."""
        while self.private_state == ConnectionState.AUTHENTICATED:
            try:
                if self.private_ws and not self.private_ws.closed:
                    ping_msg = {"op": "ping", "ts": utc_timestamp()}
                    await self._send_private_message(ping_msg)
                    self.last_ping_time = time.time()

                    if self.config.debug:
                        logger.debug("Sent private ping")

                await asyncio.sleep(self.config.ping_interval)

            except Exception as e:
                logger.error(f"Error in private ping loop: {e}")
                await asyncio.sleep(5)

    async def _market_watchdog(self) -> None:
        """Monitor market WebSocket health."""
        while self.market_state == ConnectionState.CONNECTED:
            try:
                current_time = time.time()

                # Check for missed pongs
                if (self.last_ping_time > 0 and
                    current_time - self.last_pong_time > HEALTH_CHECK["ping_timeout"]):
                    self.missed_pongs += 1

                    if self.missed_pongs >= HEALTH_CHECK["max_missed_pongs"]:
                        logger.warning("Too many missed pongs, forcing reconnection")
                        await self.reconnect_market()
                        break

                await asyncio.sleep(HEALTH_CHECK["connection_check_interval"])

            except Exception as e:
                logger.error(f"Error in market watchdog: {e}")
                await asyncio.sleep(10)

    async def _send_market_message(self, message: Dict[str, Any]) -> None:
        """Send message to market WebSocket with error handling."""
        async with self.market_lock:
            if self.market_ws and not self.market_ws.closed:
                await self.market_ws.send_json(message)
                self.metrics.messages_sent += 1
            else:
                raise ConnectionError("Market WebSocket not connected")

    async def _send_private_message(self, message: Dict[str, Any]) -> None:
        """Send message to private WebSocket with error handling."""
        async with self.private_lock:
            if self.private_ws and not self.private_ws.closed:
                await self.private_ws.send_json(message)
                self.metrics.messages_sent += 1
            else:
                raise ConnectionError("Private WebSocket not connected")

    async def _handle_market_messages(self) -> None:
        """Handle incoming market WebSocket messages."""
        while self.market_state == ConnectionState.CONNECTED:
            try:
                if not self.market_ws or self.market_ws.closed:
                    break

                async with self.market_lock:
                    msg = await asyncio.wait_for(
                        self.market_ws.receive(),
                        timeout=self.config.request_timeout
                    )

                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    await self._process_market_message(data)

                elif msg.type == aiohttp.WSMsgType.BINARY:
                    # Handle compressed data
                    import gzip
                    decompressed = gzip.decompress(msg.data)
                    data = json.loads(decompressed)
                    await self._process_market_message(data)

                elif msg.type in [aiohttp.WSMsgType.CLOSED, aiohttp.WSMsgType.ERROR]:
                    logger.warning("Market WebSocket connection closed/error")
                    break

            except asyncio.TimeoutError:
                logger.warning("Market WebSocket receive timeout")
                continue
            except Exception as e:
                logger.error(f"Error handling market message: {e}")
                await asyncio.sleep(1)

    async def _handle_private_messages(self) -> None:
        """Handle incoming private WebSocket messages."""
        while self.private_state == ConnectionState.AUTHENTICATED:
            try:
                if not self.private_ws or self.private_ws.closed:
                    break

                async with self.private_lock:
                    msg = await asyncio.wait_for(
                        self.private_ws.receive(),
                        timeout=self.config.request_timeout
                    )

                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    await self._process_private_message(data)

                elif msg.type == aiohttp.WSMsgType.BINARY:
                    # Handle compressed data
                    import gzip
                    decompressed = gzip.decompress(msg.data)
                    data = json.loads(decompressed)
                    await self._process_private_message(data)

                elif msg.type in [aiohttp.WSMsgType.CLOSED, aiohttp.WSMsgType.ERROR]:
                    logger.warning("Private WebSocket connection closed/error")
                    break

            except asyncio.TimeoutError:
                logger.warning("Private WebSocket receive timeout")
                continue
            except Exception as e:
                logger.error(f"Error handling private message: {e}")
                await asyncio.sleep(1)

    async def _process_market_message(self, data: Dict[str, Any]) -> None:
        """Process market data message."""
        self.metrics.messages_received += 1

        # Handle pong responses
        if "pong" in data:
            self.last_pong_time = time.time()
            self.metrics.last_pong_received = datetime.now()
            self.missed_pongs = 0

            if self.config.debug:
                logger.debug("Received market pong")
            return

        # Handle subscription confirmations
        if "subbed" in data:
            if self.config.debug:
                logger.debug(f"Market subscription confirmed: {data}")
            return

        # Handle data messages
        if "ch" in data and "tick" in data:
            handler = self.message_handlers.get("market_data")
            if handler:
                await handler(data)

    async def _process_private_message(self, data: Dict[str, Any]) -> None:
        """Process private data message."""
        self.metrics.messages_received += 1

        # Handle pong responses
        if data.get("op") == "pong":
            self.last_pong_time = time.time()
            self.metrics.last_pong_received = datetime.now()

            if self.config.debug:
                logger.debug("Received private pong")
            return

        # Handle notification messages
        if data.get("op") == "notify":
            handler = self.message_handlers.get("private_data")
            if handler:
                await handler(data)

    async def subscribe(self, channel: str) -> bool:
        """Subscribe to a WebSocket channel."""
        if channel.startswith("private."):
            return await self._subscribe_private(channel)
        else:
            return await self._subscribe_market(channel)

    async def _subscribe_market(self, channel: str) -> bool:
        """Subscribe to market channel."""
        if self.market_state != ConnectionState.CONNECTED:
            logger.error("Cannot subscribe: market WebSocket not connected")
            return False

        sub_message = {
            "sub": channel,
            "id": str(int(time.time()))
        }

        try:
            await self._send_market_message(sub_message)
            self.subscriptions.add(channel)

            if self.config.debug:
                logger.debug(f"Subscribed to market channel: {channel}")
            return True

        except Exception as e:
            logger.error(f"Failed to subscribe to market channel {channel}: {e}")
            return False

    async def _subscribe_private(self, channel: str) -> bool:
        """Subscribe to private channel."""
        if self.private_state != ConnectionState.AUTHENTICATED:
            logger.error("Cannot subscribe: private WebSocket not authenticated")
            return False

        sub_message = {
            "op": "sub",
            "topic": channel.replace("private.", "")
        }

        try:
            await self._send_private_message(sub_message)
            self.subscriptions.add(channel)

            if self.config.debug:
                logger.debug(f"Subscribed to private channel: {channel}")
            return True

        except Exception as e:
            logger.error(f"Failed to subscribe to private channel {channel}: {e}")
            return False

    async def reconnect_market(self) -> None:
        """Reconnect market WebSocket."""
        if self.reconnecting:
            return

        self.reconnecting = True

        try:
            logger.info("Reconnecting market WebSocket...")

            # Close existing connection
            if self.market_ws and not self.market_ws.closed:
                await self.market_ws.close()

            # Clean up tasks
            await self._cleanup_tasks(self.market_tasks)

            # Reconnect with backoff
            await self._reconnect_with_backoff(self.connect_market)

            # Resubscribe to channels
            for channel in self.subscriptions.copy():
                if not channel.startswith("private."):
                    await self._subscribe_market(channel)

            self.metrics.reconnect_count += 1
            logger.info("Market WebSocket reconnected successfully")

        except Exception as e:
            logger.error(f"Failed to reconnect market WebSocket: {e}")
        finally:
            self.reconnecting = False

    async def _reconnect_with_backoff(self, connect_func: Callable) -> None:
        """Reconnect with exponential backoff."""
        delay = 1.0

        while self.reconnect_attempts < 10:  # Max attempts
            try:
                await connect_func()
                self.reconnect_attempts = 0
                return

            except Exception as e:
                self.reconnect_attempts += 1

                # Calculate backoff delay with jitter
                backoff = min(delay * (self.config.reconnect_backoff_factor ** self.reconnect_attempts),
                             self.config.max_reconnect_delay)
                jitter = random.uniform(0, 0.1 * backoff)
                total_delay = backoff + jitter

                logger.warning(f"Reconnect attempt {self.reconnect_attempts} failed: {e}. "
                             f"Retrying in {total_delay:.2f}s")
                await asyncio.sleep(total_delay)

        raise ConnectionError("Max reconnection attempts exceeded")

    def set_message_handler(self, message_type: str, handler: MessageHandler) -> None:
        """Set message handler for specific message type."""
        self.message_handlers[message_type] = handler

    async def close(self) -> None:
        """Close all WebSocket connections and clean up."""
        logger.info("Closing HTX WebSocket connections...")

        # Clean up tasks
        await self._cleanup_tasks(self.market_tasks)
        await self._cleanup_tasks(self.private_tasks)

        # Close connections
        if self.market_ws and not self.market_ws.closed:
            await self.market_ws.close()

        if self.private_ws and not self.private_ws.closed:
            await self.private_ws.close()

        self.market_state = ConnectionState.DISCONNECTED
        self.private_state = ConnectionState.DISCONNECTED

        logger.info("HTX WebSocket connections closed")
