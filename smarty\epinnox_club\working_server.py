#!/usr/bin/env python3
"""
Working Money Circle Server
Simple server that actually starts and stays running
"""

import asyncio
import logging
import signal
import sys
from aiohttp import web

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingServer:
    def __init__(self):
        self.app = None
        self.runner = None
        self.site = None
        self.shutdown_event = asyncio.Event()
    
    async def create_app(self):
        """Create the Money Circle app."""
        logger.info("🏗️ Creating Money Circle application...")
        
        from app import MoneyCircleApp
        app_instance = MoneyCircleApp('development')
        
        # Create web app
        self.app = await app_instance.create_app()
        logger.info("✅ Money Circle application created")
        
        return self.app
    
    async def start_server(self):
        """Start the web server."""
        logger.info("🚀 Starting web server...")
        
        # Create app if not already created
        if not self.app:
            await self.create_app()
        
        # Setup runner
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        # Start site
        self.site = web.TCPSite(self.runner, 'localhost', 8087)
        await self.site.start()
        
        logger.info("✅ Money Circle server started successfully!")
        logger.info("📍 Server running at: http://localhost:8087")
        logger.info("🔐 Login: epinnox / securepass123")
        logger.info("💰 Live Trading: http://localhost:8087/live-trading")
        logger.info("🛑 Press Ctrl+C to stop")
    
    async def stop_server(self):
        """Stop the web server."""
        logger.info("🛑 Stopping server...")
        
        if self.site:
            await self.site.stop()
        
        if self.runner:
            await self.runner.cleanup()
        
        logger.info("✅ Server stopped")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self):
        """Run the server."""
        try:
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Start server
            await self.start_server()
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Stop server
            await self.stop_server()
            
        except Exception as e:
            logger.error(f"❌ Server error: {e}")
            raise

async def main():
    """Main function."""
    server = WorkingServer()
    
    try:
        await server.run()
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    except Exception as e:
        logger.error(f"❌ Server failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    print("💰 Money Circle Investment Club Platform")
    print("🚀 Working Server Starter")
    print("=" * 50)
    
    try:
        sys.exit(asyncio.run(main()))
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
