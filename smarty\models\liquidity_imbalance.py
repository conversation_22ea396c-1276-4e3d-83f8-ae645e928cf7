"""
Liquidity Imbalance Ratio model for the smart-trader system.

This model analyzes order book data to detect imbalances between bids and asks,
which can precede short squeezes or dump runs.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Deque
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from collections import deque

from core.events import OrderbookDelta, OrderbookLevel
from core.utils import timer

logger = logging.getLogger(__name__)


class ImbalanceSignal(str, Enum):
    """Liquidity imbalance signal enum."""
    BID_DOMINATED = "BID_DOMINATED"    # More bids than asks (bullish)
    BALANCED = "BALANCED"              # Roughly equal bids and asks
    ASK_DOMINATED = "ASK_DOMINATED"    # More asks than bids (bearish)


class LiquidityImbalanceModel:
    """
    Liquidity Imbalance Ratio model.

    This model analyzes order book data to detect imbalances between bids and asks,
    which can precede short squeezes or dump runs.

    It calculates:
    - Bid/ask ratio at different price levels
    - Weighted imbalance based on proximity to mid price
    - Rolling average of imbalance to detect trends
    """

    def __init__(
        self,
        config: Dict[str, Any] = None,
        window_size: int = 20,
        depth_levels: int = 5,
        significant_imbalance: float = 0.3,  # Threshold for significant imbalance
        decay_factor: float = 0.9  # Decay factor for weighting by level
    ):
        """
        Initialize the Liquidity Imbalance model.

        Args:
            config: Configuration dictionary
            window_size: Size of the rolling window for imbalance calculation
            depth_levels: Number of order book levels to consider
            significant_imbalance: Threshold for significant imbalance
            decay_factor: Decay factor for weighting by level
        """
        self.config = config or {}
        self.window_size = self.config.get("window_size", window_size)
        self.depth_levels = self.config.get("depth_levels", depth_levels)
        self.significant_imbalance = self.config.get("significant_imbalance", significant_imbalance)
        self.decay_factor = self.config.get("decay_factor", decay_factor)

        # Cache for imbalance values
        self._imbalance_history: Dict[str, Deque[float]] = {}
        self._last_update: Dict[str, datetime] = {}
        self._last_orderbook: Dict[str, OrderbookDelta] = {}

    @timer("liquidity_imbalance_predict")
    async def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a prediction based on input features.

        Args:
            features: Dictionary of input features including:
                - 'symbol': Trading symbol
                - 'orderbook': OrderbookDelta object
                - 'timestamp': Current timestamp

        Returns:
            Dictionary of prediction results including:
                - 'imbalance_ratio': Current imbalance ratio (-1 to 1)
                - 'rolling_imbalance': Rolling average of imbalance ratio
                - 'signal': Imbalance signal (BID_DOMINATED, BALANCED, ASK_DOMINATED)
                - 'bid_volume': Total bid volume in considered levels
                - 'ask_volume': Total ask volume in considered levels
                - 'action': Trading action recommendation
                - 'confidence': Confidence in the recommendation
        """
        symbol = features.get('symbol', '')
        orderbook = features.get('orderbook')
        timestamp = features.get('timestamp', datetime.now())

        # Initialize caches for this symbol if needed
        if symbol not in self._imbalance_history:
            self._imbalance_history[symbol] = deque(maxlen=self.window_size)
            self._last_update[symbol] = datetime.min

        # Check if we have a valid orderbook
        if not orderbook or not isinstance(orderbook, OrderbookDelta):
            # Try to get from features directly
            bids = features.get('bids', [])
            asks = features.get('asks', [])

            if not bids or not asks:
                # If we have a cached orderbook, use that
                if symbol in self._last_orderbook:
                    orderbook = self._last_orderbook[symbol]
                else:
                    logger.warning(f"No orderbook data available for {symbol}")
                    return self._default_prediction()
        else:
            # Cache the orderbook
            self._last_orderbook[symbol] = orderbook

        # Extract bids and asks
        if isinstance(orderbook, OrderbookDelta):
            bids = orderbook.bids
            asks = orderbook.asks
        else:
            # Try to extract from dictionary
            bids = []
            asks = []

            # Convert to OrderbookLevel objects if needed
            for bid in features.get('bids', []):
                if isinstance(bid, OrderbookLevel):
                    bids.append(bid)
                elif isinstance(bid, (list, tuple)) and len(bid) >= 2:
                    bids.append(OrderbookLevel(price=float(bid[0]), quantity=float(bid[1])))
                elif isinstance(bid, dict) and 'price' in bid and 'quantity' in bid:
                    bids.append(OrderbookLevel(price=float(bid['price']), quantity=float(bid['quantity'])))

            for ask in features.get('asks', []):
                if isinstance(ask, OrderbookLevel):
                    asks.append(ask)
                elif isinstance(ask, (list, tuple)) and len(ask) >= 2:
                    asks.append(OrderbookLevel(price=float(ask[0]), quantity=float(ask[1])))
                elif isinstance(ask, dict) and 'price' in ask and 'quantity' in ask:
                    asks.append(OrderbookLevel(price=float(ask['price']), quantity=float(ask['quantity'])))

        # Limit to specified depth
        bids = bids[:self.depth_levels]
        asks = asks[:self.depth_levels]

        # Calculate imbalance
        imbalance_ratio, bid_volume, ask_volume = self._calculate_imbalance(bids, asks)

        # Update imbalance history
        self._imbalance_history[symbol].append(imbalance_ratio)
        self._last_update[symbol] = timestamp

        # Calculate rolling average
        rolling_imbalance = np.mean(list(self._imbalance_history[symbol]))

        # Determine signal based on rolling imbalance
        if rolling_imbalance > self.significant_imbalance:
            signal = ImbalanceSignal.BID_DOMINATED
        elif rolling_imbalance < -self.significant_imbalance:
            signal = ImbalanceSignal.ASK_DOMINATED
        else:
            signal = ImbalanceSignal.BALANCED

        # Determine trading action based on signal
        if signal == ImbalanceSignal.BID_DOMINATED:
            # More bids than asks - bullish signal
            action = "BUY"
            confidence = min(1.0, abs(rolling_imbalance) / (self.significant_imbalance * 2))
        elif signal == ImbalanceSignal.ASK_DOMINATED:
            # More asks than bids - bearish signal
            action = "SELL"
            confidence = min(1.0, abs(rolling_imbalance) / (self.significant_imbalance * 2))
        else:
            # Balanced - no strong signal
            action = "HOLD"
            confidence = 0.5

        # Calculate liquidity ratio (total volume / average price)
        mid_price = self._calculate_mid_price(bids, asks)
        total_volume = bid_volume + ask_volume
        liquidity_ratio = total_volume / mid_price if mid_price > 0 else 0.0

        # Calculate thinness indicator (inverse of liquidity)
        thinness = 1.0 / (1.0 + liquidity_ratio) if liquidity_ratio > 0 else 1.0

        return {
            'imbalance_ratio': imbalance_ratio,
            'rolling_imbalance': rolling_imbalance,
            'signal': signal.value,
            'bid_volume': bid_volume,
            'ask_volume': ask_volume,
            'liquidity_ratio': liquidity_ratio,
            'thinness': thinness,
            'action': action,
            'confidence': confidence
        }

    def _calculate_imbalance(
        self,
        bids: List[OrderbookLevel],
        asks: List[OrderbookLevel]
    ) -> Tuple[float, float, float]:
        """
        Calculate the imbalance ratio between bids and asks.

        Args:
            bids: List of bid levels
            asks: List of ask levels

        Returns:
            Tuple of (imbalance_ratio, bid_volume, ask_volume)
        """
        if not bids or not asks:
            return 0.0, 0.0, 0.0

        # Calculate weighted volumes
        bid_volume = 0.0
        ask_volume = 0.0

        for i, bid in enumerate(bids):
            # Apply decay factor based on level
            weight = self.decay_factor ** i
            bid_volume += bid.quantity * weight

        for i, ask in enumerate(asks):
            # Apply decay factor based on level
            weight = self.decay_factor ** i
            ask_volume += ask.quantity * weight

        # Calculate imbalance ratio (-1 to 1)
        total_volume = bid_volume + ask_volume
        if total_volume > 0:
            imbalance_ratio = (bid_volume - ask_volume) / total_volume
        else:
            imbalance_ratio = 0.0

        return imbalance_ratio, bid_volume, ask_volume

    def _calculate_mid_price(
        self,
        bids: List[OrderbookLevel],
        asks: List[OrderbookLevel]
    ) -> float:
        """
        Calculate the mid price from the orderbook.

        Args:
            bids: List of bid levels
            asks: List of ask levels

        Returns:
            Mid price
        """
        if not bids or not asks:
            return 0.0

        best_bid = bids[0].price
        best_ask = asks[0].price

        return (best_bid + best_ask) / 2

    def _default_prediction(self) -> Dict[str, Any]:
        """
        Return default prediction when data is insufficient.

        Returns:
            Default prediction dictionary
        """
        return {
            'imbalance_ratio': 0.0,
            'rolling_imbalance': 0.0,
            'signal': ImbalanceSignal.BALANCED.value,
            'bid_volume': 0.0,
            'ask_volume': 0.0,
            'liquidity_ratio': 0.0,
            'thinness': 1.0,
            'action': "HOLD",
            'confidence': 0.0
        }
