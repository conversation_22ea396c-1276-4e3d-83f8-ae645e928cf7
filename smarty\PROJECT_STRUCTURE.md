# 📁 Smart-Trader Project Structure

## **Directory Overview**

```
smarty/
├── 📁 core/                    # Core system components
│   ├── events.py              # Event definitions and data structures
│   ├── feature_store.py       # Real-time data storage and retrieval
│   ├── rule_engine.py         # Trading rule engine
│   ├── utils.py               # Utility functions
│   └── metrics.py             # Performance metrics
│
├── 📁 models/                  # Machine Learning models
│   ├── rsi.py                 # RSI model
│   ├── vwap_deviation.py      # VWAP deviation model
│   ├── garch_volatility.py    # GARCH volatility forecaster
│   ├── funding_momentum.py    # Funding rate momentum model
│   ├── open_interest_momentum.py # Open interest analysis
│   ├── orderflow_net.py       # Neural network order flow model
│   ├── social_sentiment.py    # Social sentiment analysis
│   └── meta_ensemble.py       # Meta-ensemble model
│
├── 📁 backtester/             # Backtesting framework
│   ├── backtester.py          # Main backtesting engine
│   ├── strategies.py          # Trading strategies
│   ├── analytics.py           # Performance analytics
│   ├── visualizer.py          # Result visualization
│   └── optimizer.py           # Strategy optimization
│
├── 📁 monitoring/             # Real-time monitoring system
│   └── model_monitor.py       # Model performance monitoring
│
├── 📁 clients/                # API clients
│   ├── htx_client.py          # HTX Futures API client
│   └── signalstar_client.py   # SignalStar API client
│
├── 📁 executors/              # Trade execution engines
│   ├── htx_executor.py        # HTX Futures executor
│   └── simulation_executor.py # Simulation executor
│
├── 📁 feeds/                  # Data feed handlers
│   ├── htx_feed.py            # HTX market data feed
│   └── funding_feed.py        # Funding rate data feed
│
├── 📁 llm/                    # LLM integration
│   ├── prompts/               # LLM prompt templates
│   └── consumer.py            # LLM consumer (if exists)
│
├── 📁 pipeline/               # Data pipeline components
│   └── (pipeline components)
│
├── 📁 tests/                  # Test suites
│   └── (test files)
│
├── 📁 data/                   # Data storage
│   ├── *.db                   # SQLite databases
│   └── *.json                 # JSON data files
│
├── 📁 logs/                   # Log files
│   └── *.log                  # Application logs
│
├── 📁 results/                # Backtest results
│   └── backtest/              # Backtest output files
│
├── 📁 demo_reports/           # Demo reports
│   └── *.json                 # Demo result files
│
├── 📁 test_results/           # Test results
│   └── *.json                 # Test output files
│
└── 📁 archive/                # Archived files
    ├── migrate_to_htx_optimized.py
    ├── migrate_to_optimized_bus.py
    └── analyze_phi_e2e_test.py
```

## **Main Application Files**

### **Core System**
- `orchestrator.py` - Main system orchestrator and coordinator
- `live_trader.py` - Enhanced live trading system with monitoring
- `position_manager.py` - Advanced position management system

### **LLM Integration**
- `llm/enhanced_llm_consumer.py` - Enhanced LLM consumer with robust error handling
- `llm/enhanced_llm_manager.py` - Enhanced LLM manager with performance monitoring

### **Execution & Testing**
- `run_testnet.py` - Run system on testnet
- `run_backtest.py` - Run backtesting with strategies
- `demo_live_trading.py` - Live trading demonstration
- `test_live_integration.py` - Comprehensive integration tests
- `simple_test.py` - Basic system functionality test

### **Utilities & Tools**
- `health_check.py` - System health monitoring
- `monitor.py` - System performance monitoring
- `position_dashboard.py` - Position monitoring dashboard
- `position_utils.py` - Position management utilities
- `generate_sample_data.py` - Sample data generation
- `download_sample_data.py` - Data download utilities
- `optimize_strategy.py` - Strategy optimization tools

### **Configuration**
- `config.yaml` - **SINGLE MASTER CONFIGURATION** (all modes)
- `credentials.yaml` - API credentials and secrets

### **Dependencies**
- `requirements.txt` - Python package dependencies

## **Documentation Files**

- `README.md` - Main project documentation
- `SYSTEM_ACHIEVEMENTS.md` - Comprehensive achievement summary
- `PROJECT_STRUCTURE.md` - This file - project structure overview
- `cleanup_plan.md` - Cleanup plan and execution log
- `HTX_FUTURES_OPTIMIZATION_GUIDE.md` - HTX API optimization guide
- `MIGRATION_GUIDE.md` - System migration guide
- `SQLITE_OPTIMIZATION_GUIDE.md` - SQLite performance optimization
- `TESTNET_DEPLOYMENT.md` - Testnet deployment instructions

## **Key Features by Component**

### **🤖 Models (8 Advanced Models)**
- **RSI Model**: Relative Strength Index with divergence detection
- **VWAP Deviation**: Volume-weighted average price analysis
- **GARCH Volatility**: Advanced volatility forecasting
- **Funding Momentum**: Futures funding rate momentum analysis
- **Open Interest**: Market sentiment through OI changes
- **OrderFlow Net**: Neural network for order flow analysis
- **Social Sentiment**: SignalStar API integration for sentiment
- **Meta-Ensemble**: Stacked model combining all signals

### **📊 Backtesting Framework**
- **Multiple Strategies**: SMA, RSI, Bollinger Bands, Multi-Signal, Ensemble, Smart-Integrated
- **Advanced Analytics**: 57 performance metrics with A-F grading
- **Rich Visualizations**: Equity curves, drawdowns, trade distributions
- **Strategy Optimization**: Parameter tuning and validation

### **📈 Monitoring System**
- **Real-Time Dashboard**: Web interface at http://localhost:8081
- **Model Performance**: Accuracy, latency, signal quality tracking
- **Alert System**: Configurable thresholds with notifications
- **Health Monitoring**: System uptime and component status

### **⚡ Live Trading**
- **HTX Futures Integration**: Real API connectivity
- **Simulation Mode**: Paper trading with real market data
- **Risk Management**: Multi-layered position and loss controls
- **Position Management**: Advanced sizing and stop-loss systems

### **🧠 LLM Integration**
- **Phi-3.1 Mini**: Local LLM for trading decisions
- **Signal Fusion**: Intelligent combination of model outputs
- **Context-Aware**: Account state and market condition integration
- **Prompt Engineering**: Optimized trading prompts

## **File Count Summary**

- **Python Files**: 79 (reduced from 101)
- **Configuration Files**: 5 (reduced from 9)
- **Documentation Files**: 8
- **Total Core Files**: ~92 (significantly reduced and organized)

## **Usage Examples**

### **Run Backtesting**
```bash
python run_backtest.py --symbols BTC-USDT --strategy smart --start-date 2025-02-22 --end-date 2025-02-28
```

### **Run Live Demo**
```bash
python demo_live_trading.py
```

### **Run Integration Tests**
```bash
python test_live_integration.py
```

### **Start Live Trading**
```bash
python run_testnet.py
```

### **Monitor System Health**
```bash
python health_check.py
```

This clean, organized structure provides a solid foundation for production deployment and future development.
