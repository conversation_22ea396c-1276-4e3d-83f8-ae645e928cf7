#!/usr/bin/env python3
"""
Authentication module for Epinnox Smart Trader Dashboard
Provides secure login functionality with session management.
"""

import hashlib
import secrets
import time
import base64
from typing import Dict, Optional
from aiohttp import web
import logging

logger = logging.getLogger(__name__)

class EpinnoxAuth:
    """Authentication handler for Epinnox Smart Trader Dashboard."""

    def __init__(self):
        # Hardcoded credentials for Epinnox investment club
        self.credentials = {
            "epinnox": self._hash_password("securepass123")
        }

        # Session configuration
        self.session_timeout = 24 * 60 * 60  # 24 hours
        self.secret_key = secrets.token_bytes(32)  # Generate random secret key

        # Failed login tracking (simple rate limiting)
        self.failed_attempts: Dict[str, list] = {}
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes

    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256 with salt."""
        salt = "epinnox_smart_trader_2025"
        return hashlib.sha256((password + salt).encode()).hexdigest()

    def _check_rate_limit(self, ip_address: str) -> bool:
        """Check if IP is rate limited due to failed attempts."""
        if ip_address not in self.failed_attempts:
            return True

        # Clean old attempts
        current_time = time.time()
        self.failed_attempts[ip_address] = [
            attempt_time for attempt_time in self.failed_attempts[ip_address]
            if current_time - attempt_time < self.lockout_duration
        ]

        # Check if still locked out
        return len(self.failed_attempts[ip_address]) < self.max_attempts

    def _record_failed_attempt(self, ip_address: str):
        """Record a failed login attempt."""
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []
        self.failed_attempts[ip_address].append(time.time())

    def setup_session_middleware(self, app: web.Application):
        """Setup simple session storage (in-memory for now)."""
        # Simple in-memory session storage
        self.sessions = {}
        logger.info("🔐 Simple session storage configured")

    async def login_handler(self, request: web.Request) -> web.Response:
        """Handle login page requests."""
        if request.method == 'GET':
            # Check if already logged in
            session_id = request.cookies.get('epinnox_session')
            if session_id and session_id in self.sessions:
                session = self.sessions[session_id]
                if session.get('authenticated') and session.get('username'):
                    # Already logged in, redirect to dashboard
                    return web.Response(
                        status=302,
                        headers={'Location': '/dashboard'}
                    )

            # Show login page
            return web.Response(
                text=self._get_login_html(),
                content_type='text/html'
            )

        elif request.method == 'POST':
            return await self._handle_login_post(request)

    async def _handle_login_post(self, request: web.Request) -> web.Response:
        """Handle login form submission."""
        # Get client IP for rate limiting
        client_ip = request.remote or "unknown"

        # Check rate limiting
        if not self._check_rate_limit(client_ip):
            logger.warning(f"🚫 Rate limited login attempt from {client_ip}")
            return web.Response(
                text=self._get_login_html(error="Too many failed attempts. Please try again in 5 minutes."),
                content_type='text/html',
                status=429
            )

        try:
            # Parse form data
            data = await request.post()
            username = data.get('username', '').strip().lower()
            password = data.get('password', '')

            # Validate credentials
            if username in self.credentials and self.credentials[username] == self._hash_password(password):
                # Successful login - create session
                session_id = secrets.token_urlsafe(32)
                self.sessions[session_id] = {
                    'authenticated': True,
                    'username': username,
                    'login_time': time.time()
                }

                logger.info(f"✅ Successful login for user: {username} from {client_ip}")

                # Redirect to dashboard with session cookie
                response = web.Response(
                    status=302,
                    headers={'Location': '/dashboard'}
                )
                response.set_cookie('epinnox_session', session_id,
                                  max_age=self.session_timeout,
                                  httponly=True,
                                  secure=False)  # Set to True for HTTPS
                return response
            else:
                # Failed login
                self._record_failed_attempt(client_ip)
                logger.warning(f"❌ Failed login attempt for user: {username} from {client_ip}")

                return web.Response(
                    text=self._get_login_html(error="Invalid username or password."),
                    content_type='text/html',
                    status=401
                )

        except Exception as e:
            logger.error(f"Error processing login: {e}")
            return web.Response(
                text=self._get_login_html(error="Login system error. Please try again."),
                content_type='text/html',
                status=500
            )

    async def logout_handler(self, request: web.Request) -> web.Response:
        """Handle logout requests."""
        session_id = request.cookies.get('epinnox_session')
        username = 'unknown'

        if session_id and session_id in self.sessions:
            username = self.sessions[session_id].get('username', 'unknown')
            # Clear session
            del self.sessions[session_id]

        logger.info(f"🚪 User logged out: {username}")

        # Redirect to login page and clear cookie
        response = web.Response(
            status=302,
            headers={'Location': '/login'}
        )
        response.del_cookie('epinnox_session')
        return response

    async def auth_middleware(self, app, handler):
        """Middleware to check authentication for protected routes."""
        async def middleware_handler(request):
            # Public routes that don't require authentication
            public_routes = ['/login', '/logout', '/static']

            # Check if route is public
            if any(request.path.startswith(route) for route in public_routes):
                return await handler(request)

            # Check authentication for protected routes
            session_id = request.cookies.get('epinnox_session')
            session = None

            if session_id and session_id in self.sessions:
                session = self.sessions[session_id]

            if not session or not session.get('authenticated'):
                # Not authenticated, redirect to login
                if request.path.startswith('/api/'):
                    # API request, return JSON error
                    return web.json_response(
                        {'error': 'Authentication required', 'redirect': '/login'},
                        status=401
                    )
                else:
                    # Regular request, redirect to login
                    return web.Response(
                        status=302,
                        headers={'Location': '/login'}
                    )

            # Check session timeout
            login_time = session.get('login_time', 0)
            if time.time() - login_time > self.session_timeout:
                # Session expired
                username = session.get('username', 'unknown')
                del self.sessions[session_id]
                logger.info(f"⏰ Session expired for user: {username}")

                if request.path.startswith('/api/'):
                    return web.json_response(
                        {'error': 'Session expired', 'redirect': '/login'},
                        status=401
                    )
                else:
                    return web.Response(
                        status=302,
                        headers={'Location': '/login'}
                    )

            # Authentication successful, proceed with request
            return await handler(request)

        return middleware_handler

    def _get_login_html(self, error: Optional[str] = None) -> str:
        """Generate the login page HTML."""
        error_html = ""
        if error:
            error_html = f'''
            <div class="error-message">
                <i class="error-icon">⚠️</i>
                {error}
            </div>
            '''

        return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Epinnox Smart Trader - Login</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e2e8f0;
        }}

        .login-container {{
            background: rgba(15, 20, 25, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            width: 100%;
            max-width: 420px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }}

        .login-container::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #FFD700, #FFA500, #FF6B35);
            border-radius: 20px 20px 0 0;
        }}

        .logo-section {{
            text-align: center;
            margin-bottom: 2.5rem;
        }}

        .logo {{
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 20px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: #0f1419;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }}

        .company-name {{
            font-size: 1.8rem;
            font-weight: 700;
            color: #FFD700;
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
        }}

        .tagline {{
            color: #94a3b8;
            font-size: 0.95rem;
            font-weight: 500;
        }}

        .form-group {{
            margin-bottom: 1.5rem;
        }}

        .form-label {{
            display: block;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
            font-weight: 600;
            font-size: 0.9rem;
        }}

        .form-input {{
            width: 100%;
            padding: 1rem 1.25rem;
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #e2e8f0;
            font-size: 1rem;
            transition: all 0.3s ease;
        }}

        .form-input:focus {{
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
            background: rgba(30, 41, 59, 1);
        }}

        .form-input::placeholder {{
            color: #64748b;
        }}

        .login-button {{
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border: none;
            border-radius: 12px;
            color: #0f1419;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}

        .login-button:hover {{
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
        }}

        .login-button:active {{
            transform: translateY(0);
        }}

        .login-button:disabled {{
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }}

        .error-message {{
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #fca5a5;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}

        .error-icon {{
            font-size: 1.1rem;
        }}

        .security-note {{
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #64748b;
            font-size: 0.8rem;
        }}

        .loading {{
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }}

        .spinner {{
            width: 20px;
            height: 20px;
            border: 2px solid rgba(15, 20, 25, 0.3);
            border-top: 2px solid #0f1419;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }}

        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}

        @media (max-width: 480px) {{
            .login-container {{
                margin: 1rem;
                padding: 2rem;
            }}

            .company-name {{
                font-size: 1.5rem;
            }}
        }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo">E</div>
            <div class="company-name">EPINNOX</div>
            <div class="tagline">Smart Trader Investment Club</div>
        </div>

        {error_html}

        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="form-input"
                    placeholder="Enter your username"
                    required
                    autocomplete="username"
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="form-input"
                    placeholder="Enter your password"
                    required
                    autocomplete="current-password"
                >
            </div>

            <button type="submit" class="login-button" id="loginBtn">
                <span class="button-text">Access Dashboard</span>
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="security-note">
            🔒 Secure connection • Private investment club access only
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {{
            const btn = document.getElementById('loginBtn');
            const btnText = btn.querySelector('.button-text');
            const loading = btn.querySelector('.loading');

            btn.disabled = true;
            btnText.style.opacity = '0';
            loading.style.display = 'block';

            // Re-enable button after 3 seconds if form doesn't submit
            setTimeout(() => {{
                btn.disabled = false;
                btnText.style.opacity = '1';
                loading.style.display = 'none';
            }}, 3000);
        }});

        // Focus username field on page load
        document.getElementById('username').focus();

        // Enter key handling
        document.addEventListener('keypress', function(e) {{
            if (e.key === 'Enter') {{
                document.getElementById('loginForm').submit();
            }}
        }});
    </script>
</body>
</html>
        '''
