#!/usr/bin/env python3
"""
🔥 Dashboard Strategy Integration Test

Tests the integration between the dashboard and strategy management to ensure
that the UI and backend are properly aligned.
"""

import asyncio
import aiohttp
import json
import time
import logging
import sys
from typing import Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardTester:
    """Tests dashboard strategy integration."""
    
    def __init__(self, base_url: str = "http://localhost:8082"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_strategy_status_endpoint(self) -> bool:
        """Test the strategy status API endpoint."""
        try:
            async with self.session.get(f"{self.base_url}/api/strategy/status") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    logger.info("✅ Strategy status endpoint accessible")
                    
                    # Check expected fields
                    expected_fields = ["current_strategy", "available_strategies", "strategy_status"]
                    for field in expected_fields:
                        if field in data:
                            logger.info(f"   ✅ Field '{field}' present")
                        else:
                            logger.error(f"   ❌ Field '{field}' missing")
                            return False
                    
                    # Check available strategies
                    available = data.get("available_strategies", [])
                    expected_strategies = ["Smart Model Integrated", "Smart Strategy Only", "Order Flow"]
                    
                    logger.info(f"   Available strategies: {available}")
                    
                    for strategy in expected_strategies:
                        if strategy in available:
                            logger.info(f"   ✅ Strategy '{strategy}' available")
                        else:
                            logger.error(f"   ❌ Strategy '{strategy}' missing")
                            return False
                    
                    # Check for unexpected strategies
                    unexpected = set(available) - set(expected_strategies)
                    if unexpected:
                        logger.warning(f"   ⚠️ Unexpected strategies found: {unexpected}")
                    
                    return True
                else:
                    logger.error(f"❌ Strategy status endpoint returned {resp.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Strategy status endpoint test failed: {e}")
            return False
    
    async def test_strategy_start_api(self, strategy_name: str) -> bool:
        """Test starting a strategy via API."""
        try:
            data = {"strategy": strategy_name}
            async with self.session.post(
                f"{self.base_url}/api/strategy/start", 
                json=data
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get("success"):
                        logger.info(f"✅ Strategy '{strategy_name}' started via API")
                        return True
                    else:
                        logger.error(f"❌ Strategy '{strategy_name}' start failed: {result.get('message')}")
                        return False
                else:
                    logger.error(f"❌ Strategy start API returned {resp.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Strategy start API test failed: {e}")
            return False
    
    async def test_strategy_stop_api(self) -> bool:
        """Test stopping strategy via API."""
        try:
            async with self.session.post(f"{self.base_url}/api/strategy/stop") as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get("success"):
                        logger.info("✅ Strategy stopped via API")
                        return True
                    else:
                        logger.error(f"❌ Strategy stop failed: {result.get('message')}")
                        return False
                else:
                    logger.error(f"❌ Strategy stop API returned {resp.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Strategy stop API test failed: {e}")
            return False
    
    async def test_dashboard_accessibility(self) -> bool:
        """Test if dashboard is accessible."""
        try:
            async with self.session.get(f"{self.base_url}/dashboard") as resp:
                if resp.status == 200:
                    logger.info("✅ Dashboard accessible")
                    return True
                else:
                    logger.error(f"❌ Dashboard returned {resp.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Dashboard accessibility test failed: {e}")
            return False
    
    async def run_tests(self) -> Dict[str, bool]:
        """Run all integration tests."""
        logger.info("🔥 Dashboard Strategy Integration Test")
        logger.info("=" * 50)
        
        results = {}
        
        # Test 1: Dashboard accessibility
        logger.info("📋 Test 1: Dashboard Accessibility")
        results["dashboard_accessible"] = await self.test_dashboard_accessibility()
        
        # Test 2: Strategy status endpoint
        logger.info("\n📋 Test 2: Strategy Status Endpoint")
        results["strategy_status_api"] = await self.test_strategy_status_endpoint()
        
        # Test 3: Strategy start/stop APIs
        logger.info("\n📋 Test 3: Strategy Management APIs")
        
        # Test starting each strategy
        strategies_to_test = ["Smart Model Integrated", "Smart Strategy Only", "Order Flow"]
        
        for strategy in strategies_to_test:
            logger.info(f"\n   Testing {strategy}...")
            
            # Start strategy
            start_success = await self.test_strategy_start_api(strategy)
            results[f"start_{strategy.replace(' ', '_').lower()}"] = start_success
            
            if start_success:
                # Wait a moment
                await asyncio.sleep(2)
                
                # Stop strategy
                stop_success = await self.test_strategy_stop_api()
                results[f"stop_{strategy.replace(' ', '_').lower()}"] = stop_success
                
                # Wait before next test
                await asyncio.sleep(1)
        
        return results

async def main():
    """Main test function."""
    logger.info("🚀 Starting Dashboard Integration Tests...")
    logger.info("⚠️ Make sure the dashboard is running on localhost:8082")
    logger.info("")
    
    # Wait for user confirmation
    try:
        input("Press Enter when dashboard is ready, or Ctrl+C to cancel...")
    except KeyboardInterrupt:
        logger.info("Test cancelled by user")
        return 1
    
    async with DashboardTester() as tester:
        results = await tester.run_tests()
        
        # Summary
        logger.info("\n📊 Test Summary")
        logger.info("=" * 50)
        
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")
        
        if passed == total:
            logger.info("\n🎉 ALL INTEGRATION TESTS PASSED!")
            return 0
        else:
            logger.error("\n🚨 SOME INTEGRATION TESTS FAILED!")
            return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
