"""
HTX Futures REST API client.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List

import aiohttp

from core.utils import generate_signature, retry_async
from core.events import Order, OrderResponse, Position, Side, OrderType
from .constants import REST_BASE_URL, TESTNET_REST_BASE_URL, ENDPOINTS, ERROR_MESSAGES
from .types import HTXConfig, APIResponse, APIError, RateLimitError, ValidationError
from .parser import MessageParser

logger = logging.getLogger(__name__)


class HTXRestClient:
    """
    HTX Futures REST API client with retry logic and error handling.
    """

    def __init__(
        self,
        config: HTXConfig,
        session: Optional[aiohttp.ClientSession] = None,
        parser: Optional[MessageParser] = None
    ):
        """
        Initialize REST client.

        Args:
            config: HTX configuration
            session: aiohttp session for requests
            parser: Message parser instance
        """
        self.config = config
        self.session = session
        self.parser = parser or MessageParser(debug=config.debug)

        # Rate limiting
        self._last_request_time = 0.0
        self._request_count = 0

        # Request metrics
        self._total_requests = 0
        self._failed_requests = 0

    def _get_base_url(self) -> str:
        """Get the appropriate REST base URL based on testnet mode."""
        return TESTNET_REST_BASE_URL if self.config.testnet else REST_BASE_URL

    async def _sign_request(self, method: str, path: str, params: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Sign a request for authenticated endpoints.

        Args:
            method: HTTP method (GET, POST, etc.)
            path: API endpoint path
            params: Request parameters

        Returns:
            Dictionary with authentication headers
        """
        if not self.config.api_key or not self.config.api_secret:
            raise ValidationError("API credentials required for authenticated requests")

        timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')

        # Prepare parameters
        params = params or {}
        params_str = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])

        # Create signature string - Use the correct host for the current environment
        base_url = self._get_base_url()
        host = base_url.replace("https://", "").replace("http://", "")
        signature_str = f"{method}\n{host}\n{path}\n{params_str}"

        # Generate signature
        signature = generate_signature(self.config.api_secret, signature_str)

        # Create authentication headers
        auth_headers = {
            "AccessKeyId": self.config.api_key,
            "SignatureMethod": "HmacSHA256",
            "SignatureVersion": "2",
            "Timestamp": timestamp,
            "Signature": signature
        }

        return auth_headers

    async def _rate_limit(self) -> None:
        """Apply rate limiting to requests."""
        current_time = time.time()

        # Simple rate limiting - ensure minimum delay between requests
        min_delay = 1.0 / 10  # 10 requests per second max
        time_since_last = current_time - self._last_request_time

        if time_since_last < min_delay:
            await asyncio.sleep(min_delay - time_since_last)

        self._last_request_time = time.time()

    async def _request(
        self,
        method: str,
        endpoint: str,
        params: Dict[str, Any] = None,
        data: Dict[str, Any] = None,
        auth: bool = False
    ) -> APIResponse:
        """
        Make a request to the REST API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            params: Query parameters
            data: Request body for POST requests
            auth: Whether authentication is required

        Returns:
            Standardized API response
        """
        if self.session is None:
            raise ValidationError("HTTP session not initialized")

        # Apply rate limiting
        await self._rate_limit()

        base_url = self._get_base_url()
        url = f"{base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        params = params or {}

        # Add authentication if required
        if auth:
            try:
                auth_params = await self._sign_request(method, endpoint, params)
                params.update(auth_params)
            except Exception as e:
                logger.error(f"Failed to sign request: {e}")
                raise APIError(f"Authentication failed: {e}")

        start_time = time.time()
        self._total_requests += 1

        try:
            if self.config.debug:
                logger.debug(f"Making {method} request to {url}")

            async with self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=self.config.request_timeout)
            ) as response:

                # Calculate latency
                latency = time.time() - start_time

                # Handle rate limiting
                if response.status == 429:
                    self._failed_requests += 1
                    raise RateLimitError("Rate limit exceeded")

                # Parse response
                try:
                    response_data = await response.json()
                except Exception as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    response_text = await response.text()
                    raise APIError(f"Invalid JSON response: {response_text[:200]}")

                # Parse using message parser
                api_response = self.parser.parse_api_response(response_data)
                api_response.latency = latency

                if not api_response.success:
                    self._failed_requests += 1
                    logger.error(f"API error: {api_response.error}")
                    raise APIError(api_response.error, api_response.error_code)

                if self.config.debug:
                    logger.debug(f"Request completed in {latency:.3f}s")

                return api_response

        except aiohttp.ClientError as e:
            self._failed_requests += 1
            logger.error(f"HTTP client error: {e}")
            raise APIError(f"HTTP request failed: {e}")
        except asyncio.TimeoutError:
            self._failed_requests += 1
            logger.error("Request timeout")
            raise APIError("Request timeout")

    # Contract and market data methods
    async def get_contract_info(self, symbol: str = None) -> APIResponse:
        """Get contract information."""
        params = {}
        if symbol:
            params["contract_code"] = symbol
        return await self._request("GET", ENDPOINTS["contract_info"], params)

    async def get_price_limits(self, symbol: str) -> APIResponse:
        """Get price limits for a symbol."""
        params = {"contract_code": symbol}
        return await self._request("GET", ENDPOINTS["price_limits"], params)

    # Account methods
    async def get_account_info(self) -> APIResponse:
        """Get account information."""
        return await self._request("POST", ENDPOINTS["account_info"], auth=True)

    async def get_position_info(self, symbol: str = None) -> APIResponse:
        """Get position information."""
        params = {}
        if symbol:
            params["contract_code"] = symbol
        return await self._request("POST", ENDPOINTS["position_info"], params, auth=True)

    async def get_positions(self, symbol: str = None) -> List[Position]:
        """
        Get positions as domain objects.

        Args:
            symbol: Symbol to get positions for (None for all)

        Returns:
            List of Position objects
        """
        try:
            response = await self.get_position_info(symbol)
            positions = []

            if response.success and response.data:
                for pos_data in response.data:
                    symbol = pos_data.get("contract_code", "")
                    side_str = pos_data.get("direction", "").upper()
                    side = Side.BUY if side_str == "BUY" else Side.SELL

                    # Only add positions with non-zero size
                    if float(pos_data.get("volume", 0)) > 0:
                        positions.append(Position(
                            symbol=symbol,
                            side=side,
                            size=float(pos_data.get("volume", 0)),
                            entry_price=float(pos_data.get("cost_open", 0)),
                            leverage=float(pos_data.get("lever_rate", 1)),
                            liquidation_price=float(pos_data.get("liquidation_price", 0)),
                            unrealized_pnl=float(pos_data.get("unrealized_profit", 0)),
                            realized_pnl=float(pos_data.get("profit", 0)),
                            margin=float(pos_data.get("position_margin", 0)),
                            timestamp=datetime.now()
                        ))

            return positions

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []

    # Order methods
    async def place_order(self, order: Order) -> APIResponse:
        """Place an order."""
        # Validate order
        if not order.symbol or not order.quantity or order.quantity <= 0:
            raise ValidationError("Invalid order parameters")

        if order.order_type == OrderType.LIMIT and (not order.price or order.price <= 0):
            raise ValidationError("Limit orders require a valid price")

        # Convert order to API parameters
        params = {
            "contract_code": order.symbol,
            "volume": order.quantity,
            "direction": "buy" if order.side == Side.BUY else "sell",
            "offset": "close" if order.reduce_only else "open",
            "lever_rate": 10,  # Default leverage, should be configurable
            "order_price_type": order.order_type.value.lower()
        }

        # Add price for limit orders
        if order.order_type == OrderType.LIMIT and order.price is not None:
            params["price"] = order.price

        # Add client order ID if provided
        if order.client_order_id:
            params["client_order_id"] = order.client_order_id

        return await self._request("POST", ENDPOINTS["place_order"], params, auth=True)

    async def cancel_order(self, symbol: str, order_id: str = None, client_order_id: str = None) -> APIResponse:
        """Cancel an order."""
        if not order_id and not client_order_id:
            raise ValidationError("Either order_id or client_order_id must be provided")

        params = {"contract_code": symbol}
        if order_id:
            params["order_id"] = order_id
        elif client_order_id:
            params["client_order_id"] = client_order_id

        return await self._request("POST", ENDPOINTS["cancel_order"], params, auth=True)

    async def get_order_info(self, symbol: str, order_id: str = None, client_order_id: str = None) -> APIResponse:
        """Get information about an order."""
        if not order_id and not client_order_id:
            raise ValidationError("Either order_id or client_order_id must be provided")

        params = {"contract_code": symbol}
        if order_id:
            params["order_id"] = order_id
        elif client_order_id:
            params["client_order_id"] = client_order_id

        return await self._request("POST", ENDPOINTS["order_info"], params, auth=True)

    async def get_open_orders(self, symbol: str = None) -> APIResponse:
        """Get open orders."""
        params = {}
        if symbol:
            params["contract_code"] = symbol
        return await self._request("POST", ENDPOINTS["open_orders"], params, auth=True)

    async def get_orders(self, symbol: str = None) -> List[OrderResponse]:
        """
        Get open orders as domain objects.

        Args:
            symbol: Symbol to get open orders for (None for all)

        Returns:
            List of OrderResponse objects
        """
        try:
            response = await self.get_open_orders(symbol)
            orders = []

            if response.success and response.data and "orders" in response.data:
                for order_data in response.data["orders"]:
                    symbol = order_data.get("contract_code", "")
                    orders.append(OrderResponse(
                        symbol=symbol,
                        order_id=str(order_data.get("order_id", "")),
                        client_order_id=str(order_data.get("client_order_id", "")),
                        status=order_data.get("status", ""),
                        timestamp=datetime.fromtimestamp(int(order_data.get("created_at", 0)) / 1000)
                    ))

            return orders

        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []

    # Account balance methods
    async def get_account_balance(self) -> Dict[str, float]:
        """Get account balance."""
        try:
            response = await self.get_account_info()
            balances = {}

            if response.success and response.data:
                for account in response.data:
                    for asset in account.get("margin_asset", "").split(","):
                        if asset:
                            balances[asset] = float(account.get("margin_balance", 0))

            return balances

        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return {}

    async def set_leverage(self, symbol: str, leverage: int) -> APIResponse:
        """Set leverage for a symbol."""
        if leverage < 1 or leverage > 125:
            raise ValidationError("Leverage must be between 1 and 125")

        params = {
            "contract_code": symbol,
            "lever_rate": leverage
        }
        return await self._request("POST", ENDPOINTS["set_leverage"], params, auth=True)

    async def health_check(self) -> bool:
        """
        Perform a health check by making a simple API call.

        Returns:
            True if API is reachable and responding
        """
        try:
            response = await self.get_contract_info()
            return response.success
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    def get_metrics(self) -> Dict[str, Any]:
        """Get request metrics."""
        success_rate = 0.0
        if self._total_requests > 0:
            success_rate = (self._total_requests - self._failed_requests) / self._total_requests

        return {
            "total_requests": self._total_requests,
            "failed_requests": self._failed_requests,
            "success_rate": success_rate,
            "last_request_time": self._last_request_time
        }
