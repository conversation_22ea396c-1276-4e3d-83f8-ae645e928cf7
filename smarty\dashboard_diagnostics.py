#!/usr/bin/env python3
"""
Smart Trader Dashboard Diagnostics
Comprehensive diagnostic tool to identify why the dashboard isn't updating.
"""

import asyncio
import sqlite3
import logging
import time
import json
import aiohttp
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DashboardDiagnostics:
    """Comprehensive diagnostics for Smart Trader Dashboard."""
    
    def __init__(self):
        self.bus_db_path = "data/bus.db"
        self.dashboard_url = "http://localhost:8082"
        self.results = {}
        
    async def run_full_diagnostics(self):
        """Run all diagnostic checks."""
        logger.info("🔍 Starting Smart Trader Dashboard Diagnostics...")
        
        # Check 1: Database connectivity and recent data
        await self.check_database_status()
        
        # Check 2: Dashboard server status
        await self.check_dashboard_server()
        
        # Check 3: WebSocket connectivity
        await self.check_websocket_connection()
        
        # Check 4: Data ingestion process
        await self.check_data_ingestion()
        
        # Check 5: Background updater status
        await self.check_background_updater()
        
        # Generate summary report
        self.generate_report()
        
    async def check_database_status(self):
        """Check SQLite bus database for recent activity."""
        logger.info("📊 Checking SQLite bus database status...")
        
        try:
            if not os.path.exists(self.bus_db_path):
                self.results['database'] = {
                    'status': 'FAILED',
                    'error': f'Database file not found: {self.bus_db_path}',
                    'recommendation': 'Start the HTX data producer to create the database'
                }
                return
                
            conn = sqlite3.connect(self.bus_db_path)
            cursor = conn.cursor()
            
            # Check if messages table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='messages'")
            if not cursor.fetchone():
                self.results['database'] = {
                    'status': 'FAILED',
                    'error': 'Messages table does not exist',
                    'recommendation': 'Initialize the SQLite bus properly'
                }
                return
            
            # Check total message count
            cursor.execute("SELECT COUNT(*) FROM messages")
            total_messages = cursor.fetchone()[0]
            
            # Check recent messages (last 30 seconds)
            thirty_seconds_ago = (datetime.now() - timedelta(seconds=30)).timestamp()
            cursor.execute("SELECT COUNT(*) FROM messages WHERE ts > ?", (thirty_seconds_ago,))
            recent_messages = cursor.fetchone()[0]
            
            # Check latest message
            cursor.execute("SELECT ts, stream, payload FROM messages ORDER BY ts DESC LIMIT 1")
            latest_row = cursor.fetchone()
            
            if latest_row:
                latest_ts = datetime.fromtimestamp(latest_row[0])
                age_seconds = (datetime.now() - latest_ts).total_seconds()
                
                self.results['database'] = {
                    'status': 'ACTIVE' if recent_messages > 0 else 'STALE',
                    'total_messages': total_messages,
                    'recent_messages_30s': recent_messages,
                    'latest_message_age': f"{age_seconds:.1f} seconds ago",
                    'latest_stream': latest_row[1],
                    'latest_timestamp': latest_ts.isoformat(),
                    'is_live': age_seconds < 30
                }
                
                if recent_messages == 0:
                    self.results['database']['recommendation'] = 'No recent data - check HTX data producer'
                    
            else:
                self.results['database'] = {
                    'status': 'EMPTY',
                    'total_messages': 0,
                    'recommendation': 'Database is empty - start data ingestion'
                }
                
            conn.close()
            logger.info(f"✅ Database check complete: {self.results['database']['status']}")
            
        except Exception as e:
            self.results['database'] = {
                'status': 'ERROR',
                'error': str(e),
                'recommendation': 'Check database file permissions and integrity'
            }
            logger.error(f"❌ Database check failed: {e}")
    
    async def check_dashboard_server(self):
        """Check if dashboard server is running and responsive."""
        logger.info("🌐 Checking dashboard server status...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Test main dashboard endpoint
                async with session.get(f"{self.dashboard_url}/", timeout=5) as response:
                    if response.status == 200:
                        dashboard_status = 'RUNNING'
                    else:
                        dashboard_status = f'ERROR_{response.status}'
                
                # Test API endpoints
                api_results = {}
                api_endpoints = [
                    '/api/market-data',
                    '/api/signals', 
                    '/api/stats',
                    '/api/strategy/status'
                ]
                
                for endpoint in api_endpoints:
                    try:
                        async with session.get(f"{self.dashboard_url}{endpoint}", timeout=3) as api_response:
                            api_results[endpoint] = {
                                'status': api_response.status,
                                'response_time': 'OK' if api_response.status == 200 else 'FAILED'
                            }
                    except Exception as e:
                        api_results[endpoint] = {'status': 'ERROR', 'error': str(e)}
                
                self.results['dashboard_server'] = {
                    'status': dashboard_status,
                    'url': self.dashboard_url,
                    'api_endpoints': api_results
                }
                
                logger.info(f"✅ Dashboard server check: {dashboard_status}")
                
        except aiohttp.ClientConnectorError:
            self.results['dashboard_server'] = {
                'status': 'NOT_RUNNING',
                'error': 'Connection refused',
                'recommendation': 'Start the dashboard server: python live_dashboard.py'
            }
            logger.error("❌ Dashboard server not running")
            
        except Exception as e:
            self.results['dashboard_server'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            logger.error(f"❌ Dashboard server check failed: {e}")
    
    async def check_websocket_connection(self):
        """Test WebSocket connectivity and data flow."""
        logger.info("🔌 Testing WebSocket connection...")
        
        try:
            import websockets
            
            ws_url = f"ws://localhost:8082/ws"
            
            async with websockets.connect(ws_url, timeout=5) as websocket:
                logger.info("✅ WebSocket connected successfully")
                
                # Wait for a message with timeout
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(message)
                    
                    self.results['websocket'] = {
                        'status': 'ACTIVE',
                        'connection': 'SUCCESS',
                        'data_received': True,
                        'sample_message_type': data.get('type', 'unknown'),
                        'message_timestamp': data.get('timestamp', 'none')
                    }
                    logger.info("✅ WebSocket receiving data")
                    
                except asyncio.TimeoutError:
                    self.results['websocket'] = {
                        'status': 'CONNECTED_NO_DATA',
                        'connection': 'SUCCESS',
                        'data_received': False,
                        'recommendation': 'WebSocket connected but no data received - check background_updater'
                    }
                    logger.warning("⚠️ WebSocket connected but no data received")
                    
        except ImportError:
            self.results['websocket'] = {
                'status': 'CANNOT_TEST',
                'error': 'websockets library not installed',
                'recommendation': 'pip install websockets'
            }
            
        except Exception as e:
            self.results['websocket'] = {
                'status': 'FAILED',
                'error': str(e),
                'recommendation': 'Check if dashboard server WebSocket endpoint is working'
            }
            logger.error(f"❌ WebSocket test failed: {e}")
    
    async def check_data_ingestion(self):
        """Check if data ingestion processes are running."""
        logger.info("📡 Checking data ingestion processes...")
        
        # Check for HTX data producer process
        import psutil
        
        htx_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'htx_data_producer' in cmdline or 'feeds/htx_data_producer.py' in cmdline:
                    htx_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.results['data_ingestion'] = {
            'htx_processes': htx_processes,
            'status': 'RUNNING' if htx_processes else 'NOT_RUNNING',
            'recommendation': 'Start HTX data producer: python feeds/htx_data_producer.py' if not htx_processes else 'HTX data producer is running'
        }
        
        logger.info(f"✅ Data ingestion check: {len(htx_processes)} HTX processes found")
    
    async def check_background_updater(self):
        """Check background updater status via dashboard API."""
        logger.info("🔄 Checking background updater status...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Check debug endpoint for system status
                async with session.get(f"{self.dashboard_url}/api/debug", timeout=5) as response:
                    if response.status == 200:
                        debug_data = await response.json()
                        
                        self.results['background_updater'] = {
                            'status': 'ACCESSIBLE',
                            'system_status': debug_data.get('system_status', 'unknown'),
                            'recent_market_data': debug_data.get('recent_market_data', 0),
                            'latest_signal': debug_data.get('latest_signal', 'none'),
                            'signal_generation': debug_data.get('signal_generation', 'unknown')
                        }
                        
                        if debug_data.get('recent_market_data', 0) > 0:
                            self.results['background_updater']['data_flow'] = 'ACTIVE'
                        else:
                            self.results['background_updater']['data_flow'] = 'STALE'
                            self.results['background_updater']['recommendation'] = 'Background updater may not be running or data source is stale'
                    else:
                        self.results['background_updater'] = {
                            'status': 'API_ERROR',
                            'error': f'Debug API returned {response.status}'
                        }
                        
        except Exception as e:
            self.results['background_updater'] = {
                'status': 'ERROR',
                'error': str(e),
                'recommendation': 'Cannot access background updater status - dashboard may not be running'
            }
    
    def generate_report(self):
        """Generate comprehensive diagnostic report."""
        logger.info("📋 Generating diagnostic report...")
        
        print("\n" + "="*80)
        print("🔍 SMART TRADER DASHBOARD DIAGNOSTICS REPORT")
        print("="*80)
        
        # Overall status
        critical_failures = []
        warnings = []
        
        for component, result in self.results.items():
            status = result.get('status', 'UNKNOWN')
            if status in ['FAILED', 'ERROR', 'NOT_RUNNING', 'EMPTY']:
                critical_failures.append(component)
            elif status in ['STALE', 'CONNECTED_NO_DATA']:
                warnings.append(component)
        
        if critical_failures:
            print(f"❌ CRITICAL ISSUES FOUND: {', '.join(critical_failures)}")
        elif warnings:
            print(f"⚠️  WARNINGS: {', '.join(warnings)}")
        else:
            print("✅ ALL SYSTEMS OPERATIONAL")
        
        print()
        
        # Detailed results
        for component, result in self.results.items():
            print(f"📊 {component.upper().replace('_', ' ')}")
            print("-" * 40)
            
            status = result.get('status', 'UNKNOWN')
            status_emoji = {
                'ACTIVE': '✅',
                'RUNNING': '✅', 
                'SUCCESS': '✅',
                'STALE': '⚠️',
                'CONNECTED_NO_DATA': '⚠️',
                'FAILED': '❌',
                'ERROR': '❌',
                'NOT_RUNNING': '❌',
                'EMPTY': '❌'
            }.get(status, '❓')
            
            print(f"Status: {status_emoji} {status}")
            
            for key, value in result.items():
                if key != 'status':
                    if isinstance(value, dict):
                        print(f"{key}:")
                        for sub_key, sub_value in value.items():
                            print(f"  {sub_key}: {sub_value}")
                    else:
                        print(f"{key}: {value}")
            
            if 'recommendation' in result:
                print(f"💡 Recommendation: {result['recommendation']}")
            
            print()
        
        # Quick fix suggestions
        print("🔧 QUICK FIX SUGGESTIONS")
        print("-" * 40)
        
        if 'database' in critical_failures:
            print("1. Start HTX data producer: python feeds/htx_data_producer.py")
        
        if 'dashboard_server' in critical_failures:
            print("2. Start dashboard server: python live_dashboard.py")
        
        if 'data_ingestion' in critical_failures:
            print("3. Check HTX connection and start data ingestion")
        
        if warnings:
            print("4. Check network connectivity and exchange API status")
        
        print("\n" + "="*80)

async def main():
    """Run diagnostics."""
    diagnostics = DashboardDiagnostics()
    await diagnostics.run_full_diagnostics()

if __name__ == "__main__":
    asyncio.run(main())
