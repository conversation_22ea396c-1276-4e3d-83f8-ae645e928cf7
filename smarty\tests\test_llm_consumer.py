"""
Unit tests for the LLM Consumer.
"""

import unittest
import json
import time
import yaml
import os
import sys
from datetime import datetime
from unittest.mock import MagicMock, patch

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the LLM Consumer
from llm_consumer import LLMConsumer


class MockBus:
    """Mock message bus for testing."""

    def __init__(self):
        self.published_messages = []
        self.subscriptions = {}

    def publish(self, stream, ts, payload):
        """Publish a message to a stream."""
        self.published_messages.append((stream, ts, payload))

    def subscribe(self, stream, callback):
        """Subscribe to a stream."""
        if stream not in self.subscriptions:
            self.subscriptions[stream] = []
        self.subscriptions[stream].append(callback)

    def simulate_message(self, stream, ts, payload):
        """Simulate receiving a message."""
        if stream in self.subscriptions:
            for callback in self.subscriptions[stream]:
                callback(ts, payload)


class MockAccountClient:
    """Mock account client for testing."""

    def __init__(self):
        self.account_info = {
            "total_equity": 100.0,
            "available_balance": 90.0
        }
        self.positions = [
            {
                "symbol": "BTC-USDT",
                "side": "LONG",
                "size": 0.01,
                "entry_price": 50000.0,
                "mark_price": 51000.0
            }
        ]
        self.orders = [
            {
                "symbol": "BTC-USDT",
                "side": "BUY",
                "quantity": 0.005,
                "price": 49500.0,
                "status": "NEW"
            }
        ]

    async def get_account_info(self):
        """Get account information."""
        return self.account_info

    async def get_positions(self):
        """Get positions."""
        return self.positions

    async def get_open_orders(self):
        """Get open orders."""
        return self.orders


class MockLlama:
    """Mock Llama for testing."""

    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.calls = []
        self.response_type = "json"  # Default to JSON response

    def set_response_type(self, response_type):
        """Set the type of response to return."""
        self.response_type = response_type

    def __call__(self, prompt, **kwargs):
        """Simulate a call to the LLM."""
        self.calls.append((prompt, kwargs))

        # Return a mock response in the format expected by _call_llm
        if self.response_type == "json":
            return {
                "choices": [
                    {
                        "text": '{"action": "BUY", "confidence": 0.75, "rationale": "Test rationale"}'
                    }
                ]
            }
        elif self.response_type == "string":
            return {
                "choices": [
                    {
                        "text": "I recommend to BUY because the market looks good."
                    }
                ]
            }
        elif self.response_type == "invalid_json":
            return {
                "choices": [
                    {
                        "text": '{"action": "BUY", "confidence": 0.75, "rationale": "Test rationale'
                    }
                ]
            }
        else:
            return {
                "choices": [
                    {
                        "text": '{"action": "BUY", "confidence": 0.75, "rationale": "Test rationale"}'
                    }
                ]
            }


class TestLLMConsumer(unittest.TestCase):
    """Test cases for the LLM Consumer."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock bus
        self.bus = MockBus()

        # Create a mock config
        self.config = {
            "model_path": "models/test.gguf",
            "prompt_path": "llm/prompts/test_prompt.yaml",
            "n_ctx": 1024,
            "max_tokens": 64,
            "temperature": 0.0,
            "call_interval_s": 1,  # Short interval for testing
            "dummy_mode": True
        }

        # Create a mock prompt template
        self.prompt_template = "Symbol: {symbol}\nScore: {fused_score}\nAction: {fused_decision}\nThe answer:"

        # Create a temporary prompt file
        os.makedirs("llm/prompts", exist_ok=True)
        with open("llm/prompts/test_prompt.yaml", "w") as f:
            yaml.dump({"template": self.prompt_template}, f)

        # Patch the Llama class
        self.llama_patch = patch("llm_consumer.Llama", MockLlama)
        self.mock_llama = self.llama_patch.start()

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop the Llama patch
        self.llama_patch.stop()

        # Remove the temporary prompt file
        if os.path.exists("llm/prompts/test_prompt.yaml"):
            os.remove("llm/prompts/test_prompt.yaml")

    def test_initialization(self):
        """Test initialization of the LLM Consumer."""
        # Create a mock account client
        account_client = MockAccountClient()

        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config, account_client)

        # Check that the consumer was initialized correctly
        self.assertEqual(consumer.config, self.config)
        self.assertEqual(consumer.min_interval, self.config["call_interval_s"])
        self.assertEqual(consumer.last_call, 0)
        self.assertEqual(consumer.account_client, account_client)

    def test_on_fused_signal(self):
        """Test handling of a fused signal."""
        # Create a mock account client
        account_client = MockAccountClient()

        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config, account_client)

        # Start the consumer
        consumer.start()

        # Simulate account state messages
        self.bus.simulate_message("account.state", time.time(), {
            "total": 100.0,
            "available": 90.0,
            "reserved": 10.0,
            "timestamp": datetime.now().isoformat()
        })

        self.bus.simulate_message("positions.state", time.time(), {
            "positions": [
                {
                    "symbol": "BTC-USDT",
                    "side": "LONG",
                    "size": 0.01,
                    "entry_price": 50000.0,
                    "mark_price": 51000.0
                }
            ],
            "timestamp": datetime.now().isoformat()
        })

        self.bus.simulate_message("orders.state", time.time(), {
            "orders": [
                {
                    "symbol": "BTC-USDT",
                    "side": "BUY",
                    "quantity": 0.005,
                    "price": 49500.0,
                    "status": "NEW"
                }
            ],
            "timestamp": datetime.now().isoformat()
        })

        # Create a test signal
        signal = {
            "symbol": "BTC-USDT",
            "score": 0.5,
            "decision": "BUY",
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }

        # Simulate receiving a fused signal
        self.bus.simulate_message("signals.fused", time.time(), signal)

        # Wait for processing
        time.sleep(0.1)

        # Check that a message was published to signals.llm
        self.assertTrue(len(self.bus.published_messages) > 0)
        self.assertEqual(self.bus.published_messages[0][0], "signals.llm")

        # Check the content of the published message
        llm_signal = self.bus.published_messages[0][2]
        self.assertEqual(llm_signal["symbol"], signal["symbol"])
        self.assertEqual(llm_signal["action"], "BUY")
        self.assertGreater(llm_signal["confidence"], 0)
        self.assertIn("rationale", llm_signal)

        # Stop the consumer
        consumer.stop()

    def test_throttling(self):
        """Test throttling of LLM calls."""
        # Create a consumer with a longer interval
        self.config["call_interval_s"] = 5
        consumer = LLMConsumer(self.bus, self.config)

        # Start the consumer
        consumer.start()

        # Create a test signal
        signal = {
            "symbol": "BTC-USDT",
            "score": 0.5,
            "decision": "BUY",
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }

        # Simulate receiving multiple fused signals
        self.bus.simulate_message("signals.fused", time.time(), signal)
        self.bus.simulate_message("signals.fused", time.time(), signal)
        self.bus.simulate_message("signals.fused", time.time(), signal)

        # Wait for processing
        time.sleep(0.1)

        # Check that only one message was published (due to throttling)
        self.assertEqual(len(self.bus.published_messages), 1)

        # Stop the consumer
        consumer.stop()

    def test_feature_context(self):
        """Test inclusion of feature context in the prompt."""
        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config)

        # Start the consumer
        consumer.start()

        # Add some feature data
        self.bus.simulate_message("features.volatility", time.time(), {"z_score": 1.5})
        self.bus.simulate_message("features.funding", time.time(), {"z_score": -0.8})
        self.bus.simulate_message("features.vwap", time.time(), {"z_score": 0.3})

        # Create a test signal
        signal = {
            "symbol": "BTC-USDT",
            "score": 0.5,
            "decision": "BUY",
            "confidence": 0.8,
            "timestamp": datetime.now().isoformat()
        }

        # Simulate receiving a fused signal
        self.bus.simulate_message("signals.fused", time.time(), signal)

        # Wait for processing
        time.sleep(0.1)

        # Check that the LLM was called with a prompt containing the feature context
        self.assertTrue(len(consumer.llm.calls) > 0 if consumer.llm else True)

        # Stop the consumer
        consumer.stop()

    def test_normalize_decision_string_response(self):
        """Test normalizing a string response from the LLM."""
        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config)

        # Create a string response
        response = "I recommend to BUY because the market looks good."

        # Normalize the response
        result = consumer._normalize_decision(response)

        # Check the result
        self.assertEqual(result["action"], "BUY")
        self.assertEqual(result["confidence"], 0.5)
        self.assertEqual(result["rationale"], response)

    def test_normalize_decision_invalid_json(self):
        """Test normalizing an invalid JSON response."""
        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config)

        # Create an invalid JSON response
        response = '{"action": "BUY", "confidence": 0.75, "rationale": "Test rationale'

        # Normalize the response
        result = consumer._normalize_decision(response)

        # Check the result - should treat it as a string
        self.assertEqual(result["action"], "BUY")
        self.assertEqual(result["confidence"], 0.5)
        self.assertEqual(result["rationale"], response)

    def test_normalize_string_signal(self):
        """Test normalizing a string signal."""
        # Create a consumer
        consumer = LLMConsumer(self.bus, self.config)

        # Create a string signal
        signal = "BUY signal from test source"

        # Create a mock _publish_result method to test handling of string signals
        original_publish_result = consumer._publish_result

        try:
            # Replace _publish_result with a mock
            result_dict = {}

            def mock_publish_result(result, original_signal):
                nonlocal result_dict
                result_dict = {
                    "result": result,
                    "original_signal": original_signal
                }

            consumer._publish_result = mock_publish_result

            # Call _normalize_decision directly
            normalized = consumer._normalize_decision(signal)

            # Check the result
            self.assertEqual(normalized["action"], "BUY")
            self.assertEqual(normalized["confidence"], 0.5)
            self.assertEqual(normalized["rationale"], signal)

        finally:
            # Restore original method
            consumer._publish_result = original_publish_result


if __name__ == "__main__":
    unittest.main()
