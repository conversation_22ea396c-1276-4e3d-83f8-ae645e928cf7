#!/usr/bin/env python3
"""
Performance Monitoring System for Smart-Trader

Tracks system metrics, trading performance, and provides real-time monitoring.
"""

import asyncio
import psutil
import time
import threading
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import json


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int


@dataclass
class TradingMetrics:
    """Trading performance metrics."""
    timestamp: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    unrealized_pnl: float
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    current_drawdown: float
    average_trade_duration: float
    trades_per_hour: float


@dataclass
class APIMetrics:
    """API performance metrics."""
    timestamp: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    requests_per_minute: float
    error_rate: float
    active_connections: int


@dataclass
class ModelMetrics:
    """ML Model performance metrics."""
    timestamp: str
    model_name: str
    predictions_made: int
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    inference_time_ms: float
    confidence_avg: float


class PerformanceMonitor:
    """Real-time performance monitoring system."""
    
    def __init__(self, history_size: int = 1000, update_interval: int = 5):
        """Initialize performance monitor."""
        self.history_size = history_size
        self.update_interval = update_interval
        
        # Metric storage
        self.system_metrics: deque = deque(maxlen=history_size)
        self.trading_metrics: deque = deque(maxlen=history_size)
        self.api_metrics: deque = deque(maxlen=history_size)
        self.model_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        
        # Counters and accumulators
        self.api_requests = defaultdict(int)
        self.api_response_times = defaultdict(list)
        self.trade_history = []
        self.model_predictions = defaultdict(list)
        
        # Network baseline
        self.network_baseline = self._get_network_stats()
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        
        # Subscribers for real-time updates
        self.subscribers = []

    def start_monitoring(self) -> None:
        """Start performance monitoring."""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

    def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)

    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self.monitoring:
            try:
                # Collect system metrics
                system_metrics = self._collect_system_metrics()
                self.system_metrics.append(system_metrics)
                
                # Collect API metrics
                api_metrics = self._collect_api_metrics()
                self.api_metrics.append(api_metrics)
                
                # Collect trading metrics
                trading_metrics = self._collect_trading_metrics()
                self.trading_metrics.append(trading_metrics)
                
                # Notify subscribers
                self._notify_subscribers({
                    'system': asdict(system_metrics),
                    'api': asdict(api_metrics),
                    'trading': asdict(trading_metrics)
                })
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(self.update_interval)

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect system performance metrics."""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        # Network stats
        network = self._get_network_stats()
        network_sent = (network['bytes_sent'] - self.network_baseline['bytes_sent']) / (1024 * 1024)
        network_recv = (network['bytes_recv'] - self.network_baseline['bytes_recv']) / (1024 * 1024)
        
        # Process info
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            memory_available_mb=memory.available / (1024 * 1024),
            disk_usage_percent=disk.percent,
            disk_free_gb=disk.free / (1024 * 1024 * 1024),
            network_sent_mb=network_sent,
            network_recv_mb=network_recv,
            process_count=process_count,
            thread_count=thread_count
        )

    def _collect_api_metrics(self) -> APIMetrics:
        """Collect API performance metrics."""
        total_requests = sum(self.api_requests.values())
        successful_requests = self.api_requests.get('success', 0)
        failed_requests = total_requests - successful_requests
        
        # Calculate average response time
        all_response_times = []
        for times in self.api_response_times.values():
            all_response_times.extend(times)
        
        avg_response_time = sum(all_response_times) / len(all_response_times) if all_response_times else 0
        
        # Calculate requests per minute
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        recent_requests = sum(1 for times in self.api_response_times.values() 
                            for _ in times if len(times) > 0)
        
        error_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        
        return APIMetrics(
            timestamp=datetime.now().isoformat(),
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=avg_response_time,
            requests_per_minute=recent_requests,
            error_rate=error_rate,
            active_connections=0  # Will be updated by web server
        )

    def _collect_trading_metrics(self) -> TradingMetrics:
        """Collect trading performance metrics."""
        if not self.trade_history:
            return TradingMetrics(
                timestamp=datetime.now().isoformat(),
                total_trades=0, winning_trades=0, losing_trades=0,
                total_pnl=0.0, unrealized_pnl=0.0, win_rate=0.0,
                profit_factor=0.0, sharpe_ratio=0.0, max_drawdown=0.0,
                current_drawdown=0.0, average_trade_duration=0.0,
                trades_per_hour=0.0
            )
        
        # Calculate metrics from trade history
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl', 0) > 0)
        losing_trades = total_trades - winning_trades
        
        total_pnl = sum(trade.get('pnl', 0) for trade in self.trade_history)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Calculate profit factor
        gross_profit = sum(trade.get('pnl', 0) for trade in self.trade_history if trade.get('pnl', 0) > 0)
        gross_loss = abs(sum(trade.get('pnl', 0) for trade in self.trade_history if trade.get('pnl', 0) < 0))
        profit_factor = (gross_profit / gross_loss) if gross_loss > 0 else 0
        
        # Calculate average trade duration
        durations = [trade.get('duration', 0) for trade in self.trade_history if 'duration' in trade]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # Calculate trades per hour
        if self.trade_history:
            first_trade_time = datetime.fromisoformat(self.trade_history[0].get('timestamp', datetime.now().isoformat()))
            last_trade_time = datetime.fromisoformat(self.trade_history[-1].get('timestamp', datetime.now().isoformat()))
            time_diff_hours = (last_trade_time - first_trade_time).total_seconds() / 3600
            trades_per_hour = total_trades / time_diff_hours if time_diff_hours > 0 else 0
        else:
            trades_per_hour = 0
        
        return TradingMetrics(
            timestamp=datetime.now().isoformat(),
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            total_pnl=total_pnl,
            unrealized_pnl=0.0,  # Will be updated from positions
            win_rate=win_rate,
            profit_factor=profit_factor,
            sharpe_ratio=0.0,  # Requires more complex calculation
            max_drawdown=0.0,  # Requires equity curve analysis
            current_drawdown=0.0,
            average_trade_duration=avg_duration,
            trades_per_hour=trades_per_hour
        )

    def _get_network_stats(self) -> Dict[str, int]:
        """Get network statistics."""
        stats = psutil.net_io_counters()
        return {
            'bytes_sent': stats.bytes_sent,
            'bytes_recv': stats.bytes_recv,
            'packets_sent': stats.packets_sent,
            'packets_recv': stats.packets_recv
        }

    def record_api_call(self, endpoint: str, response_time: float, success: bool) -> None:
        """Record API call metrics."""
        status = 'success' if success else 'error'
        self.api_requests[status] += 1
        self.api_response_times[endpoint].append(response_time)
        
        # Keep only recent response times (last 100)
        if len(self.api_response_times[endpoint]) > 100:
            self.api_response_times[endpoint] = self.api_response_times[endpoint][-100:]

    def record_trade(self, trade_data: Dict[str, Any]) -> None:
        """Record trade for performance tracking."""
        trade_data['timestamp'] = datetime.now().isoformat()
        self.trade_history.append(trade_data)
        
        # Keep only recent trades
        if len(self.trade_history) > self.history_size:
            self.trade_history = self.trade_history[-self.history_size:]

    def record_model_prediction(self, model_name: str, prediction_data: Dict[str, Any]) -> None:
        """Record model prediction for performance tracking."""
        prediction_data['timestamp'] = datetime.now().isoformat()
        self.model_predictions[model_name].append(prediction_data)

    def get_system_summary(self) -> Dict[str, Any]:
        """Get system performance summary."""
        if not self.system_metrics:
            return {}
        
        latest = self.system_metrics[-1]
        return {
            'cpu_percent': latest.cpu_percent,
            'memory_percent': latest.memory_percent,
            'disk_usage_percent': latest.disk_usage_percent,
            'network_activity': {
                'sent_mb': latest.network_sent_mb,
                'recv_mb': latest.network_recv_mb
            },
            'processes': latest.process_count,
            'threads': latest.thread_count,
            'timestamp': latest.timestamp
        }

    def get_trading_summary(self) -> Dict[str, Any]:
        """Get trading performance summary."""
        if not self.trading_metrics:
            return {}
        
        latest = self.trading_metrics[-1]
        return asdict(latest)

    def get_api_summary(self) -> Dict[str, Any]:
        """Get API performance summary."""
        if not self.api_metrics:
            return {}
        
        latest = self.api_metrics[-1]
        return asdict(latest)

    def get_historical_data(self, metric_type: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical performance data."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        if metric_type == 'system':
            data = [asdict(m) for m in self.system_metrics 
                   if datetime.fromisoformat(m.timestamp) > cutoff_time]
        elif metric_type == 'trading':
            data = [asdict(m) for m in self.trading_metrics 
                   if datetime.fromisoformat(m.timestamp) > cutoff_time]
        elif metric_type == 'api':
            data = [asdict(m) for m in self.api_metrics 
                   if datetime.fromisoformat(m.timestamp) > cutoff_time]
        else:
            data = []
        
        return data

    def subscribe(self, callback) -> None:
        """Subscribe to performance updates."""
        self.subscribers.append(callback)

    def unsubscribe(self, callback) -> None:
        """Unsubscribe from performance updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def _notify_subscribers(self, data: Dict[str, Any]) -> None:
        """Notify subscribers of performance updates."""
        for callback in self.subscribers:
            try:
                callback(data)
            except Exception:
                pass  # Don't let subscriber errors affect monitoring

    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        if not self.system_metrics:
            return {'status': 'unknown', 'issues': ['No metrics available']}
        
        latest = self.system_metrics[-1]
        issues = []
        
        # Check CPU usage
        if latest.cpu_percent > 80:
            issues.append(f"High CPU usage: {latest.cpu_percent:.1f}%")
        
        # Check memory usage
        if latest.memory_percent > 85:
            issues.append(f"High memory usage: {latest.memory_percent:.1f}%")
        
        # Check disk usage
        if latest.disk_usage_percent > 90:
            issues.append(f"High disk usage: {latest.disk_usage_percent:.1f}%")
        
        # Check API error rate
        if self.api_metrics:
            latest_api = self.api_metrics[-1]
            if latest_api.error_rate > 5:
                issues.append(f"High API error rate: {latest_api.error_rate:.1f}%")
        
        status = 'healthy' if not issues else 'warning' if len(issues) < 3 else 'critical'
        
        return {
            'status': status,
            'issues': issues,
            'timestamp': latest.timestamp,
            'uptime_seconds': time.time() - psutil.boot_time()
        }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def start_monitoring() -> None:
    """Start performance monitoring."""
    performance_monitor.start_monitoring()


def stop_monitoring() -> None:
    """Stop performance monitoring."""
    performance_monitor.stop_monitoring()


def record_api_call(endpoint: str, response_time: float, success: bool) -> None:
    """Record API call."""
    performance_monitor.record_api_call(endpoint, response_time, success)


def record_trade(trade_data: Dict[str, Any]) -> None:
    """Record trade."""
    performance_monitor.record_trade(trade_data)


def get_system_summary() -> Dict[str, Any]:
    """Get system summary."""
    return performance_monitor.get_system_summary()


def get_health_status() -> Dict[str, Any]:
    """Get health status."""
    return performance_monitor.get_health_status()
