#!/usr/bin/env python3
"""
Advanced Manual Trading Interface
Professional trading interface with checkbox-controlled automation features
Designed for scalping and manual futures trading with automated assistance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
import json
from enum import Enum

from .htx_futures_client import HTXFuturesClient, Position, AccountBalance

logger = logging.getLogger(__name__)

class AutomationFeature(Enum):
    """Available automation features."""
    AUTO_CLOSE_PNL_USD = "auto_close_pnl_usd"
    AUTO_CLOSE_PNL_PERCENT = "auto_close_pnl_percent"
    AUTO_CLOSE_TIME = "auto_close_time"
    AUTO_TAKE_PROFIT = "auto_take_profit"
    AUTO_STOP_LOSS = "auto_stop_loss"
    TRAILING_STOP_LOSS = "trailing_stop_loss"
    QUICK_CLOSE_ALL = "quick_close_all"
    AUTO_SCALING_OUT = "auto_scaling_out"
    BREAKEVEN_STOP = "breakeven_stop"
    TIME_BASED_EXIT = "time_based_exit"

@dataclass
class AutomationConfig:
    """Configuration for automation features."""
    feature: AutomationFeature
    enabled: bool
    parameters: Dict[str, Any]
    created_at: datetime
    last_triggered: Optional[datetime] = None

@dataclass
class TradingState:
    """Current trading state and position information."""
    position: Optional[Position]
    account_balance: Optional[AccountBalance]
    current_price: float
    automation_configs: Dict[AutomationFeature, AutomationConfig]
    active_alerts: List[str]
    last_update: datetime

class AdvancedTradingInterface:
    """Professional manual trading interface with automated assistance."""

    def __init__(self, htx_client: HTXFuturesClient):
        """Initialize advanced trading interface."""
        self.htx_client = htx_client
        
        # Trading state
        self.trading_state = TradingState(
            position=None,
            account_balance=None,
            current_price=0.0,
            automation_configs={},
            active_alerts=[],
            last_update=datetime.now()
        )
        
        # Automation tracking
        self.position_entry_time: Optional[datetime] = None
        self.highest_profit_seen = 0.0
        self.trailing_stop_price = 0.0
        
        # Control flags
        self.running = False
        self.monitoring_interval = 0.5  # 500ms for scalping
        
        # Event callbacks
        self.on_automation_triggered: Optional[Callable] = None
        self.on_position_closed: Optional[Callable] = None
        self.on_alert_generated: Optional[Callable] = None
        
        logger.info("[TRADING] Advanced trading interface initialized")

    async def start_monitoring(self):
        """Start real-time monitoring and automation."""
        if self.running:
            return
        
        self.running = True
        logger.info("🚀 Starting advanced trading monitoring...")
        
        # Start monitoring tasks
        tasks = [
            self._monitor_trading_state(),
            self._process_automations(),
            self._monitor_risk_management(),
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _monitor_trading_state(self):
        """Monitor current trading state and update data."""
        while self.running:
            try:
                # Update position data
                position = await self.htx_client.get_current_position()
                self.trading_state.position = position
                
                # Track position entry time
                if position and not self.position_entry_time:
                    self.position_entry_time = datetime.now()
                elif not position:
                    self.position_entry_time = None
                    self.highest_profit_seen = 0.0
                    self.trailing_stop_price = 0.0
                
                # Update account balance
                self.trading_state.account_balance = await self.htx_client.get_account_info()
                
                # Update current price
                self.trading_state.current_price = await self.htx_client.get_market_price()
                
                # Update timestamp
                self.trading_state.last_update = datetime.now()
                
                # Track highest profit for trailing stops
                if position and position.pnl_usd > self.highest_profit_seen:
                    self.highest_profit_seen = position.pnl_usd
                
            except Exception as e:
                logger.error(f"❌ Error monitoring trading state: {e}")
            
            await asyncio.sleep(self.monitoring_interval)

    async def _process_automations(self):
        """Process all enabled automation features."""
        while self.running:
            try:
                position = self.trading_state.position
                if not position:
                    await asyncio.sleep(self.monitoring_interval)
                    continue
                
                # Process each enabled automation
                for feature, config in self.trading_state.automation_configs.items():
                    if not config.enabled:
                        continue
                    
                    try:
                        await self._process_automation_feature(feature, config, position)
                    except Exception as e:
                        logger.error(f"❌ Error processing {feature.value}: {e}")
                
            except Exception as e:
                logger.error(f"❌ Error in automation processing: {e}")
            
            await asyncio.sleep(self.monitoring_interval)

    async def _process_automation_feature(self, feature: AutomationFeature, 
                                        config: AutomationConfig, position: Position):
        """Process individual automation feature."""
        params = config.parameters
        
        if feature == AutomationFeature.AUTO_CLOSE_PNL_USD:
            threshold = params.get('threshold_usd', 0)
            if position.pnl_usd >= threshold and threshold > 0:
                await self._trigger_automation(feature, f"PnL reached ${threshold}")
                await self.htx_client.close_position()
            elif position.pnl_usd <= -abs(threshold) and threshold < 0:
                await self._trigger_automation(feature, f"Loss reached ${abs(threshold)}")
                await self.htx_client.close_position()
        
        elif feature == AutomationFeature.AUTO_CLOSE_PNL_PERCENT:
            threshold = params.get('threshold_percent', 0)
            if position.pnl_percent >= threshold and threshold > 0:
                await self._trigger_automation(feature, f"PnL reached {threshold}%")
                await self.htx_client.close_position()
            elif position.pnl_percent <= -abs(threshold) and threshold < 0:
                await self._trigger_automation(feature, f"Loss reached {abs(threshold)}%")
                await self.htx_client.close_position()
        
        elif feature == AutomationFeature.AUTO_CLOSE_TIME:
            max_duration = params.get('max_duration_minutes', 0)
            if self.position_entry_time and max_duration > 0:
                elapsed = (datetime.now() - self.position_entry_time).total_seconds() / 60
                if elapsed >= max_duration:
                    await self._trigger_automation(feature, f"Time limit reached ({max_duration}min)")
                    await self.htx_client.close_position()
        
        elif feature == AutomationFeature.TRAILING_STOP_LOSS:
            trail_distance = params.get('trail_distance_percent', 2.0)
            if position.side == 'long':
                # For long positions, trail below current price
                new_stop = self.trading_state.current_price * (1 - trail_distance / 100)
                if new_stop > self.trailing_stop_price:
                    self.trailing_stop_price = new_stop
                
                if self.trading_state.current_price <= self.trailing_stop_price:
                    await self._trigger_automation(feature, f"Trailing stop hit at ${self.trailing_stop_price:.6f}")
                    await self.htx_client.close_position()
            
            else:  # short position
                # For short positions, trail above current price
                new_stop = self.trading_state.current_price * (1 + trail_distance / 100)
                if new_stop < self.trailing_stop_price or self.trailing_stop_price == 0:
                    self.trailing_stop_price = new_stop
                
                if self.trading_state.current_price >= self.trailing_stop_price:
                    await self._trigger_automation(feature, f"Trailing stop hit at ${self.trailing_stop_price:.6f}")
                    await self.htx_client.close_position()
        
        elif feature == AutomationFeature.BREAKEVEN_STOP:
            profit_threshold = params.get('profit_threshold_percent', 1.0)
            if position.pnl_percent >= profit_threshold:
                # Move stop to breakeven (entry price)
                if position.side == 'long' and self.trading_state.current_price <= position.entry_price:
                    await self._trigger_automation(feature, "Breakeven stop triggered")
                    await self.htx_client.close_position()
                elif position.side == 'short' and self.trading_state.current_price >= position.entry_price:
                    await self._trigger_automation(feature, "Breakeven stop triggered")
                    await self.htx_client.close_position()

    async def _trigger_automation(self, feature: AutomationFeature, reason: str):
        """Trigger automation feature and log the action."""
        config = self.trading_state.automation_configs.get(feature)
        if config:
            config.last_triggered = datetime.now()
        
        message = f"🤖 {feature.value}: {reason}"
        logger.info(message)
        
        # Add to alerts
        self.trading_state.active_alerts.append(message)
        
        # Callback notification
        if self.on_automation_triggered:
            await self.on_automation_triggered(feature, reason)

    async def _monitor_risk_management(self):
        """Monitor risk management and generate alerts."""
        while self.running:
            try:
                position = self.trading_state.position
                balance = self.trading_state.account_balance
                
                if position and balance:
                    # Check margin ratio
                    if balance.margin_ratio > 80:  # 80% margin usage
                        alert = f"⚠️ High margin usage: {balance.margin_ratio:.1f}%"
                        if alert not in self.trading_state.active_alerts:
                            self.trading_state.active_alerts.append(alert)
                    
                    # Check large unrealized loss
                    if position.pnl_usd < -balance.total_balance * 0.1:  # 10% of account
                        alert = f"⚠️ Large unrealized loss: ${position.pnl_usd:.2f}"
                        if alert not in self.trading_state.active_alerts:
                            self.trading_state.active_alerts.append(alert)
                
                # Clean old alerts (older than 5 minutes)
                current_time = datetime.now()
                self.trading_state.active_alerts = [
                    alert for alert in self.trading_state.active_alerts
                    if not any(timestamp in alert for timestamp in [])  # Keep all for now
                ]
                
            except Exception as e:
                logger.error(f"❌ Error in risk monitoring: {e}")
            
            await asyncio.sleep(5.0)  # Check every 5 seconds

    # Public interface methods
    
    def enable_automation(self, feature: AutomationFeature, parameters: Dict[str, Any]):
        """Enable an automation feature with specified parameters."""
        config = AutomationConfig(
            feature=feature,
            enabled=True,
            parameters=parameters,
            created_at=datetime.now()
        )
        
        self.trading_state.automation_configs[feature] = config
        logger.info(f"✅ Enabled automation: {feature.value} with params {parameters}")

    def disable_automation(self, feature: AutomationFeature):
        """Disable an automation feature."""
        if feature in self.trading_state.automation_configs:
            self.trading_state.automation_configs[feature].enabled = False
            logger.info(f"❌ Disabled automation: {feature.value}")

    def get_automation_status(self) -> Dict[str, Any]:
        """Get current automation status."""
        return {
            feature.value: {
                'enabled': config.enabled,
                'parameters': config.parameters,
                'last_triggered': config.last_triggered.isoformat() if config.last_triggered else None
            }
            for feature, config in self.trading_state.automation_configs.items()
        }

    async def emergency_close_all(self) -> bool:
        """Emergency close all positions immediately."""
        try:
            logger.warning("🚨 EMERGENCY CLOSE ALL POSITIONS")
            result = await self.htx_client.close_position()
            
            if result:
                alert = "🚨 Emergency close executed"
                self.trading_state.active_alerts.append(alert)
                
                if self.on_position_closed:
                    await self.on_position_closed("emergency")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Emergency close failed: {e}")
            return False

    def get_trading_state(self) -> Dict[str, Any]:
        """Get current trading state for UI display."""
        return {
            'position': asdict(self.trading_state.position) if self.trading_state.position else None,
            'account_balance': asdict(self.trading_state.account_balance) if self.trading_state.account_balance else None,
            'current_price': self.trading_state.current_price,
            'automation_status': self.get_automation_status(),
            'active_alerts': self.trading_state.active_alerts,
            'last_update': self.trading_state.last_update.isoformat(),
            'position_entry_time': self.position_entry_time.isoformat() if self.position_entry_time else None,
            'highest_profit_seen': self.highest_profit_seen,
            'trailing_stop_price': self.trailing_stop_price
        }

    def stop_monitoring(self):
        """Stop all monitoring and automation."""
        self.running = False
        logger.info("🛑 Advanced trading interface stopped")
