#!/usr/bin/env python3
"""
Money Circle Production Startup Script
Production-ready startup with health checks and monitoring.
"""

import os
import sys
import asyncio
import logging
import signal
import time
from pathlib import Path
from datetime import datetime

# Add current directory to path
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

# Setup production logging
def setup_production_logging():
    """Setup production logging configuration."""
    log_level = os.getenv('LOG_LEVEL', 'WARNING')
    log_file = os.getenv('LOG_FILE', '/opt/money_circle/logs/money_circle.log')
    
    # Ensure log directory exists
    Path(log_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

logger = setup_production_logging()

class ProductionServer:
    """Production server manager for Money Circle."""
    
    def __init__(self):
        self.app = None
        self.shutdown_event = asyncio.Event()
        self.health_check_task = None
        
    def load_environment(self):
        """Load production environment variables."""
        env_file = Path(__file__).parent / '.env.production'
        
        if env_file.exists():
            logger.info("📄 Loading production environment from .env.production")
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
        else:
            logger.warning("⚠️ .env.production file not found, using environment variables")
        
        # Validate required environment variables
        required_vars = ['SECRET_KEY', 'DATABASE_PATH']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
            sys.exit(1)
        
        logger.info("✅ Environment configuration loaded")
    
    def validate_production_environment(self):
        """Validate production environment setup."""
        logger.info("🔍 Validating production environment...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("❌ Python 3.8+ required for production")
            sys.exit(1)
        
        # Check database directory
        db_path = Path(os.getenv('DATABASE_PATH', 'data/money_circle.db'))
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Check log directory
        log_file = Path(os.getenv('LOG_FILE', 'logs/money_circle.log'))
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Check SSL certificates if HTTPS is enabled
        if os.getenv('FORCE_HTTPS', 'True').lower() == 'true':
            ssl_cert = os.getenv('SSL_CERT_PATH')
            ssl_key = os.getenv('SSL_KEY_PATH')
            
            if ssl_cert and ssl_key:
                if not Path(ssl_cert).exists():
                    logger.warning(f"⚠️ SSL certificate not found: {ssl_cert}")
                if not Path(ssl_key).exists():
                    logger.warning(f"⚠️ SSL key not found: {ssl_key}")
            else:
                logger.info("ℹ️ HTTPS enabled but SSL certificates not configured")
        
        logger.info("✅ Production environment validation complete")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)
    
    async def health_check_monitor(self):
        """Monitor application health and log status."""
        while not self.shutdown_event.is_set():
            try:
                # Basic health check
                if self.app and hasattr(self.app, 'db_manager'):
                    # Test database connectivity
                    cursor = self.app.db_manager.conn.execute("SELECT 1")
                    cursor.fetchone()
                    
                    # Log health status every 5 minutes
                    logger.debug("💚 Health check: Application healthy")
                
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"❤️‍🩹 Health check failed: {e}")
                await asyncio.sleep(60)  # Retry in 1 minute
    
    async def start_application(self):
        """Start the Money Circle application."""
        try:
            from app import MoneyCircleApp
            
            # Create application instance
            self.app = MoneyCircleApp('production')
            logger.info("🏗️ Money Circle application instance created")
            
            # Start health monitoring
            self.health_check_task = asyncio.create_task(self.health_check_monitor())
            
            # Start the server
            await self.app.start_server()
            
        except Exception as e:
            logger.error(f"❌ Failed to start application: {e}")
            raise
    
    async def graceful_shutdown(self):
        """Perform graceful shutdown of the application."""
        logger.info("🛑 Starting graceful shutdown...")
        
        # Cancel health check task
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # Shutdown application components
        if self.app:
            try:
                # Stop advanced systems
                if hasattr(self.app, 'market_data_manager') and self.app.market_data_manager:
                    await self.app.market_data_manager.stop()
                    logger.info("📊 Market data manager stopped")
                
                if hasattr(self.app, 'notification_manager') and self.app.notification_manager:
                    await self.app.notification_manager.stop_notification_system()
                    logger.info("🔔 Notification manager stopped")
                
                if hasattr(self.app, 'live_trading_interface') and self.app.live_trading_interface:
                    await self.app.live_trading_interface.stop_monitoring()
                    logger.info("💹 Trading interface stopped")
                
                if hasattr(self.app, 'live_strategy_engine') and self.app.live_strategy_engine:
                    await self.app.live_strategy_engine.stop_automation()
                    logger.info("🤖 Strategy engine stopped")
                
            except Exception as e:
                logger.error(f"⚠️ Error during component shutdown: {e}")
        
        logger.info("✅ Graceful shutdown complete")
    
    async def run(self):
        """Run the production server."""
        try:
            # Setup environment
            self.load_environment()
            self.validate_production_environment()
            self.setup_signal_handlers()
            
            # Log startup information
            logger.info("🚀 Starting Money Circle Investment Club Platform")
            logger.info(f"🌍 Environment: {os.getenv('FLASK_ENV', 'production')}")
            logger.info(f"🏠 Host: {os.getenv('PROD_HOST', '0.0.0.0')}")
            logger.info(f"🔌 Port: {os.getenv('PROD_PORT', '8085')}")
            logger.info(f"🔒 HTTPS: {os.getenv('FORCE_HTTPS', 'True')}")
            logger.info(f"📊 Compression: {os.getenv('ENABLE_COMPRESSION', 'True')}")
            
            # Start application
            await self.start_application()
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Graceful shutdown
            await self.graceful_shutdown()
            
        except Exception as e:
            logger.error(f"❌ Production server error: {e}")
            raise

def check_dependencies():
    """Check if all required dependencies are available."""
    required_packages = [
        'aiohttp',
        'aiohttp_cors',
        'aiohttp_session', 
        'aiohttp_jinja2',
        'cryptography',
        'bcrypt'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing required packages: {', '.join(missing_packages)}")
        logger.error("💡 Install with: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main production startup function."""
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Create and run production server
    server = ProductionServer()
    
    try:
        asyncio.run(server.run())
    except KeyboardInterrupt:
        logger.info("🛑 Money Circle production server stopped by user")
    except Exception as e:
        logger.error(f"❌ Production server failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
