#!/usr/bin/env python3
"""
Test Smart Trader Integration
Comprehensive test to verify Money Circle integration with Smart Trader system.
"""

import asyncio
import sqlite3
import os
import sys
import json
import logging
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from market_data.bus_integration import get_market_data_bus
from market_data.advanced_market_data_manager import AdvancedMarketDataManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartTraderIntegrationTest:
    """Test Smart Trader integration components."""
    
    def __init__(self):
        self.bus_path = "../../data/bus.db"
        self.results = {}
    
    async def run_all_tests(self):
        """Run comprehensive integration tests."""
        logger.info("🚀 Starting Smart Trader Integration Tests...")
        
        # Test 1: Database connectivity
        await self.test_database_connectivity()
        
        # Test 2: Market data bus integration
        await self.test_market_data_bus()
        
        # Test 3: Advanced market data manager
        await self.test_advanced_market_data_manager()
        
        # Test 4: Real-time data flow
        await self.test_real_time_data_flow()
        
        # Generate report
        self.generate_report()
    
    async def test_database_connectivity(self):
        """Test connection to Smart Trader database."""
        logger.info("📊 Testing database connectivity...")
        
        try:
            # Check if bus database exists
            if os.path.exists(self.bus_path):
                logger.info(f"✅ Smart Trader bus found at: {self.bus_path}")
                
                # Connect and check tables
                conn = sqlite3.connect(self.bus_path)
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                logger.info(f"📋 Tables found: {tables}")
                
                # Check for bus_messages table
                if 'bus_messages' in tables:
                    cursor = conn.execute("SELECT COUNT(*) FROM bus_messages")
                    count = cursor.fetchone()[0]
                    logger.info(f"📈 Total messages in bus: {count}")
                    
                    # Check for recent data
                    cursor = conn.execute("""
                        SELECT COUNT(*) FROM bus_messages 
                        WHERE timestamp > datetime('now', '-1 hour')
                    """)
                    recent_count = cursor.fetchone()[0]
                    logger.info(f"🕐 Recent messages (last hour): {recent_count}")
                    
                    self.results['database_connectivity'] = {
                        'status': 'success',
                        'tables': tables,
                        'total_messages': count,
                        'recent_messages': recent_count
                    }
                else:
                    logger.warning("⚠️ bus_messages table not found")
                    self.results['database_connectivity'] = {
                        'status': 'warning',
                        'message': 'bus_messages table not found',
                        'tables': tables
                    }
                
                conn.close()
            else:
                logger.error(f"❌ Smart Trader bus not found at: {self.bus_path}")
                self.results['database_connectivity'] = {
                    'status': 'error',
                    'message': f'Database not found at {self.bus_path}'
                }
                
        except Exception as e:
            logger.error(f"❌ Database connectivity test failed: {e}")
            self.results['database_connectivity'] = {
                'status': 'error',
                'message': str(e)
            }
    
    async def test_market_data_bus(self):
        """Test market data bus integration."""
        logger.info("🔌 Testing market data bus integration...")
        
        try:
            # Get market data bus instance
            bus = get_market_data_bus()
            
            # Test connection
            connected = await bus.connect()
            
            if connected:
                logger.info("✅ Market data bus connected successfully")
                
                # Test getting latest market data
                symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT']
                market_data = {}
                
                for symbol in symbols:
                    data = await bus.get_latest_market_data(symbol)
                    if data:
                        market_data[symbol] = data
                        logger.info(f"📊 {symbol}: ${data.get('price', 'N/A')}")
                
                # Test orderbook data
                orderbook = await bus.get_latest_orderbook('BTC-USDT')
                if orderbook:
                    logger.info(f"📖 BTC-USDT orderbook: {len(orderbook.get('bids', []))} bids, {len(orderbook.get('asks', []))} asks")
                
                # Test recent trades
                trades = await bus.get_recent_trades('BTC-USDT', 5)
                if trades:
                    logger.info(f"💱 BTC-USDT recent trades: {len(trades)} trades")
                
                self.results['market_data_bus'] = {
                    'status': 'success',
                    'connected': True,
                    'market_data_symbols': list(market_data.keys()),
                    'orderbook_available': bool(orderbook),
                    'trades_available': len(trades) if trades else 0
                }
            else:
                logger.warning("⚠️ Market data bus connection failed")
                self.results['market_data_bus'] = {
                    'status': 'warning',
                    'connected': False,
                    'message': 'Connection failed'
                }
                
        except Exception as e:
            logger.error(f"❌ Market data bus test failed: {e}")
            self.results['market_data_bus'] = {
                'status': 'error',
                'message': str(e)
            }
    
    async def test_advanced_market_data_manager(self):
        """Test advanced market data manager."""
        logger.info("🔄 Testing advanced market data manager...")
        
        try:
            # Initialize market data manager
            config = {
                'symbols': ['BTC/USDT', 'ETH/USDT'],
                'update_interval': 1.0,
                'max_trade_history': 100
            }
            
            manager = AdvancedMarketDataManager(config)
            logger.info("✅ Advanced market data manager initialized")
            
            # Test data sources status
            status = manager.get_connection_status()
            logger.info(f"📡 Connection status: {status}")
            
            self.results['advanced_market_data_manager'] = {
                'status': 'success',
                'initialized': True,
                'connection_status': status
            }
            
        except Exception as e:
            logger.error(f"❌ Advanced market data manager test failed: {e}")
            self.results['advanced_market_data_manager'] = {
                'status': 'error',
                'message': str(e)
            }
    
    async def test_real_time_data_flow(self):
        """Test real-time data flow."""
        logger.info("⚡ Testing real-time data flow...")
        
        try:
            # This would test actual WebSocket connections
            # For now, we'll simulate the test
            logger.info("🔄 Simulating real-time data flow test...")
            
            # In a real implementation, this would:
            # 1. Start WebSocket connections
            # 2. Subscribe to market data
            # 3. Verify data is flowing
            # 4. Check latency and quality
            
            self.results['real_time_data_flow'] = {
                'status': 'simulated',
                'message': 'Real-time testing requires active WebSocket connections'
            }
            
        except Exception as e:
            logger.error(f"❌ Real-time data flow test failed: {e}")
            self.results['real_time_data_flow'] = {
                'status': 'error',
                'message': str(e)
            }
    
    def generate_report(self):
        """Generate comprehensive test report."""
        logger.info("\n" + "="*60)
        logger.info("📋 SMART TRADER INTEGRATION TEST REPORT")
        logger.info("="*60)
        
        for test_name, result in self.results.items():
            status = result.get('status', 'unknown')
            status_icon = {
                'success': '✅',
                'warning': '⚠️',
                'error': '❌',
                'simulated': '🔄'
            }.get(status, '❓')
            
            logger.info(f"\n{status_icon} {test_name.replace('_', ' ').title()}: {status.upper()}")
            
            if 'message' in result:
                logger.info(f"   Message: {result['message']}")
            
            # Show additional details
            for key, value in result.items():
                if key not in ['status', 'message']:
                    logger.info(f"   {key}: {value}")
        
        # Overall assessment
        success_count = sum(1 for r in self.results.values() if r.get('status') == 'success')
        total_tests = len(self.results)
        
        logger.info(f"\n📊 OVERALL RESULTS: {success_count}/{total_tests} tests successful")
        
        if success_count == total_tests:
            logger.info("🎉 All tests passed! Smart Trader integration is working correctly.")
        elif success_count > total_tests // 2:
            logger.info("⚠️ Most tests passed. Some issues need attention.")
        else:
            logger.info("❌ Multiple issues detected. Smart Trader integration needs fixes.")
        
        logger.info("="*60)

async def main():
    """Run the integration tests."""
    test_runner = SmartTraderIntegrationTest()
    await test_runner.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
