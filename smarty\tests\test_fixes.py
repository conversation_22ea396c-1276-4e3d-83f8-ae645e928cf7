"""
Tests for the fixes to the funding rate parsing, FeatureStore asyncio.Lock, and LLMConsumer string handling.
"""
import asyncio
import json
import unittest
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

# Import the modules we want to test
from core.feature_store import FeatureStore
from llm_consumer import LLMConsumer
from orchestrator import Orchestrator


class TestFundingRateParsing(unittest.TestCase):
    """Test the funding rate parsing fix."""

    @patch('feeds.htx_funding.HTXFundingClient.get_funding_rate')
    @patch('core.feature_store.FeatureStore')
    async def test_funding_rate_parsing(self, mock_feature_store, mock_get_funding_rate):
        """Test that the funding rate is correctly parsed from the HTX API response."""
        # Mock the HTX API response
        mock_get_funding_rate.return_value = {
            "status": "ok",
            "data": {
                "funding_rate": "-0.00002856",
                "estimated_rate": "0.00001234",
                "next_funding_time": "2023-01-01T00:00:00"
            }
        }

        # Mock the feature store
        mock_feature_store.set = AsyncMock()
        mock_feature_store.add_time_series = AsyncMock()

        # Create an orchestrator instance
        orchestrator = Orchestrator(feature_store=mock_feature_store)

        # Call the method that fetches funding rates
        await orchestrator._fetch_historical_funding("BTC-USDT")

        # Check that the feature store was called with the correct values
        mock_feature_store.set.assert_called_with("BTC-USDT", "funding_rate", -0.00002856)
        mock_feature_store.add_time_series.assert_called()


class TestFeatureStoreLock(unittest.TestCase):
    """Test the FeatureStore asyncio.Lock fix."""

    async def test_feature_store_lock(self):
        """Test that the FeatureStore lock is created in the correct event loop."""
        # Create a feature store
        feature_store = FeatureStore()

        # Set a value
        await feature_store.set("BTC-USDT", "test", "value")

        # Get the value
        value = await feature_store.get("BTC-USDT", "test")

        # Check that the value was correctly stored
        self.assertEqual(value, "value")

        # Create a new event loop
        new_loop = asyncio.new_event_loop()

        # Run the same test in the new loop
        async def test_in_new_loop():
            # Set a value
            await feature_store.set("BTC-USDT", "test2", "value2")
            # Get the value
            value2 = await feature_store.get("BTC-USDT", "test2")
            # Check that the value was correctly stored
            self.assertEqual(value2, "value2")

        # Run the test in the new loop
        new_loop.run_until_complete(test_in_new_loop())
        new_loop.close()


class TestLLMConsumerStringHandling(unittest.TestCase):
    """Test the LLMConsumer string handling fix."""

    def test_normalize_decision_string(self):
        """Test that the _normalize_decision method correctly handles string responses."""
        # Create an LLMConsumer instance
        llm_consumer = LLMConsumer(symbols=["BTC-USDT"])

        # Test with a string response
        string_response = "I think we should BUY because the market looks good."
        normalized = llm_consumer._normalize_decision(string_response)

        # Check that the normalized response is a dictionary with the expected fields
        self.assertIsInstance(normalized, dict)
        self.assertEqual(normalized["action"], "BUY")
        self.assertEqual(normalized["confidence"], 0.5)
        self.assertEqual(normalized["rationale"], string_response)

    def test_normalize_decision_dict(self):
        """Test that the _normalize_decision method correctly handles dictionary responses."""
        # Create an LLMConsumer instance
        llm_consumer = LLMConsumer(symbols=["BTC-USDT"])

        # Test with a dictionary response
        dict_response = {
            "action": "SELL",
            "confidence": 0.75,
            "rationale": "The market looks bad."
        }
        normalized = llm_consumer._normalize_decision(dict_response)

        # Check that the normalized response is a dictionary with the expected fields
        self.assertIsInstance(normalized, dict)
        self.assertEqual(normalized["action"], "SELL")
        self.assertEqual(normalized["confidence"], 0.75)
        self.assertEqual(normalized["rationale"], "The market looks bad.")

    @patch('llm_consumer.LLMConsumer._publish_result')
    def test_process_signal_string(self, mock_publish_result):
        """Test that the _process_signal method correctly handles string signals."""
        # Create an LLMConsumer instance
        llm_consumer = LLMConsumer(symbols=["BTC-USDT"])
        llm_consumer.last_call = 0

        # Test with a string signal
        string_signal = "BUY signal with score 0.8"
        llm_consumer._process_signal(string_signal)

        # Check that _publish_result was called with a normalized signal
        mock_publish_result.assert_called()
        args = mock_publish_result.call_args[0]
        self.assertIsInstance(args[1], dict)  # Second argument should be a dict
        self.assertEqual(args[1]["action"], "BUY")


if __name__ == '__main__':
    unittest.main()
