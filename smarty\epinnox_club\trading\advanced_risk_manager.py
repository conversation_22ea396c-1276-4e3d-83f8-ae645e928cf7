#!/usr/bin/env python3
"""
Advanced Risk Management System for Money Circle
Real-time risk monitoring, automated stop-loss, and position sizing algorithms
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Risk alert types."""
    DRAWDOWN_WARNING = "drawdown_warning"
    POSITION_SIZE_LIMIT = "position_size_limit"
    LEVERAGE_LIMIT = "leverage_limit"
    DAILY_LOSS_LIMIT = "daily_loss_limit"
    MARGIN_CALL = "margin_call"
    VOLATILITY_SPIKE = "volatility_spike"
    CORRELATION_RISK = "correlation_risk"

@dataclass
class RiskAlert:
    """Risk alert data structure."""
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    timestamp: datetime
    data: Dict[str, Any] = field(default_factory=dict)
    action_required: bool = False

@dataclass
class PositionRisk:
    """Position-specific risk metrics."""
    symbol: str
    side: str
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    leverage: float
    margin_used: float
    liquidation_price: float
    risk_score: float  # 0-100
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None

@dataclass
class PortfolioRisk:
    """Portfolio-wide risk metrics."""
    total_balance: float
    available_balance: float
    margin_used: float
    unrealized_pnl: float
    daily_pnl: float
    max_drawdown: float
    margin_ratio: float
    risk_score: float  # 0-100
    var_95: float  # Value at Risk (95%)
    expected_shortfall: float
    correlation_risk: float
    volatility_score: float

class PositionSizingMethod(Enum):
    """Position sizing methods."""
    FIXED = "fixed"
    KELLY = "kelly"
    VOLATILITY = "volatility"
    RISK_PARITY = "risk_parity"

class AdvancedRiskManager:
    """Advanced risk management system with real-time monitoring and automated controls."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the advanced risk manager."""
        self.config = config or {}
        
        # Risk limits
        self.max_position_size = self.config.get('max_position_size', 1000.0)
        self.max_leverage = self.config.get('max_leverage', 10)
        self.max_daily_loss = self.config.get('max_daily_loss', 500.0)
        self.max_drawdown_pct = self.config.get('max_drawdown_pct', 5.0)
        self.stop_loss_pct = self.config.get('stop_loss_pct', 2.0)
        self.take_profit_pct = self.config.get('take_profit_pct', 4.0)
        
        # Position sizing
        self.position_sizing_method = PositionSizingMethod(
            self.config.get('position_sizing_method', 'fixed')
        )
        self.risk_per_trade_pct = self.config.get('risk_per_trade_pct', 1.0)
        
        # Monitoring
        self.monitoring_active = False
        self.alert_callbacks: List[Callable] = []
        self.position_risks: Dict[str, PositionRisk] = {}
        self.portfolio_risk: Optional[PortfolioRisk] = None
        self.alerts_history: List[RiskAlert] = []
        
        # Historical data for calculations
        self.price_history: Dict[str, List[float]] = {}
        self.pnl_history: List[float] = []
        self.daily_start_balance = 0.0
        
        logger.info("🛡️ Advanced Risk Manager initialized")
    
    def add_alert_callback(self, callback: Callable[[RiskAlert], None]):
        """Add callback for risk alerts."""
        self.alert_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start real-time risk monitoring."""
        self.monitoring_active = True
        logger.info("🚀 Starting advanced risk monitoring...")
        
        # Start monitoring tasks
        tasks = [
            self._monitor_portfolio_risk(),
            self._monitor_position_risks(),
            self._monitor_market_volatility(),
            self._check_stop_loss_conditions()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    def stop_monitoring(self):
        """Stop risk monitoring."""
        self.monitoring_active = False
        logger.info("🛑 Risk monitoring stopped")
    
    async def _monitor_portfolio_risk(self):
        """Monitor portfolio-wide risk metrics."""
        while self.monitoring_active:
            try:
                # Calculate portfolio risk metrics
                await self._calculate_portfolio_risk()
                
                # Check for risk alerts
                await self._check_portfolio_alerts()
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in portfolio risk monitoring: {e}")
                await asyncio.sleep(10)
    
    async def _monitor_position_risks(self):
        """Monitor individual position risks."""
        while self.monitoring_active:
            try:
                # Update position risk metrics
                for symbol, position_risk in self.position_risks.items():
                    await self._calculate_position_risk(position_risk)
                    await self._check_position_alerts(position_risk)
                
                await asyncio.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in position risk monitoring: {e}")
                await asyncio.sleep(5)
    
    async def _monitor_market_volatility(self):
        """Monitor market volatility and correlation risks."""
        while self.monitoring_active:
            try:
                # Calculate volatility metrics
                await self._calculate_volatility_metrics()
                
                # Check for volatility spikes
                await self._check_volatility_alerts()
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in volatility monitoring: {e}")
                await asyncio.sleep(15)
    
    async def _check_stop_loss_conditions(self):
        """Check and execute automated stop-loss orders."""
        while self.monitoring_active:
            try:
                for symbol, position_risk in self.position_risks.items():
                    if position_risk.stop_loss_price:
                        # Check if stop-loss should be triggered
                        should_trigger = False
                        
                        if position_risk.side == 'long':
                            should_trigger = position_risk.current_price <= position_risk.stop_loss_price
                        else:  # short
                            should_trigger = position_risk.current_price >= position_risk.stop_loss_price
                        
                        if should_trigger:
                            await self._trigger_stop_loss(position_risk)
                
                await asyncio.sleep(1)  # Check every second for stop-loss
                
            except Exception as e:
                logger.error(f"❌ Error in stop-loss monitoring: {e}")
                await asyncio.sleep(2)
    
    async def _calculate_portfolio_risk(self):
        """Calculate comprehensive portfolio risk metrics."""
        try:
            # Get current portfolio data (would be injected from trading client)
            total_balance = sum(pos.margin_used for pos in self.position_risks.values())
            unrealized_pnl = sum(pos.unrealized_pnl for pos in self.position_risks.values())
            
            # Calculate daily PnL
            current_balance = total_balance + unrealized_pnl
            daily_pnl = current_balance - self.daily_start_balance if self.daily_start_balance > 0 else 0
            
            # Calculate max drawdown
            if len(self.pnl_history) > 0:
                peak = max(self.pnl_history)
                current_dd = (peak - current_balance) / peak * 100 if peak > 0 else 0
                max_drawdown = max(current_dd, getattr(self.portfolio_risk, 'max_drawdown', 0))
            else:
                max_drawdown = 0
            
            # Calculate VaR and Expected Shortfall
            var_95, expected_shortfall = self._calculate_var_metrics()
            
            # Calculate risk score (0-100)
            risk_score = self._calculate_portfolio_risk_score(
                daily_pnl, max_drawdown, unrealized_pnl, total_balance
            )
            
            self.portfolio_risk = PortfolioRisk(
                total_balance=total_balance,
                available_balance=total_balance - sum(pos.margin_used for pos in self.position_risks.values()),
                margin_used=sum(pos.margin_used for pos in self.position_risks.values()),
                unrealized_pnl=unrealized_pnl,
                daily_pnl=daily_pnl,
                max_drawdown=max_drawdown,
                margin_ratio=0.0,  # Would be calculated from exchange data
                risk_score=risk_score,
                var_95=var_95,
                expected_shortfall=expected_shortfall,
                correlation_risk=0.0,  # Would be calculated from correlation matrix
                volatility_score=0.0   # Would be calculated from price volatility
            )
            
            # Update PnL history
            self.pnl_history.append(current_balance)
            if len(self.pnl_history) > 1000:  # Keep last 1000 data points
                self.pnl_history = self.pnl_history[-1000:]
                
        except Exception as e:
            logger.error(f"❌ Error calculating portfolio risk: {e}")
    
    def _calculate_var_metrics(self) -> tuple[float, float]:
        """Calculate Value at Risk and Expected Shortfall."""
        if len(self.pnl_history) < 30:
            return 0.0, 0.0
        
        returns = np.diff(self.pnl_history) / np.array(self.pnl_history[:-1])
        returns = returns[~np.isnan(returns)]  # Remove NaN values
        
        if len(returns) == 0:
            return 0.0, 0.0
        
        # 95% VaR
        var_95 = np.percentile(returns, 5)
        
        # Expected Shortfall (average of returns below VaR)
        tail_returns = returns[returns <= var_95]
        expected_shortfall = np.mean(tail_returns) if len(tail_returns) > 0 else var_95
        
        return float(var_95), float(expected_shortfall)
    
    def _calculate_portfolio_risk_score(self, daily_pnl: float, max_drawdown: float, 
                                      unrealized_pnl: float, total_balance: float) -> float:
        """Calculate overall portfolio risk score (0-100)."""
        risk_score = 0.0
        
        # Daily loss component (0-40 points)
        if daily_pnl < 0:
            daily_loss_pct = abs(daily_pnl) / total_balance * 100 if total_balance > 0 else 0
            risk_score += min(daily_loss_pct * 8, 40)  # Max 40 points
        
        # Drawdown component (0-30 points)
        risk_score += min(max_drawdown * 6, 30)  # Max 30 points
        
        # Unrealized loss component (0-20 points)
        if unrealized_pnl < 0:
            unrealized_loss_pct = abs(unrealized_pnl) / total_balance * 100 if total_balance > 0 else 0
            risk_score += min(unrealized_loss_pct * 4, 20)  # Max 20 points
        
        # Position concentration component (0-10 points)
        if len(self.position_risks) > 0:
            max_position_pct = max(pos.margin_used / total_balance * 100 
                                 for pos in self.position_risks.values()) if total_balance > 0 else 0
            risk_score += min(max_position_pct / 10, 10)  # Max 10 points
        
        return min(risk_score, 100.0)
    
    async def calculate_optimal_position_size(self, symbol: str, side: str, 
                                            entry_price: float, stop_loss_price: float,
                                            account_balance: float) -> float:
        """Calculate optimal position size based on selected method."""
        try:
            if self.position_sizing_method == PositionSizingMethod.FIXED:
                return self._calculate_fixed_position_size(account_balance)
            
            elif self.position_sizing_method == PositionSizingMethod.KELLY:
                return await self._calculate_kelly_position_size(symbol, side, entry_price, stop_loss_price, account_balance)
            
            elif self.position_sizing_method == PositionSizingMethod.VOLATILITY:
                return await self._calculate_volatility_position_size(symbol, entry_price, account_balance)
            
            elif self.position_sizing_method == PositionSizingMethod.RISK_PARITY:
                return await self._calculate_risk_parity_position_size(symbol, entry_price, account_balance)
            
            else:
                return self._calculate_fixed_position_size(account_balance)
                
        except Exception as e:
            logger.error(f"❌ Error calculating position size: {e}")
            return self._calculate_fixed_position_size(account_balance)
    
    def _calculate_fixed_position_size(self, account_balance: float) -> float:
        """Calculate fixed position size based on risk per trade."""
        risk_amount = account_balance * (self.risk_per_trade_pct / 100)
        return min(risk_amount, self.max_position_size)
    
    async def _calculate_kelly_position_size(self, symbol: str, side: str, 
                                           entry_price: float, stop_loss_price: float,
                                           account_balance: float) -> float:
        """Calculate Kelly criterion position size."""
        # This would require historical win rate and average win/loss data
        # For now, return conservative fixed size
        return self._calculate_fixed_position_size(account_balance) * 0.5
    
    async def _calculate_volatility_position_size(self, symbol: str, entry_price: float,
                                                account_balance: float) -> float:
        """Calculate position size based on volatility."""
        # This would use historical volatility to adjust position size
        # For now, return fixed size adjusted by a volatility factor
        volatility_factor = 1.0  # Would be calculated from price history
        base_size = self._calculate_fixed_position_size(account_balance)
        return base_size / max(volatility_factor, 0.5)  # Reduce size for high volatility
    
    async def _calculate_risk_parity_position_size(self, symbol: str, entry_price: float,
                                                 account_balance: float) -> float:
        """Calculate risk parity position size."""
        # This would balance risk across all positions
        # For now, return fixed size
        return self._calculate_fixed_position_size(account_balance)
    
    async def _trigger_stop_loss(self, position_risk: PositionRisk):
        """Trigger automated stop-loss for a position."""
        try:
            alert = RiskAlert(
                alert_type=AlertType.MARGIN_CALL,
                risk_level=RiskLevel.CRITICAL,
                message=f"Stop-loss triggered for {position_risk.symbol} at ${position_risk.current_price:.6f}",
                timestamp=datetime.now(),
                data={
                    'symbol': position_risk.symbol,
                    'side': position_risk.side,
                    'current_price': position_risk.current_price,
                    'stop_loss_price': position_risk.stop_loss_price,
                    'unrealized_pnl': position_risk.unrealized_pnl
                },
                action_required=True
            )
            
            await self._send_alert(alert)
            logger.critical(f"🚨 STOP-LOSS TRIGGERED: {position_risk.symbol} - Loss: ${position_risk.unrealized_pnl:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Error triggering stop-loss: {e}")
    
    async def _send_alert(self, alert: RiskAlert):
        """Send risk alert to all registered callbacks."""
        self.alerts_history.append(alert)
        
        # Keep only last 1000 alerts
        if len(self.alerts_history) > 1000:
            self.alerts_history = self.alerts_history[-1000:]
        
        # Send to callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                logger.error(f"❌ Error in alert callback: {e}")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        return {
            'portfolio_risk': self.portfolio_risk.__dict__ if self.portfolio_risk else None,
            'position_risks': {symbol: pos.__dict__ for symbol, pos in self.position_risks.items()},
            'recent_alerts': [alert.__dict__ for alert in self.alerts_history[-10:]],
            'risk_limits': {
                'max_position_size': self.max_position_size,
                'max_leverage': self.max_leverage,
                'max_daily_loss': self.max_daily_loss,
                'max_drawdown_pct': self.max_drawdown_pct
            },
            'monitoring_active': self.monitoring_active
        }
