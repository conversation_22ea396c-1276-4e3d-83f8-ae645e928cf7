/**
 * Money Circle Exchange Management
 * Enhanced exchange account management with HTX-specific features
 */

class ExchangeManager {
    constructor() {
        this.currentExchangeId = null;
        this.supportedExchanges = {
            'HTX': {
                name: 'HTX (Huobi)',
                icon: '🏦',
                features: ['Futures Trading', 'DOGE/USDT', 'High Leverage', 'Real-time Data'],
                requiresPassphrase: false,
                testnetAvailable: false,
                description: 'Professional futures trading platform with advanced order types'
            },
            'Binance': {
                name: 'Binance',
                icon: '🟡',
                features: ['Spot Trading', 'Futures', 'Options', 'Testnet Available'],
                requiresPassphrase: false,
                testnetAvailable: true,
                description: 'World\'s largest cryptocurrency exchange'
            },
            'Bybit': {
                name: 'Bybit',
                icon: '🟠',
                features: ['Derivatives', 'Perpetual Contracts', 'Copy Trading', 'Testnet Available'],
                requiresPassphrase: false,
                testnetAvailable: true,
                description: 'Advanced derivatives trading platform'
            }
        };

        this.init();
    }

    init() {
        console.log('🔗 Initializing Exchange Manager');
        this.bindEvents();
        this.loadUserExchanges();
    }

    bindEvents() {
        // Exchange type selection
        const exchangeSelect = document.getElementById('exchange-type');
        if (exchangeSelect) {
            exchangeSelect.addEventListener('change', (e) => this.onExchangeTypeChange(e.target.value));
        }

        // Environment selection
        const envRadios = document.querySelectorAll('input[name="environment"]');
        envRadios.forEach(radio => {
            radio.addEventListener('change', (e) => this.onEnvironmentChange(e.target.value));
        });

        // Test connection button
        const testBtn = document.getElementById('test-connection-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => this.testConnection());
        }

        // Form submission
        const addForm = document.getElementById('add-exchange-form');
        if (addForm) {
            addForm.addEventListener('submit', (e) => this.handleAddExchange(e));
        }

        // Management actions
        this.bindManagementActions();
    }

    bindManagementActions() {
        const refreshBtn = document.getElementById('refresh-balance-btn');
        const testExchangeBtn = document.getElementById('test-exchange-btn');
        const removeBtn = document.getElementById('remove-exchange-btn');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshBalance());
        }

        if (testExchangeBtn) {
            testExchangeBtn.addEventListener('click', () => this.testExistingConnection());
        }

        if (removeBtn) {
            removeBtn.addEventListener('click', () => this.removeExchange());
        }
    }

    onExchangeTypeChange(exchangeType) {
        console.log(`📊 Exchange type changed: ${exchangeType}`);

        const infoPanel = document.getElementById('exchange-info');
        const passphraseGroup = document.getElementById('passphrase-group');
        const envTestnet = document.getElementById('env-testnet');
        const envMainnet = document.getElementById('env-mainnet');

        if (!exchangeType) {
            infoPanel.style.display = 'none';
            return;
        }

        const exchange = this.supportedExchanges[exchangeType];
        if (!exchange) return;

        // Show exchange info
        this.updateExchangeInfo(exchange);
        infoPanel.style.display = 'block';

        // Handle passphrase requirement
        if (exchange.requiresPassphrase) {
            passphraseGroup.style.display = 'block';
        } else {
            passphraseGroup.style.display = 'none';
        }

        // Handle testnet availability
        if (exchange.testnetAvailable) {
            envTestnet.disabled = false;
            envTestnet.checked = true; // Default to testnet for safety
            envMainnet.disabled = false;
        } else {
            envTestnet.disabled = true;
            envMainnet.checked = true; // Force mainnet if testnet not available
            envTestnet.disabled = false;
        }

        this.onEnvironmentChange(exchange.testnetAvailable ? 'testnet' : 'mainnet');
        this.validateForm();
    }

    updateExchangeInfo(exchange) {
        const icon = document.getElementById('exchange-icon');
        const name = document.getElementById('exchange-name');
        const features = document.getElementById('exchange-features');

        if (icon) icon.textContent = exchange.icon;
        if (name) name.textContent = exchange.name;

        if (features) {
            features.innerHTML = exchange.features.map(feature =>
                `<span class="feature-tag">${feature}</span>`
            ).join('');
        }
    }

    onEnvironmentChange(environment) {
        console.log(`🌐 Environment changed: ${environment}`);

        const notice = document.getElementById('environment-notice');
        if (!notice) return;

        notice.className = `environment-notice ${environment}`;

        if (environment === 'testnet') {
            notice.innerHTML = `
                <strong>🧪 Testnet Mode</strong><br>
                Safe testing environment with virtual funds. Perfect for testing your API credentials and trading strategies without risk.
            `;
        } else {
            notice.innerHTML = `
                <strong>💰 Mainnet Mode</strong><br>
                <strong>⚠️ WARNING:</strong> This is live trading with real funds. Ensure your API keys have appropriate permissions and consider starting with small amounts.
            `;
        }

        this.validateForm();
    }

    validateForm() {
        const exchangeType = document.getElementById('exchange-type')?.value;
        const apiKey = document.getElementById('api-key')?.value;
        const apiSecret = document.getElementById('api-secret')?.value;
        const submitBtn = document.getElementById('add-exchange-submit');

        const isValid = exchangeType && apiKey && apiSecret;

        if (submitBtn) {
            submitBtn.disabled = !isValid;
        }
    }

    async testConnection() {
        console.log('🔍 Testing exchange connection...');

        const testBtn = document.getElementById('test-connection-btn');
        const result = document.getElementById('connection-result');

        if (!testBtn || !result) return;

        // Get form data
        const formData = this.getFormData();
        if (!formData.exchangeType || !formData.apiKey || !formData.apiSecret) {
            this.showConnectionResult('error', 'Please fill in all required fields before testing.');
            return;
        }

        // Show loading state
        testBtn.disabled = true;
        testBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Testing...</span>';
        this.showConnectionResult('loading', 'Testing connection to exchange...');

        try {
            const response = await fetch('/api/exchanges/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                this.showConnectionResult('success', `✅ Connection successful! ${data.message || 'API credentials are valid.'}`);
                // Enable submit button after successful test
                const submitBtn = document.getElementById('add-exchange-submit');
                if (submitBtn) submitBtn.disabled = false;
            } else {
                this.showConnectionResult('error', `❌ Connection failed: ${data.error || 'Unknown error'}`);
            }

        } catch (error) {
            console.error('Connection test error:', error);
            this.showConnectionResult('error', `❌ Connection test failed: ${error.message}`);
        } finally {
            // Reset button
            testBtn.disabled = false;
            testBtn.innerHTML = '<span class="btn-icon">🔍</span><span class="btn-text">Test Connection</span>';
        }
    }

    showConnectionResult(type, message) {
        const result = document.getElementById('connection-result');
        if (!result) return;

        result.className = `connection-result ${type}`;
        result.textContent = message;
    }

    getFormData() {
        return {
            exchangeType: document.getElementById('exchange-type')?.value,
            apiKey: document.getElementById('api-key')?.value,
            apiSecret: document.getElementById('api-secret')?.value,
            passphrase: document.getElementById('api-passphrase')?.value,
            environment: document.querySelector('input[name="environment"]:checked')?.value
        };
    }

    async handleAddExchange(event) {
        event.preventDefault();
        console.log('➕ Adding exchange account...');

        const formData = this.getFormData();
        const submitBtn = document.getElementById('add-exchange-submit');

        if (!submitBtn) return;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Adding...</span>';

        try {
            const response = await fetch('/api/exchanges/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', `✅ ${formData.exchangeType} account added successfully!`);
                this.closeModal('add-exchange-modal');
                this.resetForm();
                this.loadUserExchanges(); // Refresh the exchange list
            } else {
                this.showNotification('error', `❌ Failed to add exchange: ${data.error}`);
            }

        } catch (error) {
            console.error('Add exchange error:', error);
            this.showNotification('error', `❌ Error adding exchange: ${error.message}`);
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<span class="btn-icon">➕</span><span class="btn-text">Add Exchange</span>';
        }
    }

    async loadUserExchanges() {
        console.log('📊 Loading user exchanges...');

        try {
            const response = await fetch('/api/exchanges/list');
            const data = await response.json();

            if (data.success) {
                this.updateExchangeGrid(data.exchanges);
            } else {
                console.error('Failed to load exchanges:', data.error);
            }

        } catch (error) {
            console.error('Error loading exchanges:', error);
        }
    }

    updateExchangeGrid(exchanges) {
        const grid = document.querySelector('.exchange-grid');
        if (!grid) return;

        // Keep the "Add Exchange" card and update the rest
        const addCard = grid.querySelector('.add-exchange');
        grid.innerHTML = '';

        exchanges.forEach(exchange => {
            const card = this.createExchangeCard(exchange);
            grid.appendChild(card);
        });

        // Re-add the "Add Exchange" card
        if (addCard) {
            grid.appendChild(addCard);
        }
    }

    createExchangeCard(exchange) {
        const card = document.createElement('div');
        card.className = `exchange-card ${exchange.connected ? 'connected' : 'disconnected'}`;

        const exchangeInfo = this.supportedExchanges[exchange.exchange_name] || {};

        card.innerHTML = `
            <h4>${exchangeInfo.icon || '🏦'} ${exchange.exchange_name}</h4>
            <div class="connection-status">
                ${exchange.connected ? '🟢 Connected' : '🔴 Disconnected'}
            </div>
            ${exchange.connected && exchange.balance ? `
                <div class="balance-summary">
                    <div class="balance-item">
                        <span>USDT:</span>
                        <span>$${(exchange.balance.USDT || 0).toFixed(2)}</span>
                    </div>
                </div>
            ` : ''}
            <div class="exchange-actions">
                <button onclick="exchangeManager.refreshExchange('${exchange.exchange_name}')">🔄 Refresh</button>
                <button onclick="exchangeManager.manageExchange(${exchange.id})">⚙️ Manage</button>
            </div>
        `;

        return card;
    }

    async refreshExchange(exchangeName) {
        console.log(`🔄 Refreshing ${exchangeName}...`);

        try {
            const response = await fetch(`/api/exchanges/refresh/${exchangeName}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', `✅ ${exchangeName} refreshed successfully!`);
                this.loadUserExchanges();
            } else {
                this.showNotification('error', `❌ Failed to refresh ${exchangeName}: ${data.error}`);
            }

        } catch (error) {
            console.error('Refresh error:', error);
            this.showNotification('error', `❌ Error refreshing ${exchangeName}: ${error.message}`);
        }
    }

    async manageExchange(exchangeId) {
        console.log(`⚙️ Managing exchange ID: ${exchangeId}`);
        this.currentExchangeId = exchangeId;

        try {
            const response = await fetch(`/api/exchanges/details/${exchangeId}`);
            const data = await response.json();

            if (data.success) {
                this.showManageModal(data.exchange);
            } else {
                this.showNotification('error', `❌ Failed to load exchange details: ${data.error}`);
            }

        } catch (error) {
            console.error('Manage exchange error:', error);
            this.showNotification('error', `❌ Error loading exchange details: ${error.message}`);
        }
    }

    showManageModal(exchange) {
        const modal = document.getElementById('manage-exchange-modal');
        const details = document.getElementById('exchange-details');
        const status = document.getElementById('exchange-status');

        if (!modal || !details || !status) return;

        const exchangeInfo = this.supportedExchanges[exchange.exchange_name] || {};

        // Update exchange details
        details.innerHTML = `
            <div class="exchange-info-panel">
                <div class="info-content">
                    <div class="exchange-logo">
                        <span>${exchangeInfo.icon || '🏦'}</span>
                        <span>${exchange.exchange_name}</span>
                    </div>
                    <div class="exchange-features">
                        <div class="feature-list">
                            ${exchangeInfo.features ? exchangeInfo.features.map(f =>
                                `<span class="feature-tag">${f}</span>`
                            ).join('') : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Update status information
        status.innerHTML = `
            <div class="status-item">
                <span class="status-label">Connection Status</span>
                <span class="status-value ${exchange.connected ? 'success' : 'error'}">
                    ${exchange.connected ? '🟢 Connected' : '🔴 Disconnected'}
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">Environment</span>
                <span class="status-value">${exchange.environment || 'Mainnet'}</span>
            </div>
            <div class="status-item">
                <span class="status-label">Added</span>
                <span class="status-value">${new Date(exchange.created_at).toLocaleDateString()}</span>
            </div>
            <div class="status-item">
                <span class="status-label">Last Updated</span>
                <span class="status-value">${exchange.last_sync ? new Date(exchange.last_sync).toLocaleString() : 'Never'}</span>
            </div>
            ${exchange.balance ? `
                <div class="status-item">
                    <span class="status-label">USDT Balance</span>
                    <span class="status-value success">$${(exchange.balance.USDT || 0).toFixed(2)}</span>
                </div>
            ` : ''}
        `;

        modal.style.display = 'block';
        modal.classList.add('active');
    }

    async refreshBalance() {
        if (!this.currentExchangeId) return;

        console.log(`💰 Refreshing balance for exchange ID: ${this.currentExchangeId}`);

        const btn = document.getElementById('refresh-balance-btn');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Refreshing...</span>';
        }

        try {
            // Get CSRF token if needed (but authenticated API requests should be exempt)
            const headers = {
                'Content-Type': 'application/json'
            };

            // Try to get CSRF token from global manager if available
            if (window.csrfManager && window.csrfManager.getCSRFToken()) {
                headers['X-CSRF-Token'] = window.csrfManager.getCSRFToken();
            }

            const response = await fetch(`/api/exchanges/balance/${this.currentExchangeId}`, {
                method: 'POST',
                headers: headers
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', '✅ Balance refreshed successfully!');
                // Reload the manage modal with updated data
                this.manageExchange(this.currentExchangeId);
            } else {
                this.showNotification('error', `❌ Failed to refresh balance: ${data.error}`);
            }

        } catch (error) {
            console.error('Balance refresh error:', error);
            this.showNotification('error', `❌ Error refreshing balance: ${error.message}`);
        } finally {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Refresh Balance</span>';
            }
        }
    }

    async testExistingConnection() {
        if (!this.currentExchangeId) return;

        console.log(`🔍 Testing connection for exchange ID: ${this.currentExchangeId}`);

        const btn = document.getElementById('test-exchange-btn');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Testing...</span>';
        }

        try {
            const response = await fetch(`/api/exchanges/test/${this.currentExchangeId}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', '✅ Connection test successful!');
            } else {
                this.showNotification('error', `❌ Connection test failed: ${data.error}`);
            }

        } catch (error) {
            console.error('Connection test error:', error);
            this.showNotification('error', `❌ Error testing connection: ${error.message}`);
        } finally {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<span class="btn-icon">🔍</span><span class="btn-text">Test Connection</span>';
            }
        }
    }

    async removeExchange() {
        if (!this.currentExchangeId) return;

        const confirmed = confirm('⚠️ Are you sure you want to remove this exchange account? This action cannot be undone.');
        if (!confirmed) return;

        console.log(`🗑️ Removing exchange ID: ${this.currentExchangeId}`);

        const btn = document.getElementById('remove-exchange-btn');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Removing...</span>';
        }

        try {
            const response = await fetch(`/api/exchanges/remove/${this.currentExchangeId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('success', '✅ Exchange account removed successfully!');
                this.closeModal('manage-exchange-modal');
                this.loadUserExchanges();
            } else {
                this.showNotification('error', `❌ Failed to remove exchange: ${data.error}`);
            }

        } catch (error) {
            console.error('Remove exchange error:', error);
            this.showNotification('error', `❌ Error removing exchange: ${error.message}`);
        } finally {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<span class="btn-icon">🗑️</span><span class="btn-text">Remove Exchange</span>';
            }
        }
    }

    resetForm() {
        const form = document.getElementById('add-exchange-form');
        if (form) {
            form.reset();
            document.getElementById('exchange-info').style.display = 'none';
            document.getElementById('connection-result').style.display = 'none';
            document.getElementById('add-exchange-submit').disabled = true;
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('active');
        }

        if (modalId === 'add-exchange-modal') {
            this.resetForm();
        }
    }

    showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
}

// Global functions for backward compatibility
function showAddExchangeModal() {
    const modal = document.getElementById('add-exchange-modal');
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('active');
    }
}

function closeModal(modalId) {
    if (window.exchangeManager) {
        window.exchangeManager.closeModal(modalId);
    }
}

function refreshExchange(exchangeName) {
    if (window.exchangeManager) {
        window.exchangeManager.refreshExchange(exchangeName);
    }
}

function removeExchange(exchangeId) {
    if (window.exchangeManager) {
        window.exchangeManager.manageExchange(exchangeId);
    }
}

// Initialize exchange manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Exchange Manager');
    window.exchangeManager = new ExchangeManager();
});
