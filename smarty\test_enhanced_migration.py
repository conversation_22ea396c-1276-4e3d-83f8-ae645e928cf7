#!/usr/bin/env python3
"""
Test script for Enhanced LLM Migration.

This script tests the complete replacement of the old LLM system
with the enhanced LLM consumer in the orchestrator.
"""

import asyncio
import logging
import yaml
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.utils.logging_utils import setup_logging


async def test_enhanced_orchestrator():
    """Test the orchestrator with enhanced LLM consumer."""
    print("🔄 Testing Enhanced LLM Migration in Orchestrator")
    print("=" * 60)

    # Setup logging
    logger = setup_logging(level=logging.INFO)

    # Load configuration
    try:
        with open("config.yaml", "r") as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

    # Enable dummy mode for testing
    config["llm"]["dummy_mode"] = True
    config["dummy_mode"] = True
    config["dummy_llm"] = True

    # Reduce intervals for faster testing
    config["llm"]["call_interval_s"] = 5
    config["trading"]["enabled"] = False  # Disable actual trading

    print("✅ Configuration loaded and modified for testing")

    try:
        # Import orchestrator
        from orchestrator import Orchestrator

        print("✅ Orchestrator imported successfully")

        # Create orchestrator instance
        orchestrator = Orchestrator(config)
        print("✅ Orchestrator instance created")

        # Check that enhanced LLM consumer is initialized
        if hasattr(orchestrator, 'llm_consumer'):
            consumer_type = type(orchestrator.llm_consumer).__name__
            print(f"✅ LLM Consumer type: {consumer_type}")

            if consumer_type == "EnhancedLLMConsumer":
                print("🎉 Enhanced LLM Consumer successfully integrated!")
            else:
                print(f"❌ Expected EnhancedLLMConsumer, got {consumer_type}")
                return False
        else:
            print("❌ No LLM consumer found in orchestrator")
            return False

        # Test LLM consumer methods
        if hasattr(orchestrator.llm_consumer, 'get_status'):
            print("✅ Enhanced LLM Consumer has get_status method")
        else:
            print("❌ Enhanced LLM Consumer missing get_status method")
            return False

        # Test configuration extraction
        llm_config = orchestrator.llm_consumer.llm_config
        print(f"✅ LLM Config extracted: dummy_mode={llm_config.get('dummy_mode')}")

        # Test LLM manager
        llm_manager = orchestrator.llm_consumer.llm_manager
        health = llm_manager.get_health_status()
        print(f"✅ LLM Manager health: {health['is_healthy']}")
        print(f"   Dummy mode: {health['dummy_mode']}")
        print(f"   Model loaded: {health['model_loaded']}")

        print("\n🚀 Starting orchestrator for integration test...")

        # Start orchestrator (this will test the async start method)
        start_task = asyncio.create_task(orchestrator.start())

        # Let it run for a few seconds
        await asyncio.sleep(5)

        # Check if LLM consumer is running
        if hasattr(orchestrator.llm_consumer, 'running'):
            if orchestrator.llm_consumer.running:
                print("✅ Enhanced LLM Consumer is running")
            else:
                print("❌ Enhanced LLM Consumer is not running")

        # Get status
        status = orchestrator.llm_consumer.get_status()
        print(f"✅ LLM Consumer Status:")
        print(f"   Running: {status['running']}")
        print(f"   Signals Processed: {status['signals_processed']}")
        print(f"   Decisions Made: {status['decisions_made']}")

        # Stop orchestrator
        print("\n🛑 Stopping orchestrator...")
        await orchestrator.stop()

        # Cancel the start task
        start_task.cancel()
        try:
            await start_task
        except asyncio.CancelledError:
            pass

        print("✅ Orchestrator stopped successfully")

        return True

    except Exception as e:
        print(f"❌ Error during orchestrator test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_direct_enhanced_consumer():
    """Test the enhanced consumer directly."""
    print("\n🧠 Testing Enhanced LLM Consumer Directly")
    print("=" * 50)

    try:
        from llm.enhanced_llm_consumer import EnhancedLLMConsumer
        from core.feature_store import FeatureStore

        # Mock message bus
        class MockBus:
            async def subscribe(self, channel, callback):
                print(f"   Subscribed to: {channel}")

            async def publish(self, channel, timestamp, data):
                print(f"   Published to: {channel}")

        # Create test config
        config = {
            "llm": {
                "model_path": "dummy_path",
                "prompt_path": "llm/prompts/trading_prompt_phi.yaml",
                "dummy_mode": True,
                "call_interval_s": 5
            }
        }

        # Initialize components
        bus = MockBus()
        feature_store = FeatureStore()

        # Create enhanced consumer
        consumer = EnhancedLLMConsumer(bus, feature_store, config)
        print("✅ Enhanced LLM Consumer created")

        # Test configuration extraction
        llm_config = consumer.llm_config
        print(f"✅ Config extracted: dummy_mode={llm_config['dummy_mode']}")

        # Test LLM manager
        health = consumer.llm_manager.get_health_status()
        print(f"✅ LLM Manager health: {health['is_healthy']}")

        # Start consumer
        await consumer.start()
        print("✅ Enhanced LLM Consumer started")

        # Get status
        status = consumer.get_status()
        print(f"✅ Status: running={status['running']}")

        # Stop consumer
        await consumer.stop()
        print("✅ Enhanced LLM Consumer stopped")

        return True

    except Exception as e:
        print(f"❌ Error testing enhanced consumer: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_configuration_compatibility():
    """Test configuration compatibility."""
    print("\n⚙️  Testing Configuration Compatibility")
    print("=" * 45)

    try:
        # Load config
        with open("config.yaml", "r") as f:
            config = yaml.safe_load(f)

        # Test new LLM config structure
        if "llm" in config:
            llm_config = config["llm"]
            print("✅ New LLM config structure found")
            print(f"   Model path: {llm_config.get('model_path', 'Not set')}")
            print(f"   Dummy mode: {llm_config.get('dummy_mode', 'Not set')}")
            print(f"   Adaptive throttle: {llm_config.get('adaptive_throttle', 'Not set')}")
        else:
            print("❌ New LLM config structure not found")
            return False

        # Test backward compatibility
        legacy_keys = ["model_path", "dummy_mode", "call_interval_s"]
        for key in legacy_keys:
            if key in config:
                print(f"✅ Legacy key '{key}' found for backward compatibility")
            else:
                print(f"⚠️  Legacy key '{key}' not found")

        return True

    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False


async def main():
    """Main test function."""
    print("🔄 ENHANCED LLM MIGRATION TEST SUITE")
    print("=" * 70)

    results = []

    # Test 1: Configuration compatibility
    print("\n1️⃣  CONFIGURATION COMPATIBILITY TEST")
    result1 = await test_configuration_compatibility()
    results.append(("Configuration Compatibility", result1))

    # Test 2: Direct enhanced consumer test
    print("\n2️⃣  DIRECT ENHANCED CONSUMER TEST")
    result2 = await test_direct_enhanced_consumer()
    results.append(("Direct Enhanced Consumer", result2))

    # Test 3: Full orchestrator integration test
    print("\n3️⃣  ORCHESTRATOR INTEGRATION TEST")
    result3 = await test_enhanced_orchestrator()
    results.append(("Orchestrator Integration", result3))

    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)

    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Enhanced LLM migration is successful!")
        print("\n🚀 NEXT STEPS:")
        print("   1. Set dummy_mode: false in config.yaml to enable real LLM")
        print("   2. Run testnet with: python run_testnet.py")
        print("   3. Monitor LLM performance in the dashboard")
        print("   4. Remove old LLM files when confident")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
        print("\n🔧 TROUBLESHOOTING:")
        print("   1. Check that all imports are working")
        print("   2. Verify configuration structure")
        print("   3. Ensure enhanced LLM files are in place")

    print("=" * 70)


if __name__ == "__main__":
    # Fix Windows event loop issue
    import platform
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
