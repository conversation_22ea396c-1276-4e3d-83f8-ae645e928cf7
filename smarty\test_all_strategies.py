#!/usr/bin/env python3
"""
Comprehensive Strategy Test Runner
Tests all available strategies to verify they're operational when run through the dashboard interface.
"""

import asyncio
import logging
import json
import subprocess
import sys
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StrategyTestRunner:
    """Comprehensive test runner for all strategies."""
    
    def __init__(self):
        self.strategies = {
            "Smart Model Integrated": {
                "test_script": "test_strategy_smart_model_integrated.py",
                "description": "Full orchestrator.py system with LLM integration",
                "priority": 1
            },
            "Smart Strategy Only": {
                "test_script": "test_strategy_smart_strategy_only.py", 
                "description": "Technical analysis only (run_smart_strategy_live.py)",
                "priority": 2
            },
            "Order Flow": {
                "test_script": "test_strategy_order_flow.py",
                "description": "Dataframe-based order flow analysis",
                "priority": 3
            },
            "RSI Strategy": {
                "test_script": "test_strategy_data_producer.py",
                "description": "RSI-based trading signals",
                "priority": 4
            },
            "Bollinger Bands": {
                "test_script": "test_strategy_data_producer.py",
                "description": "Bollinger Bands trading strategy",
                "priority": 5
            },
            "Multi-Signal": {
                "test_script": "test_strategy_data_producer.py",
                "description": "Multiple signal combination strategy",
                "priority": 6
            },
            "Ensemble Model": {
                "test_script": "test_strategy_data_producer.py",
                "description": "Ensemble model predictions",
                "priority": 7
            },
            "SMA Crossover": {
                "test_script": "test_strategy_data_producer.py",
                "description": "Simple Moving Average crossover",
                "priority": 8
            },
            "VWAP Strategy": {
                "test_script": "test_strategy_data_producer.py",
                "description": "Volume Weighted Average Price strategy",
                "priority": 9
            },
            "Scalper Strategy": {
                "test_script": "test_strategy_data_producer.py",
                "description": "High-frequency scalping strategy",
                "priority": 10
            }
        }
        
        self.test_results = {}
        self.overall_results = {
            "test_start_time": datetime.now().isoformat(),
            "strategies_tested": 0,
            "strategies_passed": 0,
            "strategies_failed": 0,
            "test_duration": 0,
            "detailed_results": {}
        }
    
    async def run_strategy_test(self, strategy_name: str, strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """Run test for a specific strategy."""
        logger.info(f"\n{'='*80}")
        logger.info(f"🎯 TESTING STRATEGY: {strategy_name}")
        logger.info(f"📝 Description: {strategy_config['description']}")
        logger.info(f"🔧 Test Script: {strategy_config['test_script']}")
        logger.info(f"{'='*80}")
        
        test_start = datetime.now()
        
        try:
            # Prepare command
            if strategy_config['test_script'] == 'test_strategy_data_producer.py':
                # Pass strategy name as argument for data producer tests
                cmd = [sys.executable, strategy_config['test_script'], strategy_name]
            else:
                cmd = [sys.executable, strategy_config['test_script']]
            
            # Run the test
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=Path.cwd()
            )
            
            stdout, stderr = await process.communicate()
            
            test_end = datetime.now()
            test_duration = (test_end - test_start).total_seconds()
            
            # Parse results
            success = process.returncode == 0
            
            result = {
                "strategy_name": strategy_name,
                "test_script": strategy_config['test_script'],
                "description": strategy_config['description'],
                "success": success,
                "return_code": process.returncode,
                "test_duration": test_duration,
                "test_start": test_start.isoformat(),
                "test_end": test_end.isoformat(),
                "stdout": stdout.decode('utf-8') if stdout else "",
                "stderr": stderr.decode('utf-8') if stderr else ""
            }
            
            # Try to load detailed results from JSON file if available
            result_file = f"test_results_{strategy_name.lower().replace(' ', '_')}.json"
            try:
                if Path(result_file).exists():
                    with open(result_file, 'r') as f:
                        detailed_results = json.load(f)
                        result["detailed_results"] = detailed_results
            except Exception as e:
                logger.warning(f"Could not load detailed results: {e}")
            
            # Log result
            status = "✅ PASSED" if success else "❌ FAILED"
            logger.info(f"\n📊 {strategy_name}: {status} (Duration: {test_duration:.1f}s)")
            
            if not success and stderr:
                logger.error(f"❌ Error output: {stderr.decode('utf-8')[:500]}...")
            
            return result
            
        except Exception as e:
            test_end = datetime.now()
            test_duration = (test_end - test_start).total_seconds()
            
            logger.error(f"❌ Exception testing {strategy_name}: {e}")
            
            return {
                "strategy_name": strategy_name,
                "test_script": strategy_config['test_script'],
                "description": strategy_config['description'],
                "success": False,
                "return_code": -1,
                "test_duration": test_duration,
                "test_start": test_start.isoformat(),
                "test_end": test_end.isoformat(),
                "stdout": "",
                "stderr": str(e),
                "exception": str(e)
            }
    
    async def run_all_tests(self, selected_strategies: List[str] = None) -> Dict[str, Any]:
        """Run tests for all strategies or selected strategies."""
        start_time = datetime.now()
        
        logger.info("🚀 EPINNOX STRATEGY TESTING SUITE")
        logger.info("=" * 80)
        logger.info(f"📅 Test Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Determine which strategies to test
        if selected_strategies:
            strategies_to_test = {k: v for k, v in self.strategies.items() if k in selected_strategies}
        else:
            strategies_to_test = self.strategies
        
        logger.info(f"🎯 Testing {len(strategies_to_test)} strategies")
        
        # Sort strategies by priority
        sorted_strategies = sorted(strategies_to_test.items(), key=lambda x: x[1]['priority'])
        
        # Run tests
        for strategy_name, strategy_config in sorted_strategies:
            result = await self.run_strategy_test(strategy_name, strategy_config)
            self.test_results[strategy_name] = result
            self.overall_results["detailed_results"][strategy_name] = result
            
            # Update counters
            self.overall_results["strategies_tested"] += 1
            if result["success"]:
                self.overall_results["strategies_passed"] += 1
            else:
                self.overall_results["strategies_failed"] += 1
        
        # Calculate overall results
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        self.overall_results.update({
            "test_end_time": end_time.isoformat(),
            "test_duration": total_duration,
            "success_rate": (self.overall_results["strategies_passed"] / 
                           self.overall_results["strategies_tested"] * 100) if self.overall_results["strategies_tested"] > 0 else 0
        })
        
        # Print summary
        self.print_summary()
        
        # Save results
        self.save_results()
        
        return self.overall_results
    
    def print_summary(self):
        """Print test summary."""
        logger.info("\n" + "="*80)
        logger.info("📊 STRATEGY TESTING SUMMARY")
        logger.info("="*80)
        
        logger.info(f"📈 Total Strategies Tested: {self.overall_results['strategies_tested']}")
        logger.info(f"✅ Strategies Passed: {self.overall_results['strategies_passed']}")
        logger.info(f"❌ Strategies Failed: {self.overall_results['strategies_failed']}")
        logger.info(f"📊 Success Rate: {self.overall_results['success_rate']:.1f}%")
        logger.info(f"⏱️ Total Test Duration: {self.overall_results['test_duration']:.1f} seconds")
        
        logger.info("\n📋 DETAILED RESULTS:")
        logger.info("-" * 80)
        
        for strategy_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            duration = result["test_duration"]
            logger.info(f"{strategy_name:25} | {status} | {duration:6.1f}s | {result['description']}")
        
        # Recommendations
        logger.info("\n🎯 RECOMMENDATIONS:")
        logger.info("-" * 40)
        
        failed_strategies = [name for name, result in self.test_results.items() if not result["success"]]
        
        if not failed_strategies:
            logger.info("🎉 All strategies are operational and ready for dashboard use!")
            logger.info("✅ You can safely start any strategy through the dashboard interface.")
        else:
            logger.info(f"⚠️ {len(failed_strategies)} strategies need attention:")
            for strategy in failed_strategies:
                logger.info(f"   - {strategy}: Check logs and fix issues before using in dashboard")
        
        logger.info("\n🔗 Next Steps:")
        logger.info("   1. Review detailed test results in JSON files")
        logger.info("   2. Fix any failed strategies")
        logger.info("   3. Test strategies individually through dashboard interface")
        logger.info("   4. Monitor strategy performance in live trading")
    
    def save_results(self):
        """Save comprehensive test results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"strategy_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.overall_results, f, indent=2)
        
        logger.info(f"\n💾 Comprehensive results saved to: {filename}")

async def main():
    """Main test execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Epinnox trading strategies")
    parser.add_argument("--strategies", nargs="+", help="Specific strategies to test")
    parser.add_argument("--list", action="store_true", help="List available strategies")
    
    args = parser.parse_args()
    
    runner = StrategyTestRunner()
    
    if args.list:
        print("\n🎯 AVAILABLE STRATEGIES:")
        print("=" * 50)
        for name, config in runner.strategies.items():
            print(f"{config['priority']:2d}. {name:25} - {config['description']}")
        return
    
    # Run tests
    results = await runner.run_all_tests(args.strategies)
    
    # Exit with appropriate code
    success = results["strategies_failed"] == 0
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
