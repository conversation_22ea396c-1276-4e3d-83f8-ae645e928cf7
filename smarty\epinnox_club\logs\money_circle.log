2025-05-31 12:58:59,599 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:11:58:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:01,188 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,199 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,205 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:03,206 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:05,194 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:07,193 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:09,188 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:11,186 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:13,204 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 12:59:15,198 - aiohttp.access - INFO - ::1 [31/May/2025:11:59:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:49,018 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:12:09:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:49,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:51,279 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:09:54,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:09:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:53,301 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:59,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:14:59,778 - aiohttp.access - INFO - ::1 [31/May/2025:12:14:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:01,255 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:05,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:05,206 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:07,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:11,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:11,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:13,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:17,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:17,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:19,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:23,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:23,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:25,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:29,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:29,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:31,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:35,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:35,219 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:37,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:41,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:41,220 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:43,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:47,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:47,221 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:49,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:53,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:53,219 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:55,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:59,220 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:15:59,236 - aiohttp.access - INFO - ::1 [31/May/2025:12:15:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:01,230 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:05,216 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:05,232 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:07,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:11,333 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:11,363 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:11 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:13,232 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:17,487 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:17,502 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:17 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:19,346 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:23,567 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:23,779 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:25,740 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:29,658 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:29,752 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:29 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:31,456 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:36,256 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:37,129 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:38,748 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:46,681 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:47,002 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:47,017 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:53,470 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:54,526 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:16:56,629 - aiohttp.access - INFO - ::1 [31/May/2025:12:16:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:00,328 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:03,830 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:05,865 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:06,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:09,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:12,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:14,641 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:15,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:18,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:21,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:24,182 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:24,599 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:27,186 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:30,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:33,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:33,879 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:36,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:39,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:41,733 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:42,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:45,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:48,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:48,808 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:51,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:54,212 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:57,213 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:17:57,727 - aiohttp.access - INFO - ::1 [31/May/2025:12:17:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:00,229 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:03,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:06,248 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:07,433 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:09,254 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:12,293 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:14,910 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:15,253 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:18,417 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:21,374 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:24,745 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:24,807 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:27,409 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:30,711 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:31,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:34,131 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:37,744 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:39,800 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:41,839 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:46,489 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:50,109 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:50,838 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:54,775 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:58,158 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:18:59,303 - aiohttp.access - INFO - ::1 [31/May/2025:12:18:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:01,738 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:03,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:06,910 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:07,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:07,473 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:13,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:15,043 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:15,529 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:19,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:23,356 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:25,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:25,806 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:30,668 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:31,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:32,524 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:37,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:38,572 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:42,991 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:43,210 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:45,677 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:49,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:50,475 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:55,209 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:55,522 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:19:59,052 - aiohttp.access - INFO - ::1 [31/May/2025:12:19:59 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:01,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,210 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:03,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:05,581 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:07,213 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:08,944 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:13,308 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:15,019 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:16,358 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:19,297 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:23,518 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:25,845 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:26,709 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:26 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:31,542 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:32,719 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:33,765 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:38,760 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:40,452 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:43,095 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:45,519 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:50,160 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:50,486 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:56,049 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:20:57,780 - aiohttp.access - INFO - ::1 [31/May/2025:12:20:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:01,172 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,180 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,196 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:04,240 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:06,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:06,224 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:10,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:10 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:12,181 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:13,255 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:16,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:18,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:20,455 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:20 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:22,183 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:22 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:24,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:27,396 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:28,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:30,200 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:34,204 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:36,202 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:36,951 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:36 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:40,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:42,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:42 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:44,768 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:44 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:46,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:48,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:48 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:52,144 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:52,207 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:54,218 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:58,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:21:58,898 - aiohttp.access - INFO - ::1 [31/May/2025:12:21:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:00,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,188 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,191 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:03,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:04,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:06,264 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:08,578 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:10,231 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:10 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:12,237 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:16,230 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:16 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:18,349 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:18,676 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:18 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:22,355 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:22 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:24,381 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:25,281 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:28,383 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:30,788 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:30 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:33,717 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:35,011 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:38,470 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:38 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:40,934 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:40 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:43,022 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:47,719 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:47,937 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:47 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:51,945 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:54,824 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:54 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:22:56,171 - aiohttp.access - INFO - ::1 [31/May/2025:12:22:56 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:00,036 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:00 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:03,212 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:04,066 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:04 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:05,730 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:07,889 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:09,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:12,424 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:14,982 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:15,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:20,892 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:20 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:21,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:24,371 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:24 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:27,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:28,070 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:28 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:32,745 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:32 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:33,211 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:34,642 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:34 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:39,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:41,369 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:41,615 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:45,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:50,495 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:50 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:51,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:52,189 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:57,217 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:23:57,575 - aiohttp.access - INFO - ::1 [31/May/2025:12:23:57 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:01,347 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,187 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:03,198 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:05,623 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:05 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:08,732 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:08 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:09,229 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:14,639 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:14 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:15,243 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:15,398 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:21,271 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:23,362 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:23 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:25,290 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:27,637 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,548 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,563 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:33,952 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:39,724 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:41,322 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:41 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:43,892 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:46,349 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:46 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:51,671 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:52,121 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:52 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:53,441 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:53 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:24:58,833 - aiohttp.access - INFO - ::1 [31/May/2025:12:24:58 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:01,331 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:01 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,201 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,203 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /api/portfolio HTTP/1.1" 401 205 "http://localhost:8084/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:03,902 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:03 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:06,988 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:06 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:07,190 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:07 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:09,184 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:09 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:12,938 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:12 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:13,192 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:13 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:15,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:15 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:19,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:19,701 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:19 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:21,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:21 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:25,185 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:25 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:27,177 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:27,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:27 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:31,193 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:31 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:33,195 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:33 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:35,378 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:35 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:37,194 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:37 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:39,199 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:39 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:43,206 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:43 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:45,197 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:45,677 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:45 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:49,208 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:49 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:51,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:51 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:55,205 - aiohttp.access - INFO - ::1 [31/May/2025:12:25:55 -0600] "GET /ws HTTP/1.1" 302 292 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:25:58,368 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:25:58,368 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:25:58,374 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,380 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,385 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,391 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,396 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,402 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,409 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,414 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,421 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,426 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,432 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,438 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,445 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,450 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,454 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,458 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,463 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,468 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,549 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:25:58,549 - market_data.market_data_api - INFO - 📡 Market Data API initialized
2025-05-31 13:25:58,549 - market_data.websocket_streamer - INFO - 🌐 WebSocket Streamer initialized
2025-05-31 13:25:58,549 - notifications.notification_manager - INFO - 🔔 Notification Manager initialized
2025-05-31 13:25:58,549 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:25:58,549 - strategies.live_strategy_engine - INFO - ⚡ Live Strategy Engine initialized
2025-05-31 13:25:58,549 - app - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:25:58,550 - app - INFO - [STARTUP] Money Circle initialized with config: default
2025-05-31 13:25:58,552 - market_data.market_data_api - INFO - ✅ Market Data API routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Market Data API routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:25:58,552 - app - INFO - [OK] Routes configured
2025-05-31 13:25:58,553 - app - INFO - [OK] Web application configured
2025-05-31 13:25:58,568 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:25:58,568 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:25:58,576 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,581 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,585 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,591 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,596 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,600 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,605 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,610 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,615 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,622 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,628 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,633 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,639 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,644 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,649 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,653 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,658 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,663 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:25:58,663 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:25:58,663 - market_data.market_data_api - INFO - 📡 Market Data API initialized
2025-05-31 13:25:58,663 - market_data.websocket_streamer - INFO - 🌐 WebSocket Streamer initialized
2025-05-31 13:25:58,663 - notifications.notification_manager - INFO - 🔔 Notification Manager initialized
2025-05-31 13:25:58,663 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:25:58,663 - strategies.live_strategy_engine - INFO - ⚡ Live Strategy Engine initialized
2025-05-31 13:25:58,663 - app - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:25:58,663 - app - INFO - [STARTUP] Money Circle initialized with config: default
2025-05-31 13:25:58,665 - market_data.market_data_api - INFO - ✅ Market Data API routes configured
2025-05-31 13:25:58,665 - app - INFO - [OK] Market Data API routes configured
2025-05-31 13:25:58,666 - app - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:25:58,666 - app - INFO - [OK] Routes configured
2025-05-31 13:25:58,667 - app - INFO - [OK] Web application configured
2025-05-31 13:46:33,471 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 13:46:33,472 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 13:46:33,477 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,482 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,487 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,492 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,496 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,501 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,505 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,510 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,516 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,521 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,528 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,532 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,537 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,542 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,547 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,552 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,558 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,562 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 13:46:33,645 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 13:46:33,646 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 13:46:33,646 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 13:46:33,646 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 13:46:33,646 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 13:46:33,646 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 13:46:33,647 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 13:46:33,647 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 13:46:33,650 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 13:46:33,650 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 13:46:33,651 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 13:46:33,651 - __main__ - INFO - [OK] Routes configured
2025-05-31 13:46:33,652 - __main__ - INFO - [OK] Web application configured
2025-05-31 13:46:33,659 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8080
2025-05-31 13:46:33,659 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 13:46:33,659 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 13:46:33,659 - __main__ - INFO - 🌐 Market data WebSocket streamer subscribed to updates
2025-05-31 13:46:33,660 - __main__ - INFO - 📡 Notification system started
2025-05-31 13:46:33,660 - __main__ - INFO - ⚡ Live trading monitoring started
2025-05-31 13:46:33,660 - __main__ - INFO - 🤖 Strategy automation started
2025-05-31 13:46:33,660 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 13:46:33,661 - notifications.notification_manager - INFO - 📡 Starting notification delivery system...
2025-05-31 13:46:33,661 - trading.live_trading_interface - INFO - 📊 Starting real-time trading monitoring...
2025-05-31 13:46:33,661 - strategies.live_strategy_engine - INFO - 🚀 Starting live strategy automation...
2025-05-31 13:46:33,661 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 13:46:33,682 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 13:46:33,683 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 13:46:33,691 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:46:33,692 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 13:46:33,692 - notifications.notification_manager - ERROR - ❌ Error cleaning up notifications: no such column: expires_at
2025-05-31 13:46:35,159 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 13:46:35,241 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 13:46:35,242 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 13:46:35,313 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 13:46:35,539 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 13:46:37,475 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET / HTTP/1.1" 302 132 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,489 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /login HTTP/1.1" 200 17109 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,526 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /static/js/common.js HTTP/1.1" 304 179 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:37,624 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:37 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 172 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:41,356 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 13:46:41,357 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 13:46:41,357 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 13:46:41,358 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:41 -0600] "POST /login HTTP/1.1" 302 408 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:46:41,363 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:46:41,402 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    return aiohttp_jinja2.render_template('personal_dashboard.html', request, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:46:41,406 - aiohttp.access - INFO - ::1 [31/May/2025:12:46:41 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:47:33,687 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:47:33,687 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:48:33,697 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:48:33,697 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:49:33,702 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:49:33,702 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:50:33,717 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:50:33,717 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:51:33,731 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:51:33,731 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 13:52:18,078 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:52:18,108 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    # Render using template
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:52:18,110 - aiohttp.access - INFO - ::1 [31/May/2025:12:52:18 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:52:18,634 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 13:52:18,657 - aiohttp.server - ERROR - Error handling request
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
    resp = await request_handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_app.py", line 567, in _handle
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\personal_dashboard.py", line 60, in serve_personal_dashboard
    # Render using template
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\personal_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 40, in top-level template code
    {% include 'components/header.html' %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\components\header.html", line 15, in top-level template code
    <a href="/dashboard" class="nav-link {% if request.path == '/dashboard' or request.path == '/personal' %}active{% endif %}">
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
jinja2.exceptions.UndefinedError: 'request' is undefined
2025-05-31 13:52:18,658 - aiohttp.access - INFO - ::1 [31/May/2025:12:52:18 -0600] "GET /dashboard HTTP/1.1" 500 336 "http://localhost:8080/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 13:52:33,734 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 13:52:33,734 - market_data.advanced_market_data_manager - ERROR - ❌ Error fetching CoinGecko data: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-05-31 15:56:20,069 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 15:56:20,069 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 15:56:20,074 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,079 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,083 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,088 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,093 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,097 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,101 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,108 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,113 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,119 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,123 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,128 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,133 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,137 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,141 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,146 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,150 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,155 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 15:56:20,311 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 15:56:20,312 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 15:56:20,312 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 15:56:20,312 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 15:56:20,312 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 15:56:20,312 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 15:56:20,312 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 15:56:20,312 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 15:56:20,315 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 15:56:20,315 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 15:56:20,315 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 15:56:20,316 - __main__ - INFO - [OK] Routes configured
2025-05-31 15:56:20,317 - __main__ - INFO - [OK] Web application configured
2025-05-31 15:56:20,321 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 15:56:20,321 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 15:56:20,321 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 15:56:20,321 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 15:56:20,321 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 15:56:20,322 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 15:56:20,322 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 15:56:20,322 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 15:56:20,323 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 15:56:20,323 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 15:56:20,323 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 15:56:20,323 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 15:56:20,350 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 15:56:20,351 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 15:56:20,366 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 15:56:22,262 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 15:56:22,266 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 15:56:22,333 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 15:56:22,554 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 15:56:22,798 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 15:57:20,370 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 15:58:20,382 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 15:58:26,623 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /club HTTP/1.1" 302 133 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,643 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,680 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/css/design_system.css HTTP/1.1" 304 180 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,683 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/css/unified_footer.css HTTP/1.1" 304 180 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,683 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/css/unified_header.css HTTP/1.1" 304 180 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,683 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/js/common.js HTTP/1.1" 304 180 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,684 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/css/auth.css HTTP/1.1" 304 179 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:26,721 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:26 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,862 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 15:58:29,863 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 15:58:29,863 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 15:58:29,864 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "POST /login HTTP/1.1" 302 409 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,871 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 15:58:29,912 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /dashboard HTTP/1.1" 200 30612 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,948 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /static/css/dashboard.css HTTP/1.1" 304 180 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,949 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /static/css/club.css HTTP/1.1" 304 180 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,950 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /static/js/personal_dashboard.js HTTP/1.1" 304 180 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,977 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 15:58:29,979 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:29,981 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 15:58:30,020 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /api/portfolio HTTP/1.1" 200 10354 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:48,426 - club.analytics - ERROR - Error generating club overview: no such column: is_active
2025-05-31 15:58:48,427 - club.strategy_governance - ERROR - Error getting pending strategies: no such column: sp.title
2025-05-31 15:58:48,427 - club.strategy_governance - ERROR - Error getting voting strategies: no such column: sp.title
2025-05-31 15:58:48,428 - club.strategy_governance - ERROR - Error getting approved strategies: no such column: sp.title
2025-05-31 15:58:48,442 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\club_dashboard.py", line 64, in serve_club_dashboard
    return aiohttp_jinja2.render_template('club_dashboard.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 172, in block 'content'
    <span class="performance {{ 'positive' if member.performance >= 0 else 'negative' }}">
    ^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'performance'
2025-05-31 15:58:48,453 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:48 -0600] "GET /club HTTP/1.1" 500 337 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:48,591 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 15:58:48,592 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:29 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:50,011 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 15:58:51,161 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace strategies: no such column: sp.title
2025-05-31 15:58:51,161 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting strategy categories: no such column: strategy_type
2025-05-31 15:58:51,161 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting featured strategies: no such column: sp.title
2025-05-31 15:58:51,162 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace stats: no such column: sp.is_active
2025-05-31 15:58:51,323 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:51 -0600] "GET /club/strategies HTTP/1.1" 200 16703 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:51,457 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:51 -0600] "GET /static/css/strategy_marketplace.css HTTP/1.1" 200 239 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:51,458 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:51 -0600] "GET /static/js/strategy_marketplace.js HTTP/1.1" 200 246 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:51,530 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 15:58:51,530 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:50 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:51,553 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:51 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:58:56,664 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\enhanced_member_directory.py", line 55, in serve_member_directory
    return aiohttp_jinja2.render_template('member_directory.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 110, in block 'content'
    <div class="performance {{ 'positive' if member.total_pnl >= 0 else 'negative' }}">
    ^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'total_pnl'
2025-05-31 15:58:56,668 - aiohttp.access - INFO - ::1 [31/May/2025:14:58:56 -0600] "GET /club/members HTTP/1.1" 500 337 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:59:12,740 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 15:59:12,785 - aiohttp.access - INFO - ::1 [31/May/2025:14:59:12 -0600] "GET /dashboard HTTP/1.1" 200 30619 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:59:12,891 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 15:59:12,893 - aiohttp.access - INFO - ::1 [31/May/2025:14:59:12 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:59:12,895 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 15:59:12,941 - aiohttp.access - INFO - ::1 [31/May/2025:14:59:12 -0600] "GET /api/portfolio HTTP/1.1" 200 10346 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 15:59:20,390 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 15:59:42,894 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 15:59:42,917 - aiohttp.access - INFO - ::1 [31/May/2025:14:59:42 -0600] "GET /api/portfolio HTTP/1.1" 200 10353 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:12,894 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:00:12,917 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:12 -0600] "GET /api/portfolio HTTP/1.1" 200 10349 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:14,570 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:14 -0600] "GET /login HTTP/1.1" 200 17086 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:14,789 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:14 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:20,397 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:00:22,073 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 16:00:22,073 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 16:00:22,074 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 16:00:22,074 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:21 -0600] "POST /login HTTP/1.1" 302 409 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:22,084 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,084 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,084 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,085 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,085 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,085 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,086 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,086 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,086 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,086 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,086 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,086 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,087 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,087 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,087 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,087 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,087 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,087 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,090 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:00:22,121 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:22 -0600] "GET /dashboard HTTP/1.1" 200 27540 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:22,210 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:00:22,211 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,211 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,211 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,212 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,212 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,212 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,212 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,212 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,213 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,213 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,213 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,213 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,213 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,214 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,214 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,214 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:00:22,214 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:00:22,214 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:00:22,217 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:22 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:22,218 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:00:22,251 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:22 -0600] "GET /api/portfolio HTTP/1.1" 200 5463 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:29,367 - club.analytics - ERROR - Error generating club overview: no such column: is_active
2025-05-31 16:00:29,367 - club.strategy_governance - ERROR - Error getting pending strategies: no such column: sp.title
2025-05-31 16:00:29,368 - club.strategy_governance - ERROR - Error getting voting strategies: no such column: sp.title
2025-05-31 16:00:29,369 - club.strategy_governance - ERROR - Error getting approved strategies: no such column: sp.title
2025-05-31 16:00:29,370 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\club_dashboard.py", line 64, in serve_club_dashboard
    return aiohttp_jinja2.render_template('club_dashboard.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 172, in block 'content'
    <span class="performance {{ 'positive' if member.performance >= 0 else 'negative' }}">
    ^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'performance'
2025-05-31 16:00:29,373 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:29 -0600] "GET /club HTTP/1.1" 500 337 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:29,468 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:00:29,469 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:22 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:30,532 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:00:31,284 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace strategies: no such column: sp.title
2025-05-31 16:00:31,285 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting strategy categories: no such column: strategy_type
2025-05-31 16:00:31,286 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting featured strategies: no such column: sp.title
2025-05-31 16:00:31,287 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace stats: no such column: sp.is_active
2025-05-31 16:00:31,288 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:31 -0600] "GET /club/strategies HTTP/1.1" 200 16692 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:31,349 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:00:31,349 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:30 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:31,395 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:31 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:33,105 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\enhanced_member_directory.py", line 55, in serve_member_directory
    return aiohttp_jinja2.render_template('member_directory.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 110, in block 'content'
    <div class="performance {{ 'positive' if member.total_pnl >= 0 else 'negative' }}">
    ^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'total_pnl'
2025-05-31 16:00:33,107 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:33 -0600] "GET /club/members HTTP/1.1" 500 337 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:35,111 - dashboards.enhanced_club_analytics - ERROR - Error getting club overview: no such column: tp.trade_size
2025-05-31 16:00:35,112 - dashboards.enhanced_club_analytics - ERROR - Error getting performance metrics: no such function: STDEV
2025-05-31 16:00:35,112 - dashboards.enhanced_club_analytics - ERROR - Error getting strategy analytics: no such column: sp.title
2025-05-31 16:00:35,113 - dashboards.enhanced_club_analytics - ERROR - Error getting risk metrics: no such column: max_drawdown
2025-05-31 16:00:35,128 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\enhanced_club_analytics.py", line 59, in serve_club_analytics
    return aiohttp_jinja2.render_template('club_analytics.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_analytics.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_analytics.html", line 37, in block 'content'
    <div class="card-value">${{ club_overview.total_portfolio_value|round(2) }}</div>
    ^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\filters.py", line 1182, in do_round
    return round(value, precision)
TypeError: type Undefined doesn't define __round__ method
2025-05-31 16:00:35,136 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:35 -0600] "GET /club/analytics HTTP/1.1" 500 337 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:40,958 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 16:00:40,959 - aiohttp.server - ERROR - Unhandled exception
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2002, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 446, in select
    self._poll(timeout)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 775, in _poll
    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 693, in websocket_handler
    async for msg in ws:
    ...<4 lines>...
            logger.error(f'WebSocket error: {ws.exception()}')
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 616, in __anext__
    msg = await self.receive()
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 541, in receive
    msg = await self._reader.read()
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "aiohttp\\_websocket\\reader_c.py", line 118, in read
  File "aiohttp\\_websocket\\reader_c.py", line 115, in aiohttp._websocket.reader_c.WebSocketDataQueue.read
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 602, in start
    resp, reset = await task
                  ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 539, in _handle_request
    self._handler_waiter.set_result(None)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
asyncio.exceptions.InvalidStateError: invalid state
2025-05-31 16:00:44,419 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 16:00:44,420 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 16:00:44,425 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,431 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,437 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,442 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,448 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,454 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,459 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,465 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,470 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,475 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,480 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,485 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,490 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,495 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,500 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,506 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,511 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,516 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:00:44,611 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 16:00:44,611 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 16:00:44,611 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 16:00:44,612 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 16:00:44,612 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 16:00:44,612 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 16:00:44,612 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 16:00:44,612 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 16:00:44,614 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 16:00:44,614 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 16:00:44,615 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 16:00:44,615 - __main__ - INFO - [OK] Routes configured
2025-05-31 16:00:44,616 - __main__ - INFO - [OK] Web application configured
2025-05-31 16:00:44,620 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 16:00:44,620 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 16:00:44,621 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 16:00:44,621 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 16:00:44,621 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 16:00:44,621 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 16:00:44,621 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 16:00:44,621 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 16:00:44,621 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 16:00:44,621 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 16:00:44,621 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 16:00:44,621 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 16:00:44,639 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 16:00:44,640 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 16:00:44,648 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 16:00:44,738 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:44 -0600] "GET /api/portfolio HTTP/1.1" 401 206 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:46,183 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:46 -0600] "GET /ws HTTP/1.1" 302 293 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:46,275 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 16:00:46,275 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 16:00:46,369 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 16:00:46,564 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 16:00:46,942 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 16:00:49,407 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:49 -0600] "GET /club/strategies HTTP/1.1" 302 133 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:49,423 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:49 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:49,574 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:49 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:52,185 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:52 -0600] "GET /ws HTTP/1.1" 302 293 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:52,633 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 16:00:52,634 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 16:00:52,634 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 16:00:52,634 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:52 -0600] "POST /login HTTP/1.1" 302 409 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:52,640 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:00:52,680 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:52 -0600] "GET /dashboard HTTP/1.1" 200 30624 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:52,757 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 16:00:52,759 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:00:52 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:52,759 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:00:52,787 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:52 -0600] "GET /api/portfolio HTTP/1.1" 200 10358 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:54,244 - club.analytics - ERROR - Error generating club overview: no such column: is_active
2025-05-31 16:00:54,246 - club.strategy_governance - ERROR - Error getting pending strategies: no such column: sp.title
2025-05-31 16:00:54,246 - club.strategy_governance - ERROR - Error getting voting strategies: no such column: sp.title
2025-05-31 16:00:54,247 - club.strategy_governance - ERROR - Error getting approved strategies: no such column: sp.title
2025-05-31 16:00:54,266 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\club_dashboard.py", line 64, in serve_club_dashboard
    return aiohttp_jinja2.render_template('club_dashboard.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 172, in block 'content'
    <span class="performance {{ 'positive' if member.performance >= 0 else 'negative' }}">
    ^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'performance'
2025-05-31 16:00:54,278 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:54 -0600] "GET /club HTTP/1.1" 500 337 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:54,346 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 16:00:54,347 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:52 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:00:55,551 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 16:00:58,188 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:01:13,191 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:01:13,212 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:01:13 -0600] "GET /api/portfolio HTTP/1.1" 200 10355 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:01:25,553 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:01:25,579 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:01:25 -0600] "GET /api/portfolio HTTP/1.1" 200 10357 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:01:30,239 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 16:01:30,240 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 16:01:30,240 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 16:01:30,240 - aiohttp.access - INFO - ::1 [31/May/2025:15:01:30 -0600] "POST /login HTTP/1.1" 302 238 "-" "curl/8.12.1"
2025-05-31 16:01:30,241 - aiohttp.access - INFO - ::1 [31/May/2025:15:01:30 -0600] "POST /dashboard HTTP/1.1" 405 218 "-" "curl/8.12.1"
2025-05-31 16:01:38,258 - aiohttp.access - INFO - ::1 [31/May/2025:15:01:38 -0600] "GET /dashboard HTTP/1.1" 302 133 "-" "curl/8.12.1"
2025-05-31 16:01:43,185 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:01:43,208 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:01:43 -0600] "GET /api/portfolio HTTP/1.1" 200 10346 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:01:44,661 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:01:45,367 - aiohttp.access - INFO - ::1 [31/May/2025:15:01:45 -0600] "HEAD / HTTP/1.1" 401 170 "-" "curl/8.12.1"
2025-05-31 16:01:55,519 - aiohttp.access - INFO - ::1 [31/May/2025:15:01:55 -0600] "GET /login HTTP/1.1" 200 17086 "-" "curl/8.12.1"
2025-05-31 16:01:55,554 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:01:55,581 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:01:55 -0600] "GET /api/portfolio HTTP/1.1" 200 10346 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:02:13,193 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:02:13,216 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:02:13 -0600] "GET /api/portfolio HTTP/1.1" 200 10352 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:02:25,554 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:02:25,580 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:02:25 -0600] "GET /api/portfolio HTTP/1.1" 200 10356 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:02:44,669 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:02:55,554 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:02:55,575 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:02:55 -0600] "GET /api/portfolio HTTP/1.1" 200 10359 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:03:03,197 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:03:03,227 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:03:03 -0600] "GET /api/portfolio HTTP/1.1" 200 10349 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:03:25,553 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:03:25,577 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:03:25 -0600] "GET /api/portfolio HTTP/1.1" 200 10354 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:03:44,681 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:03:55,554 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:03:55,578 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:03:55 -0600] "GET /api/portfolio HTTP/1.1" 200 10354 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:04:03,197 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:04:03,222 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:04:03 -0600] "GET /api/portfolio HTTP/1.1" 200 10344 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:04:25,557 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:04:25,583 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:04:25 -0600] "GET /api/portfolio HTTP/1.1" 200 10353 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:04:44,682 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:04:55,557 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:04:55,579 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:04:55 -0600] "GET /api/portfolio HTTP/1.1" 200 10344 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:05:03,200 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:05:03,235 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:05:03 -0600] "GET /api/portfolio HTTP/1.1" 200 10343 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:05:25,555 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:05:25,578 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:05:25 -0600] "GET /api/portfolio HTTP/1.1" 200 10350 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:05:44,683 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:05:55,552 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:05:55,574 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:05:55 -0600] "GET /api/portfolio HTTP/1.1" 200 10352 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:03,200 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:03,230 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:03 -0600] "GET /api/portfolio HTTP/1.1" 200 10346 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:07,723 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:07,746 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:07 -0600] "GET /dashboard HTTP/1.1" 200 30617 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:07,750 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:06:07,750 - aiohttp.access - INFO - ::1 [31/May/2025:15:00:55 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:07,828 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:06:07,830 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:07 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:07,831 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:07,855 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:07 -0600] "GET /api/portfolio HTTP/1.1" 200 10359 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,145 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:09,172 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:09 -0600] "GET /dashboard HTTP/1.1" 200 30611 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,175 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:06:09,175 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:07 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,251 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:06:09,253 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:09 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,254 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:09,278 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:09 -0600] "GET /api/portfolio HTTP/1.1" 200 10350 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,822 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:09,846 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:09 -0600] "GET /dashboard HTTP/1.1" 200 30616 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,849 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:06:09,850 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:09 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,911 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:06:09,914 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:09 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:09,915 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:09,939 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:09 -0600] "GET /api/portfolio HTTP/1.1" 200 10355 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:11,270 - club.analytics - ERROR - Error generating club overview: no such column: is_active
2025-05-31 16:06:11,271 - club.strategy_governance - ERROR - Error getting pending strategies: no such column: sp.title
2025-05-31 16:06:11,271 - club.strategy_governance - ERROR - Error getting voting strategies: no such column: sp.title
2025-05-31 16:06:11,272 - club.strategy_governance - ERROR - Error getting approved strategies: no such column: sp.title
2025-05-31 16:06:11,273 - aiohttp.server - ERROR - Error handling request from 127.0.0.1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\club_dashboard.py", line 64, in serve_club_dashboard
    return aiohttp_jinja2.render_template('club_dashboard.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_dashboard.html", line 172, in block 'content'
    <span class="performance {{ 'positive' if member.performance >= 0 else 'negative' }}">
    ^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'performance'
2025-05-31 16:06:11,276 - aiohttp.access - INFO - 127.0.0.1 [31/May/2025:15:06:11 -0600] "GET /club HTTP/1.1" 500 337 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:11,343 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:06:11,344 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:09 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:12,543 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:06:42,540 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:42,566 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:42 -0600] "GET /api/portfolio HTTP/1.1" 200 10354 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:06:44,689 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:06:47,072 - auth.user_manager - INFO - [AUTH] Successful login: epinnox from ::1
2025-05-31 16:06:47,072 - auth.user_manager - INFO - [AUTH] Session created for user: epinnox
2025-05-31 16:06:47,072 - __main__ - INFO - [OK] User logged in: epinnox
2025-05-31 16:06:47,073 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:46 -0600] "POST /login HTTP/1.1" 302 238 "-" "curl/8.12.1"
2025-05-31 16:06:54,794 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,794 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,794 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,795 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,795 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,795 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,795 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,795 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,795 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,796 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,796 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - Decryption error: 
2025-05-31 16:06:54,796 - exchanges.encryption_utils - ERROR - API credential decryption error: 
2025-05-31 16:06:54,797 - exchanges.account_manager - ERROR - Error creating exchange client: 
2025-05-31 16:06:54,799 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:06:54,822 - aiohttp.access - INFO - ::1 [31/May/2025:15:06:54 -0600] "GET /dashboard HTTP/1.1" 200 27550 "-" "curl/8.12.1"
2025-05-31 16:07:03,204 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:07:03,239 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:03 -0600] "GET /api/portfolio HTTP/1.1" 200 10356 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:03,465 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 16:07:03,465 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 16:07:03,465 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 16:07:03,466 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:03 -0600] "POST /login HTTP/1.1" 302 238 "-" "curl/8.12.1"
2025-05-31 16:07:09,756 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:07:09,757 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 16:07:09,757 - aiohttp.server - ERROR - Unhandled exception
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2002, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 446, in select
    self._poll(timeout)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 775, in _poll
    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 693, in websocket_handler
    async for msg in ws:
    ...<4 lines>...
            logger.error(f'WebSocket error: {ws.exception()}')
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 616, in __anext__
    msg = await self.receive()
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 541, in receive
    msg = await self._reader.read()
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "aiohttp\\_websocket\\reader_c.py", line 118, in read
  File "aiohttp\\_websocket\\reader_c.py", line 115, in aiohttp._websocket.reader_c.WebSocketDataQueue.read
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 602, in start
    resp, reset = await task
                  ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 539, in _handle_request
    self._handler_waiter.set_result(None)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
asyncio.exceptions.InvalidStateError: invalid state
2025-05-31 16:07:09,762 - aiohttp.server - ERROR - Unhandled exception
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2002, in _run_once
    event_list = self._selector.select(timeout)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 446, in select
    self._poll(timeout)
    ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\windows_events.py", line 775, in _poll
    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 693, in websocket_handler
    async for msg in ws:
    ...<4 lines>...
            logger.error(f'WebSocket error: {ws.exception()}')
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 616, in __anext__
    msg = await self.receive()
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_ws.py", line 541, in receive
    msg = await self._reader.read()
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "aiohttp\\_websocket\\reader_c.py", line 118, in read
  File "aiohttp\\_websocket\\reader_c.py", line 115, in aiohttp._websocket.reader_c.WebSocketDataQueue.read
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 602, in start
    resp, reset = await task
                  ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 539, in _handle_request
    self._handler_waiter.set_result(None)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
asyncio.exceptions.InvalidStateError: invalid state
2025-05-31 16:07:16,934 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 16:07:16,934 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 16:07:16,942 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,947 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,952 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,958 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,963 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,968 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,972 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,976 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,981 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,985 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,992 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:16,999 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,006 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,012 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,018 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,024 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,030 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,036 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:07:17,142 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 16:07:17,142 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 16:07:17,142 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 16:07:17,142 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 16:07:17,142 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 16:07:17,142 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 16:07:17,142 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 16:07:17,142 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 16:07:17,144 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 16:07:17,144 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 16:07:17,144 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 16:07:17,145 - __main__ - INFO - [OK] Routes configured
2025-05-31 16:07:17,146 - __main__ - INFO - [OK] Web application configured
2025-05-31 16:07:17,151 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 16:07:17,151 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 16:07:17,151 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 16:07:17,151 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 16:07:17,151 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 16:07:17,151 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 16:07:17,151 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 16:07:17,152 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 16:07:17,152 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 16:07:17,152 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 16:07:17,152 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 16:07:17,152 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 16:07:17,174 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 16:07:17,175 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 16:07:17,190 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 16:07:17,344 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:17 -0600] "GET /ws HTTP/1.1" 302 293 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:17,954 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:17 -0600] "GET /dashboard HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:17,969 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:17 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:18,087 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:18 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:18,875 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 16:07:18,876 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 16:07:18,968 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 16:07:19,160 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 16:07:19,165 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 16:07:21,845 - auth.user_manager - INFO - [AUTH] Successful login: crypto_sarah from ::1
2025-05-31 16:07:21,845 - auth.user_manager - INFO - [AUTH] Session created for user: crypto_sarah
2025-05-31 16:07:21,845 - __main__ - INFO - [OK] User logged in: crypto_sarah
2025-05-31 16:07:21,846 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:21 -0600] "POST /login HTTP/1.1" 302 409 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:21,857 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:07:21,937 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:21 -0600] "GET /dashboard HTTP/1.1" 200 30887 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:22,057 - __main__ - INFO - [WS] WebSocket connected (total: 1)
2025-05-31 16:07:22,060 - market_data.bus_integration - INFO - ✅ Connected to SQLite bus, found 2 tables
2025-05-31 16:07:22,060 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:22 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:22,110 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:22 -0600] "GET /api/portfolio HTTP/1.1" 200 10793 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:22,218 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:22 -0600] "HEAD /club/analytics HTTP/1.1" 401 170 "-" "curl/8.12.1"
2025-05-31 16:07:23,185 - __main__ - INFO - [WS] WebSocket connected (total: 2)
2025-05-31 16:07:23,925 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace strategies: no such column: sp.title
2025-05-31 16:07:23,926 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting strategy categories: no such column: strategy_type
2025-05-31 16:07:23,926 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting featured strategies: no such column: sp.title
2025-05-31 16:07:23,928 - dashboards.enhanced_strategy_marketplace - ERROR - Error getting marketplace stats: no such column: sp.is_active
2025-05-31 16:07:23,946 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:23 -0600] "GET /club/strategies HTTP/1.1" 200 16525 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:24,017 - __main__ - INFO - [WS] WebSocket disconnected (total: 1)
2025-05-31 16:07:24,018 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:22 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:24,030 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:24 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:25,421 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\enhanced_member_directory.py", line 55, in serve_member_directory
    return aiohttp_jinja2.render_template('member_directory.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\member_directory.html", line 110, in block 'content'
    <div class="performance {{ 'positive' if member.total_pnl >= 0 else 'negative' }}">
    ^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'total_pnl'
2025-05-31 16:07:25,435 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:25 -0600] "GET /club/members HTTP/1.1" 500 337 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:27,267 - dashboards.enhanced_club_analytics - ERROR - Error getting club overview: no such column: tp.trade_size
2025-05-31 16:07:27,268 - dashboards.enhanced_club_analytics - ERROR - Error getting performance metrics: no such function: STDEV
2025-05-31 16:07:27,269 - dashboards.enhanced_club_analytics - ERROR - Error getting strategy analytics: no such column: sp.title
2025-05-31 16:07:27,271 - dashboards.enhanced_club_analytics - ERROR - Error getting risk metrics: no such column: max_drawdown
2025-05-31 16:07:27,283 - aiohttp.server - ERROR - Error handling request from ::1
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_protocol.py", line 510, in _handle_request
    resp = await request_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_app.py", line 569, in _handle
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_session\__init__.py", line 191, in factory
    response = await handler(request)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\app.py", line 206, in auth_middleware
    return await handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\dashboards\enhanced_club_analytics.py", line 59, in serve_club_analytics
    return aiohttp_jinja2.render_template('club_analytics.html', request, context)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 176, in render_template
    response.text = render_string(template_name, request, context, app_key=app_key)
                    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\aiohttp_jinja2\__init__.py", line 139, in render_string
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_analytics.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\base.html", line 44, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\dev\smarty\epinnox_club\templates\club_analytics.html", line 37, in block 'content'
    <div class="card-value">${{ club_overview.total_portfolio_value|round(2) }}</div>
    ^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\jinja2\filters.py", line 1182, in do_round
    return round(value, precision)
TypeError: type Undefined doesn't define __round__ method
2025-05-31 16:07:27,290 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:27 -0600] "GET /club/analytics HTTP/1.1" 500 337 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:30,350 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:30 -0600] "GET /club/members HTTP/1.1" 302 133 "-" "curl/8.12.1"
2025-05-31 16:07:30,445 - auth.user_manager - INFO - [AUTH] Session destroyed for user: crypto_sarah
2025-05-31 16:07:30,446 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:30 -0600] "GET /logout HTTP/1.1" 302 218 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:30,449 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:30 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/club/strategies" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:30,552 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:30 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:31,690 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:31 -0600] "GET /dashboard HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:31,694 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:31 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:31,775 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:31 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,307 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /club HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,311 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,405 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,840 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /club/strategies HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,844 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:32,914 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:32 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,307 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /club/members HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,311 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,387 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,851 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /club/analytics HTTP/1.1" 302 133 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,855 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:33,929 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:33 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:35,314 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:35 -0600] "GET /logout HTTP/1.1" 302 218 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:35,317 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:35 -0600] "GET /login HTTP/1.1" 200 17086 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:07:35,393 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:35 -0600] "GET /static/images/favicon.ico HTTP/1.1" 404 173 "http://localhost:8085/login" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:08:03,199 - aiohttp.access - INFO - ::1 [31/May/2025:15:08:03 -0600] "GET /api/portfolio HTTP/1.1" 401 206 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:08:17,193 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:08:24,976 - aiohttp.access - INFO - ::1 [31/May/2025:15:08:24 -0600] "GET /api/portfolio HTTP/1.1" 401 206 "http://localhost:8085/dashboard" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:08:26,160 - __main__ - INFO - [WS] WebSocket disconnected (total: 0)
2025-05-31 16:08:26,160 - aiohttp.access - INFO - ::1 [31/May/2025:15:07:23 -0600] "GET /ws HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-05-31 16:09:17,204 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:09:53,541 - auth.user_manager - INFO - [AUTH] Successful login: trader_alex from ::1
2025-05-31 16:09:53,541 - auth.user_manager - INFO - [AUTH] Session created for user: trader_alex
2025-05-31 16:09:53,541 - __main__ - INFO - [OK] User logged in: trader_alex
2025-05-31 16:09:53,542 - aiohttp.access - INFO - ::1 [31/May/2025:15:09:53 -0600] "POST /login HTTP/1.1" 302 238 "-" "curl/8.12.1"
2025-05-31 16:09:53,543 - aiohttp.access - INFO - ::1 [31/May/2025:15:09:53 -0600] "POST /dashboard HTTP/1.1" 405 218 "-" "curl/8.12.1"
2025-05-31 16:10:03,125 - aiohttp.access - INFO - ::1 [31/May/2025:15:10:03 -0600] "GET /api/portfolio HTTP/1.1" 401 206 "-" "curl/8.12.1"
2025-05-31 16:10:11,834 - aiohttp.access - INFO - ::1 [31/May/2025:15:10:11 -0600] "GET /login HTTP/1.1" 200 17086 "-" "curl/8.12.1"
2025-05-31 16:10:17,209 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:11:17,211 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:12:17,225 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:12:21,743 - aiohttp.access - INFO - ::1 [31/May/2025:15:12:21 -0600] "GET /login HTTP/1.1" 200 17086 "-" "curl/8.12.1"
2025-05-31 16:12:29,173 - aiohttp.access - INFO - ::1 [31/May/2025:15:12:29 -0600] "GET /api/market/status HTTP/1.1" 401 206 "-" "curl/8.12.1"
2025-05-31 16:12:36,778 - aiohttp.access - INFO - ::1 [31/May/2025:15:12:36 -0600] "GET /club/members HTTP/1.1" 302 133 "-" "curl/8.12.1"
2025-05-31 16:12:44,961 - aiohttp.access - INFO - ::1 [31/May/2025:15:12:44 -0600] "HEAD /static/css/dashboard.css HTTP/1.1" 200 239 "-" "curl/8.12.1"
2025-05-31 16:12:52,744 - aiohttp.access - INFO - ::1 [31/May/2025:15:12:52 -0600] "HEAD /ws HTTP/1.1" 401 170 "-" "curl/8.12.1"
2025-05-31 16:13:17,237 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:14:17,241 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:14:38,715 - aiohttp.access - INFO - ::1 [31/May/2025:15:14:38 -0600] "GET /login HTTP/1.1" 200 17086 "-" "curl/8.12.1"
2025-05-31 16:14:38,782 - aiohttp.access - INFO - ::1 [31/May/2025:15:14:38 -0600] "GET /static/css/dashboard.css HTTP/1.1" 200 239 "-" "curl/8.12.1"
2025-05-31 16:14:38,822 - aiohttp.access - INFO - ::1 [31/May/2025:15:14:38 -0600] "GET /api/portfolio HTTP/1.1" 401 206 "-" "curl/8.12.1"
2025-05-31 16:15:01,905 - aiohttp.access - INFO - ::1 [31/May/2025:15:15:01 -0600] "GET /login HTTP/1.1" 200 17086 "-" "curl/8.12.1"
2025-05-31 16:15:10,424 - aiohttp.access - INFO - ::1 [31/May/2025:15:15:10 -0600] "HEAD /login HTTP/1.1" 200 156 "-" "curl/8.12.1"
2025-05-31 16:15:17,245 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:15:18,574 - aiohttp.access - INFO - ::1 [31/May/2025:15:15:18 -0600] "HEAD /static/css/dashboard.css HTTP/1.1" 200 239 "-" "curl/8.12.1"
2025-05-31 16:15:25,875 - aiohttp.access - INFO - ::1 [31/May/2025:15:15:25 -0600] "HEAD /api/portfolio HTTP/1.1" 401 170 "-" "curl/8.12.1"
2025-05-31 16:16:17,260 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:17:17,274 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:18:17,276 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:19:17,286 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:20:17,296 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:21:17,311 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:22:17,320 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:23:17,331 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:24:17,342 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:25:17,357 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:26:17,367 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:27:17,370 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:28:17,372 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:28:28,781 - database.models - INFO - [OK] Database migrations completed successfully
2025-05-31 16:28:28,781 - database.models - INFO - [OK] Database initialized successfully
2025-05-31 16:28:28,790 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,796 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,801 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,806 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,811 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,815 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,820 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,825 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,830 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,837 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,842 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,848 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,852 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,857 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,862 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,867 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,872 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,877 - database.club_models - INFO - [OK] Club database tables initialized successfully
2025-05-31 16:28:28,965 - market_data.advanced_market_data_manager - INFO - [DATA] Advanced Market Data Manager initialized
2025-05-31 16:28:28,965 - market_data.market_data_api - INFO - [API] Market Data API initialized
2025-05-31 16:28:28,965 - market_data.websocket_streamer - INFO - [WS] WebSocket Streamer initialized
2025-05-31 16:28:28,965 - notifications.notification_manager - INFO - [NOTIFY] Notification Manager initialized
2025-05-31 16:28:28,965 - trading.live_trading_interface - INFO - [TRADING] Live Trading Interface initialized
2025-05-31 16:28:28,965 - strategies.live_strategy_engine - INFO - [STRATEGY] Live Strategy Engine initialized
2025-05-31 16:28:28,965 - __main__ - INFO - [OK] Advanced trading components with real-time market data initialized
2025-05-31 16:28:28,965 - __main__ - INFO - [STARTUP] Money Circle initialized with config: development
2025-05-31 16:28:28,968 - market_data.market_data_api - INFO - [API] Market Data API routes configured
2025-05-31 16:28:28,968 - __main__ - INFO - [OK] Market Data API routes configured
2025-05-31 16:28:28,968 - __main__ - INFO - [OK] Market Data WebSocket routes configured
2025-05-31 16:28:28,968 - __main__ - INFO - [OK] Routes configured
2025-05-31 16:28:28,969 - __main__ - INFO - [OK] Web application configured
2025-05-31 16:28:28,974 - __main__ - INFO - [SERVER] Money Circle running at http://localhost:8085
2025-05-31 16:28:28,974 - __main__ - INFO - [READY] Platform ready for Money Circle investment club members
2025-05-31 16:28:28,974 - __main__ - INFO - [DATA] Advanced market data manager started
2025-05-31 16:28:28,975 - __main__ - INFO - [WS] Market data WebSocket streamer subscribed to updates
2025-05-31 16:28:28,975 - __main__ - INFO - [NOTIFY] Notification system started
2025-05-31 16:28:28,975 - __main__ - INFO - [TRADING] Live trading monitoring started
2025-05-31 16:28:28,975 - __main__ - INFO - [STRATEGY] Strategy automation started
2025-05-31 16:28:28,975 - market_data.advanced_market_data_manager - INFO - [DATA] Starting advanced market data manager...
2025-05-31 16:28:28,975 - notifications.notification_manager - INFO - [NOTIFY] Starting notification delivery system...
2025-05-31 16:28:28,975 - trading.live_trading_interface - INFO - [TRADING] Starting real-time trading monitoring...
2025-05-31 16:28:28,975 - strategies.live_strategy_engine - INFO - [STRATEGY] Starting live strategy automation...
2025-05-31 16:28:28,976 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to Binance WebSocket...
2025-05-31 16:28:28,994 - market_data.advanced_market_data_manager - INFO - [DATA] Connecting to HTX WebSocket...
2025-05-31 16:28:28,995 - market_data.advanced_market_data_manager - INFO - 🔄 Connecting to Bybit Spot WebSocket...
2025-05-31 16:28:29,003 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 0.0% (0 excellent, 0 good)
2025-05-31 16:28:30,643 - market_data.advanced_market_data_manager - INFO - ✅ Connected to Bybit Spot WebSocket
2025-05-31 16:28:30,644 - market_data.advanced_market_data_manager - INFO - 📡 Subscribed to Bybit tickers: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
2025-05-31 16:28:30,726 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to Binance WebSocket
2025-05-31 16:28:30,861 - market_data.advanced_market_data_manager - INFO - [DATA] Connected to HTX WebSocket
2025-05-31 16:28:30,934 - market_data.advanced_market_data_manager - INFO - ✅ Bybit subscription confirmed: subscribe
2025-05-31 16:29:29,005 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:30:29,019 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:31:29,024 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:32:29,035 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:33:29,036 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:34:29,051 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:35:29,060 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:36:29,062 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:37:29,069 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:38:29,077 - market_data.advanced_market_data_manager - INFO - 📊 Data quality score: 1.0% (3 excellent, 0 good)
2025-05-31 16:38:53,719 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-5' coro=<AdvancedMarketDataManager.start() running at C:\Users\<USER>\Documents\dev\smarty\epinnox_club\market_data\advanced_market_data_manager.py:128> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\Users\<USER>\miniconda3\Lib\asyncio\tasks.py:820]>
