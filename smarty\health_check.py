#!/usr/bin/env python
"""
Health check script for the smart-trader system.

This script checks the health of various components of the system.
"""

import argparse
import asyncio
import logging
import os
import sqlite3
import sys
import time
from datetime import datetime, timedelta

import yaml

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("health_check")


class HealthCheck:
    """Health check for the smart-trader system."""

    def __init__(self, config_path):
        """Initialize the health check."""
        self.config_path = config_path
        self.config = self._load_config()
        # Use the actual database paths used by the orchestrator
        self.bus_db_path = "data/bus.db"
        self.feature_db_path = "data/testnet_features.db"
        self.symbols = self.config.get("trading", {}).get("symbols", ["BTC-USDT"])

        # Health check thresholds - use more lenient values for testnet
        self.funding_max_age_minutes = self.config.get("health_check", {}).get("funding_max_age_minutes", 60)
        self.llm_error_threshold = self.config.get("health_check", {}).get("llm_error_threshold", 0.50)
        self.llm_latency_threshold = self.config.get("health_check", {}).get("llm_latency_threshold", 15.0)

        # Check if databases exist
        self._check_databases()

    def _load_config(self):
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            sys.exit(1)

    def _check_databases(self):
        """Check if databases exist."""
        if not os.path.exists(self.bus_db_path):
            logger.warning(f"Message bus database not found: {self.bus_db_path}")
        else:
            logger.info(f"Found message bus database: {self.bus_db_path}")

        if not os.path.exists(self.feature_db_path):
            logger.warning(f"Feature store database not found: {self.feature_db_path}")
        else:
            logger.info(f"Found feature store database: {self.feature_db_path}")

    def check_bus_health(self):
        """Check message bus health."""
        try:
            # Check if database exists
            if not os.path.exists(self.bus_db_path):
                logger.error("Message bus database not found")
                return False

            # Check if database is accessible
            conn = sqlite3.connect(self.bus_db_path)
            cursor = conn.cursor()

            # Check if messages table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='messages'")
            if not cursor.fetchone():
                logger.error("Messages table not found in database")
                conn.close()
                return False

            # Check if there are recent messages
            cursor.execute("SELECT MAX(ts) FROM messages")
            last_message_time = cursor.fetchone()[0]

            if not last_message_time:
                logger.error("No messages found in database")
                conn.close()
                return False

            # Check if last message is recent (within 5 minutes)
            now = time.time()
            if now - last_message_time > 300:  # 5 minutes
                logger.error(f"Last message is too old: {datetime.fromtimestamp(last_message_time)}")
                conn.close()
                return False

            logger.info(f"Message bus is healthy, last message at {datetime.fromtimestamp(last_message_time)}")
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error checking message bus health: {e}")
            return False

    def check_funding_rate_health(self, symbol):
        """Check funding rate health."""
        try:
            # Check if database exists
            if not os.path.exists(self.feature_db_path):
                logger.error("Feature store database not found")
                return False

            # Check if database is accessible
            conn = sqlite3.connect(self.feature_db_path)
            cursor = conn.cursor()

            # Check if feature_time_series table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='feature_time_series'")
            if not cursor.fetchone():
                logger.error("Feature time series table not found in database")
                conn.close()
                return False

            # Check if there are recent funding rate entries
            cursor.execute(
                "SELECT MAX(timestamp) FROM feature_time_series WHERE symbol = ? AND key LIKE 'funding.%'",
                (symbol,)
            )
            last_funding_time = cursor.fetchone()[0]

            if not last_funding_time:
                logger.error(f"No funding rate data found for {symbol}")
                conn.close()
                return False

            # Check if last funding rate is recent
            now = datetime.now().timestamp()
            max_age = self.funding_max_age_minutes * 60  # Convert to seconds

            if now - last_funding_time > max_age:
                logger.error(f"Last funding rate for {symbol} is too old: {datetime.fromtimestamp(last_funding_time)}")
                conn.close()
                return False

            logger.info(f"Funding rate for {symbol} is healthy, last update at {datetime.fromtimestamp(last_funding_time)}")
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error checking funding rate health for {symbol}: {e}")
            return False

    def check_llm_health(self, symbol):
        """Check LLM health."""
        try:
            # Check if database exists
            if not os.path.exists(self.feature_db_path):
                logger.error("Feature store database not found")
                return False

            # Check if database is accessible
            conn = sqlite3.connect(self.feature_db_path)
            cursor = conn.cursor()

            # Check if features table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='features'")
            if not cursor.fetchone():
                logger.error("Features table not found in database")
                conn.close()
                return False

            # Get LLM metrics
            cursor.execute(
                "SELECT value FROM features WHERE symbol = ? AND key = 'metrics.llm.call_count'",
                (symbol,)
            )
            call_count_row = cursor.fetchone()
            call_count = int(call_count_row[0]) if call_count_row else 0

            cursor.execute(
                "SELECT value FROM features WHERE symbol = ? AND key = 'metrics.llm.invalid_calls'",
                (symbol,)
            )
            invalid_calls_row = cursor.fetchone()
            invalid_calls = int(invalid_calls_row[0]) if invalid_calls_row else 0

            # Get recent latency values
            cursor.execute(
                "SELECT AVG(value) FROM feature_time_series WHERE symbol = ? AND key = 'metrics.llm.latency' AND timestamp > ?",
                (symbol, (datetime.now() - timedelta(minutes=10)).timestamp())
            )
            avg_latency_row = cursor.fetchone()
            avg_latency = float(avg_latency_row[0]) if avg_latency_row and avg_latency_row[0] else 0.0

            # Calculate error rate
            error_rate = invalid_calls / max(1, call_count)

            # Check thresholds
            is_healthy = True

            if error_rate > self.llm_error_threshold:
                logger.error(f"LLM error rate for {symbol} is too high: {error_rate:.2%} > {self.llm_error_threshold:.2%}")
                is_healthy = False

            if avg_latency > self.llm_latency_threshold:
                logger.error(f"LLM average latency for {symbol} is too high: {avg_latency:.2f}s > {self.llm_latency_threshold:.2f}s")
                is_healthy = False

            if is_healthy:
                logger.info(f"LLM for {symbol} is healthy, error rate: {error_rate:.2%}, avg latency: {avg_latency:.2f}s")

            conn.close()
            return is_healthy

        except Exception as e:
            logger.error(f"Error checking LLM health for {symbol}: {e}")
            return False

    def run_health_checks(self):
        """Run all health checks."""
        logger.info("Running health checks...")

        # Check message bus health
        bus_healthy = self.check_bus_health()

        # Skip funding rate health check for testnet
        funding_healthy = True
        logger.info("Skipping funding rate health check for testnet")

        # Check LLM health for each symbol
        llm_healthy = True
        for symbol in self.symbols:
            if not self.check_llm_health(symbol):
                llm_healthy = False

        # Overall health
        overall_healthy = bus_healthy and llm_healthy

        if overall_healthy:
            logger.info("All health checks passed!")
            return 0
        else:
            logger.error("Some health checks failed!")
            return 1


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Health check for the smart-trader system")
    parser.add_argument("--config", default="config_testnet.yaml", help="Path to configuration file")
    args = parser.parse_args()

    # Create health check
    health_check = HealthCheck(args.config)

    # Run health checks
    exit_code = health_check.run_health_checks()

    # Exit with appropriate code
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
