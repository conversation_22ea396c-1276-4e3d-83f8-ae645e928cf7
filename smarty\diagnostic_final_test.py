#!/usr/bin/env python3
"""
Final comprehensive test for Epinnox Investment Club dashboard fixes.
Verifies all components are working after troubleshooting.
"""

import requests
import time
import json

def test_dashboard_after_login():
    """Test dashboard components after authentication."""
    print("🔍 FINAL DASHBOARD COMPONENT TEST")
    print("=" * 50)
    print("⚠️  Please login to the dashboard first:")
    print("   1. Go to http://localhost:8082")
    print("   2. Login with: epinnox / securepass123")
    print("   3. Wait for dashboard to load")
    print("   4. Press Enter to continue testing...")
    input()
    
    # Create session to maintain authentication
    session = requests.Session()
    
    # Test all API endpoints
    endpoints = {
        '/api/orderbook': 'Order Book',
        '/api/recent-trades': 'Recent Trades',
        '/api/debug': 'Debug Information',
        '/api/ai-analysis': 'AI Analysis',
        '/api/market-sentiment': 'Market Sentiment',
        '/api/strategy/status': 'Strategy Status',
        '/api/market-data': 'Market Data'
    }
    
    print("\n📡 Testing API Endpoints...")
    results = {}
    
    for endpoint, name in endpoints.items():
        try:
            response = session.get(f'http://localhost:8082{endpoint}', timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                
                # Analyze response content
                if endpoint == '/api/orderbook':
                    bids = len(data.get('bids', []))
                    asks = len(data.get('asks', []))
                    results[name] = f"✅ SUCCESS: {bids} bids, {asks} asks"
                    
                elif endpoint == '/api/recent-trades':
                    trades = len(data) if isinstance(data, list) else 0
                    results[name] = f"✅ SUCCESS: {trades} recent trades"
                    
                elif endpoint == '/api/debug':
                    status = data.get('system_status', 'Unknown')
                    market_data = data.get('recent_market_data', 0)
                    results[name] = f"✅ SUCCESS: Status={status}, Data={market_data}"
                    
                elif endpoint == '/api/ai-analysis':
                    rsi = data.get('rsi', 'N/A')
                    trend = data.get('trend', 'N/A')
                    results[name] = f"✅ SUCCESS: RSI={rsi}, Trend={trend}"
                    
                elif endpoint == '/api/market-sentiment':
                    overall = data.get('overall', 'N/A')
                    results[name] = f"✅ SUCCESS: Sentiment={overall}"
                    
                elif endpoint == '/api/strategy/status':
                    current = data.get('current_strategy', 'None')
                    running = data.get('strategy_status', {}).get('strategy_running', False)
                    results[name] = f"✅ SUCCESS: Strategy={current}, Running={running}"
                    
                elif endpoint == '/api/market-data':
                    symbol = data.get('symbol', 'Unknown')
                    price = data.get('price', 'N/A')
                    results[name] = f"✅ SUCCESS: {symbol} @ {price}"
                    
                else:
                    results[name] = f"✅ SUCCESS: {len(str(data))} bytes"
                    
            elif response.status_code == 401:
                results[name] = "🔐 AUTHENTICATION REQUIRED"
            else:
                results[name] = f"❌ ERROR: HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ EXCEPTION: {str(e)[:50]}"
    
    # Display results
    print("\n📊 API ENDPOINT TEST RESULTS:")
    print("-" * 40)
    for name, result in results.items():
        print(f"{name:20}: {result}")
    
    # Count successes
    success_count = sum(1 for r in results.values() if '✅ SUCCESS' in r)
    auth_count = sum(1 for r in results.values() if '🔐 AUTHENTICATION' in r)
    error_count = len(results) - success_count - auth_count
    
    print(f"\n📈 SUMMARY:")
    print(f"   Successful: {success_count}/{len(results)}")
    print(f"   Auth Required: {auth_count}/{len(results)}")
    print(f"   Errors: {error_count}/{len(results)}")
    
    if success_count > 0:
        print(f"\n✅ DIAGNOSIS: Dashboard components are working!")
        print(f"   - API endpoints are responding with data")
        print(f"   - Frontend should now display live information")
        print(f"   - Loading issues should be resolved")
    else:
        print(f"\n❌ DIAGNOSIS: Issues still present")
        if auth_count > 0:
            print(f"   - Make sure you're logged in to the dashboard")
        if error_count > 0:
            print(f"   - {error_count} endpoints have errors")
    
    return success_count > 0

def test_frontend_components():
    """Test frontend component loading."""
    print(f"\n🖥️  FRONTEND COMPONENT VERIFICATION")
    print("=" * 40)
    print("Please check the dashboard interface for:")
    print("   ✅ Order Book: Should show bids/asks instead of 'Loading...'")
    print("   ✅ Recent Trades: Should show trade list instead of 'Loading...'")
    print("   ✅ Debug Panel: Should show system status instead of 'Loading...'")
    print("   ✅ AI Analysis: Should show metrics instead of 'Loading...'")
    print("   ✅ Market Sentiment: Should show sentiment data instead of 'Loading...'")
    print("   ✅ Strategy Status: Should show correct running/stopped status")
    
    print(f"\n🔄 The dashboard should auto-refresh every 5 seconds with new data")
    print(f"📡 WebSocket should show live updates in real-time")
    
    response = input(f"\nAre all components displaying data correctly? (y/n): ").lower().strip()
    return response == 'y'

def test_strategy_management():
    """Test strategy start/stop functionality."""
    print(f"\n⚙️  STRATEGY MANAGEMENT TEST")
    print("=" * 30)
    print("Please test strategy management:")
    print("   1. Select a strategy from the dropdown")
    print("   2. Click 'Start Strategy' button")
    print("   3. Verify status changes to 'running' with PID")
    print("   4. Check console logs for strategy output")
    print("   5. Click 'Stop Strategy' button")
    print("   6. Verify status changes to 'stopped'")
    
    response = input(f"\nDoes strategy start/stop work correctly? (y/n): ").lower().strip()
    return response == 'y'

def main():
    """Run final comprehensive test."""
    print("🎯 EPINNOX DASHBOARD FINAL VERIFICATION")
    print("=" * 60)
    print("This test verifies all fixes are working correctly.")
    
    # Test 1: API endpoints
    api_working = test_dashboard_after_login()
    
    # Test 2: Frontend components
    frontend_working = test_frontend_components()
    
    # Test 3: Strategy management
    strategy_working = test_strategy_management()
    
    # Final assessment
    print(f"\n🏆 FINAL ASSESSMENT")
    print("=" * 30)
    print(f"API Endpoints: {'✅ WORKING' if api_working else '❌ ISSUES'}")
    print(f"Frontend Components: {'✅ WORKING' if frontend_working else '❌ ISSUES'}")
    print(f"Strategy Management: {'✅ WORKING' if strategy_working else '❌ ISSUES'}")
    
    if api_working and frontend_working and strategy_working:
        print(f"\n🎉 SUCCESS! All dashboard issues have been resolved:")
        print(f"   ✅ Order book displays live market depth")
        print(f"   ✅ Recent trades show real-time trade stream")
        print(f"   ✅ Debug panel shows system status")
        print(f"   ✅ AI analysis displays market metrics")
        print(f"   ✅ Strategy management works correctly")
        print(f"   ✅ All components load data instead of 'Loading...'")
        print(f"\n🚀 The Epinnox Investment Club dashboard is fully operational!")
    else:
        print(f"\n⚠️  Some issues remain - please check the failing components")
        if not api_working:
            print(f"   - API endpoints need authentication or have errors")
        if not frontend_working:
            print(f"   - Frontend components still showing 'Loading...'")
        if not strategy_working:
            print(f"   - Strategy start/stop buttons not working")

if __name__ == "__main__":
    main()
