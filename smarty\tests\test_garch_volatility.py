"""
Tests for the GARCH Volatility model.
"""

import asyncio
import unittest
import numpy as np
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.garch_volatility import GARCHVolatilityModel, VolatilityLevel


class TestGARCHVolatilityModel(unittest.TestCase):
    """Test the GARCH Volatility model."""

    def setUp(self):
        """Set up test environment."""
        self.model = GARCHVolatilityModel(
            lookback_periods=50,
            update_interval=60,  # 1 minute for testing
            long_term_window=100
        )

    def test_simple_volatility_estimation(self):
        """Test simple volatility estimation."""
        # Create test returns
        returns = [0.001, -0.002, 0.003, -0.001, 0.002] * 10

        # Estimate volatility
        volatility = self.model._estimate_simple_volatility(returns)

        # Check that volatility is positive
        self.assertGreater(volatility, 0)

    def test_volatility_level_classification(self):
        """Test volatility level classification."""
        # Test very low volatility
        self.assertEqual(self.model._classify_volatility_level(-2.0), VolatilityLevel.VERY_LOW)

        # Test low volatility
        self.assertEqual(self.model._classify_volatility_level(-1.0), VolatilityLevel.LOW)

        # Test normal volatility
        self.assertEqual(self.model._classify_volatility_level(0.0), VolatilityLevel.NORMAL)

        # Test high volatility
        self.assertEqual(self.model._classify_volatility_level(1.0), VolatilityLevel.HIGH)

        # Test very high volatility
        self.assertEqual(self.model._classify_volatility_level(2.0), VolatilityLevel.VERY_HIGH)

    def test_position_size_multiplier(self):
        """Test position size multiplier calculation."""
        # Test very high volatility
        self.assertEqual(self.model._calculate_position_size_multiplier(2.0), 0.5)

        # Test high volatility
        self.assertEqual(self.model._calculate_position_size_multiplier(1.0), 0.75)

        # Test normal volatility
        self.assertEqual(self.model._calculate_position_size_multiplier(0.0), 1.0)

        # Test low volatility
        self.assertEqual(self.model._calculate_position_size_multiplier(-1.0), 1.25)

        # Test very low volatility
        self.assertEqual(self.model._calculate_position_size_multiplier(-2.0), 1.5)

    def test_stop_width_multiplier(self):
        """Test stop width multiplier calculation."""
        # Test very high volatility
        self.assertEqual(self.model._calculate_stop_width_multiplier(2.0), 2.0)

        # Test high volatility
        self.assertEqual(self.model._calculate_stop_width_multiplier(1.0), 1.5)

        # Test normal volatility
        self.assertEqual(self.model._calculate_stop_width_multiplier(0.0), 1.0)

        # Test low volatility
        self.assertEqual(self.model._calculate_stop_width_multiplier(-1.0), 0.75)

        # Test very low volatility
        self.assertEqual(self.model._calculate_stop_width_multiplier(-2.0), 0.5)

    def test_predict_with_synthetic_data(self):
        """Test predict method with synthetic data."""
        async def test_predict_async():
            # Create synthetic price data with sine wave volatility
            np.random.seed(42)
            prices = []
            volatility_pattern = []

            # Generate 200 data points
            for i in range(200):
                # Volatility follows a sine wave (0.001 to 0.003)
                vol = 0.002 + 0.001 * np.sin(i / 20)
                volatility_pattern.append(vol)

                # Generate return with current volatility
                if i == 0:
                    prices.append(100.0)
                else:
                    ret = np.random.normal(0, vol)
                    prices.append(prices[-1] * np.exp(ret))

            # Feed data points one by one
            for i in range(len(prices)):
                features = {
                    "symbol": "BTC-USDT",
                    "close_prices": prices[:i+1],
                    "timestamp": datetime.now() + timedelta(minutes=i)
                }

                prediction = await self.model.predict(features)

                # Only check the last prediction (after we have enough data)
                if i == len(prices) - 1:
                    # Verify prediction structure
                    self.assertIn("volatility_forecast", prediction)
                    self.assertIn("volatility_z", prediction)
                    self.assertIn("volatility_level", prediction)
                    self.assertIn("position_size_multiplier", prediction)
                    self.assertIn("stop_width_multiplier", prediction)

                    # Verify volatility level is one of the valid levels
                    self.assertIn(prediction["volatility_level"], [l.value for l in VolatilityLevel])

                    # Verify position size multiplier is between 0.5 and 1.5
                    self.assertGreaterEqual(prediction["position_size_multiplier"], 0.5)
                    self.assertLessEqual(prediction["position_size_multiplier"], 1.5)

                    # Verify stop width multiplier is between 0.5 and 2.0
                    self.assertGreaterEqual(prediction["stop_width_multiplier"], 0.5)
                    self.assertLessEqual(prediction["stop_width_multiplier"], 2.0)

        # Run async test
        asyncio.run(test_predict_async())

    def test_flat_price_series(self):
        """Test behavior with flat price series."""
        async def test_flat_series_async():
            # Create flat price series
            prices = [100.0] * 100

            features = {
                "symbol": "BTC-USDT",
                "close_prices": prices,
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Verify prediction structure
            self.assertIn("volatility_forecast", prediction)
            self.assertIn("volatility_z", prediction)

            # Volatility should be very low or zero
            self.assertLessEqual(prediction["volatility_forecast"], 0.001)

        # Run async test
        asyncio.run(test_flat_series_async())

    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        async def test_insufficient_data_async():
            # Create features with insufficient data
            features = {
                "symbol": "BTC-USDT",
                "close_prices": [100.0, 101.0],
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Should return default prediction
            self.assertEqual(prediction["volatility_level"], VolatilityLevel.NORMAL.value)
            self.assertEqual(prediction["position_size_multiplier"], 1.0)
            self.assertEqual(prediction["stop_width_multiplier"], 1.0)

        # Run async test
        asyncio.run(test_insufficient_data_async())

    def test_invalid_data(self):
        """Test behavior with invalid data."""
        async def test_invalid_data_async():
            # Create features with invalid data
            features = {
                "symbol": "BTC-USDT",
                "close_prices": [100.0, -50.0, 0.0, float('nan'), float('inf')],
                "timestamp": datetime.now()
            }

            # Get prediction
            prediction = await self.model.predict(features)

            # Should handle invalid data gracefully
            self.assertIsInstance(prediction["volatility_forecast"], float)
            self.assertIsInstance(prediction["volatility_z"], float)

        # Run async test
        asyncio.run(test_invalid_data_async())


if __name__ == "__main__":
    unittest.main()
