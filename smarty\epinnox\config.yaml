# Epinnox Trading System Configuration
# Enhanced smart strategy with CCXT integration

# System Settings
system:
  name: "Epinnox Trading System"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# Exchange Configuration
exchanges:
  default: "binance"
  enabled:
    - "binance"
    - "bybit" 
    - "okx"
    - "huobi"
  
  # Exchange-specific settings
  binance:
    sandbox: true
    api_key: ""
    api_secret: ""
    rate_limit: 1200  # requests per minute
    has_futures: true
    
  bybit:
    sandbox: true
    api_key: ""
    api_secret: ""
    rate_limit: 600
    has_futures: true
    
  okx:
    sandbox: true
    api_key: ""
    api_secret: ""
    passphrase: ""
    rate_limit: 600
    has_futures: true
    
  huobi:
    sandbox: true
    api_key: ""
    api_secret: ""
    rate_limit: 600
    has_futures: true

# Trading Configuration
trading:
  enabled: false
  simulation_mode: true
  max_positions: 1
  position_size: 0.01  # BTC
  leverage: 1
  
  # Risk management
  stop_loss_pct: 2.0
  take_profit_pct: 4.0
  max_drawdown_pct: 5.0

# Symbols to trade
symbols:
  - "BTC/USDT"
  - "ETH/USDT"

# Strategy Configuration
strategy:
  name: "epinnox_smart"
  
  # Component weights (can be optimized)
  weights:
    technical: 0.3
    vwap: 0.2
    rsi_model: 0.15
    funding: 0.1
    open_interest: 0.1
    volatility: 0.1
    ensemble: 0.05
  
  # Signal thresholds
  base_buy_threshold: 0.3
  base_sell_threshold: -0.3
  min_data_points: 30
  
  # Technical analysis settings
  rsi_period: 14
  rsi_overbought: 70
  rsi_oversold: 30
  sma_fast: 10
  sma_slow: 30
  bb_period: 20
  bb_std: 2.0

# Model Configurations
models:
  # RSI Model
  rsi:
    enabled: true
    period: 14
    overbought_threshold: 70.0
    oversold_threshold: 30.0
    divergence_lookback: 10
  
  # VWAP Deviation Model
  vwap:
    enabled: true
    lookback_periods: 100
    significant_deviation: 2.0
    vwap_types: ["1min", "5min", "15min", "1h"]
  
  # Funding Momentum Model
  funding:
    enabled: true
    short_window: 5      # minutes
    long_window: 60      # minutes
    signal_threshold: 1.0
    contrarian: true
  
  # Open Interest Momentum Model
  open_interest:
    enabled: true
    delta_window: 5      # minutes
    z_score_window: 60   # minutes
    threshold_z: 1.0
    mode: "contrarian"   # "trend" or "contrarian"
    base_weight: 0.5
    boost_in_low_volatility: true
  
  # Volatility Regime Model
  volatility:
    enabled: false
    window: 48
    threshold: 1.5
    update_interval: 300

# Data Sources
data:
  # Feature store settings
  feature_store:
    use_redis: false
    redis_url: "redis://localhost:6379/0"
    max_time_series_size: 10000
  
  # Market data settings
  market_data:
    timeframes: ["1m", "5m", "15m", "1h"]
    history_limit: 1000
    update_interval: 1  # seconds

# Backtesting Configuration
backtesting:
  enabled: true
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_balance: 10000
  commission: 0.001  # 0.1%
  slippage: 0.0005   # 0.05%

# Optimization Configuration
optimization:
  enabled: false
  method: "grid_search"  # "grid_search", "random_search", "genetic"
  target_metric: "sharpe_ratio"
  max_iterations: 100
  
  # Parameter ranges for optimization
  parameter_ranges:
    weights:
      technical: [0.2, 0.4]
      vwap: [0.1, 0.3]
      rsi_model: [0.1, 0.2]
    thresholds:
      base_buy_threshold: [0.2, 0.4]
      base_sell_threshold: [-0.4, -0.2]

# Monitoring and Alerts
monitoring:
  enabled: true
  metrics_interval: 60  # seconds
  
  # Performance alerts
  alerts:
    max_drawdown: 5.0
    min_sharpe_ratio: 0.5
    max_consecutive_losses: 5

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/epinnox.log"
  max_size: "10MB"
  backup_count: 5

# API Configuration
api:
  enabled: false
  host: "0.0.0.0"
  port: 8000
  cors_origins: ["*"]
  
# Database Configuration (optional)
database:
  enabled: false
  type: "sqlite"  # "sqlite", "postgresql", "mysql"
  url: "sqlite:///epinnox.db"
