#!/usr/bin/env python3
"""
Comprehensive database seeding script for Money Circle Investment Club Platform.
Creates realistic demo data to showcase all dashboard features.
"""

import sqlite3
import random
import hashlib
from datetime import datetime, timedelta
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MoneyCircleDataSeeder:
    def __init__(self, db_path='data/money_circle.db'):
        self.db_path = db_path
        self.conn = None

        # Demo user data
        self.demo_users = [
            {
                'username': 'trader_alex', 'email': '<EMAIL>', 'role': 'admin',
                'display_name': '<PERSON>', 'bio': 'Quantitative trader with 8 years experience in algorithmic trading. Specializes in momentum strategies and risk management.',
                'location': 'New York, NY', 'specialization': 'day_trading', 'experience_level': 'expert'
            },
            {
                'username': 'crypto_sarah', 'email': '<EMAIL>', 'role': 'member',
                'display_name': '<PERSON>', 'bio': 'Cryptocurrency enthusiast and DeFi researcher. Focus on altcoin analysis and yield farming strategies.',
                'location': 'San Francisco, CA', 'specialization': 'crypto', 'experience_level': 'advanced'
            },
            {
                'username': 'quant_mike', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Michael Rodriguez', 'bio': 'Former hedge fund analyst turned independent trader. Expert in statistical arbitrage and pairs trading.',
                'location': 'Chicago, IL', 'specialization': 'long_term', 'experience_level': 'expert'
            },
            {
                'username': 'forex_emma', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Emma Johnson', 'bio': 'Professional forex trader with focus on major currency pairs. Specializes in technical analysis and scalping.',
                'location': 'London, UK', 'specialization': 'forex', 'experience_level': 'advanced'
            },
            {
                'username': 'options_david', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'David Kim', 'bio': 'Options trading specialist with expertise in volatility strategies and covered calls.',
                'location': 'Los Angeles, CA', 'specialization': 'options', 'experience_level': 'advanced'
            },
            {
                'username': 'swing_lisa', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Lisa Wang', 'bio': 'Swing trader focusing on growth stocks and sector rotation strategies. Part-time trader with engineering background.',
                'location': 'Seattle, WA', 'specialization': 'swing_trading', 'experience_level': 'intermediate'
            },
            {
                'username': 'momentum_james', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'James Wilson', 'bio': 'Momentum trader specializing in breakout strategies and high-volume stocks.',
                'location': 'Austin, TX', 'specialization': 'day_trading', 'experience_level': 'intermediate'
            },
            {
                'username': 'value_maria', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Maria Garcia', 'bio': 'Value investor with focus on undervalued dividend stocks and long-term wealth building.',
                'location': 'Miami, FL', 'specialization': 'long_term', 'experience_level': 'intermediate'
            },
            {
                'username': 'algo_robert', 'email': '<EMAIL>', 'role': 'admin',
                'display_name': 'Robert Chen', 'bio': 'Algorithmic trading developer and platform administrator. Expert in Python and trading system architecture.',
                'location': 'Boston, MA', 'specialization': 'day_trading', 'experience_level': 'expert'
            },
            {
                'username': 'scalp_jenny', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Jennifer Brown', 'bio': 'Professional scalper trading futures and forex. High-frequency trading specialist.',
                'location': 'Denver, CO', 'specialization': 'day_trading', 'experience_level': 'advanced'
            },
            {
                'username': 'growth_tom', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Thomas Anderson', 'bio': 'Growth stock investor with focus on technology and biotech sectors.',
                'location': 'Portland, OR', 'specialization': 'swing_trading', 'experience_level': 'intermediate'
            },
            {
                'username': 'macro_anna', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Anna Petrov', 'bio': 'Macro trader analyzing global economic trends and currency movements.',
                'location': 'Toronto, ON', 'specialization': 'forex', 'experience_level': 'advanced'
            },
            {
                'username': 'tech_kevin', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Kevin Lee', 'bio': 'Technology sector specialist with focus on AI and semiconductor stocks.',
                'location': 'San Jose, CA', 'specialization': 'swing_trading', 'experience_level': 'intermediate'
            },
            {
                'username': 'dividend_susan', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Susan Miller', 'bio': 'Conservative investor focused on dividend growth stocks and REITs.',
                'location': 'Phoenix, AZ', 'specialization': 'long_term', 'experience_level': 'beginner'
            },
            {
                'username': 'futures_carlos', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Carlos Mendez', 'bio': 'Commodities and futures trader with expertise in energy and agricultural markets.',
                'location': 'Houston, TX', 'specialization': 'day_trading', 'experience_level': 'advanced'
            },
            {
                'username': 'etf_rachel', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Rachel Davis', 'bio': 'ETF strategist focusing on sector rotation and international diversification.',
                'location': 'Atlanta, GA', 'specialization': 'swing_trading', 'experience_level': 'intermediate'
            },
            {
                'username': 'penny_steve', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Steve Johnson', 'bio': 'Small-cap and penny stock trader with focus on biotech and mining sectors.',
                'location': 'Las Vegas, NV', 'specialization': 'day_trading', 'experience_level': 'beginner'
            },
            {
                'username': 'bond_patricia', 'email': '<EMAIL>', 'role': 'member',
                'display_name': 'Patricia Wilson', 'bio': 'Fixed income specialist trading government and corporate bonds.',
                'location': 'Washington, DC', 'specialization': 'long_term', 'experience_level': 'advanced'
            }
        ]

        # Trading strategies data
        self.demo_strategies = [
            {
                'title': 'Momentum Breakout Pro', 'description': 'High-frequency momentum strategy targeting breakout patterns in large-cap stocks.',
                'strategy_type': 'day_trading', 'risk_level': 'high', 'min_investment': 5000
            },
            {
                'title': 'Crypto Swing Master', 'description': 'Multi-timeframe analysis for cryptocurrency swing trading with risk management.',
                'strategy_type': 'crypto', 'risk_level': 'high', 'min_investment': 2000
            },
            {
                'title': 'Dividend Growth Elite', 'description': 'Long-term strategy focusing on dividend aristocrats and growth stocks.',
                'strategy_type': 'long_term', 'risk_level': 'low', 'min_investment': 10000
            },
            {
                'title': 'Forex Scalping Bot', 'description': 'Automated scalping strategy for major currency pairs with tight risk controls.',
                'strategy_type': 'forex', 'risk_level': 'medium', 'min_investment': 3000
            },
            {
                'title': 'Options Income Generator', 'description': 'Conservative options strategy using covered calls and cash-secured puts.',
                'strategy_type': 'options', 'risk_level': 'medium', 'min_investment': 15000
            },
            {
                'title': 'Tech Sector Rotation', 'description': 'Sector rotation strategy focusing on technology subsectors and growth trends.',
                'strategy_type': 'swing_trading', 'risk_level': 'medium', 'min_investment': 7500
            },
            {
                'title': 'Mean Reversion Alpha', 'description': 'Statistical arbitrage strategy exploiting mean reversion in stock pairs.',
                'strategy_type': 'day_trading', 'risk_level': 'medium', 'min_investment': 25000
            },
            {
                'title': 'Global Macro Trends', 'description': 'Macro-economic strategy trading currencies, commodities, and bonds.',
                'strategy_type': 'forex', 'risk_level': 'high', 'min_investment': 20000
            },
            {
                'title': 'Small Cap Growth Hunter', 'description': 'Growth strategy targeting undervalued small-cap stocks with high potential.',
                'strategy_type': 'swing_trading', 'risk_level': 'high', 'min_investment': 5000
            },
            {
                'title': 'Volatility Harvester', 'description': 'Advanced options strategy profiting from volatility changes and time decay.',
                'strategy_type': 'options', 'risk_level': 'high', 'min_investment': 30000
            }
        ]

    def connect_db(self):
        """Connect to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            logger.info(f"✅ Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False

    def hash_password(self, password):
        """Hash password for storage."""
        return hashlib.sha256(password.encode()).hexdigest()

    def clear_existing_data(self):
        """Clear existing demo data to start fresh."""
        try:
            tables_to_clear = [
                'trading_performance', 'strategy_following', 'strategy_proposals',
                'member_profiles', 'user_connections', 'member_achievements',
                'strategy_performance', 'club_analytics'
            ]

            for table in tables_to_clear:
                try:
                    self.conn.execute(f"DELETE FROM {table}")
                    logger.info(f"✅ Cleared table: {table}")
                except sqlite3.OperationalError:
                    logger.warning(f"⚠️ Table {table} doesn't exist, skipping")

            # Clear demo users (keep epinnox admin)
            self.conn.execute("DELETE FROM users WHERE username != 'epinnox'")

            self.conn.commit()
            logger.info("✅ Existing demo data cleared")

        except Exception as e:
            logger.error(f"❌ Error clearing data: {e}")

    def create_demo_users(self):
        """Create demo user accounts."""
        try:
            password_hash = self.hash_password("securepass123")

            for user_data in self.demo_users:
                # Insert user
                self.conn.execute("""
                    INSERT OR REPLACE INTO users (username, email, hashed_password, role, date_joined)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    user_data['username'],
                    user_data['email'],
                    password_hash,
                    user_data['role'],
                    datetime.now().isoformat()
                ))

                # Get user ID
                user_id = self.conn.execute(
                    "SELECT id FROM users WHERE username = ?",
                    (user_data['username'],)
                ).fetchone()[0]

                # Insert member profile
                self.conn.execute("""
                    INSERT OR REPLACE INTO member_profiles
                    (user_id, display_name, bio, location, specialization, experience_level, verified, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user_id,
                    user_data['display_name'],
                    user_data['bio'],
                    user_data['location'],
                    user_data['specialization'],
                    user_data['experience_level'],
                    random.choice([True, False]),
                    datetime.now().isoformat()
                ))

            self.conn.commit()
            logger.info(f"✅ Created {len(self.demo_users)} demo users")

        except Exception as e:
            logger.error(f"❌ Error creating demo users: {e}")

    def generate_trading_performance(self):
        """Generate realistic trading performance data."""
        try:
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()

            # Performance profiles for different trader types
            performance_profiles = {
                'conservative': {'return_range': (-2, 4), 'win_rate_range': (55, 70), 'volatility_range': (0.08, 0.15)},
                'moderate': {'return_range': (-4, 6), 'win_rate_range': (45, 65), 'volatility_range': (0.12, 0.25)},
                'aggressive': {'return_range': (-8, 12), 'win_rate_range': (40, 60), 'volatility_range': (0.20, 0.40)}
            }

            for user in users:
                user_id = user[0]
                username = user[1]

                # Assign performance profile based on username
                if any(x in username for x in ['dividend', 'value', 'bond']):
                    profile = performance_profiles['conservative']
                    base_portfolio = random.randint(15000, 50000)
                elif any(x in username for x in ['crypto', 'penny', 'futures']):
                    profile = performance_profiles['aggressive']
                    base_portfolio = random.randint(5000, 25000)
                else:
                    profile = performance_profiles['moderate']
                    base_portfolio = random.randint(8000, 35000)

                # Generate 6 months of daily performance
                start_date = datetime.now() - timedelta(days=180)
                current_portfolio = base_portfolio

                for day in range(180):
                    trade_date = start_date + timedelta(days=day)

                    # Skip weekends
                    if trade_date.weekday() >= 5:
                        continue

                    # Generate daily return
                    daily_return = random.uniform(*profile['return_range'])

                    # Apply some trending behavior
                    if day > 30:
                        trend_factor = random.uniform(-0.5, 0.5)
                        daily_return += trend_factor

                    # Update portfolio value
                    current_portfolio *= (1 + daily_return / 100)

                    # Calculate metrics
                    win_rate = random.uniform(*profile['win_rate_range'])
                    volatility = random.uniform(*profile['volatility_range'])
                    sharpe_ratio = max(0.1, (daily_return / 100) / volatility * 15.8)  # Annualized approximation

                    # Insert performance record
                    self.conn.execute("""
                        INSERT INTO trading_performance
                        (user_id, date, total_return, daily_return, portfolio_value, win_rate,
                         volatility, sharpe_ratio, trades_count, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        trade_date.date().isoformat(),
                        ((current_portfolio - base_portfolio) / base_portfolio) * 100,
                        daily_return,
                        current_portfolio,
                        win_rate,
                        volatility,
                        min(sharpe_ratio, 3.0),  # Cap Sharpe ratio
                        random.randint(5, 50),
                        datetime.now().isoformat()
                    ))

            self.conn.commit()
            logger.info("✅ Generated trading performance data for all users")

        except Exception as e:
            logger.error(f"❌ Error generating trading performance: {e}")

    def create_trading_strategies(self):
        """Create demo trading strategies."""
        try:
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()

            for i, strategy_data in enumerate(self.demo_strategies):
                # Assign strategy to a random user
                creator = random.choice(users)
                creator_id = creator[0]

                # Insert strategy
                self.conn.execute("""
                    INSERT INTO strategy_proposals
                    (user_id, proposed_by, title, name, description, strategy_type, risk_level,
                     status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    creator_id,
                    creator_id,
                    strategy_data['title'],
                    strategy_data['title'],
                    strategy_data['description'],
                    strategy_data['strategy_type'],
                    strategy_data['risk_level'],
                    'approved',
                    datetime.now().isoformat()
                ))

                strategy_id = self.conn.lastrowid

                # Generate strategy performance history
                start_date = datetime.now() - timedelta(days=90)
                base_value = 100000  # Starting portfolio value
                current_value = base_value

                for day in range(90):
                    perf_date = start_date + timedelta(days=day)

                    # Skip weekends
                    if perf_date.weekday() >= 5:
                        continue

                    # Generate strategy performance based on risk level
                    if strategy_data['risk_level'] == 'low':
                        daily_return = random.uniform(-1, 3)
                    elif strategy_data['risk_level'] == 'medium':
                        daily_return = random.uniform(-3, 5)
                    else:  # high risk
                        daily_return = random.uniform(-6, 8)

                    current_value *= (1 + daily_return / 100)
                    total_return = ((current_value - base_value) / base_value) * 100

                    # Insert strategy performance
                    self.conn.execute("""
                        INSERT INTO strategy_performance
                        (strategy_id, date, portfolio_value, daily_return, total_return,
                         drawdown, trades_count, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        strategy_id,
                        perf_date.date().isoformat(),
                        current_value,
                        daily_return,
                        total_return,
                        random.uniform(0, 15),  # Max drawdown %
                        random.randint(3, 25),
                        datetime.now().isoformat()
                    ))

                # Create followers for popular strategies
                follower_count = random.randint(5, 30)
                potential_followers = [u for u in users if u[0] != creator_id]
                followers = random.sample(potential_followers, min(follower_count, len(potential_followers)))

                for follower in followers:
                    self.conn.execute("""
                        INSERT INTO strategy_following
                        (follower_id, strategy_id, strategy_creator_id, investment_amount,
                         is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        follower[0],
                        strategy_id,
                        creator_id,
                        random.randint(1000, 10000),
                        True,
                        datetime.now().isoformat()
                    ))

            self.conn.commit()
            logger.info(f"✅ Created {len(self.demo_strategies)} trading strategies")

        except Exception as e:
            logger.error(f"❌ Error creating trading strategies: {e}")

    def create_user_connections(self):
        """Create social connections between users."""
        try:
            users = self.conn.execute("SELECT id FROM users WHERE username != 'epinnox'").fetchall()
            user_ids = [u[0] for u in users]

            # Create random connections
            for user_id in user_ids:
                # Each user follows 3-8 other users
                connection_count = random.randint(3, 8)
                potential_connections = [uid for uid in user_ids if uid != user_id]
                connections = random.sample(potential_connections, min(connection_count, len(potential_connections)))

                for connected_user_id in connections:
                    self.conn.execute("""
                        INSERT OR IGNORE INTO user_connections
                        (user_id, connected_user_id, connection_type, created_at)
                        VALUES (?, ?, ?, ?)
                    """, (
                        user_id,
                        connected_user_id,
                        'following',
                        datetime.now().isoformat()
                    ))

            self.conn.commit()
            logger.info("✅ Created user connections")

        except Exception as e:
            logger.error(f"❌ Error creating user connections: {e}")

    def create_member_achievements(self):
        """Create member achievements and badges."""
        try:
            users = self.conn.execute("SELECT id, username FROM users WHERE username != 'epinnox'").fetchall()

            achievements = [
                ('top_performer', 'Top Performer', '🏆', 'Achieved top 10% returns'),
                ('consistent_trader', 'Consistent Trader', '🎯', 'Maintained positive returns for 30+ days'),
                ('strategy_master', 'Strategy Master', '🧠', 'Created 3+ successful strategies'),
                ('social_leader', 'Social Leader', '🤝', 'Has 10+ followers'),
                ('veteran_trader', 'Veteran Trader', '⭐', 'Member for 1+ year'),
                ('risk_manager', 'Risk Manager', '🛡️', 'Excellent risk-adjusted returns'),
                ('volume_trader', 'Volume Trader', '📈', 'High trading volume'),
                ('diversified_portfolio', 'Diversified Portfolio', '🌐', 'Trades multiple asset classes')
            ]

            for user in users:
                user_id = user[0]
                username = user[1]

                # Assign 2-5 random achievements per user
                user_achievements = random.sample(achievements, random.randint(2, 5))

                for achievement_id, title, icon, description in user_achievements:
                    self.conn.execute("""
                        INSERT INTO member_achievements
                        (user_id, achievement_id, title, description, icon, earned_date, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        achievement_id,
                        title,
                        description,
                        icon,
                        (datetime.now() - timedelta(days=random.randint(1, 180))).isoformat(),
                        datetime.now().isoformat()
                    ))

            self.conn.commit()
            logger.info("✅ Created member achievements")

        except Exception as e:
            logger.error(f"❌ Error creating member achievements: {e}")

    def generate_club_analytics(self):
        """Generate club-wide analytics data."""
        try:
            # Get aggregated data
            total_users = self.conn.execute("SELECT COUNT(*) FROM users WHERE username != 'epinnox'").fetchone()[0]

            # Calculate club performance metrics
            avg_return = self.conn.execute("""
                SELECT AVG(total_return) FROM trading_performance
                WHERE date >= date('now', '-30 days')
            """).fetchone()[0] or 0

            total_strategies = self.conn.execute("SELECT COUNT(*) FROM strategy_proposals").fetchone()[0]

            total_connections = self.conn.execute("SELECT COUNT(*) FROM user_connections").fetchone()[0]

            # Log club analytics (table structure may vary)
            logger.info(f"Club Analytics Summary:")
            logger.info(f"  Total Members: {total_users}")
            logger.info(f"  Avg Monthly Return: {avg_return:.2f}%")
            logger.info(f"  Total Strategies: {total_strategies}")
            logger.info(f"  Total Connections: {total_connections}")
            logger.info(f"  Platform AUM: ${random.randint(2000000, 5000000):,}")

            self.conn.commit()
            logger.info("✅ Generated club analytics data")

        except Exception as e:
            logger.error(f"❌ Error generating club analytics: {e}")

    def run_full_seed(self):
        """Run the complete seeding process."""
        logger.info("🚀 Starting Money Circle database seeding...")

        if not self.connect_db():
            return False

        try:
            # Step 1: Clear existing data
            logger.info("📝 Step 1: Clearing existing demo data...")
            self.clear_existing_data()

            # Step 2: Create demo users
            logger.info("👥 Step 2: Creating demo users...")
            self.create_demo_users()

            # Step 3: Generate trading performance
            logger.info("📊 Step 3: Generating trading performance data...")
            self.generate_trading_performance()

            # Step 4: Create trading strategies
            logger.info("🎯 Step 4: Creating trading strategies...")
            self.create_trading_strategies()

            # Step 5: Create user connections
            logger.info("🤝 Step 5: Creating user connections...")
            self.create_user_connections()

            # Step 6: Create member achievements
            logger.info("🏆 Step 6: Creating member achievements...")
            self.create_member_achievements()

            # Step 7: Generate club analytics
            logger.info("📈 Step 7: Generating club analytics...")
            self.generate_club_analytics()

            logger.info("✅ Database seeding completed successfully!")
            logger.info(f"📊 Created demo data for {len(self.demo_users)} users")
            logger.info(f"🎯 Created {len(self.demo_strategies)} trading strategies")
            logger.info("🔐 All demo accounts use password: securepass123")

            return True

        except Exception as e:
            logger.error(f"❌ Seeding failed: {e}")
            return False

        finally:
            if self.conn:
                self.conn.close()

def main():
    """Main function to run the seeding script."""
    seeder = MoneyCircleDataSeeder()
    success = seeder.run_full_seed()

    if success:
        print("\nMONEY CIRCLE DEMO DATA SEEDING COMPLETE!")
        print("=" * 60)
        print("Database populated with realistic demo data")
        print("18 diverse member accounts created")
        print("6 months of trading performance history")
        print("10 trading strategies with followers")
        print("Social connections and achievements")
        print("Club analytics and aggregated data")
        print("\nLogin Credentials:")
        print("   Username: Any demo username (e.g., trader_alex)")
        print("   Password: securepass123")
        print("\nPlatform ready at: http://localhost:8084")
        return 0
    else:
        print("\nSEEDING FAILED - Check logs for details")
        return 1

if __name__ == "__main__":
    exit(main())
