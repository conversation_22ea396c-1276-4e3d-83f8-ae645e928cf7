"""
Enhanced LLM Manager for the smart-trader system.

This module provides a unified, robust LLM interface with:
- Standardized JSON parsing and validation
- Enhanced error handling and fallbacks
- Performance monitoring and health checks
- Adaptive throttling and rate limiting
- Memory management and context tracking
"""

import json
import logging
import os
import re
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum

# Import llama_cpp with fallback
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    logging.warning("llama_cpp not available, LLM will run in dummy mode")

logger = logging.getLogger(__name__)


class LLMAction(Enum):
    """Valid LLM actions."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class LLMResponse:
    """Standardized LLM response structure."""
    action: LLMAction
    confidence: float
    rationale: str
    raw_response: str = ""
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "action": self.action.value,
            "confidence": self.confidence,
            "rationale": self.rationale,
            "timestamp": self.timestamp.isoformat(),
            "processing_time": self.processing_time
        }


@dataclass
class LLMMetrics:
    """LLM performance metrics."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    avg_latency: float = 0.0
    avg_confidence: float = 0.0
    last_call_time: Optional[datetime] = None
    error_rate: float = 0.0
    
    def update_call(self, success: bool, latency: float, confidence: float = 0.0):
        """Update metrics with new call data."""
        self.total_calls += 1
        self.last_call_time = datetime.now()
        
        if success:
            self.successful_calls += 1
            # Update running averages
            self.avg_latency = (self.avg_latency * (self.successful_calls - 1) + latency) / self.successful_calls
            self.avg_confidence = (self.avg_confidence * (self.successful_calls - 1) + confidence) / self.successful_calls
        else:
            self.failed_calls += 1
        
        self.error_rate = self.failed_calls / self.total_calls if self.total_calls > 0 else 0.0


class EnhancedLLMManager:
    """
    Enhanced LLM Manager with robust error handling and monitoring.
    """
    
    def __init__(
        self,
        model_path: str,
        prompt_template: Dict[str, Any],
        config: Dict[str, Any],
        dummy_mode: bool = False
    ):
        """
        Initialize the Enhanced LLM Manager.
        
        Args:
            model_path: Path to the GGUF model file
            prompt_template: Prompt template configuration
            config: LLM configuration
            dummy_mode: Whether to run in dummy mode
        """
        self.model_path = model_path
        self.prompt_template = prompt_template
        self.config = config
        self.dummy_mode = dummy_mode or not LLAMA_CPP_AVAILABLE
        
        # Model instance
        self.model: Optional[Llama] = None
        
        # Performance metrics
        self.metrics = LLMMetrics()
        
        # Adaptive throttling
        self.last_call_time = 0.0
        self.current_throttle = config.get("call_interval_s", 30)
        self.min_throttle = config.get("min_throttle_interval", 10)
        self.max_throttle = config.get("max_throttle_interval", 120)
        
        # Context memory for better responses
        self.context_memory: List[Dict[str, Any]] = []
        self.max_memory_size = config.get("max_memory_size", 10)
        
        # Initialize model
        if not self.dummy_mode:
            self._load_model()
        
        logger.info(f"Enhanced LLM Manager initialized (dummy_mode={self.dummy_mode})")

    def _load_model(self) -> None:
        """Load the LLM model with enhanced error handling."""
        try:
            if not os.path.exists(self.model_path):
                logger.error(f"Model file not found: {self.model_path}")
                self.dummy_mode = True
                return
            
            logger.info(f"Loading LLM model from {self.model_path}...")
            
            # Model configuration
            model_config = {
                "model_path": self.model_path,
                "n_ctx": self.config.get("n_ctx", 2048),
                "n_threads": self.config.get("n_threads", 4),
                "n_gpu_layers": self.config.get("n_gpu_layers", 0),
                "verbose": self.config.get("verbose", False)
            }
            
            self.model = Llama(**model_config)
            
            # Test the model with a simple prompt
            test_response = self.model(
                prompt="Test",
                max_tokens=1,
                temperature=0.0
            )
            
            logger.info("LLM model loaded and tested successfully")
            
        except Exception as e:
            logger.error(f"Failed to load LLM model: {e}")
            self.dummy_mode = True
            self.model = None

    def _should_throttle(self) -> bool:
        """Check if we should throttle the LLM call."""
        current_time = time.time()
        time_since_last = current_time - self.last_call_time
        return time_since_last < self.current_throttle

    def _update_adaptive_throttle(self, success: bool, latency: float) -> None:
        """Update adaptive throttling based on performance."""
        if not self.config.get("adaptive_throttle", True):
            return
        
        # Increase throttle on failures or high latency
        if not success or latency > 5.0:
            self.current_throttle = min(
                self.current_throttle * 1.5,
                self.max_throttle
            )
        else:
            # Decrease throttle on success
            self.current_throttle = max(
                self.current_throttle * 0.95,
                self.min_throttle
            )
        
        logger.debug(f"Adaptive throttle updated to {self.current_throttle:.1f}s")

    def _format_prompt(self, context: Dict[str, Any]) -> str:
        """Format the prompt with context data."""
        try:
            # Add context memory to the prompt
            if self.context_memory:
                context["recent_decisions"] = self._format_memory()
            
            # Get template string
            template_str = self.prompt_template.get("template", "")
            
            # Format with context
            formatted_prompt = template_str.format(**context)
            
            logger.debug(f"Formatted prompt length: {len(formatted_prompt)} characters")
            return formatted_prompt
            
        except KeyError as e:
            logger.error(f"Missing key in prompt template: {e}")
            # Fallback prompt
            return self._create_fallback_prompt(context)
        except Exception as e:
            logger.error(f"Error formatting prompt: {e}")
            return self._create_fallback_prompt(context)

    def _create_fallback_prompt(self, context: Dict[str, Any]) -> str:
        """Create a simple fallback prompt."""
        symbol = context.get("symbol", "BTC-USDT")
        signals = context.get("signals", "No signals available")
        
        return f"""
        <|system|>
        You are a trading assistant. Analyze the market data and respond with JSON only.
        <|user|>
        Symbol: {symbol}
        Signals: {signals}
        
        Respond with JSON in this format:
        {{"action": "HOLD", "confidence": 0.5, "rationale": "Your reasoning"}}
        <|assistant|>
        """

    def _format_memory(self) -> str:
        """Format recent decisions for context."""
        if not self.context_memory:
            return "No recent decisions"
        
        memory_str = "Recent decisions:\n"
        for i, decision in enumerate(self.context_memory[-3:]):  # Last 3 decisions
            memory_str += f"{i+1}. {decision['action']} (confidence: {decision['confidence']:.2f}) - {decision['rationale'][:50]}...\n"
        
        return memory_str

    def _parse_response(self, raw_response: str) -> LLMResponse:
        """Parse LLM response with enhanced error handling."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{[^{}]*\}', raw_response)
            
            if not json_match:
                logger.warning("No JSON found in LLM response")
                return self._create_fallback_response(raw_response)
            
            json_str = json_match.group(0)
            parsed_data = json.loads(json_str)
            
            # Validate and normalize response
            action_str = parsed_data.get("action", "HOLD").upper()
            
            # Validate action
            try:
                action = LLMAction(action_str)
            except ValueError:
                logger.warning(f"Invalid action '{action_str}', defaulting to HOLD")
                action = LLMAction.HOLD
            
            # Validate confidence
            confidence = parsed_data.get("confidence", 0.5)
            try:
                confidence = float(confidence)
                confidence = max(0.0, min(1.0, confidence))  # Clamp to [0, 1]
            except (ValueError, TypeError):
                logger.warning(f"Invalid confidence '{confidence}', defaulting to 0.5")
                confidence = 0.5
            
            # Get rationale
            rationale = parsed_data.get("rationale", "No rationale provided")
            if not isinstance(rationale, str):
                rationale = str(rationale)
            
            return LLMResponse(
                action=action,
                confidence=confidence,
                rationale=rationale,
                raw_response=raw_response
            )
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error: {e}")
            return self._create_fallback_response(raw_response)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return self._create_fallback_response(raw_response)

    def _create_fallback_response(self, raw_response: str) -> LLMResponse:
        """Create a fallback response when parsing fails."""
        return LLMResponse(
            action=LLMAction.HOLD,
            confidence=0.5,
            rationale=f"Failed to parse LLM response: {raw_response[:100]}...",
            raw_response=raw_response
        )

    def _create_dummy_response(self, context: Dict[str, Any]) -> LLMResponse:
        """Create a dummy response for testing."""
        # Simple logic for dummy responses
        signals = context.get("signals", "")
        if "buy" in signals.lower() or "bullish" in signals.lower():
            action = LLMAction.BUY
            confidence = 0.7
            rationale = "Dummy response: Bullish signals detected"
        elif "sell" in signals.lower() or "bearish" in signals.lower():
            action = LLMAction.SELL
            confidence = 0.7
            rationale = "Dummy response: Bearish signals detected"
        else:
            action = LLMAction.HOLD
            confidence = 0.6
            rationale = "Dummy response: Mixed or unclear signals"
        
        return LLMResponse(
            action=action,
            confidence=confidence,
            rationale=rationale,
            raw_response="[DUMMY MODE]"
        )

    async def generate_decision(self, context: Dict[str, Any]) -> LLMResponse:
        """
        Generate a trading decision using the LLM.
        
        Args:
            context: Context dictionary with market data and signals
            
        Returns:
            LLMResponse with the trading decision
        """
        start_time = time.time()
        
        try:
            # Check throttling
            if self._should_throttle():
                time_to_wait = self.current_throttle - (time.time() - self.last_call_time)
                logger.debug(f"Throttling LLM call, waiting {time_to_wait:.1f}s")
                return self._create_fallback_response("Throttled")
            
            # Update last call time
            self.last_call_time = time.time()
            
            # Handle dummy mode
            if self.dummy_mode:
                response = self._create_dummy_response(context)
                response.processing_time = time.time() - start_time
                self.metrics.update_call(True, response.processing_time, response.confidence)
                return response
            
            # Format prompt
            prompt = self._format_prompt(context)
            
            # Generate response
            logger.info("Calling LLM for trading decision...")
            
            llm_output = self.model(
                prompt=prompt,
                max_tokens=self.config.get("max_tokens", 128),
                temperature=self.config.get("temperature", 0.0),
                stop=["\n\n", "<|user|>", "<|system|>"]
            )
            
            # Extract text from response
            if isinstance(llm_output, dict):
                raw_response = llm_output.get("choices", [{}])[0].get("text", "")
            else:
                raw_response = str(llm_output)
            
            # Parse response
            response = self._parse_response(raw_response)
            response.processing_time = time.time() - start_time
            
            # Update metrics
            success = response.action != LLMAction.HOLD or "failed" not in response.rationale.lower()
            self.metrics.update_call(success, response.processing_time, response.confidence)
            
            # Update adaptive throttling
            self._update_adaptive_throttle(success, response.processing_time)
            
            # Add to context memory
            self._add_to_memory(response, context)
            
            logger.info(f"LLM decision: {response.action.value} (confidence: {response.confidence:.2f})")
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error generating LLM decision: {e}")
            
            # Update metrics for failure
            self.metrics.update_call(False, processing_time)
            self._update_adaptive_throttle(False, processing_time)
            
            # Return fallback response
            return LLMResponse(
                action=LLMAction.HOLD,
                confidence=0.5,
                rationale=f"LLM error: {str(e)}",
                processing_time=processing_time
            )

    def _add_to_memory(self, response: LLMResponse, context: Dict[str, Any]) -> None:
        """Add decision to context memory."""
        memory_entry = {
            "timestamp": response.timestamp.isoformat(),
            "symbol": context.get("symbol", ""),
            "action": response.action.value,
            "confidence": response.confidence,
            "rationale": response.rationale,
            "processing_time": response.processing_time
        }
        
        self.context_memory.append(memory_entry)
        
        # Limit memory size
        if len(self.context_memory) > self.max_memory_size:
            self.context_memory.pop(0)

    def get_health_status(self) -> Dict[str, Any]:
        """Get LLM health status and metrics."""
        return {
            "is_healthy": self.metrics.error_rate < 0.1 and self.metrics.avg_latency < 10.0,
            "dummy_mode": self.dummy_mode,
            "model_loaded": self.model is not None,
            "metrics": {
                "total_calls": self.metrics.total_calls,
                "successful_calls": self.metrics.successful_calls,
                "failed_calls": self.metrics.failed_calls,
                "error_rate": self.metrics.error_rate,
                "avg_latency": self.metrics.avg_latency,
                "avg_confidence": self.metrics.avg_confidence,
                "last_call_time": self.metrics.last_call_time.isoformat() if self.metrics.last_call_time else None
            },
            "throttling": {
                "current_throttle": self.current_throttle,
                "min_throttle": self.min_throttle,
                "max_throttle": self.max_throttle
            },
            "memory": {
                "entries": len(self.context_memory),
                "max_size": self.max_memory_size
            }
        }

    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.metrics = LLMMetrics()
        logger.info("LLM metrics reset")

    def clear_memory(self) -> None:
        """Clear context memory."""
        self.context_memory.clear()
        logger.info("LLM context memory cleared")
