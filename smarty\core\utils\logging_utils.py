"""
Logging utility functions for the smart-trader system.
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional, Dict, Any


def setup_logging(
    level: int = logging.INFO,
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    json_format: bool = True
) -> logging.Logger:
    """
    Set up structured logging with improved handler management.

    Args:
        level: Logging level
        log_file: Optional file path for logging
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        json_format: Whether to use JSON formatting

    Returns:
        Logger instance
    """
    logger = logging.getLogger("smarty")
    logger.setLevel(level)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Create formatters
    if json_format:
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "message": "%(message)s", '
            '"module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}'
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
        )

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Create file handler if specified
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Use rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(f"smarty.{name}")


def log_function_call(logger: logging.Logger, func_name: str, args: tuple, kwargs: dict) -> None:
    """
    Log function call details for debugging.
    
    Args:
        logger: Logger instance
        func_name: Function name
        args: Function arguments
        kwargs: Function keyword arguments
    """
    # Sanitize arguments for logging (avoid logging sensitive data)
    safe_args = []
    for arg in args:
        if isinstance(arg, str) and len(arg) > 100:
            safe_args.append(f"{arg[:50]}...{arg[-50:]}")
        else:
            safe_args.append(str(arg))
    
    safe_kwargs = {}
    for key, value in kwargs.items():
        if key.lower() in ['password', 'secret', 'key', 'token']:
            safe_kwargs[key] = "***REDACTED***"
        elif isinstance(value, str) and len(value) > 100:
            safe_kwargs[key] = f"{value[:50]}...{value[-50:]}"
        else:
            safe_kwargs[key] = value
    
    logger.debug(f"Calling {func_name} with args={safe_args}, kwargs={safe_kwargs}")


def log_performance_metrics(logger: logging.Logger, metrics: Dict[str, Any]) -> None:
    """
    Log performance metrics in a structured format.
    
    Args:
        logger: Logger instance
        metrics: Dictionary of metrics to log
    """
    logger.info(f"Performance metrics: {metrics}")


def configure_third_party_loggers(level: int = logging.WARNING) -> None:
    """
    Configure third-party library loggers to reduce noise.
    
    Args:
        level: Logging level for third-party loggers
    """
    # Common noisy loggers
    noisy_loggers = [
        'aiohttp.access',
        'aiohttp.client',
        'aiohttp.internal',
        'asyncio',
        'urllib3.connectionpool',
        'requests.packages.urllib3',
        'websockets.protocol',
        'websockets.client',
    ]
    
    for logger_name in noisy_loggers:
        logging.getLogger(logger_name).setLevel(level)


class ContextFilter(logging.Filter):
    """
    Custom filter to add context information to log records.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__()
        self.context = context
    
    def filter(self, record):
        for key, value in self.context.items():
            setattr(record, key, value)
        return True


def add_context_to_logger(logger: logging.Logger, context: Dict[str, Any]) -> None:
    """
    Add context information to all log records from a logger.
    
    Args:
        logger: Logger instance
        context: Context dictionary to add to log records
    """
    context_filter = ContextFilter(context)
    logger.addFilter(context_filter)


if __name__ == "__main__":
    # Test logging utilities
    print("Testing logging utilities:")
    
    # Test basic setup
    logger = setup_logging(level=logging.DEBUG, json_format=False)
    logger.info("Test info message")
    logger.debug("Test debug message")
    logger.warning("Test warning message")
    
    # Test file logging
    test_log_file = "test_logs/test.log"
    file_logger = setup_logging(level=logging.INFO, log_file=test_log_file)
    file_logger.info("Test file logging")
    
    # Test function call logging
    test_logger = get_logger("test")
    log_function_call(test_logger, "test_function", ("arg1", "arg2"), {"key": "value"})
    
    # Test performance metrics
    metrics = {"latency": 0.123, "throughput": 1000, "errors": 0}
    log_performance_metrics(test_logger, metrics)
    
    # Test context filter
    add_context_to_logger(test_logger, {"component": "test", "version": "1.0"})
    test_logger.info("Message with context")
    
    print("Logging tests completed")
