/**
 * Money Circle - Common JavaScript Functionality
 * Shared utilities and components for all pages
 */

// Global Money Circle namespace
window.MoneyCircle = window.MoneyCircle || {};

// Utility functions
MoneyCircle.utils = {
    /**
     * Format currency values
     */
    formatCurrency: function(value, currency = 'USD', decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '$0.00';
        }

        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(value);
    },

    /**
     * Format percentage values
     */
    formatPercentage: function(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '0.00%';
        }

        return new Intl.NumberFormat('en-US', {
            style: 'percent',
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(value / 100);
    },

    /**
     * Format large numbers with K, M, B suffixes
     */
    formatLargeNumber: function(value, decimals = 1) {
        if (value === null || value === undefined || isNaN(value)) {
            return '0';
        }

        const absValue = Math.abs(value);
        const sign = value < 0 ? '-' : '';

        if (absValue >= 1e9) {
            return sign + (absValue / 1e9).toFixed(decimals) + 'B';
        } else if (absValue >= 1e6) {
            return sign + (absValue / 1e6).toFixed(decimals) + 'M';
        } else if (absValue >= 1e3) {
            return sign + (absValue / 1e3).toFixed(decimals) + 'K';
        } else {
            return sign + absValue.toFixed(decimals);
        }
    },

    /**
     * Format timestamps
     */
    formatTimestamp: function(timestamp, format = 'relative') {
        if (!timestamp) return 'Unknown';

        const date = new Date(timestamp);
        const now = new Date();

        if (format === 'relative') {
            const diffMs = now - date;
            const diffSecs = Math.floor(diffMs / 1000);
            const diffMins = Math.floor(diffSecs / 60);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffSecs < 60) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;

            return date.toLocaleDateString();
        } else if (format === 'full') {
            return date.toLocaleString();
        } else {
            return date.toLocaleDateString();
        }
    },

    /**
     * Debounce function calls
     */
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    },

    /**
     * Throttle function calls
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// API utilities
MoneyCircle.api = {
    /**
     * Make authenticated API requests
     */
    request: async function(endpoint, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include'
        };

        const config = { ...defaultOptions, ...options };

        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            const response = await fetch(endpoint, config);

            if (response.status === 401) {
                // Redirect to login on authentication failure
                window.location.href = '/login';
                return null;
            }

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    },

    /**
     * GET request helper
     */
    get: function(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    },

    /**
     * POST request helper
     */
    post: function(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: data
        });
    },

    /**
     * PUT request helper
     */
    put: function(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data
        });
    },

    /**
     * DELETE request helper
     */
    delete: function(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }
};

// UI utilities
MoneyCircle.ui = {
    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info', duration = 3000) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-message">${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to page
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Auto-remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, duration);
    },

    /**
     * Show loading spinner
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element) {
            element.classList.add('loading');
            element.setAttribute('data-original-content', element.innerHTML);
            element.innerHTML = '<div class="spinner"></div>';
        }
    },

    /**
     * Hide loading spinner
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element && element.classList.contains('loading')) {
            element.classList.remove('loading');
            const originalContent = element.getAttribute('data-original-content');
            if (originalContent) {
                element.innerHTML = originalContent;
                element.removeAttribute('data-original-content');
            }
        }
    },

    /**
     * Animate number changes
     */
    animateNumber: function(element, targetValue, duration = 1000) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (!element) return;

        const startValue = parseFloat(element.textContent.replace(/[^0-9.-]/g, '')) || 0;
        const difference = targetValue - startValue;
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = startValue + (difference * easeOut);

            element.textContent = MoneyCircle.utils.formatCurrency(currentValue);

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }
};

// WebSocket utilities
MoneyCircle.websocket = {
    connections: new Map(),

    /**
     * Create WebSocket connection
     */
    connect: function(endpoint, handlers = {}) {
        const wsUrl = `${MoneyCircle.config.wsBaseUrl}${endpoint}`;
        const ws = new WebSocket(wsUrl);

        ws.onopen = function(event) {
            console.log(`WebSocket connected: ${endpoint}`);
            if (handlers.onOpen) handlers.onOpen(event);
        };

        ws.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                if (handlers.onMessage) handlers.onMessage(data);
            } catch (error) {
                console.error('WebSocket message parse error:', error);
            }
        };

        ws.onclose = function(event) {
            console.log(`WebSocket closed: ${endpoint}`);
            if (handlers.onClose) handlers.onClose(event);
        };

        ws.onerror = function(error) {
            console.error(`WebSocket error: ${endpoint}`, error);
            if (handlers.onError) handlers.onError(error);
        };

        this.connections.set(endpoint, ws);
        return ws;
    },

    /**
     * Close WebSocket connection
     */
    disconnect: function(endpoint) {
        const ws = this.connections.get(endpoint);
        if (ws) {
            ws.close();
            this.connections.delete(endpoint);
        }
    },

    /**
     * Send WebSocket message
     */
    send: function(endpoint, data) {
        const ws = this.connections.get(endpoint);
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(data));
        }
    }
};

// Header and Footer loading utilities
MoneyCircle.layout = {
    /**
     * Load header and footer components
     */
    loadHeaderFooter: async function() {
        try {
            // Load header
            const headerPlaceholder = document.getElementById('header-placeholder');
            if (headerPlaceholder) {
                const headerResponse = await fetch('/static/components/header.html');
                if (headerResponse.ok) {
                    headerPlaceholder.innerHTML = await headerResponse.text();
                }
            }

            // Load footer
            const footerPlaceholder = document.getElementById('footer-placeholder');
            if (footerPlaceholder) {
                const footerResponse = await fetch('/static/components/footer.html');
                if (footerResponse.ok) {
                    footerPlaceholder.innerHTML = await footerResponse.text();
                }
            }

            // Initialize header navigation after loading
            if (typeof initializeHeaderNavigation === 'function') {
                initializeHeaderNavigation();
            }
        } catch (error) {
            console.error('Error loading header/footer:', error);
        }
    }
};

// Global function for backward compatibility
window.loadHeaderFooter = MoneyCircle.layout.loadHeaderFooter;

// Initialize common functionality when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add toast container styles if not present
    if (!document.querySelector('style[data-toast-styles]')) {
        const toastStyles = document.createElement('style');
        toastStyles.setAttribute('data-toast-styles', 'true');
        toastStyles.textContent = `
            .toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            .toast {
                background: rgba(15, 20, 25, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 12px 16px;
                color: #e2e8f0;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease-out;
            }
            .toast-info { border-left: 4px solid #3b82f6; }
            .toast-success { border-left: 4px solid #22c55e; }
            .toast-warning { border-left: 4px solid #f59e0b; }
            .toast-error { border-left: 4px solid #ef4444; }
            .toast-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
            }
            .toast-close {
                background: none;
                border: none;
                color: #94a3b8;
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .toast-close:hover { color: #e2e8f0; }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .spinner {
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-top: 2px solid #8b5cf6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(toastStyles);
    }

    console.log('Money Circle common functionality initialized');
});
