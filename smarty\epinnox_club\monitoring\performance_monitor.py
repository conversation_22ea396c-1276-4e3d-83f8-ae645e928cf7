"""
Performance Monitor
Real-time performance monitoring and optimization for Money Circle production deployment
"""

import time
import psutil
import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import deque
import sqlite3
from aiohttp import web

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.request_times = deque(maxlen=10000)  # Keep last 10000 request times
        self.error_counts = {}
        self.endpoint_stats = {}
        self.alerts = []
        
        # Performance thresholds
        self.cpu_threshold = config.get('cpu_threshold', 80.0)
        self.memory_threshold = config.get('memory_threshold', 85.0)
        self.response_time_threshold = config.get('response_time_threshold', 2.0)
        self.error_rate_threshold = config.get('error_rate_threshold', 5.0)
        
        # Monitoring intervals
        self.metrics_interval = config.get('metrics_interval', 60)  # 1 minute
        self.cleanup_interval = config.get('cleanup_interval', 3600)  # 1 hour
        
        logger.info("[MONITOR] Performance Monitor initialized with real-time tracking")

    async def performance_middleware(self, request: web.Request, handler):
        """Performance monitoring middleware."""
        start_time = time.time()
        endpoint = f"{request.method} {request.path}"
        
        try:
            # Execute request
            response = await handler(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Record metrics
            self._record_request_metrics(endpoint, response_time, response.status)
            
            # Add performance headers
            response.headers['X-Response-Time'] = f"{response_time:.3f}s"
            response.headers['X-Server-Time'] = datetime.now().isoformat()
            
            return response
            
        except Exception as e:
            # Record error
            response_time = time.time() - start_time
            self._record_request_metrics(endpoint, response_time, 500, str(e))
            
            # Log error
            logger.error(f"[MONITOR] Request error: {endpoint} - {str(e)}")
            raise

    def _record_request_metrics(self, endpoint: str, response_time: float, status_code: int, error: str = None):
        """Record request metrics."""
        current_time = time.time()
        
        # Record response time
        self.request_times.append({
            'timestamp': current_time,
            'endpoint': endpoint,
            'response_time': response_time,
            'status_code': status_code,
            'error': error
        })
        
        # Update endpoint statistics
        if endpoint not in self.endpoint_stats:
            self.endpoint_stats[endpoint] = {
                'total_requests': 0,
                'total_time': 0,
                'error_count': 0,
                'status_codes': {}
            }
        
        stats = self.endpoint_stats[endpoint]
        stats['total_requests'] += 1
        stats['total_time'] += response_time
        
        if status_code not in stats['status_codes']:
            stats['status_codes'][status_code] = 0
        stats['status_codes'][status_code] += 1
        
        if error or status_code >= 400:
            stats['error_count'] += 1
        
        # Check for performance alerts
        self._check_performance_alerts(endpoint, response_time, status_code)

    def _check_performance_alerts(self, endpoint: str, response_time: float, status_code: int):
        """Check for performance issues and generate alerts."""
        current_time = time.time()
        
        # Check response time threshold
        if response_time > self.response_time_threshold:
            alert = {
                'type': 'slow_response',
                'timestamp': current_time,
                'endpoint': endpoint,
                'response_time': response_time,
                'threshold': self.response_time_threshold,
                'severity': 'warning' if response_time < self.response_time_threshold * 2 else 'critical'
            }
            self.alerts.append(alert)
            logger.warning(f"[ALERT] Slow response: {endpoint} took {response_time:.3f}s")
        
        # Check error rate
        if status_code >= 400:
            recent_requests = [
                req for req in self.request_times
                if current_time - req['timestamp'] < 300  # Last 5 minutes
                and req['endpoint'] == endpoint
            ]
            
            if len(recent_requests) >= 10:  # Minimum sample size
                error_rate = (sum(1 for req in recent_requests if req['status_code'] >= 400) / len(recent_requests)) * 100
                
                if error_rate > self.error_rate_threshold:
                    alert = {
                        'type': 'high_error_rate',
                        'timestamp': current_time,
                        'endpoint': endpoint,
                        'error_rate': error_rate,
                        'threshold': self.error_rate_threshold,
                        'severity': 'critical'
                    }
                    self.alerts.append(alert)
                    logger.error(f"[ALERT] High error rate: {endpoint} has {error_rate:.1f}% errors")

    async def collect_system_metrics(self):
        """Collect system performance metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available
            memory_total = memory.total
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free
            disk_total = disk.total
            
            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                bytes_sent = network.bytes_sent
                bytes_recv = network.bytes_recv
            except:
                bytes_sent = bytes_recv = 0
            
            # Database metrics
            db_metrics = await self._get_database_metrics()
            
            # Application metrics
            app_metrics = self._get_application_metrics()
            
            metrics = {
                'timestamp': time.time(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'percent': memory_percent,
                    'available': memory_available,
                    'total': memory_total,
                    'used': memory_total - memory_available
                },
                'disk': {
                    'percent': disk_percent,
                    'free': disk_free,
                    'total': disk_total,
                    'used': disk_total - disk_free
                },
                'network': {
                    'bytes_sent': bytes_sent,
                    'bytes_recv': bytes_recv
                },
                'database': db_metrics,
                'application': app_metrics
            }
            
            # Store metrics
            self.metrics_history.append(metrics)
            
            # Check system alerts
            self._check_system_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"[MONITOR] Error collecting system metrics: {e}")
            return {}

    async def _get_database_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        try:
            # Check database file size
            import os
            db_path = 'data/money_circle.db'
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path)
            else:
                db_size = 0
            
            # Test database connection time
            start_time = time.time()
            try:
                conn = sqlite3.connect(db_path, timeout=5)
                conn.execute("SELECT 1").fetchone()
                conn.close()
                connection_time = time.time() - start_time
                connection_status = 'healthy'
            except Exception as e:
                connection_time = time.time() - start_time
                connection_status = f'error: {str(e)}'
            
            return {
                'size_bytes': db_size,
                'connection_time': connection_time,
                'status': connection_status
            }
            
        except Exception as e:
            logger.error(f"[MONITOR] Error getting database metrics: {e}")
            return {'status': 'error', 'error': str(e)}

    def _get_application_metrics(self) -> Dict[str, Any]:
        """Get application-specific metrics."""
        current_time = time.time()
        
        # Calculate recent request statistics
        recent_requests = [
            req for req in self.request_times
            if current_time - req['timestamp'] < 300  # Last 5 minutes
        ]
        
        if recent_requests:
            avg_response_time = sum(req['response_time'] for req in recent_requests) / len(recent_requests)
            error_count = sum(1 for req in recent_requests if req['status_code'] >= 400)
            error_rate = (error_count / len(recent_requests)) * 100
        else:
            avg_response_time = 0
            error_count = 0
            error_rate = 0
        
        # Get top endpoints by request count
        endpoint_requests = {}
        for req in recent_requests:
            endpoint = req['endpoint']
            if endpoint not in endpoint_requests:
                endpoint_requests[endpoint] = 0
            endpoint_requests[endpoint] += 1
        
        top_endpoints = sorted(endpoint_requests.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'total_requests_5min': len(recent_requests),
            'avg_response_time': avg_response_time,
            'error_count_5min': error_count,
            'error_rate_5min': error_rate,
            'top_endpoints': top_endpoints,
            'total_endpoints': len(self.endpoint_stats),
            'active_alerts': len([alert for alert in self.alerts if current_time - alert['timestamp'] < 3600])
        }

    def _check_system_alerts(self, metrics: Dict[str, Any]):
        """Check for system-level alerts."""
        current_time = time.time()
        
        # CPU alert
        cpu_percent = metrics.get('cpu', {}).get('percent', 0)
        if cpu_percent > self.cpu_threshold:
            alert = {
                'type': 'high_cpu',
                'timestamp': current_time,
                'value': cpu_percent,
                'threshold': self.cpu_threshold,
                'severity': 'warning' if cpu_percent < self.cpu_threshold * 1.2 else 'critical'
            }
            self.alerts.append(alert)
            logger.warning(f"[ALERT] High CPU usage: {cpu_percent:.1f}%")
        
        # Memory alert
        memory_percent = metrics.get('memory', {}).get('percent', 0)
        if memory_percent > self.memory_threshold:
            alert = {
                'type': 'high_memory',
                'timestamp': current_time,
                'value': memory_percent,
                'threshold': self.memory_threshold,
                'severity': 'warning' if memory_percent < self.memory_threshold * 1.1 else 'critical'
            }
            self.alerts.append(alert)
            logger.warning(f"[ALERT] High memory usage: {memory_percent:.1f}%")
        
        # Disk space alert
        disk_percent = metrics.get('disk', {}).get('percent', 0)
        if disk_percent > 90:
            alert = {
                'type': 'low_disk_space',
                'timestamp': current_time,
                'value': disk_percent,
                'threshold': 90,
                'severity': 'critical'
            }
            self.alerts.append(alert)
            logger.error(f"[ALERT] Low disk space: {disk_percent:.1f}% used")

    async def start_monitoring(self):
        """Start background monitoring tasks."""
        logger.info("[MONITOR] Starting performance monitoring tasks")
        
        # Start metrics collection task
        asyncio.create_task(self._metrics_collection_loop())
        
        # Start cleanup task
        asyncio.create_task(self._cleanup_loop())

    async def _metrics_collection_loop(self):
        """Background task to collect metrics periodically."""
        while True:
            try:
                await self.collect_system_metrics()
                await asyncio.sleep(self.metrics_interval)
            except Exception as e:
                logger.error(f"[MONITOR] Error in metrics collection loop: {e}")
                await asyncio.sleep(self.metrics_interval)

    async def _cleanup_loop(self):
        """Background task to clean up old data."""
        while True:
            try:
                current_time = time.time()
                
                # Clean up old alerts (keep last 24 hours)
                self.alerts = [
                    alert for alert in self.alerts
                    if current_time - alert['timestamp'] < 86400
                ]
                
                # Clean up old error counts
                for endpoint in list(self.error_counts.keys()):
                    self.error_counts[endpoint] = [
                        timestamp for timestamp in self.error_counts[endpoint]
                        if current_time - timestamp < 3600
                    ]
                    if not self.error_counts[endpoint]:
                        del self.error_counts[endpoint]
                
                logger.info("[MONITOR] Completed periodic cleanup")
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"[MONITOR] Error in cleanup loop: {e}")
                await asyncio.sleep(self.cleanup_interval)

    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        current_time = time.time()
        
        # Get recent metrics
        recent_metrics = [
            metric for metric in self.metrics_history
            if current_time - metric['timestamp'] < 3600  # Last hour
        ]
        
        if not recent_metrics:
            return {'error': 'No recent metrics available'}
        
        # Calculate averages
        avg_cpu = sum(m['cpu']['percent'] for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m['memory']['percent'] for m in recent_metrics) / len(recent_metrics)
        avg_disk = sum(m['disk']['percent'] for m in recent_metrics) / len(recent_metrics)
        
        # Get endpoint performance
        endpoint_performance = {}
        for endpoint, stats in self.endpoint_stats.items():
            if stats['total_requests'] > 0:
                endpoint_performance[endpoint] = {
                    'avg_response_time': stats['total_time'] / stats['total_requests'],
                    'total_requests': stats['total_requests'],
                    'error_rate': (stats['error_count'] / stats['total_requests']) * 100,
                    'status_codes': stats['status_codes']
                }
        
        # Get recent alerts
        recent_alerts = [
            alert for alert in self.alerts
            if current_time - alert['timestamp'] < 3600
        ]
        
        return {
            'timestamp': current_time,
            'system_averages': {
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory,
                'disk_percent': avg_disk
            },
            'endpoint_performance': endpoint_performance,
            'recent_alerts': recent_alerts,
            'total_requests': len(self.request_times),
            'metrics_collected': len(self.metrics_history),
            'monitoring_status': 'healthy'
        }
