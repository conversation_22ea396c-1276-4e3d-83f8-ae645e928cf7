#!/usr/bin/env python3
"""
AI/ML Enhancement Module for Smart-Trader System

This module provides advanced AI/ML capabilities including:
- Model auto-tuning and optimization
- Ensemble optimization with dynamic weights
- Market regime detection using ML
- Sentiment analysis integration
- Hyperparameter optimization
- Model performance prediction
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import sqlite3
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Model configuration for auto-tuning."""
    model_name: str
    parameters: Dict[str, Any]
    performance_score: float
    last_updated: datetime
    optimization_history: List[Dict]


@dataclass
class EnsembleWeights:
    """Dynamic ensemble weights."""
    weights: Dict[str, float]
    confidence: float
    last_optimization: datetime
    performance_improvement: float


@dataclass
class MarketRegime:
    """Market regime classification."""
    regime: str
    confidence: float
    features: Dict[str, float]
    timestamp: datetime
    duration: timedelta


class AIMLEnhancements:
    """
    Advanced AI/ML enhancement system for trading optimization.

    Features:
    - Automatic hyperparameter tuning
    - Dynamic ensemble weight optimization
    - Market regime detection using ML
    - Sentiment analysis integration
    - Model performance prediction
    - Adaptive strategy switching
    """

    def __init__(self, db_path: str = "ai_ml_enhancements.db"):
        """Initialize the AI/ML enhancement system."""
        self.db_path = db_path
        self.model_configs = {}
        self.ensemble_weights = {}
        self.market_regimes = []
        self.sentiment_scores = []

        # Optimization settings
        self.optimization_frequency = timedelta(hours=6)  # Optimize every 6 hours
        self.min_samples_for_optimization = 50
        self.performance_threshold = 0.05  # 5% improvement threshold

        # Market regime features
        self.regime_features = [
            'volatility', 'trend_strength', 'volume_ratio',
            'rsi', 'macd', 'bollinger_position', 'sentiment'
        ]

        # Initialize database
        self._init_database()

        logger.info("🤖 AI/ML Enhancement System initialized")

    def _init_database(self) -> None:
        """Initialize database for AI/ML enhancements."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Model configurations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_configs (
                    model_name TEXT PRIMARY KEY,
                    parameters TEXT,
                    performance_score REAL,
                    last_updated TEXT,
                    optimization_count INTEGER DEFAULT 0
                )
            ''')

            # Ensemble weights table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ensemble_weights (
                    timestamp TEXT PRIMARY KEY,
                    weights TEXT,
                    confidence REAL,
                    performance_improvement REAL
                )
            ''')

            # Market regimes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_regimes_ml (
                    timestamp TEXT PRIMARY KEY,
                    regime TEXT,
                    confidence REAL,
                    features TEXT,
                    duration_minutes INTEGER
                )
            ''')

            # Optimization history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS optimization_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    model_name TEXT,
                    old_params TEXT,
                    new_params TEXT,
                    performance_before REAL,
                    performance_after REAL,
                    improvement REAL
                )
            ''')

            # Sentiment analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sentiment_analysis (
                    timestamp TEXT PRIMARY KEY,
                    source TEXT,
                    sentiment_score REAL,
                    confidence REAL,
                    text_sample TEXT
                )
            ''')

            conn.commit()
            conn.close()

            logger.info("🗄️ AI/ML enhancement database initialized")

        except Exception as e:
            logger.error(f"Failed to initialize AI/ML database: {e}")

    async def auto_tune_model(self, model_name: str, performance_data: List[Dict],
                             current_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Automatically tune model hyperparameters using Bayesian optimization.

        Args:
            model_name: Name of the model to tune
            performance_data: Historical performance data
            current_params: Current model parameters

        Returns:
            Optimized parameters
        """
        try:
            if len(performance_data) < self.min_samples_for_optimization:
                logger.info(f"🤖 Insufficient data for {model_name} optimization ({len(performance_data)} samples)")
                return current_params

            logger.info(f"🎯 Starting auto-tuning for {model_name}")

            # Extract performance metrics
            performances = [d.get('performance', 0.0) for d in performance_data]
            current_performance = np.mean(performances[-10:]) if len(performances) >= 10 else np.mean(performances)

            # Define parameter search space based on model type
            search_space = self._get_parameter_search_space(model_name, current_params)

            # Bayesian optimization simulation (simplified)
            best_params = current_params.copy()
            best_performance = current_performance

            # Try multiple parameter combinations
            for iteration in range(10):  # Limited iterations for demo
                # Generate candidate parameters
                candidate_params = self._generate_candidate_parameters(search_space, current_params)

                # Simulate performance (in real implementation, this would retrain the model)
                simulated_performance = self._simulate_model_performance(
                    model_name, candidate_params, performance_data
                )

                # Update best if improvement found
                if simulated_performance > best_performance:
                    best_performance = simulated_performance
                    best_params = candidate_params.copy()

                    logger.info(f"🎯 {model_name} improvement found: {simulated_performance:.4f} vs {current_performance:.4f}")

            # Check if improvement is significant
            improvement = (best_performance - current_performance) / abs(current_performance) if current_performance != 0 else 0

            if improvement > self.performance_threshold:
                # Store optimization result
                await self._store_optimization_result(
                    model_name, current_params, best_params,
                    current_performance, best_performance, improvement
                )

                logger.info(f"✅ {model_name} auto-tuning completed: {improvement:.2%} improvement")
                return best_params
            else:
                logger.info(f"📊 {model_name} auto-tuning: No significant improvement found")
                return current_params

        except Exception as e:
            logger.error(f"Error in auto-tuning {model_name}: {e}")
            return current_params

    def _get_parameter_search_space(self, model_name: str, current_params: Dict[str, Any]) -> Dict[str, Tuple]:
        """Define parameter search space for different models."""
        search_spaces = {
            'rsi': {
                'period': (10, 30),
                'overbought': (65, 85),
                'oversold': (15, 35)
            },
            'vwap_deviation': {
                'period': (10, 50),
                'std_multiplier': (1.0, 3.0),
                'signal_threshold': (0.1, 0.5)
            },
            'garch_volatility': {
                'window': (20, 100),
                'alpha': (0.01, 0.2),
                'beta': (0.7, 0.99)
            },
            'funding_momentum': {
                'lookback_hours': (6, 48),
                'momentum_threshold': (0.001, 0.01),
                'signal_decay': (0.8, 0.99)
            },
            'meta_ensemble': {
                'min_models': (3, 7),
                'confidence_threshold': (0.6, 0.9),
                'weight_decay': (0.9, 0.99)
            }
        }

        return search_spaces.get(model_name, {
            param: (value * 0.5, value * 1.5) if isinstance(value, (int, float)) else (value, value)
            for param, value in current_params.items()
        })

    def _generate_candidate_parameters(self, search_space: Dict[str, Tuple],
                                     current_params: Dict[str, Any]) -> Dict[str, Any]:
        """Generate candidate parameters within search space."""
        candidate = current_params.copy()

        for param, (min_val, max_val) in search_space.items():
            if isinstance(min_val, (int, float)) and isinstance(max_val, (int, float)):
                if isinstance(current_params.get(param, min_val), int):
                    candidate[param] = np.random.randint(int(min_val), int(max_val) + 1)
                else:
                    candidate[param] = np.random.uniform(min_val, max_val)

        return candidate

    def _simulate_model_performance(self, model_name: str, params: Dict[str, Any],
                                  performance_data: List[Dict]) -> float:
        """Simulate model performance with new parameters."""
        # Simplified simulation - in reality, this would retrain the model
        base_performance = np.mean([d.get('performance', 0.0) for d in performance_data[-20:]])

        # Add some randomness and parameter-based adjustments
        param_adjustment = 0.0

        if model_name == 'rsi':
            # RSI-specific adjustments
            period = params.get('period', 14)
            optimal_period = 14
            param_adjustment = -abs(period - optimal_period) * 0.001

        elif model_name == 'vwap_deviation':
            # VWAP-specific adjustments
            std_mult = params.get('std_multiplier', 2.0)
            optimal_mult = 2.0
            param_adjustment = -abs(std_mult - optimal_mult) * 0.005

        # Add noise to simulate real performance variation
        noise = np.random.normal(0, 0.01)

        return base_performance + param_adjustment + noise

    async def optimize_ensemble_weights(self, model_performances: Dict[str, Dict]) -> Dict[str, float]:
        """
        Optimize ensemble weights using advanced algorithms.

        Args:
            model_performances: Dictionary of model performance data

        Returns:
            Optimized weights dictionary
        """
        try:
            if not model_performances:
                return {}

            logger.info("🎯 Optimizing ensemble weights using ML")

            # Extract performance matrices
            models = list(model_performances.keys())
            performance_matrix = []

            for model in models:
                perf_data = model_performances[model]
                returns = perf_data.get('returns', [])
                if returns:
                    performance_matrix.append(returns[-50:])  # Last 50 returns

            if not performance_matrix or len(performance_matrix[0]) < 10:
                # Fallback to equal weights
                equal_weight = 1.0 / len(models)
                return {model: equal_weight for model in models}

            # Convert to numpy array
            perf_matrix = np.array(performance_matrix)

            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(perf_matrix)

            # Mean returns and covariance
            mean_returns = np.mean(perf_matrix, axis=1)
            cov_matrix = np.cov(perf_matrix)

            # Optimize weights using mean-variance optimization
            weights = self._optimize_portfolio_weights(mean_returns, cov_matrix)

            # Ensure weights are positive and sum to 1
            weights = np.maximum(weights, 0.01)  # Minimum weight
            weights = weights / np.sum(weights)

            # Create weights dictionary
            optimized_weights = {model: float(weight) for model, weight in zip(models, weights)}

            # Calculate confidence based on diversification
            confidence = self._calculate_weight_confidence(weights, correlation_matrix)

            # Store results
            await self._store_ensemble_weights(optimized_weights, confidence)

            logger.info(f"✅ Ensemble weights optimized: {optimized_weights}")
            logger.info(f"📊 Optimization confidence: {confidence:.2f}")

            return optimized_weights

        except Exception as e:
            logger.error(f"Error optimizing ensemble weights: {e}")
            # Return equal weights as fallback
            if model_performances:
                equal_weight = 1.0 / len(model_performances)
                return {model: equal_weight for model in model_performances.keys()}
            return {}

    def _optimize_portfolio_weights(self, mean_returns: np.ndarray, cov_matrix: np.ndarray) -> np.ndarray:
        """Optimize portfolio weights using mean-variance optimization."""
        try:
            # Add regularization to handle singular matrices
            regularization = 1e-6 * np.eye(len(mean_returns))
            cov_matrix_reg = cov_matrix + regularization

            # Inverse covariance matrix
            inv_cov = np.linalg.inv(cov_matrix_reg)

            # Optimal weights (simplified mean-variance)
            ones = np.ones(len(mean_returns))

            # Calculate weights
            numerator = inv_cov @ mean_returns
            denominator = ones.T @ inv_cov @ mean_returns

            if abs(denominator) > 1e-10:
                weights = numerator / denominator
            else:
                # Fallback to equal weights
                weights = ones / len(mean_returns)

            return weights

        except Exception as e:
            logger.error(f"Error in portfolio optimization: {e}")
            # Return equal weights
            return np.ones(len(mean_returns)) / len(mean_returns)

    def _calculate_weight_confidence(self, weights: np.ndarray, correlation_matrix: np.ndarray) -> float:
        """Calculate confidence in weight optimization based on diversification."""
        try:
            # Diversification ratio
            diversification = 1.0 - np.max(weights)

            # Correlation penalty
            avg_correlation = np.mean(np.abs(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]))
            correlation_penalty = 1.0 - avg_correlation

            # Weight concentration penalty
            weight_entropy = -np.sum(weights * np.log(weights + 1e-10))
            max_entropy = np.log(len(weights))
            entropy_ratio = weight_entropy / max_entropy if max_entropy > 0 else 0

            # Combined confidence
            confidence = (diversification * 0.4 + correlation_penalty * 0.4 + entropy_ratio * 0.2)

            return min(max(confidence, 0.0), 1.0)

        except Exception as e:
            logger.error(f"Error calculating weight confidence: {e}")
            return 0.5

    async def detect_market_regime_ml(self, market_data: Dict) -> MarketRegime:
        """
        Detect market regime using machine learning techniques.

        Args:
            market_data: Market data including prices, volumes, indicators

        Returns:
            MarketRegime object with classification and confidence
        """
        try:
            logger.info("🔍 Detecting market regime using ML")

            # Extract features
            features = self._extract_regime_features(market_data)

            if not features:
                return MarketRegime("UNKNOWN", 0.0, {}, datetime.now(), timedelta(0))

            # Simple ML-based regime classification
            regime, confidence = self._classify_market_regime(features)

            # Calculate regime duration
            duration = self._calculate_regime_duration(regime)

            regime_obj = MarketRegime(
                regime=regime,
                confidence=confidence,
                features=features,
                timestamp=datetime.now(),
                duration=duration
            )

            # Store regime
            await self._store_market_regime(regime_obj)

            logger.info(f"🔍 Market regime detected: {regime} (confidence: {confidence:.2f})")

            return regime_obj

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketRegime("ERROR", 0.0, {}, datetime.now(), timedelta(0))

    def _extract_regime_features(self, market_data: Dict) -> Dict[str, float]:
        """Extract features for market regime classification."""
        try:
            features = {}

            prices = market_data.get('prices', [])
            volumes = market_data.get('volumes', [])

            if len(prices) < 20:
                return {}

            # Price-based features
            returns = np.diff(np.log(prices))
            features['volatility'] = np.std(returns) * np.sqrt(252)
            features['skewness'] = float(pd.Series(returns).skew()) if len(returns) > 2 else 0.0
            features['kurtosis'] = float(pd.Series(returns).kurtosis()) if len(returns) > 3 else 0.0

            # Trend features
            sma_20 = np.mean(prices[-20:])
            sma_50 = np.mean(prices[-50:]) if len(prices) >= 50 else sma_20
            features['trend_strength'] = (prices[-1] - sma_50) / sma_50 if sma_50 > 0 else 0
            features['momentum'] = (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 and prices[-10] > 0 else 0

            # Volume features
            if volumes:
                avg_volume = np.mean(volumes[-20:])
                features['volume_ratio'] = volumes[-1] / avg_volume if avg_volume > 0 else 1.0
                features['volume_trend'] = (np.mean(volumes[-5:]) - np.mean(volumes[-20:-5])) / np.mean(volumes[-20:-5]) if len(volumes) >= 20 else 0
            else:
                features['volume_ratio'] = 1.0
                features['volume_trend'] = 0.0

            # Technical indicators
            features['rsi'] = self._calculate_rsi(prices)
            features['macd'] = self._calculate_macd_signal(prices)
            features['bollinger_position'] = self._calculate_bollinger_position(prices)

            return features

        except Exception as e:
            logger.error(f"Error extracting regime features: {e}")
            return {}

    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate RSI indicator."""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return float(rsi)

    def _calculate_macd_signal(self, prices: List[float]) -> float:
        """Calculate MACD signal."""
        if len(prices) < 26:
            return 0.0

        ema_12 = pd.Series(prices).ewm(span=12).mean().iloc[-1]
        ema_26 = pd.Series(prices).ewm(span=26).mean().iloc[-1]

        macd = ema_12 - ema_26
        return float(macd / prices[-1]) if prices[-1] > 0 else 0.0

    def _calculate_bollinger_position(self, prices: List[float], period: int = 20) -> float:
        """Calculate position within Bollinger Bands."""
        if len(prices) < period:
            return 0.5

        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])

        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)

        if upper_band == lower_band:
            return 0.5

        position = (prices[-1] - lower_band) / (upper_band - lower_band)
        return max(0.0, min(1.0, position))

    def _classify_market_regime(self, features: Dict[str, float]) -> Tuple[str, float]:
        """Classify market regime based on features."""
        try:
            # Simple rule-based classification (can be replaced with ML model)
            volatility = features.get('volatility', 0.2)
            trend_strength = features.get('trend_strength', 0.0)
            volume_ratio = features.get('volume_ratio', 1.0)
            rsi = features.get('rsi', 50.0)

            # High volatility regime
            if volatility > 0.4:
                return "HIGH_VOLATILITY", 0.8

            # Trending regimes
            if abs(trend_strength) > 0.1:
                if trend_strength > 0:
                    confidence = min(0.9, 0.6 + abs(trend_strength))
                    return "BULL_TREND", confidence
                else:
                    confidence = min(0.9, 0.6 + abs(trend_strength))
                    return "BEAR_TREND", confidence

            # Overbought/Oversold
            if rsi > 75 and volume_ratio > 1.2:
                return "OVERBOUGHT", 0.7
            elif rsi < 25 and volume_ratio > 1.2:
                return "OVERSOLD", 0.7

            # Low volatility
            if volatility < 0.15:
                return "LOW_VOLATILITY", 0.6

            # Default to sideways
            return "SIDEWAYS", 0.5

        except Exception as e:
            logger.error(f"Error classifying market regime: {e}")
            return "UNKNOWN", 0.0

    def _calculate_regime_duration(self, current_regime: str) -> timedelta:
        """Calculate how long the current regime has been active."""
        try:
            if not self.market_regimes:
                return timedelta(0)

            # Find when current regime started
            regime_start = datetime.now()
            for regime in reversed(self.market_regimes):
                if regime.regime != current_regime:
                    break
                regime_start = regime.timestamp

            return datetime.now() - regime_start

        except Exception as e:
            logger.error(f"Error calculating regime duration: {e}")
            return timedelta(0)

    async def analyze_sentiment(self, text_data: List[str], source: str = "news") -> float:
        """
        Analyze sentiment from text data (news, social media, etc.).

        Args:
            text_data: List of text strings to analyze
            source: Source of the text data

        Returns:
            Sentiment score (-1 to 1, negative to positive)
        """
        try:
            if not text_data:
                return 0.0

            logger.info(f"📰 Analyzing sentiment from {len(text_data)} {source} items")

            # Simple sentiment analysis (can be replaced with advanced NLP)
            sentiment_scores = []

            for text in text_data:
                score = self._calculate_text_sentiment(text)
                sentiment_scores.append(score)

            # Average sentiment
            avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0.0
            confidence = 1.0 - np.std(sentiment_scores) if len(sentiment_scores) > 1 else 0.5

            # Store sentiment analysis
            await self._store_sentiment_analysis(source, avg_sentiment, confidence, text_data[0] if text_data else "")

            logger.info(f"📊 Sentiment analysis complete: {avg_sentiment:.3f} (confidence: {confidence:.2f})")

            return avg_sentiment

        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return 0.0

    def _calculate_text_sentiment(self, text: str) -> float:
        """Calculate sentiment score for a single text."""
        # Simple keyword-based sentiment (can be replaced with ML model)
        positive_words = [
            'bullish', 'positive', 'growth', 'increase', 'rise', 'up', 'gain',
            'profit', 'success', 'strong', 'good', 'excellent', 'buy', 'optimistic'
        ]

        negative_words = [
            'bearish', 'negative', 'decline', 'decrease', 'fall', 'down', 'loss',
            'crash', 'weak', 'bad', 'poor', 'sell', 'pessimistic', 'risk'
        ]

        text_lower = text.lower()

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        total_words = len(text.split())

        if total_words == 0:
            return 0.0

        # Normalize by text length
        positive_score = positive_count / total_words
        negative_score = negative_count / total_words

        # Return sentiment score
        return positive_score - negative_score

    async def predict_model_performance(self, model_name: str, market_conditions: Dict) -> float:
        """
        Predict model performance under current market conditions.

        Args:
            model_name: Name of the model
            market_conditions: Current market condition features

        Returns:
            Predicted performance score
        """
        try:
            logger.info(f"🔮 Predicting performance for {model_name}")

            # Get historical performance data
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT performance_before, performance_after, new_params
                FROM optimization_history
                WHERE model_name = ?
                ORDER BY timestamp DESC LIMIT 20
            ''', (model_name,))

            history = cursor.fetchall()
            conn.close()

            if not history:
                return 0.5  # Default prediction

            # Extract features from market conditions
            volatility = market_conditions.get('volatility', 0.2)
            trend_strength = market_conditions.get('trend_strength', 0.0)
            volume_ratio = market_conditions.get('volume_ratio', 1.0)

            # Simple performance prediction based on market conditions
            base_performance = np.mean([h[1] for h in history])

            # Adjust based on market conditions
            if model_name == 'rsi':
                # RSI performs better in ranging markets
                if abs(trend_strength) < 0.05:
                    adjustment = 0.1
                else:
                    adjustment = -0.05
            elif model_name == 'vwap_deviation':
                # VWAP deviation works well in trending markets
                adjustment = abs(trend_strength) * 0.2
            elif model_name == 'garch_volatility':
                # Volatility models work better in high volatility
                adjustment = (volatility - 0.2) * 0.3
            else:
                adjustment = 0.0

            predicted_performance = base_performance + adjustment
            predicted_performance = max(0.0, min(1.0, predicted_performance))

            logger.info(f"🔮 {model_name} predicted performance: {predicted_performance:.3f}")

            return predicted_performance

        except Exception as e:
            logger.error(f"Error predicting model performance: {e}")
            return 0.5

    async def adaptive_strategy_switching(self, current_strategy: str, market_regime: str,
                                        model_performances: Dict[str, float]) -> str:
        """
        Recommend strategy switching based on market regime and model performance.

        Args:
            current_strategy: Currently active strategy
            market_regime: Current market regime
            model_performances: Current model performance scores

        Returns:
            Recommended strategy name
        """
        try:
            logger.info(f"🔄 Evaluating strategy switch from {current_strategy} in {market_regime} regime")

            # Strategy performance in different regimes
            strategy_regime_performance = {
                'smart_model_integrated_strategy': {
                    'BULL_TREND': 0.8, 'BEAR_TREND': 0.7, 'SIDEWAYS': 0.9,
                    'HIGH_VOLATILITY': 0.6, 'LOW_VOLATILITY': 0.8
                },
                'rsi_strategy': {
                    'BULL_TREND': 0.6, 'BEAR_TREND': 0.6, 'SIDEWAYS': 0.9,
                    'HIGH_VOLATILITY': 0.5, 'LOW_VOLATILITY': 0.8
                },
                'sma_strategy': {
                    'BULL_TREND': 0.8, 'BEAR_TREND': 0.8, 'SIDEWAYS': 0.4,
                    'HIGH_VOLATILITY': 0.6, 'LOW_VOLATILITY': 0.7
                },
                'bollinger_strategy': {
                    'BULL_TREND': 0.5, 'BEAR_TREND': 0.5, 'SIDEWAYS': 0.8,
                    'HIGH_VOLATILITY': 0.9, 'LOW_VOLATILITY': 0.6
                }
            }

            # Calculate scores for each strategy
            strategy_scores = {}

            for strategy, regime_scores in strategy_regime_performance.items():
                regime_score = regime_scores.get(market_regime, 0.5)

                # Adjust based on current model performances
                model_adjustment = 0.0
                if strategy in model_performances:
                    model_adjustment = (model_performances[strategy] - 0.5) * 0.2

                strategy_scores[strategy] = regime_score + model_adjustment

            # Find best strategy
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]
            current_score = strategy_scores.get(current_strategy, 0.5)

            # Only switch if significant improvement (>10%)
            if best_score > current_score * 1.1 and best_strategy != current_strategy:
                logger.info(f"🔄 Strategy switch recommended: {current_strategy} -> {best_strategy}")
                logger.info(f"📊 Score improvement: {current_score:.3f} -> {best_score:.3f}")
                return best_strategy
            else:
                logger.info(f"✅ Current strategy {current_strategy} remains optimal")
                return current_strategy

        except Exception as e:
            logger.error(f"Error in adaptive strategy switching: {e}")
            return current_strategy

    # Database storage methods
    async def _store_optimization_result(self, model_name: str, old_params: Dict, new_params: Dict,
                                       old_performance: float, new_performance: float, improvement: float) -> None:
        """Store optimization result in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO optimization_history
                (timestamp, model_name, old_params, new_params, performance_before, performance_after, improvement)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                model_name,
                json.dumps(old_params),
                json.dumps(new_params),
                old_performance,
                new_performance,
                improvement
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing optimization result: {e}")

    async def _store_ensemble_weights(self, weights: Dict[str, float], confidence: float) -> None:
        """Store ensemble weights in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO ensemble_weights
                (timestamp, weights, confidence, performance_improvement)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                json.dumps(weights),
                confidence,
                0.0  # Will be calculated later
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing ensemble weights: {e}")

    async def _store_market_regime(self, regime: MarketRegime) -> None:
        """Store market regime in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO market_regimes_ml
                (timestamp, regime, confidence, features, duration_minutes)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                regime.timestamp.isoformat(),
                regime.regime,
                regime.confidence,
                json.dumps(regime.features),
                int(regime.duration.total_seconds() / 60)
            ))

            conn.commit()
            conn.close()

            # Update internal list
            self.market_regimes.append(regime)

            # Keep only recent regimes
            if len(self.market_regimes) > 100:
                self.market_regimes = self.market_regimes[-100:]

        except Exception as e:
            logger.error(f"Error storing market regime: {e}")

    async def _store_sentiment_analysis(self, source: str, sentiment: float,
                                      confidence: float, sample_text: str) -> None:
        """Store sentiment analysis in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO sentiment_analysis
                (timestamp, source, sentiment_score, confidence, text_sample)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                source,
                sentiment,
                confidence,
                sample_text[:500]  # Limit text length
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing sentiment analysis: {e}")


# Global AI/ML enhancement instance
ai_ml_engine = AIMLEnhancements()
