{"timestamp": "20250530_224732", "total_test_suites": 1, "passed_suites": 0, "failed_suites": 1, "suite_results": {"Strategy Implementation Validation": {"status": "failed", "message": "Test suite failed with return code 1", "return_code": 1, "output": "", "error": "2025-05-30 22:47:32,329 - INFO - \\U0001f525 Starting Strategy Implementation Validation\n2025-05-30 22:47:32,329 - INFO - ============================================================\n2025-05-30 22:47:32,329 - INFO - \n\\U0001f4cb Phase 1: Dashboard Alignment Check\n2025-05-30 22:47:32,329 - ERROR - \\u274c Dashboard alignment check failed: 'charmap' codec can't decode byte 0x9d in position 12835: character maps to <undefined>\n2025-05-30 22:47:32,329 - INFO - \n\\U0001f9ea Phase 2: Individual Strategy Validation\n2025-05-30 22:47:32,329 - INFO - \n\\U0001f50d Validating: Smart Model Integrated\n2025-05-30 22:47:32,330 - INFO -    Command: python orchestrator.py --debug\n2025-05-30 22:47:32,330 - INFO -    Script: orchestrator.py\n2025-05-30 22:47:32,330 - INFO - \\u2705 Smart Model Integrated: Script file found at orchestrator.py\n2025-05-30 22:47:32,379 - ERROR - \\u274c Smart Model Integrated: Import validation failed\n2025-05-30 22:47:32,379 - ERROR -    STDERR: Traceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 23, in decode\n    return codecs.charmap_decode(input,self.errors,decoding_table)[0]\nUnicodeDecodeError: 'charmap' codec can't decode byte 0x9d in position 11235: character maps to <undefined>\n\n2025-05-30 22:47:32,379 - ERROR - \\u274c Smart Model Integrated: VALIDATION FAILED\n2025-05-30 22:47:32,379 - INFO - \n\\U0001f50d Validating: Smart Strategy Only\n2025-05-30 22:47:32,379 - INFO -    Command: python run_smart_strategy_live.py\n2025-05-30 22:47:32,379 - INFO -    Script: run_smart_strategy_live.py\n2025-05-30 22:47:32,379 - INFO - \\u2705 Smart Strategy Only: Script file found at run_smart_strategy_live.py\n2025-05-30 22:47:32,437 - ERROR - \\u274c Smart Strategy Only: Import validation failed\n2025-05-30 22:47:32,437 - ERROR -    STDERR: Traceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 23, in decode\n    return codecs.charmap_decode(input,self.errors,decoding_table)[0]\nUnicodeDecodeError: 'charmap' codec can't decode byte 0x9d in position 811: character maps to <undefined>\n\n2025-05-30 22:47:32,437 - ERROR - \\u274c Smart Strategy Only: VALIDATION FAILED\n2025-05-30 22:47:32,437 - INFO - \n\\U0001f50d Validating: Order Flow\n2025-05-30 22:47:32,437 - INFO -    Command: python live_dataframe_strategy_runner.py\n2025-05-30 22:47:32,437 - INFO -    Script: live_dataframe_strategy_runner.py\n2025-05-30 22:47:32,437 - INFO - \\u2705 Order Flow: Script file found at live_dataframe_strategy_runner.py\n2025-05-30 22:47:32,484 - ERROR - \\u274c Order Flow: Import validation failed\n2025-05-30 22:47:32,484 - ERROR -    STDERR: Traceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\encodings\\cp1252.py\", line 23, in decode\n    return codecs.charmap_decode(input,self.errors,decoding_table)[0]\nUnicodeDecodeError: 'charmap' codec can't decode byte 0x9d in position 2082: character maps to <undefined>\n\n2025-05-30 22:47:32,484 - ERROR - \\u274c Order Flow: VALIDATION FAILED\n2025-05-30 22:47:32,484 - INFO - \n\\U0001f4ca Validation Summary\n2025-05-30 22:47:32,484 - INFO - ============================================================\n2025-05-30 22:47:32,484 - INFO - Total Strategies: 3\n2025-05-30 22:47:32,484 - INFO - Passed: 0\n2025-05-30 22:47:32,484 - INFO - Failed: 3\n2025-05-30 22:47:32,484 - INFO - Dashboard Aligned: \\u274c\n2025-05-30 22:47:32,484 - ERROR - \\U0001f6a8 VALIDATION FAILURES DETECTED - REQUIRES FIXES BEFORE PRODUCTION\n2025-05-30 22:47:32,485 - INFO - \\U0001f4c4 Results saved to: strategy_validation_results_20250530_224732.json\n", "execution_time": 0.22318315505981445}}}