#!/usr/bin/env python3
import shutil
from datetime import datetime
from pathlib import Path

def create_backup():
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = Path('backups') / f'backup_{timestamp}'
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Backup database
    if Path('data/money_circle_production.db').exists():
        shutil.copy2('data/money_circle_production.db', backup_dir / 'database.db')
    
    # Backup configuration
    if Path('config').exists():
        shutil.copytree('config', backup_dir / 'config')
    
    # Compress backup
    shutil.make_archive(str(backup_dir), 'gztar', str(backup_dir))
    shutil.rmtree(backup_dir)
    
    print(f"[OK] Backup created: {backup_dir}.tar.gz")

if __name__ == '__main__':
    create_backup()
