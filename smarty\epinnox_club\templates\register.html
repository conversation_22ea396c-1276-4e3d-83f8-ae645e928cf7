<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join Money Circle Investment Club</title>
    <link rel="stylesheet" href="/static/css/club.css">
    <style>
        .registration-container {
            max-width: 500px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .registration-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .registration-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }
        
        .registration-header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }
        
        .progress-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
        }
        
        .progress-step.active {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .progress-step:not(:last-child)::after {
            content: "→";
            margin: 0 1rem;
            color: var(--text-secondary);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: var(--input-bg);
            color: var(--text-primary);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
        }
        
        .password-requirements {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .password-requirements ul {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
        }
        
        .error-message {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #dc3545;
        }
        
        .register-button {
            width: 100%;
            padding: 0.75rem;
            background: var(--primary-color);
            color: var(--bg-primary);
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .register-button:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }
        
        .register-button:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }
        
        .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .security-notice {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .security-notice h4 {
            margin: 0 0 0.5rem 0;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-header">
            <h1>💰 Join Money Circle</h1>
            <p>Become a member of our exclusive investment club</p>
        </div>
        
        <div class="progress-indicator">
            <div class="progress-step active">Step 1: Registration</div>
            <div class="progress-step">Step 2: Agreement</div>
        </div>
        
        {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endif %}
        
        <div class="security-notice">
            <h4>🔐 Secure Registration</h4>
            <p>Your information is encrypted and protected. We use industry-standard security measures to keep your data safe.</p>
        </div>
        
        <form method="POST" action="/register" id="registrationForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required 
                       value="{{ username or '' }}" 
                       pattern="[a-zA-Z0-9_-]+" 
                       minlength="3" maxlength="30"
                       title="Username can only contain letters, numbers, hyphens, and underscores">
            </div>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required 
                       value="{{ email or '' }}">
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required 
                       minlength="8" maxlength="128">
                <div class="password-requirements">
                    <strong>Password Requirements:</strong>
                    <ul>
                        <li>At least 8 characters long</li>
                        <li>Contains uppercase and lowercase letters</li>
                        <li>Contains at least one number</li>
                    </ul>
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">Confirm Password</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <button type="submit" class="register-button" id="submitButton">
                Create Account
            </button>
        </form>
        
        <div class="login-link">
            Already have an account? <a href="/login">Sign in here</a>
        </div>
    </div>
    
    <script>
        // Client-side validation
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
                return false;
            }
            
            // Disable submit button to prevent double submission
            document.getElementById('submitButton').disabled = true;
            document.getElementById('submitButton').textContent = 'Creating Account...';
        });
        
        // Real-time password validation
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const requirements = document.querySelector('.password-requirements ul');
            const items = requirements.querySelectorAll('li');
            
            // Check length
            items[0].style.color = password.length >= 8 ? 'green' : '';
            
            // Check case
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            items[1].style.color = (hasUpper && hasLower) ? 'green' : '';
            
            // Check number
            const hasNumber = /\d/.test(password);
            items[2].style.color = hasNumber ? 'green' : '';
        });
    </script>
</body>
</html>
