#!/usr/bin/env python3
"""
🔥 Strategy Implementation Validation Script

This script validates that all strategies listed in the dashboard are properly implemented
and can be started/stopped successfully. This is critical for production readiness.

Usage:
    python validate_strategy_implementations.py
"""

import os
import sys
import time
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StrategyValidator:
    """Validates strategy implementations against dashboard configuration."""
    
    def __init__(self):
        self.results = {
            "validation_timestamp": time.strftime("%Y%m%d_%H%M%S"),
            "total_strategies": 0,
            "passed": 0,
            "failed": 0,
            "strategy_results": {}
        }
        
        # Expected working strategies from dashboard
        self.expected_strategies = {
            "Smart Model Integrated": {
                "command": "python orchestrator.py --debug",
                "script_path": "orchestrator.py",
                "description": "Full system with LLM integration"
            },
            "Smart Strategy Only": {
                "command": "python run_smart_strategy_live.py",
                "script_path": "run_smart_strategy_live.py", 
                "description": "Technical analysis only"
            },
            "Order Flow": {
                "command": "python live_dataframe_strategy_runner.py",
                "script_path": "live_dataframe_strategy_runner.py",
                "description": "DataFrame analysis"
            }
        }
        
    def validate_file_exists(self, strategy_name: str, script_path: str) -> bool:
        """Check if strategy script file exists."""
        try:
            full_path = Path(script_path)
            exists = full_path.exists()
            
            if exists:
                logger.info(f"✅ {strategy_name}: Script file found at {script_path}")
                return True
            else:
                logger.error(f"❌ {strategy_name}: Script file NOT found at {script_path}")
                return False
                
        except Exception as e:
            logger.error(f"❌ {strategy_name}: Error checking file {script_path}: {e}")
            return False
    
    def validate_imports(self, strategy_name: str, script_path: str) -> bool:
        """Check if strategy script can be imported without errors."""
        try:
            # Try to run the script with --help or similar to check imports
            result = subprocess.run(
                ["python", "-c", f"import sys; sys.path.append('.'); exec(open('{script_path}').read())"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {strategy_name}: Import validation passed")
                return True
            else:
                logger.error(f"❌ {strategy_name}: Import validation failed")
                logger.error(f"   STDERR: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.warning(f"⚠️ {strategy_name}: Import validation timed out (may be normal)")
            return True  # Timeout might be normal for some scripts
        except Exception as e:
            logger.error(f"❌ {strategy_name}: Import validation error: {e}")
            return False
    
    def validate_strategy_start_stop(self, strategy_name: str, command: str) -> bool:
        """Test if strategy can be started and stopped cleanly."""
        try:
            logger.info(f"🚀 Testing start/stop for {strategy_name}...")
            
            # Start the strategy process
            proc = subprocess.Popen(
                command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a few seconds to see if it starts successfully
            time.sleep(3)
            
            # Check if process is still running
            if proc.poll() is None:
                logger.info(f"✅ {strategy_name}: Started successfully (PID: {proc.pid})")
                
                # Try to stop it gracefully
                proc.terminate()
                try:
                    proc.wait(timeout=10)
                    logger.info(f"✅ {strategy_name}: Stopped gracefully")
                    return True
                except subprocess.TimeoutExpired:
                    proc.kill()
                    logger.warning(f"⚠️ {strategy_name}: Had to force kill")
                    return True
            else:
                # Process died immediately
                stdout, stderr = proc.communicate()
                logger.error(f"❌ {strategy_name}: Failed to start")
                logger.error(f"   Return code: {proc.returncode}")
                logger.error(f"   STDOUT: {stdout}")
                logger.error(f"   STDERR: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ {strategy_name}: Start/stop test failed: {e}")
            return False
    
    def validate_dashboard_alignment(self) -> bool:
        """Check if dashboard configuration matches expected strategies."""
        try:
            # Read the dashboard file to check strategy configuration
            dashboard_path = Path("live_dashboard.py")
            if not dashboard_path.exists():
                logger.error("❌ Dashboard file not found: live_dashboard.py")
                return False
            
            with open(dashboard_path, 'r') as f:
                content = f.read()
            
            # Check if all expected strategies are in available_strategies
            for strategy_name in self.expected_strategies.keys():
                if f'"{strategy_name}"' in content:
                    logger.info(f"✅ Dashboard alignment: {strategy_name} found in configuration")
                else:
                    logger.error(f"❌ Dashboard alignment: {strategy_name} NOT found in configuration")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Dashboard alignment check failed: {e}")
            return False
    
    def run_validation(self) -> Dict[str, Any]:
        """Run complete validation suite."""
        logger.info("🔥 Starting Strategy Implementation Validation")
        logger.info("=" * 60)
        
        self.results["total_strategies"] = len(self.expected_strategies)
        
        # 1. Dashboard alignment check
        logger.info("\n📋 Phase 1: Dashboard Alignment Check")
        dashboard_aligned = self.validate_dashboard_alignment()
        
        # 2. Individual strategy validation
        logger.info("\n🧪 Phase 2: Individual Strategy Validation")
        
        for strategy_name, config in self.expected_strategies.items():
            logger.info(f"\n🔍 Validating: {strategy_name}")
            logger.info(f"   Command: {config['command']}")
            logger.info(f"   Script: {config['script_path']}")
            
            strategy_result = {
                "file_exists": False,
                "imports_valid": False,
                "start_stop_works": False,
                "overall_status": "FAILED"
            }
            
            # Test 1: File exists
            strategy_result["file_exists"] = self.validate_file_exists(
                strategy_name, config["script_path"]
            )
            
            # Test 2: Imports work (only if file exists)
            if strategy_result["file_exists"]:
                strategy_result["imports_valid"] = self.validate_imports(
                    strategy_name, config["script_path"]
                )
            
            # Test 3: Start/stop works (only if imports work)
            if strategy_result["imports_valid"]:
                strategy_result["start_stop_works"] = self.validate_strategy_start_stop(
                    strategy_name, config["command"]
                )
            
            # Determine overall status
            if all([
                strategy_result["file_exists"],
                strategy_result["imports_valid"], 
                strategy_result["start_stop_works"]
            ]):
                strategy_result["overall_status"] = "PASSED"
                self.results["passed"] += 1
                logger.info(f"✅ {strategy_name}: VALIDATION PASSED")
            else:
                strategy_result["overall_status"] = "FAILED"
                self.results["failed"] += 1
                logger.error(f"❌ {strategy_name}: VALIDATION FAILED")
            
            self.results["strategy_results"][strategy_name] = strategy_result
        
        # 3. Summary
        logger.info("\n📊 Validation Summary")
        logger.info("=" * 60)
        logger.info(f"Total Strategies: {self.results['total_strategies']}")
        logger.info(f"Passed: {self.results['passed']}")
        logger.info(f"Failed: {self.results['failed']}")
        logger.info(f"Dashboard Aligned: {'✅' if dashboard_aligned else '❌'}")
        
        if self.results["failed"] == 0 and dashboard_aligned:
            logger.info("🎉 ALL VALIDATIONS PASSED - READY FOR PRODUCTION!")
        else:
            logger.error("🚨 VALIDATION FAILURES DETECTED - REQUIRES FIXES BEFORE PRODUCTION")
        
        return self.results
    
    def save_results(self):
        """Save validation results to file."""
        filename = f"strategy_validation_results_{self.results['validation_timestamp']}.json"
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        logger.info(f"📄 Results saved to: {filename}")

def main():
    """Main entry point."""
    validator = StrategyValidator()
    results = validator.run_validation()
    validator.save_results()
    
    # Exit with error code if any validations failed
    if results["failed"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
